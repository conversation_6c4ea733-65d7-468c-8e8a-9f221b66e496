<template>
  <ul
    class="h-auto w-11/12 mx-auto flex flex-wrap items-end justify-start gap-1 lg:w-4/5 lg:mx-auto lg:min-w-[1560px]"
    role="tablist"
  >
    <li class="nav-item" v-for="item in generalTabs" :key="item.value">
      <div
        class="whitespace-nowrap cursor-pointer font-semibold px-4 py-3 relative md:ml-0.5 md:-mb-0.5"
        :class="{
          // Active tab styles - file cabinet folder appearance
          'text-active-dark z-10 md:shadow-lg md:bg-card-background md:rounded-t-xl md:text-active':
            tabIndex === item?.value,
          // Inactive tab styles
          'text-inactive rounded-t-lg': tabIndex !== item?.value,
        }"
        @click="handleActiveTab($event)"
        :data-tab-index="item.value"
        role="tab"
      >
        {{ item.label }}
      </div>
    </li>
  </ul>
  <div
    class="bg-card-background text-card-text-light w-11/12 mx-auto mb-4 h-auto rounded-xl border-2 border-active-border relative md:-mt-0.5 lg:w-4/5 lg:mx-auto lg:min-w-[1560px]"
    :class="{
      'md:rounded-tl-none': tabIndex === EGeneralTab.WellInformation,
    }"
  >
    <div class="h-auto w-11/12 flex flex-col gap-6 mx-auto px-3 py-4">
      <component
        :is="currentComponent"
        ref="currentChildTab"
        :showCustomize="toggleCustomize"
      />
    </div>
    <Customize
      :currentTab="currentHelpTab"
      :isVisible="displayCustomize"
      :toggleCustomize="toggleCustomize"
    />
  </div>
</template>

<script lang="ts">
import { EGeneralTab, generalTabs } from "@/constants/daily-report";
import { computed, defineComponent, ref } from "vue";
import Bits from "./bits/Bits.vue";
import CasedHole from "./cased-hole/CasedHole.vue";
import DrillString from "./drill-string/DrillString.vue";
import Nozzles from "./nozzles/Nozzles.vue";
import OpenHold from "./open-hole/OpenHole.vue";
import WellInformation from "./WellInformation.vue";
import AlertService from "@/services/AlertService";
import Customize from "@/layouts/main-layout/Customize.vue";

const tabComponentMap = {
  [EGeneralTab.WellInformation]: WellInformation,
  [EGeneralTab.CasedHole]: CasedHole,
  [EGeneralTab.OpenHold]: OpenHold,
  [EGeneralTab.DrillString]: DrillString,
  [EGeneralTab.Bits]: Bits,
  [EGeneralTab.Nozzles]: Nozzles,
};

export default defineComponent({
  name: "daily-report-general",
  components: {
    WellInformation,
    CasedHole,
    OpenHold,
    DrillString,
    Bits,
    Nozzles,
    Customize,
  },
  props: {
    setChildActiveTab: {
      type: Function,
      required: false,
      default: () => {},
    },
  },
  setup(props) {
    const tabIndex = ref<EGeneralTab>(EGeneralTab.WellInformation);
    const currentChildTab = ref<{ isFormDirty: () => void } | null>(null);
    const currentComponent = computed(() => tabComponentMap[tabIndex.value]);
    const displayCustomize = ref(false);
    const currentHelpTab = ref<EGeneralTab>(EGeneralTab.WellInformation);

    const setActiveTab = (e: Event) => {
      const target = e.target as HTMLInputElement;
      tabIndex.value = Number(target.getAttribute("data-tab-index"));
      if (props?.setChildActiveTab) {
        props.setChildActiveTab(tabIndex.value);
      }
    };

    const handleActiveTab = async (e: Event) => {
      if (tabIndex.value !== EGeneralTab.WellInformation) {
        setActiveTab(e);
        currentHelpTab.value = tabIndex.value;
      } else {
        if (isFormOfChildTabDirty()) {
          AlertService.incompleteFormAlert(
            {
              onConfirmed: () => {
                setActiveTab(e);
              },
            },
            "You have unsaved changes. Are you sure you want to leave?"
          );
        } else {
          setActiveTab(e);
          currentHelpTab.value = tabIndex.value;
        }
      }
    };

    const toggleCustomize = () => {
      currentHelpTab.value = tabIndex.value;
      displayCustomize.value = !displayCustomize.value;
    };

    const isFormOfChildTabDirty = () => {
      if (currentChildTab?.value && currentChildTab?.value?.isFormDirty) {
        return currentChildTab?.value?.isFormDirty();
      }

      return false;
    };

    return {
      handleActiveTab,
      isFormOfChildTabDirty,
      toggleCustomize,
      currentChildTab,
      EGeneralTab,
      generalTabs,
      tabIndex,
      currentComponent,
      currentHelpTab,
      displayCustomize,
    };
  },
});
</script>
