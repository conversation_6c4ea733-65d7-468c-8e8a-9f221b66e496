import{d as o,_ as r,c as a,a as t,ak as n,b as c,w as l,r as d,o as i,B as m}from"./index-CGNRhvz7.js";const p=o({name:"error-500",components:{},setup(){return{}}}),_="/media/auth/500-error.png",f="/media/auth/500-error-dark.png",x={class:"d-flex flex-column flex-center flex-column-fluid"},u={class:"d-flex flex-column flex-center text-center p-10"},h={class:"card card-flush w-lg-650px py-5"},g={class:"card-body py-15 py-lg-20"},b={class:"mb-0"};function w(v,e,y,k,B,N){const s=d("router-link");return i(),a("div",x,[t("div",u,[t("div",h,[t("div",g,[e[1]||(e[1]=n('<h1 class="fw-bolder fs-2qx text-gray-900 mb-4">System Error</h1><div class="fw-semibold fs-6 text-gray-500 mb-7"> Something went wrong! Please try again later. </div><div class="mb-11"><img src="'+_+'" class="mw-100 mh-300px theme-light-show" alt=""><img src="'+f+'" class="mw-100 mh-300px theme-dark-show" alt=""></div>',3)),t("div",b,[c(s,{to:"/",class:"btn btn-sm btn-primary"},{default:l(()=>e[0]||(e[0]=[m("Return Home")])),_:1})])])])])])}const $=r(p,[["render",w]]);export{$ as default};
