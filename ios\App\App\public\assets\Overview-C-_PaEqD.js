import{P as ae}from"./PageHeader-l8HvxxsN.js";import{T as re}from"./TablePagination-BmkwndgK.js";import{S as ie}from"./SvgIcon-CfrWCA-H.js";import{T as de}from"./TableHeader-DGMH-x_O.js";import{d as ee,q as d,Z as ue,x as oe,L as R,Y as pe,_ as te,c,o as i,a as l,b as a,w as h,l as S,r as u,p as le,$ as D,A as z,h as ce,a3 as me,e as fe,E as Y,g as ge,H as L,m as Z,n as G,B as P,I as he,F as J,k as be,t as y,M as K}from"./index-DalLS0_6.js";import{S as Q,a as ve}from"./table-bhK9qpe4.js";import{u as ye}from"./user-UjS69U41.js";import{u as we}from"./company-DGE9srvS.js";import{U as xe}from"./UserModal-DfH6xbe7.js";import"./handleFailure-DrOe_u9W.js";import"./validator-BJ5Qi8qK.js";import"./index.esm-C3uaQ3c9.js";const W={value:"",label:"All"},X={value:0,label:"All"},ke=ee({name:"user-management-filter",components:{},props:{hideFilter:{type:Function,required:!1,default:()=>{}},onFilter:{type:Function,required:!0}},setup(e){const o=we(),U=d(!1),g=d([W]),C=[X,...ue],_=[X,...pe],w={email:"",status:0,role:0,companyId:""},n=d({...w});oe(()=>{R()&&f()});const f=async()=>{U.value=!0,o.getCompanies({params:{page:1,limit:500},callback:{onSuccess:m=>{var b;g.value=[W,...(b=m==null?void 0:m.items)==null?void 0:b.map(p=>({value:p==null?void 0:p.id,label:p==null?void 0:p.name}))]},onFinish:()=>{U.value=!1}}})},x=()=>{e!=null&&e.hideFilter&&(e==null||e.hideFilter())},r=()=>{n.value={...w},A()},A=()=>{var m,b,p,k;e.onFilter({email:((m=n.value)==null?void 0:m.email)||null,status:((b=n.value)==null?void 0:b.status)||null,role:((p=n.value)==null?void 0:p.role)||null,companyId:((k=n.value)==null?void 0:k.companyId)||null}),x()};return{loading:U,isSystemAdmin:R,filterForm:n,roleOptions:C,companyOptions:g,statusOptions:_,apply:A,hideFilter:x,resetFilter:r}}}),Fe={class:"bg-minicard-background text-minicard-text-light max-h-11/12 w-11/12 flex flex-col items-center gap-3 mx-auto p-5 rounded-xl overflow-y-scroll shadow-sm md:overflow-hidden lg:w-2/5 lg:h-3/5"},Se={class:"h-auto w-full flex flex-col"},Ue={class:"h-auto w-full overflow-x-scroll flex flex-row gap-2 md:overflow-hidden"},Ce={class:"h-auto w-full flex flex-col gap-2"},_e={class:"h-auto w-full flex flex-col gap-2"},Ae={class:"h-auto w-full flex flex-col gap-2"},$e={key:0,class:"h-auto w-full flex flex-col gap-2"};function Ve(e,o,U,g,C,_){const w=u("el-input"),n=u("el-form-item"),f=u("el-select-v2"),x=u("el-form");return i(),c("div",Fe,[l("div",Se,[o[7]||(o[7]=l("h5",{class:"h-auto w-full font-semibold self-start"}," Filter ",-1)),l("div",Ue,[l("button",{class:"bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover rounded-md px-4 py-2 font-semibold",onClick:o[0]||(o[0]=(...r)=>e.resetFilter&&e.resetFilter(...r))}," Reset "),l("button",{class:"bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover rounded-md px-4 py-2 font-semibold",onClick:o[1]||(o[1]=(...r)=>e.hideFilter&&e.hideFilter(...r))}," Close "),l("button",{class:"bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover rounded-md px-4 py-2 font-semibold",type:"button",onClick:o[2]||(o[2]=(...r)=>e.apply&&e.apply(...r))}," Apply ")])]),a(x,{model:e.filterForm,onSubmit:le(e.apply,["prevent"]),class:"h-auto w-full flex flex-col gap-3 md:flex-row"},{default:h(()=>[l("div",Ce,[o[8]||(o[8]=l("label",{class:"font-semibold"},"Email",-1)),a(n,{prop:"email"},{default:h(()=>[a(w,{placeholder:"Email",name:"email",modelValue:e.filterForm.email,"onUpdate:modelValue":o[3]||(o[3]=r=>e.filterForm.email=r)},null,8,["modelValue"])]),_:1})]),l("div",_e,[o[9]||(o[9]=l("label",{class:"font-semibold"}," Status ",-1)),a(n,{prop:"status"},{default:h(()=>[a(f,{options:e.statusOptions,placeholder:"Status",name:"status",modelValue:e.filterForm.status,"onUpdate:modelValue":o[4]||(o[4]=r=>e.filterForm.status=r)},null,8,["options","modelValue"])]),_:1})]),l("div",Ae,[o[10]||(o[10]=l("label",{class:"font-semibold"}," Roles ",-1)),a(n,{prop:"role"},{default:h(()=>[a(f,{options:e.roleOptions,placeholder:"Roles",name:"role",modelValue:e.filterForm.role,"onUpdate:modelValue":o[5]||(o[5]=r=>e.filterForm.role=r)},null,8,["options","modelValue"])]),_:1})]),e.isSystemAdmin()?(i(),c("div",$e,[o[11]||(o[11]=l("label",{class:"font-semibold"}," Company ",-1)),a(n,{prop:"companyId"},{default:h(()=>[a(f,{options:e.companyOptions,placeholder:"Company",name:"companyId",modelValue:e.filterForm.companyId,"onUpdate:modelValue":o[6]||(o[6]=r=>e.filterForm.companyId=r),loading:e.loading},null,8,["options","modelValue","loading"])]),_:1})])):S("",!0)]),_:1},8,["model","onSubmit"])])}const Pe=te(ke,[["render",Ve]]),Ie=ee({name:"user-overview",components:{PageHeader:ae,SvgIcon:ie,Filter:Pe,TablePagination:re,UserModal:xe,TableHeader:de},setup(){const e=ye(),o=fe(),U=["User Management","Overview"],g=d([]),C=d(!1),_=d(0),w=d(0),n=d(1),f=d(!1),x=d(""),r=d(!1),A=d(null),m=d([]),b=d({}),p=d(!1),k=d({sortDirection:ve.ASC,sortBy:Q.Name}),T=[{label:"",class:"w-25px",display:D()},{label:"FULL NAME",sortBy:Q.Name,class:"min-w-150px"},{label:"ADDRESS",class:"min-w-150px"},{label:"OFFICE NUMBER",class:"min-w-120px"},{label:"MOBILE NUMBER",class:"min-w-120px"},{label:"STATUS",class:"min-w-150px"},{label:"COMPANY",class:"min-w-150px",display:R()},{label:"ROLES",class:"min-w-120px"},{label:"ACTIONS",class:"min-w-60px",display:D()}];oe(()=>{v()}),z(n,()=>{v()});const v=async()=>{f.value=!0,e.getUsers({params:{page:n.value,limit:10,name:x.value.trim()||null,...k.value,...b.value},callback:{onSuccess:s=>{var V;const $=(V=s==null?void 0:s.items)==null?void 0:V.map(E=>{var j;return{...E,roles:(j=E==null?void 0:E.roles)==null?void 0:j.map(B=>{var q;return((q=ce(B==null?void 0:B.value,ge))==null?void 0:q.label)||""})}});m.value=[...$],_.value=s==null?void 0:s.totalPage,w.value=s==null?void 0:s.total,n.value=s==null?void 0:s.page},onFinish:()=>{f.value=!1}}})},I=s=>{k.value={...s},v()},N=()=>{n.value!==1?n.value=1:v()},t=s=>{n.value=s},F=()=>{p.value=!p.value};z(g,s=>{C.value=m.value.length!==0&&s.length===m.value.length});const O=s=>{var $;($=s==null?void 0:s.target)!=null&&$.checked?g.value=m.value.map(V=>V.id):g.value=[]},M=s=>{o.push({path:`/users/${s}`})},se=s=>{Y.deletionAlert({onConfirmed:()=>{H([s])}})},ne=()=>{Y.deletionAlert({onConfirmed:()=>{H(g.value,!0)}})},H=async(s,$=!1)=>{f.value=!0,e.removeUsers({ids:s,callback:{onSuccess:V=>{$&&(g.value=[]),v()},onFinish:()=>{f.value=!1}}})};return{sortParams:k,tableHeader:T,search:x,loading:f,UserStatus:me,breadcrumbs:U,checkedRows:g,checkAll:C,listUser:m,currentPage:n,totalElements:w,pageCount:_,isShowFilter:r,userModal:A,isModalVisible:p,isSystemAdmin:R,toggleNewUser:F,pageChange:t,deleteUser:se,view:M,isAdmin:D,onToggleCheckAll:O,onRemove:ne,onFilter:s=>{b.value={...s},n.value!==1?n.value=1:v()},toggleFilter:s=>{r.value=s},searchUsers:N,getUserList:v,reloadUserList:()=>{v()},onSort:I}}}),Ne={class:"bg-screen-background h-auto w-11/12 mx-auto my-4 rounded-xl flex flex-col items-center md:text-lg lg:w-4/5 lg:mx-auto lg:min-w-[1560px]"},Oe={class:"bg-card-background text-card-text rounded-lg h-auto w-full flex flex-col items-start my-4 px-8 py-4 gap-3"},Me={class:"h-auto w-full"},Ee={class:"h-auto w-full overflow-x-scroll flex flex-row items-center gap-2 justify-between md:overflow-hidden md:justify-start md:gap-4"},Re={class:"bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover rounded-md px-4 py-2 font-semibold flex flex-row items-center gap-2 justify-betweend md:px-4 md:py-3 md:text-xl"},Te={key:0,class:"text-center p-5"},Be={key:2,class:"h-auto w-full overflow-x-scroll p-4"},De={class:"lg:mx-auto"},Le={class:"font-bold"},He={class:"font-bold"},je={key:0},qe={class:"form-check form-check-sm form-check-custom form-check-solid"},ze={key:1,class:"p-4"},Ye={key:0},Ze={class:"form-check form-check-sm form-check-custom form-check-solid"},Ge=["value"],Je={class:"p-4 truncate"},Ke={class:"flex flex-row items-center gap-3"},Qe=["src"],We={class:"flex flex-col font-semibold"},Xe={class:"p-4 font-semibold text-center whitespace-nowrap"},eo={class:"p-4 font-semibold text-center whitespace-nowrap"},oo={class:"p-4 font-semibold text-center whitespace-nowrap"},to={class:"p-4"},lo={key:1,class:"p-4"},so={class:"font-semibold text-nowrap"},no={class:"p-4 font-semibold text-center whitespace-nowrap"},ao={key:2},ro={class:"h-auto w-full flex flex-row items-center justify-evenly gap-2"},io=["onClick"],uo=["onClick"],po={class:"text-danger"},co={class:"h-auto w-full flex flex-col items-center my-5"},mo={key:0,class:"font-semibold"};function fo(e,o,U,g,C,_){var I,N;const w=u("PageHeader"),n=u("SvgIcon"),f=u("el-icon"),x=u("el-input"),r=u("el-form-item"),A=u("el-form"),m=u("Filter"),b=u("el-empty"),p=u("TableHeader"),k=u("router-link"),T=u("TablePagination"),v=u("UserModal");return i(),c(J,null,[a(w,{title:"User Management",breadcrumbs:e.breadcrumbs},null,8,["breadcrumbs"]),l("div",Ne,[l("div",Oe,[o[11]||(o[11]=l("h1",{class:"font-bold text-lg"},"Users management",-1)),l("div",Me,[a(A,{onSubmit:le(e.searchUsers,["prevent"])},{default:h(()=>[a(r,{class:"mb-0"},{default:h(()=>[a(x,{placeholder:"Search",modelValue:e.search,"onUpdate:modelValue":o[0]||(o[0]=t=>e.search=t),name:"search",size:"large"},{prefix:h(()=>[a(f,{class:"el-input__icon"},{default:h(()=>[a(n,{icon:"searchIcon"})]),_:1})]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["onSubmit"])]),l("div",Ee,[l("button",{class:G(["flex flex-row justify-between rounded-md px-4 py-2 font-semibold text-button-text-light hover:text-button-text-light-hover md:px-4 md:py-3 md:text-xl",e.isShowFilter?"bg-button-primary-active":"bg-button-primary hover:bg-button-primary-hover"]),onClick:o[1]||(o[1]=()=>e.toggleFilter(!e.isShowFilter))},[a(n,{icon:"filterIcon"}),o[7]||(o[7]=P(" Filter "))],2),e.checkedRows.length!==0&&e.isAdmin()?(i(),c("button",{key:0,class:"bg-danger text-white rounded-md px-3 py-2 font-semibold md:px-4 md:py-3 md:text-xl",onClick:o[2]||(o[2]=(...t)=>e.onRemove&&e.onRemove(...t))}," Remove ")):S("",!0),e.isAdmin()?(i(),c("button",{key:1,class:"bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover rounded-md px-4 py-2 font-semibold flex flex-row items-center gap-2 justify-between md:px-4 md:py-3 md:text-xl",onClick:o[3]||(o[3]=(...t)=>e.toggleNewUser&&e.toggleNewUser(...t))},[a(n,{icon:"addIcon"}),o[8]||(o[8]=P(" New "))])):S("",!0),l("button",Re,[a(n,{icon:"exportIcon",classname:"rotate-90"}),o[9]||(o[9]=P(" Export "))])]),L(a(m,{hideFilter:()=>e.toggleFilter(!1),onFilter:e.onFilter},null,8,["hideFilter","onFilter"]),[[he,e.isShowFilter]]),e.loading?(i(),c("div",Te,o[10]||(o[10]=[l("div",{class:"spinner-border text-primary",role:"status"},[l("span",{class:"sr-only"},"Loading...")],-1)]))):e.listUser.length===0?(i(),Z(b,{key:1,description:"No Data"})):(i(),c("div",Be,[l("table",De,[l("thead",Le,[l("tr",He,[a(p,{headers:e.tableHeader,sortBy:e.sortParams.sortBy,sortDirection:e.sortParams.sortDirection,onSort:e.onSort},{customHeader:h(({header:t})=>[t.label===""?(i(),c("div",je,[l("div",qe,[L(l("input",{class:"h-4 w-4",type:"checkbox","onUpdate:modelValue":o[4]||(o[4]=F=>e.checkAll=F),onChange:o[5]||(o[5]=(...F)=>e.onToggleCheckAll&&e.onToggleCheckAll(...F))},null,544),[[K,e.checkAll]])])])):(i(),c("div",ze,y(t.label),1))]),_:1},8,["headers","sortBy","sortDirection","onSort"])])]),l("tbody",null,[(i(!0),c(J,null,be(e.listUser,t=>{var F,O;return i(),c("tr",{key:t.id},[e.isAdmin()?(i(),c("td",Ye,[l("div",Ze,[L(l("input",{class:"h-4 w-4",type:"checkbox",value:t.id,"onUpdate:modelValue":o[6]||(o[6]=M=>e.checkedRows=M)},null,8,Ge),[[K,e.checkedRows]])])])):S("",!0),l("td",Je,[l("div",Ke,[l("img",{class:"h-11 w-11 rounded-full",src:(t==null?void 0:t.avatar)||"/media/avatars/blank.png",alt:""},null,8,Qe),l("div",We,[a(k,{to:`/users/${t==null?void 0:t.id}`,class:"font-bold text-link hover:text-link-hover"},{default:h(()=>[P(y(`${t==null?void 0:t.firstName} ${t==null?void 0:t.lastName}`),1)]),_:2},1032,["to"]),P(y(t==null?void 0:t.email),1)])])]),l("td",Xe,y(t==null?void 0:t.address),1),l("td",eo,y(t==null?void 0:t.officePhone),1),l("td",oo,y(t==null?void 0:t.mobilePhone),1),l("td",to,[l("span",{class:G(["px-2 py-1 rounded-sm text-xs md:text-lg",(t==null?void 0:t.status)===e.UserStatus.Active?"bg-green-200 text-green-500":"bg-red-200 text-red-500"])},y((t==null?void 0:t.status)===e.UserStatus.Active?"Active":"Deactive"),3)]),e.isSystemAdmin()?(i(),c("td",lo,[l("span",so,y((F=t==null?void 0:t.company)==null?void 0:F.name),1)])):S("",!0),l("td",no,y((O=t==null?void 0:t.roles)==null?void 0:O.join(", ")),1),e.isAdmin()?(i(),c("td",ao,[l("div",ro,[l("button",{class:"bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover font-semibold h-8 w-8 rounded-lg pl-[7.5px] pt-[1px] md:h-10 md:w-10 md:pl-[6.52px] md:pt-[2px]",onClick:()=>e.view((t==null?void 0:t.id)??"")},[a(n,{icon:"eyeIcon",classname:"md:h-7 md:w-7"})],8,io),l("button",{class:"bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover font-semibold h-8 w-8 rounded-lg pl-[7px] pb-[3px] md:h-10 md:w-10 md:pl-[6.5px] md:pb-[4.8px]",onClick:M=>e.deleteUser(t.id)},[l("span",po,[a(n,{icon:"trashIcon",classname:"md:h-7 md:w-7"})])],8,uo)])])):S("",!0)])}),128))])])])),l("div",co,[(I=e.listUser)!=null&&I.length?(i(),c("div",mo,y(`Showing ${(e.currentPage-1)*10+1} to ${(N=e.listUser)==null?void 0:N.length} of ${e.totalElements} entries`),1)):S("",!0),e.pageCount>=1?(i(),Z(T,{key:1,"total-pages":e.pageCount,total:e.totalElements,"per-page":10,"current-page":e.currentPage,onPageChange:e.pageChange},null,8,["total-pages","total","current-page","onPageChange"])):S("",!0)])]),a(v,{isVisible:e.isModalVisible,close:e.toggleNewUser,ref:"userModal",loadPage:e.reloadUserList},null,8,["isVisible","close","loadPage"])])],64)}const Vo=te(Ie,[["render",fo]]);export{Vo as default};
