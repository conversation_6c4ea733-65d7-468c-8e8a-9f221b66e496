import type { Callback } from "../types/common";
declare namespace Provide {
  export type UserInfo = {
    userInfo: any;
    updateUserInfo: (info: User.Info) => void;
  };

  export type DailyReport = {
    setDailyReportId: (id: string) => void;
    getDailyReportId: () => string;
    createDailyReport: ({
      wellId,
      callback,
    }: {
      wellId: string;
      callback: Callback;
    }) => void;
    resetDailyReportId: () => void;
  };
}
