import type { Callback } from "../types/common";
import type { Ref } from "vue";

declare namespace Provide {
  export type UserInfo = {
    userInfo: any;
    updateUserInfo: (info: User.Info) => void;
  };

  export type DailyReport = {
    setDailyReportId: (id: string) => void;
    getDailyReportId: () => string;
    createDailyReport: ({
      wellId,
      callback,
    }: {
      wellId: string;
      callback: Callback;
    }) => void;
    resetDailyReportId: () => void;
  };

  export type Title = {
    title: Ref<string>;
    setTitle: (value: string) => void;
    getTitle: () => string;
  };
}
