<template>
  <div class="h-auto w-screen flex flex-col px-4 bg-header-background text-header-text-dark">
    <h1 class="font-bold">
      {{ title }}
    </h1>
    <ul class="h-auto w-full flex flex-row text-sm font-semibold items-center py-2">
      <template v-for="(breadcrumb, index) in breadcrumbs" :key="index">
        <li v-if="index === breadcrumbs.length - 1" class="">
          {{ breadcrumb }}
        </li>
        <template v-else>
          <li>
            {{ breadcrumb }}
          </li>
          <div class="bg-icon-dark h-[2px] w-[5px] mx-2"></div>
        </template>
      </template>
    </ul>
  </div>
</template>

<script lang="ts">
import { defineComponent } from "vue";

export default defineComponent({
  name: "page-header",
  components: {},
  props: {
    title: String,
    breadcrumbs: {
      type: Array<String>,
      default: () => [],
    },
  },
  setup() {
    return {};
  },
});
</script>
