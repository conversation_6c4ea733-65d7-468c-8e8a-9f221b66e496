<template>
  <div
    v-if="isVisible"
    class="fixed top-0 right-0 bottom-0 left-0 z-40 flex items-center"
  >
    <!-- Dark overlay background -->
    <div class="absolute inset-0 bg-sidebar-background opacity-25"></div>
    <div
      class="relative bg-card-background text-card-text max-h-11/12 w-11/12 flex flex-col items-center gap-3 mx-auto p-5 rounded-xl md:w-4/5 lg:w-2/5 lg:h-auto"
    >
      <div class="h-auto w-full flex flex-row items-center justify-between">
        <h3 class="font-bold">Change Password</h3>
        <span class="cursor-pointer" @click="close">
          <SvgIcon icon="closeModalIcon" />
        </span>
      </div>
      <el-form
        id="change_pass_form"
        @submit.prevent="submit"
        :model="targetData"
        :rules="rules"
        ref="formRef"
        class="h-auto w-full flex flex-col items-start gap-3"
      >
        <div class="h-auto w-full flex flex-col relative">
          <label class="font-semibold">
            Current Password
            <span class="text-danger-active font-light">*</span>
          </label>
          <div class="relative">
            <el-form-item prop="currentPassword">
              <el-input
                size="large"
                v-model="targetData.currentPassword"
                name="currentPassword"
                :type="targetData?.hideCurrentPassword ? 'password' : 'input'"
              ></el-input>
            </el-form-item>
            <span
              class="absolute top-3 right-2"
              @click="() => toggleEye('currentPassword')"
            >
              <SvgIcon
                :icon="
                  targetData?.hideCurrentPassword
                    ? 'hidePasswordIcon'
                    : 'showPasswordIcon'
                "
              />
            </span>
          </div>
        </div>
        <div class="h-auto w-full flex flex-col relative">
          <label class="font-semibold">
            New Password <span class="text-danger-active font-light">*</span>
          </label>
          <div class="relative">
            <el-form-item prop="newPassword">
              <el-input
                size="large"
                v-model="targetData.newPassword"
                name="newPassword"
                :type="targetData?.hideNewPassword ? 'password' : 'input'"
              ></el-input>
            </el-form-item>
            <span
              class="absolute top-3 right-2"
              @click="() => toggleEye('newPassword')"
            >
              <SvgIcon
                :icon="
                  targetData?.hideNewPassword
                    ? 'hidePasswordIcon'
                    : 'showPasswordIcon'
                "
              />
            </span>
          </div>
        </div>

        <div class="h-auto w-full flex flex-col relative">
          <label class="font-semibold">
            Confirm Password
            <span class="text-danger-active font-light">*</span>
          </label>
          <div class="relative">
            <el-form-item prop="confirmPassword">
              <el-input
                size="large"
                v-model="targetData.confirmPassword"
                name="confirmPassword"
                :type="targetData?.hideConfirmPassword ? 'password' : 'input'"
              ></el-input>
            </el-form-item>
            <span
              class="absolute top-3 right-2"
              @click="() => toggleEye('confirmPassword')"
            >
              <SvgIcon
                :icon="
                  targetData?.hideConfirmPassword
                    ? 'hidePasswordIcon'
                    : 'showPasswordIcon'
                "
              />
            </span>
          </div>
        </div>

        <div className="text-xs font-medium mb-4">
          <p>New password must contain:</p>
          <div>
            <div
              v-for="[key, value] in Object.entries(checkPassword)"
              :key="key"
              :className="`flex items-center mt-1 gap-2 ${
                value.isValid ? 'text-success' : 'text-danger'
              }`"
            >
              <SvgIcon
                :icon="value.isValid ? 'checkMarkIcon' : 'exclamationMarkIcon'"
              />{{ value.text }}
            </div>
          </div>
        </div>
        <div class="flex flex-row items-center self-center gap-2">
          <button
            type="button"
            class="bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover rounded-md px-4 py-2 font-semibold"
            @click="close"
            :disabled="loading"
          >
            Discard
          </button>
          <button
            class="bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover rounded-md px-4 py-2 font-semibold"
            type="submit"
            :disabled="loading"
          >
            <span v-if="!loading" class="indicator-label"> Save </span>
            <span v-if="loading" class="indicator-progress">
              Please wait...
              <span
                class="spinner-border spinner-border-sm align-middle ms-2"
              ></span>
            </span>
          </button>
        </div>
      </el-form>
    </div>
  </div>
</template>

<script lang="ts">
import SvgIcon from "@/constants/SvgIcon.vue";
import {
  RegexLowercase,
  RegexPasswordLength,
  RegexSpecialChar,
  RegexUppercase,
} from "@/constants/regex";
import { rolesOptions } from "@/constants/user";
import AlertService from "@/services/AlertService";
import { validator } from "@/utils/validator";
import { defineComponent, ref } from "vue";
import { useUserStore } from "@/stores/user";

interface Form {
  currentPassword: string;
  newPassword: string;
  confirmPassword: string;
  hideCurrentPassword: boolean;
  hideNewPassword: boolean;
  hideConfirmPassword: boolean;
}

export default defineComponent({
  name: "change-password-modal",
  components: { SvgIcon },
  props: {
    isVisible: {
      type: Boolean,
      required: true,
    },
    close: {
      type: Function,
      required: true,
    },
  },
  setup(props) {
    const userStore = useUserStore();
    const modal = ref(false);
    const initialValues: Form = {
      currentPassword: "",
      newPassword: "",
      confirmPassword: "",
      hideCurrentPassword: true,
      hideNewPassword: true,
      hideConfirmPassword: true,
    };
    const initialCheckPassFormat = {
      length: {
        isValid: false,
        text: "At least 8 characters",
      },
      lowercase: {
        isValid: false,
        text: "At least 1 lowercase character",
      },
      uppercase: {
        isValid: false,
        text: "At least 1 uppercase character",
      },
      specialCharacter: {
        isValid: false,
        text: "At least 1 number and 1 special character",
      },
    };
    const targetData = ref<Form>({ ...initialValues });
    const loading = ref<boolean>(false);
    const formRef = ref<null | HTMLFormElement>(null);
    const checkPassword = ref({ ...initialCheckPassFormat });

    const validateNewPasswordFormat = (value: string) => {
      const errLength = validator.validate(value, {
        pattern: RegexPasswordLength,
        errorsMessage: { pattern: "Incorrect password format." },
      });
      const errLowercase = validator.validate(value, {
        pattern: RegexLowercase,
        errorsMessage: { pattern: "Incorrect password format." },
      });
      const errUppercase = validator.validate(value, {
        pattern: RegexUppercase,
        errorsMessage: { pattern: "Incorrect password format." },
      });
      const errNumberAndChar = validator.validate(value, {
        pattern: RegexSpecialChar,
        errorsMessage: { pattern: "Incorrect password format." },
      });

      checkPassword.value = {
        length: {
          isValid: errLength ? false : true,
          text: "At least 8 characters",
        },
        lowercase: {
          isValid: errLowercase ? false : true,
          text: "At least 1 lowercase character",
        },
        uppercase: {
          isValid: errUppercase ? false : true,
          text: "At least 1 uppercase character",
        },
        specialCharacter: {
          isValid: errNumberAndChar ? false : true,
          text: "At least 1 number and 1 special character",
        },
      };

      const err =
        errLength || errLowercase || errUppercase || errNumberAndChar || "";

      return err;
    };

    const close = () => {
      props.close();
    };

    const reset = () => {
      targetData.value = { ...initialValues };
      checkPassword.value = { ...initialCheckPassFormat };
      formRef?.value?.resetFields();
    };

    const validateNewPass = (_rule: any, value: any, callback: any) => {
      if (value === "") {
        callback(new Error("Please type New Password"));
      } else {
        const err = validateNewPasswordFormat(value);
        if (err !== "") {
          callback(new Error(err));
        } else {
          callback();
        }
      }
    };

    const validateConfirmPass = (_rule: any, value: any, callback: any) => {
      if (value === "") {
        callback(new Error("Please type Confirm Password"));
      } else if (value !== targetData.value.newPassword) {
        callback(new Error("Confirm Password doesn't match New Password!"));
      } else {
        callback();
      }
    };

    const rules = ref({
      currentPassword: [
        {
          required: true,
          message: "Please type Current Password",
          trigger: ["change", "blur"],
        },
      ],
      newPassword: [
        { validator: validateNewPass, trigger: ["change", "blur"] },
      ],
      confirmPassword: [
        { validator: validateConfirmPass, trigger: ["change", "blur"] },
      ],
    });

    const submit = () => {
      if (!formRef.value) {
        return;
      }

      formRef.value.validate((valid: boolean) => {
        if (valid) {
          promptChangePassword();
        }
      });
    };

    const toggleEye = (field: string) => {
      if (field === "currentPassword") {
        targetData.value.hideCurrentPassword =
          !targetData.value.hideCurrentPassword;
      } else if (field === "confirmPassword") {
        targetData.value.hideConfirmPassword =
          !targetData.value.hideConfirmPassword;
      } else if (field === "newPassword") {
        targetData.value.hideNewPassword = !targetData.value.hideNewPassword;
      }
    };

    const promptChangePassword = () => {
      AlertService.alert(
        "Are you sure you want to change your password?",
        {
          confirmButtonText: "Yes, Change it!",
          cancelButtonText: "No, cancel!",
          confirmButtonClass: "btn fw-bold btn-primary btn-sm",
          cancelButtonClass: "btn fw-bold btn-blue btn-sm",
          callback: {
            onConfirmed: () => {
              changePass();
            },
          },
        },
        "warning"
      );
    };

    const changePass = () => {
      userStore.changePassword({
        params: {
          currentPassword: targetData?.value?.currentPassword,
          newPassword: targetData?.value?.newPassword,
        },
        callback: {
          onSuccess: (_res: any) => {
            props.close();
            AlertService.resultAlert("Password is changed!", "success");
          },
        },
      });
    };

    return {
      formRef,
      rules,
      modal,
      loading,
      targetData,
      rolesOptions,
      checkPassword,
      reset,
      submit,
      toggleEye,
      close,
    };
  },
});
</script>

<style scoped>
.validate-password {
  font-size: 14px;
  font-weight: 400;
  line-height: 20px;

  .valid {
    color: #4caf50;
  }

  .invalid {
    color: #ff5252;
  }

  svg {
    width: 16px !important;
  }
}
</style>
