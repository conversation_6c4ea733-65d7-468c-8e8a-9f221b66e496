<template>
  <div
    class="h-auto w-full my-4 p-4 bg-minicard-background text-minicard-text-light rounded-lg border-[1px] border-dashed font-semibold"
  >
    <h4 class="font-bold mb-4">Pump Equipment</h4>

    <div v-if="loadingPump" class="text-center">
      <div class="spinner-border text-primary" role="status">
        <span class="sr-only">Loading...</span>
      </div>
    </div>
    <template v-else>
      <NoEntries
        v-if="pumpEquipmentList.length === 0"
        :addNew="() => togglePumpModal()"
      />
      <div v-else class="grid grid-cols-1 gap-4 md:grid-cols-2 md:gap-7 lg:grid-cols-3">
        <div
          v-for="item in pumpEquipmentList"
          :key="item?.id"
          class="rounded-lg border-[1px] border-dashed relative cursor-pointer my-2 first:mt-0 last:mb-0 md:first:mt-0 md:last:mb-0 md:m-0"
        >
          <div class="flex flex-col gap-3 p-4">
            <h5
              class="font-bold underline underline-offset-2"
              @click="togglePumpDetailsModal(item?.id || '')"
            >
              {{ item?.description }}
            </h5>
            <div
              class="flex items-center justify-between border-b-[1px] border-dashed pb-3"
            >
              <span>In Use</span>
              <span
                :class="{
                  ['text-success']: item?.inUse,
                  ['text-danger']: !item?.inUse,
                }"
              >
                {{ item?.inUse ? "Active" : "Inactive" }}
              </span>
            </div>
            <div
              class="flex items-center justify-between border-b-[1px] border-dashed pb-3"
            >
              <span>Model</span>
              <span>
                {{ item?.model }}
              </span>
            </div>
            <div
              class="flex items-center justify-between border-b-[1px] border-dashed pb-3"
            >
              <span>Total Duration</span>
              <span>
                {{ `${item?.totalDurations || "0"} hrs` }}
              </span>
            </div>
          </div>
          <div
            class="flex flex-row items-center gap-2 absolute top-0 end-0 -translate-y-1/2"
          >
            <el-tooltip
              content="Add Duration"
              placement="top"
              effect="customize"
            >
              <button
                class="p-2 rounded-full bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover"
                @click="() => togglePumpDurationModal(item?.id || '')"
              >
                <SvgIcon icon="durationIcon" />
              </button>
            </el-tooltip>

            <el-tooltip content="Edit Pump" placement="top" effect="customize">
              <button
                class="p-2 rounded-full bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover"
                @click="togglePumpModal(item?.id || '')"
              >
                <SvgIcon icon="pencilIcon" />
              </button>
            </el-tooltip>
            <el-tooltip
              content="Delete Pump"
              placement="top"
              effect="customize"
            >
              <button
                class="p-2 rounded-full bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover"
                @click="deletePump(item?.id || '')"
              >
                <span class="text-danger">
                  <SvgIcon icon="trashIcon" />
                </span>
              </button>
            </el-tooltip>
          </div>
        </div>
      </div>
    </template>
  </div>
  <PumpModal
    :isVisible="isPumpModalVisible"
    :close="togglePumpModal"
    ref="pumpModal"
    :loadPage="loadData"
  />
  <PumpDetailsModal
    :isVisible="isPumpDetailsModalVisible"
    :close="togglePumpDetailsModal"
    ref="pumpDetailsModal"
    :loadPage="loadData"
  />
  <PumpDurationModal
    :isVisible="isPumpDurationModalVisible"
    :close="togglePumpDurationModal"
    ref="pumpDurationModal"
    :loadPage="loadData"
  />
</template>

<script lang="ts">
import NoEntries from "@/components/NoEntries.vue";
import SvgIcon from "@/constants/SvgIcon.vue";
import AlertService from "@/services/AlertService";
import { usePumpStore } from "@/stores/pump";
import { defineComponent, inject, onMounted, ref, type Ref } from "vue";
import PumpDetailsModal from "./PumpDetailsModal.vue";
import PumpDurationModal from "./PumpDurationModal.vue";
import PumpModal from "./PumpModal.vue";
import type { Provide } from "@/types/injection-types";

export default defineComponent({
  name: "pump-equipment",
  components: {
    SvgIcon,
    PumpModal,
    PumpDetailsModal,
    PumpDurationModal,
    NoEntries,
  },
  props: {
    loadPage: {
      type: Function,
      required: true,
    },
  },
  setup(props) {
    const pumpStore = usePumpStore();
    const pumpModal: Ref<any> = ref<typeof PumpModal | null>(null);
    const pumpDetailsModal: Ref<any> = ref<typeof PumpDetailsModal | null>(
      null
    );
    const pumpDurationModal: Ref<any> = ref<typeof PumpDurationModal | null>(
      null
    );
    const pumpEquipmentList = ref<any>([]);
    const loadingPump = ref(false);
    const dailyReportProvide = inject<Provide.DailyReport>("dailyReport");
    const isPumpModalVisible = ref(false);
    const isPumpDetailsModalVisible = ref(false);
    const isPumpDurationModalVisible = ref(false);

    onMounted(() => {
      if (dailyReportProvide?.getDailyReportId()) {
        getPumps();
      }
    });

    const loadData = () => {
      getPumps();
      if (props?.loadPage) {
        props?.loadPage();
      }
    };

    const getPumps = async (
      params = {
        dailyReportId: dailyReportProvide?.getDailyReportId(),
        page: 1,
        limit: 200,
      }
    ): Promise<void> => {
      loadingPump.value = true;

      pumpStore.getPumps({
        params: params,
        callback: {
          onSuccess: (res: any) => {
            pumpEquipmentList.value = res?.items;
          },
          onFinish: (_err: any) => {
            loadingPump.value = false;
          },
        },
      });
    };

    const togglePumpModal = (pumpId?: string) => {
      if (pumpId) {
        pumpModal?.value?.setId(pumpId);
      }
      isPumpModalVisible.value = !isPumpModalVisible.value;
    };

    const togglePumpDurationModal = (pumpId: string, durationId?: string) => {
      pumpDurationModal?.value?.setId(pumpId, durationId || "");
      isPumpDurationModalVisible.value = !isPumpDurationModalVisible.value;
    };

    const togglePumpDetailsModal = (pumpId?: string) => {
      if (pumpId) {
        pumpDetailsModal?.value?.setId(pumpId);
      }
      isPumpDetailsModalVisible.value = !isPumpDetailsModalVisible.value;
    };

    const deletePump = (id: string) => {
      AlertService.deletionAlert({
        onConfirmed: () => {
          deletePumpById(id);
        },
      });
    };

    const deletePumpById = async (id: string): Promise<void> => {
      loadingPump.value = true;
      pumpStore.deletePump({
        id: id,
        callback: {
          onSuccess: (_res: any) => {
            loadData();
          },
          onFinish: (_err: any) => {
            loadingPump.value = false;
          },
        },
      });
    };

    return {
      pumpModal,
      loadingPump,
      pumpDetailsModal,
      pumpDurationModal,
      pumpEquipmentList,
      isPumpModalVisible,
      isPumpDetailsModalVisible,
      isPumpDurationModalVisible,
      deletePump,
      loadData,
      togglePumpModal,
      togglePumpDetailsModal,
      togglePumpDurationModal,
    };
  },
});
</script>
