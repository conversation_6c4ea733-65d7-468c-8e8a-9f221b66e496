<template>
  <div
    v-if="isVisible"
    class="fixed top-0 right-0 bottom-0 left-0 z-40 flex items-center"
  >
    <!-- Dark overlay background -->
    <div class="absolute inset-0 bg-sidebar-background opacity-25"></div>
    <div
      class="relative bg-card-background text-card-text-light max-h-11/12 w-11/12 flex flex-col items-center gap-3 mx-auto my-6 p-5 rounded-xl md:w-4/5 md:text-lg lg:w-2/5 lg:h-auto"
    >
      <div class="h-auto w-full flex flex-row items-center justify-between">
        <h3 class="text-lg font-bold">
          {{ `${id ? "Edit Open Hole" : "New Open Hole"}` }}
        </h3>
        <span class="cursor-pointer" @click="closeModal">
          <SvgIcon icon="closeModalIcon" />
        </span>
      </div>

      <el-form
        id="open_hole_form"
        @submit.prevent="submit"
        :model="targetData"
        :rules="rules"
        ref="formRef"
        class="h-auto w-full"
      >
        <div class="h-auto w-full flex flex-col gap-3 md:flex-row md:gap-4">
          <div class="h-auto w-full flex flex-col gap-3">
            <div class="h-auto w-full flex flex-col gap-1">
              <label class="font-bold"
                >Description<span class="text-danger-active font-light">*</span>
              </label>
              <el-form-item prop="description" class="mt-auto">
                <el-input
                  v-model="targetData.description"
                  placeholder=""
                  name="description"
                ></el-input>
              </el-form-item>
            </div>
            <div class="h-auto w-full flex flex-col gap-1">
              <label class="font-bold"
                >ID (Inside Diameter) (inches)<el-popover
                  placement="top"
                  :width="300"
                  trigger="hover"
                >
                  <template #reference>
                    <i class="fas fa-exclamation-circle ms-2 fs-7"></i>
                  </template>
                  <span
                    >The Inside Diameter is the inner measurement of the open
                    hole or wellbore, typically measured in inches or
                    millimeters.
                  </span>
                </el-popover></label
              >
              <el-form-item prop="insideDiameter">
                <el-input
                  type="number"
                  :controls="false"
                  step="any"
                  v-model="targetData.insideDiameter"
                  placeholder=""
                  name="insideDiameter"
                ></el-input>
              </el-form-item>
            </div>
          </div>
          <div class="h-auto w-full flex flex-col gap-3">
            <div class="h-auto w-full flex flex-col gap-1">
              <label class="font-bold"
                >MD (Measured Depth) (ft)<el-popover
                  placement="top"
                  :width="300"
                  trigger="hover"
                >
                  <template #reference>
                    <i class="fas fa-exclamation-circle ms-2 fs-7"></i>
                  </template>
                  <span
                    >Measured Depth represents the length of the open hole from
                    the surface to a specific depth.
                  </span>
                </el-popover></label
              >
              <el-form-item prop="measuredDepth">
                <el-input
                  type="number"
                  :controls="false"
                  step="any"
                  v-model="targetData.measuredDepth"
                  placeholder=""
                  name="measuredDepth"
                ></el-input>
              </el-form-item>
            </div>
            <div class="h-auto w-full flex flex-col gap-1">
              <label class="font-bold">
                Washout (%)<el-popover
                  placement="top"
                  :width="300"
                  trigger="hover"
                >
                  <template #reference>
                    <i class="fas fa-exclamation-circle ms-2 fs-7"></i>
                  </template>
                  <span
                    >Washout refers to the enlargement of the open hole diameter
                    due to erosion or other factors. It is expressed as a
                    percentage and can vary at different depths. It is an input,
                    and additional information may include the depth intervals
                    with washout and the severity of washout (e.g., mild,
                    moderate, severe).
                  </span>
                </el-popover>
              </label>

              <el-form-item prop="washout">
                <el-input
                  type="number"
                  :controls="false"
                  step="any"
                  v-model="targetData.washout"
                  placeholder=""
                  name="washout"
                ></el-input>
              </el-form-item>
            </div>
          </div>
        </div>
        <div class="h-auto w-full flex flex-row items-center gap-2">
          <button
            type="button"
            class="bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover rounded-md px-4 py-2 font-semibold md:px-4 md:py-3 md:text-xl"
            @click="closeModal"
            :disabled="loading"
          >
            Discard
          </button>
          <button
            class="bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover rounded-md px-4 py-2 font-semibold md:px-4 md:py-3 md:text-xl"
            type="submit"
            :disabled="loading"
          >
            <span v-if="!loading" class="indicator-label"> Save </span>
            <span v-if="loading" class="indicator-progress">
              Please wait...
              <span
                class="spinner-border spinner-border-sm align-middle ms-2"
              ></span>
            </span>
          </button>
        </div>
      </el-form>
    </div>
  </div>
</template>

<script lang="ts">
import SvgIcon from "@/constants/SvgIcon.vue";
import { useOpenHoleStore } from "@/stores/open-hole";
import { defineComponent, inject, ref, watch } from "vue";
import { useRoute } from "vue-router";
import type { Provide } from "@/types/injection-types";

interface NewOpenHoleData {
  description: string;
  measuredDepth: number | null;
  insideDiameter: number | null;
  washout: number | null;
}

export default defineComponent({
  name: "open-hole-modal",
  components: { SvgIcon },
  props: {
    isVisible: {
      type: Boolean,
      required: true,
    },
    close: {
      type: Function,
      required: true,
    },
    loadPage: {
      type: Function,
      default: () => {},
      required: true,
    },
  },
  setup(props) {
    const route = useRoute();
    const openHoleStore = useOpenHoleStore();
    const modal = ref(false);
    const targetData = ref<NewOpenHoleData>({
      description: "",
      measuredDepth: null,
      insideDiameter: null,
      washout: null,
    });
    const formRef = ref<null | HTMLFormElement>(null);
    const loading = ref<boolean>(false);
    const id = ref("");
    const dailyReportProvide = inject<Provide.DailyReport>("dailyReport");

    watch(id, (newValue) => {
      if (newValue !== "") {
        getOpenHoleDetails();
      }
    });

    watch(
      () => props.isVisible,
      (newValue) => {
        if (newValue === false) {
          id.value = "";
          reset();
          formRef?.value?.resetFields();
        }
      }
    );

    const getOpenHoleDetails = async (): Promise<void> => {
      openHoleStore.getOpenHoleDetails({
        id: id.value,
        callback: {
          onSuccess: (res: any) => {
            targetData.value = { ...res };
          },
        },
      });
    };

    const updateOpenHole = async (param: any): Promise<void> => {
      loading.value = true;
      openHoleStore.updateOpenHole({
        id: id.value,
        params: param,
        callback: {
          onSuccess: (_res: any) => {
            if (props?.loadPage) {
              props?.loadPage();
            }
            props.close();
          },
          onFinish: (_err: any) => {
            loading.value = false;
          },
        },
      });
    };

    const createOpenHole = async (param: any): Promise<void> => {
      loading.value = true;
      openHoleStore.createOpenHole({
        params: param,
        callback: {
          onSuccess: (_res: any) => {
            if (props?.loadPage) {
              props?.loadPage();
            }
            props.close();
          },
          onFinish: (_err: any) => {
            loading.value = false;
          },
        },
      });
    };

    const closeModal = () => {
      props.close();
    };

    const setId = (openHoleId: string) => {
      id.value = openHoleId.toString();
    };

    const rules = ref({
      description: [
        {
          required: true,
          message: "Please type Description",
          trigger: "blur",
        },
      ],
    });

    const submit = () => {
      if (!formRef.value) {
        return;
      }

      formRef.value.validate((valid: boolean) => {
        if (valid) {
          const param = {
            description: targetData?.value?.description,
            measuredDepth: Number(targetData?.value?.measuredDepth),
            insideDiameter: Number(targetData?.value?.insideDiameter),
            washout: Number(targetData?.value?.washout),
          };

          if (id?.value) {
            updateOpenHole({
              ...param,
              dailyReportId: dailyReportProvide?.getDailyReportId(),
            });
          } else {
            dailyReportProvide?.createDailyReport({
              wellId: route?.params?.id as string,
              callback: {
                onSuccess: (res: string) => {
                  createOpenHole({ ...param, dailyReportId: res });
                },
              },
            });
          }
        }
      });
    };

    const reset = () => {
      targetData.value = {
        description: "",
        measuredDepth: null,
        insideDiameter: null,
        washout: null,
      };
    };

    return {
      id,
      modal,
      rules,
      loading,
      targetData,
      formRef,
      closeModal,
      submit,
      setId,
      reset,
    };
  },
});
</script>
