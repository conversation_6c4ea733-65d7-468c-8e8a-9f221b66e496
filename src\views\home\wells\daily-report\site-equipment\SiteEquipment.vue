<template>
  <div class="site-equipment">
    <div
      class="bg-card-background text-card-text-light w-11/12 mx-auto mb-4 h-auto rounded-xl"
    >
      <div class="h-auto w-11/12 flex flex-col gap-6 mt-7 mx-auto px-3 py-4">
        <h1 class="font-bold mb-4">Site Equipment</h1>
        <div
          class="h-auto w-full p-4 bg-minicard-background text-minicard-text-light rounded-lg border-[1px] border-dashed font-semibold"
        >
          <h4 class="mb-4">Detail</h4>
          <div v-if="loading" class="text-center">
            <div class="spinner-border text-primary" role="status">
              <span class="sr-only">Loading...</span>
            </div>
          </div>
          <div v-else class="flex flex-col gap-3 md:flex-row md:gap-4">
            <div
              class="h-auto w-full p-4 rounded-lg border-[1px] border-active border-dashed flex flex-col gap-3"
            >
              <h4>
                Total Rate<el-popover
                  placement="top"
                  :width="300"
                  trigger="hover"
                >
                  <template #reference>
                    <i class="fas fa-exclamation-circle ms-2 fs-7 me-4"></i>
                  </template>
                  <span>
                    The total flow rate of fluid delivered by all active pumps
                    combined.
                  </span>
                </el-popover>
              </h4>
              <p class="text-sm">
                Total Rate (gpm) = Sum(Rate (gpm) of all active pumps)
              </p>
              <h3>
                {{ `${summaryData?.totalRate || "0"} (gmp)` }}
              </h3>
            </div>
            <div
              class="h-auto w-full p-4 rounded-lg border-[1px] border-active border-dashed flex flex-col gap-3"
            >
              <h4>
                Pump pressure<el-popover
                  placement="top"
                  :width="200"
                  trigger="hover"
                >
                  <template #reference>
                    <i class="fas fa-exclamation-circle ms-2 fs-7 me-4"></i>
                  </template>
                  <span> The pressure generated by the pumps. </span>
                </el-popover>
              </h4>
              <p class="text-sm">
                Pump Pressure (psi) = (Displacement (bbl/stroke) * Rate (gpm) *
                8.33) / (Efficiency (%) * 2 * Stroke (stroke/min))
              </p>
              <h3>
                {{ `${summaryData?.pumpPressure || "0"} (psi)` }}
              </h3>
            </div>
          </div>
        </div>
        <Pump ref="pumpRef" :loadPage="getPumpSummary" />
        <Solid ref="solidRef" :loadPage="getPumpSummary" />
      </div>
    </div>
  </div>
  <BottomTool :showHelpInfo="false">
    <template v-slot:header>
      <div class="flex flex-wrap items-center">
        <el-dropdown placement="top">
          <button
            class="bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover hover:border-[2px] hover:border-button-hover-outline rounded-full p-4"
          >
            <SvgIcon icon="addIcon" />
          </button>
          <template #dropdown>
            <el-dropdown-menu class="p-4">
              <el-dropdown-item
                class="customize-dropdown-item mb-3"
                @click="toggleAddPumpModal"
                >Add Pump</el-dropdown-item
              >
              <el-dropdown-item
                class="customize-dropdown-item"
                @click="toggleAddSolidModal"
                >Add Solids Control</el-dropdown-item
              >
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </template>
  </BottomTool>
</template>

<script lang="ts">
import SvgIcon from "@/constants/SvgIcon.vue";
import { usePumpStore } from "@/stores/pump";
import { defineComponent, onMounted, ref, type Ref } from "vue";
import BottomTool from "@/components/common/BottomTool.vue";
import Pump from "./pump/Pump.vue";
import Solid from "./solid/Solid.vue";
import { inject } from "vue";
import type { Provide } from "@/types/injection-types";

export default defineComponent({
  name: "site-equipment",
  components: {
    SvgIcon,
    Pump,
    Solid,
    BottomTool,
  },
  props: {
    setChildActiveTab: {
      type: Function,
      required: false,
      default: () => {},
    },
  },
  setup(_props) {
    const pumpStore = usePumpStore();
    const pumpRef: Ref<any> = ref<typeof Pump | null>(null);
    const solidRef: Ref<any> = ref<typeof Solid | null>(null);
    const loading = ref(false);
    const summaryData = ref<any>([]);
    const dailyReportProvide = inject<Provide.DailyReport>("dailyReport");

    onMounted(() => {
      getPumpSummary();
    });

    const getPumpSummary = async (): Promise<void> => {
      if (!dailyReportProvide?.getDailyReportId()) return;

      loading.value = true;

      pumpStore.getPumpSummary({
        dailyReportId: dailyReportProvide?.getDailyReportId(),
        callback: {
          onSuccess: (res: any) => {
            summaryData.value = res;
          },
          onFinish: (_err: any) => {
            loading.value = false;
          },
        },
      });
    };

    const toggleAddPumpModal = () => {
      pumpRef?.value?.togglePumpModal();
    };

    const toggleAddSolidModal = () => {
      solidRef?.value?.toggleSolidModal();
    };

    return {
      loading,
      summaryData,
      pumpRef,
      solidRef,
      toggleAddPumpModal,
      toggleAddSolidModal,
      getPumpSummary,
    };
  },
});
</script>
