import{P as C}from"./PageHeader-3hadTn26.js";import{d as f,q as l,x as D,J as g,_ as b,c,l as y,b as v,m as _,a as d,F as k,r as u,o as p}from"./index-CGNRhvz7.js";import{u as B}from"./company-oDyd0dWV.js";import{C as M}from"./CompanyDetail-DrR_iaiM.js";import"./handleFailure-DtTpu7r3.js";import"./CompanyModal-Cba-MY3X.js";import"./SvgIcon-CMhyaXWN.js";import"./TablePagination-BmVxunEG.js";import"./d-left-arrow-079-B3YbfCzd.js";import"./TableHeader-C1CWTWQa.js";import"./table-bhK9qpe4.js";import"./date-CvSHk5ED.js";import"./customer-C9SausZF.js";import"./CustomerModal-B1iI3o6f.js";import"./user-KFDu8xJF.js";import"./index.esm-DXW765zG.js";import"./AssignUserModal-B-x10c2w.js";import"./UserModal-Ck8RxOB2.js";import"./validator-6laVLK0J.js";const P=f({name:"my-company",components:{CompanyDetail:M,PageHeader:C},setup(){const o=l(!1),e=B(),t=l();D(()=>{n()});const n=async()=>{var m,a;const r=(a=(m=g)==null?void 0:m.getUserInfo())==null?void 0:a.companyId;r&&(o.value=!0,e.getCompanyById({id:r,callback:{onSuccess:s=>{t.value=s},onFinish:()=>{o.value=!1}}}))};return{loading:o,companyDetail:t,getCompanyDetails:n}}}),S={key:0,class:"text-center my-auto"};function $(o,e,t,n,r,m){var i;const a=u("PageHeader"),s=u("CompanyDetail");return p(),c(k,null,[o.loading?(p(),c("div",S,e[0]||(e[0]=[d("div",{class:"spinner-border text-primary",role:"status"},[d("span",{class:"sr-only"},"Loading...")],-1)]))):y("",!0),v(a,{title:"My Company",breadcrumbs:["My Company",((i=o.companyDetail)==null?void 0:i.name)||""]},null,8,["breadcrumbs"]),!o.loading&&o.companyDetail?(p(),_(s,{key:1,companyDetail:o.companyDetail,reloadCompanyData:o.getCompanyDetails},null,8,["companyDetail","reloadCompanyData"])):y("",!0)],64)}const Q=b(P,[["render",$]]);export{Q as default};
