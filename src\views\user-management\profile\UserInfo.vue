<template>
  <div
    class="bg-card-background text-card-text rounded-lg h-auto w-full flex flex-col my-3 p-4 md:flex-row md:items-center md:justify-between lg:w-4/5 lg:mx-auto lg:min-w-[1560px]"
  >
    <!--begin: Pic-->
    <div class="me-7 mb-4 md:mx-10 lg">
      <div class="symbol symbol-100px symbol-lg-160px symbol-fixed relative">
        <img
          class="h-24 w-24 rounded-full object-cover md:h-32 md:w-32"
          :src="avatar ? avatar : fileImage || '/media/avatars/blank.png'"
          alt="image"
        />
        <input
          type="file"
          ref="fileRef"
          class="hidden"
          @change="handleChooseImage"
        />
        <div class="h-auto w-auto absolute top-16 left-17">
          <button
            type="button"
            :class="`bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover font-semibold h-8 w-8 pl-2 rounded-full ${
              !isAdmin() ? 'hidden' : ''
            }`"
            :disabled="uploadingAvatar"
            @click="clickInputFile"
          >
            <span v-if="!uploadingAvatar">
              <SvgIcon :icon="'pencilIcon'" />
            </span>
            <span
              v-else
              class="spinner-border spinner-border-sm align-middle text-primary"
            ></span>
          </button>
        </div>
        <div
          :class="`absolute top-10 left-21 mb-6 rounded-full border-4 border-light-border h-5 w-5 ${
            userDetail?.status === UserStatus.Active
              ? 'bg-success'
              : 'bg-danger'
          }`"
        ></div>
      </div>
    </div>
    <!--end::Pic-->

    <!--begin::Info-->
    <div class="h-auto w-full text-sm">
      <a href="#" class="text-link hover:text-link font-bold text-lg">{{
        `${userDetail?.firstName || ""} ${userDetail?.lastName || ""}`
      }}</a>

      <div class="md:h-auto md:w-full md:flex md:gap-4 md:justify-between">
        <div class="md:h-auto md:w-full md:flex md:flex-col md:gap-4">
          <div class="flex flex-row items-center gap-2 hover:text-link">
            <SvgIcon :icon="'shieldIcon'" />
            <div class="h-auto w-full flex flex-col my-3">
              <h6 class="font-semibold">Roles</h6>
              {{ userRoles }}
            </div>
          </div>
          <div class="flex flex-row items-center gap-2 hover:text-link">
            <SvgIcon :icon="'atIcon'" />
            <div class="h-auto w-full flex flex-col my-3">
              <h6 class="font-semibold">Email</h6>
              <p class="mb-0">{{ userDetail?.email || "" }}</p>
            </div>
          </div>
          <div class="flex flex-row items-center gap-2 hover:text-link">
            <SvgIcon :icon="'activeCallIcon'" />
            <div class="h-auto w-full flex flex-col my-3">
              <h6 class="font-semibold">Office Phone</h6>
              {{ userDetail?.officePhone || "" }}
            </div>
          </div>
        </div>

        <div class="md:h-auto md:w-full md:flex md:flex-col md:gap-4">
          <div class="flex flex-row items-center gap-2 hover:text-link">
            <SvgIcon :icon="'starIcon'" classname="scale-50" />
            <div class="h-auto w-full flex flex-col my-3">
              <h6 class="font-semibold">Status</h6>
              <span
                class="p-0 user-status"
                :class="
                  userDetail?.status === UserStatus.Active
                    ? 'text-green-400'
                    : 'text-danger'
                "
                >{{
                  userDetail?.status === UserStatus.Active
                    ? "Active"
                    : "Deactive"
                }}</span
              >
            </div>
          </div>
          <div class="flex flex-row items-center gap-2 hover:text-link">
            <SvgIcon :icon="'pinIcon'" />
            <div class="h-auto w-full flex flex-col my-3">
              <h6 class="font-semibold">Address</h6>
              {{ userDetail?.address || "" }}
            </div>
          </div>
          <div class="flex flex-row items-center gap-2 hover:text-link">
            <SvgIcon :icon="'activeCallIcon'" />
            <div class="h-auto w-full flex flex-col my-3">
              <h6 class="font-semibold">Mobile Phone</h6>
              {{ userDetail?.mobilePhone || "" }}
            </div>
          </div>
        </div>

        <div class="md:h-auto md:w-full md:flex md:flex-col md:gap-4">
          <div
            v-if="!isUserDetailSystemAdmin"
            class="flex flex-row items-center gap-2 hover:text-link"
          >
            <SvgIcon :icon="'briefcaseIcon'" />
            <div class="h-auto w-full flex flex-col my-3">
              <h6 class="font-semibold">Company</h6>
              {{ userDetail?.company?.name || "" }}
            </div>
          </div>
          <div
            v-if="userProfileIsEngineer()"
            class="flex flex-col items-center gap-2 hover:text-link"
          >
            <div
              class="flex flex-row items-center gap-2 hover:text-link self-start"
            >
              <SvgIcon :icon="'userIcon'" />
              <h6 class="font-semibold">Supervisor</h6>
            </div>
            <div
              class="flex flex-row gap-2 items-center self-start"
              v-if="userDetail?.supervisor"
            >
              <img
                class="h-11 w-11 rounded-full"
                :src="
                  userDetail?.supervisor?.avatar || '/media/avatars/blank.png'
                "
                alt="Avatar"
              />

              <div class="flex flex-col items-start gap-2 hover:text-link">
                <router-link
                  :to="`/users/${userDetail?.supervisor?.id}`"
                  class="font-bold text-link hover:text-link"
                  >{{
                    `${userDetail?.supervisor?.firstName || ""} ${
                      userDetail?.supervisor?.lastName || ""
                    }`
                  }}</router-link
                >
                {{ userDetail?.supervisor?.email }}
              </div>
            </div>
          </div>
        </div>
      </div>

      <slot name="toolbar"></slot>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, inject, ref, type PropType } from "vue";
import { UserStatus, UserType } from "../../../constants/user";
import { getUserRoleList } from "../../../utils/user";
import { useRoute } from "vue-router";
import JwtService, { isAdmin } from "../../../services/JwtService";
import { useUserStore } from "../../../stores/user";
import type { Provide } from "../../../types/injection-types";
import SvgIcon from "../../../constants/SvgIcon.vue";

export default defineComponent({
  name: "font-semibold",
  components: { SvgIcon },
  props: {
    userDetail: {
      type: Object as PropType<User.Info>,
      required: true,
    },
  },
  setup(props) {
    const userStore = useUserStore();
    const route = useRoute();
    const fileImage = ref<any>(null);
    const avatar = ref<string>(props.userDetail?.avatar || "");
    const fileRef = ref<HTMLInputElement | null>(null);
    const uploadingAvatar = ref<boolean>(false);
    const userInfoProvide = inject<Provide.UserInfo>("userInfo");
    const userRoles = getUserRoleList(props?.userDetail?.roles || [])
      .map((item) => item.key)
      .join(", ");
    const isUserDetailSystemAdmin = Boolean(
      props?.userDetail?.roles?.find(
        (item) => item.value === UserType.SystemAdmin
      )
    );

    const userProfileIsEngineer = (): boolean => {
      if (
        props?.userDetail?.roles?.length === 1 &&
        props?.userDetail?.roles?.find(
          (item) => item?.value === UserType.Engineer
        )
      ) {
        return true;
      }
      return false;
    };

    const isOwner = () => {
      return (
        // route.params?.id?.toString() === JwtService.getUserInfo()?.id ||
        route.name === "my-profile"
      );
    };
    const handleChooseImage = (e: any) => {
      if (e.target.files[0]) {
        fileImage.value = URL.createObjectURL(e.target.files[0]);
        const formData = new FormData();
        formData.append("file", e.target.files[0], e.target.files[0].name);
        formData.append(
          "userId",
          isOwner() ? JwtService.getUserInfo()?.id : props?.userDetail?.id
        );
        uploadMedia(formData);
      }
    };

    const uploadMedia = async (params: any): Promise<void> => {
      uploadingAvatar.value = true;
      userStore.uploadAvatar({
        params,
        callback: {
          onSuccess: (res: any) => {
            avatar.value = res?.url;

            if (
              route.params?.id?.toString() === JwtService.getUserInfo()?.id ||
              isOwner()
            ) {
              userInfoProvide?.updateUserInfo({
                avatar: res?.url,
              });
            }
          },
          onFinish: () => {
            uploadingAvatar.value = false;
          },
        },
      });
    };

    const clickInputFile = () => {
      fileRef?.value?.click();
    };

    return {
      isAdmin,
      UserStatus,
      userRoles,
      avatar,
      fileImage,
      fileRef,
      uploadingAvatar,
      isUserDetailSystemAdmin,
      userProfileIsEngineer,
      handleChooseImage,
      clickInputFile,
    };
  },
});
</script>
