import{d as m,u as f,j as _,q as h,x,e as v,_ as g,c as n,a as e,o as a}from"./index-CGNRhvz7.js";import{u as y}from"./user-KFDu8xJF.js";import{_ as k,a as b}from"./agency-dark-DzOWuJJK.js";import"./handleFailure-DtTpu7r3.js";const w=m({name:"accept-invite",components:{},setup(){var l,d;const o=y(),s=f(),r=v(),c=((d=(l=_().params)==null?void 0:l.token)==null?void 0:d.toString())||"",t=h(!1);x(()=>{i()});const i=async()=>{t.value=!0,o.acceptInviteToken({token:c,callback:{onSuccess:u=>{s.setAuth(u),r.push({path:"/register-invited-user"})},onFinish:()=>{t.value=!1}}})};return{loading:t,acceptInvite:i}}}),S={class:"d-flex flex-column flex-center flex-column-fluid"},A={class:"d-flex flex-column flex-center text-center p-10"},I={class:"card card-flush w-lg-650px py-5"},$={class:"card-body py-15 py-lg-20"},B={key:0,class:"text-center p-5"},R={key:1};function j(o,s,r,p,c,t){return a(),n("div",S,[e("div",A,[e("div",I,[e("div",$,[o.loading?(a(),n("div",B,s[0]||(s[0]=[e("div",{class:"spinner-border text-primary",role:"status"},[e("span",{class:"sr-only"},"Loading...")],-1)]))):(a(),n("div",R,s[1]||(s[1]=[e("h1",{class:"fw-bolder fs-2hx text-gray-900 mb-4"}," Accept Invitation ",-1),e("div",{class:"mb-3"},[e("img",{src:k,class:"mw-100 mh-300px theme-light-show",alt:""}),e("img",{src:b,class:"mw-100 mh-300px theme-dark-show",alt:""})],-1)])))])])])])}const L=g(w,[["render",j]]);export{L as default};
