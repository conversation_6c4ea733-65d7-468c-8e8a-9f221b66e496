<template>
  <div class="bg-screen-background h-auto w-11/12 mx-auto mt-4 rounded-xl">
    <UserInfo :userDetail="userDetail">
      <div class="bg-amber-300 d-flex my-4 gap-3">
        <button
          v-if="resendInvitation"
          class="btn btn-flex btn-sm btn-success"
          @click="sendInvitation"
        >
          <span v-if="!sendingInvitation"> Resend Invitation </span>
          <span v-else class="indicator-progress">
            Please wait...
            <span
              class="spinner-border spinner-border-sm align-middle ms-2"
            ></span>
          </span>
        </button>
        <button
          v-if="userDetail?.status === UserStatus.Inactive"
          class="btn btn-sm btn-primary"
          type="button"
          @click="promptChangeAccountStatus(UserStatus.Active)"
        >
          <span v-if="!submitting">
            <span class="svg-icon svg-icon-1 me-1">
              <inline-svg src="media/icons/duotune/general/unlock.svg" />
            </span>

            Active Account</span
          >
          <span v-else class="indicator-progress">
            Please wait...
            <span
              class="spinner-border spinner-border-sm align-middle ms-2"
            ></span>
          </span>
        </button>
        <button
          v-else-if="userDetail?.status === UserStatus.Active"
          class="btn btn-sm btn-danger"
          type="button"
          @click="promptChangeAccountStatus(UserStatus.Inactive)"
        >
          <span v-if="!submitting"
            ><span class="svg-icon svg-icon-1">
              <inline-svg src="media/icons/duotune/general/lock.svg" />
            </span>
            Deactive Account
          </span>
          <span v-else class="indicator-progress">
            Please wait...
            <span
              class="spinner-border spinner-border-sm align-middle ms-2"
            ></span>
          </span>
        </button>
        <button
          type="button"
          class="btn text-gray-700 btn-sm btn-blue"
          v-if="!isOwner()"
          @click="back"
        >
          Back
        </button>
      </div>
    </UserInfo>

    <!--begin::Navs-->
    <ul
      class="bg-card-background text-card-text h-auto w-full mt-4 pt-4 px-4 rounded-t-lg flex flex-row items-center justify-start gap-3 border-transparent font-bold lg:w-4/5 lg:mx-auto lg:min-w-[1560px]"
    >
      <!--begin::Nav item-->
      <li
        class="cursor-pointer font-semibold hover:text-primary hover:border-b-2 hover:border-primary"
        v-for="item in tabs"
      >
        <div
          class="nav-link cursor-pointer hover:text-active-hover hover:border-b-2 hover:border-active-border-hover"
          :class="{
            active: tabIndex === item.value,
            'text-active border-b-2 border-active-border':
              tabIndex === item?.value,
            'text-inactive border-b-2 border-inactive-border':
              tabIndex !== item?.value,
          }"
          @click="setActiveTab($event)"
          :data-tab-index="item.value"
          role="tab"
        >
          {{ item.key }}
        </div>
      </li>

      <!--end::Nav item-->
    </ul>
    <!--begin::Navs-->
    <Overview
      v-if="tabIndex === tabs[0].value"
      :hideRole="isSystemAdmin()"
      :userDetail="userDetail"
      :reloadUserData="reloadUserData"
    />
    <EngineerList
      v-else-if="tabIndex === tabs[1].value && userDetail?.id"
      :userDetail="userDetail"
    />
  </div>
</template>

<script lang="ts">
import SvgIcon from "@/constants/SvgIcon.vue";
import UserInfo from "./UserInfo.vue";
import { UserStatus, UserType } from "@/constants/user";
import AlertService from "@/services/AlertService";
import JwtService, { isAdmin } from "@/services/JwtService";
import { useUserStore } from "@/stores/user";
import { defineComponent, onMounted, ref, type PropType } from "vue";
import { useRoute, useRouter } from "vue-router";
import EngineerList from "./EngineerList.vue";
import Overview from "./Overview.vue";

export default defineComponent({
  name: "user-detail",
  components: {
    SvgIcon,
    EngineerList,
    Overview,
    UserInfo,
  },
  props: {
    userDetail: {
      type: Object as PropType<User.Info>,
      required: true,
    },
    reloadUserData: {
      type: Function,
      required: true,
    },
  },

  setup(props) {
    const getTab = () => {
      if (
        props?.userDetail?.roles?.find(
          (item) => item?.value === UserType.Supervisor
        ) &&
        !JwtService.isJustEngineer()
      ) {
        return [
          { value: "overview", key: "Overview" },
          { value: "engineers", key: "Engineers" },
        ];
      } else {
        return [{ value: "overview", key: "Overview" }];
      }
    };
    const tabs = getTab();
    const userStore = useUserStore();
    const currentPage = ref(1);
    const pageCount = ref(5);
    const totalElements = ref(50);
    const route = useRoute();
    const router = useRouter();
    const submitting = ref<boolean>(false);
    const resendInvitation = ref<boolean>(false);
    const tabIndex = ref<string>(tabs[0].value);
    const sendingInvitation = ref<boolean>(false);

    onMounted(() => {
      if (isAdmin() && !isOwner()) {
        isResendInvitation();
      }
    });

    const isResendInvitation = async (): Promise<void> => {
      userStore.getInvitedUser({
        callback: {
          onSuccess: (res: any) => {
            if (
              Array.isArray(res) &&
              res?.some((item) => item.id === props?.userDetail?.id)
            ) {
              resendInvitation.value = true;
            }
          },
        },
      });
    };

    const pageChange = (newPage: number) => {
      currentPage.value = newPage;
    };

    const promptChangeAccountStatus = (status: number) => {
      if (status === UserStatus.Active) {
        AlertService.alert(
          "Are you sure you want to activate this user account?",
          {
            confirmButtonText: "Yes, Activate it!",
            cancelButtonText: "No, cancel!",
            confirmButtonClass: "btn fw-bold btn-primary btn-sm",
            cancelButtonClass: "btn fw-bold btn-blue btn-sm",
            callback: {
              onConfirmed: () => {
                updateUserProfileById(props?.userDetail?.id!, status);
              },
            },
          },
          "warning"
        );
      } else {
        AlertService.alert(
          "Are you sure you want to deactivate this user account?",
          {
            confirmButtonText: "Yes, Deactivate it!",
            cancelButtonText: "No, cancel!",
            confirmButtonClass: "btn fw-bold btn-light-danger btn-sm",
            cancelButtonClass: "btn fw-bold btn-blue btn-sm",
            callback: {
              onConfirmed: () => {
                updateUserProfileById(props?.userDetail?.id!, status);
              },
            },
          },
          "warning"
        );
      }
    };

    const userProfileIsEngineer = (): boolean => {
      if (
        props?.userDetail?.roles?.length === 1 &&
        props?.userDetail?.roles?.find(
          (item) => item?.value === UserType.Engineer
        )
      ) {
        return true;
      }
      return false;
    };

    const sendInvitation = async () => {
      if (!props?.userDetail?.email) return;
      sendingInvitation.value = true;
      userStore.inviteUsers({
        params: {
          emails: [props.userDetail.email],
          userRoles: props.userDetail?.roles?.map((item) => item.value) || [],
          companyId: props.userDetail?.company?.id || "",
        },
        callback: {
          onSuccess: (_res: any) => {
            AlertService.toast("Invited successfully", "success", "top-right");
          },
          onFinish: () => {
            sendingInvitation.value = false;
          },
        },
      });
    };

    const isOwner = () => {
      return (
        // route.params?.id?.toString() === JwtService.getUserInfo()?.id ||
        route.name === "my-profile"
      );
    };

    const isSystemAdmin = () => {
      return (
        route.params?.id?.toString() === JwtService.getUserInfo()?.id &&
        Boolean(
          props?.userDetail?.roles?.find(
            (item) => item.value === UserType.SystemAdmin
          )
        )
      );
    };

    const setActiveTab = (e: Event) => {
      const target = e.target as HTMLInputElement;
      tabIndex.value = target.getAttribute("data-tab-index") as string;
    };

    const updateUserProfileById = (id: string, status: number) => {
      submitting.value = true;
      userStore.updateUserProfile({
        id,
        params: { status },
        callback: {
          onSuccess: () => {
            props?.reloadUserData?.();
            if (status === UserStatus.Active) {
              AlertService.resultAlert("Account is activated!", "success");
            } else {
              AlertService.resultAlert("Account is deactivated!.", "success");
            }
          },
          onFinish: () => {
            submitting.value = false;
          },
        },
      });
    };

    const back = () => {
      router.go(-1);
    };

    return {
      sendingInvitation,
      resendInvitation,
      submitting,
      UserStatus,
      currentPage,
      totalElements,
      pageCount,
      route,
      tabIndex,
      tabs,
      back,
      isOwner,
      pageChange,
      isAdmin,
      setActiveTab,
      promptChangeAccountStatus,
      userProfileIsEngineer,
      isSystemAdmin,
      sendInvitation,
    };
  },
});
</script>

<style>
.user-overview {
  .user-info {
    font-weight: 500;
  }
  .user-status {
    font-size: 14px;
    font-weight: 700;
  }
  .btn-edit-image {
    position: absolute;
    bottom: 10px;
    right: 10px;
    .btn.btn-light {
      padding: 0px !important;
      height: 35px;
      width: 35px;
      justify-content: center;
      align-items: center;
      .svg-icon {
        margin: 0px 0px 0px 2px;
      }
    }
  }
}
</style>
