<template>
  <div class="min-h-screen w-full bg-app-baselayer">
    <RouterView />
  </div>
</template>

<script lang="ts">
import { defineComponent } from "vue";
import { RouterView } from "vue-router";

export default defineComponent({
  name: "app",
  components: {
    RouterView,
  },
});
</script>

<style>
/* @import "bootstrap-icons/font/bootstrap-icons.css"; */
@import "apexcharts/dist/apexcharts.css";
/* @import "quill/dist/quill.snow.css"; */
/* @import "animate.css"; */
@import "sweetalert2/dist/sweetalert2.css";
/* @import "nouislider/distribute/nouislider.css"; */
@import "@fortawesome/fontawesome-free/css/all.min.css";
/* @import "socicon/css/socicon.css"; */
/* @import "line-awesome/dist/line-awesome/css/line-awesome.css"; */
/* @import "dropzone/dist/dropzone.css"; */
/* @import "@vueform/multiselect/themes/default.css"; */
/* @import "prism-themes/themes/prism-shades-of-purple.css"; */
@import "element-plus/dist/index.css";

/* @import "assets/fonticon/fonticon.css"; */

#app {
  display: contents;
}
</style>
