import{S as ne}from"./SvgIcon-CMhyaXWN.js";import{U as be,O as me}from"./Overview-CymoOfok.js";import{d as te,q as u,a0 as j,x as ae,A as Z,a4 as q,U as $,E as B,e as oe,J,j as le,_ as ie,c,o as l,a as n,b as h,m as z,l as E,w as R,r as b,p as fe,B as F,H as x,M as ee,t as I,F as G,k as re,s as he,n as ye}from"./index-CGNRhvz7.js";import{u as ce}from"./user-KFDu8xJF.js";import{A as ke}from"./AssignUserModal-B-x10c2w.js";import{T as Ae}from"./TablePagination-BmVxunEG.js";import{T as we}from"./TableHeader-C1CWTWQa.js";import{S as se,a as De}from"./table-bhK9qpe4.js";import{f as _e}from"./date-CvSHk5ED.js";const pe=te({name:"user-info-engineer",components:{SvgIcon:ne,TablePagination:Ae,AssignUserModal:ke,TableHeader:we},props:{userDetail:{type:Object,required:!0}},setup(e){var W,X;const t=ce(),V=le(),T=oe(),k=u([]),N=u(!1),A=u(0),S=u(0),m=u(1),g=u(!1),w=u(!1),f=u(""),v=u([]),y=u([]),D=u(null),M=u(!1),L=u({sortDirection:De.ASC,sortBy:se.Name}),O=[{label:"",class:"w-25px",display:j()},{label:"FULL NAME",sortBy:se.Name,class:"min-w-150px"},{label:"MOBILE NUMBER",class:"min-w-120px"},{label:"OFFICE NUMBER",class:"min-w-120px"},{label:"ADDED DATE",class:"min-w-120px"},{label:"ACTIONS",class:"min-w-60px",display:j()}];ae(()=>{s(),Y()}),Z(m,()=>{s()});const P=()=>{M.value=!M.value},s=async()=>{var a;g.value=!0,t.getAssignedEngineers({params:{supervisorId:(a=e==null?void 0:e.userDetail)==null?void 0:a.id,name:f.value.trim()||null,page:m.value,limit:10,...L.value},callback:{onSuccess:o=>{v.value=[...o==null?void 0:o.items],A.value=o==null?void 0:o.totalPage,S.value=o==null?void 0:o.total,m.value=o==null?void 0:o.page},onFinish:()=>{g.value=!1}}})},C=a=>{L.value={...a},s()},Y=async(a="")=>{var o;w.value=!0,t.getUsers({params:{role:$.Engineer,status:q.Active,companyId:(o=e.userDetail)==null?void 0:o.companyId,sortBy:"email",name:a.trim()||null,page:m.value,limit:500},callback:{onSuccess:p=>{y.value=[...p==null?void 0:p.items]},onFinish:()=>{w.value=!1}}})},K=a=>{B.deletionAlert({onConfirmed:()=>{Q([a])}})},i=a=>{m.value=a};Z(k,a=>{N.value=a.length===v.value.length});const r=a=>{var o;(o=a==null?void 0:a.target)!=null&&o.checked?k.value=v.value.map(p=>p.id):k.value=[]},d=a=>{T.push({path:`/users/${a}`})},U=a=>{var o;ve(a),(o=D==null?void 0:D.value)==null||o.closeModal()},_=a=>{Y(a)},H=()=>{m.value!==1?m.value=1:s()},de=()=>{var a;(a=D==null?void 0:D.value)==null||a.show()},ue=()=>{var a,o;return(J.checkRole($.SystemAdmin)||J.checkRole($.CompanyAdmin))&&((o=(a=e==null?void 0:e.userDetail)==null?void 0:a.roles)==null?void 0:o.find(p=>(p==null?void 0:p.value)===$.Supervisor))&&V.name!=="my-profile"},ge=()=>{B.deletionAlert({onConfirmed:()=>{Q(k.value,!0)}})},Q=async(a,o=!1)=>{g.value=!0,t.removeEngineers({engineerIds:a,callback:{onSuccess:p=>{o&&(k.value=[]),s()},onFinish:()=>{g.value=!1}}})},ve=async a=>{var o;g.value=!0,t.assignEngineers({params:{engineerIds:a,supervisorId:(o=e.userDetail)==null?void 0:o.id},callback:{onSuccess:p=>{s(),B.toast("Added successfully","success","top-right")},onFinish:()=>{g.value=!1}}})};return{sortParams:L,tableHeader:O,userListForAssignUserModal:y,search:f,loading:g,assignedEngineerList:v,checkedRows:k,checkAll:N,currentPage:m,totalElements:S,pageCount:A,assignEngineersModal:D,loadingUserListForAssignUserModal:w,initialAddUserValue:{userRoles:[$.Engineer],companyId:(X=(W=e==null?void 0:e.userDetail)==null?void 0:W.company)==null?void 0:X.id},isModalVisible:M,showAddBtn:ue,pageChange:i,deleteEngineer:K,onSearch:_,onAdd:U,view:d,onClickAdd:de,isAdmin:j,onToggleCheckAll:r,searchTable:H,onRemove:ge,formatDate:_e,onSort:C,toggleModal:P}}}),Se={class:"card h-100 my-8 user-overview"},Ce={class:"card-header py-4 align-items-center"},Ue={class:"card-toolbar gap-3 ms-auto"},Ee={class:"d-flex align-items-center"},Ie={class:"svg-icon svg-icon-2"},$e={class:"btn btn-flex btn-sm btn-success btn-export-success"},Be={class:"svg-icon svg-icon-2 rotate-90"},Te={key:0,class:"text-center p-5"},Me={key:2},Le={class:"table-responsive mt-3 mx-8"},Pe={class:"table table-row-dashed table-row-gray-300 align-middle gs-0 gy-2"},Re={class:"fw-bold text-gray-400"},Fe={key:0},Ve={class:"form-check form-check-sm form-check-custom form-check-solid"},Ne={key:1},Oe={key:0},He={class:"form-check form-check-sm form-check-custom form-check-solid"},Ye=["value"],je={class:"d-flex align-items-center"},qe={class:"symbol symbol-45px symbol-circle me-5"},Je=["src"],ze={class:"d-flex justify-content-start flex-column"},Ge={class:"text-gray-400 fw-semibold d-block fs-5"},Ke={class:"text-gray-600 fw-semibold d-block fs-5"},Qe={class:"text-gray-600 fw-semibold d-block fs-5"},We={class:"text-gray-600 fw-semibold d-block fs-5"},Xe={key:1},Ze={class:"d-flex align-items-center"},xe=["onClick"],es={class:"svg-icon svg-icon-3"},ss=["onClick"],ns={class:"svg-icon svg-icon-3 text-danger"},ts={class:"d-flex flex-wrap align-items-center mb-5 mx-8"},as={key:0,class:"text-gray-700 fw-semibold fs-6 me-auto"};function os(e,t,V,T,k,N){var O,P;const A=b("SvgIcon"),S=b("el-icon"),m=b("el-input"),g=b("el-form-item"),w=b("el-form"),f=b("inline-svg"),v=b("el-empty"),y=b("TableHeader"),D=b("router-link"),M=b("TablePagination"),L=b("AssignUserModal");return l(),c(G,null,[n("div",Se,[n("div",Ce,[t[7]||(t[7]=n("div",null,[n("h1",{class:"mb-0 me-4 text-gray-900"},"Assigned Engineers")],-1)),n("div",Ue,[h(w,{onSubmit:fe(e.searchTable,["prevent"])},{default:R(()=>[n("div",Ee,[h(g,{class:"mb-0"},{default:R(()=>[h(m,{class:"w-250px",placeholder:"Search",modelValue:e.search,"onUpdate:modelValue":t[0]||(t[0]=s=>e.search=s),name:"search",size:"large"},{prefix:R(()=>[h(S,{class:"el-input__icon"},{default:R(()=>[n("span",Ie,[h(A,{icon:"searchIcon"})])]),_:1})]),_:1},8,["modelValue"])]),_:1})])]),_:1},8,["onSubmit"]),e.checkedRows.length!==0?(l(),c("button",{key:0,class:"btn btn-flex btn-sm btn-danger",type:"button",onClick:t[1]||(t[1]=(...s)=>e.onRemove&&e.onRemove(...s))}," Remove ")):E("",!0),e.showAddBtn()?(l(),c("button",{key:1,class:"btn btn-flex btn-sm btn-primary",type:"button",onClick:t[2]||(t[2]=(...s)=>e.onClickAdd&&e.onClickAdd(...s))}," Add ")):E("",!0),n("button",$e,[n("span",Be,[h(f,{src:"/media/icons/duotune/arrows/arr092.svg"})]),t[6]||(t[6]=F(" Export "))])])]),e.loading?(l(),c("div",Te,t[8]||(t[8]=[n("div",{class:"spinner-border text-primary",role:"status"},[n("span",{class:"sr-only"},"Loading...")],-1)]))):e.assignedEngineerList.length===0?(l(),z(v,{key:1,description:"No Data"})):(l(),c("div",Me,[n("div",Le,[n("table",Pe,[n("thead",null,[n("tr",Re,[h(y,{headers:e.tableHeader,sortBy:e.sortParams.sortBy,sortDirection:e.sortParams.sortDirection,onSort:e.onSort},{customHeader:R(({header:s})=>[s.label===""?(l(),c("div",Fe,[n("div",Ve,[x(n("input",{class:"form-check-input",type:"checkbox","onUpdate:modelValue":t[3]||(t[3]=C=>e.checkAll=C),onChange:t[4]||(t[4]=(...C)=>e.onToggleCheckAll&&e.onToggleCheckAll(...C))},null,544),[[ee,e.checkAll]])])])):(l(),c("div",Ne,I(s.label),1))]),_:1},8,["headers","sortBy","sortDirection","onSort"])])]),n("tbody",null,[(l(!0),c(G,null,re(e.assignedEngineerList,s=>(l(),c("tr",{key:s.id},[e.isAdmin()?(l(),c("td",Oe,[n("div",He,[x(n("input",{class:"form-check-input widget-9-check",type:"checkbox",value:s.id,"onUpdate:modelValue":t[5]||(t[5]=C=>e.checkedRows=C)},null,8,Ye),[[ee,e.checkedRows]])])])):E("",!0),n("td",null,[n("div",je,[n("div",qe,[n("img",{src:(s==null?void 0:s.avatar)||"/media/avatars/blank.png",alt:""},null,8,Je)]),n("div",ze,[h(D,{to:`/users/${s==null?void 0:s.id}`,class:"text-dark fw-bold text-hover-primary fs-6"},{default:R(()=>[F(I(`${s==null?void 0:s.firstName} ${s==null?void 0:s.lastName}`),1)]),_:2},1032,["to"]),n("span",Ge,I(s==null?void 0:s.email),1)])])]),n("td",null,[n("span",Ke,I(s==null?void 0:s.mobilePhone),1)]),n("td",null,[n("span",Qe,I(s==null?void 0:s.officePhone),1)]),n("td",null,[n("span",We,I(e.formatDate(s==null?void 0:s.assignedDate,"MMM DD, YYYY")),1)]),e.isAdmin()?(l(),c("td",Xe,[n("div",Ze,[n("button",{class:"btn btn-icon btn-sm btn-blue me-3",onClick:()=>e.view((s==null?void 0:s.id)??"")},[n("span",es,[h(A,{icon:"eyeIcon"})])],8,xe),n("button",{class:"btn btn-icon btn-sm btn-blue me-3",onClick:C=>e.deleteEngineer(s==null?void 0:s.id)},[n("span",ns,[h(A,{icon:"trashIcon"})])],8,ss)])])):E("",!0)]))),128))])])]),n("div",ts,[(O=e.assignedEngineerList)!=null&&O.length?(l(),c("div",as,I(`Showing ${(e.currentPage-1)*10+1} to ${(P=e.assignedEngineerList)==null?void 0:P.length} of ${e.totalElements} entries`),1)):E("",!0),e.pageCount>=1?(l(),z(M,{key:1,"total-pages":e.pageCount,total:e.totalElements,"per-page":10,"current-page":e.currentPage,onPageChange:e.pageChange},null,8,["total-pages","total","current-page","onPageChange"])):E("",!0)])]))]),h(L,{ref:"assignEngineersModal",title:"Assign Engineers",userList:e.userListForAssignUserModal,onAdd:e.onAdd,onSearch:e.onSearch,loadingSearch:e.loadingUserListForAssignUserModal,initialAddUserValue:e.initialAddUserValue,isVisible:e.isModalVisible,close:e.toggleModal},null,8,["userList","onAdd","onSearch","loadingSearch","initialAddUserValue","isVisible","close"])],64)}const ls=ie(pe,[["render",os]]),is=te({name:"user-detail",components:{SvgIcon:ne,EngineerList:ls,Overview:me,UserInfo:be},props:{userDetail:{type:Object,required:!0},reloadUserData:{type:Function,required:!0}},setup(e){const V=(()=>{var i,r;return(r=(i=e==null?void 0:e.userDetail)==null?void 0:i.roles)!=null&&r.find(d=>(d==null?void 0:d.value)===$.Supervisor)&&!J.isJustEngineer()?[{value:"overview",key:"Overview"},{value:"engineers",key:"Engineers"}]:[{value:"overview",key:"Overview"}]})(),T=ce(),k=u(1),N=u(5),A=u(50),S=le(),m=oe(),g=u(!1),w=u(!1),f=u(V[0].value),v=u(!1);ae(()=>{j()&&!P()&&y()});const y=async()=>{T.getInvitedUser({callback:{onSuccess:i=>{Array.isArray(i)&&(i!=null&&i.some(r=>{var d;return r.id===((d=e==null?void 0:e.userDetail)==null?void 0:d.id)}))&&(w.value=!0)}}})},D=i=>{k.value=i},M=i=>{i===q.Active?B.alert("Are you sure you want to activate this user account?",{confirmButtonText:"Yes, Activate it!",cancelButtonText:"No, cancel!",confirmButtonClass:"btn fw-bold btn-primary btn-sm",cancelButtonClass:"btn fw-bold btn-blue btn-sm",callback:{onConfirmed:()=>{var r;Y((r=e==null?void 0:e.userDetail)==null?void 0:r.id,i)}}},"warning"):B.alert("Are you sure you want to deactivate this user account?",{confirmButtonText:"Yes, Deactivate it!",cancelButtonText:"No, cancel!",confirmButtonClass:"btn fw-bold btn-light-danger btn-sm",cancelButtonClass:"btn fw-bold btn-blue btn-sm",callback:{onConfirmed:()=>{var r;Y((r=e==null?void 0:e.userDetail)==null?void 0:r.id,i)}}},"warning")},L=()=>{var i,r,d,U;return!!(((r=(i=e==null?void 0:e.userDetail)==null?void 0:i.roles)==null?void 0:r.length)===1&&((U=(d=e==null?void 0:e.userDetail)==null?void 0:d.roles)!=null&&U.find(_=>(_==null?void 0:_.value)===$.Engineer)))},O=async()=>{var i,r,d,U,_;(i=e==null?void 0:e.userDetail)!=null&&i.email&&(v.value=!0,T.inviteUsers({params:{emails:[e.userDetail.email],userRoles:((d=(r=e.userDetail)==null?void 0:r.roles)==null?void 0:d.map(H=>H.value))||[],companyId:((_=(U=e.userDetail)==null?void 0:U.company)==null?void 0:_.id)||""},callback:{onSuccess:H=>{B.toast("Invited successfully","success","top-right")},onFinish:()=>{v.value=!1}}}))},P=()=>S.name==="my-profile",s=()=>{var i,r,d,U,_;return((r=(i=S.params)==null?void 0:i.id)==null?void 0:r.toString())===((d=J.getUserInfo())==null?void 0:d.id)&&!!((_=(U=e==null?void 0:e.userDetail)==null?void 0:U.roles)!=null&&_.find(H=>H.value===$.SystemAdmin))},C=i=>{const r=i.target;f.value=r.getAttribute("data-tab-index")},Y=(i,r)=>{g.value=!0,T.updateUserProfile({id:i,params:{status:r},callback:{onSuccess:()=>{var d;(d=e==null?void 0:e.reloadUserData)==null||d.call(e),r===q.Active?B.resultAlert("Account is activated!","success"):B.resultAlert("Account is deactivated!.","success")},onFinish:()=>{g.value=!1}}})};return{sendingInvitation:v,resendInvitation:w,submitting:g,UserStatus:q,currentPage:k,totalElements:A,pageCount:N,route:S,tabIndex:f,tabs:V,back:()=>{m.go(-1)},isOwner:P,pageChange:D,isAdmin:j,setActiveTab:C,promptChangeAccountStatus:M,userProfileIsEngineer:L,isSystemAdmin:s,sendInvitation:O}}}),rs={class:"card h-100 my-8 user-overview"},cs={class:"card"},ds={class:"card-body pt-9 pb-0"},us={class:"d-flex my-4 gap-3"},gs={key:0},vs={key:1,class:"indicator-progress"},bs={key:0},ms={class:"svg-icon svg-icon-1 me-1"},fs={key:1,class:"indicator-progress"},hs={key:0},ys={class:"svg-icon svg-icon-1"},ks={key:1,class:"indicator-progress"},As={class:"d-flex overflow-auto h-55px border-top"},ws={class:"nav nav-stretch nav-line-tabs nav-line-tabs-2x border-transparent fs-5 fw-bold flex-nowrap"},Ds={class:"nav-item"},_s=["data-tab-index"];function ps(e,t,V,T,k,N){var w;const A=b("inline-svg"),S=b("UserInfo"),m=b("Overview"),g=b("EngineerList");return l(),c(G,null,[he(e.$slots,"pageHeader"),n("div",rs,[n("div",cs,[n("div",ds,[h(S,{userDetail:e.userDetail},{default:R(()=>{var f,v;return[n("div",us,[e.resendInvitation?(l(),c("button",{key:0,class:"btn btn-flex btn-sm btn-success",onClick:t[0]||(t[0]=(...y)=>e.sendInvitation&&e.sendInvitation(...y))},[e.sendingInvitation?(l(),c("span",vs,t[5]||(t[5]=[F(" Please wait... "),n("span",{class:"spinner-border spinner-border-sm align-middle ms-2"},null,-1)]))):(l(),c("span",gs," Resend Invitation "))])):E("",!0),((f=e.userDetail)==null?void 0:f.status)===e.UserStatus.Inactive?(l(),c("button",{key:1,class:"btn btn-sm btn-primary",type:"button",onClick:t[1]||(t[1]=y=>e.promptChangeAccountStatus(e.UserStatus.Active))},[e.submitting?(l(),c("span",fs,t[7]||(t[7]=[F(" Please wait... "),n("span",{class:"spinner-border spinner-border-sm align-middle ms-2"},null,-1)]))):(l(),c("span",bs,[n("span",ms,[h(A,{src:"media/icons/duotune/general/unlock.svg"})]),t[6]||(t[6]=F(" Active Account"))]))])):((v=e.userDetail)==null?void 0:v.status)===e.UserStatus.Active?(l(),c("button",{key:2,class:"btn btn-sm btn-danger",type:"button",onClick:t[2]||(t[2]=y=>e.promptChangeAccountStatus(e.UserStatus.Inactive))},[e.submitting?(l(),c("span",ks,t[9]||(t[9]=[F(" Please wait... "),n("span",{class:"spinner-border spinner-border-sm align-middle ms-2"},null,-1)]))):(l(),c("span",hs,[n("span",ys,[h(A,{src:"media/icons/duotune/general/lock.svg"})]),t[8]||(t[8]=F(" Deactive Account "))]))])):E("",!0),e.isOwner()?E("",!0):(l(),c("button",{key:3,type:"button",class:"btn text-gray-700 btn-sm btn-blue",onClick:t[3]||(t[3]=(...y)=>e.back&&e.back(...y))}," Back "))])]}),_:1},8,["userDetail"]),n("div",As,[n("ul",ws,[(l(!0),c(G,null,re(e.tabs,f=>(l(),c("li",Ds,[n("div",{class:ye(["nav-link cursor-pointer text-active-primary fw-semibold text-hover-primary fs-5",{active:e.tabIndex===f.value}]),"data-bs-toggle":"tab",onClick:t[4]||(t[4]=v=>e.setActiveTab(v)),"data-tab-index":f.value,role:"tab"},I(f.key),11,_s)]))),256))])])])])]),e.tabIndex===e.tabs[0].value?(l(),z(m,{key:0,hideRole:e.isSystemAdmin(),userDetail:e.userDetail,reloadUserData:e.reloadUserData},null,8,["hideRole","userDetail","reloadUserData"])):e.tabIndex===e.tabs[1].value&&((w=e.userDetail)!=null&&w.id)?(l(),z(g,{key:1,userDetail:e.userDetail},null,8,["userDetail"])):E("",!0)],64)}const Ls=ie(is,[["render",ps]]);export{Ls as U};
