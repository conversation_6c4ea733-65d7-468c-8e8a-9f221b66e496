<template>
  <el-form
    @submit.prevent="submit"
    :model="targetData"
    :rules="rules"
    ref="formRef"
    class="bg-minicard-background text-minicard-text-light rounded-xl p-4 h-auto w-full"
  >
    <div v-if="loading" class="text-center">
      <div class="spinner-border text-primary" role="status">
        <span class="sr-only">Loading...</span>
      </div>
    </div>
    <div v-else class="h-auto w-full">
      <div class="flex flex-row items-center w-full justify-start gap-4">
        <button
          type="button"
          class="bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover rounded-md px-4 py-2 font-semibold"
          @click="cancel"
          :disabled="isFormDirty() !== true || submitting"
        >
          Cancel
        </button>
        <button
          class="bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover rounded-md px-4 py-2 font-semibold"
          type="submit"
          :disabled="isFormDirty() !== true || submitting"
        >
          <span v-if="!submitting" class="indicator-label"> Save </span>
          <span v-if="submitting" class="indicator-progress">
            Please wait...
            <span
              class="spinner-border spinner-border-sm align-middle ms-2"
            ></span>
          </span>
        </button>
      </div>
      <div class="h-auto w-full flex flex-col gap-3 md:flex-row">
        <div class="h-auto w-full flex flex-col gap-1 md:justify-between">
          <label class="font-semibold">Well name</label>
          <el-form-item prop="nameOrNo">
            <el-input
              :value="wellInfo?.nameOrNo"
              placeholder=""
              name="nameOrNo"
              disabled
            ></el-input>
          </el-form-item>
        </div>
        <div v-if="isSystemAdmin" class="h-auto w-full flex flex-col gap-1">
          <label class="font-semibold">Company</label>
          <el-form-item>
            <el-input :value="wellInfo?.company?.name" disabled></el-input>
          </el-form-item>
        </div>
        <div class="h-auto w-full flex flex-col gap-1 md:justify-between">
          <label class="font-semibold">
            Date Time<span class="text-danger-active font-light">*</span>
          </label>
          <el-form-item prop="reportedAt">
            <el-date-picker
              type="datetime"
              v-model="targetData.reportedAt"
              placeholder="MM/DD/YYYY"
              format="MM/DD/YYYY HH:mm"
              formatValue="YYYY-MM-DD HH:mm:ss"
              name="reportedAt"
              :disabled="dailyReportId ? true : false"
            />
          </el-form-item>
        </div>
        <div class="h-auto w-full flex flex-col gap-1 md:justify-between">
          <label class="font-semibold">
            Engineers
            <span class="text-danger-active font-light">*</span></label
          >
          <el-form-item prop="engineerId">
            <el-select-v2
              v-model="targetData.engineerId"
              :options="engineerList"
              placeholder="Search Engineer"
              filterable
              clearable
              name="engineerId"
            />
          </el-form-item>
        </div>
      </div>
      <div class="h-auto w-full flex flex-col gap-1 md:justify-between">
        <label class="font-semibold">Activity</label>
        <el-form-item prop="activity">
          <el-input
            v-model="targetData.activity"
            type="textarea"
            :rows="2"
            name="activity"
            placeholder=""
          />
        </el-form-item>
      </div>
      <div class="h-auto w-full flex flex-col gap-3 md:flex-row">
        <div class="h-auto w-full flex flex-col gap-1 md:justify-between">
          <label class="font-semibold"
            ><span>MD (Measured Depth) (ft)</span>
            <el-popover placement="top" :width="400" trigger="hover">
              <template #reference>
                <i class="fas fa-exclamation-circle ms-2 fs-7"></i>
              </template>
              <span>
                Measured Depth represents the length of the wellbore from the
                surface to the current drilling depth. It is an input and
                doesn't require a calculation.
              </span>
            </el-popover></label
          >
          <el-form-item prop="measuredDepth">
            <el-input
              type="number"
              :controls="false"
              step="any"
              v-model="targetData.measuredDepth"
              placeholder=""
              name="measuredDepth"
            ></el-input>
          </el-form-item>
        </div>
        <div class="h-auto w-full flex flex-col gap-1 md:justify-between">
          <label class="font-semibold"
            ><span>TVD (True Vertical Depth) (ft)</span>
            <el-popover placement="top" :width="400" trigger="hover">
              <template #reference>
                <i class="fas fa-exclamation-circle ms-2 fs-7"></i>
              </template>
              <span>
                True Vertical Depth represents the vertical distance from the
                wellbore to the target or the depth below the surface at which
                the wellbore reaches the target zone. TVD may require
                calculation based on the well's inclination and azimuth.
              </span>
            </el-popover></label
          >
          <el-form-item prop="trueVerticalDepth">
            <el-input
              type="number"
              :controls="false"
              step="any"
              v-model="targetData.trueVerticalDepth"
              placeholder=""
              name="trueVerticalDepth"
            ></el-input>
          </el-form-item>
        </div>
        <div class="h-auto w-full flex flex-col gap-1 md:justify-between">
          <label class="font-semibold"
            ><span>Inc. (Inclination) (deg)</span>
            <el-popover placement="top" :width="400" trigger="hover">
              <template #reference>
                <i class="fas fa-exclamation-circle ms-2 fs-7"></i>
              </template>
              <span>
                Inclination is the angle at which the wellbore deviates from
                vertical. It is an input and doesn't require a calculation.
              </span>
            </el-popover></label
          >
          <el-form-item prop="inclination">
            <el-input
              type="number"
              :controls="false"
              step="any"
              v-model="targetData.inclination"
              placeholder=""
              name="inclination"
            ></el-input>
          </el-form-item>
        </div>
        <div class="h-auto w-full flex flex-col gap-1 md:justify-between">
          <label class="font-semibold"
            ><span>Azi (Azimuth) (deg)</span>
            <el-popover placement="top" :width="400" trigger="hover">
              <template #reference>
                <i class="fas fa-exclamation-circle ms-2 fs-7"></i>
              </template>
              <span>
                Azimuth is the compass direction in which the wellbore is
                drilled horizontally. It is an input and doesn't require a
                calculation.
              </span>
            </el-popover></label
          >
          <el-form-item prop="azimuth">
            <el-input
              type="number"
              :controls="false"
              step="any"
              v-model="targetData.azimuth"
              placeholder=""
              name="azimuth"
            ></el-input>
          </el-form-item>
        </div>
      </div>
      <div class="h-auto w-full flex flex-col gap-3 md:flex-row">
        <div class="h-auto w-full flex flex-col gap-1 md:justify-between">
          <label class="font-semibold"
            ><span>WOB (Weight on Bit) (lbf)</span>
            <el-popover placement="top" :width="400" trigger="hover">
              <template #reference>
                <i class="fas fa-exclamation-circle ms-2 fs-7"></i>
              </template>
              <span>
                Weight on Bit is the downward force applied to the drill bit to
                aid in drilling. It is an input and doesn't require a
                calculation.
              </span>
            </el-popover></label
          >
          <el-form-item prop="weightOnBit">
            <el-input
              type="number"
              :controls="false"
              step="any"
              v-model="targetData.weightOnBit"
              placeholder=""
              name="weightOnBit"
            ></el-input>
          </el-form-item>
        </div>
        <div class="h-auto w-full flex flex-col gap-1 md:justify-between">
          <label class="font-semibold"
            ><span>Rot. Wt. (Rotary Weight) (lbf)</span>
            <el-popover placement="top" :width="400" trigger="hover">
              <template #reference>
                <i class="fas fa-exclamation-circle ms-2 fs-7"></i>
              </template>
              <span>
                Rotary Weight represents the weight applied to the drill bit due
                to the rotation of the drill string. It is an input and doesn't
                require a calculation.
              </span>
            </el-popover></label
          >
          <el-form-item prop="rotaryWeight">
            <el-input
              type="number"
              :controls="false"
              step="any"
              v-model="targetData.rotaryWeight"
              placeholder=""
              name="rotaryWeight"
            ></el-input>
          </el-form-item>
        </div>
        <div class="h-auto w-full flex flex-col gap-1 md:justify-between">
          <label class="font-semibold"
            ><span>S/O Wt. (Standoff Weight) (lbf)</span>
            <el-popover placement="top" :width="400" trigger="hover">
              <template #reference>
                <i class="fas fa-exclamation-circle ms-2 fs-7"></i>
              </template>
              <span>
                Standoff Weight is the weight applied to the drill bit due to
                the hydraulic pressure exerted by the drilling mud. It is an
                input and doesn't require a calculation.
              </span>
            </el-popover></label
          >
          <el-form-item prop="standOffWeight">
            <el-input
              type="number"
              :controls="false"
              step="any"
              v-model="targetData.standOffWeight"
              placeholder=""
              name="standOffWeight"
            ></el-input>
          </el-form-item>
        </div>
        <div class="h-auto w-full flex flex-col gap-1 md:justify-between">
          <label class="font-semibold"
            ><span>P/U Wt. (Pull-Up Weight) (lbf)</span>
            <el-popover placement="top" :width="400" trigger="hover">
              <template #reference>
                <i class="fas fa-exclamation-circle ms-2 fs-7"></i>
              </template>
              <span>
                Pull-Up Weight is the weight applied to the drill bit during the
                tripping process when the drill string is pulled out of the
                hole. It is an input and doesn't require a calculation.
              </span>
            </el-popover></label
          >
          <el-form-item prop="pullUpWeight">
            <el-input
              type="number"
              :controls="false"
              step="any"
              v-model="targetData.pullUpWeight"
              placeholder=""
              name="pullUpWeight"
            ></el-input>
          </el-form-item>
        </div>
      </div>
      <div class="h-auto w-full flex flex-col gap-3 md:flex-row">
        <div class="h-auto w-full flex flex-col gap-1 md:justify-between">
          <label class="font-semibold"
            ><span>RPM (Revolutions Per Minute) (rpm)</span>
            <el-popover placement="top" :width="400" trigger="hover">
              <template #reference>
                <i class="fas fa-exclamation-circle ms-2 fs-7"></i>
              </template>
              <span>
                RPM represents the rotational speed of the drill bit. It is an
                input and doesn't require a calculation.
              </span>
            </el-popover></label
          >
          <el-form-item prop="revolutionsPerMinute">
            <el-input
              type="number"
              :controls="false"
              step="any"
              v-model="targetData.revolutionsPerMinute"
              placeholder=""
              name="revolutionsPerMinute"
            ></el-input>
          </el-form-item>
        </div>
        <div class="h-auto w-full flex flex-col gap-1 md:justify-between">
          <label class="font-semibold"
            ><span>ROP (Rate of Penetration) (ft/hr)</span>
            <el-popover placement="top" :width="400" trigger="hover">
              <template #reference>
                <i class="fas fa-exclamation-circle ms-2 fs-7"></i>
              </template>
              <span>
                Rate of Penetration is the speed at which the drill bit advances
                in feet per hour. It is a calculation based on the depth drilled
                (ft) and the drilling interval (time).
              </span>
            </el-popover></label
          >
          <el-form-item prop="rateOfPenetration">
            <el-input
              type="number"
              :controls="false"
              step="any"
              v-model="targetData.rateOfPenetration"
              placeholder=""
              name="rateOfPenetration"
            ></el-input>
          </el-form-item>
        </div>
        <div class="h-auto w-full flex flex-col gap-1 md:justify-between">
          <label class="font-semibold"
            ><span>Drilling Interval</span>
            <el-popover placement="top" :width="400" trigger="hover">
              <template #reference>
                <i class="fas fa-exclamation-circle ms-2 fs-7"></i>
              </template>
              <span>
                The drilling interval is the time it takes to drill a certain
                depth. It is an input and can be used in the ROP calculation.
              </span>
            </el-popover></label
          >
          <el-form-item prop="drillingInterval">
            <el-input
              v-model="targetData.drillingInterval"
              placeholder=""
              name="drillingInterval"
            ></el-input>
          </el-form-item>
        </div>
        <div class="h-auto w-full flex flex-col gap-1 md:justify-between">
          <label class="font-semibold"
            ><span>Formation</span>
            <el-popover placement="top" :width="400" trigger="hover">
              <template #reference>
                <i class="fas fa-exclamation-circle ms-2 fs-7"></i>
              </template>
              <span>
                Formation refers to the geological strata or rock types
                encountered while drilling. It is an input and doesn't require a
                calculation.
              </span>
            </el-popover></label
          >
          <el-form-item prop="formation">
            <el-input
              v-model="targetData.formation"
              placeholder=""
              name="formation"
            ></el-input>
          </el-form-item>
        </div>
      </div>
      <div class="h-auto w-full flex flex-col gap-3 md:flex-row">
        <div class="h-auto w-full flex flex-col gap-1 md:justify-between">
          <label class="font-semibold"
            ><span>Depth Drilled (ft)</span>
            <el-popover placement="top" :width="400" trigger="hover">
              <template #reference>
                <i class="fas fa-exclamation-circle ms-2 fs-7"></i>
              </template>
              <span>
                Depth Drilled represents the amount of wellbore drilled during a
                specific drilling operation. It is an input and can be used in
                the ROP calculation.
              </span>
            </el-popover></label
          >
          <el-form-item prop="depthDrilled">
            <el-input
              type="number"
              :controls="false"
              step="any"
              v-model="targetData.depthDrilled"
              placeholder=""
              name="depthDrilled"
              disabled
            ></el-input>
          </el-form-item>
        </div>
        <div class="h-auto w-full flex flex-col gap-1 md:justify-between">
          <label class="font-semibold"
            ><span>Total String Length = MD</span>
            <el-popover placement="top" :width="400" trigger="hover">
              <template #reference>
                <i class="fas fa-exclamation-circle ms-2 fs-7"></i>
              </template>
              <span>
                Total String Length is the overall length of the drill string,
                which is typically equal to the Measured Depth (MD). It is an
                input and doesn't require a separate calculation.
              </span>
            </el-popover></label
          >
          <el-form-item prop="totalStringLength">
            <el-input
              type="number"
              :controls="false"
              step="any"
              v-model="targetData.totalStringLength"
              placeholder=""
              name="totalStringLength"
            ></el-input>
          </el-form-item>
        </div>
        <div class="h-auto w-full flex flex-col gap-1 md:justify-between">
          <label class="font-semibold"
            ><span>Total Length = MD</span>
            <el-popover placement="top" :width="400" trigger="hover">
              <template #reference>
                <i class="fas fa-exclamation-circle ms-2 fs-7"></i>
              </template>
              <span>
                Total Length can refer to the overall length of the wellbore
                from the surface to the target zone. It may require a
                calculation based on MD and TVD: Total Length = √(MD^2 + TVD^2)
              </span>
            </el-popover></label
          >
          <el-form-item prop="totalLength">
            <el-input
              type="number"
              :controls="false"
              step="any"
              v-model="targetData.totalLength"
              placeholder=""
              name="totalLength"
            ></el-input>
          </el-form-item>
        </div>
        <div class="h-auto w-full flex flex-col gap-1 md:justify-between">
          <label class="font-semibold"
            ><span>TFA (Total Flow Area) (in^2)</span>
            <el-popover placement="top" :width="400" trigger="hover">
              <template #reference>
                <i class="fas fa-exclamation-circle ms-2 fs-7"></i>
              </template>
              <span>
                "Total Flow Area" measured in square inches (in^2). Total Flow
                Area in this context typically pertains to the collective
                cross-sectional area available for the passage of drilling mud
                or fluids through the components of the drill string, such as
                drill pipes, drill bits, and nozzles.<br /><br />

                Total Flow Area is an essential parameter to consider when
                designing and optimizing the drill string and associated
                equipment. It affects the flow rate, fluid velocity, and
                pressure drop within the drill string. Properly managing TFA is
                important for maintaining effective drilling fluid circulation,
                ensuring efficient cooling of the drill bit, and carrying
                drilled cuttings and formation samples to the surface for mud
                logging and analysis. Adjusting TFA, for example by selecting
                appropriate nozzles or pipe sizes, can help optimize drilling
                performance and hole cleaning.
              </span>
            </el-popover></label
          >
          <el-form-item prop="tfa">
            <el-input
              type="number"
              :controls="false"
              step="any"
              v-model="targetData.tfa"
              placeholder=""
              name="tfa"
              disabled
            ></el-input>
          </el-form-item>
        </div>
      </div>
    </div>
  </el-form>
</template>

<script lang="ts">
import SvgIcon from "@/constants/SvgIcon.vue";
import { UserType } from "@/constants/user";
import { formatDate } from "@/utils/date";
import { setupNavigationGuard } from "@/utils/navigation-guard";
import AlertService from "@/services/AlertService";
import JwtService from "@/services/JwtService";
import { useDailyReportStore } from "@/stores/daily-report";
import { useUserStore } from "@/stores/user";
import { useWellInformationStore } from "@/stores/well-information";
import _ from "lodash";
import moment from "moment";
import { defineComponent, inject, onMounted, ref, watch } from "vue";
import { onBeforeRouteLeave, useRoute } from "vue-router";
import type { Provide } from "@/types/injection-types";

export default defineComponent({
  name: "well-information",
  components: {
    SvgIcon,
  },
  setup() {
    const route = useRoute();
    const userStore = useUserStore();
    const wellInfoStore = useWellInformationStore();
    const dailyReportStore = useDailyReportStore();
    const wellInfo = ref<any>();
    const engineerList = ref([]);
    const loading = ref(false);
    const submitting = ref(false);
    const latestReport = ref<any>();
    const wellId = route?.params?.id;
    const isFirstRender = ref(true);
    const dailyReportProvide = inject<Provide.DailyReport>("dailyReport");
    const initialForm = ref<Well.ReportInfo>({
      dailyReportId: dailyReportProvide?.getDailyReportId(),
      reportedAt: formatDate(moment().toDate(), "YYYY-MM-DD HH:mm:ss"),
      engineerId:
        !dailyReportProvide?.getDailyReportId() &&
        JwtService.checkRole(UserType.Engineer)
          ? JwtService.getUserInfo()?.id
          : null,
      activity: "",
      measuredDepth: null,
      trueVerticalDepth: null,
      inclination: null,
      azimuth: null,
      weightOnBit: null,
      rotaryWeight: null,
      standOffWeight: null,
      pullUpWeight: null,
      revolutionsPerMinute: null,
      rateOfPenetration: null,
      drillingInterval: "",
      formation: "",
      depthDrilled: null,
      totalStringLength: null,
      totalLength: null,
      tfa: null,
    });
    const targetData = ref<Well.ReportInfo>({ ...initialForm.value });
    const formRef = ref<null | HTMLFormElement>(null);
    const wellInformation = ref<any>();

    const rules = ref({
      reportedAt: [
        {
          required: true,
          message: "Please select Date",
          trigger: "change",
        },
      ],
      engineerId: [
        {
          required: true,
          message: "Please select Engineer",
          trigger: "change",
        },
      ],
    });

    onBeforeRouteLeave((to, from, next) => {
      setupNavigationGuard(to, from, next, submitting.value || isFormDirty());
    });

    watch(
      () => targetData.value.measuredDepth,
      (newValue) => {
        if (newValue && !isNaN(newValue)) {
          const newDepthDrilled =
            Number(targetData.value?.measuredDepth || 0) -
            Number(latestReport?.value?.wellInformation?.measuredDepth || 0);
          if (!isFirstRender.value || !dailyReportProvide?.getDailyReportId()) {
            initialForm.value = {
              ...initialForm.value,
              depthDrilled: newDepthDrilled,
            };
            targetData.value = {
              ...targetData.value,
              depthDrilled: newDepthDrilled,
            };
          } else {
            isFirstRender.value = false;
          }
        } else {
          initialForm.value = {
            ...initialForm.value,
            depthDrilled: null,
          };
          targetData.value = {
            ...targetData.value,
            depthDrilled: null,
          };
        }
      }
    );

    watch(
      () => dailyReportProvide?.getDailyReportId(),
      (newValue) => {
        if (newValue) {
          getDailyReportDetail();
        }
      }
    );

    onMounted(() => {
      getDetail();
      getEngineers();
      if (dailyReportProvide?.getDailyReportId()) {
        getDailyReportDetail();
      }
    });

    const getEngineers = async (): Promise<void> => {
      userStore.getUsers({
        params: {
          role: UserType.Engineer,
          page: 1,
          limit: 500,
        },
        callback: {
          onSuccess: (res: any) => {
            engineerList.value = res?.items.map((item: any) => {
              return {
                label: `${item?.firstName} ${item?.lastName}`,
                value: item.id,
              };
            });
          },
        },
      });
    };

    const getDailyReportDetail = async (): Promise<void> => {
      if (!dailyReportProvide?.getDailyReportId()) return;

      dailyReportStore.getDailyReportById({
        id: dailyReportProvide?.getDailyReportId(),
        callback: {
          onSuccess: (res: any) => {
            const wellInfo = res?.wellInformation;
            wellInformation.value = wellInfo;

            let data;

            if (wellInfo) {
              data = {
                ...wellInfo,
                dailyReportId: dailyReportProvide?.getDailyReportId(),
                engineerId: wellInfo?.engineer?.id,
              };
            } else {
              data = {
                ...targetData.value,
                engineerId: JwtService.checkRole(UserType.Engineer)
                  ? JwtService.getUserInfo()?.id
                  : null,
              };
            }

            initialForm.value = JSON.parse(JSON.stringify(data));
            targetData.value = JSON.parse(JSON.stringify(data));
          },
        },
      });
    };

    const getDetail = async () => {
      if (!wellId) return;
      loading.value = true;

      dailyReportStore.getWellInformationTab({
        wellId: wellId as string,
        callback: {
          onSuccess: (res: any) => {
            const [wellInfoRes, latestReportRes] = res;
            wellInfo.value = wellInfoRes?.data?.data;
            latestReport.value = latestReportRes?.data?.data;
          },
          onFinish: () => {
            loading.value = false;
          },
        },
      });
    };

    const createWellInfo = async (params: any): Promise<void> => {
      wellInfoStore.createWellInformation({
        params: params,
        callback: {
          onSuccess: (_res: any) => {
            // created, next tab??
            getDetail();
            if (dailyReportProvide?.getDailyReportId()) {
              getDailyReportDetail();
            }
          },
          onFinish: () => {
            submitting.value = false;
          },
        },
      });
    };

    const updateWellInfo = async (params: any): Promise<void> => {
      submitting.value = true;
      wellInfoStore.updateWellInformation({
        id: wellInformation?.value?.id,
        params: params,
        callback: {
          onSuccess: (_res: any) => {
            // updated, next tab??
            getDetail();
            if (dailyReportProvide?.getDailyReportId()) {
              getDailyReportDetail();
            }
          },
          onFinish: () => {
            submitting.value = false;
          },
        },
      });
    };

    const isFormDirty = () => {
      return !_.isEqual(targetData.value, initialForm.value);
    };

    const isValidForm = async (): Promise<boolean> => {
      const result = await formRef?.value?.validate((valid: boolean) => {
        return valid;
      });

      return result;
    };

    const submit = () => {
      if (!formRef.value) {
        return;
      }

      formRef.value.validate((valid: boolean) => {
        if (valid) {
          if (submitting.value) return;
          if (!isFormDirty()) return;
          const params = {
            ...targetData.value,
            measuredDepth: Number(targetData.value.measuredDepth),
            trueVerticalDepth: Number(targetData.value.trueVerticalDepth),
            inclination: Number(targetData.value.inclination),
            azimuth: Number(targetData.value.azimuth),
            weightOnBit: Number(targetData.value.weightOnBit),
            rotaryWeight: Number(targetData.value.rotaryWeight),
            standOffWeight: Number(targetData.value.standOffWeight),
            pullUpWeight: Number(targetData.value.pullUpWeight),
            revolutionsPerMinute: Number(targetData.value.revolutionsPerMinute),
            rateOfPenetration: Number(targetData.value.rateOfPenetration),
            depthDrilled: targetData.value?.depthDrilled
              ? Number(targetData.value.depthDrilled)
              : null,
            totalStringLength: Number(targetData.value.totalStringLength),
            totalLength: Number(targetData.value.totalLength),
          };

          if (dailyReportProvide?.getDailyReportId()) {
            if (wellInformation.value) {
              updateWellInfo({
                ...params,
                dailyReportId: dailyReportProvide?.getDailyReportId(),
              });
            } else {
              createWellInfo({
                ...params,
                dailyReportId: dailyReportProvide?.getDailyReportId(),
              });
            }
          } else {
            submitting.value = true;
            dailyReportProvide?.createDailyReport({
              wellId: wellId as string,
              callback: {
                onSuccess: (res: string) => {
                  // updated, next tab??
                  createWellInfo({ ...params, dailyReportId: res });
                },
                onFailure: () => {
                  submitting.value = false;
                },
              },
            });
          }
        }
      });
    };

    const cancel = () => {
      AlertService.incompleteFormAlert(
        {
          onConfirmed: () => {
            targetData.value = JSON.parse(JSON.stringify(initialForm.value));
          },
        },
        "Cancel changes on this section?",
        "Yes"
      );
    };

    return {
      submit,
      cancel,
      isFormDirty,
      isValidForm,
      loading,
      submitting,
      wellInfo,
      rules,
      engineerList,
      formRef,
      targetData,
      isSystemAdmin: JwtService.checkRole(UserType.SystemAdmin),
      dailyReportId: route?.params?.dailyReportId,
    };
  },
});
</script>

<style scoped>
.card-header {
  min-height: auto !important;
  padding-top: 2rem;
}
</style>
