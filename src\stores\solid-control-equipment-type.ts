import ApiService from "../services/ApiService";
import { get, noop } from "lodash";
import { defineStore } from "pinia";
import { handleFailure } from "../utils/handleFailure";
import type { Callback } from "../types/common";

export const useSolidControlEquipmentTypeStore = defineStore(
  "solidControlEquipmentType",
  () => {
    const getSolidControlEquipmentTypes = async ({
      callback,
    }: {
      callback: Callback;
    }): Promise<void> => {
      const onSuccess = get(callback, "onSuccess", noop);
      const onFinish = get(callback, "onFinish", noop);

      try {
        ApiService.setHeader();
        const response = await ApiService.get("solidControlEquipmentTypes");
        onSuccess(response.data?.data || response.data);
      } catch (error) {
        handleFailure(error, callback);
      } finally {
        onFinish();
      }
    };

    return {
      getSolidControlEquipmentTypes,
    };
  }
);
