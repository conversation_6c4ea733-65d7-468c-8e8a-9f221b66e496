import { getOption } from "./option";
import { userRoleOptions, UserType } from "../constants/user";

export const getUserRoleList = (
  userRoles: { value: number }[]
): { value: UserType; key: string }[] => {
  return (
    userRoles.map((item) => {
      return {
        value: item?.value as UserType,
        key: getOption(item?.value, userRoleOptions)?.label || "",
      };
    }) || []
  );
};
