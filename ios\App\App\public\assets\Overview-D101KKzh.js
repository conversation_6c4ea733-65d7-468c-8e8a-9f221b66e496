import{P as j}from"./PageHeader-3hadTn26.js";import{T as q}from"./TablePagination-BmVxunEG.js";import{S as z}from"./SvgIcon-CMhyaXWN.js";import{S as U,a as w}from"./table-bhK9qpe4.js";import{f as G}from"./date-CvSHk5ED.js";import{d as J,q as l,x as K,A as B,R as Q,L as W,E as X,_ as Z,c as m,b as n,a as e,m as R,w as C,p as oo,B as T,F as H,k as eo,l as E,t as b,r as c,o as d}from"./index-CGNRhvz7.js";import{u as so}from"./company-oDyd0dWV.js";import{C as ao}from"./CompanyModal-Cba-MY3X.js";import"./d-left-arrow-079-B3YbfCzd.js";import"./handleFailure-DtTpu7r3.js";const to=J({name:"companies-overview",components:{PageHeader:j,SvgIcon:z,TablePagination:q,CompanyModal:ao},setup(){const s=so(),t=["Companies","Overview"],_=l([]),I=l(!1),$=l(0),V=l(0),u=l(1),r=l(!1),k=l(""),y=l(null),g=l(!1),v=l([]),i=l(w.ASC);K(()=>{f()}),B(u,()=>{f()}),B(i,()=>{f()});const S=Q(()=>i.value===w.ASC?"/media/icons/arrows/order-by-asc.svg":"/media/icons/arrows/order-by-des.svg"),L=()=>{i.value===w.ASC?i.value=w.DESC:i.value=w.ASC},f=async()=>{r.value=!0,s.getCompanies({params:{sortBy:U.Name,sortDirection:i.value,page:u.value,limit:10,keyword:k.value.trim()||null},callback:{onSuccess:a=>{v.value=[...a==null?void 0:a.items],$.value=a==null?void 0:a.totalPage,V.value=a==null?void 0:a.total,u.value=a==null?void 0:a.page},onFinish:()=>{r.value=!1}}})},A=()=>{u.value=1,f()},P=a=>{u.value=a},o=()=>{console.log(g.value),g.value=!g.value},x=a=>{var p;(p=y==null?void 0:y.value)==null||p.setId(a),g.value=!g.value};B(_,a=>{I.value=v.value.length!==0&&a.length===v.value.length});const M=a=>{var p;(p=a==null?void 0:a.target)!=null&&p.checked?_.value=v.value.map(h=>h.id):_.value=[]},N=a=>{X.deletionAlert({onConfirmed:()=>{D(a)}})},D=async a=>{r.value=!0,s.deleteCompanyById({id:a,callback:{onSuccess:p=>{f()},onFinish:()=>{r.value=!1}}})};return{sortDirection:i,loading:r,search:k,breadcrumbs:t,checkedRows:_,checkAll:I,companyList:v,currentPage:u,totalElements:V,pageCount:$,companyModal:y,isModalVisible:g,isSystemAdmin:W,arrowPath:S,formatDate:G,toggleModal:o,pageChange:P,deleteCompany:N,toggleEditCompany:x,onToggleCheckAll:M,onClickSort:L,searchCompanies:A,getCompanyList:f}}}),no={class:"bg-white h-auto w-11/12 overflow-y-scroll mx-auto my-4 rounded-xl flex flex-col items-center"},lo={class:"h-auto w-4/5 flex flex-col items-start my-4 px-8 gap-3"},ro={class:"svg-icon svg-icon-2"},co={key:0,class:"text-center p-5"},io={key:2,class:"h-auto w-11/12 overflow-x-scroll"},po={class:"font-bold text-gray-400 whitespace-nowrap"},uo={class:"p-4"},go={class:"h-auto w-full flex flex-row items-centerbg-blue gap-2"},fo=["src"],mo={class:"text-gray-600 font-semibold"},vo={class:"p-4"},yo={class:"w-36 p-4"},ho={key:0,class:"flex flex-row items-center gap-3"},Co=["src"],bo={class:"flex flex-col text-start overflow-auto"},_o={class:"text-gray-400 font-semibold truncate"},wo={key:0,class:"p-4"},ko={class:"text-gray-600 font-semibold whitespace-nowrap"},So={class:"p-4"},Ao={class:"h-auto w-full flex flex-row gap-2 items-center justify-evenly"},Po=["onClick"],xo=["onClick"],Mo={class:"text-danger"},No={class:"flex flex-col items-center my-5"},Do={key:0,class:"text-gray-700 font-semibold"};function Eo(s,t,_,I,$,V){var A,P;const u=c("PageHeader"),r=c("SvgIcon"),k=c("el-icon"),y=c("el-input"),g=c("el-form-item"),v=c("el-form"),i=c("el-empty"),S=c("router-link"),L=c("TablePagination"),f=c("CompanyModal");return d(),m(H,null,[n(u,{title:"Companies",breadcrumbs:s.breadcrumbs},null,8,["breadcrumbs"]),e("div",no,[e("div",lo,[t[4]||(t[4]=e("h1",{class:"text-gray-900 font-bold"},"Companies",-1)),n(v,{onSubmit:oo(s.searchCompanies,["prevent"])},{default:C(()=>[n(g,null,{default:C(()=>[n(y,{placeholder:"Search",modelValue:s.search,"onUpdate:modelValue":t[0]||(t[0]=o=>s.search=o),name:"search",size:"large"},{prefix:C(()=>[n(k,{class:"el-input__icon"},{default:C(()=>[e("span",ro,[n(r,{icon:"searchIcon"})])]),_:1})]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["onSubmit"]),e("button",{class:"bg-primary text-white rounded-md px-4 py-2 font-semibold flex flex-row items-center gap-2 justify-between",onClick:t[1]||(t[1]=(...o)=>s.toggleModal&&s.toggleModal(...o))},[n(r,{icon:"addIcon"}),t[3]||(t[3]=T(" New "))])]),s.loading?(d(),m("div",co,t[5]||(t[5]=[e("div",{class:"spinner-border text-primary",role:"status"},[e("span",{class:"sr-only"},"Loading...")],-1)]))):s.companyList.length===0?(d(),R(i,{key:1,description:"No Data"})):(d(),m("div",io,[e("table",null,[e("thead",null,[e("tr",po,[t[7]||(t[7]=e("th",{class:"p-4"},"COMPANY ID",-1)),e("th",uo,[e("div",go,[t[6]||(t[6]=e("span",null,"COMPANY NAME",-1)),e("span",{class:"h-5 w-5",onClick:t[2]||(t[2]=(...o)=>s.onClickSort&&s.onClickSort(...o))},[e("img",{src:s.arrowPath},null,8,fo)])])]),t[8]||(t[8]=e("th",{class:"p-4"},"COMPANY ADMIN",-1)),t[9]||(t[9]=e("th",{class:"p-4"},"CREATED DATE",-1)),t[10]||(t[10]=e("th",{class:"p-4"},"ACTIONS",-1))])]),e("tbody",null,[(d(!0),m(H,null,eo(s.companyList,o=>{var x,M,N,D,a,p;return d(),m("tr",{key:o.id,class:"font-bold text-gray-400 my-2 text-center border-b-[1px] border-grey-400 border-dashed"},[e("td",null,[e("span",mo,b((o==null?void 0:o.id)||""),1)]),e("td",vo,[n(S,{to:`/companies/${o.id}`,class:"text-dark fw-bold text-hover-primary"},{default:C(()=>[T(b(o==null?void 0:o.name),1)]),_:2},1032,["to"])]),e("td",yo,[o!=null&&o.users&&(o!=null&&o.users.length)?(d(),m("div",ho,[e("img",{class:"h-11 w-11 rounded-full",src:((M=(x=o==null?void 0:o.users)==null?void 0:x[0])==null?void 0:M.avatar)||"/media/avatars/blank.png",alt:"Avatar"},null,8,Co),e("div",bo,[n(S,{to:`/users/${(D=(N=o==null?void 0:o.users)==null?void 0:N[0])==null?void 0:D.id}`,class:"text-dark font-semi hover:text-primary"},{default:C(()=>{var h,Y,O,F;return[T(b(`${((Y=(h=o==null?void 0:o.users)==null?void 0:h[0])==null?void 0:Y.firstName)||""} ${((F=(O=o==null?void 0:o.users)==null?void 0:O[0])==null?void 0:F.lastName)||""}`),1)]}),_:2},1032,["to"]),e("span",_o,b(((p=(a=o==null?void 0:o.users)==null?void 0:a[0])==null?void 0:p.email)||""),1)])])):E("",!0)]),s.isSystemAdmin()?(d(),m("td",wo,[e("span",ko,b(s.formatDate(o==null?void 0:o.createdAt,"MMM DD, YYYY")),1)])):E("",!0),e("td",So,[e("div",Ao,[e("button",{class:"bg-grey-300 h-8 w-8 rounded-lg p-2",onClick:h=>s.toggleEditCompany(o==null?void 0:o.id)},[n(r,{icon:"newReportIcon"})],8,Po),e("button",{class:"bg-grey-300 h-8 w-8 rounded-lg px-1.5 py-0.5",onClick:h=>s.deleteCompany(o==null?void 0:o.id)},[e("span",Mo,[n(r,{icon:"trashIcon"})])],8,xo)])])])}),128))])])])),e("div",No,[(A=s.companyList)!=null&&A.length?(d(),m("div",Do,b(`Showing ${(s.currentPage-1)*10+1} to ${(P=s.companyList)==null?void 0:P.length} of ${s.totalElements} entries`),1)):E("",!0),s.pageCount>=1?(d(),R(L,{key:1,"total-pages":s.pageCount,total:s.totalElements,"per-page":10,"current-page":s.currentPage,onPageChange:s.pageChange},null,8,["total-pages","total","current-page","onPageChange"])):E("",!0)])]),n(f,{isVisible:s.isModalVisible,close:s.toggleModal,loadPage:s.getCompanyList,ref:"companyModal"},null,8,["isVisible","close","loadPage"])],64)}const Ho=Z(to,[["render",Eo]]);export{Ho as default};
