<template>
  <div
    v-if="isVisible"
    class="fixed top-0 right-0 bottom-0 left-0 z-40 flex items-center"
  >
    <!-- Dark overlay background -->
    <div class="absolute inset-0 bg-sidebar-background opacity-25"></div>
    <div
      class="relative bg-card-background text-card-text-light max-h-3/4 w-11/12 flex flex-col items-center gap-3 mx-auto my-6 p-5 rounded-xl overflow-y-scroll md:overflow-hidden md:text-lg lg:w-2/5 lg:h-auto"
    >
      <div class="flex flex-row h-auto w-full items-center justify-between">
        <h3 class="text-lg font-bold">
          {{ `${id ? "Edit Cased Hole" : "New Cased Hole"}` }}
        </h3>
        <span class="cursor-pointer" @click="closeModal">
          <SvgIcon icon="closeModalIcon" />
        </span>
      </div>

      <el-form
        id="cased_hole_form"
        @submit.prevent="submit"
        :model="targetData"
        :rules="rules"
        ref="formRef"
        class="h-auto w-full"
      >
        <div class="h-auto w-full flex flex-col gap-3 md:flex-row md:gap-4">
          <div class="h-auto w-full flex flex-col gap-3">
            <div class="h-auto w-full flex flex-col gap-1">
              <label class="font-bold"
                >Description<span class="text-danger-active font-light">*</span>
              </label>
              <el-form-item prop="description">
                <el-input
                  v-model="targetData.description"
                  placeholder=""
                  name="description"
                ></el-input>
              </el-form-item>
            </div>
            <div class="h-auto w-full flex flex-col gap-1">
              <label class="font-bold"
                >Top (Top Depth) (ft)<el-popover
                  placement="top"
                  :width="300"
                  trigger="hover"
                >
                  <template #reference>
                    <i class="fas fa-exclamation-circle ms-2 fs-7"></i>
                  </template>
                  <span
                    >The Top Depth refers to the depth at which the casing is
                    set or begins in the wellbore.</span
                  >
                </el-popover></label
              >
              <el-form-item prop="topDepth">
                <el-input
                  type="number"
                  :controls="false"
                  step="any"
                  v-model="targetData.topDepth"
                  placeholder=""
                  name="topDepth"
                ></el-input>
              </el-form-item>
            </div>
            <div class="h-auto w-full flex flex-col gap-1">
              <label class="font-bold">
                OD (Outside Diameter) (in)<el-popover
                  placement="top"
                  :width="300"
                  trigger="hover"
                >
                  <template #reference>
                    <i class="fas fa-exclamation-circle ms-2 fs-7"></i>
                  </template>
                  <span
                    >The Outside Diameter is the outer measurement of the casing
                    pipe, typically in inches or millimeters.</span
                  >
                </el-popover>
              </label>

              <el-form-item prop="outsideDiameter">
                <el-input
                  type="number"
                  :controls="false"
                  step="any"
                  v-model="targetData.outsideDiameter"
                  placeholder=""
                  name="outsideDiameter"
                ></el-input>
              </el-form-item>
            </div>
            <div class="h-auto w-full flex flex-col gap-1">
              <label class="font-bold"
                >Wt (Weight) (lb/ft)<el-popover
                  placement="top"
                  :width="300"
                  trigger="hover"
                >
                  <template #reference>
                    <i class="fas fa-exclamation-circle ms-2 fs-7"></i>
                  </template>
                  <span
                    >The Weight of the casing pipe is typically expressed in
                    pounds per foot (lb/ft) and represents the mass of the
                    casing material per linear foot.</span
                  >
                </el-popover></label
              >
              <el-form-item prop="weight">
                <el-input
                  type="number"
                  :controls="false"
                  step="any"
                  v-model="targetData.weight"
                  placeholder=""
                  name="weight"
                ></el-input>
              </el-form-item>
            </div>
          </div>
          <div class="h-auto w-full flex flex-col gap-3">
            <div class="h-auto w-full flex flex-col gap-1">
              <label class="font-bold">
                ID (Inside Diameter) (in)<el-popover
                  placement="top"
                  :width="300"
                  trigger="hover"
                >
                  <template #reference>
                    <i class="fas fa-exclamation-circle ms-2 fs-7"></i>
                  </template>
                  <span
                    >The Inside Diameter is the inner measurement of the casing
                    pipe, typically in inches or millimeters.</span
                  >
                </el-popover>
              </label>

              <el-form-item prop="insideDiameter">
                <el-input
                  type="number"
                  :controls="false"
                  step="any"
                  v-model="targetData.insideDiameter"
                  placeholder=""
                  name="insideDiameter"
                ></el-input>
              </el-form-item>
            </div>
            <div class="h-auto w-full flex flex-col gap-1">
              <label class="font-bold"
                >Casing Shoe Depth (ft)<el-popover
                  placement="top"
                  :width="300"
                  trigger="hover"
                >
                  <template #reference>
                    <i class="fas fa-exclamation-circle ms-2 fs-7"></i>
                  </template>
                  <span
                    >The depth at which the casing shoe is located. This is the
                    point where the casing is designed to support the weight of
                    the wellbore fluids.</span
                  >
                </el-popover></label
              >
              <el-form-item prop="casingShoeDepth">
                <el-input
                  type="number"
                  :controls="false"
                  step="any"
                  v-model="targetData.casingShoeDepth"
                  placeholder=""
                  name="casingShoeDepth"
                ></el-input>
              </el-form-item>
            </div>
            <div class="h-auto w-full flex flex-col gap-1">
              <label class="font-bold">
                Casing Length (ft)<el-popover
                  placement="top"
                  :width="300"
                  trigger="hover"
                >
                  <template #reference>
                    <i class="fas fa-exclamation-circle ms-2 fs-7"></i>
                  </template>
                  <span
                    >The total length of the casing string in the wellbore,
                    which may include multiple casing joints connected
                    together.</span
                  >
                </el-popover>
              </label>

              <el-form-item prop="casingLength">
                <el-input
                  type="number"
                  :controls="false"
                  step="any"
                  v-model="targetData.casingLength"
                  placeholder=""
                  name="casingLength"
                ></el-input>
              </el-form-item>
            </div>
          </div>
        </div>
        <div class="h-auto w-full flex flex-row items-center gap-2">
          <button
            type="button"
            class="bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover rounded-md px-4 py-2 font-semibold md:px-4 md:py-3 md:text-xl"
            @click="closeModal"
            :disabled="loading"
          >
            Discard
          </button>
          <button
            class="bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover rounded-md px-4 py-2 font-semibold md:px-4 md:py-3 md:text-xl"
            type="submit"
            :disabled="loading"
          >
            <span v-if="!loading" class="indicator-label"> Save </span>
            <span v-if="loading" class="indicator-progress">
              Please wait...
              <span
                class="spinner-border spinner-border-sm align-middle ms-2"
              ></span>
            </span>
          </button>
        </div>
      </el-form>
    </div>
  </div>
</template>

<script lang="ts">
import SvgIcon from "@/constants/SvgIcon.vue";
import { useCasedHoleStore } from "@/stores/cased-hole";
import { defineComponent, inject, ref, watch } from "vue";
import { useRoute } from "vue-router";
import type { Provide } from "@/types/injection-types";

interface NewCasedHoleData {
  description: string;
  topDepth: number | null;
  casingShoeDepth: number | null;
  casingLength: number | null;
  outsideDiameter: number | null;
  insideDiameter: number | null;
  weight: number | null;
}

export default defineComponent({
  name: "cased-hole-modal",
  components: { SvgIcon },
  props: {
    isVisible: {
      type: Boolean,
      required: true,
    },
    close: {
      type: Function,
      required: true,
    },
    loadPage: {
      type: Function,
      default: () => {},
      required: true,
    },
  },
  setup(props) {
    const route = useRoute();
    const casedHoleStore = useCasedHoleStore();
    const modal = ref(false);
    const targetData = ref<NewCasedHoleData>({
      description: "",
      topDepth: null,
      casingShoeDepth: null,
      casingLength: null,
      outsideDiameter: null,
      insideDiameter: null,
      weight: null,
    });
    const formRef = ref<null | HTMLFormElement>(null);
    const loading = ref<boolean>(false);
    const id = ref("");
    const dailyReportProvide = inject<Provide.DailyReport>("dailyReport");

    watch(id, (newValue) => {
      if (newValue !== "") {
        getCasedHoleDetails();
      }
    });

    watch(
      () => props.isVisible,
      (newValue) => {
        if (newValue === false) {
          id.value = "";
          reset();
          formRef?.value?.resetFields();
        }
      }
    );

    const getCasedHoleDetails = async (): Promise<void> => {
      casedHoleStore.getCasedHoleDetails({
        id: id.value,
        callback: {
          onSuccess: (res: any) => {
            targetData.value = res;
          },
        },
      });
    };

    const updateCasedHole = async (param: any): Promise<void> => {
      loading.value = true;
      casedHoleStore.updateCasedHole({
        id: id.value,
        params: param,
        callback: {
          onSuccess: (_res: any) => {
            if (props?.loadPage) {
              props?.loadPage();
            }
            props.close();
          },
          onFinish: (_err: any) => {
            loading.value = false;
          },
        },
      });
    };

    const createCasedHole = async (param: any): Promise<void> => {
      loading.value = true;
      casedHoleStore.createCasedHole({
        params: param,
        callback: {
          onSuccess: (_res: any) => {
            if (props?.loadPage) {
              props?.loadPage();
            }
            props.close();
          },
          onFinish: (_err: any) => {
            loading.value = false;
          },
        },
      });
    };

    const closeModal = () => {
      props.close();
    };

    const setId = (caseHoleId: string) => {
      id.value = caseHoleId.toString();
    };

    const rules = ref({
      description: [
        {
          required: true,
          message: "Please type Description",
          trigger: "blur",
        },
      ],
    });

    const submit = () => {
      if (!formRef.value) {
        return;
      }

      formRef.value.validate((valid: boolean) => {
        if (valid) {
          const param = {
            description: targetData?.value?.description,
            topDepth: targetData?.value?.topDepth
              ? Number(targetData?.value?.topDepth)
              : null,
            casingShoeDepth: Number(targetData?.value?.casingShoeDepth),
            casingLength: Number(targetData?.value?.casingLength),
            outsideDiameter: Number(targetData?.value?.outsideDiameter),
            insideDiameter: Number(targetData?.value?.insideDiameter),
            weight: Number(targetData?.value?.weight),
          };

          if (id?.value) {
            updateCasedHole({
              ...param,
              dailyReportId: dailyReportProvide?.getDailyReportId(),
            });
          } else {
            dailyReportProvide?.createDailyReport({
              wellId: route?.params?.id as string,
              callback: {
                onSuccess: (res: string) => {
                  createCasedHole({ ...param, dailyReportId: res });
                },
              },
            });
          }
        }
      });
    };

    const reset = () => {
      targetData.value = {
        description: "",
        topDepth: null,
        casingShoeDepth: null,
        casingLength: null,
        outsideDiameter: null,
        insideDiameter: null,
        weight: null,
      };
    };

    return {
      id,
      modal,
      rules,
      loading,
      targetData,
      formRef,
      submit,
      setId,
      reset,
      closeModal,
    };
  },
});
</script>
