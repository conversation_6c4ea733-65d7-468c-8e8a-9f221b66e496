<template>
  <slot></slot>
</template>

<script setup lang="ts">
import { provide, ref } from "vue";
import JwtService from "../services/JwtService";
const userInfo = ref(JwtService.getUserInfo());

const updateUserInfo = (info: any) => {
  const newInfo = {
    ...userInfo.value,
    ...info,
  };
  userInfo.value = { ...newInfo };
  JwtService.saveUserInfo(JSON.stringify(newInfo));
};

provide("userInfo", {
  userInfo, updateUserInfo
})
</script>