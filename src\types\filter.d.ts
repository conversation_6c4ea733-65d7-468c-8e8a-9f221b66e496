declare namespace Filter {
  export interface FilterForm {
    keyword?: string | null;
    name?: string | null;
    fullName?: string | null;
    email?: string | null;
    status?: 1 | 2 | 0 | null;
    role?: number | null;
    company?: string | null;
    priceFrom?: number | null;
    priceTo?: number | null;
    supervisor?: string | null;
    engineer?: string | null;
    customer?: string | null;
    companyId?: string | null;
    page?: number | null;
    limit?: number | null;
    sortDirection?: string | null;
    sortBy?: string | null;
  }
}
