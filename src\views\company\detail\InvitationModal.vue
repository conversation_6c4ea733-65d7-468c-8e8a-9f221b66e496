<template>
  <div
    v-if="isVisible"
    class="fixed top-0 right-0 bottom-0 left-0 z-40 flex items-center"
  >
    <!-- Dark overlay background -->
    <div class="absolute inset-0 bg-sidebar-background opacity-25"></div>
    <div
      class="relative bg-card-background text-card-text h-auto w-11/12 flex flex-col items-center gap-3 mx-auto p-5 rounded-xl md:w-4/5 md:text-lg lg:w-2/5"
    >
      <div class="h-auto w-full flex flex-row items-center justify-between">
        <h3 class="text-lg font-bold">Send Invitation</h3>
        <span class="cursor-pointer" @click="closeModal">
          <SvgIcon icon="closeModalIcon" />
        </span>
      </div>

      <p class="text-xs text-center md:text-lg">
        An email will be sent out to the employees email that you input. Your
        employee will need to access to the link to create their account to use
        the Muddy Software under your company
      </p>

      <div class="h-auto w-full flex flex-col gap-4">
        <div class="flex flex-col gap-2">
          <label class="font-semibold">Emails</label>
          <el-tag
            v-for="tag in emails"
            size="large"
            :key="tag"
            closable
            class="me-2"
            :disable-transitions="false"
            @close="handleClose(tag)"
          >
            {{ tag }}
          </el-tag>
          <el-input
            ref="InputRef"
            v-model="inputValue"
            size="large"
            placeholder="Enter email"
            @keyup.enter="handleInputConfirm"
            @blur="handleInputConfirm"
          />
          <el-text class="mx-1 align-self-start" type="danger">{{
            inputEmailError
          }}</el-text>
        </div>
        <div class="flex flex-col gap-2">
          <label class="font-semibold">Roles</label>
          <el-select-v2
            size="large"
            v-model="roles"
            :options="rolesOptions"
            placeholder="Roles"
            multiple
            clearable
            name="roles"
          />
        </div>
      </div>
      <div class="text-end mt-4">
        <button
          class="bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover rounded-md px-4 py-2 font-semibold"
          type="button"
          :disabled="loading"
          @click="sendInvitation"
        >
          <span v-if="!loading" class="indicator-label"> Send Invitation </span>
          <span v-if="loading" class="indicator-progress">
            Please wait...
            <span
              class="spinner-border spinner-border-sm align-middle ms-2"
            ></span>
          </span>
        </button>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import SvgIcon from "@/constants/SvgIcon.vue";
import { rolesOptions } from "@/constants/user";
import AlertService from "@/services/AlertService";
import { useUserStore } from "@/stores/user";
import { ElInput } from "element-plus";
import { defineComponent, nextTick, ref, watch } from "vue";
import * as yup from "yup";

interface ListItem {
  value: string;
  label: string;
}

export default defineComponent({
  name: "user-modal",
  components: { SvgIcon },
  props: {
    isVisible: {
      type: Boolean,
      required: true,
    },
    close: {
      type: Function,
      required: true,
    },
    loadPage: {
      type: Function,
      required: false,
    },
    companyId: {
      type: String,
      required: true,
    },
  },
  setup(props) {
    const userStore = useUserStore();
    const loading = ref<boolean>(false);
    const options = ref<ListItem[]>([]);
    const inputEmailError = ref<string>("");

    const inputValue = ref("");
    const emails = ref<string[]>([]);
    const roles = ref<number[]>([]);
    const InputRef = ref<InstanceType<typeof ElInput>>();

    const schema = yup.object().shape({
      email: yup
        .string()
        .email("Invalid email address")
        .required("Email is required")
        .test("is-unique", "Email is already taken", (value) => {
          return !emails.value.includes(value as string);
        }),
    });

    watch(
      () => props.isVisible,
      (newValue) => {
        if (newValue === false) {
          reset();
        }
      }
    );

    const handleClose = (tag: string) => {
      emails.value.splice(emails.value.indexOf(tag), 1);
      props.close();
    };

    const closeModal = () => {
      props.close();
    };

    const showInput = () => {
      nextTick(() => {
        InputRef.value!.input!.focus();
      });
    };

    const handleInputConfirm = () => {
      if (inputValue.value) {
        checkIsValidEmail(inputValue.value);
      }
    };

    const checkIsValidEmail = (email: string) => {
      inputEmailError.value = "";
      schema
        .validate({ email })
        .then(() => {
          emails.value.push(email);
          inputValue.value = "";
        })
        .catch((err) => {
          inputEmailError.value = err?.errors?.[0] || "Invalid email address";
        });
    };

    const reset = () => {
      emails.value = [];
      roles.value = [];
      inputEmailError.value = "";
      inputValue.value = "";
    };

    const sendInvitation = async () => {
      if (!emails.value.length) {
        inputEmailError.value = "Please type Email";
        return;
      }

      loading.value = true;

      userStore.inviteUsers({
        params: {
          emails: emails.value,
          userRoles: roles.value,
          companyId: props?.companyId,
        },
        callback: {
          onSuccess: (_res: any) => {
            props.close();
            AlertService.toast("Invited successfully", "success", "top-right");
          },
          onFinish: () => {
            loading.value = false;
          },
        },
      });
    };

    return {
      roles,
      loading,
      rolesOptions,
      options,
      inputValue,
      emails,
      InputRef,
      inputEmailError,
      sendInvitation,
      handleClose,
      showInput,
      handleInputConfirm,
      reset,
      closeModal,
    };
  },
});
</script>
