declare namespace Customer {
  export type Info = {
    id?: string;
    customerName?: string;
    notes?: string;
    createdAt?: string;
    companyId?: string;
    company?: Company.Info;
  };

  export interface Contact {
    id?: string;
    name?: string;
    address?: string | null;
    officePhone?: string | null;
    mobilePhone?: string;
    emailAddress?: string;
    notes?: string | null;
    primaryContact?: boolean;
    notifyOnNewReport?: boolean;
    createdAt?: string;
    customerId?: string | null;
  }

  export interface GetFilter extends Filter.FilterForm {
    customerId?: string | null;
  }
}
