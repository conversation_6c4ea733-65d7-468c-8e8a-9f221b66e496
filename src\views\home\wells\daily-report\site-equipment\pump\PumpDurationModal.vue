<template>
  <div
    v-if="isVisible"
    class="fixed top-0 right-0 bottom-0 left-0 z-40 flex items-center"
  >
    <!-- Dark overlay background -->
    <div class="absolute inset-0 bg-sidebar-background opacity-25"></div>
    <div
      class="relative bg-card-background text-card-text-light max-h-11/12 w-11/12 flex flex-col items-center gap-3 mx-auto my-6 p-4 rounded-xl md:w-1/4"
    >
      <div class="flex flex-row h-auto w-full items-center justify-between">
        <h3 class="text-lg font-bold">
          {{ `${id ? "Edit Pump Duration" : "Add Pump Duration"}` }}
        </h3>
        <span class="cursor-pointer" @click="closeModal">
          <SvgIcon icon="closeModalIcon" />
        </span>
      </div>

      <el-form
        id="pump_duration_form"
        @submit.prevent="submit"
        :model="targetData"
        :rules="rules"
        ref="formRef"
        class="h-auto w-full flex flex-col gap-3"
      >
        <div class="flex flex-col gap-1">
          <label class="font-bold"
            >Duration (hr)<span class="text-danger-active font-light">*</span>
          </label>
          <el-form-item prop="durations" class="mt-auto">
            <el-input
              type="number"
              :controls="false"
              step="any"
              v-model="targetData.durations"
              placeholder=""
              name="durations"
            ></el-input>
          </el-form-item>
        </div>
        <div class="h-auto w-full flex flex-row items-center gap-2">
          <button
            type="button"
            class="bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover rounded-md px-4 py-2 font-semibold"
            @click="closeModal"
            :disabled="loading"
          >
            Discard
          </button>
          <button
            class="bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover rounded-md px-4 py-2 font-semibold"
            type="submit"
            :disabled="loading"
          >
            <span v-if="!loading" class="indicator-label"> Save </span>
            <span v-if="loading" class="indicator-progress">
              Please wait...
              <span
                class="spinner-border spinner-border-sm align-middle ms-2"
              ></span>
            </span>
          </button>
        </div>
      </el-form>
    </div>
  </div>
</template>

<script lang="ts">
import SvgIcon from "@/constants/SvgIcon.vue";
import { usePumpDurationStore } from "@/stores/pump-duration";
import { defineComponent, ref, watch } from "vue";

export default defineComponent({
  name: "pump-durations-modal",
  components: { SvgIcon },
  props: {
    isVisible: {
      type: Boolean,
      required: true,
    },
    close: {
      type: Function,
      required: true,
    },
    loadPage: {
      type: Function,
      required: false,
    },
  },
  setup(props) {
    const pumpDurationStore = usePumpDurationStore();
    const modal = ref(false);
    const targetData = ref({
      durations: null,
    });
    const formRef = ref<null | HTMLFormElement>(null);
    const loading = ref<boolean>(false);
    const pumpId = ref("");
    const id = ref("");

    watch(id, (newValue) => {
      if (newValue !== "") {
        getPumpDuration();
      }
    });

    watch(
      () => props.isVisible,
      (newValue) => {
        if (newValue === false) {
          id.value = "";
          pumpId.value = "";
          reset();
          formRef?.value?.resetFields();
        }
      }
    );

    const getPumpDuration = async (): Promise<void> => {
      pumpDurationStore.getPumpDurationDetails({
        id: id.value,
        callback: {
          onSuccess: (res: any) => {
            targetData.value = { ...res };
          },
        },
      });
    };

    const updatePumpDuration = async (param: any): Promise<void> => {
      loading.value = true;
      pumpDurationStore.updatePumpDuration({
        id: id.value,
        params: param,
        callback: {
          onSuccess: (_res: any) => {
            if (props?.loadPage) {
              props?.loadPage();
            }
            closeModal();
          },
          onFinish: (_err: any) => {
            loading.value = false;
          },
        },
      });
    };

    const createPumpDuration = async (param: any): Promise<void> => {
      loading.value = true;
      pumpDurationStore.createPumpDuration({
        params: param,
        callback: {
          onSuccess: (_res: any) => {
            if (props?.loadPage) {
              props?.loadPage();
            }
            closeModal();
          },
          onFinish: (_err: any) => {
            loading.value = false;
          },
        },
      });
    };

    const closeModal = () => {
      props.close();
    };

    const setId = (pump: string, duration: string) => {
      pumpId.value = pump;
      id.value = duration;
    };

    const rules = ref({
      durations: [
        {
          required: true,
          message: "Please type Duration",
          trigger: "blur",
        },
      ],
    });

    const submit = () => {
      if (!formRef.value) {
        return;
      }

      formRef.value.validate((valid: boolean) => {
        if (valid) {
          const param = {
            pumpId: pumpId?.value,
            durations: Number(targetData?.value?.durations),
          };

          if (id?.value) {
            updatePumpDuration(param);
          } else {
            createPumpDuration(param);
          }
        }
      });
    };

    const reset = () => {
      targetData.value = {
        durations: null,
      };
    };

    return {
      id,
      modal,
      rules,
      loading,
      targetData,
      formRef,
      closeModal,
      submit,
      setId,
      reset,
    };
  },
});
</script>
