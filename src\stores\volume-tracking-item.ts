import ApiService from "../services/ApiService";
import { get, noop } from "lodash";
import { defineStore } from "pinia";
import { handleFailure } from "../utils/handleFailure";
import type { Callback } from "../types/common";

export const useVolumeTrackingItemStore = defineStore(
  "volumeTrackingItem",
  () => {
    const getVolumeTrackingItemDetails = async ({
      id,
      callback,
    }: {
      id: string;
      callback: Callback;
    }): Promise<void> => {
      const onSuccess = get(callback, "onSuccess", noop);
      const onFinish = get(callback, "onFinish", noop);

      try {
        ApiService.setHeader();
        const response = await ApiService.get(`volumeTrackingItems/${id}`);
        onSuccess(response.data?.data || response.data);
      } catch (error) {
        handleFailure(error, callback);
      } finally {
        onFinish();
      }
    };

    const updateVolumeTrackingItem = async ({
      id,
      params,
      callback,
    }: {
      id: string;
      params: any;
      callback: Callback;
    }): Promise<void> => {
      const onSuccess = get(callback, "onSuccess", noop);
      const onFinish = get(callback, "onFinish", noop);

      try {
        ApiService.setHeader();
        const response = await ApiService.put(
          `volumeTrackingItems/${id}`,
          params
        );
        onSuccess(response.data?.data || response.data);
      } catch (error) {
        handleFailure(error, callback);
      } finally {
        onFinish();
      }
    };

    const deleteVolumeTrackingItem = async ({
      id,
      callback,
    }: {
      id: string;
      callback: Callback;
    }): Promise<void> => {
      const onSuccess = get(callback, "onSuccess", noop);
      const onFinish = get(callback, "onFinish", noop);

      try {
        ApiService.setHeader();
        const response = await ApiService.delete(`volumeTrackingItems/${id}`);
        onSuccess(response.data?.data || response.data);
      } catch (error) {
        handleFailure(error, callback);
      } finally {
        onFinish();
      }
    };

    const createVolumeTrackingItem = async ({
      params,
      callback,
    }: {
      params: any;
      callback: Callback;
    }): Promise<void> => {
      const onSuccess = get(callback, "onSuccess", noop);
      const onFinish = get(callback, "onFinish", noop);

      try {
        ApiService.setHeader();
        const response = await ApiService.post("volumeTrackingItems", params);
        onSuccess(response.data?.data || response.data);
      } catch (error) {
        handleFailure(error, callback);
      } finally {
        onFinish();
      }
    };

    return {
      getVolumeTrackingItemDetails,
      updateVolumeTrackingItem,
      deleteVolumeTrackingItem,
      createVolumeTrackingItem,
    };
  }
);
