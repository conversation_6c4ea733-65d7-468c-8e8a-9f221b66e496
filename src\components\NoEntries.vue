<template>
  <el-empty>
    <el-button @click="addNew" type="primary" description="No entries"
      >Click to add new</el-button
    >
  </el-empty>
</template>

<script lang="ts">
import { defineComponent } from "vue";

export default defineComponent({
  name: "no-entries",
  components: {},
  props: {
    addNew: {
      type: Function,
      default: () => {},
      required: true,
    },
  },
  setup() {
    return {};
  },
});
</script>
