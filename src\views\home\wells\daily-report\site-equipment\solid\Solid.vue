<template>
  <div
    class="h-auto w-full mt-4 p-4 bg-minicard-background text-minicard-text-light rounded-lg border-[1px] border-dashed font-semibold"
  >
    <h4 class="font-bold mb-4">Solid Control Equipment</h4>

    <div v-if="loading" class="text-center">
      <div class="spinner-border text-primary" role="status">
        <span class="sr-only">Loading...</span>
      </div>
    </div>
    <template v-else>
      <NoEntries
        v-if="solidControlEquipmentList.length === 0"
        :addNew="() => toggleSolidModal()"
      />
      <div v-else class="grid grid-cols-1 gap-4 md:grid-cols-2 md:gap-7 lg:grid-cols-3">
        <div
          v-for="item in solidControlEquipmentList"
          :key="item?.id"
          class="rounded-lg border-[1px] border-dashed relative cursor-pointer my-6 first:mt-0 last: mb-0 md:first:mt-0 md:last:mb-0 md:m-0"
        >
          <div class="flex flex-col gap-3 p-4">
            <h5
              class="font-bold underline underline-offset-2"
              @click="toggleSolidDetailsModal(item?.id.toString() || '')"
            >
              {{ `${item?.type?.name} ${item?.screen}` }}
            </h5>
            <div class="flex flex-col gap-3">
              <div
                class="flex flex-col items-center justify-between border-b-[1px] border-dashed pb-3"
              >
                <div
                  v-if="item?.inputs[0]"
                  class="h-auto w-full flex items-center justify-between"
                >
                  <span>{{ item?.inputs[0].description }}</span>
                  <span class="ms-auto">
                    {{ `${item?.inputs[0].value} ${item?.inputs[0].units}` }}
                  </span>
                </div>
                <div
                  v-if="item?.inputs[1]"
                  class="h-auto w-full flex items-center justify-between"
                >
                  <span>{{ item?.inputs[1].description }}</span>
                  <span>
                    {{ `${item?.inputs[1].value} ${item?.inputs[1].units}` }}
                  </span>
                </div>
                <div
                  v-if="item?.inputs.length > 2"
                  class="italic cursor-pointer"
                >
                  More...
                </div>
              </div>
              <div class="h-auto w-full flex items-center justify-between">
                <span>Total Duration</span>
                <span>
                  {{ `${item?.totalDurations || "0"} hrs` }}
                </span>
              </div>
            </div>
          </div>
          <div
            class="flex flex-row items-center gap-2 absolute top-0 end-0 -translate-y-1/2"
          >
            <el-tooltip content="Add Input" placement="top" effect="customize">
              <button
                class="p-2 rounded-full bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover"
                @click="toggleSolidInputModal(item?.id || '')"
              >
                <SvgIcon icon="addIcon" />
              </button>
            </el-tooltip>
            <el-tooltip
              content="Add Duration"
              placement="top"
              effect="customize"
            >
              <button
                class="p-2 rounded-full bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover"
                @click="toggleSolidDurationModal(item?.id || '')"
              >
                <SvgIcon icon="durationIcon" />
              </button>
            </el-tooltip>

            <el-tooltip content="Edit" placement="top" effect="customize">
              <button
                class="p-2 rounded-full bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover"
                @click="toggleSolidModal(item?.id.toString() || '')"
              >
                <SvgIcon icon="pencilIcon" />
              </button>
            </el-tooltip>
            <el-tooltip content="Delete" placement="top" effect="customize">
              <button
                class="p-2 rounded-full bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover"
                @click="deleteSolid(item?.id.toString() || '')"
              >
                <span class="text-danger">
                  <SvgIcon icon="trashIcon" />
                </span>
              </button>
            </el-tooltip>
          </div>
        </div>
      </div>
    </template>
  </div>
  <SolidModal
    :isVisible="isSolidModalVisible"
    :close="toggleSolidModal"
    ref="solidModal"
    :loadPage="loadData"
    :zIndex="40"
  />
  <SolidInputModal
    :isVisible="isSolidInputModalVisible"
    :close="toggleSolidInputModal"
    ref="solidInputModal"
    :loadPage="loadData"
    :zIndex="40"
  />
  <SolidDurationModal
    :isVisible="isSolidDurationModalVisible"
    :close="toggleSolidDurationModal"
    ref="solidDurationModal"
    :loadPage="loadData"
    :zIndex="40"
  />
  <SolidDetailsModal
    :isVisible="isSolidDetailsModalVisible"
    :close="toggleSolidDetailsModal"
    ref="solidDetailsModal"
    :loadPage="loadData"
    :zIndex="40"
  />
</template>

<script lang="ts">
import NoEntries from "@/components/NoEntries.vue";
import SvgIcon from "@/constants/SvgIcon.vue";
import AlertService from "@/services/AlertService";
import { useSolidControlEquipmentStore } from "@/stores/solid-control-equipment";
import { defineComponent, inject, onMounted, ref, type Ref } from "vue";
import SolidDetailsModal from "./SolidDetailsModal.vue";
import SolidDurationModal from "./SolidDurationModal.vue";
import SolidInputModal from "./SolidInputModal.vue";
import SolidModal from "./SolidModal.vue";
import type { Provide } from "@/types/injection-types";

export default defineComponent({
  name: "site-equipment",
  components: {
    SvgIcon,
    SolidModal,
    SolidDetailsModal,
    SolidDurationModal,
    SolidInputModal,
    NoEntries,
  },
  props: {
    loadPage: {
      type: Function,
      required: true,
    },
  },
  setup(props) {
    const solidControlEquipmentStore = useSolidControlEquipmentStore();
    const solidModal: Ref<any> = ref<typeof SolidModal | null>(null);
    const solidDetailsModal: Ref<any> = ref<typeof SolidDetailsModal | null>(
      null
    );
    const solidDurationModal: Ref<any> = ref<typeof SolidDurationModal | null>(
      null
    );
    const solidInputModal: Ref<any> = ref<typeof SolidInputModal | null>(null);
    const loading = ref(false);
    const solidControlEquipmentList = ref<any>([]);
    const dailyReportProvide = inject<Provide.DailyReport>("dailyReport");
    const isSolidModalVisible = ref(false);
    const isSolidInputModalVisible = ref(false);
    const isSolidDurationModalVisible = ref(false);
    const isSolidDetailsModalVisible = ref(false);

    onMounted(() => {
      if (dailyReportProvide?.getDailyReportId()) {
        getSolids();
      }
    });

    const loadData = () => {
      getSolids();
      if (props?.loadPage) {
        props?.loadPage();
      }
    };

    const getSolids = async (
      params = {
        dailyReportId: dailyReportProvide?.getDailyReportId(),
        page: 1,
        limit: 200,
      }
    ): Promise<void> => {
      loading.value = true;

      solidControlEquipmentStore.getSolidControlEquipments({
        params: params,
        callback: {
          onSuccess: (res: any) => {
            solidControlEquipmentList.value = res?.items;
          },
          onFinish: (_err: any) => {
            loading.value = false;
          },
        },
      });
    };

    const toggleSolidModal = (solidId?: string) => {
      if (solidId) {
        solidModal?.value?.setId(solidId);
      }
      isSolidModalVisible.value = !isSolidModalVisible.value;
    };

    const toggleSolidInputModal = (solidId: string) => {
      if (solidId) {
        solidInputModal?.value?.setId(solidId, "");
      }
      isSolidInputModalVisible.value = !isSolidInputModalVisible.value;
    };

    const toggleSolidDurationModal = (solidId?: string, durationId = "") => {
      if (solidId) {
        solidDurationModal?.value?.setId(solidId, durationId);
      }
      isSolidDurationModalVisible.value = !isSolidDurationModalVisible.value;
    };

    const toggleSolidDetailsModal = (solidId?: string) => {
      if (solidId) {
        solidDetailsModal?.value?.setId(solidId);
      }
      isSolidDetailsModalVisible.value = !isSolidDetailsModalVisible.value;
    };

    const deleteSolid = (id: string) => {
      AlertService.deletionAlert({
        onConfirmed: () => {
          deleteSolidById(id);
        },
      });
    };

    const deleteSolidById = async (id: string): Promise<void> => {
      loading.value = true;
      solidControlEquipmentStore.deleteSolidControlEquipment({
        id: id,
        callback: {
          onSuccess: (_res: any) => {
            loadData();
          },
          onFinish: (_err: any) => {
            loading.value = false;
          },
        },
      });
    };

    return {
      loading,
      solidModal,
      solidInputModal,
      solidDetailsModal,
      solidDurationModal,
      solidControlEquipmentList,
      isSolidModalVisible,
      isSolidInputModalVisible,
      isSolidDurationModalVisible,
      isSolidDetailsModalVisible,
      loadData,
      deleteSolid,
      toggleSolidModal,
      toggleSolidInputModal,
      toggleSolidDetailsModal,
      toggleSolidDurationModal,
    };
  },
});
</script>
