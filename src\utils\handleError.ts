import AlertService from "../services/AlertService";
import { get, noop } from "lodash";
import { ExceptionMessages, ExceptionCode } from "../constants/exceptions";
import type { Callback } from "../types/common";

export const handleError = async (
  error: any,
  callback: Callback
): Promise<void> => {
  const fallbackMessage =
    "Sorry, looks like there are some errors detected, please try again.";

  if (callback?.onFailure) {
    const onFailure = get(callback, "onFailure", noop);

    onFailure(error);
  } else {
    const errorCode = error?.response?.data?.errorCode as
      | ExceptionCode
      | undefined;
    const fallbackError = error?.response?.data?.message || error?.message;
    const message =
      ExceptionMessages[errorCode!] ?? fallbackError ?? fallbackMessage;
    AlertService.resultAlert(message, "error");
  }
};
