<template>
  <div
    class="bg-card-background text-card-text-light w-11/12 mx-auto mb-4 h-auto rounded-xl"
  >
    <div class="h-auto w-11/12 mx-auto mt-7 px-3 py-4">
      <h1 class="font-bold">Product & Package Inventory</h1>
    </div>
    <div class="card-body">
      <div v-if="loadingPackageInfo" class="text-center">
        <div class="spinner-border text-primary" role="status">
          <span class="sr-only">Loading...</span>
        </div>
      </div>
      <div
        v-else
        class="h-auto w-11/12 mx-auto rounded-xl border-[1px] border-dashed p-4 font-semibold"
      >
        <h5 class="font-bold">Details</h5>
        <div class="flex items-center justify-between">
          <span>Total cost </span>
          <span>{{ `$${numberWithCommas(packageInfo?.totalCost)}` }}</span>
        </div>
        <!-- <div
                class="d-flex flex-wrap align-items-center justify-content-between"
              >
                <span class="fw-bold me-5">Total Product Volume (bbl)</span>
                <span class="fw-300 ms-auto">{{
                  numberWithCommas(packageInfo?.totalProductVolume)
                }}</span>
              </div>
              <div
                class="d-flex flex-wrap align-items-center justify-content-between"
              >
                <span class="fw-bold me-5">Weight Materials (bbl)</span>
                <span class="fw-300 ms-auto">{{
                  numberWithCommas(packageInfo?.weightMaterials)
                }}</span>
              </div>
              <div
                class="d-flex flex-wrap align-items-center justify-content-between"
              >
                <span class="fw-bold me-5">Base Fluid (bbl)</span>
                <span class="fw-300 ms-auto">{{
                  numberWithCommas(packageInfo?.baseFluid)
                }}</span>
              </div>
              <div
                class="d-flex flex-wrap align-items-center justify-content-between"
              >
                <span class="fw-bold me-5">Add Water (bbl) </span>
                <span class="fw-300 ms-auto">{{
                  numberWithCommas(packageInfo?.addWater)
                }}</span>
              </div>
              <div
                class="d-flex flex-wrap align-items-center justify-content-between"
              >
                <span class="fw-bold me-5">Total Volume (bbl)</span>
                <span class="fw-300 ms-auto">{{
                  numberWithCommas(packageInfo?.totalVolume)
                }}</span>
              </div> -->
      </div>

      <div class="h-auto w-11/12 flex flex-col gap-3 mx-auto p-4">
        <div
          class="accordion accordion-flush"
          id="accordionPanelsStayOpenExample"
        >
          <div v-if="loadingPackageList" class="text-center">
            <div class="spinner-border text-primary" role="status">
              <span class="sr-only">Loading...</span>
            </div>
          </div>
          <div v-else>
            <NoEntries
              v-if="packageList.length === 0"
              :addNew="toggleAddInitial"
            />
            <div v-else class="grid grid-cols-1 gap-4 md:grid-cols-2 md:gap-7 lg:grid-cols-3">
              <div
                v-for="(item, index) in packageList"
                :key="item?.id"
                class="w-full my-2 first:mt-0 last:mb-0 bg-minicard-background text-minicard-text-light rounded-lg border-[1px] border-dashed transition-all duration-300 ease-in-out md:first:mt-0 md:last:mb-0 md:m-0"
                :class="openAccordions.has(item?.id) ? 'h-80 md:h-90' : 'h-40'"
              >
                <div
                  class="h-auto w-full p-4 pb-0 flex flex-col items-center rounded p4 gap-3 justify-between font-semibold"
                  @click="toggleAccordion(item?.id)"
                >
                  <div class="h-auto w-full flex gap-2 items-center">
                    <span class="bg-success rounded-lg h-15 w-2"></span>
                    <div class="h-auto w-full flex flex-col gap-0.5">
                      <h4>
                        {{ item?.product?.name }}
                      </h4>
                      <span class="text-success">
                        {{ `Quantity: ${item?.quantity}` }}
                      </span>
                      <span class="text-danger">
                        {{
                          `Total Cost: $${numberWithCommas(item?.totalCost)}`
                        }}
                      </span>
                    </div>
                  </div>

                  <div class="flex items-center gap-2">
                    <el-tooltip
                      content="Received"
                      placement="top"
                      effect="customize"
                    >
                      <button
                        class="p-2 rounded-full bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover"
                        @click="
                          toggleItem(
                            item?.id,
                            '',
                            ProductAndPackageInventoryType.received,
                            item?.product?.id
                          )
                        "
                      >
                        <SvgIcon icon="addIcon" color="#43a047" />
                      </button>
                    </el-tooltip>
                    <el-tooltip
                      content="Used"
                      placement="top"
                      effect="customize"
                    >
                      <button
                        class="p-2 rounded-full bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover"
                        @click="
                          toggleItem(
                            item?.id,
                            '',
                            ProductAndPackageInventoryType.used,
                            item?.product?.id
                          )
                        "
                      >
                        <SvgIcon icon="minusIcon" />
                      </button>
                    </el-tooltip>
                    <el-tooltip
                      content="Returned"
                      placement="top"
                      effect="customize"
                    >
                      <button
                        class="p-2 rounded-full bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover"
                        @click="
                          toggleItem(
                            item?.id,
                            '',
                            ProductAndPackageInventoryType.returned,
                            item?.product?.id
                          )
                        "
                      >
                        <span class="svg-icon svg-icon-3">
                          <SvgIcon icon="refreshIcon" />
                        </span>
                      </button>
                    </el-tooltip>
                    <el-tooltip
                      content="Adjusted"
                      placement="top"
                      effect="customize"
                    >
                      <button
                        class="p-2 rounded-full bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover"
                        @click="
                          toggleItem(
                            item?.id,
                            '',
                            ProductAndPackageInventoryType.adjusted,
                            item?.product?.id
                          )
                        "
                      >
                        <SvgIcon icon="pencilIcon" />
                      </button>
                    </el-tooltip>
                    <el-tooltip
                      content="Delete"
                      placement="top"
                      effect="customize"
                    >
                      <button
                        class="p-2 rounded-full bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover"
                        @click="deleteProductPackage(item?.id)"
                      >
                        <span class="text-danger">
                          <SvgIcon icon="trashIcon" />
                        </span>
                      </button>
                    </el-tooltip>
                  </div>
                </div>

                <div
                  class="h-auto w-full p-4 pt-0 transition-all"
                  :class="
                    openAccordions.has(item?.id)
                      ? 'opacity-100 pt-4 duration-300 ease-in'
                      : 'opacity-0 duration-200 ease-in'
                  "
                >
                  <div class="h-auto w-full overflow-x-scroll">
                    <p class="font-bold text-sm">History</p>

                    <!--begin::Table-->
                    <table>
                      <thead>
                        <tr class="font-bold">
                          <th>Date</th>
                          <th>Type</th>
                          <th>Location</th>
                          <th>Quantity</th>
                          <th>Cost</th>
                          <th></th>
                        </tr>
                      </thead>
                      <tbody>
                        <td v-if="item?.loading" colspan="6">
                          <div class="text-center">
                            <div
                              class="spinner-border text-primary"
                              role="status"
                            >
                              <span class="sr-only">Loading...</span>
                            </div>
                          </div>
                        </td>
                        <template
                          v-else
                          v-for="subItem in item?.historyList"
                          :key="item.id"
                        >
                          <tr class="font-bold">
                            <td class="p-4">
                              {{ formatDate(subItem?.createdAt) }}
                            </td>
                            <td class="p-4">
                              {{
                                getOption(
                                  subItem?.type,
                                  productAndPackageInventoryOptions
                                )?.label
                              }}
                            </td>
                            <td class="text-break">
                              {{ subItem?.location?.name }}
                            </td>
                            <td class="p-4">
                              {{ numberWithCommas(subItem?.quantity) }}
                            </td>
                            <td class="p-4">
                              {{
                                subItem?.cost
                                  ? `$${numberWithCommas(subItem?.cost)}`
                                  : ""
                              }}
                            </td>
                            <td class="p-4">
                              <div
                                class="h-auto w-full flex flex-row items-center gap-2 justify-between"
                              >
                                <button
                                  class="bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover font-semibold h-8 w-8 rounded-full px-2 py-0.5"
                                  @click="
                                    toggleItem(
                                      item?.id,
                                      subItem?.id,
                                      subItem?.type,
                                      item?.product?.id
                                    )
                                  "
                                >
                                  <SvgIcon icon="newReportIcon" />
                                </button>
                                <button
                                  class="bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover font-semibold h-8 w-8 rounded-full px-2 pb-1"
                                  @click="deleteProductPackageItem(subItem?.id)"
                                  :disabled="
                                    subItem?.type ==
                                    ProductAndPackageInventoryType.initial
                                  "
                                >
                                  <span class="text-danger">
                                    <SvgIcon icon="trashIcon" />
                                  </span>
                                </button>
                              </div>
                            </td>
                          </tr>
                        </template>
                      </tbody>
                      <!--end::Table body-->
                    </table>
                    <!--end::Table-->
                  </div>
                  <div class="h-auto w-full flex flex-col items-center">
                    <div v-if="item?.historyList?.length">
                      {{
                        `Showing ${(item?.currentPage - 1) * 10 + 1} to ${
                          item?.historyList?.length
                        } of ${item?.totalElements} entries`
                      }}
                    </div>
                    <TablePagination
                      v-if="item?.pageCount >= 1"
                      :total-pages="item?.pageCount"
                      :total="item?.totalElements"
                      :per-page="10"
                      :current-page="item?.currentPage"
                      @page-change="(newPage: number) => pageChange(index, item?.id, newPage)"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <BottomTool :addNew="toggleAddInitial" :showHelpInfo="false" />
  <FormModal
    :isVisible="isModalVisible"
    :close="toggleAddInitial"
    ref="formModal"
    :productAndPackageInventoryId="productAndPackageInventoryId"
    :loadTable="getPackageInfo"
  />
</template>

<script lang="ts">
import NoEntries from "@/components/NoEntries.vue";
import TablePagination from "@/components/common/TablePagination.vue";
import SvgIcon from "@/constants/SvgIcon.vue";
import { getOption } from "@/utils/option";
import {
  productAndPackageInventoryOptions,
  ProductAndPackageInventoryType,
} from "@/constants/product-package-inventory";
import { formatDate } from "@/utils/date";
import { numberWithCommas } from "@/utils/numberFormatter";
import AlertService from "@/services/AlertService";
import { useProductPackageInventoryStore } from "@/stores/product-package-inventory";
import { useProductPackageItemStore } from "@/stores/product-package-inventory-item";
import { useProductPackageReportStore } from "@/stores/product-package-inventory-report";
import { defineComponent, inject, onMounted, ref, type Ref } from "vue";
import { useRoute } from "vue-router";
import BottomTool from "@/components/common/BottomTool.vue";
import FormModal from "./FormModal.vue";
import type { Provide } from "@/types/injection-types";

export default defineComponent({
  name: "product-package",
  components: {
    SvgIcon,
    FormModal,
    BottomTool,
    TablePagination,
    NoEntries,
  },
  props: {
    setChildActiveTab: {
      type: Function,
      required: false,
      default: () => {},
    },
  },
  setup(_props) {
    const route = useRoute();
    const productReportStore = useProductPackageReportStore();
    const productPackageStore = useProductPackageInventoryStore();
    const productPackageItemStore = useProductPackageItemStore();

    const loadingPackageInfo = ref<boolean>(false);
    const loadingPackageList = ref<boolean>(false);
    const packageInfo = ref<any>();
    const packageList = ref<any>([]);
    const formModal: Ref<any> = ref<typeof FormModal | null>(null);
    const productAndPackageInventoryId = ref("");
    const dailyReportProvide = inject<Provide.DailyReport>("dailyReport");
    const isModalVisible = ref(false);
    const openAccordions = ref<Set<string>>(new Set());

    onMounted(() => {
      if (dailyReportProvide?.getDailyReportId()) {
        getPackageInfo();
      } else {
        createDailyReport();
      }
    });

    const createDailyReport = async (): Promise<void> => {
      dailyReportProvide?.createDailyReport({
        wellId: route?.params?.id as string,
        callback: {
          onSuccess: (_res: string) => {
            getPackageInfo();
          },
        },
      });
    };

    const getPackageInfo = async (): Promise<void> => {
      if (!dailyReportProvide?.getDailyReportId()) return;

      loadingPackageInfo.value = true;

      productPackageStore.getProductPackageInventories({
        dailyReportId: dailyReportProvide?.getDailyReportId(),
        callback: {
          onSuccess: (res: any) => {
            packageInfo.value = JSON.parse(JSON.stringify(res));
            productAndPackageInventoryId.value = res?.id;
            if (res?.id) {
              getPackageReportInfo(res.id);
            }
          },
          onFinish: (_err: any) => {
            loadingPackageInfo.value = false;
          },
        },
      });
    };

    const getPackageReportInfo = async (id: string): Promise<void> => {
      loadingPackageList.value = true;

      productReportStore.getProductPackageReports({
        params: {
          productAndPackageInventoryId: id,
          page: 1,
          limit: 200,
        },
        callback: {
          onSuccess: (res: any) => {
            packageList.value = JSON.parse(JSON.stringify(res?.items));

            for (let i = 0; i < packageList?.value?.length; i++) {
              const updatedPackage = {
                ...packageList?.value[i],
                historyList: [],
                pageCount: 0,
                totalElements: 0,
                currentPage: 1,
                loading: false,
              };

              packageList.value[i] = updatedPackage;

              if (
                packageList.value[i]?.id &&
                packageList.value[i]?.currentPage
              ) {
                getPackageHistory(
                  i,
                  packageList.value[i].id,
                  packageList.value[i].currentPage
                );
              }
            }
          },
          onFinish: (_err: any) => {
            loadingPackageList.value = false;
          },
        },
      });
    };

    const getPackageHistory = async (
      index: number,
      reportId: string,
      page: number
    ): Promise<void> => {
      packageList.value[index].loading = true;

      productPackageItemStore.getProductPackageItems({
        params: {
          productAndPackageInventoryReportId: reportId,
          page: page,
          limit: 10,
        },
        callback: {
          onSuccess: (res: any) => {
            packageList.value[index].historyList = JSON.parse(
              JSON.stringify(res?.items)
            );
            packageList.value[index].pageCount = res?.totalPage;
            packageList.value[index].totalElements = res?.total;
            packageList.value[index].currentPage = res?.page;
          },
          onFinish: (_err: any) => {
            packageList.value[index].loading = false;
          },
        },
      });
    };

    const pageChange = (index: number, reportId: string, newPage: number) => {
      packageList.value[index].currentPage = newPage;
      getPackageHistory(index, reportId, newPage);
    };

    const deleteProductPackage = (id: string) => {
      AlertService.deletionAlert({
        onConfirmed: () => {
          deleteProductPackageReportById(id);
        },
      });
    };

    const deleteProductPackageReportById = async (
      id: string
    ): Promise<void> => {
      productReportStore.deleteProductPackageReport({
        id: id,
        callback: {
          onSuccess: (_res: any) => {
            getPackageInfo();
          },
        },
      });
    };

    const deleteProductPackageItem = (id: string) => {
      AlertService.deletionAlert({
        onConfirmed: () => {
          deleteProductPackageItemById(id);
        },
      });
    };

    const deleteProductPackageItemById = async (id: string): Promise<void> => {
      productPackageItemStore.deleteProductPackageItem({
        id: id,
        callback: {
          onSuccess: (_res: any) => {
            getPackageInfo();
          },
        },
      });
    };

    const toggleItem = (
      reportId: string,
      itemId: string,
      type: number,
      productId: string
    ) => {
      if (type == ProductAndPackageInventoryType.received) {
        formModal?.value?.setType(ProductAndPackageInventoryType.received);
      } else if (type == ProductAndPackageInventoryType.used) {
        formModal?.value?.setType(ProductAndPackageInventoryType.used);
      } else if (type == ProductAndPackageInventoryType.adjusted) {
        formModal?.value?.setType(ProductAndPackageInventoryType.adjusted);
      } else if (type == ProductAndPackageInventoryType.returned) {
        formModal?.value?.setType(ProductAndPackageInventoryType.returned);
      } else if (type == ProductAndPackageInventoryType.initial) {
        formModal?.value?.setType(ProductAndPackageInventoryType.initial);
      }
      formModal?.value?.setId(reportId, itemId, productId);
      formModal?.value?.show();
    };

    const toggleAddInitial = () => {
      formModal?.value?.setType(ProductAndPackageInventoryType.initial);
      isModalVisible.value = !isModalVisible.value;
    };

    const toggleAccordion = (id: string) => {
      if (openAccordions.value.has(id)) {
        openAccordions.value.delete(id);
      } else {
        openAccordions.value.add(id);
      }
    };

    return {
      packageInfo,
      packageList,
      formModal,
      loadingPackageInfo,
      loadingPackageList,
      productAndPackageInventoryOptions,
      productAndPackageInventoryId,
      ProductAndPackageInventoryType,
      isModalVisible,
      openAccordions,
      formatDate,
      getOption,
      getPackageInfo,
      pageChange,
      deleteProductPackage,
      deleteProductPackageItem,
      numberWithCommas,
      toggleItem,
      toggleAddInitial,
      toggleAccordion,
    };
  },
});
</script>
