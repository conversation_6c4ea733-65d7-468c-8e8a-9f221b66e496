<template>
  <div class="open-hole">
    <div class="row gap-10">
      <div v-if="loading" class="text-center">
        <div class="spinner-border text-primary" role="status">
          <span class="sr-only">Loading...</span>
        </div>
      </div>
      <template v-else>
        <NoEntries
          v-if="openHoleList.length === 0"
          :addNew="toggleNewOpenHoleModal"
        />
        <div v-else class="grid grid-cols-1 gap-3 md:grid-cols-2 md:gap-7 lg:grid-cols-3">
          <div
            v-for="item in openHoleList"
            :key="item.id"
            class="relative h-auto w-full bg-minicard-background text-minicard-text-light flex flex-col items-center p-4 rounded-xl border-t-2 border-active my-2 first:mt-3 last:mb-5 font-semibold md:first:mt-0 md:last:mb-0 md:m-0"
          >
            <div class="h-auto w-full flex flex-col gap-3">
              <h5 class="text-xl font-bold">{{ item.description }}</h5>
              <div
                class="flex items-center justify-between border-dashed border-b-[1px] py-3"
              >
                <span>MD (Measured Depth) (ft)</span>
                <span>
                  {{ `${numberWithCommas(item.measuredDepth)} (ft)` }}
                </span>
              </div>

              <div class="flex flex-wrap items-center gap-2">
                <div
                  class="flex items-center gap-2 border border-gray-300 border-dashed rounded py-3 px-4"
                >
                  <div>
                    {{ `${numberWithCommas(item.insideDiameter)} (in)` }}
                  </div>
                  <div>ID</div>
                </div>
                <div
                  class="flex items-center gap-2 border border-gray-300 border-dashed rounded py-3 px-4"
                >
                  <div class="text-danger">
                    {{ `${numberWithCommas(item.washout)}%` }}
                  </div>
                  <div class="text-info">Washout</div>
                </div>
              </div>
            </div>
            <div
              class="flex flex-row items-center gap-2 absolute top-0 end-0 -translate-y-1/2"
            >
              <button
                class="p-2 rounded-full bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover"
                @click="toggleEditOpenHoleModal(item.id.toString())"
              >
                <SvgIcon icon="pencilIcon" />
              </button>
              <button
                class="p-2 rounded-full bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover"
                @click="deleteOpenHole(item.id.toString())"
              >
                <span class="text-danger">
                  <SvgIcon icon="trashIcon" />
                </span>
              </button>
            </div>
          </div>
        </div>
      </template>
    </div>
  </div>
  <BottomTool
    :addNew="toggleNewOpenHoleModal"
    :showHelpWindow="showCustomize"
  />
  <OpenHoleModal
    :isVisible="isModalVisible"
    :close="toggleNewOpenHoleModal"
    ref="openHoleModal"
    :loadPage="getOpenHoles"
  />
</template>

<script lang="ts">
import NoEntries from "@/components/NoEntries.vue";
import SvgIcon from "@/constants/SvgIcon.vue";
import { numberWithCommas } from "@/utils/numberFormatter";
import AlertService from "@/services/AlertService";
import { useOpenHoleStore } from "@/stores/open-hole";
import { defineComponent, inject, onMounted, ref, type Ref } from "vue";
import BottomTool from "@/components/common/BottomTool.vue";
import OpenHoleModal from "./OpenHoleModal.vue";
import type { Provide } from "@/types/injection-types";

export default defineComponent({
  name: "open-hole",
  components: {
    SvgIcon,
    BottomTool,
    OpenHoleModal,
    NoEntries,
  },
  props: {
    showCustomize: {
      type: Function,
      required: false,
    },
  },
  setup() {
    const openHoleModal: Ref<any> = ref<typeof OpenHoleModal | null>(null);
    const loading = ref(false);
    const openHoleStore = useOpenHoleStore();
    const openHoleList = ref<any>([]);
    const dailyReportProvide = inject<Provide.DailyReport>("dailyReport");
    const isModalVisible = ref(false);

    onMounted(() => {
      if (dailyReportProvide?.getDailyReportId()) {
        getOpenHoles();
      }
    });

    const getOpenHoles = async (
      params = {
        dailyReportId: dailyReportProvide?.getDailyReportId(),
        page: 1,
        limit: 200,
      }
    ): Promise<void> => {
      loading.value = true;

      openHoleStore.getOpenHoles({
        params: params,
        callback: {
          onSuccess: (res: any) => {
            openHoleList.value = res?.items;
          },
          onFinish: (_err: any) => {
            loading.value = false;
          },
        },
      });
    };

    const toggleNewOpenHoleModal = () => {
      isModalVisible.value = !isModalVisible.value;
    };

    const toggleEditOpenHoleModal = (id: string): void => {
      openHoleModal?.value?.setId(id);
      isModalVisible.value = !isModalVisible.value;
    };

    const deleteOpenHole = (id: string) => {
      AlertService.deletionAlert({
        onConfirmed: () => {
          deleteOpenHoleById(id);
        },
      });
    };

    const deleteOpenHoleById = async (id: string): Promise<void> => {
      loading.value = true;
      openHoleStore.deleteOpenHole({
        id: id,
        callback: {
          onSuccess: (_res: any) => {
            getOpenHoles();
          },
          onFinish: (_err: any) => {
            loading.value = false;
          },
        },
      });
    };

    return {
      loading,
      openHoleList,
      openHoleModal,
      isModalVisible,
      getOpenHoles,
      numberWithCommas,
      deleteOpenHole,
      toggleEditOpenHoleModal,
      toggleNewOpenHoleModal,
    };
  },
});
</script>
