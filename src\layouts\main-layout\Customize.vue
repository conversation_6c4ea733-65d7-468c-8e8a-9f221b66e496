<template>
  <div
    v-if="isVisible"
    class="fixed top-0 right-0 bottom-0 left-0 z-40 flex items-center"
  >
    <!-- Dark overlay background -->
    <div class="absolute inset-0 bg-sidebar-background opacity-25"></div>
    <div
      class="relative bg-card-background text-card-text-light max-h-11/12 w-11/12 flex flex-col items-center gap-3 mx-auto my-6 p-5 rounded-xl overflow-y-scroll md:overflow-hidden md:w-1/2 md:h-1/2 md:text-lg"
    >
      <div class="h-auto w-full flex flex-row items-center justify-between">
        <h3 class="text-lg font-bold">
          {{ currentInfo?.title || "" }}
        </h3>
        <span class="cursor-pointer" @click="toggleCustomize">
          <SvgIcon icon="closeModalIcon" />
        </span>
      </div>
      <div class="h-auto w-full overflow-y-auto">
        <div v-if="currentInfo?.content" v-html="currentInfo.content"></div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, computed, type PropType } from "vue";
import SvgIcon from "../../constants/SvgIcon.vue";
import { EGeneralTab, EMudPropertiesTab } from "../../constants/daily-report";

const info = [
  {
    value: EGeneralTab.CasedHole,
    title: "What is a Cased Hole?",
    content:
      '<p class="mb-0"> Cased holes are sections of a wellbore where casing (metallic or non-metallic pipe) is installed to provide structural support, isolation, and control of fluids. The type of cased hole depends on its specific purpose and location within the well. Here are some common types of cased holes that you may encounter:<br/><br/>Conductor Casing: Conductor casing is the first casing string set in a well, and it extends from the surface to a certain depth below the ground or seabed. Its primary function is to provide structural support for the wellbore and prevent collapse of the hole near the surface.<br/><br/>Surface Casing: Surface casing is set below the conductor casing and typically extends to a shallower depth. It serves to isolate shallow, potentially unstable formations, protect freshwater aquifers, and provide a foundation for the subsequent casing strings.<br/><br/>Intermediate Casing: Intermediate casing is set at intermediate depths within the wellbore. Its purpose varies but may include isolating troublesome zones, controlling lost circulation, or providing additional structural support.<br/><br/>Production Casing: Production casing is set to isolate and support the production zone or reservoir. It serves as a conduit for hydrocarbon production from the reservoir to the surface. Its size and specifications depend on the production requirements.<br/><br/>Liner: A liner is a casing string that is installed inside another casing string, typically in the lower part of the well. It may be used to extend the casing to a greater depth, isolate problematic zones, or provide additional support without reaching the surface.<br/><br/>Scab Liner: A scab liner is a relatively short casing string used to repair a section of damaged or corroded casing. It is typically installed inside the damaged section to restore well integrity.<br/><br/>Cemented Casing: This refers to casing strings that have been cemented in place to ensure wellbore integrity, isolation of formations, and zonal isolation.<br/><br/>Slotted Liner or Screen: Slotted liners or screens are used in wells to allow fluid flow from the formation while preventing the ingress of sand or other debris. They are often used in open-hole completions.<br/><br/>Hanger Casing: Hanger casing is used in wells with subsea completions, where a casing hanger supports the casing strings within a subsea wellhead assembly.<br/><br/>Packer Casing: Packer casing is used in wells with packer systems, which are devices that create seals or isolate zones within the wellbore.</p>',
  },
  {
    value: EGeneralTab.OpenHold,
    title: "What is an Open Hole?",
    content:
      '<p class="mb-0"> An "Open Hole" refers to the section of a wellbore that has been drilled but is not yet cased or lined with casing or tubing. In open-hole drilling, the wellbore is left in its natural state, allowing for direct contact with the surrounding geological formations. Open holes are typically encountered during the drilling process before casing is installed. The purpose of open-hole sections varies and may include geological evaluation, wellbore stability assessment, and the potential for hydrocarbon discovery.<br/><br/>Types of Open Hole:<br/><br/>Different types of open holes can be encountered during drilling operations, depending on the well\'s objectives and geological conditions. Some common types of open holes include:<br/><br/>Exploratory Open Hole: This is the initial open-hole section drilled to explore the subsurface and evaluate geological formations for potential hydrocarbon reservoirs.<br/><br/>Production Open Hole: In some cases, a well may have an open-hole production section where hydrocarbons are produced directly from the formation without casing.<br/><br/>Logging Open Hole: An open-hole section may be specifically drilled to facilitate logging tools, such as wireline or logging while drilling (LWD/MWD) tools, to collect formation evaluation data.<br/><br/>Evaluation Open Hole: Open holes are often drilled for the purpose of evaluating formation properties, including porosity, permeability, and lithology.<br/><br/>Directional Open Hole: In directional drilling, open-hole sections may be drilled with specific inclinations and azimuths to intersect target zones.<br/><br/>Geotechnical Open Hole: Open-hole sections may be drilled for geotechnical purposes, such as assessing rock and soil properties for civil engineering projects.</p>',
  },
  {
    value: EGeneralTab.DrillString,
    title: "What is a Drill String?",
    content:
      '<p class="mb-0">A "Drill String" refers to the assembly of drilling tools, drill pipes, and other components used in drilling operations to advance the wellbore into the subsurface. The drill string is typically composed of multiple sections of drill pipe, each joined by tool joints, and may include other specialized tools and equipment.<br/><br/>Types of Drill Strings:<br/><br/>Different types of drill strings may be used in drilling operations, depending on the drilling objectives, well design, and geological conditions. Some common types of drill strings include:<br/><br/>Standard Drill String: This is a typical drill string configuration consisting of drill pipe sections joined by tool joints. It is commonly used in various drilling applications.<br/><br/>Heavyweight Drill String: Heavyweight drill strings include heavyweight drill pipe or drill collars to provide additional weight on bit (WOB) and stability in challenging drilling conditions.<br/><br/>irectional Drill String: Directional drilling often involves specialized drill string configurations designed to achieve specific wellbore trajectories, including bent sub assemblies and steerable tools.<br/><br/>Slim Hole Drill String: Slim hole drill strings are used in narrow wellbores or when smaller-diameter wells are desired, such as in slim hole drilling or geothermal drilling.<br/><br/>Wireline Drill String: Wireline drill strings are used in wireline coring and well logging operations, where tools are lowered into the wellbore on a wireline cable.<br/><br/>Coiled Tubing: Coiled tubing drill strings consist of continuous tubing wound on a reel and are used in various well intervention and completion operations.</p>',
  },
  {
    value: EGeneralTab.Bits,
    title: "What is a Drill Bit?",
    content:
      '<p class="mb-0">A "Drill Bit" is a cutting tool used in drilling operations to create a hole in the earth\'s subsurface. Drill bits are attached to the bottom of the drill string and are responsible for breaking and removing rock or other materials as the wellbore is advanced. Drill bits are crucial components in the drilling process and come in various types and designs to suit different geological conditions and drilling objectives.<br/><br/>Types of Drill Bits:<br/><br/>There are several types of drill bits used in drilling operations, each designed for specific applications and geological conditions. Some common types of drill bits include:<br/><br/>PDC (Polycrystalline Diamond Compact) Bits: PDC bits use diamond-impregnated cutters and are known for their efficiency in drilling through soft to medium-hard formations.<br/><br/>Roller Cone Bits: Roller cone bits have rotating cones with hardened teeth that crush and cut the rock. They are versatile and can be used in a wide range of formations.<br/><br/>Diamond-Impregnated Bits: These bits have a matrix embedded with industrial diamonds. They are suitable for drilling through hard and abrasive formations.<br/><br/>Fixed Cutter Bits: Fixed cutter bits, such as diamond bits and PDC bits, have non-rotating cutting elements that shear or scrape the rock. They are used in various applications, including oil and gas drilling and mining.<br/><br/>Tri-cone Bits: Tri-cone bits have three cones with different tooth designs and are often used in medium to hard formations.<br/><br/>Reaming Bits: Reaming bits are used to enlarge the hole diameter after the initial drilling and can be essential for well completion and casing installation.<br/><br/>Directional and Steerable Bits: These bits are designed for directional drilling and are equipped with mechanisms that allow them to control the wellbore\'s trajectory.<br/><br/>Underreamer Bits: Underreamer bits are used to enlarge the hole diameter below a restriction in the wellbore, such as a previously set casing.<br/><br/>The choice of drill bit type depends on the geological formations, drilling objectives, and wellbore conditions. Drill bit selection is a critical factor in drilling efficiency and wellbore integrity.</p>',
  },
  {
    value: EGeneralTab.Nozzles,
    title: "What is a Nozzle?",
    content:
      '<p class="mb-0">In the context of drilling operations, a "Nozzle" refers to a specialized component that directs the flow of drilling fluids, such as drilling mud or drilling fluid additives, from the mud pumps into the wellbore. Nozzles are typically located in the drill string or drill pipe near the bottom of the well, and they play a crucial role in various aspects of drilling, including cleaning the bottom of the hole, cooling the drill bit, and optimizing fluid flow.<br/><br/>Types of Nozzles:<br/><br/>There are several types of nozzles used in drilling operations, each designed for specific purposes and fluid flow characteristics. Some common types of nozzles include:<br/><br/>Fan Nozzles: These nozzles emit a fan-shaped spray pattern, which is useful for cleaning the bottom of the wellbore and distributing drilling fluids evenly.<br/><br/>Jet Nozzles: Jet nozzles produce a high-velocity, focused stream of drilling fluid. They are often used for drilling hard formations and cutting through tough materials.<br/><br/>Crossflow Nozzles: Crossflow nozzles have multiple orifices that emit fluid in multiple directions simultaneously, improving hole cleaning efficiency.<br/><br/>Variable Flow Nozzles: These nozzles allow for adjustable flow rates, enabling operators to control the amount of fluid being pumped through them.<br/><br/>Solid Nozzles: Solid nozzles have a single, fixed orifice and are commonly used for specific applications, such as fluid sampling or pressure testing.<br/><br/>Replaceable Nozzles: Some nozzles are designed to be easily replaceable, allowing for quick changes to optimize drilling performance.<br/><br/>The choice of nozzle type and size depends on the drilling objectives, wellbore conditions, and the specific requirements of the drilling operation. Proper nozzle selection is important for efficient drilling fluid management and hole cleaning.</p>',
  },
  {
    value: EMudPropertiesTab.Solids,
    title: "What is Solid?",
    content:
      '<p class="mb-0">Characterization and measurement of various solid components present in the drilling mud. It includes parameters related to the composition and properties of solids, which can significantly impact the mud\'s performance and behavior.</p>',
  },
];

export default defineComponent({
  name: "customize-layout",
  props: {
    currentTab: {
      type: Number,
      required: true,
    },
    isVisible: {
      type: Boolean,
      required: true,
    },
    toggleCustomize: {
      type: Function as PropType<() => void>,
      required: true,
    },
  },
  components: { SvgIcon },
  setup(props) {
    const currentInfo = computed(() => {
      return info.filter((item) => item.value === props.currentTab)[0] || null;
    });

    return { currentInfo };
  },
});
</script>
