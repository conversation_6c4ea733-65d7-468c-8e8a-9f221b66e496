<template>
  <div class="well-plan">
    <div
      class="bg-card-background text-card-text-light w-11/12 mx-auto p-4 h-auto rounded-xl"
    >
      <div class="h-auto w-full flex flex-row p-4 items-center justify-between md:mb-4">
        <h1 class="text-lg font-bold">Plans</h1>
        <div
          class="bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover rounded-md px-4 py-2 font-semibold flex flex-row items-center gap-2 justify-between md:px-4 md:py-3 md:text-xl"
          @click="toggleNewPlan"
        >
          <SvgIcon icon="addIcon" />
          New Plan
        </div>
      </div>

      <div v-if="loading" class="text-center">
        <div class="spinner-border text-primary" role="status">
          <span class="sr-only">Loading...</span>
        </div>
      </div>

      <el-empty v-else-if="planList.length === 0" description="No Data" />

      <!-- Add grid classes to this container div -->
      <div v-else class="grid grid-cols-1 gap-6 md:grid-cols-2 md:gap-7 lg:grid-cols-3">
        <div
          v-for="item in planList"
          :key="item.id"
          class="bg-minicard-background text-minicard-text-light rounded-lg shadow-sm border-t-2 border-active relative first:mt-0 last:mb-0 md:mt-0 md:mb-0"
        >
          <div class="flex flex-col gap-5 font-semibold text-lg p-4">
            <h5 class="text-lg">
              {{ `${item.day} Days` }}
            </h5>
            <div
              class="flex items-center justify-between border-b-[1px] border-dashed pb-3"
            >
              <span>Measured Depth</span>
              <span class="text-success">
                {{ `${item?.mudDepth} (ft)` }}
              </span>
            </div>
            <div class="flex items-center justify-between">
              <span>Cost</span>
              <span>
                {{ `$${numberWithCommas(item?.cost || 0)}` }}
              </span>
            </div>
          </div>
          <div
            class="flex flex-row items-center gap-2 absolute top-0 right-0 -translate-y-1/2"
          >
            <el-tooltip content="Edit Plan">
              <div
                class="rounded-full p-1.5 bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover"
                @click="toggleEditPlan(item?.id?.toString() || '')"
              >
                <SvgIcon icon="pencilIcon" classname="md:h-7 md:w-7" />
              </div>
            </el-tooltip>
            <el-tooltip content="Delete Plan">
              <button
                class="rounded-full p-1.5 bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover"
                @click="deletePlan(item?.id || '')"
              >
                <span class="text-danger">
                  <SvgIcon icon="trashIcon" classname="md:h-7 md:w-7" />
                </span>
              </button>
            </el-tooltip>
          </div>
        </div>
      </div>
      <!--end::Card body-->
    </div>
    <PlanModal
      :isVisible="isModalVisible"
      :close="toggleNewPlan"
      ref="planModal"
      :loadPage="getPlans"
      :wellId="id"
    />
  </div>
</template>

<script lang="ts">
import TablePagination from "@/components/common/TablePagination.vue";
import SvgIcon from "@/constants/SvgIcon.vue";
import { numberWithCommas } from "@/utils/numberFormatter";
import AlertService from "@/services/AlertService";
import { usePlanStore } from "@/stores/plan";
import { defineComponent, onMounted, ref, type Ref } from "vue";
import PlanModal from "./PlanModal.vue";

export default defineComponent({
  name: "well-plan",
  props: {
    id: {
      type: String,
      required: true,
    },
  },
  components: {
    SvgIcon,
    TablePagination,
    PlanModal,
  },
  setup(props) {
    const planStore = usePlanStore();
    const planList = ref<Plan.Info[]>([]);
    const loading = ref(false);
    const planModal: Ref<any> = ref<typeof PlanModal | null>(null);
    const isModalVisible = ref(false);

    onMounted(() => {
      getPlans();
    });

    const getPlans = async (
      params: Plan.GetListParams = {
        wellId: props?.id,
        page: 1,
        limit: 200,
      }
    ): Promise<void> => {
      loading.value = true;

      planStore.getPlans({
        params: params,
        callback: {
          onSuccess: (res: any) => {
            planList.value = res?.items;
          },
          onFinish: (_err: any) => {
            loading.value = false;
          },
        },
      });
    };

    const changePosition = (oldPosition: number, newPosition: number) => {
      const tem = planList.value[newPosition];
      planList.value[newPosition] = planList.value[oldPosition];
      planList.value[oldPosition] = tem;
    };

    const toggleNewPlan = (): void => {
      isModalVisible.value = !isModalVisible.value;
    };

    const toggleEditPlan = (id: string): void => {
      planModal?.value?.setId(id);
      isModalVisible.value = !isModalVisible.value;
    };

    const deletePlan = (id: string) => {
      AlertService.deletionAlert({
        onConfirmed: () => {
          deletePlanById(id);
        },
      });
    };

    const deletePlanById = async (id: string): Promise<void> => {
      loading.value = true;

      planStore.deletePlan({
        id: id,
        callback: {
          onSuccess: (_res: any) => {
            getPlans();
          },
          onFinish: (_err: any) => {
            loading.value = false;
          },
        },
      });
    };

    return {
      numberWithCommas,
      changePosition,
      toggleNewPlan,
      toggleEditPlan,
      deletePlan,
      getPlans,
      planList,
      planModal,
      loading,
      isModalVisible,
    };
  },
});
</script>

<style scoped>
.well-plan {
  .card-item {
    width: 300px;
    height: fit-content;
  }

  .border-blue-light {
    border-color: #e4e6ef !important;
  }
}
</style>
