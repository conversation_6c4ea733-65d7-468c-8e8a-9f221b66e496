import{d as m,u as f,j as x,q as v,x as _,e as k,_ as g,c as n,a as t,o as r}from"./index-BmHWvWFS.js";import{u as h}from"./user-CVSNmFaf.js";import{_ as b}from"./agency-WmjrUBZ-.js";import"./handleFailure-WBgBpurp.js";const y=m({name:"accept-invite",components:{},setup(){var u,l;const o=h(),e=f(),a=k(),c=((l=(u=x().params)==null?void 0:u.token)==null?void 0:l.toString())||"",s=v(!1);_(()=>{i()});const i=async()=>{s.value=!0,o.acceptInviteToken({token:c,callback:{onSuccess:d=>{e.setAuth(d),a.push({path:"/register-invited-user"})},onFinish:()=>{s.value=!1}}})};return{loading:s,acceptInvite:i}}}),S={class:"bg-screen-background text-card-text-dark min-h-screen max-w-screen flex flex-col items-center justify-center text-center md:text-lg"},A={key:0,class:"text-center p-5"},I={key:1};function $(o,e,a,p,c,s){return r(),n("div",S,[o.loading?(r(),n("div",A,e[0]||(e[0]=[t("div",{class:"spinner-border text-primary",role:"status"},[t("span",{class:"sr-only"},"Loading...")],-1)]))):(r(),n("div",I,e[1]||(e[1]=[t("h1",{class:"font-extrabold mb-4"},"Accept Invitation",-1),t("div",{class:"mb-3"},[t("img",{src:b,class:"w-4/5 h-auto mx-auto",alt:""})],-1)])))])}const q=g(y,[["render",$]]);export{q as default};
