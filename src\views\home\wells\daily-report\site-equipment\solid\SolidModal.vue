<template>
  <div
    v-if="isVisible"
    class="fixed top-0 right-0 bottom-0 left-0 z-40 flex items-center"
  >
    <!-- Dark overlay background -->
    <div class="absolute inset-0 bg-sidebar-background opacity-25"></div>
    <div
      class="relative bg-card-background text-card-text-light max-h-11/12 w-11/12 flex flex-col items-center gap-3 mx-auto my-6 p-5 rounded-xl md:w-2/5 lg:w-2/5 lg:h-auto"
    >
      <div class="flex flex-row h-auto w-full items-center justify-between">
        <h3 class="text-lg font-bold">
          {{
            `${
              id
                ? "Edit Solids Control Equipment"
                : "Add Solids Control Equipment"
            }`
          }}
        </h3>
        <span class="cursor-pointer" @click="closeModal">
          <SvgIcon icon="closeModalIcon" />
        </span>
      </div>

      <el-form
        id="solid_form"
        @submit.prevent="submit"
        :model="targetData"
        :rules="rules"
        ref="formRef"
        class="h-auto w-full flex flex-col gap-3"
      >
        <div class="flex flex-col gap-1">
          <label class="font-bold"
            >Type<span class="text-danger-active font-light">*</span>
          </label>
          <el-form-item prop="typeId">
            <el-select v-model="targetData.typeId" placeholder="" clearable>
              <el-option
                v-for="item in solidTypes"
                :key="item.value"
                :label="item.key"
                :value="item.value"
                name="typeId"
              />
            </el-select>
          </el-form-item>
        </div>

        <div class="flex flex-col gap-1">
          <label class="font-bold"
            >Screen<span class="text-danger-active font-light">*</span
            ><el-popover placement="top" :width="350" trigger="hover">
              <template #reference>
                <i class="fas fa-exclamation-circle ms-2 fs-7"></i>
              </template>
              <span
                >The "Screen" typically refers to the mesh size and
                configuration of the screens used in the equipment. The screen
                mesh size defines the size of particles that can pass through
                the screen, while the number of layers may indicate the screen's
                efficiency in capturing solids. (i.e. 140 x 4)</span
              >
            </el-popover></label
          >
          <el-form-item prop="screen">
            <el-input
              v-model="targetData.screen"
              placeholder=""
              name="screen"
            ></el-input>
          </el-form-item>
        </div>

        <div class="h-auto w-full flex flex-row items-center gap-2">
          <button
            type="button"
            class="bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover rounded-md px-4 py-2 font-semibold"
            @click="closeModal"
            :disabled="loading"
          >
            Discard
          </button>
          <button
            class="bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover rounded-md px-4 py-2 font-semibold"
            type="submit"
            :disabled="loading"
          >
            <span v-if="!loading" class="indicator-label"> Save </span>
            <span v-if="loading" class="indicator-progress">
              Please wait...
              <span
                class="spinner-border spinner-border-sm align-middle ms-2"
              ></span>
            </span>
          </button>
        </div>
      </el-form>
    </div>
  </div>
</template>

<script lang="ts">
import SvgIcon from "@/constants/SvgIcon.vue";
import { useSolidControlEquipmentStore } from "@/stores/solid-control-equipment";
import { useSolidControlEquipmentTypeStore } from "@/stores/solid-control-equipment-type";
import { defineComponent, inject, onMounted, ref, watch } from "vue";
import { useRoute } from "vue-router";
import type { Provide } from "@/types/injection-types";

export default defineComponent({
  name: "solid-modal",
  components: { SvgIcon },
  props: {
    isVisible: {
      type: Boolean,
      required: true,
    },
    close: {
      type: Function,
      required: true,
    },
    loadPage: {
      type: Function,
      default: () => {},
      required: true,
    },
    zIndex: {
      type: Number,
      default: 40,
      required: false,
    },
  },
  setup(props) {
    const route = useRoute();
    const solidControlEquipmentStore = useSolidControlEquipmentStore();
    const solidControlEquipmentTypeStore = useSolidControlEquipmentTypeStore();
    const modal = ref(false);
    const initialForm = {
      dailyReportId: "",
      typeId: null,
      screen: "",
    };
    const targetData = ref<any>(JSON.parse(JSON.stringify(initialForm)));
    const solidTypes = ref<any>([]);
    const formRef = ref<null | HTMLFormElement>(null);
    const loading = ref<boolean>(false);
    const id = ref("");
    const dailyReportProvide = inject<Provide.DailyReport>("dailyReport");

    watch(id, (newValue) => {
      if (newValue !== "") {
        getSolidDetails();
        getSolidTypes();
      }
    });

    watch(
      () => props.isVisible,
      (newValue) => {
        if (newValue === false) {
          id.value = "";
          reset();
          formRef?.value?.resetFields();
        }
      }
    );

    onMounted(() => {
      getSolidTypes();
    });

    const getSolidTypes = async (): Promise<void> => {
      solidControlEquipmentTypeStore.getSolidControlEquipmentTypes({
        callback: {
          onSuccess: (res: any) => {
            solidTypes.value = res?.map((item: any) => {
              return {
                value: item?.id,
                key: item?.name,
              };
            });
          },
        },
      });
    };

    const getSolidDetails = async (): Promise<void> => {
      solidControlEquipmentStore.getSolidControlEquipmentDetails({
        id: id.value,
        callback: {
          onSuccess: (res: any) => {
            targetData.value = {
              dailyReportId: dailyReportProvide?.getDailyReportId(),
              typeId: res?.type?.id,
              screen: res?.screen,
            };
          },
        },
      });
    };

    const updateSolid = async (param: any): Promise<void> => {
      loading.value = true;
      solidControlEquipmentStore.updateSolidControlEquipment({
        id: id.value,
        params: param,
        callback: {
          onSuccess: (_res: any) => {
            if (props?.loadPage) {
              props?.loadPage();
            }
            closeModal();
          },
          onFinish: (_err: any) => {
            loading.value = false;
          },
        },
      });
    };

    const createSolid = async (param: any): Promise<void> => {
      loading.value = true;
      solidControlEquipmentStore.createSolidControlEquipment({
        params: param,
        callback: {
          onSuccess: (_res: any) => {
            if (props?.loadPage) {
              props?.loadPage();
            }
            closeModal();
          },
          onFinish: (_err: any) => {
            loading.value = false;
          },
        },
      });
    };

    const closeModal = () => {
      props.close();
    };

    const setId = (pumpId: string) => {
      id.value = pumpId.toString();
    };

    const rules = ref({
      typeId: [
        {
          required: true,
          message: "Please select Type",
          trigger: "change",
        },
      ],
      screen: [
        {
          required: true,
          message: "Please type Screen",
          trigger: ["change", "blur"],
        },
      ],
    });

    const submit = () => {
      if (!formRef.value) {
        return;
      }

      formRef.value.validate((valid: boolean) => {
        if (valid) {
          if (id?.value) {
            updateSolid({
              ...targetData?.value,
              dailyReportId: dailyReportProvide?.getDailyReportId(),
            });
          } else {
            dailyReportProvide?.createDailyReport({
              wellId: route?.params?.id as string,
              callback: {
                onSuccess: (_res: string) => {
                  createSolid({
                    ...targetData?.value,
                    dailyReportId: dailyReportProvide?.getDailyReportId(),
                  });
                },
              },
            });
          }
        }
      });
    };

    const reset = () => {
      targetData.value = JSON.parse(JSON.stringify(initialForm));
    };

    return {
      id,
      modal,
      rules,
      loading,
      targetData,
      formRef,
      solidTypes,
      closeModal,
      submit,
      setId,
      reset,
    };
  },
});
</script>
