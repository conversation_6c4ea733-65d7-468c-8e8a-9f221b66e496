import{S as M}from"./SvgIcon-CfrWCA-H.js";import{d as _,c as p,a as t,b as a,w as y,r as d,o as l,u as N,e as P,_ as $,f as V,i as z,g as I,J as b,U as g,h as E,j as q,t as w,F as C,k as T,l as O,n as U,m as H,p as D,q as L,s as F,v as J,x as X,C as B,y as j,z as G}from"./index-DalLS0_6.js";import{_ as K}from"./opslink-light-DYL7OcHB.js";const Q={class:"flex flex-row items-center bg-header-background pt-15 md:justify-center"},W={class:"h-auto w-full ps-20 md:ps-0 md:flex md:justify-center"},Y=_({__name:"Header",setup(e){return(o,s)=>{const i=d("router-link");return l(),p("div",Q,[t("div",{class:"p-4 cursor-pointer",onClick:s[0]||(s[0]=n=>o.$emit("toggle-sidebar"))},[a(M,{icon:"sidebarMenu",classname:"text-icon-dark"})]),t("div",W,[a(i,{to:"/",class:"flex flex-row items-center gap-4"},{default:y(()=>s[1]||(s[1]=[t("img",{alt:"Logo",src:"/media/logos/opslink-dark.png",class:"h-20 w-auto md:h-40"},null,-1)])),_:1})])])}}}),Z=_({name:"sidebar-logo",components:{SvgIcon:M},setup(){const e=N(),o=P();return{signOut:()=>{e.logout(),o.push({name:"sign-in"})}}}}),ee={class:"h-1/12 w-full px-6 bg-sidebar-background border-b-4 border-light-border flex flex-row justify-between items-center"},oe={class:"h-auto w-full md:flex md:items-center md:justify-center"},te={class:"h-auto w-full flex flex-row items-center justify-end md:w-2/12"};function se(e,o,s,i,n,v){const f=d("router-link"),u=d("SvgIcon");return l(),p("div",ee,[t("div",oe,[a(f,{to:"/"},{default:y(()=>o[1]||(o[1]=[t("img",{alt:"Logo",src:K,class:"h-15 w-auto md:h-40"},null,-1)])),_:1})]),t("div",te,[t("span",{class:"cursor-pointer app-sidebar-logo-default",onClick:o[0]||(o[0]=(...m)=>e.signOut&&e.signOut(...m))},[a(u,{icon:"logoutIcon",classname:"text-icon-light"})]),a(u,{icon:"doubleLeftArrow",classname:"text-icon-light"})])])}const ne=$(Z,[["render",se]]),re=_({name:"sidebar-menu",components:{SvgIcon:M},props:{close:{type:Function,required:!0}},setup(e){const{t:o,te:s}=V(),i=q(),n=z("userInfo");return{userProfile:n==null?void 0:n.userInfo,userRoleOptions:I,getUserRoles:()=>{var c,x,r,k,R;return(x=(c=n==null?void 0:n.userInfo)==null?void 0:c.value)!=null&&x.roles?((R=(k=(r=n==null?void 0:n.userInfo)==null?void 0:r.value)==null?void 0:k.roles)==null?void 0:R.map(h=>{var A;return{value:h==null?void 0:h.value,label:(A=E(h==null?void 0:h.value,I))==null?void 0:A.label}}))||[]:[]},translate:c=>s(c)?o(c):c,hasActiveChildren:c=>i.path.indexOf(c)!==-1,closeSidebar:()=>{e.close()}}},data(){const e=[{sectionTitle:"Home",route:"/wells/overview",icon:"homeMenu"},{sectionTitle:"Companies",route:"/companies",icon:"customerMenu"},{sectionTitle:"My Company",route:"/my-company",icon:"customerMenu"},{sectionTitle:"User Management",route:"/users",icon:"useMenu"},{sectionTitle:"Settings",route:"/settings",icon:"settingMenu"}];return{menuConfig:(()=>{const s=[e[0]];return b.checkRole(g.SystemAdmin)&&s.push(e[1]),(b.checkRole(g.CompanyAdmin)||b.checkRole(g.Supervisor)||b.checkRole(g.Engineer))&&s.push(e[2]),s.push(e[3]),(b.checkRole(g.SystemAdmin)||b.checkRole(g.CompanyAdmin)||b.checkRole(g.Supervisor))&&s.push(e[4]),s})()}}}),ae={class:"h-full w-full flex flex-col bg-side"},ie={class:"bg-sidebar-userinfo-background h-2/12 w-full flex flex-row items-center gap-2 px-6 py-3 md:text-xl"},le=["src"],ce={class:"flex flex-col"},de={class:"font-semibold"},ue={class:"text-xs font-semibold md:text-sm"},pe={class:"relative group"},fe={class:"cursor-pointer"},me={class:"hidden group-hover:block bg-popup-background h-auto w-auto px-2 py-3 rounded-lg absolute top-7 -left-7 z-20"},be={class:"h-full w-full"},ge={class:"pt-6 px-5"},_e={class:"menu-title group-hover:text-sidebar-text-hover"};function he(e,o,s,i,n,v){var m,S,c,x;const f=d("SvgIcon"),u=d("router-link");return l(),p("div",ae,[t("div",ie,[t("img",{class:"h-4/5 w-auto rounded-2xl",src:((m=e.userProfile)==null?void 0:m.avatar)||"/media/avatars/blank.png"},null,8,le),t("div",ce,[t("div",de,w(`${(S=e.userProfile)==null?void 0:S.firstName} ${(c=e.userProfile)==null?void 0:c.lastName}`),1),t("div",ue,w((x=e.userProfile)==null?void 0:x.email),1),(l(!0),p(C,null,T(e.getUserRoles(),r=>(l(),p("div",{key:r.value,class:"font-semibold text-sm md:text-lg"},w(r.label),1))),128))]),t("div",pe,[t("div",fe,[a(f,{icon:"settingUser",classname:"text-icon-light"})]),t("div",me,[o[1]||(o[1]=t("div",{class:"absolute -top-2 left-1/2 -translate-x-1/2 w-0 h-0 border-l-8 border-r-8 border-b-8 border-transparent border-b-popup-background"},null,-1)),a(u,{to:"/my-profile"},{default:y(()=>o[0]||(o[0]=[t("span",{class:"text-popup-text font-semibold"},"Settings",-1)])),_:1})])])]),t("div",be,[t("div",ge,[(l(!0),p(C,null,T(e.menuConfig,(r,k)=>(l(),p(C,{key:k},[r.sectionTitle&&r.route?(l(),p("div",{key:0,class:U({show:e.hasActiveChildren(r.route)})},[t("div",null,[r.route?(l(),H(u,{key:0,class:"group mx-2 px-2 py-3 bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover rounded-md font-semibold flex flex-row items-center gap-2","active-class":"active",to:r.route,onClick:e.closeSidebar},{default:y(()=>[a(f,{icon:r.icon,classname:"group-hover:text-icon-dark"},null,8,["icon"]),t("span",_e,w(e.translate(r.sectionTitle)),1)]),_:2},1032,["to","onClick"])):O("",!0)])],2)):O("",!0)],64))),128))])])])}const ve=$(re,[["render",he]]),Se=_({name:"sidebar",props:{displaySidebar:{type:Boolean,required:!0}},methods:{closeSidebar(){this.$emit("update:displaySidebar",!1)}},components:{SidebarLogo:ne,SidebarMenu:ve}});function xe(e,o,s,i,n,v){const f=d("SidebarLogo"),u=d("SidebarMenu");return l(),p(C,null,[t("div",{class:U(["fixed left-0 top-0 bottom-0 right-0 bg-sidebar-background opacity-10 transform ease-in-out",{"translate-x-0":e.displaySidebar,"-translate-x-full":!e.displaySidebar}]),onClick:o[0]||(o[0]=(...m)=>e.closeSidebar&&e.closeSidebar(...m))},null,2),t("div",{class:U(["flex flex-col fixed top-0 left-0 h-full w-3/4 bg-sidebar-background transform transition-transform duration-300 ease-in-out z-10 pt-15 md:w-2/5",{"translate-x-0":e.displaySidebar,"-translate-x-full":!e.displaySidebar}]),onClick:o[1]||(o[1]=D(()=>{},["stop"]))},[a(f),a(u,{close:e.closeSidebar},null,8,["close"])],2)],64)}const ye=$(Se,[["render",xe]]),$e=_({__name:"UserInfoProvide",setup(e){const o=L(b.getUserInfo());return J("userInfo",{userInfo:o,updateUserInfo:i=>{const n={...o.value,...i};o.value={...n},b.saveUserInfo(JSON.stringify(n))}}),(i,n)=>F(i.$slots,"default")}}),ke=_({name:"chat-box",setup(){return X(()=>{{const e=document.createElement("script");e.type="text/javascript",e.textContent=`
      window.embeddedChatbotConfig = {
        chatbotId: "${B}",
        domain: "${j}"
      };
    `,document.head.appendChild(e);const o=document.createElement("script");o.src=G,o.setAttribute("chatbotId",B),o.setAttribute("domain",j),o.defer=!0,document.head.appendChild(o)}}),{}}});function we(e,o,s,i,n,v){return l(),p("div")}const Ce=$(ke,[["render",we]]),Me=_({name:"default-layout",components:{Sidebar:ye,SvgIcon:M,UserInfoProvide:$e,Header:Y,ChatBox:Ce},setup(){const e=L(!1);return{displaySidebar:e,toggleSidebar:()=>{e.value=!e.value}}}}),Ue={class:"flex flex-col h-screen w-full bg-screen-background overflow-y-scroll"};function Re(e,o,s,i,n,v){const f=d("Header"),u=d("Sidebar"),m=d("RouterView"),S=d("UserInfoProvide");return l(),H(S,null,{default:y(()=>[t("div",Ue,[a(f,{onToggleSidebar:e.toggleSidebar},null,8,["onToggleSidebar"]),a(u,{displaySidebar:e.displaySidebar,"onUpdate:displaySidebar":e.toggleSidebar},null,8,["displaySidebar","onUpdate:displaySidebar"]),t("div",null,[a(m)])])]),_:1})}const Oe=$(Me,[["render",Re]]);export{Oe as default};
