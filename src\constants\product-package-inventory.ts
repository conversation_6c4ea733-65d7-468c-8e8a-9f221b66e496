export enum ProductAndPackageInventoryType {
  "initial" = 1,
  "received" = 2,
  "used" = 3,
  "adjusted" = 4,
  "returned" = 5,
}

export const productAndPackageInventoryOptions = [
  { value: ProductAndPackageInventoryType.initial, label: "Initial" },
  { value: ProductAndPackageInventoryType.received, label: "Received" },
  { value: ProductAndPackageInventoryType.used, label: "Used" },
  { value: ProductAndPackageInventoryType.adjusted, label: "Adjusted" },
  { value: ProductAndPackageInventoryType.returned, label: "Returned" },
];
