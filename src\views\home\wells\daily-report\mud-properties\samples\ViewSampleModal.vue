<template>
  <div
    v-if="isVisible"
    class="fixed top-0 right-0 bottom-0 left-0 z-40 flex items-center"
  >
    <!-- Dark overlay background -->
    <div class="absolute inset-0 bg-sidebar-background opacity-25"></div>
    <div
      class="relative bg-card-background text-card-text-light max-h-11/12 w-11/12 flex flex-col items-center gap-3 mx-auto my-6 p-5 rounded-xl overflow-y-scroll md:overflow-hidden lg:w-auto lg:h-auto"
    >
      <div class="flex flex-row h-auto w-full items-center justify-between">
        <h3 class="text-lg font-bold">View Sample</h3>
        <span class="cursor-pointer ms-auto" @click="closeModal">
          <SvgIcon icon="closeModalIcon" />
        </span>
      </div>

      <div v-if="loading" class="text-center">
        <div class="spinner-border text-primary" role="status">
          <span class="sr-only">Loading...</span>
        </div>
      </div>
      <div v-else class="font-semibold">
        <div class="flex flex-col gap-2 md:gap-7">
          <div class="flex flex-col gap-2 md:flex-row md:gap-4">
            <div class="flex flex-col gap-2">
              <div class="h-auto w-full flex items-center justify-between">
                <span>Fluid Type: </span>
                <span>{{
                  getOption(sampleData?.fluidType, fluidTypeOptions)?.label ||
                  ""
                }}</span>
              </div>
              <div class="h-auto w-full flex items-center justify-between">
                <span>Weighted Mud</span>
                <el-form-item prop="weightedMud" style="margin-bottom: 0">
                  <input
                    class="h-4 w-4"
                    type="checkbox"
                    :checked="sampleData?.weightedMud"
                    disabled
                  />
                </el-form-item>
              </div>
              <div class="h-auto w-full flex items-center justify-between">
                <span>Sample From</span>
                <span>{{
                  getOption(sampleData?.sampleFrom, sampleFromOptions)?.label ||
                  ""
                }}</span>
              </div>
              <div class="h-auto w-full flex items-center justify-between">
                <span>Time Sample Taken (hh:mm) </span>
                <span>{{ formatTime(sampleData?.timeSampleTaken) || "" }}</span>
              </div>
              <div class="h-auto w-full flex items-center justify-between">
                <span>Flowline Temperature (F)</span>
                <span>{{ sampleData?.flowlineTemperature || "" }}</span>
              </div>
              <div class="h-auto w-full flex items-center justify-between">
                <span>Measured Depth (ft) </span>
                <span>{{ sampleData?.measuredDepth || "" }}</span>
              </div>
              <div class="h-auto w-full flex items-center justify-between">
                <span>MW (ppg or lbs/gal)</span>
                <span>{{ sampleData?.weightedMud || "" }}</span>
              </div>
              <div class="h-auto w-full flex items-center justify-between">
                <span>Funnel Viscosity (sec/qt) </span>
                <span>{{ sampleData?.funnelViscosity || "" }}</span>
              </div>
              <div class="h-auto w-full flex items-center justify-between">
                <span>Temperature for Plastic Viscosity (f) </span>
                <span>{{
                  sampleData?.temperatureForPlasticViscosity || ""
                }}</span>
              </div>
              <div class="h-auto w-full flex items-center justify-between">
                <span>PV (Plastic Viscosity) (cP) </span>
                <span>{{ sampleData?.plasticViscosity || "" }}</span>
              </div>
              <div class="h-auto w-full flex items-center justify-between">
                <span>YP (Yield Point) (lbf/100ft2)</span>
                <span>{{ sampleData?.yieldPoint || "" }}</span>
              </div>
              <div class="h-auto w-full flex items-center justify-between">
                <span>Gel Str. 10s (lbf/100ft2) </span>
                <span>{{ sampleData?.gelStrength10s || "" }}</span>
              </div>
              <div class="h-auto w-full flex items-center justify-between">
                <span>Gel Str. 10m (lbf/100ft2) </span>
                <span>{{ sampleData?.gelStrength10m || "" }}</span>
              </div>
              <div class="h-auto w-full flex items-center justify-between">
                <span>Gel Str. 30m (lbf/100ft2) </span>
                <span>{{ sampleData?.gelStrength30m || "" }}</span>
              </div>
              <div class="h-auto w-full flex items-center justify-between">
                <span>API filtrate (ml/30min) </span>
                <span>{{ sampleData?.apiFiltrate || "" }}</span>
              </div>
              <div class="h-auto w-full flex items-center justify-between">
                <span>API cake thickness (1/32in)</span>
                <span>{{ sampleData?.apiCakeThickness || "" }}</span>
              </div>
            </div>
            <div class="flex flex-col gap-2">
              <div class="h-auto w-full flex items-center justify-between">
                <span>Temperature for HTHP (F) </span>
                <span>{{ sampleData?.temperatureForHTHP || "" }}</span>
              </div>
              <div class="h-auto w-full flex items-center justify-between">
                <span>HTHP filtrate (ml/30min)</span>
                <span>{{ sampleData?.hthpFiltrate || "" }}</span>
              </div>
              <div class="h-auto w-full flex items-center justify-between">
                <span>HTHP cake thickness (1/32in)</span>
                <span>{{ sampleData?.hthpCakeThickness || "" }}</span>
              </div>
              <div class="h-auto w-full flex items-center justify-between">
                <span>Solids (%)</span>
                <span>{{ sampleData?.solids || "" }}</span>
              </div>
              <div class="h-auto w-full flex items-center justify-between">
                <span>Oil (%)</span>
                <span>{{ sampleData?.oil || "" }}</span>
              </div>
              <div class="h-auto w-full flex items-center justify-between">
                <span>Water (%)</span>
                <span>{{ sampleData?.water || "" }}</span>
              </div>
              <div class="h-auto w-full flex items-center justify-between">
                <span>Sand Content (%)</span>
                <span>{{ sampleData?.sandContent || "" }}</span>
              </div>
              <div class="h-auto w-full flex items-center justify-between">
                <span>MBT capacity (lb/bbl)</span>
                <span>{{ sampleData?.mbtCapacity || "" }}</span>
              </div>
              <div class="h-auto w-full flex items-center justify-between">
                <span>pH </span>
                <span>{{ sampleData?.pH || "" }}</span>
              </div>
              <div class="h-auto w-full flex items-center justify-between">
                <span>Mud Alkalinity (Pm) (ml)</span>
                <span>{{ sampleData?.mudAlkalinity || "" }}</span>
              </div>
              <div class="h-auto w-full flex items-center justify-between">
                <span>Filtrate Alkalinity (Pf) (ml)</span>
                <span>{{ sampleData?.filtrateAlkalinity || "" }}</span>
              </div>
              <div class="h-auto w-full flex items-center justify-between">
                <span>Filtrate Alkalinity (Pf) (ml)</span>
                <span>{{ sampleData?.filtrateAlkalinity || "" }}</span>
              </div>
              <div class="h-auto w-full flex items-center justify-between">
                <span>Calcium (mg/L) </span>
                <span>{{ sampleData?.calcium || "" }}</span>
              </div>
              <div class="h-auto w-full flex items-center justify-between">
                <span>Chlorides (mg/L) </span>
                <span>{{ sampleData?.chlorides || "" }}</span>
              </div>
              <div class="h-auto w-full flex items-center justify-between">
                <span>Total Hardness (mg/L)</span>
                <span>{{ sampleData?.totalHardness || "" }}</span>
              </div>
              <div class="h-auto w-full flex items-center justify-between">
                <span>Excess Lime (lb/bbl)</span>
                <span>{{ sampleData?.excessLime || "" }}</span>
              </div>
            </div>
            <div class="flex flex-col gap-2">
              <div class="h-auto w-full flex items-center justify-between">
                <span>K+ (mg/L)</span>
                <span>{{ sampleData?.kPlus || "" }}</span>
              </div>
              <div class="h-auto w-full flex items-center justify-between">
                <span>Make up water: Chlorides (mg/L)</span>
                <span>{{ sampleData?.makeUpWater || "" }}</span>
              </div>
              <div class="h-auto w-full flex items-center justify-between">
                <span>Solids adjusted for salt (%)</span>
                <span>{{ sampleData?.solidsAdjustedForSalt || "" }}</span>
              </div>
              <div class="h-auto w-full flex items-center justify-between">
                <span>Fine LCM (lb/bbl)</span>
                <span>{{ sampleData?.fineLCM || "" }}</span>
              </div>
              <div class="h-auto w-full flex items-center justify-between">
                <span>Coarse LCM (lb/bbl)</span>
                <span>{{ sampleData?.coarseLCM || "" }}</span>
              </div>
              <div class="h-auto w-full flex items-center justify-between">
                <span
                  >Linear Gel Strength (LGS) (%)
                  <el-popover placement="top" :width="300" trigger="hover">
                    <template #reference>
                      <i class="fas fa-exclamation-circle ms-2 fs-7"></i>
                    </template>
                    <span>
                      LGS (%) = (Gel Strength (lbs/100 ft²) / Mud Weight
                      (lbs/gal)) x 100
                    </span>
                  </el-popover></span
                >
                <span>{{ sampleData?.linearGelStrengthPercent || "" }}</span>
              </div>
              <div class="h-auto w-full flex items-center justify-between">
                <span
                  >Linear Gel Strength (LGS) (lb/bbl)<el-popover
                    placement="top"
                    :width="300"
                    trigger="hover"
                  >
                    <template #reference>
                      <i class="fas fa-exclamation-circle ms-2 fs-7"></i>
                    </template>
                    <span>
                      LGS (lb/bbl) = (Gel Strength (lbs/100 ft²) / (Mud Weight
                      (lbs/gal) * 42)) x 100
                    </span>
                  </el-popover></span
                >
                <span>{{ sampleData?.linearGelStrengthLbBbl || "" }}</span>
              </div>
              <div class="h-auto w-full flex items-center justify-between">
                <span
                  >High Gel Strength (HGS) (%)<el-popover
                    placement="top"
                    :width="300"
                    trigger="hover"
                  >
                    <template #reference>
                      <i class="fas fa-exclamation-circle ms-2 fs-7"></i>
                    </template>
                    <span>
                      HGS (%) = (Gel Str. 30m (lbf/100ft²) / MW (lb/gal)) x 100
                    </span>
                  </el-popover></span
                >
                <span>{{ sampleData?.highGelStrengthPercent || "" }}</span>
              </div>
              <div class="h-auto w-full flex items-center justify-between">
                <span
                  >High Gel Strength (HGS) (lb/bbl)<el-popover
                    placement="top"
                    :width="300"
                    trigger="hover"
                  >
                    <template #reference>
                      <i class="fas fa-exclamation-circle ms-2 fs-7"></i>
                    </template>
                    <span>
                      HGS (lb/bbl) = Gel Str. 30m (lbf/100ft²) / 100
                    </span>
                  </el-popover>
                </span>
                <span>{{ sampleData?.highGelStrengthLbBbl || "" }}</span>
              </div>
              <div class="h-auto w-full flex items-center justify-between">
                <span
                  >Bentonite Concentration (%)<el-popover
                    placement="top"
                    :width="300"
                    trigger="hover"
                  >
                    <template #reference>
                      <i class="fas fa-exclamation-circle ms-2 fs-7"></i>
                    </template>
                    <span>
                      Bentonite (%) = (MBT capacity (lb/bbl) / MW (lb/gal)) x
                      100
                    </span>
                  </el-popover></span
                >
                <span>{{
                  sampleData?.bentoniteConcentrationPercent || ""
                }}</span>
              </div>
              <div class="h-auto w-full flex items-center justify-between">
                <span
                  >Bentonite Concentration (lb/bbl)<el-popover
                    placement="top"
                    :width="300"
                    trigger="hover"
                  >
                    <template #reference>
                      <i class="fas fa-exclamation-circle ms-2 fs-7"></i>
                    </template>
                    <span> Bentonite (lb/bbl) = MBT capacity (lb/bbl) </span>
                  </el-popover></span
                >
                <span>{{ sampleData?.bentoniteConcentrationLbBbl || "" }}</span>
              </div>
              <div class="h-auto w-full flex items-center justify-between">
                <span
                  >Drill Solids Concentration (%)<el-popover
                    placement="top"
                    :width="300"
                    trigger="hover"
                  >
                    <template #reference>
                      <i class="fas fa-exclamation-circle ms-2 fs-7"></i>
                    </template>
                    <span> Drill Solids (%) = Solids (%) - Bentonite (%) </span>
                  </el-popover></span
                >
                <span>{{
                  sampleData?.drillSolidsConcentrationPercent || ""
                }}</span>
              </div>
              <div class="h-auto w-full flex items-center justify-between">
                <span
                  >Drill Solids Concentration (lb/bbl)<el-popover
                    placement="top"
                    :width="300"
                    trigger="hover"
                  >
                    <template #reference>
                      <i class="fas fa-exclamation-circle ms-2 fs-7"></i>
                    </template>
                    <span>
                      Drill Solids (lb/bbl) = (Solids (%) - Bentonite (%)) x MW
                      (lb/gal) / 100
                    </span>
                  </el-popover>
                </span>
                <span>{{
                  sampleData?.drillSolidsConcentrationLbBbl || ""
                }}</span>
              </div>
              <div class="h-auto w-full flex items-center justify-between">
                <span
                  >DS/Bent Ratio (Drill Solids to Bentonite Ratio)<el-popover
                    placement="top"
                    :width="300"
                    trigger="hover"
                  >
                    <template #reference>
                      <i class="fas fa-exclamation-circle ms-2 fs-7"></i>
                    </template>
                    <span>
                      DS/Bent Ratio = (Drill Solids (lb/bbl) / Bentonite
                      (lb/bbl))
                    </span>
                  </el-popover>
                </span>
                <span>{{ sampleData?.drillSolidsToBentoniteRatio || "" }}</span>
              </div>
              <div class="h-auto w-full flex items-center justify-between">
                <span
                  >Average Specific Gravity of Solids<el-popover
                    placement="top"
                    :width="300"
                    trigger="hover"
                  >
                    <template #reference>
                      <i class="fas fa-exclamation-circle ms-2 fs-7"></i>
                    </template>
                    <span>
                      Average SG of Solids = Σ(Specific Gravity of Each Solid x
                      Volume Fraction of Each Solid)
                    </span>
                  </el-popover></span
                >
                <span>{{
                  sampleData?.averageSpecificGravityOfSolids || ""
                }}</span>
              </div>
            </div>
          </div>
          <div class="flex flex-col gap-2 md:flex-row md:gap-4">
            <div class="flex flex-col gap-2">
              <h3 class="font-bold h-auto w-full border-b-2 border-dashed pb-2">
                Rheological Properties
              </h3>

              <div class="h-auto w-full flex items-center justify-between">
                <span>Shear Rate: 600 (sec^-1) (rpm) </span>
                <span>{{ sampleData?.shearRate600 || "" }}</span>
              </div>
              <div class="h-auto w-full flex items-center justify-between">
                <span>Shear Rate: 300 (sec^-1) (rpm)</span>
                <span>{{ sampleData?.shearRate300 || "" }}</span>
              </div>
              <div class="h-auto w-full flex items-center justify-between">
                <span>Shear Rate: 200 (sec^-1) (rpm)</span>
                <span>{{ sampleData?.shearRate200 || "" }}</span>
              </div>
              <div class="h-auto w-full flex items-center justify-between">
                <span>Shear Rate: 100 (sec^-1) (rpm)</span>
                <span>{{ sampleData?.shearRate100 || "" }}</span>
              </div>
              <div class="h-auto w-full flex items-center justify-between">
                <span>Shear Rate: 6 (sec^-1) (rpm)</span>
                <span>{{ sampleData?.shearRate6 || "" }}</span>
              </div>
              <div class="h-auto w-full flex items-center justify-between">
                <span>Shear Rate: 3 (sec^-1) (rpm)</span>
                <span>{{ sampleData?.shearRate3 || "" }}</span>
              </div>
              <div class="h-auto w-full flex items-center justify-between">
                <span>Plastic Viscosity (PV) (cP)</span>
                <span>{{ sampleData?.plasticViscosity || "" }}</span>
              </div>
              <div class="h-auto w-full flex items-center justify-between">
                <span>Yield Point (YP) (lbf/100ft2)</span>
                <span>{{ sampleData?.yieldPoint || "" }}</span>
              </div>
              <div class="h-auto w-full flex items-center justify-between">
                <span
                  >Apparent Viscosity (AV) (cP)
                  <el-popover placement="top" :width="300" trigger="hover">
                    <template #reference>
                      <i class="fas fa-exclamation-circle ms-2 fs-7"></i>
                    </template>
                    <span>
                      Apparent Viscosity (AV) (cP) is a measure of the overall
                      resistance to flow of the drilling mud. It's the sum of
                      the plastic viscosity and half of the yield point.<br /><br />
                      AV (cP) = PV + YP / 2
                    </span>
                  </el-popover></span
                >
                <span>{{ sampleData?.apparentViscosity || "" }}</span>
              </div>
              <div class="h-auto w-full flex items-center justify-between">
                <span
                  >Shear Rate<el-popover
                    placement="top"
                    :width="300"
                    trigger="hover"
                  >
                    <template #reference>
                      <i class="fas fa-exclamation-circle ms-2 fs-7"></i>
                    </template>
                    <span>
                      Shear rate is a measure of how fast the mud is flowing.
                      <br /><br />
                      γ = (2 x Pump Speed (rpm)) / (Dial Reading (sec^-1))
                    </span>
                  </el-popover></span
                >
                <span>{{ sampleData?.shearRate || "" }}</span>
              </div>
              <div class="h-auto w-full flex items-center justify-between">
                <span
                  >Shear Stress τ (lbf/100ft²)<el-popover
                    placement="top"
                    :width="300"
                    trigger="hover"
                  >
                    <template #reference>
                      <i class="fas fa-exclamation-circle ms-2 fs-7"></i>
                    </template>
                    <span>
                      Shear stress is the force applied per unit area on the
                      mud.

                      <br /><br />
                      τ (lbf/100ft²) = PV + (YP x Shear Rate)
                    </span>
                  </el-popover></span
                >
                <span>{{ sampleData?.shearStress || "" }}</span>
              </div>
            </div>

            <apexchart
              type="area"
              :options="chartOptions"
              :series="series"
              class="h-auto w-3/4"
            ></apexchart>
          </div>
        </div>
      </div>

      <div class="h-auto w-full flex flex-row items-center gap-2">
        <button
          type="button"
          class="bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover rounded-md px-4 py-2 font-semibold md:px-4 md:py-3 md:text-xl"
          @click="closeModal"
        >
          Close
        </button>
        <button
          class="bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover rounded-md px-4 py-2 font-semibold md:px-4 md:py-3 md:text-xl"
          type="button"
          @click="clickEdit"
        >
          Edit
        </button>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import SvgIcon from "@/constants/SvgIcon.vue";
import { getOption } from "@/utils/option";
import { fluidTypeOptions, sampleFromOptions } from "@/constants/sample";
import { formatTime } from "@/utils/date";
import { useSampleStore } from "@/stores/sample";
import { defineComponent, ref, watch } from "vue";
import type { SampleInformation } from "@/types/sample";

export default defineComponent({
  name: "view-sample-modal",
  components: { SvgIcon },
  props: {
    isVisible: {
      type: Boolean,
      required: true,
    },
    close: {
      type: Function,
      required: true,
    },
  },
  emits: ["editModal"],
  setup(props, { emit }) {
    const sampleStore = useSampleStore();
    const id = ref("");
    const sampleData = ref<any>();
    const loading = ref(false);
    const sampleChartData = ref<any>({
      shearRate: [],
      shearStress: [],
    });

    const series = ref([
      {
        name: "Shear Stress",
        data: sampleChartData?.value?.shearStress,
      },
    ]);

    const chartOptions = ref<any>({
      chart: {
        type: "area",
        height: 350,
        toolbar: {
          show: false,
        },
      },
      xaxis: {
        categories: sampleChartData?.value?.shearRate,
        title: {
          text: "Shear Rate",
        },
        tooltip: {
          enabled: false,
        },
      },
      yaxis: {
        title: {
          text: "Shear Stress",
        },
      },
      dataLabels: {
        enabled: false,
      },
      stroke: {
        curve: "smooth",
      },
      tooltip: {
        x: {
          show: false,
        },
      },
    });

    watch(id, (newValue) => {
      if (newValue !== "") {
        getSampleDetails();
        getSampleChartInfo();
      }
    });

    const getSampleDetails = async (): Promise<void> => {
      loading.value = true;
      sampleStore.getSampleDetails({
        id: id.value,
        callback: {
          onSuccess: (res: any) => {
            sampleData.value = { ...res };
          },
          onFinish: () => {
            loading.value = false;
          },
        },
      });
    };

    const getSampleChartInfo = async (): Promise<void> => {
      sampleStore.getSampleChartInfo({
        id: id.value,
        callback: {
          onSuccess: (res: any) => {
            if (res?.length) {
              res.sort(
                (item1: SampleInformation, item2: SampleInformation) =>
                  (item1.shearRate ?? 0) - (item2.shearRate ?? 0)
              );
            }
            for (let i = 0; i < res?.length; i++) {
              if (res[i]?.shearRate >= 3) {
                sampleChartData?.value?.shearRate?.push(
                  Math.round(res[i]?.shearRate * 100) / 100
                );

                sampleChartData?.value?.shearStress?.push(
                  Math.round(res[i]?.shearStress * 100) / 100
                );
              }
            }
            setChartValue();
          },
        },
      });
    };

    const setChartValue = () => {
      series.value = [
        {
          name: "Shear Stress",
          data: sampleChartData?.value?.shearStress,
        },
      ];

      chartOptions.value = {
        ...chartOptions.value,
        xaxis: {
          categories: sampleChartData?.value?.shearRate,
          title: {
            text: "Shear Rate",
          },
        },
      };
    };

    const closeModal = () => {
      props.close();
    };

    const setId = (sampleId: string) => {
      id.value = sampleId.toString();
    };

    const reset = () => {
      id.value = "";
      sampleData.value = null;
      sampleChartData.value = {
        shearRate: [],
        shearStress: [],
      };
      setChartValue();
    };

    const clickEdit = () => {
      emit("editModal", id.value);
      closeModal();
    };

    return {
      id,
      series,
      sampleData,
      fluidTypeOptions,
      sampleFromOptions,
      chartOptions,
      closeModal,
      setId,
      reset,
      getOption,
      formatTime,
      clickEdit,
      loading,
    };
  },
});
</script>
