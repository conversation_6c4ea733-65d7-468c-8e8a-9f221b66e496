import{O as g,P as s,Q as a}from"./index-BmHWvWFS.js";import{h as i}from"./handleFailure-WBgBpurp.js";const f=g("costSetting",()=>({getCostSettings:async({params:c,callback:o})=>{var e;const n=s.get(o,"onSuccess",s.noop),r=s.get(o,"onFinish",s.noop);try{a.setHeader();const t=await a.getWithParams("costSettings",c);n(((e=t.data)==null?void 0:e.data)||t.data)}catch(t){i(t,o)}finally{r()}},getCostSettingDetails:async({id:c,callback:o})=>{var e;const n=s.get(o,"onSuccess",s.noop),r=s.get(o,"onFinish",s.noop);try{a.setHeader();const t=await a.get(`costSettings/${c}`);n(((e=t.data)==null?void 0:e.data)||t.data)}catch(t){i(t,o)}finally{r()}},updateCostSetting:async({id:c,params:o,callback:n})=>{var t;const r=s.get(n,"onSuccess",s.noop),e=s.get(n,"onFinish",s.noop);try{a.setHeader();const d=await a.put(`costSettings/${c}`,o);r(((t=d.data)==null?void 0:t.data)||d.data)}catch(d){i(d,n)}finally{e()}},deleteCostSetting:async({id:c,callback:o})=>{var e;const n=s.get(o,"onSuccess",s.noop),r=s.get(o,"onFinish",s.noop);try{a.setHeader();const t=await a.delete(`costSettings/${c}`);n(((e=t.data)==null?void 0:e.data)||t.data)}catch(t){i(t,o)}finally{r()}},createCostSetting:async({params:c,callback:o})=>{var e;const n=s.get(o,"onSuccess",s.noop),r=s.get(o,"onFinish",s.noop);try{a.setHeader();const t=await a.post("costSettings",c);n(((e=t.data)==null?void 0:e.data)||t.data)}catch(t){i(t,o)}finally{r()}}})),C=g("product",()=>({getProductDetails:async({id:c,callback:o})=>{var e;const n=s.get(o,"onSuccess",s.noop),r=s.get(o,"onFinish",s.noop);try{a.setHeader();const t=await a.get(`products/${c}`);n(((e=t.data)==null?void 0:e.data)||t.data)}catch(t){i(t,o)}finally{r()}},updateProduct:async({id:c,params:o,callback:n})=>{var t;const r=s.get(n,"onSuccess",s.noop),e=s.get(n,"onFinish",s.noop);try{a.setHeader();const d=await a.put(`products/${c}`,o);r(((t=d.data)==null?void 0:t.data)||d.data)}catch(d){i(d,n)}finally{e()}},deleteProduct:async({id:c,callback:o})=>{var e;const n=s.get(o,"onSuccess",s.noop),r=s.get(o,"onFinish",s.noop);try{a.setHeader();const t=await a.delete(`products/${c}`);n(((e=t.data)==null?void 0:e.data)||t.data)}catch(t){i(t,o)}finally{r()}},getProducts:async({params:c,callback:o})=>{var e;const n=s.get(o,"onSuccess",s.noop),r=s.get(o,"onFinish",s.noop);try{a.setHeader();const t=await a.getWithParams("products",c);n(((e=t.data)==null?void 0:e.data)||t.data)}catch(t){i(t,o)}finally{r()}},createProduct:async({params:c,callback:o})=>{var e;const n=s.get(o,"onSuccess",s.noop),r=s.get(o,"onFinish",s.noop);try{a.setHeader();const t=await a.post("products",c);n(((e=t.data)==null?void 0:e.data)||t.data)}catch(t){i(t,o)}finally{r()}}}));export{C as a,f as u};
