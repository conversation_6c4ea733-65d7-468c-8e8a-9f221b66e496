<template>
  <PageHeader title="User Management" :breadcrumbs="breadcrumbs" />

  <div
    class="bg-screen-background h-auto w-11/12 mx-auto my-4 rounded-xl flex flex-col items-center md:text-lg lg:w-4/5 lg:mx-auto lg:min-w-[1560px]"
  >
    <!--begin::Card header-->
    <div
      class="bg-card-background text-card-text rounded-lg h-auto w-full flex flex-col items-start my-4 px-8 py-4 gap-3"
    >
      <h1 class="font-bold text-lg">Users management</h1>
      <div class="h-auto w-full">
        <el-form @submit.prevent="searchUsers">
          <el-form-item class="mb-0">
            <el-input
              placeholder="Search"
              v-model="search"
              name="search"
              size="large"
              ><template #prefix>
                <el-icon class="el-input__icon">
                  <SvgIcon icon="searchIcon" />
                </el-icon> </template
            ></el-input>
          </el-form-item>
        </el-form>
      </div>
      <div
        class="h-auto w-full overflow-x-scroll flex flex-row items-center gap-2 justify-between md:overflow-hidden md:justify-start md:gap-4"
      >
        <button
          class="flex flex-row justify-between rounded-md px-4 py-2 font-semibold text-button-text-light hover:text-button-text-light-hover md:px-4 md:py-3 md:text-xl"
          :class="
            isShowFilter
              ? 'bg-button-primary-active'
              : 'bg-button-primary hover:bg-button-primary-hover'
          "
          @click="() => toggleFilter(!isShowFilter)"
        >
          <SvgIcon icon="filterIcon" />
          Filter
        </button>
        <button
          class="bg-danger text-white rounded-md px-3 py-2 font-semibold md:px-4 md:py-3 md:text-xl"
          v-if="checkedRows.length !== 0 && isAdmin()"
          @click="onRemove"
        >
          Remove
        </button>
        <button
          v-if="isAdmin()"
          class="bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover rounded-md px-4 py-2 font-semibold flex flex-row items-center gap-2 justify-between md:px-4 md:py-3 md:text-xl"
          @click="toggleNewUser"
        >
          <SvgIcon icon="addIcon" />
          New
        </button>
        <button
          class="bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover rounded-md px-4 py-2 font-semibold flex flex-row items-center gap-2 justify-betweend md:px-4 md:py-3 md:text-xl"
        >
          <SvgIcon icon="exportIcon" classname="rotate-90" />
          Export
        </button>
      </div>

      <Filter
        v-show="isShowFilter"
        :hideFilter="() => toggleFilter(false)"
        :onFilter="onFilter"
      />
      <!--end::Card header-->
      <div v-if="loading" class="text-center p-5">
        <div class="spinner-border text-primary" role="status">
          <span class="sr-only">Loading...</span>
        </div>
      </div>
      <el-empty v-else-if="listUser.length === 0" description="No Data" />
      <div v-else class="h-auto w-full overflow-x-scroll p-4">
        <!--begin::Table-->
        <table class="lg:mx-auto">
          <!--begin::Table head-->
          <thead class="font-bold">
            <tr class="font-bold">
              <TableHeader
                :headers="tableHeader"
                :sortBy="sortParams.sortBy!"
                :sortDirection="sortParams.sortDirection!"
                :onSort="onSort"
                ><template v-slot:customHeader="{ header }">
                  <div v-if="header.label === ''">
                    <div
                      class="form-check form-check-sm form-check-custom form-check-solid"
                    >
                      <input
                        class="h-4 w-4"
                        type="checkbox"
                        v-model="checkAll"
                        @change="onToggleCheckAll"
                      />
                    </div>
                  </div>
                  <div v-else class="p-4">
                    {{ header.label }}
                  </div>
                </template></TableHeader
              >
            </tr>
          </thead>
          <!--end::Table head-->

          <!--begin::Table body-->
          <tbody>
            <template v-for="item in listUser" :key="item.id">
              <tr>
                <td v-if="isAdmin()">
                  <div
                    class="form-check form-check-sm form-check-custom form-check-solid"
                  >
                    <input
                      class="h-4 w-4"
                      type="checkbox"
                      :value="item.id"
                      v-model="checkedRows"
                    />
                  </div>
                </td>

                <td class="p-4 truncate">
                  <div class="flex flex-row items-center gap-3">
                    <img
                      class="h-11 w-11 rounded-full"
                      :src="item?.avatar || '/media/avatars/blank.png'"
                      alt=""
                    />
                    <div class="flex flex-col font-semibold">
                      <router-link
                        :to="`/users/${item?.id}`"
                        class="font-bold text-link hover:text-link-hover"
                        >{{
                          `${item?.firstName} ${item?.lastName}`
                        }}</router-link
                      >{{ item?.email }}
                    </div>
                  </div>
                </td>

                <td class="p-4 font-semibold text-center whitespace-nowrap">
                  {{ item?.address }}
                </td>
                <td class="p-4 font-semibold text-center whitespace-nowrap">
                  {{ item?.officePhone }}
                </td>
                <td class="p-4 font-semibold text-center whitespace-nowrap">
                  {{ item?.mobilePhone }}
                </td>

                <td class="p-4">
                  <span class="px-2 py-1 rounded-sm text-xs md:text-lg"
                    :class="
                      item?.status === UserStatus.Active
                        ? 'bg-green-200 text-green-500'
                        : 'bg-red-200 text-red-500'
                    "
                    >{{
                      item?.status === UserStatus.Active ? "Active" : "Deactive"
                    }}</span
                  >
                </td>

                <td v-if="isSystemAdmin()" class="p-4">
                  <span class="font-semibold text-nowrap">{{
                    item?.company?.name
                  }}</span>
                </td>
                <td class="p-4 font-semibold text-center whitespace-nowrap">
                  {{ item?.roles?.join(", ") }}
                </td>

                <td v-if="isAdmin()">
                  <div
                    class="h-auto w-full flex flex-row items-center justify-evenly gap-2"
                  >
                    <button
                      class="bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover font-semibold h-8 w-8 rounded-lg pl-[7.5px] pt-[1px] md:h-10 md:w-10 md:pl-[6.52px] md:pt-[2px]"
                      @click="() => view(item?.id ?? '')"
                    >
                      <SvgIcon icon="eyeIcon" classname="md:h-7 md:w-7" />
                    </button>
                    <button
                      class="bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover font-semibold h-8 w-8 rounded-lg pl-[7px] pb-[3px] md:h-10 md:w-10 md:pl-[6.5px] md:pb-[4.8px]"
                      @click="deleteUser(item.id!)"
                    >
                      <span class="text-danger">
                        <SvgIcon icon="trashIcon" classname="md:h-7 md:w-7" />
                      </span>
                    </button>
                  </div>
                </td>
              </tr>
            </template>
          </tbody>
          <!--end::Table body-->
        </table>
        <!--end::Table-->
      </div>
      <div class="h-auto w-full flex flex-col items-center my-5">
        <div v-if="listUser?.length" class="font-semibold">
          {{
            `Showing ${(currentPage - 1) * 10 + 1} to ${
              listUser?.length
            } of ${totalElements} entries`
          }}
        </div>
        <TablePagination
          v-if="pageCount >= 1"
          :total-pages="pageCount"
          :total="totalElements"
          :per-page="10"
          :current-page="currentPage"
          @page-change="pageChange"
        />
      </div>
    </div>
    <UserModal
      :isVisible="isModalVisible"
      :close="toggleNewUser"
      ref="userModal"
      :loadPage="reloadUserList"
    />
  </div>
</template>

<script lang="ts">
import PageHeader from "@/components/PageHeader.vue";
import TablePagination from "@/components/common/TablePagination.vue";
import SvgIcon from "@/constants/SvgIcon.vue";
import TableHeader from "@/components/common/TableHeader.vue";
import { getOption } from "@/utils/option";
import { SortByEnum, SortDirectionEnum } from "@/constants/table";
import { userRoleOptions, UserStatus } from "@/constants/user";
import AlertService from "@/services/AlertService";
import { isAdmin, isSystemAdmin } from "@/services/JwtService";
import { useUserStore } from "@/stores/user";
import { defineComponent, onMounted, ref, type Ref, watch } from "vue";
import { useRouter } from "vue-router";
import Filter from "./Filter.vue";
import UserModal from "./UserModal.vue";

export default defineComponent({
  name: "user-overview",
  components: {
    PageHeader,
    SvgIcon,
    Filter,
    TablePagination,
    UserModal,
    TableHeader,
  },
  setup() {
    const userStore = useUserStore();
    const router = useRouter();
    const breadcrumbs = ["User Management", "Overview"];
    const checkedRows = ref<Array<string>>([]);
    const checkAll = ref<boolean>(false);
    const pageCount = ref(0);
    const totalElements = ref(0);
    const currentPage = ref(1);
    const loading = ref(false);
    const search = ref<string>("");
    const isShowFilter = ref(false);
    const userModal: Ref<any> = ref<typeof UserModal | null>(null);
    const listUser = ref<User.Info[]>([]);
    const filterParams = ref<Filter.FilterForm>({});
    const isModalVisible = ref(false);
    const sortParams = ref<Company.GetFilter>({
      sortDirection: SortDirectionEnum.ASC,
      sortBy: SortByEnum.Name,
    });

    const tableHeader: Table.ColumnHeader[] = [
      { label: "", class: "w-25px", display: isAdmin() },
      { label: "FULL NAME", sortBy: SortByEnum.Name, class: "min-w-150px" },
      { label: "ADDRESS", class: "min-w-150px" },
      { label: "OFFICE NUMBER", class: "min-w-120px" },
      { label: "MOBILE NUMBER", class: "min-w-120px" },
      { label: "STATUS", class: "min-w-150px" },
      { label: "COMPANY", class: "min-w-150px", display: isSystemAdmin() },
      { label: "ROLES", class: "min-w-120px" },
      { label: "ACTIONS", class: "min-w-60px", display: isAdmin() },
    ];

    onMounted(() => {
      getUserList();
    });

    watch(currentPage, () => {
      getUserList();
    });

    const getUserList = async (): Promise<void> => {
      loading.value = true;
      userStore.getUsers({
        params: {
          page: currentPage.value,
          limit: 10,
          name: search.value.trim() || null,
          ...sortParams.value,
          ...filterParams.value,
        },
        callback: {
          onSuccess: (res: any) => {
            const users = res?.items?.map((item: User.Info) => {
              return {
                ...item,
                roles: item?.roles?.map(
                  (role) => getOption(role?.value, userRoleOptions)?.label || ""
                ),
              };
            });

            listUser.value = [...users];
            pageCount.value = res?.totalPage;
            totalElements.value = res?.total;
            currentPage.value = res?.page;
          },
          onFinish: () => {
            loading.value = false;
          },
        },
      });
    };

    const onSort = (newSortValue: Filter.FilterForm) => {
      sortParams.value = { ...newSortValue };

      getUserList();
    };

    const searchUsers = () => {
      if (currentPage.value !== 1) {
        currentPage.value = 1;
      } else {
        getUserList();
      }
    };

    const pageChange = (newPage: number) => {
      currentPage.value = newPage;
    };

    const toggleNewUser = (): void => {
      isModalVisible.value = !isModalVisible.value;
    };

    watch(checkedRows, (newValue) => {
      checkAll.value =
        listUser.value.length !== 0 &&
        newValue.length === listUser.value.length;
    });

    const onToggleCheckAll = (e: any) => {
      if (e?.target?.checked) {
        checkedRows.value = listUser.value.map((user) => user.id!);
      } else {
        checkedRows.value = [];
      }
    };

    const view = (id: string) => {
      router.push({ path: `/users/${id}` });
    };

    const deleteUser = (id: string) => {
      AlertService.deletionAlert({
        onConfirmed: () => {
          removeUsers([id]);
        },
      });
    };

    const onRemove = () => {
      AlertService.deletionAlert({
        onConfirmed: () => {
          removeUsers(checkedRows.value, true);
        },
      });
    };

    const removeUsers = async (
      engineerIds: string[],
      clearRemoveList = false
    ): Promise<void> => {
      loading.value = true;
      userStore.removeUsers({
        ids: engineerIds,
        callback: {
          onSuccess: (_res: any) => {
            if (clearRemoveList) {
              checkedRows.value = [];
            }
            getUserList();
          },
          onFinish: () => {
            loading.value = false;
          },
        },
      });
    };

    const toggleFilter = (value: boolean) => {
      isShowFilter.value = value;
    };

    const onFilter = (value: Filter.FilterForm) => {
      filterParams.value = { ...value };

      if (currentPage.value !== 1) {
        currentPage.value = 1;
      } else {
        getUserList();
      }
    };

    const reloadUserList = () => {
      getUserList();
    };

    return {
      sortParams,
      tableHeader,
      search,
      loading,
      UserStatus,
      breadcrumbs,
      checkedRows,
      checkAll,
      listUser,
      currentPage,
      totalElements,
      pageCount,
      isShowFilter,
      userModal,
      isModalVisible,
      isSystemAdmin,
      toggleNewUser,
      pageChange,
      deleteUser,
      view,
      isAdmin,
      onToggleCheckAll,
      onRemove,
      onFilter,
      toggleFilter,
      searchUsers,
      getUserList,
      reloadUserList,
      onSort,
    };
  },
});
</script>
