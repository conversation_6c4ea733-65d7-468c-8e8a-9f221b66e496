<template>
  <div
    v-if="isVisible"
    class="fixed top-0 right-0 bottom-0 left-0 z-40 flex items-center"
  >
    <!-- Dark overlay background -->
    <div class="absolute inset-0 bg-sidebar-background opacity-25"></div>
    <div
      class="relative bg-card-background text-card-text max-h-11/12 w-11/12 flex flex-col items-center gap-3 mx-auto p-5 rounded-xl md:w-4/5 md:text-lg lg:w-2/5 lg:h-auto"
    >
      <div class="h-auto w-full flex flex-row items-center justify-between">
        <h3 class="text-lg font-bold md:text-xl">
          {{ `${id ? "Edit Interval" : "New Interval"}` }}
        </h3>
        <span class="cursor-pointer" @click="closeModal">
          <SvgIcon icon="closeModalIcon" />
        </span>
      </div>

      <el-form
        id="interval_form"
        @submit.prevent="submit"
        :model="targetData"
        :rules="rules"
        ref="formRef"
        class="h-auto w-full"
      >
        <div>
          <el-form-item prop="interval">
            <label class="font-semibold pr-6 md:text-lg"
              >Interval number<span class="text-danger-active font-light"
                >*</span
              ></label
            >
            <el-input-number
              type="number"
              :controls="false"
              :min="0"
              :step="1"
              v-model="targetData.interval"
              placeholder=""
              name="interval"
            ></el-input-number>
          </el-form-item>
        </div>
        <div class="flex flex-col gap-2">
          <label class="font-semibold"
            >Notes<span class="text-danger-active font-light">*</span>
          </label>
          <el-form-item prop="notes">
            <el-input
              v-model="targetData.notes"
              type="textarea"
              rows="10"
              name="notes"
              placeholder="Type Notes"
            />
          </el-form-item>
        </div>
        <div class="flex flex-row items-start mt-4 gap-3">
          <button
            type="button"
            class="bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover rounded-md px-4 py-2 font-semibold md:px-4 md:py-3 md:text-xl"
            @click="closeModal"
            :disabled="loading"
          >
            Discard
          </button>
          <button
            :data-kt-indicator="loading ? 'on' : null"
            class="bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover rounded-md px-4 py-2 font-semibold md:px-4 md:py-3 md:text-xl"
            type="submit"
            :disabled="loading"
          >
            <span v-if="!loading" class="indicator-label"> Save </span>
            <span v-if="loading" class="indicator-progress">
              Please wait...
              <span
                class="spinner-border spinner-border-sm align-middle ms-2"
              ></span>
            </span>
          </button>
        </div>
      </el-form>
    </div>
  </div>
</template>

<script lang="ts">
import SvgIcon from "@/constants/SvgIcon.vue";
import { useIntervalStore } from "@/stores/interval";
import { defineComponent, ref, watch } from "vue";

export default defineComponent({
  name: "interval-modal",
  components: { SvgIcon },
  props: {
    isVisible: {
      type: Boolean,
      required: true,
    },
    close: {
      type: Function,
      required: true,
    },
    loadPage: {
      type: Function,
      default: () => {},
      required: true,
    },
    wellId: {
      type: String,
      required: true,
    },
  },
  setup(props) {
    const intervalStore = useIntervalStore();
    const modal = ref(false);
    const id = ref("");
    const targetData = ref<Interval.Info>({
      interval: null,
      notes: "",
    });
    const formRef = ref<null | HTMLFormElement>(null);
    const loading = ref<boolean>(false);

    watch(id, (newValue) => {
      if (newValue !== "") {
        getIntervalDetails();
      }
    });

    watch(modal, (newValue) => {
      if (newValue === false) {
        id.value = "";
        reset();
        formRef?.value?.resetFields();
      }
    });

    const getIntervalDetails = async (): Promise<void> => {
      intervalStore.getIntervalDetails({
        id: id.value,
        callback: {
          onSuccess: (res: any) => {
            targetData.value = res;
          },
        },
      });
    };

    const updateInterval = async (param: Interval.Info): Promise<void> => {
      loading.value = true;
      intervalStore.updateInterval({
        id: id.value,
        params: param,
        callback: {
          onSuccess: (_res: any) => {
            if (props?.loadPage) {
              props?.loadPage();
            }
            props.close();
          },
          onFinish: (_err: any) => {
            loading.value = false;
          },
        },
      });
    };

    const createInterval = async (param: Interval.Info): Promise<void> => {
      loading.value = true;
      intervalStore.createInterval({
        params: param,
        callback: {
          onSuccess: (_res: any) => {
            if (props?.loadPage) {
              props?.loadPage();
            }
            props.close();
          },
          onFinish: (_err: any) => {
            loading.value = false;
          },
        },
      });
    };

    const closeModal = () => {
      props.close();
    };

    const setId = (wellId: string) => {
      id.value = wellId.toString();
    };

    const rules = ref({
      interval: [
        {
          required: true,
          message: "Please input interval number",
          trigger: "blur",
        },
      ],
      notes: [
        {
          required: true,
          message: "Please input notes",
          trigger: "blur",
        },
      ],
    });

    const submit = () => {
      if (!formRef.value) {
        return;
      }

      formRef.value.validate((valid: boolean) => {
        if (valid) {
          loading.value = true;

          if (id?.value) {
            updateInterval(targetData.value);
          } else {
            createInterval({ ...targetData.value, wellId: props?.wellId });
          }
        }
      });
    };

    const reset = () => {
      targetData.value = {
        interval: null,
        notes: "",
      };
    };

    return {
      id,
      modal,
      rules,
      loading,
      targetData,
      formRef,
      submit,
      setId,
      reset,
      closeModal,
    };
  },
});
</script>

<style>
#interval-modal {
  font-family: Poppins;

  .el-textarea__inner {
    font-family: "Fira Mono";
    color: black !important;
  }
}
</style>
