<template>
  <div
    className="h-auto w-auto fixed bottom-4 right-5 cursor-pointer"
  >
    <div v-if="$slots.header">
      <slot name="header" />
    </div>
    <div v-else class="flex flex-row gap-4 items-center">
      <div class="relative group inline-block pt-1">
        <button
          class="bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover hover:border-[2px] hover:border-button-hover-outline rounded-full p-4"
          @click="addNewModal"
        >
          <SvgIcon icon="addIcon" />
        </button>
        <div
          class="h-auto w-auto absolute left-1/2 transform -translate-x-1/2 bottom-full mb-2 py-1 px-2 hidden group-hover:block bg-light-border text-popup-text rounded whitespace-nowrap"
        >
          Add New
          <div
            class="absolute left-1/2 transform -translate-x-1/2 top-full w-0 h-0 border-l-4 border-r-4 border-t-4 border-t-light-border border-l-transparent border-r-transparent"
          ></div>
        </div>
      </div>

      <div v-if="showHelpInfo" class="relative group inline-block">
        <button
          class="bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover hover:border-[2px] hover:border-button-hover-outline rounded-full p-4"
          @click="showHelp"
        >
          <SvgIcon icon="helpIcon" />
        </button>
        <div
          class="h-auto w-auto absolute left-1/2 transform -translate-x-1/2 bottom-full mb-2 py-1 px-2 hidden group-hover:block bg-light-border text-popup-text rounded whitespace-nowrap"
        >
          More Info
          <div
            class="absolute left-1/2 transform -translate-x-1/2 top-full w-0 h-0 border-l-4 border-r-4 border-t-4 border-t-light-border border-l-transparent border-r-transparent"
          ></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent } from "vue";
import SvgIcon from "../../constants/SvgIcon.vue";

export default defineComponent({
  name: "bottom-tool",
  components: {
    SvgIcon,
  },
  props: {
    addNew: {
      type: Function,
      required: false,
    },
    showHelpWindow: {
      type: Function,
      required: false,
    },
    showAddNew: {
      type: Boolean,
      required: false,
      default: true,
    },
    showHelpInfo: {
      type: Boolean,
      required: false,
      default: true,
    },
  },
  setup(props) {
    const addNewModal = () => {
      if (props.addNew) {
        props.addNew();
      }
    };
    const showHelp = () => {
      if (props.showHelpWindow) {
        props.showHelpWindow();
      }
    };
    return { addNewModal, showHelp };
  },
});
</script>
