<template>
  <DailyReportProvide>
    <DailyReportContent />
  </DailyReportProvide>
</template>

<script lang="ts">
import DailyReportProvide from "@/provide/DailyReportProvide.vue";
import { defineComponent } from "vue";
import DailyReportContent from "./DailyReportContent.vue";

export default defineComponent({
  name: "provide-component",
  components: {
    DailyReportProvide,
    DailyReportContent,
  },
  setup() {
    return {};
  },
});
</script>
