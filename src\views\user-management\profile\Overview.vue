<template>
  <!--begin::details View-->
  <div
    class="bg-card-background text-card-text h-auto w-full mx-auto py-4 px-4 mb-4 rounded-b-lg lg:w-4/5 lg:mx-auto lg:min-w-[1560px]"
  >
    <!--begin::Card header-->
    <div class="h-auto w-full flex flex-row items-center justify-between">
      <h2 class="font-bold">User information</h2>
      <!--end::Card title-->

      <!--begin::Action-->
      <div v-if="isAdmin() || isOwner()">
        <button
          class="bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover rounded-md px-4 py-2 font-semibold"
          v-if="!isEditMode"
          @click="editUserInfo"
        >
          Edit
        </button>

        <div
          v-else
          class="h-auto w-full gap-2 flex flex-row items-end justify-between"
        >
          <button
            class="bg-danger text-white rounded-md px-3 py-2 font-semibold"
            @click="cancelEditUserInfo"
            :disabled="submittingUserInfo"
          >
            Cancel
          </button>
          <button
            class="bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover rounded-md px-4 py-2 font-semibold"
            type="button"
            @click="submit"
          >
            <span v-if="!submittingUserInfo" class="indicator-label">
              Save
            </span>
            <span v-if="submittingUserInfo" class="indicator-progress">
              Please wait...
              <span
                class="spinner-border spinner-border-sm align-middle ms-2"
              ></span>
            </span>
          </button>
        </div>
      </div>
      <!--end::Action-->
    </div>
    <!--begin::Card header-->

    <!--begin::Card body-->
    <el-form
      id="user_form"
      @submit.prevent="submit"
      :model="formData"
      :rules="userInfoRules"
      ref="userInfoFormRef"
      :disabled="!isEditMode || submittingUserInfo"
      class="h-auto w-full flex flex-col gap-2 text-sm mt-4"
    >
      <div class="md:h-auto md:w-full md:flex md:gap-4">
        <div class="md:h-auto md:w-full md:flex md:flex-col">
          <div class="flex flex-col gap-2 font-semibold">
            <label
              >First Name
              <span class="text-danger-active font-light">*</span></label
            >
            <el-form-item prop="firstName">
              <el-input
                placeholder="First Name"
                name="firstName"
                v-model="formData.firstName"
              ></el-input>
            </el-form-item>
          </div>
          <div class="flex flex-col gap-2 font-semibold">
            <label
              >Last name
              <span class="text-danger-active font-light">*</span></label
            >
            <el-form-item prop="lastName">
              <el-input
                placeholder="Last Name"
                name="lastName"
                v-model="formData.lastName"
              ></el-input>
            </el-form-item>
          </div>
          <div class="flex flex-col gap-2 font-semibold">
            <label
              >Email Address
              <span class="text-danger-active font-light">*</span></label
            >
            <el-form-item prop="email">
              <el-input
                placeholder="Email"
                name="email"
                v-model="formData.email"
              ></el-input>
            </el-form-item>
          </div>
        </div>
        <div class="md:h-auto md:w-full md:flex md:flex-col">
          <div class="flex flex-col gap-2 font-semibold">
            Office Phone
            <el-form-item prop="officePhone">
              <el-input
                placeholder="Office Phone"
                name="officePhone"
                v-model="formData.officePhone"
              ></el-input>
            </el-form-item>
          </div>
          <div class="flex flex-col gap-2 font-semibold">
            <label
              >Mobile Phone
              <span class="text-danger-active font-light">*</span></label
            >
            <el-form-item prop="mobilePhone">
              <el-input
                placeholder="Mobile Phone"
                name="mobilePhone"
                v-model="formData.mobilePhone"
              ></el-input>
            </el-form-item>
          </div>
          <div
            v-if="!isUserDetailSystemAdmin"
            class="flex flex-col gap-2 font-semibold"
          >
            <label
              >Company <span class="text-danger-active font-light">*</span></label
            >
            <el-form-item prop="companyId" class="mt-auto">
              <el-select
                v-model="formData.companyId"
                placeholder="Select company"
                class="w-100"
                clearable
                :disabled="!isSystemAdmin()"
              >
                <el-option
                  v-for="item in companyList"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                  name="companyId"
                /> </el-select
            ></el-form-item>
          </div>
        </div>
      </div>

      <div class="flex flex-col gap-2 font-semibold">
        Address
        <el-form-item prop="address">
          <el-input
            placeholder="Address"
            name="address"
            v-model="formData.address"
          ></el-input>
        </el-form-item>
      </div>
      <div class="flex flex-col gap-2 font-semibold">
        Note
        <el-form-item prop="note" class="mt-auto">
          <el-input
            placeholder=""
            type="textarea"
            rows="2"
            name="note"
            v-model="formData.note"
          ></el-input>
        </el-form-item>
      </div>
    </el-form>

    <!--end::Card body-->

    <div
      class="h-auto w-full border-t-[1px] border-dashed border-grey-300 pt-3"
    >
      <!--begin::Header-->
      <h2 class="font-bold">Security</h2>
      <!--end::Header-->

      <!--begin::Form-->
      <div class="py-4">
        <el-form
          v-if="!hideRole"
          id="role_form"
          @submit.prevent="submitRole"
          :model="roleData"
          ref="roleFormRef"
          class="flex flex-col gap-3"
          :disabled="!isEditRole"
          :rules="roleRules"
        >
          <!--begin::Label-->
          <div class="h-auto w-full flex flex-row items-center justify-between">
            <label class="font-bold">Roles</label>
            <div v-if="isAdmin()">
              <button
                type="button"
                class="bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover rounded-md px-4 py-2 font-semibold"
                v-if="!isEditRole"
                @click="editRole"
              >
                Edit
              </button>

              <div v-else class="h-auto w-full flex flex-row items-start gap-2">
                <button
                  type="button"
                  class="bg-danger text-white rounded-md px-3 py-2 font-semibold"
                  @click="cancelEditRole"
                  :disabled="submittingRole"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  class="bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover rounded-md px-4 py-2 font-semibold"
                >
                  <span v-if="!submittingRole" class="indicator-label">
                    Save
                  </span>
                  <span v-if="submittingRole" class="indicator-progress">
                    Please wait...
                    <span
                      class="spinner-border spinner-border-sm align-middle ms-2"
                    ></span>
                  </span>
                </button>
              </div>
            </div>
          </div>
          <!--end::Label-->

          <!--begin::Options-->
          <el-form-item prop="userRoles">
            <el-checkbox-group
              v-model="roleData.userRoles"
              class="h-auto w-full flex flex-col items-start gap-2"
            >
              <el-checkbox :label="UserType.CompanyAdmin" name="role">
                Company Admin
              </el-checkbox>
              <el-checkbox :label="UserType.Supervisor" name="role">
                Supervisor
              </el-checkbox>
              <el-checkbox :label="UserType.Engineer" name="role">
                Engineer
              </el-checkbox>
            </el-checkbox-group>
          </el-form-item>
          <!--end::Options-->
        </el-form>
        <!--end::Input group-->

        <!--begin::Input group-->
        <div v-if="isShowPassword()">
          <!--begin::Label-->
          <!--end::Label-->
          <label class="font-bold">Password</label>
          <div
            class="h-auto w-full mt-4 flex flex-row flex-wrap items-start gap-3 text-sm"
          >
            <button
              v-if="isAdmin()"
              type="button"
              class="bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover rounded-md px-4 py-2 font-semibold flex flex-row items-center gap-2 justify-between"
              @click="resetPass"
            >
              <SvgIcon :icon="'gearIcon'" />
              <span v-if="!loading"> Reset password </span>
              <span v-if="loading" class="indicator-progress">
                Please wait...
                <span
                  class="spinner-border spinner-border-sm align-middle ms-2"
                ></span>
              </span>
            </button>

            <!-- for user profile -->

            <button
              type="button"
              class="bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover rounded-md px-4 py-2 font-semibold flex flex-row items-center gap-2 justify-between"
              v-if="isOwner()"
              @click="toggleChangePassword"
            >
              <SvgIcon :icon="'gearIcon'" />
              Change Password
            </button>
          </div>
        </div>
        <!--end::Input group-->
      </div>
      <!--end::Form-->
    </div>
  </div>
  <ChangePasswordModal
    :isVisible="isModalVisible"
    :close="toggleChangePassword"
    ref="changePasswordModal"
  />
  <!--end::details View-->
</template>

<script lang="ts">
import { UserType } from "@/constants/user";
import AlertService from "@/services/AlertService";
import { yupValidate } from "@/utils/validator";
import JwtService, { isAdmin, isSystemAdmin } from "@/services/JwtService";
import { useCompanyStore } from "@/stores/company";
import { useUserStore } from "@/stores/user";
import type { FormRules } from "element-plus";
import { defineComponent, onMounted, ref, type PropType, type Ref } from "vue";
import { useRoute } from "vue-router";
import * as yup from "yup";
import ChangePasswordModal from "./ChangePasswordModal.vue";
import type { Option } from "@/types/common";
import SvgIcon from "@/constants/SvgIcon.vue";

export default defineComponent({
  name: "account-overview",
  components: { ChangePasswordModal, SvgIcon },
  props: {
    userDetail: {
      type: Object as PropType<User.Info>,
      required: true,
    },
    hideRole: {
      type: Boolean,
      required: false,
      default: false,
    },
    reloadUserData: {
      type: Function,
      required: true,
    },
  },
  setup(props) {
    const companyStore = useCompanyStore();
    const userStore = useUserStore();
    const route = useRoute();
    const isEditMode = ref<boolean>(false);
    const isEditRole = ref<boolean>(false);
    const submittingUserInfo = ref<boolean>(false);
    const submittingRole = ref<boolean>(false);
    const loading = ref<boolean>(false);
    const userInfoFormRef = ref<null | HTMLFormElement>(null);
    const roleFormRef = ref<null | HTMLFormElement>(null);
    const isModalVisible = ref(false);
    const formData = ref({
      ...props.userDetail,
      companyId: props?.userDetail?.company?.id,
    });
    const companyList = ref<Option[]>([]);
    const isUserDetailSystemAdmin = Boolean(
      // current user detail is system admin
      props?.userDetail?.roles?.find(
        (item) => item.value === UserType.SystemAdmin
      )
    );

    onMounted(() => {
      getCompanyOptions();
    });

    const getCompanyOptions = () => {
      if (isSystemAdmin()) {
        getCompanyList();
      } else {
        getMyCompany();
      }
    };

    const getCompanyList = async (): Promise<void> => {
      companyStore.getCompanies({
        params: {
          page: 1,
          limit: 500,
        },
        callback: {
          onSuccess: (res: any) => {
            companyList.value = res?.items?.map((item: Company.Info) => {
              return {
                value: item?.id,
                label: item?.name,
              };
            });
          },
        },
      });
    };

    const getMyCompany = async (): Promise<void> => {
      const id = JwtService?.getUserInfo()?.companyId;
      if (!id) return;
      companyStore.getCompanyById({
        id,
        callback: {
          onSuccess: (res: any) => {
            companyList.value = [
              {
                value: res?.id,
                label: res?.name,
              },
            ];
          },
        },
      });
    };

    const getUserRoles = () => {
      const roles: number[] = [];
      if (props?.userDetail?.roles) {
        for (const item of props.userDetail.roles) {
          if (item.value !== UserType.SystemAdmin) {
            roles.push(item.value);
          }
        }
      }
      return roles;
    };

    const roleData = ref({
      userRoles: getUserRoles(),
    });

    const changePasswordModal: Ref<any> = ref<
      typeof ChangePasswordModal | null
    >(null);

    const yupRule = yup.object().shape({
      email: yupValidate.emailAddress,
      mobilePhone: yupValidate.mobilePhone,
      officePhone: yupValidate.officePhone,
    });

    const initialRules = {
      firstName: [
        {
          required: true,
          message: "Please type First Name",
          trigger: ["blur", "change"],
        },
      ],
      lastName: [
        {
          required: true,
          message: "Please type Last Name",
          trigger: ["blur", "change"],
        },
      ],
      email: [
        {
          required: true,
          validator: (_: any, value: any, callback: any) => {
            (yupRule.fields.email as yup.AnySchema)
              .validate(value)
              .then(() => {
                callback();
              })
              .catch((error) => {
                callback(new Error(error.errors[0]));
              });
          },
          trigger: ["blur", "change"],
        },
      ],
      mobilePhone: [
        {
          required: true,
          validator: (_: any, value: any, callback: any) => {
            (yupRule.fields.mobilePhone as yup.AnySchema)
              .validate(value)
              .then(() => {
                callback();
              })
              .catch((error) => {
                callback(new Error(error.errors[0]));
              });
          },
          trigger: ["blur", "change"],
        },
      ],
      officePhone: [
        {
          validator: (_: any, value: any, callback: any) => {
            if (value) {
              (yupRule.fields.officePhone as yup.AnySchema)
                .validate(value)
                .then(() => {
                  callback();
                })
                .catch((error) => {
                  callback(new Error(error.errors[0]));
                });
            } else {
              callback();
            }
          },
          trigger: ["blur", "change"],
        },
      ],
    };

    const userInfoRules = ref<FormRules<any>>(
      isSystemAdmin() || isUserDetailSystemAdmin
        ? {
            ...initialRules,
            companyId: [
              {
                required: true,
                message: "Please select Company",
                trigger: ["change", "blur"],
              },
            ],
          }
        : initialRules
    );

    const roleRules = ref<FormRules<any>>({
      userRoles: [
        {
          type: "array",
          required: true,
          message: "Please choose role",
          trigger: "change",
        },
      ],
    });

    const editUserInfo = () => {
      isEditMode.value = true;
    };

    const cancelEditUserInfo = () => {
      isEditMode.value = false;
      userInfoFormRef?.value?.resetFields();
      formData.value = {
        ...props.userDetail,
        companyId: props?.userDetail?.company?.id,
      };
    };

    const cancelEditRole = () => {
      isEditRole.value = false;
      roleFormRef?.value?.resetFields();
      roleData.value = {
        userRoles: getUserRoles(),
      };
    };

    const editRole = () => {
      isEditRole.value = true;
    };

    const submit = () => {
      if (!userInfoFormRef.value) {
        return;
      }

      userInfoFormRef.value.validate((valid: boolean) => {
        if (valid) {
          submittingUserInfo.value = true;

          const params = {
            id: formData.value?.id,
            email:
              formData.value?.email !== props?.userDetail?.email
                ? formData.value?.email
                : null,
            firstName: formData.value?.firstName,
            lastName: formData.value?.lastName,
            address: formData.value?.address || null,
            note: formData.value?.note || null,
            officePhone: formData.value?.officePhone || null,
            mobilePhone: formData.value?.mobilePhone,
            companyId: formData.value?.companyId,
          };

          if (isOwner()) {
            updateMyProfile(params, () => {
              submittingUserInfo.value = false;
              isEditMode.value = false;
            });
          } else {
            updateUserProfileById(params, () => {
              submittingUserInfo.value = false;
              isEditMode.value = false;
            });
          }
        }
      });
    };

    const submitRole = () => {
      if (!roleFormRef.value) {
        return;
      }

      roleFormRef.value.validate((valid: boolean) => {
        if (valid) {
          submittingRole.value = true;
          if (isOwner()) {
            updateMyProfile(roleData.value, () => {
              submittingRole.value = false;
              isEditRole.value = false;
            });
          } else {
            updateUserProfileById(
              { ...roleData.value, id: formData.value?.id },
              () => {
                submittingRole.value = false;
                isEditRole.value = false;
              }
            );
          }
        }
      });
    };

    const resetPass = () => {
      loading.value = true;
      userStore.resetUserPassword({
        params: { email: props.userDetail.email || "" },
        callback: {
          onSuccess: () => {
            loading.value = false;
            AlertService.resultAlert(
              "An email is sent to user’s email with the link to reset user password. Please check with your user!",
              undefined,
              "/media/reset-pass.jpg",
              70
            );
          },
          onFinish: () => {
            loading.value = false;
          },
        },
      });
    };

    const isCompanyAdmin = () => {
      return JwtService.checkRole(UserType.CompanyAdmin);
    };

    const isOwner = () => {
      return (
        route.name === "my-profile" ||
        JwtService.getUserInfo()?.id === props?.userDetail?.id
      );
    };

    const isShowPassword = () => {
      return isAdmin() || isOwner();
    };

    const toggleChangePassword = (): void => {
      isModalVisible.value = !isModalVisible.value;
    };

    const updateMyProfile = (params: User.Info, onFinish: () => void) => {
      userStore.updateMyProfile({
        params,
        callback: {
          onSuccess: (_res: any) => {
            props?.reloadUserData?.();
          },
          onFinish,
        },
      });
    };

    const updateUserProfileById = (params: User.Info, onFinish: () => void) => {
      userStore.updateUserProfile({
        id: params?.id!,
        params,
        callback: {
          onSuccess: () => {
            props?.reloadUserData?.();
          },
          onFinish,
        },
      });
    };

    return {
      isSystemAdmin,
      isUserDetailSystemAdmin,
      UserType,
      roleRules,
      userInfoRules,
      formData,
      roleData,
      userInfoFormRef,
      roleFormRef,
      isEditMode,
      isEditRole,
      submittingUserInfo,
      submittingRole,
      changePasswordModal,
      companyList,
      loading,
      isModalVisible,
      submit,
      resetPass,
      submitRole,
      isAdmin,
      editUserInfo,
      cancelEditUserInfo,
      isOwner,
      isShowPassword,
      isCompanyAdmin,
      editRole,
      cancelEditRole,
      toggleChangePassword,
    };
  },
});
</script>

<style>
.security {
  .card {
    height: 100%;
  }

  .security-action {
    button {
      width: fit-content;
      margin-top: 16px;
    }
  }
}
</style>
