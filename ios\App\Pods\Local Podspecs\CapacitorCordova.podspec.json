{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "module_name": "Cordova", "version": "7.2.0", "summary": "Capacitor Cordova Compatibility Layer", "homepage": "https://capacitorjs.com", "license": "MIT", "authors": {"Ionic Team": "<EMAIL>"}, "source": {"git": "https://github.com/ionic-team/capacitor", "tag": "7.2.0"}, "platforms": {"ios": "14.0"}, "source_files": "CapacitorCordova/<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>/**/*.{h,m}", "public_header_files": ["CapacitorCordova/CapacitorCordova/Classes/Public/*.h", "CapacitorCordova/CapacitorCordova/CapacitorCordova.h"], "module_map": "CapacitorCordova/CapacitorCordova/CapacitorCordova.modulemap", "resources": ["CapacitorCordova/CapacitorCordova/PrivacyInfo.xcprivacy"], "requires_arc": true, "frameworks": "WebKit"}