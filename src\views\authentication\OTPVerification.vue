<template>
  <div class="text-center mb-10">
    <img src="/media/OTP-verification.svg" :width="100" />
  </div>
  <div class="d-flex flex-column gap-10">
    <h1 class="text-dark text-center">OTP Verification</h1>
    <div class="text-gray-400 text-center fw-semibold fs-3">
      {{
        `Please enter the 6 digit code sent to ${email} within 3
      minutes. Remember to check your email!`
      }}
    </div>
    <div class="fv-row">
      <div class="text-dark fw-bold mb-3">Type your 6 digit security code</div>
      <vue3-otp-input
        ref="otpInput"
        input-classes="otp-input"
        inputType="number"
        :num-inputs="6"
        v-model:value="bindValue"
        :should-auto-focus="true"
        :should-focus-order="true"
        @on-complete="handleOnComplete"
        @update:value="bindValue = $event"
      />
      <div v-if="error" class="error-message mt-3">{{ error }}</div>
    </div>
    <div class="text-gray-400 text-center fw-semibold fs-3">
      <vue-countdown
        v-if="counting"
        auto-start
        :time="180 * 1000"
        v-slot="{ totalSeconds }"
        @end="onCountdownEnd"
      >
        Resend code in {{ totalSeconds }} s
      </vue-countdown>
      <span
        v-else-if="counting === false"
        class="link-primary fs-3 fw-bold resend-code"
        @click="startCountdown"
      >
        Resend code
      </span>
    </div>
    <div class="d-flex flex-wrap justify-content-end pb-lg-0">
      <router-link to="/sign-in" class="btn btn-lg btn-light-primary fw-bold"
        >Cancel</router-link
      >
      <button
        class="btn btn-sm btn-primary ms-5"
        type="submit"
        :disabled="loading"
        @click="onVerify"
      >
        <span v-if="!loading" class="indicator-label"> Verify Code </span>
        <span v-if="loading" class="indicator-progress">
          Please wait...
          <span
            class="spinner-border spinner-border-sm align-middle ms-2"
          ></span>
        </span>
      </button>
    </div>
  </div>
</template>

<script lang="ts">
import { ExceptionCode, ExceptionMessages } from "@/constants/exceptions";
import { useAuthStore } from "@/stores/auth";
import Swal from "sweetalert2";
import { defineComponent, ref } from "vue";
import { useRouter } from "vue-router";
import Vue3OtpInput from "vue3-otp-input";

export default defineComponent({
  name: "otp-verification",
  components: { Vue3OtpInput },
  props: {
    email: String,
    userForgot: {
      type: Object,
      required: false,
    },
  },
  setup(props) {
    const store = useAuthStore();
    const router = useRouter();
    const otpInput = <InstanceType<typeof Vue3OtpInput> | null>null;
    const bindValue = ref("");
    const counting = ref<boolean | null>(true);
    const error = ref<string>("");
    const loading = ref<boolean>(false);

    const handleOnComplete = (_value: string) => {
      ////
    };

    const startCountdown = () => {
      store.forgotPassword({
        email: props?.email || "",
        callback: {
          onSuccess: () => {
            counting.value = true;
          },
          onFinish: () => {
            loading.value = false;
          },
          onFailure: (error) => {
            Swal.fire({
              text:
                ExceptionMessages[error?.response?.data?.errorCode as ExceptionCode] ||
                error?.response?.data?.message ||
                error?.message ||
                "Sorry, looks like there are some errors detected, please try again.",

              icon: "error",
              buttonsStyling: false,
              confirmButtonText: "Got it!",
              heightAuto: false,
              customClass: {
                confirmButton: "btn fw-semibold btn-light-danger",
              },
            });
          },
        },
      });
    };

    const onCountdownEnd = () => {
      counting.value = false;
    };

    const onVerify = () => {
      if (bindValue.value.length !== 6) {
        error.value = "Please type the code";
      } else {
        error.value = "";
        loading.value = true;
        store.validateOTP({
          params: {
            email: props?.email || "",
            otp: bindValue.value,
          },
          callback: {
            onSuccess: () => {
              router.push({
                path: "/reset-password",
                query: {
                  email: props?.email,
                  otp: bindValue.value,
                  user_name: props?.userForgot?.name,
                },
              });
            },
            onFinish: () => {
              loading.value = false;
            },
            onFailure: (error) => {
              Swal.fire({
                text:
                  ExceptionMessages[error?.response?.data?.errorCode as ExceptionCode] ||
                  error?.response?.data?.message ||
                  error?.message ||
                  "Sorry, looks like there are some errors detected, please try again.",

                icon: "error",
                buttonsStyling: false,
                confirmButtonText: "Got it!",
                heightAuto: false,
                customClass: {
                  confirmButton: "btn fw-semibold btn-light-danger",
                },
              });
            },
          },
        });
      }
    };

    return {
      startCountdown,
      onCountdownEnd,
      handleOnComplete,
      onVerify,
      error,
      bindValue,
      otpInput,
      counting,
      loading,
    };
  },
});
</script>