<template>
  <div
    v-if="isVisible"
    class="fixed top-0 right-0 bottom-0 left-0 z-40 flex items-center"
  >
    <!-- Dark overlay background -->
    <div class="absolute inset-0 bg-sidebar-background opacity-25"></div>
    <div
      class="relative bg-card-background text-card-text-light max-h-11/12 w-11/12 flex flex-col items-center gap-3 mx-auto my-6 p-5 rounded-xl md:overflow-hidden md:w-3/5 lg:w-2/5 lg:h-auto"
    >
      <div class="flex flex-row h-auto w-full items-center justify-between">
        <h3 class="text-lg font-bold">
          {{ `${id ? "Edit Note" : "New Note"}` }}
        </h3>
        <span class="cursor-pointer ms-auto" @click="closeModal">
          <SvgIcon icon="closeModalIcon" />
        </span>
      </div>

      <el-form
        id="cost_form"
        @submit.prevent="submit"
        :model="targetData"
        :rules="rules"
        ref="formRef"
        class="h-auto w-full flex flex-col gap-3"
      >
        <div class="flex flex-col gap-1">
          <label class="font-bold"
            >Title<span class="text-danger-active font-light">*</span>
          </label>
          <el-form-item prop="title" class="mt-auto">
            <el-input
              v-model="targetData.title"
              placeholder=""
              name="title"
            ></el-input>
          </el-form-item>
        </div>

        <div class="flex flex-col gap-1">
          <label class="font-bold"
            >Notes<span class="text-danger-active font-light">*</span></label
          >
          <el-form-item prop="notes">
            <el-input
              v-model="targetData.notes"
              placeholder=""
              name="notes"
              type="textarea"
              :rows="5"
            ></el-input>
          </el-form-item>
        </div>

        <div class="h-auto w-full flex flex-row items-center gap-2">
          <button
            type="button"
            class="bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover rounded-md px-4 py-2 font-semibold"
            @click="closeModal"
            :disabled="loading"
          >
            Discard
          </button>
          <button
            class="bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover rounded-md px-4 py-2 font-semibold"
            type="submit"
            :disabled="loading"
          >
            <span v-if="!loading" class="indicator-label"> Save </span>
            <span v-if="loading" class="indicator-progress">
              Please wait...
              <span
                class="spinner-border spinner-border-sm align-middle ms-2"
              ></span>
            </span>
          </button>
        </div>
      </el-form>
    </div>
  </div>
</template>

<script lang="ts">
import SvgIcon from "@/constants/SvgIcon.vue";
import { useNoteStore } from "@/stores/note";
import { defineComponent, inject, ref, watch } from "vue";
import { useRoute } from "vue-router";
import type { Provide } from "@/types/injection-types";

// interface FileItem {
//   title: string;
//   type: string;
//   size: string;
// }

export default defineComponent({
  name: "notes-modal",
  components: { SvgIcon },
  props: {
    isVisible: {
      type: Boolean,
      required: true,
    },
    close: {
      type: Function,
      required: true,
    },
    loadPage: {
      type: Function,
      default: () => {},
      required: true,
    },
  },
  setup(props) {
    const route = useRoute();
    const noteStore = useNoteStore();
    const modal = ref(false);
    const targetData = ref<any>({
      title: "",
      notes: "",
    });
    const formRef = ref<null | HTMLFormElement>(null);
    const inputFile = ref<null | HTMLFormElement>(null);
    const loading = ref<boolean>(false);
    const id = ref("");
    const dailyReportProvide = inject<Provide.DailyReport>("dailyReport");

    watch(id, (newValue) => {
      if (newValue !== "") {
        getNoteDetails();
      }
    });

    watch(
      () => props.isVisible,
      (newValue) => {
        if (newValue === false) {
          id.value = "";
          reset();
          formRef?.value?.resetFields();
        }
      }
    );

    const getNoteDetails = async (): Promise<void> => {
      noteStore.getNoteDetails({
        id: id.value,
        callback: {
          onSuccess: (res: any) => {
            targetData.value = {
              ...res,
              costSettingId: res?.costSetting?.id,
            };
          },
        },
      });
    };

    const updateNote = async (param: any): Promise<void> => {
      loading.value = true;
      noteStore.updateNote({
        id: id.value,
        params: param,
        callback: {
          onSuccess: (_res: any) => {
            if (props?.loadPage) {
              props?.loadPage();
            }
            closeModal();
          },
          onFinish: (_err: any) => {
            loading.value = false;
          },
        },
      });
    };

    const createNote = async (param: any): Promise<void> => {
      loading.value = true;
      noteStore.createNote({
        params: param,
        callback: {
          onSuccess: (_res: any) => {
            if (props?.loadPage) {
              props?.loadPage();
            }
            closeModal();
          },
          onFinish: (_err: any) => {
            loading.value = false;
          },
        },
      });
    };

    const closeModal = () => {
      props.close();
    };

    const setId = (wellId: string) => {
      id.value = wellId.toString();
    };

    const rules = ref({
      title: [
        {
          required: true,
          message: "Please select Title",
          trigger: "change",
        },
      ],
      notes: [
        {
          required: true,
          message: "Please type Notes",
          trigger: "blur",
        },
      ],
    });

    const submit = () => {
      if (!formRef.value) {
        return;
      }

      formRef.value.validate((valid: boolean) => {
        if (valid) {
          if (id?.value) {
            updateNote({
              ...targetData?.value,
              dailyReportId: dailyReportProvide?.getDailyReportId(),
            });
          } else {
            dailyReportProvide?.createDailyReport({
              wellId: route?.params?.id as string,
              callback: {
                onSuccess: (_res: string) => {
                  createNote({
                    ...targetData?.value,
                    dailyReportId: dailyReportProvide?.getDailyReportId(),
                  });
                },
              },
            });
          }
        }
      });
    };

    const reset = () => {
      targetData.value = {
        title: "",
        notes: "",
      };
    };

    const deleteFile = (item: any) => {
      const index = targetData?.value?.files?.indexOf(item);
      if (index !== -1) {
        targetData?.value.files?.splice(index, 1);
      }
    };

    const addFile = () => {
      inputFile?.value?.click();
    };

    const handleFile = (e: any) => {
      if (e.target?.files?.length > 0) {
        // const newFilesArray = Array.from(e.target.files).map((file: any) => {
        //   return {
        //     title: file?.name.toString(),
        //     type: file?.name.toString().split(".").pop(),
        //     size: getSize(file?.size),
        //   };
        // });
        // targetData.value.files = targetData.value.files.concat(newFilesArray);
      }
    };

    // const getSize = (size: number) => {
    //   if (size < 1024) return `${size} byte`;
    //   return `${Math.round(size / 1024).toString()} kb`;
    // };

    return {
      id,
      modal,
      rules,
      loading,
      targetData,
      formRef,
      inputFile,
      closeModal,
      submit,
      setId,
      reset,
      deleteFile,
      addFile,
      handleFile,
    };
  },
});
</script>
