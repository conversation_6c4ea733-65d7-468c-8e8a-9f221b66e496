import{S as $}from"./SvgIcon-CMhyaXWN.js";import{d as D,_ as N,c as R,o as F,l as v,a as y,m as _,s as I,b as H,r as C,B,w as P,O as w,P as t,Q as r}from"./index-CGNRhvz7.js";import{h as p}from"./handleFailure-DtTpu7r3.js";const x=D({name:"bottom-tool",components:{SvgIcon:$},props:{addNew:{type:Function,required:!1},showAddNew:{type:Boolean,required:!1,default:!0},showHelpInfo:{type:Boolean,required:!1,default:!0}},setup(l){return{addNewModal:()=>{l.addNew&&l.addNew()}}}}),T={className:"h-auto w-11/12 flex flex-row gap-4 items-center justify-end fixed bottom-4 right-5 cursor-pointer"},W={key:0},M={class:"relative group inline-block"},O={class:"btn rounded-circle btn-icon btn-success btn-tool"};function A(l,u,g,S,m,f){const d=C("SvgIcon"),n=C("el-tooltip");return F(),R("div",T,[l.$slots.header?(F(),R("div",W,[I(l.$slots,"header")])):v("",!0),y("div",M,[y("button",{class:"bg-primary rounded-full p-4",onClick:u[0]||(u[0]=(...a)=>l.addNewModal&&l.addNewModal(...a))},[H(d,{icon:"addIcon"})]),u[1]||(u[1]=y("div",{class:"h-auto w-auto absolute left-1/2 transform -translate-x-1/2 bottom-full mb-2 py-1 px-2 hidden group-hover:block bg-white text-dark rounded whitespace-nowrap"},[B(" Add New "),y("div",{class:"absolute left-1/2 transform -translate-x-1/2 top-full w-0 h-0 border-l-4 border-r-4 border-t-4 border-t-white border-l-transparent border-r-transparent"})],-1))]),l.showHelpInfo?(F(),_(n,{key:1,content:"Help Info",placement:"top",effect:"customize"},{default:P(()=>[y("button",O,[H(d,{icon:"helpIcon"})])]),_:1})):v("",!0)])}const E=N(x,[["render",A]]),j=w("dailyReport",()=>({getDailyReports:async({params:a,callback:o})=>{var i;const s=t.get(o,"onSuccess",t.noop),e=t.get(o,"onFinish",t.noop);try{r.setHeader();const c=await r.getWithParams("dailyReports",a);s(((i=c.data)==null?void 0:i.data)||c.data)}catch(c){p(c,o)}finally{e()}},getDailyReportById:async({id:a,callback:o})=>{var i;const s=t.get(o,"onSuccess",t.noop),e=t.get(o,"onFinish",t.noop);try{r.setHeader();const c=await r.get(`dailyReports/${a}`);s(((i=c.data)==null?void 0:i.data)||c.data)}catch(c){p(c,o)}finally{e()}},getTodayReport:async({id:a,callback:o})=>{var i;const s=t.get(o,"onSuccess",t.noop),e=t.get(o,"onFinish",t.noop);try{r.setHeader();const c=await r.get(`dailyReports/todayReport/${a}`);s(((i=c.data)==null?void 0:i.data)||c.data)}catch(c){p(c,o)}finally{e()}},updateDailyReport:async({id:a,params:o,callback:s})=>{var c;const e=t.get(s,"onSuccess",t.noop),i=t.get(s,"onFinish",t.noop);try{r.setHeader();const h=await r.put(`dailyReports/${a}`,o);e(((c=h.data)==null?void 0:c.data)||h.data)}catch(h){p(h,s)}finally{i()}},deleteDailyReport:async({id:a,callback:o})=>{var i;const s=t.get(o,"onSuccess",t.noop),e=t.get(o,"onFinish",t.noop);try{r.setHeader();const c=await r.delete(`dailyReports/${a}`);s(((i=c.data)==null?void 0:i.data)||c.data)}catch(c){p(c,o)}finally{e()}},createDailyReport:async({wellId:a,callback:o})=>{var i;const s=t.get(o,"onSuccess",t.noop),e=t.get(o,"onFinish",t.noop);try{r.setHeader();const c=await r.post("dailyReports",{wellId:a});s(((i=c.data)==null?void 0:i.data)||c.data)}catch(c){p(c,o)}finally{e()}},getLatestDailyReport:async({wellId:a,callback:o})=>{var i;const s=t.get(o,"onSuccess",t.noop),e=t.get(o,"onFinish",t.noop);try{r.setHeader();const c=await r.get(`dailyReports/latest/${a}`);s(((i=c.data)==null?void 0:i.data)||c.data)}catch(c){p(c,o)}finally{e()}},getWellInformationTab:async({wellId:a,callback:o})=>{const s=t.get(o,"onSuccess",t.noop),e=t.get(o,"onFinish",t.noop);try{r.setHeader();const i=await Promise.all([r.get(`wells/${a}`),r.get(`dailyReports/latest/${a}`)]);s(i)}catch(i){p(i,o)}finally{e()}}})),z=w("cost",()=>({getCosts:async({params:d,callback:n})=>{var s;const a=t.get(n,"onSuccess",t.noop),o=t.get(n,"onFinish",t.noop);try{r.setHeader();const e=await r.getWithParams("costs",d);a(((s=e.data)==null?void 0:s.data)||e.data)}catch(e){p(e,n)}finally{o()}},getCostDetails:async({id:d,callback:n})=>{var s;const a=t.get(n,"onSuccess",t.noop),o=t.get(n,"onFinish",t.noop);try{r.setHeader();const e=await r.get(`costs/${d}`);a(((s=e.data)==null?void 0:s.data)||e.data)}catch(e){p(e,n)}finally{o()}},getCostSummary:async({params:d,callback:n})=>{var s;const a=t.get(n,"onSuccess",t.noop),o=t.get(n,"onFinish",t.noop);try{r.setHeader();const e=await r.getWithParams("costs/detail/costSummary",d);a(((s=e.data)==null?void 0:s.data)||e.data)}catch(e){p(e,n)}finally{o()}},updateCost:async({id:d,params:n,callback:a})=>{var e;const o=t.get(a,"onSuccess",t.noop),s=t.get(a,"onFinish",t.noop);try{r.setHeader();const i=await r.put(`costs/${d}`,n);o(((e=i.data)==null?void 0:e.data)||i.data)}catch(i){p(i,a)}finally{s()}},deleteCost:async({id:d,callback:n})=>{var s;const a=t.get(n,"onSuccess",t.noop),o=t.get(n,"onFinish",t.noop);try{r.setHeader();const e=await r.delete(`costs/${d}`);a(((s=e.data)==null?void 0:s.data)||e.data)}catch(e){p(e,n)}finally{o()}},createCost:async({params:d,callback:n})=>{var s;const a=t.get(n,"onSuccess",t.noop),o=t.get(n,"onFinish",t.noop);try{r.setHeader();const e=await r.post("costs",d);a(((s=e.data)==null?void 0:s.data)||e.data)}catch(e){p(e,n)}finally{o()}}})),Q=w("sample",()=>({getSamples:async({params:d,callback:n})=>{var s;const a=t.get(n,"onSuccess",t.noop),o=t.get(n,"onFinish",t.noop);try{r.setHeader();const e=await r.getWithParams("samples",d);a(((s=e.data)==null?void 0:s.data)||e.data)}catch(e){p(e,n)}finally{o()}},getSampleDetails:async({id:d,callback:n})=>{var s;const a=t.get(n,"onSuccess",t.noop),o=t.get(n,"onFinish",t.noop);try{r.setHeader();const e=await r.get(`samples/${d}`);a(((s=e.data)==null?void 0:s.data)||e.data)}catch(e){p(e,n)}finally{o()}},updateSample:async({id:d,params:n,callback:a})=>{var e;const o=t.get(a,"onSuccess",t.noop),s=t.get(a,"onFinish",t.noop);try{r.setHeader();const i=await r.put(`samples/${d}`,n);o(((e=i.data)==null?void 0:e.data)||i.data)}catch(i){p(i,a)}finally{s()}},deleteSample:async({id:d,callback:n})=>{var s;const a=t.get(n,"onSuccess",t.noop),o=t.get(n,"onFinish",t.noop);try{r.setHeader();const e=await r.delete(`samples/${d}`);a(((s=e.data)==null?void 0:s.data)||e.data)}catch(e){p(e,n)}finally{o()}},createSample:async({params:d,callback:n})=>{var s;const a=t.get(n,"onSuccess",t.noop),o=t.get(n,"onFinish",t.noop);try{r.setHeader();const e=await r.post("samples",d);a(((s=e.data)==null?void 0:s.data)||e.data)}catch(e){p(e,n)}finally{o()}},getSampleChartInfo:async({id:d,callback:n})=>{var s;const a=t.get(n,"onSuccess",t.noop),o=t.get(n,"onFinish",t.noop);try{r.setHeader();const e=await r.get(`samples/chart/${d}`);a(((s=e.data)==null?void 0:s.data)||e.data)}catch(e){p(e,n)}finally{o()}}})),G=[{value:1,label:"Water"},{value:2,label:"Oil"},{value:3,label:"Synthetic"}],J=[{value:"1",label:"Active"},{value:"2",label:"Suction Pit"},{value:"3",label:"Reserve Pit"},{value:"4",label:"Shakers"},{value:"5",label:"Mud Tanks"},{value:"6",label:"Pump Suction"},{value:"7",label:"Other Locations"}];export{E as B,z as a,Q as b,G as f,J as s,j as u};
