<template>
  <div
    v-if="isVisible"
    class="fixed top-0 right-0 bottom-0 left-0 z-40 flex items-center"
  >
    <!-- Dark overlay background -->
    <div class="absolute inset-0 bg-sidebar-background opacity-25"></div>
    <div
      class="relative bg-card-background text-card-text max-h-11/12 w-11/12 flex flex-col items-center gap-3 mx-auto p-5 rounded-xl md:w-4/6 md:text-lg lg:w-2/5 lg:h-2/5"
    >
      <div class="h-auto w-full flex flex-row items-center justify-between">
        <h3 class="text-lg font-bold">
          {{ id ? `Edit ${label}` : `Add ${label}` }}
        </h3>
        <span class="cursor-pointer" @click="close">
          <SvgIcon icon="closeModalIcon" />
        </span>
      </div>

      <div class="h-auto w-full">
        <el-form
          id="service_costs_form"
          @submit.prevent="submit"
          :model="targetData"
          :rules="rules"
          ref="formRef"
          class="form"
        >
          <div v-if="isSystemAdmin()" class="h-auto w-full flex flex-col gap-2">
            <label class="font-semibold">Company </label>
            <el-form-item prop="companyId">
              <el-select-v2
                v-model="targetData.companyId"
                :options="companyList"
                placeholder="Search Company"
                filterable
                name="companyId"
                :loading="loadingCompany"
              />
            </el-form-item>
          </div>
          <div class="h-auto w-full flex flex-col gap-2">
            <label class="font-semibold">Name </label>
            <el-form-item prop="name">
              <el-input
                v-model="targetData.name"
                placeholder=""
                name="name"
              ></el-input>
            </el-form-item>
          </div>
          <div class="h-auto w-full flex flex-col gap-2">
            <label class="font-semibold">Description </label>
            <el-form-item prop="description">
              <el-input
                type="textarea"
                :rows="5"
                v-model="targetData.description"
                placeholder=""
                name="description"
              ></el-input>
            </el-form-item>
          </div>
          <div class="h-auto w-full flex flex-col gap-2">
            <label class="font-semibold">Price </label>
            <el-form-item prop="cost">
              <el-input
                type="number"
                :controls="false"
                step="any"
                v-model="targetData.cost"
                placeholder=""
                name="cost"
              ></el-input>
            </el-form-item>
          </div>
          <div class="flex flex-row items-start mt-4 gap-3">
            <button
              type="button"
              class="bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover rounded-md px-4 py-2 font-semibold"
              @click="close"
              :disabled="loading"
            >
              Discard
            </button>
            <button
              class="bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover rounded-md px-4 py-2 font-semibold"
              type="submit"
              :disabled="loading"
            >
              <span v-if="!loading" class="indicator-label"> Save </span>
              <span v-if="loading" class="indicator-progress">
                Please wait...
                <span
                  class="spinner-border spinner-border-sm align-middle ms-2"
                ></span>
              </span>
            </button>
          </div>
        </el-form>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import SvgIcon from "@/constants/SvgIcon.vue";
import JwtService, { isSystemAdmin } from "@/services/JwtService";
import { useCompanyStore } from "@/stores/company";
import { useCostSettingStore } from "@/stores/cost-setting";
import { defineComponent, onMounted, ref, watch } from "vue";
import type { Option } from "@/types/common";

export default defineComponent({
  name: "costs-modal",
  components: { SvgIcon },
  props: {
    isVisible: {
      type: Boolean,
      required: true,
    },
    close: {
      type: Function,
      required: true,
    },
    loadPage: {
      type: Function,
      required: false,
    },
    type: {
      type: Number,
      required: true,
    },
    label: {
      type: String,
      required: true,
    },
  },
  setup(props) {
    const companyStore = useCompanyStore();
    const costSettingStore = useCostSettingStore();
    const modal = ref(false);
    const initialData = {
      companyId: isSystemAdmin() ? null : JwtService.getUserInfo()?.companyId,
      name: "",
      description: "",
      cost: null,
    };
    const targetData = ref({ ...initialData });
    const formRef = ref<null | HTMLFormElement>(null);
    const loading = ref<boolean>(false);
    const loadingCompany = ref<boolean>(false);
    const companyList = ref<Option[]>([]);
    const id = ref("");

    onMounted(() => {
      if (isSystemAdmin()) {
        getCompanies();
      }
    });

    watch(id, (newValue) => {
      if (newValue !== "") {
        getCostDetails();
      }
    });

    watch(modal, (newValue) => {
      if (newValue === false) {
        id.value = "";
        reset();
        formRef?.value?.resetFields();
      }
    });

    const getCompanies = async (): Promise<void> => {
      loadingCompany.value = true;
      companyStore.getCompanies({
        params: {
          page: 1,
          limit: 500,
        },
        callback: {
          onSuccess: (res: any) => {
            const list = res?.items.map((item: any) => {
              return {
                label: item?.name,
                value: item?.id,
              };
            });
            companyList.value = [...list];
          },
          onFinish: () => {
            loadingCompany.value = false;
          },
        },
      });
    };

    const getCostDetails = async (): Promise<void> => {
      costSettingStore.getCostSettingDetails({
        id: id.value,
        callback: {
          onSuccess: (res: any) => {
            targetData.value = { ...res, companyId: res?.company?.id };
          },
        },
      });
    };

    const updateCost = async (param: any): Promise<void> => {
      loading.value = true;
      costSettingStore.updateCostSetting({
        id: id.value,
        params: {
          ...param,
          type: props.type,
        },
        callback: {
          onSuccess: (_res: any) => {
            if (props?.loadPage) {
              props?.loadPage();
            }
            props.close();
          },
          onFinish: (_err: any) => {
            loading.value = false;
          },
        },
      });
    };

    const createCost = async (param: any): Promise<void> => {
      loading.value = true;
      costSettingStore.createCostSetting({
        params: {
          ...param,
          type: props.type,
        },
        callback: {
          onSuccess: (_res: any) => {
            if (props?.loadPage) {
              props?.loadPage();
            }
            props.close();
          },
          onFinish: (_err: any) => {
            loading.value = false;
          },
        },
      });
    };

    const close = () => {
      props.close();
    };

    const setId = (solidId: string) => {
      id.value = solidId.toString();
    };

    const initialRule = {
      name: [
        {
          required: true,
          message: "Please type Description",
          trigger: "blur",
        },
      ],
      description: [
        {
          required: true,
          message: "Please type Description",
          trigger: "blur",
        },
      ],
      cost: [
        {
          required: true,
          message: "Please type Price",
          trigger: "blur",
        },
      ],
    };

    const rules = isSystemAdmin()
      ? {
          ...initialRule,
          companyId: [
            {
              required: true,
              message: "Please select Company",
              trigger: ["change", "blur"],
            },
          ],
        }
      : initialRule;

    const submit = () => {
      if (!formRef.value) {
        return;
      }

      formRef.value.validate((valid: boolean) => {
        if (valid) {
          const params = {
            ...targetData?.value,
            cost: Number(targetData?.value?.cost),
          };
          if (id?.value) {
            updateCost(params);
          } else {
            createCost(params);
          }
        }
      });
    };

    const reset = () => {
      targetData.value = { ...initialData };
    };

    return {
      id,
      modal,
      rules,
      loading,
      targetData,
      formRef,
      isSystemAdmin,
      loadingCompany,
      companyList,
      close,
      submit,
      setId,
      reset,
    };
  },
});
</script>
