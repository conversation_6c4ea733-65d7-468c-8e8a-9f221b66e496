import{d as ee,q as n,j as ve,a0 as q,x as fe,A as O,J,E as j,e as pe,_ as oe,c as r,o as a,a as o,b as m,m as z,w as P,r as u,p as he,l as U,B as Y,H as W,M as X,t as A,F as K,k as se,Z as Ue,a1 as Ve,a2 as Ee,U as F,a3 as be,R as De,n as xe}from"./index-CGNRhvz7.js";import{C as Te}from"./CompanyModal-Cba-MY3X.js";import{P as Ce}from"./PageHeader-3hadTn26.js";import{T as ke}from"./TablePagination-BmVxunEG.js";import{S as me}from"./SvgIcon-CMhyaXWN.js";import{T as we}from"./TableHeader-C1CWTWQa.js";import{S as Z,a as $e}from"./table-bhK9qpe4.js";import{f as _e}from"./date-CvSHk5ED.js";import{u as Pe}from"./customer-C9SausZF.js";import{C as Le}from"./CustomerModal-B1iI3o6f.js";import{u as Ae}from"./user-KFDu8xJF.js";import{c as Ne,a as Re}from"./index.esm-DXW765zG.js";import{A as Fe}from"./AssignUserModal-B-x10c2w.js";import{u as Be}from"./company-oDyd0dWV.js";const He=ee({name:"customer-list",components:{PageHeader:Ce,SvgIcon:me,TablePagination:ke,CustomerModal:Le,TableHeader:we},setup(){const e=Pe(),s=ve(),I=pe(),V=n([]),M=n(!1),b=n(0),c=n(0),f=n(1),w=n(!1),y=n(""),g=n(!1),d=n(null),E=n([]),$=n([]),p=s.name==="my-company",D=n({sortDirection:$e.ASC,sortBy:Z.CustomerName}),S=n(!1),h=[{label:"",class:"w-25px",display:q()},{label:"FULL NAME",sortBy:Z.CustomerName,class:"min-w-150px"},{label:"NOTE",class:"min-w-150px"},{label:"CREATED DATE",class:"min-w-150px"},{label:"ACTIONS",class:"min-w-60px",display:q()}];fe(()=>{l()}),O(V,v=>{M.value=$.value.length!==0&&v.length===$.value.length}),O(f,()=>{l()});const l=async()=>{var v,L,B;w.value=!0,e.getCustomers({params:{name:y.value.trim()||null,companyId:p?(v=J.getUserInfo())==null?void 0:v.companyId:(B=(L=s.params)==null?void 0:L.id)==null?void 0:B.toString(),page:f.value,limit:10,...D.value},callback:{onSuccess:x=>{$.value=[...x==null?void 0:x.items],b.value=x==null?void 0:x.totalPage,c.value=x==null?void 0:x.total,f.value=x==null?void 0:x.page},onFinish:()=>{w.value=!1}}})},_=v=>{D.value={...v},l()},t=()=>{f.value!==1?f.value=1:l()},N=()=>{S.value=!S.value},te=v=>{var L;S.value=!S.value,(L=d==null?void 0:d.value)==null||L.setId(v)},R=()=>{j.deletionAlert({onConfirmed:()=>{Q(V.value,!0)}})},le=v=>{j.deletionAlert({onConfirmed:()=>{Q([v])}})},G=v=>{f.value=v},ne=()=>{g.value=!g.value},ae=v=>{var L;(L=v==null?void 0:v.target)!=null&&L.checked?V.value=$.value.map(B=>B.id):V.value=[]},re=v=>{I.push({path:`/users/${v}`})},ie=v=>s.name==="company-detail"?`/companies/${s.params.id}/customer/${v}`:s.name==="my-company"?`/my-company/customer/${v}`:"#",Q=async(v,L=!1)=>{w.value=!0,e.removeCustomers({customerIds:v,callback:{onSuccess:B=>{L&&(V.value=[]),l()},onFinish:()=>{w.value=!1}}})};return{sortParams:D,tableHeader:h,search:y,loading:w,customerModal:d,checkedRows:V,checkAll:M,customerList:$,currentPage:f,totalElements:c,pageCount:b,isShowModal:g,searchList:E,isModalVisible:S,pageChange:G,toggleFilter:ne,deleteCustomer:le,formatDate:_e,view:re,onRemove:R,toggleNewCustomer:N,toggleEditCustomer:te,isAdmin:q,getCustomerDetailPath:ie,onToggleCheckAll:ae,getCompanyCustomer:l,searchCustomer:t,onSort:_}}}),qe={class:"bg-white rounded-lg p-4"},Oe={class:"h-auto w-full flex flex-col gap-2 items-start"},je={class:"h-auto w-full"},Ye={class:"svg-icon svg-icon-2"},ze={class:"h-auto w-full flex flex-row justify-end gap-4"},Ke={key:0,class:"text-center p-5"},Je={key:2,class:"h-auto w-full overflow-x-scroll p-4"},Ze={class:"font-bold text-gray-400 whitespace-nowrap"},Ge={class:"font-bold text-gray-400"},Qe={key:0},We={class:"form-check form-check-sm form-check-custom form-check-solid"},Xe={key:1,class:"p-4"},eo={key:0},oo=["value"],so={class:"w-36 p-4"},to={class:"flex flex-row items-center gap-3"},lo={class:"text-grey-600 font-semibold"},no={class:"text-grey-600 font-semibold"},ao={key:1},ro={class:"h-auto w-full flex flex-row items-center justify-evenly gap-2"},io=["onClick"],uo=["onClick"],co={class:"text-danger"},po={class:"flex flex-col items-center my-5"},mo={key:0,class:"text-gray-700 font-semibold"};function go(e,s,I,V,M,b){var S,h;const c=u("SvgIcon"),f=u("el-icon"),w=u("el-input"),y=u("el-form-item"),g=u("el-form"),d=u("el-empty"),E=u("TableHeader"),$=u("router-link"),p=u("TablePagination"),D=u("CustomerModal");return a(),r(K,null,[o("div",qe,[o("div",Oe,[s[7]||(s[7]=o("h1",{class:"text-gray-900 text-lg font-bold"},"Customer",-1)),o("div",je,[m(g,{onSubmit:he(e.searchCustomer,["prevent"])},{default:P(()=>[m(y,{class:"mb-0"},{default:P(()=>[m(w,{class:"w-250px",placeholder:"Search",modelValue:e.search,"onUpdate:modelValue":s[0]||(s[0]=l=>e.search=l),name:"search",size:"large"},{prefix:P(()=>[m(f,{class:"el-input__icon"},{default:P(()=>[o("span",Ye,[m(c,{icon:"searchIcon"})])]),_:1})]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["onSubmit"])]),o("div",ze,[e.checkedRows.length!==0&&e.isAdmin()?(a(),r("button",{key:0,class:"bg-danger text-white rounded-md px-4 py-2 font-semibold",onClick:s[1]||(s[1]=(...l)=>e.onRemove&&e.onRemove(...l))}," Remove ")):U("",!0),e.isAdmin()?(a(),r("button",{key:1,class:"bg-primary text-white rounded-md px-4 py-2 font-semibold flex flex-row items-center gap-2 justify-between",onClick:s[2]||(s[2]=(...l)=>e.toggleNewCustomer&&e.toggleNewCustomer(...l))},[m(c,{icon:"addIcon"}),s[6]||(s[6]=Y(" New "))])):U("",!0)])]),e.loading?(a(),r("div",Ke,s[8]||(s[8]=[o("div",{class:"spinner-border text-primary",role:"status"},[o("span",{class:"sr-only"},"Loading...")],-1)]))):e.customerList.length===0?(a(),z(d,{key:1,description:"No Data"})):(a(),r("div",Je,[o("table",null,[o("thead",Ze,[o("tr",Ge,[m(E,{headers:e.tableHeader,sortBy:e.sortParams.sortBy,sortDirection:e.sortParams.sortDirection,onSort:e.onSort},{customHeader:P(({header:l})=>[l.label===""?(a(),r("div",Qe,[o("div",We,[W(o("input",{class:"h-4 w-4",type:"checkbox","onUpdate:modelValue":s[3]||(s[3]=_=>e.checkAll=_),onChange:s[4]||(s[4]=(..._)=>e.onToggleCheckAll&&e.onToggleCheckAll(..._))},null,544),[[X,e.checkAll]])])])):(a(),r("div",Xe,A(l.label),1))]),_:1},8,["headers","sortBy","sortDirection","onSort"])])]),o("tbody",null,[(a(!0),r(K,null,se(e.customerList,l=>(a(),r("tr",{key:l.id,class:"font-bold text-gray-400 my-2 text-center border-b-[1px] border-grey-400 border-dashed"},[e.isAdmin()?(a(),r("td",eo,[W(o("input",{class:"h-4 w-4",type:"checkbox",value:l.id,"onUpdate:modelValue":s[5]||(s[5]=_=>e.checkedRows=_)},null,8,oo),[[X,e.checkedRows]])])):U("",!0),o("td",so,[o("div",to,[m($,{to:e.getCustomerDetailPath(l.id),class:"text-dark font-semibold hover:text-primary"},{default:P(()=>[Y(A(l==null?void 0:l.customerName),1)]),_:2},1032,["to"])])]),o("td",null,[o("span",lo,A(l==null?void 0:l.notes),1)]),o("td",null,[o("span",no,A(e.formatDate(l==null?void 0:l.createdAt,"MMM DD, YYYY")),1)]),e.isAdmin()?(a(),r("td",ao,[o("div",ro,[o("button",{class:"bg-grey-300 h-8 w-8 rounded-lg px-1.5 py-0.5",onClick:_=>e.toggleEditCustomer(l==null?void 0:l.id)},[m(c,{icon:"newReportIcon"})],8,io),o("button",{class:"bg-grey-300 h-8 w-8 rounded-lg px-2 py-0.5",onClick:_=>e.deleteCustomer(l==null?void 0:l.id)},[o("span",co,[m(c,{icon:"trashIcon"})])],8,uo)])])):U("",!0)]))),128))])]),o("div",po,[(S=e.customerList)!=null&&S.length?(a(),r("div",mo,A(`Showing ${(e.currentPage-1)*10+1} to ${(h=e.customerList)==null?void 0:h.length} of ${e.totalElements} entries`),1)):U("",!0),e.pageCount>=1?(a(),z(p,{key:1,"total-pages":e.pageCount,total:e.totalElements,"per-page":10,"current-page":e.currentPage,onPageChange:e.pageChange},null,8,["total-pages","total","current-page","onPageChange"])):U("",!0)])]))]),m(D,{isVisible:e.isModalVisible,close:e.toggleNewCustomer,ref:"customerModal",loadPage:e.getCompanyCustomer},null,8,["isVisible","close","loadPage"])],64)}const yo=oe(He,[["render",go]]),vo=ee({name:"user-modal",components:{SvgIcon:me},props:{isVisible:{type:Boolean,required:!0},close:{type:Function,required:!0},loadPage:{type:Function,required:!1},companyId:{type:String,required:!0}},setup(e){const s=Ae(),I=n(!1),V=n([]),M=n(""),b=n(""),c=n([]),f=n([]),w=n(),y=Ne().shape({email:Re().email("Invalid email address").required("Email is required").test("is-unique","Email is already taken",h=>!c.value.includes(h))});O(()=>e.isVisible,h=>{h===!1&&D()});const g=h=>{c.value.splice(c.value.indexOf(h),1),e.close()},d=()=>{e.close()},E=()=>{Ve(()=>{w.value.input.focus()})},$=()=>{b.value&&p(b.value)},p=h=>{M.value="",y.validate({email:h}).then(()=>{c.value.push(h),b.value=""}).catch(l=>{var _;M.value=((_=l==null?void 0:l.errors)==null?void 0:_[0])||"Invalid email address"})},D=()=>{c.value=[],f.value=[],M.value="",b.value=""};return{roles:f,loading:I,rolesOptions:Ue,options:V,inputValue:b,emails:c,InputRef:w,inputEmailError:M,sendInvitation:async()=>{if(!c.value.length){M.value="Please type Email";return}I.value=!0,s.inviteUsers({params:{emails:c.value,userRoles:f.value,companyId:e==null?void 0:e.companyId},callback:{onSuccess:h=>{e.close(),j.toast("Invited successfully","success","top-right")},onFinish:()=>{I.value=!1}}})},handleClose:g,showInput:E,handleInputConfirm:$,reset:D,closeModal:d}}}),fo={key:0,class:"fixed top-0 right-0 bottom-0 left-0 flex flex-row items-center bg-dark z-40"},ho={class:"bg-white h-auto w-11/12 flex flex-col items-center gap-3 mx-auto p-5 rounded-xl"},bo={class:"h-auto w-full flex flex-row items-center justify-between"},Co={class:"h-auto w-full flex flex-col gap-4"},ko={class:"flex flex-col gap-2"},wo={class:"flex flex-col gap-2"},$o={class:"text-end mt-4"},_o=["disabled"],Ao={key:0,class:"indicator-label"},Io={key:1,class:"indicator-progress"};function So(e,s,I,V,M,b){const c=u("SvgIcon"),f=u("el-tag"),w=u("el-input"),y=u("el-text"),g=u("el-select-v2");return e.isVisible?(a(),r("div",fo,[o("div",ho,[o("div",bo,[s[4]||(s[4]=o("h3",{class:"text-lg font-bold text-dark"},"Send Invitation",-1)),o("span",{class:"cursor-pointer",onClick:s[0]||(s[0]=(...d)=>e.closeModal&&e.closeModal(...d))},[m(c,{icon:"closeModalIcon"})])]),s[8]||(s[8]=o("p",{class:"text-gray-600 text-xs text-center"}," An email will be sent out to the employees email that you input. Your employee will need to access to the link to create their account to use the Muddy Software under your company ",-1)),o("div",Co,[o("div",ko,[s[5]||(s[5]=o("label",{class:"font-semibold"},"Emails",-1)),(a(!0),r(K,null,se(e.emails,d=>(a(),z(f,{size:"large",key:d,closable:"",class:"me-2","disable-transitions":!1,onClose:E=>e.handleClose(d)},{default:P(()=>[Y(A(d),1)]),_:2},1032,["onClose"]))),128)),m(w,{ref:"InputRef",modelValue:e.inputValue,"onUpdate:modelValue":s[1]||(s[1]=d=>e.inputValue=d),size:"large",placeholder:"Enter email",onKeyup:Ee(e.handleInputConfirm,["enter"]),onBlur:e.handleInputConfirm},null,8,["modelValue","onKeyup","onBlur"]),m(y,{class:"mx-1 align-self-start",type:"danger"},{default:P(()=>[Y(A(e.inputEmailError),1)]),_:1})]),o("div",wo,[s[6]||(s[6]=o("label",{class:"font-semibold"},"Roles",-1)),m(g,{size:"large",modelValue:e.roles,"onUpdate:modelValue":s[2]||(s[2]=d=>e.roles=d),options:e.rolesOptions,placeholder:"Roles",multiple:"",clearable:"",name:"roles"},null,8,["modelValue","options"])])]),o("div",$o,[o("button",{class:"bg-primary text-white rounded-md px-4 py-2 font-semibold",type:"button",disabled:e.loading,onClick:s[3]||(s[3]=(...d)=>e.sendInvitation&&e.sendInvitation(...d))},[e.loading?U("",!0):(a(),r("span",Ao," Send Invitation ")),e.loading?(a(),r("span",Io,s[7]||(s[7]=[Y(" Please wait... "),o("span",{class:"spinner-border spinner-border-sm align-middle ms-2"},null,-1)]))):U("",!0)],8,_o)])])])):U("",!0)}const Mo=oe(vo,[["render",So]]),de=[{value:F.CompanyAdmin,key:"Admins",titleModal:"Add Company Admins",initialAddUserValue:{roles:[F.CompanyAdmin]}},{value:F.Supervisor,key:"Supervisors",titleModal:"Add Company Supervisors",initialAddUserValue:{roles:[F.Supervisor]}},{value:F.Engineer,key:"Engineers",titleModal:"Add Company Engineers",initialAddUserValue:{roles:[F.Engineer]}},{value:5,key:be.Customer,titleModal:"Add Customer"}],Uo=ee({name:"user-list",components:{PageHeader:Ce,SvgIcon:me,TablePagination:ke,AssignUserModal:Fe,TableHeader:we},props:{listType:{type:Number,required:!0}},setup(e){const s=ve(),I=Be(),V=Ae(),M=pe(),b=n([]),c=n(!1),f=n(0),w=n(0),y=n(1),g=n(!1),d=n(!1),E=n(""),$=n(null),p=n(!1),D=n([]),S=n(de.find(i=>i.value===e.listType)),h=n([]),l=s.name==="my-company",_=n({sortDirection:$e.ASC,sortBy:Z.Name}),t=n(!1),N=[{label:"",class:"w-25px",display:q()},{label:"FULL NAME",sortBy:Z.Name,class:"min-w-150px"},{label:"MOBILE NUMBER",class:"min-w-120px"},{label:"OFFICE NUMBER",class:"min-w-120px"},{label:"ADDED DATE",sortBy:Z.AssignedDate,class:"min-w-120px"},{label:"ACTIONS",class:"min-w-60px",display:q()}];fe(()=>{R(),G()});const te=De(()=>{var i,C,T,k,H;return{companyId:l?(i=J.getUserInfo())==null?void 0:i.companyId:(T=(C=s.params)==null?void 0:C.id)==null?void 0:T.toString(),userRoles:((H=(k=S==null?void 0:S.value)==null?void 0:k.initialAddUserValue)==null?void 0:H.roles)||[]}});O(e,i=>{S.value=de.find(C=>C.value===i.listType),b.value=[],E.value="",y.value=1,R()}),O(b,i=>{c.value=h.value.length!==0&&i.length===h.value.length}),O(y,()=>{R()});const R=async()=>{var i,C,T;g.value=!0,I.getCompanyUsers({params:{name:E.value.trim()||null,companyId:l?(i=J.getUserInfo())==null?void 0:i.companyId:(T=(C=s.params)==null?void 0:C.id)==null?void 0:T.toString(),role:e.listType,page:y.value,limit:10,..._.value},callback:{onSuccess:k=>{h.value=[...k==null?void 0:k.items],f.value=k==null?void 0:k.totalPage,w.value=k==null?void 0:k.total,y.value=k==null?void 0:k.page},onFinish:()=>{g.value=!1}}})},le=i=>{_.value={...i},R()},G=async(i="")=>{var C,T,k;p.value=!0,V.getUsers({params:{name:i.trim()||null,companyId:l?(C=J.getUserInfo())==null?void 0:C.companyId:(k=(T=s.params)==null?void 0:T.id)==null?void 0:k.toString(),page:1,limit:500},callback:{onSuccess:H=>{D.value=[...H==null?void 0:H.items]},onFinish:()=>{p.value=!1}}})},ne=()=>{t.value=!t.value},ae=()=>{t.value=!t.value},re=i=>{y.value=i},ie=()=>{d.value=!d.value},Q=i=>{var C;(C=i==null?void 0:i.target)!=null&&C.checked?b.value=h.value.map(T=>T.id):b.value=[]},v=i=>{M.push({path:`/users/${i}`})},L=i=>{var C;Me(i),(C=$==null?void 0:$.value)==null||C.closeModal()},B=i=>{G(i)},x=()=>{y.value!==1?y.value=1:R()},Ie=()=>{j.deletionAlert({onConfirmed:()=>{ge(b.value,!0)}})},Se=i=>{j.deletionAlert({onConfirmed:()=>{ge([i])}})},ge=async(i,C=!1)=>{g.value=!0,I.removeUserListOfCompany({params:{userIds:i,role:e.listType},callback:{onSuccess:T=>{C&&(b.value=[]),R()},onFinish:()=>{g.value=!1}}})},Me=async i=>{var C,T,k;g.value=!0,I.addUserListToCompany({params:{companyId:l?(C=J.getUserInfo())==null?void 0:C.companyId:(k=(T=s.params)==null?void 0:T.id)==null?void 0:k.toString(),role:e.listType,userIds:i},callback:{onSuccess:H=>{R(),j.toast("Added successfully","success","top-right")},onFinish:()=>{g.value=!1}}})};return{sortParams:_,tableHeader:N,search:E,loading:g,checkedRows:b,checkAll:c,userList:h,currentPage:y,totalElements:w,pageCount:f,isShowModal:d,addUserModal:$,userListForAddUserModal:D,tabs:de,currentTab:S,initialAddUserValue:te,loadingUserListForAddUserModal:p,isModalVisible:t,onSort:le,pageChange:re,toggleFilter:ie,deleteUser:Se,toggleNewUser:ne,formatDate:_e,view:v,onAdd:L,onSearchForAddUserModal:B,onRemove:Ie,isAdmin:q,onToggleCheckAll:Q,searchUser:x,toggleModal:ae}}}),Vo={class:"bg-white rounded-lg p-4"},Eo={class:"h-auto w-full flex flex-col gap-2 items-start"},Do={class:"text-gray-900 text-lg font-bold"},xo={class:"h-auto w-full"},To={class:"h-auto w-full flex flex-row justify-end gap-4"},Po={key:0,class:"text-center p-5"},Lo={key:2,class:"h-auto w-full overflow-x-scroll p-4"},No={class:"font-bold text-gray-400 whitespace-nowrap"},Ro={key:0},Fo={key:1,class:"p-4"},Bo={key:0},Ho=["value"],qo={class:"w-36 p-4"},Oo={class:"flex flex-row items-center gap-3"},jo=["src"],Yo={class:"flex flex-col text-start truncate"},zo={class:"text-gray-400 font-semibold"},Ko={class:"text-grey-600 font-semibold"},Jo={class:"text-grey-600 font-semibold"},Zo={class:"text-grey-600 font-semibold"},Go={key:1},Qo={class:"h-auto w-full flex flex-row items-center justify-evenly gap-2"},Wo=["onClick"],Xo=["onClick"],es={class:"text-danger"},os={class:"flex flex-col items-center my-5"},ss={key:0,class:"text-gray-700 font-semibold"};function ts(e,s,I,V,M,b){var S,h,l,_;const c=u("SvgIcon"),f=u("el-icon"),w=u("el-input"),y=u("el-form-item"),g=u("el-form"),d=u("el-empty"),E=u("TableHeader"),$=u("router-link"),p=u("TablePagination"),D=u("AssignUserModal");return a(),r(K,null,[o("div",Vo,[o("div",Eo,[o("h1",Do,A(`Company ${(S=e.currentTab)==null?void 0:S.key}`),1),o("div",xo,[m(g,{onSubmit:he(e.searchUser,["prevent"])},{default:P(()=>[m(y,{class:"mb-0"},{default:P(()=>[m(w,{placeholder:"Search",modelValue:e.search,"onUpdate:modelValue":s[0]||(s[0]=t=>e.search=t),name:"search",size:"large"},{prefix:P(()=>[m(f,{class:"el-input__icon"},{default:P(()=>[m(c,{icon:"searchIcon"})]),_:1})]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["onSubmit"])]),o("div",To,[e.checkedRows.length!==0&&e.isAdmin()?(a(),r("button",{key:0,class:"bg-danger text-white rounded-md px-4 py-2 font-semibold",onClick:s[1]||(s[1]=(...t)=>e.onRemove&&e.onRemove(...t))}," Remove ")):U("",!0),e.isAdmin()?(a(),r("button",{key:1,class:"bg-primary text-white rounded-md px-4 py-2 font-semibold",onClick:s[2]||(s[2]=(...t)=>e.toggleNewUser&&e.toggleNewUser(...t))}," Add ")):U("",!0)])]),e.loading?(a(),r("div",Po,s[6]||(s[6]=[o("div",{class:"spinner-border text-primary",role:"status"},[o("span",{class:"sr-only"},"Loading...")],-1)]))):e.userList.length===0?(a(),z(d,{key:1,description:"No Data"})):(a(),r("div",Lo,[o("table",null,[o("thead",null,[o("tr",No,[m(E,{headers:e.tableHeader,sortBy:e.sortParams.sortBy,sortDirection:e.sortParams.sortDirection,onSort:e.onSort},{customHeader:P(({header:t})=>[t.label===""?(a(),r("div",Ro,[W(o("input",{class:"h-4 w-4",type:"checkbox","onUpdate:modelValue":s[3]||(s[3]=N=>e.checkAll=N),onChange:s[4]||(s[4]=(...N)=>e.onToggleCheckAll&&e.onToggleCheckAll(...N))},null,544),[[X,e.checkAll]])])):(a(),r("div",Fo,A(t.label),1))]),_:1},8,["headers","sortBy","sortDirection","onSort"])])]),o("tbody",null,[(a(!0),r(K,null,se(e.userList,t=>(a(),r("tr",{key:t.id,class:"font-bold text-gray-400 my-2 text-center border-b-[1px] border-grey-400 border-dashed"},[e.isAdmin()?(a(),r("td",Bo,[W(o("input",{class:"h-4 w-4",type:"checkbox",value:t.id,"onUpdate:modelValue":s[5]||(s[5]=N=>e.checkedRows=N)},null,8,Ho),[[X,e.checkedRows]])])):U("",!0),o("td",qo,[o("div",Oo,[o("img",{class:"h-11 w-11 rounded-full",src:(t==null?void 0:t.avatar)||"/media/avatars/blank.png",alt:""},null,8,jo),o("div",Yo,[m($,{to:`/users/${t==null?void 0:t.id}`,class:"text-dark font-semi hover:text-primary"},{default:P(()=>[Y(A(`${t==null?void 0:t.firstName} ${t==null?void 0:t.lastName}`),1)]),_:2},1032,["to"]),o("span",zo,A(t==null?void 0:t.email),1)])])]),o("td",null,[o("span",Ko,A(t==null?void 0:t.mobilePhone),1)]),o("td",null,[o("span",Jo,A(t==null?void 0:t.officePhone),1)]),o("td",null,[o("span",Zo,A(e.formatDate(t==null?void 0:t.assignedDate,"MMM DD, YYYY")),1)]),e.isAdmin()?(a(),r("td",Go,[o("div",Qo,[o("button",{class:"bg-grey-300 h-8 w-8 rounded-lg px-1.5 py-0.5",onClick:N=>e.view((t==null?void 0:t.id)??""),"item.addedDate":""},[m(c,{icon:"eyeIcon"})],8,Wo),o("button",{class:"bg-grey-300 h-8 w-8 rounded-lg px-2 py-0.5",onClick:N=>e.deleteUser(t==null?void 0:t.id)},[o("span",es,[m(c,{icon:"trashIcon"})])],8,Xo)])])):U("",!0)]))),128))])])])),o("div",os,[(h=e.userList)!=null&&h.length?(a(),r("div",ss,A(`Showing ${(e.currentPage-1)*10+1} to ${(l=e.userList)==null?void 0:l.length} of ${e.totalElements} entries`),1)):U("",!0),e.pageCount>=1?(a(),z(p,{key:1,"total-pages":e.pageCount,total:e.totalElements,"per-page":10,"current-page":e.currentPage,onPageChange:e.pageChange},null,8,["total-pages","total","current-page","onPageChange"])):U("",!0)])]),m(D,{ref:"addUserModal",title:((_=e.currentTab)==null?void 0:_.titleModal)||"",userList:e.userListForAddUserModal,onAdd:e.onAdd,onSearch:e.onSearchForAddUserModal,initialAddUserValue:e.initialAddUserValue,loadingSearch:e.loadingUserListForAddUserModal,isVisible:e.isModalVisible,close:e.toggleModal},null,8,["title","userList","onAdd","onSearch","initialAddUserValue","loadingSearch","isVisible","close"])],64)}const ls=oe(Uo,[["render",ts]]),ye=[{value:F.CompanyAdmin,key:"Administration"},{value:F.Supervisor,key:"Supervisors"},{value:F.Engineer,key:"Engineers"},{value:be.Customer,key:"Customer"}],ue=n(!1),ce=n(!1),ns=ee({name:"company-detail",components:{UserList:ls,CompanyModal:Te,InvitationModal:Mo,CustomerList:yo},props:{companyDetail:{type:Object,required:!0},reloadCompanyData:{type:Function,required:!0}},setup(e){const s=pe(),I=n(null),V=n(null),M=n(ye[0].value);return{companyModal:I,invitationModal:V,tabs:ye,tabIndex:M,isCompanyModalVisible:ue,isInvitationModalVisible:ce,back:()=>{s.go(-1)},setActiveTab:y=>{const g=y.target;M.value=Number(g.getAttribute("data-tab-index"))},toggleEditCompany:()=>{var y,g;(g=I==null?void 0:I.value)==null||g.setId((y=e==null?void 0:e.companyDetail)==null?void 0:y.id),ue.value=!ue.value},toggleInvitation:()=>{ce.value=!ce.value},isAdmin:q}}}),as={class:"h-auto w-11/12 overflow-y-scroll mx-auto my-4 flex flex-col items-center gap-3"},rs={class:"bg-white rounded-xl h-auto w-full p-4"},is={class:"h-auto w-full py-4 flex flex-row items-center justify-start gap-3 overflow-y-scroll"},ds=["data-tab-index"],us={class:"pb-4 flex flex-col gap-2 items-start border-b-2 border-solid border-grey-300"},cs={key:0,class:"h-auto w-full flex flex-row justify-between items-center"},ps={class:"p-4 text-sm"},ms={class:"flex flex-col gap-2"},gs={class:"text-gray-400 hover:text-primary"},ys={class:"mb-0"},vs={class:"text-gray-400 hover:text-primary"},fs={class:"text-gray-400 hover:text-primary"},hs={key:0,class:"h-auto w-full"},bs={key:1,class:"h-auto w-full"};function Cs(e,s,I,V,M,b){var g,d,E,$;const c=u("UserList"),f=u("CustomerList"),w=u("CompanyModal"),y=u("InvitationModal");return a(),r("div",as,[o("div",rs,[o("ul",is,[(a(!0),r(K,null,se(e.tabs,p=>(a(),r("li",{class:"nav-item",key:p.value},[o("div",{class:xe(["cursor-pointer font-semibold hover:text-primary hover:border-b-2 hover:border-primary",{active:e.tabIndex===p.value,"text-primary border-b-2 border-primary":e.tabIndex===(p==null?void 0:p.value),"text-grey-400":e.tabIndex!==(p==null?void 0:p.value)}]),onClick:s[0]||(s[0]=D=>e.setActiveTab(D)),"data-tab-index":p.value,role:"tab"},A(p.key),11,ds)]))),128))]),o("div",us,[s[4]||(s[4]=o("h1",{class:"font-bold text-lg text-gray-900"},"Company Overview",-1)),e.isAdmin()?(a(),r("div",cs,[o("button",{class:"bg-success text-white rounded-md px-4 py-2 font-semibold",onClick:s[1]||(s[1]=(...p)=>e.toggleInvitation&&e.toggleInvitation(...p))}," Send Invitation "),o("button",{class:"bg-primary text-white rounded-md px-4 py-2 font-semibold",onClick:s[2]||(s[2]=(...p)=>e.toggleEditCompany&&e.toggleEditCompany(...p))}," Edit "),o("button",{type:"button",class:"bg-grey-300 text-gray-700 rounded-md px-4 py-2 font-semibold",onClick:s[3]||(s[3]=(...p)=>e.back&&e.back(...p))}," Back ")])):U("",!0)]),o("div",ps,[o("div",ms,[o("div",gs,[s[5]||(s[5]=o("h4",{class:"text-gray-800 text-md font-bold"},"Company ID",-1)),o("p",ys,A(((g=e.companyDetail)==null?void 0:g.id)||""),1)]),o("div",null,[s[6]||(s[6]=o("h4",{class:"text-gray-800 text-md font-bold"},"Company Name",-1)),o("p",vs,A(((d=e.companyDetail)==null?void 0:d.name)||""),1)]),o("div",null,[s[7]||(s[7]=o("h4",{class:"text-gray-800 text-md font-bold"},"Description",-1)),o("p",fs,A(((E=e.companyDetail)==null?void 0:E.description)||""),1)])])])]),e.tabIndex!==e.tabs[3].value?(a(),r("div",hs,[m(c,{listType:e.tabIndex},null,8,["listType"])])):(a(),r("div",bs,[m(f)])),m(w,{isVisible:e.isCompanyModalVisible,close:e.toggleEditCompany,ref:"companyModal",loadPage:e.reloadCompanyData},null,8,["isVisible","close","loadPage"]),($=e.companyDetail)!=null&&$.id?(a(),z(y,{key:2,isVisible:e.isInvitationModalVisible,close:e.toggleInvitation,ref:"invitationModal",companyId:e.companyDetail.id},null,8,["isVisible","close","companyId"])):U("",!0)])}const Ps=oe(ns,[["render",Cs]]);export{Ps as C};
