<template>
  <div v-if="loading" class="text-center my-auto">
    <div class="spinner-border text-primary" role="status">
      <span class="sr-only">Loading...</span>
    </div>
  </div>
  <PageHeader
    title="Companies"
    :breadcrumbs="['Companies', companyDetail?.name || '']"
  />
  <CompanyDetail
    v-if="!loading && companyDetail"
    :companyDetail="companyDetail"
    :reloadCompanyData="getCompanyDetails"
  >
  </CompanyDetail>
</template>

<script lang="ts">
import PageHeader from "@/components/PageHeader.vue";
import { useCompanyStore } from "@/stores/company";
import { defineComponent, onMounted, ref } from "vue";
import { useRoute } from "vue-router";
import CompanyDetail from "./CompanyDetail.vue";

export default defineComponent({
  name: "company-overview",
  components: {
    CompanyDetail,
    PageHeader,
  },
  setup() {
    const loading = ref<boolean>(false);
    const companyStore = useCompanyStore();
    const route = useRoute();
    const companyDetail = ref<Company.Info>();
    const companyId = route.params?.id?.toString() || "";

    onMounted(() => {
      if (companyId) {
        getCompanyDetails();
      }
    });

    const getCompanyDetails = async (): Promise<void> => {
      loading.value = true;
      companyStore.getCompanyById({
        id: companyId,
        callback: {
          onSuccess: (res: any) => {
            companyDetail.value = res;
          },
          onFinish: () => {
            loading.value = false;
          },
        },
      });
    };

    return {
      loading,
      companyId,
      companyDetail,
      getCompanyDetails,
    };
  },
});
</script>
