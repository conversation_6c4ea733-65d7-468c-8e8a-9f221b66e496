import{d as i,e as a,_ as c,c as d,o as p,a as s,t as n}from"./index-BmHWvWFS.js";const l=i({name:"success-template",components:{},props:{description:{type:String,required:!1,default:""},message:{type:String,required:!0},btnTitle:{type:String,required:!1,default:"Go To Login"},btnPath:{type:String,required:!1,default:"/sign-in"}},setup(e){const t=a();return{goToSignIn:()=>{t.push({path:e.btnPath})}}}}),u="/media/success.svg",g={class:"w-80 mx-auto flex flex-col justify-center items-center"},m={class:"text-dark text-center mb-3"},f={class:"text-gray-400 text-center font-semibold"},b={class:"indicator-label"};function S(e,t,o,y,_,h){return p(),d("div",g,[t[1]||(t[1]=s("img",{src:u,width:170},null,-1)),s("h1",m,n(e.message),1),s("div",f,n(e.description),1),s("button",{type:"button",class:"mt-12 px-3 py-2 rounded-md bg-primary font-bold text-white",onClick:t[0]||(t[0]=(...r)=>e.goToSignIn&&e.goToSignIn(...r))},[s("span",b,n(e.btnTitle),1)])])}const x=c(l,[["render",S]]);export{x as S};
