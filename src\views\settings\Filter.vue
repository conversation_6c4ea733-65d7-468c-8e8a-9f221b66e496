<template>
  <div
    class="bg-minicard-background text-minicard-text-light max-h-11/12 w-11/12 flex flex-col items-center gap-3 mx-auto p-5 rounded-xl shadow-sm lg:w-2/5 lg:h-3/5"
  >
    <div class="h-auto w-full flex flex-col">
      <h5 class="h-auto w-full font-semibold self-start">Filter</h5>

      <div class="h-auto w-full flex flex-row gap-2 justify-center">
        <button
          class="bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover rounded-md px-4 py-2 font-semibold"
          @click="resetFilter"
        >
          Reset
        </button>
        <button
          class="bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover rounded-md px-4 py-2 font-semibold"
          @click="hideFilter"
        >
          Close
        </button>
        <button
          class="bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover rounded-md px-4 py-2 font-semibold"
          type="button"
          @click="apply"
        >
          Apply
        </button>
      </div>
    </div>
    <div class="h-auto w-full flex flex-col gap-3">
      <el-form :model="filterForm" @submit.prevent="apply">
        <div class="flex flex-col gap-2">
          <label class="font-semibold">Price from</label>
          <el-form-item prop="priceFrom">
            <el-input
              type="number"
              :controls="false"
              step="any"
              v-model="filterForm.priceFrom"
              placeholder="Price from"
              name="priceFrom"
            />
          </el-form-item>
        </div>
        <div class="flex flex-col gap-2">
          <label class="font-semibold">Price to</label>
          <el-form-item prop="priceTo">
            <el-input
              type="number"
              :controls="false"
              step="any"
              v-model="filterForm.priceTo"
              placeholder="Price to"
              name="priceTo"
            />
          </el-form-item>
        </div>
        <div v-if="isSystemAdmin()" class="flex flex-col gap-2">
          <label class="font-semibold"> Company </label>
          <el-form-item prop="companyId">
            <el-select-v2
              :options="companyOptions"
              placeholder="Select Company"
              name="companyId"
              v-model="filterForm.companyId"
            />
          </el-form-item>
        </div>
        <!-- <button class="btn btn-sm btn-primary d-none" type="submit"></button> -->
      </el-form>
    </div>
  </div>
</template>

<script lang="ts">
import { UserType } from "@/constants/user";
import JwtService, { isSystemAdmin } from "@/services/JwtService";
import { useCompanyStore } from "@/stores/company";
import { defineComponent, onMounted, ref } from "vue";

const allCompanyOption = { value: "", label: "All" };

export default defineComponent({
  name: "settings-filter",
  components: {},
  props: {
    hideFilter: {
      type: Function,
      required: false,
      default: () => {},
    },
    onFilter: {
      type: Function,
      required: true,
    },
  },
  setup(props) {
    const companyStore = useCompanyStore();
    const companyOptions = ref([allCompanyOption]);
    const filterForm = ref<Filter.FilterForm>({
      companyId: "",
    });

    onMounted(() => {
      if (isSystemAdmin()) {
        getCompanies();
      }
    });

    const getCompanies = async (): Promise<void> => {
      companyStore.getCompanies({
        params: {
          page: 1,
          limit: 500,
        },
        callback: {
          onSuccess: (res: any) => {
            const list = res?.items.map((item: any) => {
              return {
                label: item?.name,
                value: item?.id,
              };
            });
            companyOptions.value = [allCompanyOption, ...list];
          },
        },
      });
    };

    const hideFilter = () => {
      if (props?.hideFilter) {
        props?.hideFilter();
      }
    };

    const resetFilter = () => {
      filterForm.value = {
        companyId: "",
      };

      apply();
    };

    const apply = () => {
      props.onFilter({
        priceFrom: Number(filterForm.value?.priceFrom) || null,
        priceTo: Number(filterForm.value?.priceTo) || null,
        companyId: isSystemAdmin()
          ? filterForm.value?.companyId || null
          : JwtService?.getUserInfo()?.companyId,
      });
    };

    return {
      UserType,
      filterForm,
      companyOptions,
      isSystemAdmin,
      apply,
      hideFilter,
      resetFilter,
    };
  },
});
</script>
