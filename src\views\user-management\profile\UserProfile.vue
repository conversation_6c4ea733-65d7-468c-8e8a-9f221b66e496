<template>
  <div v-if="loading" class="text-center my-auto">
    <div class="spinner-border text-primary" role="status">
      <span class="sr-only">Loading...</span>
    </div>
  </div>
  <PageHeader
    title="User Profile"
    :breadcrumbs="breadcrumbs"
    :reloadUserData="getUserProfile"
  />
  <UserDetail
    v-if="!loading && userDetail"
    :userDetail="userDetail"
    :reloadUserData="getUserProfile"
  />
</template>

<script lang="ts">
import PageHeader from "@/components/PageHeader.vue";
import JwtService from "@/services/JwtService";
import { useUserStore } from "@/stores/user";
import { defineComponent, inject, onMounted, ref, watch } from "vue";
import { useRoute } from "vue-router";
import UserDetail from "./UserDetail.vue";
import type { Provide } from "@/types/injection-types";

export default defineComponent({
  name: "my-profile",
  components: {
    UserDetail,
    PageHeader,
  },
  setup() {
    const userStore = useUserStore();
    const route = useRoute();
    const loading = ref<boolean>(false);
    const userId = ref(route.params.id);
    const userDetail = ref<User.Info>();
    const userInfoProvide = inject<Provide.UserInfo>("userInfo");
    const breadcrumbs = ["User Management", "User Profile"];

    onMounted(() => {
      if (userId) {
        getUserProfile();
        //get user detail by userId
      }
    });

    watch(
      () => route.params.id,
      (newId) => {
        userId.value = newId;
        newId && getUserProfile();
      }
    );

    const getUserProfile = async (): Promise<void> => {
      if (!userId) return;
      loading.value = true;
      userStore.getUserProfile({
        id: userId.value as string,
        callback: {
          onSuccess: (res: User.Info) => {
            userDetail.value = res;
            if (JwtService?.getUserInfo()?.id === res?.id) {
              userInfoProvide?.updateUserInfo({ ...res });
            }
          },
          onFinish: () => {
            loading.value = false;
          },
        },
      });
    };

    return {
      loading,
      userDetail,
      breadcrumbs,
      getUserProfile,
    };
  },
});
</script>
