export const numberWithCommas = (number: number) => {
  if (typeof number !== "number") return "";
  const parts = (Math.round((number + Number.EPSILON) * 100) / 100)
    .toString()
    .split(".");
  const integerPart = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, ",");

  if (parts.length === 2) {
    const decimalPart = parts[1];
    return `${integerPart}.${decimalPart}`;
  }

  return integerPart;
};
