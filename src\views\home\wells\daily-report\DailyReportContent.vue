<template>
  <PageHeader title="Daily Report General" :breadcrumbs="breadcrumbs" />
  <div class="p-4">
    <!--begin::Actions-->
    <ul role="tablist" class="flex flex-wrap items-center justify-start gap-2">
      <li class="nav-item" v-for="item in dailyTabs" :key="item?.value">
        <div
          class="whitespace-nowrap cursor-pointer font-semibold hover:text-active-hover hover:border-b-2 hover:border-active-border-hover"
          :class="{
            active: tabIndex === item?.value,
            'text-active-dark border-b-2 border-active-border-dark':
              tabIndex === item?.value,
            'text-inactive border-b-2 border-inactive-border':
              tabIndex !== item?.value,
          }"
          @click="setActiveTab($event)"
          :data-tab-index="item?.value"
          role="tab"
        >
          {{ item?.label }}
        </div>
      </li>
    </ul>
  </div>

  <div v-if="loading" class="text-center mt-7">
    <div class="spinner-border text-primary" role="status">
      <span class="sr-only">Loading...</span>
    </div>
  </div>

  <div v-else>
    <component
      :is="currentComponent"
      :setChildActiveTab="setChildActiveTab"
      ref="currentTab"
    />
  </div>
  
</template>

<script lang="ts">
import PageHeader from "@/components/PageHeader.vue";
import SvgIcon from "@/constants/SvgIcon.vue";
import { EDailyTab, dailyTabs } from "@/constants/daily-report";
import { UserType } from "@/constants/user";
import { formatDate, isValidDate } from "@/utils/date";
import JwtService from "@/services/JwtService";

import {
  computed,
  defineComponent,
  inject,
  onMounted,
  onUnmounted,
  ref,
} from "vue";
import { useRoute } from "vue-router";
import Costs from "./costs/Costs.vue";
import General from "./general/General.vue";
import MudProperties from "./mud-properties/MudProperties.vue";
import Notes from "./notes/Notes.vue";
import ProductPackage from "./product-package/ProductPackage.vue";
import SiteEquipment from "./site-equipment/SiteEquipment.vue";
import Tasks from "./tasks/Tasks.vue";
import VolumeTracking from "./volume-tracking/VolumeTracking.vue";
import AlertService from "@/services/AlertService";
import type { Provide } from "@/types/injection-types";

const breadcrumbs = ["Home", "Daily Report"];

interface TabComponent {
  isFormOfChildTabDirty?: () => boolean;
}

const tabComponentMap = {
  [EDailyTab.General]: General,
  [EDailyTab.MudProperties]: MudProperties,
  [EDailyTab.SiteEquipment]: SiteEquipment,
  [EDailyTab.Tasks]: Tasks,
  [EDailyTab.Product]: ProductPackage,
  [EDailyTab.VolumeTracking]: VolumeTracking,
  [EDailyTab.Costs]: Costs,
  [EDailyTab.Notes]: Notes,
};

export default defineComponent({
  name: "daily-report",
  components: {
    SvgIcon,
    General,
    MudProperties,
    SiteEquipment,
    Tasks,
    ProductPackage,
    VolumeTracking,
    Costs,
    Notes,
    PageHeader,
  },
  setup() {
    const route = useRoute();
    const tabIndex = ref<number>(EDailyTab.General);
    const currentChildTabIndex = ref<number>(-1);
    const wellData = ref<any>();
    const currentComponent = computed(
      () => tabComponentMap[tabIndex.value as EDailyTab]
    );
    const currentTab = ref<TabComponent | null>(null);
    const loading = ref(false);
    const dailyReportProvide = inject<Provide.DailyReport>("dailyReport");

    onMounted(() => {
      if (route?.params?.dailyReportId) {
        dailyReportProvide?.setDailyReportId(
          route?.params?.dailyReportId as string
        );
      } else {
        dailyReportProvide?.resetDailyReportId();
      }
    });

    onUnmounted(() => {
      dailyReportProvide?.resetDailyReportId();
    });

    const setActiveTab = async (e: Event) => {
      const target = e.target as HTMLInputElement;
      if (
        tabIndex.value !== EDailyTab.General &&
        tabIndex.value !== EDailyTab.MudProperties
      ) {
        tabIndex.value = Number(target.getAttribute("data-tab-index"));
      } else {
        if (isFormOfChildTabDirty()) {
          AlertService.incompleteFormAlert(
            {
              onConfirmed: () => {
                tabIndex.value = Number(target.getAttribute("data-tab-index"));
              },
            },
            "You have unsaved changes. Are you sure you want to leave?"
          );
        } else {
          tabIndex.value = Number(target.getAttribute("data-tab-index"));
        }
      }
    };

    const isFormOfChildTabDirty = () => {
      if (currentTab?.value && currentTab?.value?.isFormOfChildTabDirty) {
        return currentTab?.value?.isFormOfChildTabDirty();
      }
    };

    const setChildActiveTab = (tabIndex: number) => {
      currentChildTabIndex.value = tabIndex;
    };

    return {
      currentTab,
      loading,
      EDailyTab,
      dailyTabs,
      breadcrumbs,
      wellData,
      tabIndex,
      currentComponent,
      currentChildTabIndex,
      isSystemAdmin: JwtService.checkRole(UserType.SystemAdmin),
      formatDate,
      isValidDate,
      setActiveTab,
      setChildActiveTab,
    };
  },
});
</script>