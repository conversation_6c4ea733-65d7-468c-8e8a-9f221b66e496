<template>
  <div v-if="loading" class="text-center">
    <div class="spinner-border text-primary" role="status">
      <span class="sr-only">Loading...</span>
    </div>
  </div>
  <div v-else>
    <div class="h-auto w-full">
      <ul
        class="h-auto w-full flex flex-row flex-wrap items-center gap-2"
        role="tablist"
      >
        <li v-for="item in tabs" :key="item?.value">
          <div
            class="whitespace-nowrap cursor-pointer font-semibold hover:text-active-hover hover:border-b-2 hover:border-active-border-hover"
            :class="{
              active: tabIndex === item?.value,
              'text-active border-b-2 border-active-border':
                tabIndex === item?.value,
              'text-inactive border-b-2 border-inactive-border': tabIndex !== item?.value,
            }"
            @click="setActiveTab($event)"
            :data-tab-index="item?.value"
            role="tab"
          >
            {{ item?.title }}
          </div>
        </li>
      </ul>
    </div>
    <div class="py-4">
      <el-empty v-if="sampleList.length === 0" description="No Data" />
      <div v-else v-for="sample in sampleList" :key="sample?.id">
        <div v-show="tabIndex == sample?.id">
          <SampleInfo :sample="sample" :costSummary="costSummary" />
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import SvgIcon from "@/constants/SvgIcon.vue";
import { ExceptionCode, ExceptionMessages } from "@/constants/exceptions";
import AlertService from "@/services/AlertService";
import { useCostStore } from "@/stores/cost";
import { useDailyReportStore } from "@/stores/daily-report";
import { useSampleStore } from "@/stores/sample";
import { defineComponent, onMounted, ref, watch } from "vue";
import SampleInfo from "./SampleInfo.vue";

export default defineComponent({
  name: "properties-tab",
  components: { SvgIcon, SampleInfo },
  props: {
    wellId: {
      type: String,
      required: true,
    },
  },
  setup(props) {
    const dailyReportStore = useDailyReportStore();
    const costStore = useCostStore();
    const sampleStore = useSampleStore();
    const dailyReportId = ref("");
    const loading = ref(false);
    const sampleList = ref<any>([]);
    const tabs = ref<any>([]);
    const tabIndex = ref<string>("");
    const costSummary = ref<any>({});

    onMounted(() => {
      if (props?.wellId) {
        getDailyReportId();
      }
    });

    watch(
      () => props?.wellId,
      () => {
        if (props?.wellId) {
          getDailyReportId();
        }
      }
    );

    const getDailyReportId = async (): Promise<void> => {
      if (!props?.wellId) return;
      loading.value = true;
      dailyReportStore.getLatestDailyReport({
        wellId: props?.wellId,
        callback: {
          onSuccess: (res: any) => {
            if (res?.id) {
              dailyReportId.value = res?.id;
              getSamples();
              getCostSummary();
            }
          },
          onFailure: (error: any) => {
            AlertService.resultAlert(
              ExceptionMessages[
                error?.response?.data?.errorCode as ExceptionCode
              ] ||
                error?.response?.data?.message ||
                error?.message ||
                "Sorry, looks like there are some errors detected, please try again.",
              "error"
            );
          },
          onFinish: () => {
            loading.value = false;
          },
        },
      });
    };

    const getSamples = async (): Promise<void> => {
      if (!dailyReportId.value) return;
      loading.value = true;

      const params = {
        dailyReportId: dailyReportId?.value,
        page: 1,
        limit: 200,
      };

      sampleStore.getSamples({
        params: params,
        callback: {
          onSuccess: (res: any) => {
            sampleList.value = res?.items;
            tabs.value = res?.items?.map((item: any, index: number) => {
              return {
                value: item?.id,
                title: "Sample " + (index + 1),
              };
            });

            tabIndex.value = res?.items[0]?.id;
          },
          onFinish: (_err: any) => {
            loading.value = false;
          },
        },
      });
    };

    const getCostSummary = async (): Promise<void> => {
      if (!dailyReportId.value) return;
      loading.value = true;

      const params = {
        dailyReportId: dailyReportId?.value,
        wellId: props?.wellId,
      };

      costStore.getCostSummary({
        params: params,
        callback: {
          onSuccess: (res: any) => {
            costSummary.value = res;
          },
          onFinish: (_err: any) => {
            loading.value = false;
          },
        },
      });
    };

    const setActiveTab = (e: Event) => {
      const target = e.target as HTMLInputElement;

      tabIndex.value = target.getAttribute("data-tab-index") as string;
    };

    const reset = () => {
      dailyReportId.value = "";
      sampleList.value = [];
      tabs.value = [];
      tabIndex.value = "";
      costSummary.value = {};
    };

    return {
      loading,
      tabs,
      tabIndex,
      sampleList,
      costSummary,
      reset,
      setActiveTab,
    };
  },
});
</script>
