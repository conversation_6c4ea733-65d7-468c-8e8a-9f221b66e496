// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 48;
	objects = {

/* Begin PBXBuildFile section */
		048D5F25A0BC829307C92C327E8327F6 /* CapacitorExtension.swift in Sources */ = {isa = PBXBuildFile; fileRef = CEF51C8471224BC2655BD348DEAB9DF3 /* CapacitorExtension.swift */; };
		051D26D34EF77E9280B4DAAF7201F907 /* TmpViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = D2277C9066695A746C2B03854C4E9A01 /* TmpViewController.swift */; };
		05B1023C6FDAE9FD0F512581C05A83B4 /* CAPBridge.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7CEAEDC15D6FF65D93022C61120DBC6B /* CAPBridge.swift */; };
		0A0232D7A9E79A4B498D7917E22A70CC /* CDVCommandDelegateImpl.m in Sources */ = {isa = PBXBuildFile; fileRef = 0293F0D768018F82F2F82CF064B18FAD /* CDVCommandDelegateImpl.m */; };
		0AE0D6CA16F0A3992160BE9AE8725986 /* Foundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 6CA66746F86BE55810CFE7AD03678EED /* Foundation.framework */; };
		0E8E880BEF966C7DFF96FB0C0A2EA603 /* WebKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = EDB8C9ACBBD85B912C1048908BCC00F8 /* WebKit.framework */; };
		0ED62D6A4B18BEF1218E763D644F72A2 /* HttpRequestHandler.swift in Sources */ = {isa = PBXBuildFile; fileRef = ECA4C90DF34492CBD0454C1916F0D836 /* HttpRequestHandler.swift */; };
		10502F7DE7D1017FDCA7208175DD15D9 /* CDVInvokedUrlCommand.h in Headers */ = {isa = PBXBuildFile; fileRef = F42BE94011D8DFBD0B6DC8E15970737B /* CDVInvokedUrlCommand.h */; settings = {ATTRIBUTES = (Public, ); }; };
		10B5B177925E7E326DBB61DA6E358E27 /* WebViewAssetHandler.swift in Sources */ = {isa = PBXBuildFile; fileRef = 5D3B51661F79F3A51DBE4FCD5F494ABD /* WebViewAssetHandler.swift */; };
		117AD9BED875805A9EB89A6972251FA6 /* CDVScreenOrientationDelegate.h in Headers */ = {isa = PBXBuildFile; fileRef = 6F6A52EC154CAE91F8C02DE230F2FA19 /* CDVScreenOrientationDelegate.h */; settings = {ATTRIBUTES = (Public, ); }; };
		13479EFCC90FF58AD01CDE4A78144DEC /* CAPBridgedPlugin+getMethod.swift in Sources */ = {isa = PBXBuildFile; fileRef = DA242E3BD635269D5803BE2C82F00DB7 /* CAPBridgedPlugin+getMethod.swift */; };
		160DDB1B498FDA3434F0609495709CEE /* CapacitorCookieManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = F47E17E260BCAECBD710DEA38EE35CCC /* CapacitorCookieManager.swift */; };
		174520D52CAF7D96D08DAC7229C1A9D2 /* Data+Capacitor.swift in Sources */ = {isa = PBXBuildFile; fileRef = CD982DA41ACEA4C94648180FAF4680DA /* Data+Capacitor.swift */; };
		184E2616B15277B8C336AE461DE3120B /* CAPFile.swift in Sources */ = {isa = PBXBuildFile; fileRef = EB4DCD3E7C676AD822D90E6696B84A4B /* CAPFile.swift */; };
		1C0C08D1F8D7204BE175E284D8374CA3 /* CAPPluginCall.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7917C20F55CF50D6525B1AC071D74EAD /* CAPPluginCall.swift */; };
		1C1591A44C89E03BD7285E4CDF7507B0 /* native-bridge.js in Resources */ = {isa = PBXBuildFile; fileRef = 62314F6490651760FCAF24722F104957 /* native-bridge.js */; };
		1E0DCD5A20E4CBC197A0935086584D5A /* CAPPluginCall.h in Headers */ = {isa = PBXBuildFile; fileRef = 013C2007ADB883DDE88B9CF3BED780AC /* CAPPluginCall.h */; settings = {ATTRIBUTES = (Public, ); }; };
		1F9853A4A505AD77876819A90FC79EF3 /* JS.swift in Sources */ = {isa = PBXBuildFile; fileRef = E62EEDAEFC5F4EAC20E17747D55E1CE7 /* JS.swift */; };
		226A16ECFF4872E1A37853017E6876C4 /* JSTypes.swift in Sources */ = {isa = PBXBuildFile; fileRef = 483A7E917F85201415428100D1902E50 /* JSTypes.swift */; };
		22C5D969A8431EB8B4AD90BAB22469D3 /* DefaultPlugins.m in Sources */ = {isa = PBXBuildFile; fileRef = 6F5CDA8547582EEB988682D47A7159A5 /* DefaultPlugins.m */; };
		289BEDF882C1482107311C269CB892B5 /* Foundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 6CA66746F86BE55810CFE7AD03678EED /* Foundation.framework */; };
		2C353DDB806C9F4E6A95E60EE92D640E /* CAPPlugin.h in Headers */ = {isa = PBXBuildFile; fileRef = 1371FB7BD11014FC9B2031B9A86858BD /* CAPPlugin.h */; settings = {ATTRIBUTES = (Public, ); }; };
		2EE14A75353535491AAA5BB580540E5E /* KeyValueStore.swift in Sources */ = {isa = PBXBuildFile; fileRef = BE217A9B1E8D8424071BE90A5EF9A56B /* KeyValueStore.swift */; };
		323BDB40840A0CB2B8A79018B66F9DC4 /* CAPBridgeDelegate.swift in Sources */ = {isa = PBXBuildFile; fileRef = 426AF87D355DC8AE2E685975ADDB239F /* CAPBridgeDelegate.swift */; };
		33ADA954019B2B8379813598247766EC /* CDVConfigParser.h in Headers */ = {isa = PBXBuildFile; fileRef = C15C8317F65B6E822297930918CF1AB9 /* CDVConfigParser.h */; settings = {ATTRIBUTES = (Public, ); }; };
		347FA990421572B787D061BF8006773A /* Router.swift in Sources */ = {isa = PBXBuildFile; fileRef = 708C634B883A31A16740514A42283091 /* Router.swift */; };
		360977DA24B5AA5503059C5EA6456B96 /* WebViewDelegationHandler.swift in Sources */ = {isa = PBXBuildFile; fileRef = 769272A43927FDDD328FFEAC8D7FCC23 /* WebViewDelegationHandler.swift */; };
		399DAAD86DDC3E2E76112004922C2766 /* CAPInstanceConfiguration.h in Headers */ = {isa = PBXBuildFile; fileRef = 013F78B63A7411CA87743EDE704CF770 /* CAPInstanceConfiguration.h */; settings = {ATTRIBUTES = (Public, ); }; };
		3B531711DB6122B52F4C05735A9895BC /* CDVPlugin+Resources.h in Headers */ = {isa = PBXBuildFile; fileRef = 3638898D77C605535B6198D07648DA7A /* CDVPlugin+Resources.h */; settings = {ATTRIBUTES = (Public, ); }; };
		******************************** /* AppDelegate.h in Headers */ = {isa = PBXBuildFile; fileRef = 57CB45540AF8E78111F5CEB9D8330076 /* AppDelegate.h */; settings = {ATTRIBUTES = (Public, ); }; };
		405EEA7E03B536DBF98A53AF01FFF824 /* NSDictionary+CordovaPreferences.h in Headers */ = {isa = PBXBuildFile; fileRef = 77E979CB60533514AD86F35E580AACE7 /* NSDictionary+CordovaPreferences.h */; settings = {ATTRIBUTES = (Public, ); }; };
		41021200A7A22592A5C2A37864D2E891 /* NotificationHandlerProtocol.swift in Sources */ = {isa = PBXBuildFile; fileRef = CFA478543667D2B3E6B1CE754A8436A5 /* NotificationHandlerProtocol.swift */; };
		41B2A309D94A219FC9C6DE83EC12D149 /* CDVCommandDelegateImpl.h in Headers */ = {isa = PBXBuildFile; fileRef = DC6C89520A36FA0E491595008ABF29DA /* CDVCommandDelegateImpl.h */; settings = {ATTRIBUTES = (Public, ); }; };
		42E58594342FC0BF6237043C4F53BE7B /* Array+Capacitor.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8EE3B1188829A53DF610268CF433A773 /* Array+Capacitor.swift */; };
		438CAEBDB8751AF9F81C9FF322EC6809 /* PluginCallResult.swift in Sources */ = {isa = PBXBuildFile; fileRef = B28607DF31712199DB80866E7DBC0698 /* PluginCallResult.swift */; };
		4952FB2AD73A65174074014264BEEC19 /* Capacitor-dummy.m in Sources */ = {isa = PBXBuildFile; fileRef = 595B646866811594D1A406528F65E069 /* Capacitor-dummy.m */; };
		5214ABCD2A694C1BF72181686FB1717F /* WebView.swift in Sources */ = {isa = PBXBuildFile; fileRef = CA72F52F7878008518B2EB0018D2816B /* WebView.swift */; };
		522A7B978F3543240D0D9A292A226C94 /* JSValueEncoder.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1261413CBFB683BAF1FA11B1CB5CB0FB /* JSValueEncoder.swift */; };
		537E3799CE5EC6A72C89175FE34D148D /* CAPBridgedPlugin.h in Headers */ = {isa = PBXBuildFile; fileRef = 81E3E1B306511994219F4A25B7D23B3D /* CAPBridgedPlugin.h */; settings = {ATTRIBUTES = (Public, ); }; };
		53E87210D102416A0A80FAB50ACD83D3 /* CAPInstanceDescriptor.h in Headers */ = {isa = PBXBuildFile; fileRef = C0477FB8DC1ABA464A9FD6DDA3E41565 /* CAPInstanceDescriptor.h */; settings = {ATTRIBUTES = (Public, ); }; };
		54BAB1D0C95B9A62E9787B57AB8AC536 /* CDV.h in Headers */ = {isa = PBXBuildFile; fileRef = 71B00CA3A86D287878E8C98C0B7114D4 /* CDV.h */; settings = {ATTRIBUTES = (Public, ); }; };
		554DA2CF416BCB2AEA3551E11AA55826 /* Foundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 6CA66746F86BE55810CFE7AD03678EED /* Foundation.framework */; };
		581B691E7A4C48D2B5D0BF1B85C35356 /* CAPPlugin.m in Sources */ = {isa = PBXBuildFile; fileRef = ABA5DA78C55B1F05E4F2A0558AD9A891 /* CAPPlugin.m */; };
		589EF4ADAB147A9AC7BD177F540578B0 /* CapacitorCordova.h in Headers */ = {isa = PBXBuildFile; fileRef = C0FB4BA7C70938D31B605A88FFF61EA1 /* CapacitorCordova.h */; settings = {ATTRIBUTES = (Public, ); }; };
		595DAE4CD8156E7FEE2652457F53042F /* AppDelegate.m in Sources */ = {isa = PBXBuildFile; fileRef = 9BCA041504820B441CADA508303E04A8 /* AppDelegate.m */; };
		69AEC8938C583ED3E053A55FA6F18E70 /* KeyPath.swift in Sources */ = {isa = PBXBuildFile; fileRef = 81CB858857E087CEA11597A4E9128AAA /* KeyPath.swift */; };
		6FAE407747D49868BC690636FE805D76 /* UIStatusBarManager+CAPHandleTapAction.m in Sources */ = {isa = PBXBuildFile; fileRef = 4E6308568F081B738DF401762B76350F /* UIStatusBarManager+CAPHandleTapAction.m */; };
		710096C917E692BB98E178E392042A08 /* CDVWebViewProcessPoolFactory.m in Sources */ = {isa = PBXBuildFile; fileRef = 8AE3AF2DD907B0897188777CC3917214 /* CDVWebViewProcessPoolFactory.m */; };
		72A876E5948B92D74B3611C701CCEB7F /* CAPApplicationDelegateProxy.swift in Sources */ = {isa = PBXBuildFile; fileRef = 972A8A6FA6ACB070EACA121A73C1E795 /* CAPApplicationDelegateProxy.swift */; };
		72F7D1919CB887F2EE766E1347EC112D /* WKWebView+Capacitor.m in Sources */ = {isa = PBXBuildFile; fileRef = 0AB9C4345E74D86AD901E4C4876FF974 /* WKWebView+Capacitor.m */; };
		79414E379F0F40F185D78CBE2F00EDD7 /* Capacitor.h in Headers */ = {isa = PBXBuildFile; fileRef = 53E0B777B61322799639839C77AB004B /* Capacitor.h */; settings = {ATTRIBUTES = (Public, ); }; };
		79FC484DFA6F276CF629E49E54135434 /* CAPInstanceConfiguration.m in Sources */ = {isa = PBXBuildFile; fileRef = E3B0D7FA2D80A1F2B86D0FF5A842D4E6 /* CAPInstanceConfiguration.m */; };
		7B07578247E6AEB7ABFC98F178D5A144 /* NSDictionary+CordovaPreferences.m in Sources */ = {isa = PBXBuildFile; fileRef = 3A2026FFE2A3B41768B471901CB09A4E /* NSDictionary+CordovaPreferences.m */; };
		7BBFA2A46B1706161DCF7246DA0EB3D9 /* WKWebView+Capacitor.swift in Sources */ = {isa = PBXBuildFile; fileRef = F762FE8707B45CFE2FD2B2B55003ACA3 /* WKWebView+Capacitor.swift */; };
		80BFC0B4B8FF3FC417EE811D9DAD7CC0 /* CDVPluginManager.m in Sources */ = {isa = PBXBuildFile; fileRef = CA56855168CB521A9C44917D380C3387 /* CDVPluginManager.m */; };
		835F507A32CA5C33886433667B50C991 /* CAPBridgeViewController+CDVScreenOrientationDelegate.m in Sources */ = {isa = PBXBuildFile; fileRef = A559716444AE63CDF6B9D40BEF726E5D /* CAPBridgeViewController+CDVScreenOrientationDelegate.m */; };
		84304B5535036D3DC79FE810507502CE /* Console.swift in Sources */ = {isa = PBXBuildFile; fileRef = 06376241E440BC59D068F16FFE956BB4 /* Console.swift */; };
		843E2CE985D0A6203D1057006417118E /* PrivacyInfo.xcprivacy in Resources */ = {isa = PBXBuildFile; fileRef = 46D3F2AEF5794DF6621084FAA11246C6 /* PrivacyInfo.xcprivacy */; };
		8A6F6D00589BD8EA57B78C77063902FD /* CapacitorCordova-dummy.m in Sources */ = {isa = PBXBuildFile; fileRef = FA84198FE2DD80BFF71F1DD2528547E3 /* CapacitorCordova-dummy.m */; };
		8ACC35F45A404098A6CB0D9BA2072944 /* CAPPluginMethod.m in Sources */ = {isa = PBXBuildFile; fileRef = 3CECE922154B20FAFEA56E7B1AFCFF90 /* CAPPluginMethod.m */; };
		8D3AC0C0367991F8D6F1145AE0CFFEA9 /* CapacitorBridge.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4929D8051C4DF669DC63415396C702D5 /* CapacitorBridge.swift */; };
		919E27170BC8FF6001A14D4A9A13D140 /* UIColor.swift in Sources */ = {isa = PBXBuildFile; fileRef = 11F645258146D17F4F0D79CB4C83C714 /* UIColor.swift */; };
		961067F66821FC49DB3473A6E4B84E94 /* AppUUID.swift in Sources */ = {isa = PBXBuildFile; fileRef = EEF5AC4ED432132007FD05D2492B22F1 /* AppUUID.swift */; };
		98D195B9562C05FC903187C2E4DC5AF3 /* CAPBridgeViewController+CDVScreenOrientationDelegate.h in Headers */ = {isa = PBXBuildFile; fileRef = EAAC1BE5EEB7D923B613A042F83A241E /* CAPBridgeViewController+CDVScreenOrientationDelegate.h */; settings = {ATTRIBUTES = (Public, ); }; };
		9A7981042BD0247C666F73DDA307674E /* CAPPluginCall.m in Sources */ = {isa = PBXBuildFile; fileRef = C09281FABDD3BA660F59803DC7161FCF /* CAPPluginCall.m */; };
		9C4D07DAEAE68399F2594CAFA92009A4 /* CDVPlugin+Resources.m in Sources */ = {isa = PBXBuildFile; fileRef = 4367997E0109CC6C8B58D9C7D537B0F9 /* CDVPlugin+Resources.m */; };
		9CE0F011BFE274075507C37C7B8928D0 /* CDVPlugin.m in Sources */ = {isa = PBXBuildFile; fileRef = B7094E5121E81F9505A500C89C3861B5 /* CDVPlugin.m */; };
		9DC210339C97CC020F47372921ED4515 /* CapacitorCookies.swift in Sources */ = {isa = PBXBuildFile; fileRef = CAC58A84F8B7BE0D8FAE1CACCA36739A /* CapacitorCookies.swift */; };
		9DC22C515EB3098349A1B59B2F2B92CA /* CAPPluginMethod.h in Headers */ = {isa = PBXBuildFile; fileRef = FDC74997E9D0D188436EEDB93C32D640 /* CAPPluginMethod.h */; settings = {ATTRIBUTES = (Public, ); }; };
		9E0D5E29E18F0B17C8EC90A084CE7778 /* PrivacyInfo.xcprivacy in Resources */ = {isa = PBXBuildFile; fileRef = 5424997D198A8CE6A865229C43788D92 /* PrivacyInfo.xcprivacy */; };
		A20710404F7DB197F5E6205BB5A63F29 /* PluginConfig.swift in Sources */ = {isa = PBXBuildFile; fileRef = 3FF407535B5DDA63FAE43E6A5013DB57 /* PluginConfig.swift */; };
		A6952C4E25E041CD0803140AB69202F1 /* DocLinks.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8B7410417BE6C26643106131C76444C6 /* DocLinks.swift */; };
		A6C49C2C77A81F24ED6F60B3E4435CCD /* CDVViewController.h in Headers */ = {isa = PBXBuildFile; fileRef = D26F52FAB94CF3750820A443D06D7DF3 /* CDVViewController.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AF84643D0E3EDDFD92AB1B18570C76B1 /* CDVPlugin.h in Headers */ = {isa = PBXBuildFile; fileRef = 1794276BD482FF380AF10B0B573B2460 /* CDVPlugin.h */; settings = {ATTRIBUTES = (Public, ); }; };
		B3AE88FB10FE19CF223B283787FCD578 /* CDVPluginResult.m in Sources */ = {isa = PBXBuildFile; fileRef = DD82EC12D409DBBF114A7E2708EF5AC5 /* CDVPluginResult.m */; };
		B454EE4626E107A57DCBB091D32D88E9 /* CDVPluginManager.h in Headers */ = {isa = PBXBuildFile; fileRef = F3ADF50771FC4E72AB3EDEFD5537A6A7 /* CDVPluginManager.h */; settings = {ATTRIBUTES = (Public, ); }; };
		B6AE510BA5E025E893C38FD913011639 /* CDVCommandDelegate.h in Headers */ = {isa = PBXBuildFile; fileRef = 8AAB9186EE1FC72AD95AF9C0CA9AF2B0 /* CDVCommandDelegate.h */; settings = {ATTRIBUTES = (Public, ); }; };
		BD97C80AD69BEA86BDB34B38C93A3BEB /* CAPBridgeViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2E554BED55665EA8CDC92D09BEB2C1CF /* CAPBridgeViewController.swift */; };
		BF2EF986989EC3148A44D2688569A292 /* CAPInstancePlugin.swift in Sources */ = {isa = PBXBuildFile; fileRef = 5ED16C4FA5E807EDF9EB4B1B91AFE650 /* CAPInstancePlugin.swift */; };
		C1002D0D7C277F31743E05CAEF5499BA /* CDVURLProtocol.m in Sources */ = {isa = PBXBuildFile; fileRef = 7053BC2BEE366AA04B1BA32E2E9B947C /* CDVURLProtocol.m */; };
		C30005EBEC8A238A420481DE95E5B254 /* CapacitorHttp.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2DE386678C539B0C359C815458F21C21 /* CapacitorHttp.swift */; };
		C3DC227F5D040C2DD9D3D54133EBAD2E /* CDVInvokedUrlCommand.m in Sources */ = {isa = PBXBuildFile; fileRef = E9DB92071E7B0A1EA013B99A118DD2F8 /* CDVInvokedUrlCommand.m */; };
		C4C16EE9E04F827A52474FB8A379E813 /* NotificationRouter.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1DDCB2840C3FA044AB8A00AF68BC4619 /* NotificationRouter.swift */; };
		C4DF33BE3379E21421B319114E15061B /* CAPInstanceDescriptor.m in Sources */ = {isa = PBXBuildFile; fileRef = 06F1BA4FDEEDEBBD021A81DD339BB427 /* CAPInstanceDescriptor.m */; };
		C6C3B156184692CC45BFDC15D6739D45 /* CDVViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = DA885063C838E5DCF7137AB975B61F1C /* CDVViewController.m */; };
		C896023861D7B8790C651E1D61708129 /* Pods-App-dummy.m in Sources */ = {isa = PBXBuildFile; fileRef = 6D748E1EB3CD22A59197E91481E3FBE9 /* Pods-App-dummy.m */; };
		CB9E20A024AAC579D5BC41C316DB7D5E /* CAPLog.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0B043939F309B458C44B7B48CD6A8DD4 /* CAPLog.swift */; };
		CCB4C4C5745844FFC8B825B1E88C5D20 /* CAPInstanceConfiguration.swift in Sources */ = {isa = PBXBuildFile; fileRef = BBFE193B36A52BF11BD972361D2F5488 /* CAPInstanceConfiguration.swift */; };
		CCEA3FF8FAB47DB8DCFB66322D3DFF15 /* CAPBridgedJSTypes.m in Sources */ = {isa = PBXBuildFile; fileRef = 459D656A1194F653B5A53CE8FB17749C /* CAPBridgedJSTypes.m */; };
		CFB593EFB4AE4CB646963872311BD957 /* CAPPluginMethod.swift in Sources */ = {isa = PBXBuildFile; fileRef = 94D304B8016A5D6F055983F2229FDC15 /* CAPPluginMethod.swift */; };
		CFD8F1153D96D05DB6C0456B2707F6FC /* JSValueDecoder.swift in Sources */ = {isa = PBXBuildFile; fileRef = 9BAD47686D5F2B6C45F82A8346F74E1D /* JSValueDecoder.swift */; };
		D046E4CE27CA3585CE9626F9938654E6 /* CDVWebViewProcessPoolFactory.h in Headers */ = {isa = PBXBuildFile; fileRef = 5F18641E719663C845313D1168AA0DB2 /* CDVWebViewProcessPoolFactory.h */; settings = {ATTRIBUTES = (Public, ); }; };
		D300648B6DF295685032E531A3AA0CE1 /* CAPBridgeProtocol.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2787E488114E74FD1E5DB1F560216939 /* CAPBridgeProtocol.swift */; };
		D39A0BD0C9B25E98D3AA4648F2A42DAC /* CDVConfigParser.m in Sources */ = {isa = PBXBuildFile; fileRef = 6557620450A23B04FDAB81D12512E616 /* CDVConfigParser.m */; };
		DCFE5B79E2E68FF6A3AAA7A01995CF5E /* Pods-App-umbrella.h in Headers */ = {isa = PBXBuildFile; fileRef = 8E75B29AE1472628C750F928572B5885 /* Pods-App-umbrella.h */; settings = {ATTRIBUTES = (Public, ); }; };
		DE7D610458280A4D690BE1FB362BCEE0 /* CAPInstanceDescriptor.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0AC116CC7DE4F7BBD9F7BC76F5B94143 /* CAPInstanceDescriptor.swift */; };
		E283743C6AE551632BF1CA4123C7D3E2 /* CAPBridgedJSTypes.h in Headers */ = {isa = PBXBuildFile; fileRef = 585B0BFD483847A0D2622B293B4E6347 /* CAPBridgedJSTypes.h */; settings = {ATTRIBUTES = (Public, ); }; };
		E6E4D54F839A26790C76FF5B086EAA46 /* JSExport.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8C1A1AACE684A589816FAD3458DB610B /* JSExport.swift */; };
		EACC4EB222690A8EA82FCCE32B0EF331 /* CapacitorUrlRequest.swift in Sources */ = {isa = PBXBuildFile; fileRef = 890A05938EAF51E0B361AF5D1F83C1DB /* CapacitorUrlRequest.swift */; };
		EACD8E483B6D3D3094F0B336E20552E8 /* CAPNotifications.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4C53D1E0413B7AA59CC2CCD1C94D8E1B /* CAPNotifications.swift */; };
		F08861E1A08953274BECA5F3F2C488B6 /* CAPPlugin+LoadInstance.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7C065DA858DC1F1887CD3196A97AB78D /* CAPPlugin+LoadInstance.swift */; };
		F3C678E7D36698F6CD083B5DF56319CA /* CDVURLProtocol.h in Headers */ = {isa = PBXBuildFile; fileRef = 745C6CCCF231F2FF9EF803A99D63F3BC /* CDVURLProtocol.h */; settings = {ATTRIBUTES = (Public, ); }; };
		F3CB2898585CE47A7D157676DC14D7B5 /* CDVPluginResult.h in Headers */ = {isa = PBXBuildFile; fileRef = B27BFFD821EE4016CC068636C0046916 /* CDVPluginResult.h */; settings = {ATTRIBUTES = (Public, ); }; };
		F461ADD5B52B2FAD506BEB9156338303 /* CDVAvailability.h in Headers */ = {isa = PBXBuildFile; fileRef = 407F9CAE07317EEB499C379F617AA867 /* CDVAvailability.h */; settings = {ATTRIBUTES = (Public, ); }; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		42C9FB0746E2DF32F3060178595BCFB1 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 400AE44335852A2D8D746557E21E8EB0;
			remoteInfo = CapacitorCordova;
		};
		A23AF702DEC4D912BAF6D65BEBEF7AC2 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 400AE44335852A2D8D746557E21E8EB0;
			remoteInfo = CapacitorCordova;
		};
		AAC5B4C5E2673D810C1EA0226B8C61A3 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 0ECF3D6BFCC08377AE23B027EE1D4371;
			remoteInfo = Capacitor;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		013C2007ADB883DDE88B9CF3BED780AC /* CAPPluginCall.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = CAPPluginCall.h; path = Capacitor/Capacitor/CAPPluginCall.h; sourceTree = "<group>"; };
		013F78B63A7411CA87743EDE704CF770 /* CAPInstanceConfiguration.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = CAPInstanceConfiguration.h; path = Capacitor/Capacitor/CAPInstanceConfiguration.h; sourceTree = "<group>"; };
		0293F0D768018F82F2F82CF064B18FAD /* CDVCommandDelegateImpl.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; path = CDVCommandDelegateImpl.m; sourceTree = "<group>"; };
		06376241E440BC59D068F16FFE956BB4 /* Console.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = Console.swift; sourceTree = "<group>"; };
		06F1BA4FDEEDEBBD021A81DD339BB427 /* CAPInstanceDescriptor.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = CAPInstanceDescriptor.m; path = Capacitor/Capacitor/CAPInstanceDescriptor.m; sourceTree = "<group>"; };
		09209143938B2386BB3906033655559D /* Pods-App.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = "Pods-App.debug.xcconfig"; sourceTree = "<group>"; };
		0AB9C4345E74D86AD901E4C4876FF974 /* WKWebView+Capacitor.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = "WKWebView+Capacitor.m"; path = "Capacitor/Capacitor/WKWebView+Capacitor.m"; sourceTree = "<group>"; };
		0AC116CC7DE4F7BBD9F7BC76F5B94143 /* CAPInstanceDescriptor.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = CAPInstanceDescriptor.swift; path = Capacitor/Capacitor/CAPInstanceDescriptor.swift; sourceTree = "<group>"; };
		0B043939F309B458C44B7B48CD6A8DD4 /* CAPLog.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = CAPLog.swift; path = Capacitor/Capacitor/CAPLog.swift; sourceTree = "<group>"; };
		11F645258146D17F4F0D79CB4C83C714 /* UIColor.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = UIColor.swift; path = Capacitor/Capacitor/UIColor.swift; sourceTree = "<group>"; };
		1261413CBFB683BAF1FA11B1CB5CB0FB /* JSValueEncoder.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = JSValueEncoder.swift; sourceTree = "<group>"; };
		1371FB7BD11014FC9B2031B9A86858BD /* CAPPlugin.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = CAPPlugin.h; path = Capacitor/Capacitor/CAPPlugin.h; sourceTree = "<group>"; };
		1794276BD482FF380AF10B0B573B2460 /* CDVPlugin.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = CDVPlugin.h; sourceTree = "<group>"; };
		1ABFDA3391AEBA42EFB0FEBF824A6E34 /* CapacitorCordova */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; name = CapacitorCordova; path = Cordova.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		1DDCB2840C3FA044AB8A00AF68BC4619 /* NotificationRouter.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = NotificationRouter.swift; path = Capacitor/Capacitor/NotificationRouter.swift; sourceTree = "<group>"; };
		254FC3D0CF581D9256427E9CBAA97934 /* Capacitor.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = Capacitor.release.xcconfig; sourceTree = "<group>"; };
		27424556CFFCFE6091D7FCE7968135EB /* Capacitor.modulemap */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.module; name = Capacitor.modulemap; path = Capacitor/Capacitor/Capacitor.modulemap; sourceTree = "<group>"; };
		2787E488114E74FD1E5DB1F560216939 /* CAPBridgeProtocol.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = CAPBridgeProtocol.swift; path = Capacitor/Capacitor/CAPBridgeProtocol.swift; sourceTree = "<group>"; };
		2DE386678C539B0C359C815458F21C21 /* CapacitorHttp.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = CapacitorHttp.swift; sourceTree = "<group>"; };
		2E554BED55665EA8CDC92D09BEB2C1CF /* CAPBridgeViewController.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = CAPBridgeViewController.swift; path = Capacitor/Capacitor/CAPBridgeViewController.swift; sourceTree = "<group>"; };
		3638898D77C605535B6198D07648DA7A /* CDVPlugin+Resources.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "CDVPlugin+Resources.h"; sourceTree = "<group>"; };
		3A2026FFE2A3B41768B471901CB09A4E /* NSDictionary+CordovaPreferences.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; path = "NSDictionary+CordovaPreferences.m"; sourceTree = "<group>"; };
		3CECE922154B20FAFEA56E7B1AFCFF90 /* CAPPluginMethod.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = CAPPluginMethod.m; path = Capacitor/Capacitor/CAPPluginMethod.m; sourceTree = "<group>"; };
		3FF407535B5DDA63FAE43E6A5013DB57 /* PluginConfig.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = PluginConfig.swift; path = Capacitor/Capacitor/PluginConfig.swift; sourceTree = "<group>"; };
		407F9CAE07317EEB499C379F617AA867 /* CDVAvailability.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = CDVAvailability.h; sourceTree = "<group>"; };
		426AF87D355DC8AE2E685975ADDB239F /* CAPBridgeDelegate.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = CAPBridgeDelegate.swift; path = Capacitor/Capacitor/CAPBridgeDelegate.swift; sourceTree = "<group>"; };
		4367997E0109CC6C8B58D9C7D537B0F9 /* CDVPlugin+Resources.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; path = "CDVPlugin+Resources.m"; sourceTree = "<group>"; };
		44D6062E8C618EC2B8F1D7229E8AD501 /* Capacitor */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; name = Capacitor; path = Capacitor.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		459D656A1194F653B5A53CE8FB17749C /* CAPBridgedJSTypes.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = CAPBridgedJSTypes.m; path = Capacitor/Capacitor/CAPBridgedJSTypes.m; sourceTree = "<group>"; };
		46D3F2AEF5794DF6621084FAA11246C6 /* PrivacyInfo.xcprivacy */ = {isa = PBXFileReference; includeInIndex = 1; name = PrivacyInfo.xcprivacy; path = CapacitorCordova/CapacitorCordova/PrivacyInfo.xcprivacy; sourceTree = "<group>"; };
		483A7E917F85201415428100D1902E50 /* JSTypes.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = JSTypes.swift; path = Capacitor/Capacitor/JSTypes.swift; sourceTree = "<group>"; };
		4929D8051C4DF669DC63415396C702D5 /* CapacitorBridge.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = CapacitorBridge.swift; path = Capacitor/Capacitor/CapacitorBridge.swift; sourceTree = "<group>"; };
		4C53D1E0413B7AA59CC2CCD1C94D8E1B /* CAPNotifications.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = CAPNotifications.swift; path = Capacitor/Capacitor/CAPNotifications.swift; sourceTree = "<group>"; };
		4E6308568F081B738DF401762B76350F /* UIStatusBarManager+CAPHandleTapAction.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = "UIStatusBarManager+CAPHandleTapAction.m"; path = "Capacitor/Capacitor/UIStatusBarManager+CAPHandleTapAction.m"; sourceTree = "<group>"; };
		53E0B777B61322799639839C77AB004B /* Capacitor.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = Capacitor.h; path = Capacitor/Capacitor/Capacitor.h; sourceTree = "<group>"; };
		5424997D198A8CE6A865229C43788D92 /* PrivacyInfo.xcprivacy */ = {isa = PBXFileReference; includeInIndex = 1; name = PrivacyInfo.xcprivacy; path = Capacitor/Capacitor/PrivacyInfo.xcprivacy; sourceTree = "<group>"; };
		575150CBDD80D09F5831F0F3E92BE1F0 /* Capacitor.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = Capacitor.debug.xcconfig; sourceTree = "<group>"; };
		57CB45540AF8E78111F5CEB9D8330076 /* AppDelegate.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = AppDelegate.h; sourceTree = "<group>"; };
		582BE78B8628846F9AE5B4DB49FD2666 /* CapacitorCordova.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = CapacitorCordova.debug.xcconfig; sourceTree = "<group>"; };
		585B0BFD483847A0D2622B293B4E6347 /* CAPBridgedJSTypes.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = CAPBridgedJSTypes.h; path = Capacitor/Capacitor/CAPBridgedJSTypes.h; sourceTree = "<group>"; };
		595B646866811594D1A406528F65E069 /* Capacitor-dummy.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; path = "Capacitor-dummy.m"; sourceTree = "<group>"; };
		5BEF4602752E47C46E8C10FB8B4B57F2 /* Pods-App */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; name = "Pods-App"; path = Pods_App.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		5D210D9A3706D1B22A17C8C42EE0E8B4 /* Capacitor.podspec */ = {isa = PBXFileReference; explicitFileType = text.script.ruby; includeInIndex = 1; indentWidth = 2; lastKnownFileType = text; path = Capacitor.podspec; sourceTree = "<group>"; tabWidth = 2; xcLanguageSpecificationIdentifier = xcode.lang.ruby; };
		5D3B51661F79F3A51DBE4FCD5F494ABD /* WebViewAssetHandler.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = WebViewAssetHandler.swift; path = Capacitor/Capacitor/WebViewAssetHandler.swift; sourceTree = "<group>"; };
		5ED16C4FA5E807EDF9EB4B1B91AFE650 /* CAPInstancePlugin.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = CAPInstancePlugin.swift; path = Capacitor/Capacitor/CAPInstancePlugin.swift; sourceTree = "<group>"; };
		5ED1C08B4DD9D6BE03603691078E4F55 /* CapacitorCordova-prefix.pch */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "CapacitorCordova-prefix.pch"; sourceTree = "<group>"; };
		5F18641E719663C845313D1168AA0DB2 /* CDVWebViewProcessPoolFactory.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = CDVWebViewProcessPoolFactory.h; sourceTree = "<group>"; };
		61B12281162B2669A9CD4FDB2800B3FF /* Pods-App-frameworks.sh */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.script.sh; path = "Pods-App-frameworks.sh"; sourceTree = "<group>"; };
		62314F6490651760FCAF24722F104957 /* native-bridge.js */ = {isa = PBXFileReference; includeInIndex = 1; path = "native-bridge.js"; sourceTree = "<group>"; };
		6557620450A23B04FDAB81D12512E616 /* CDVConfigParser.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; path = CDVConfigParser.m; sourceTree = "<group>"; };
		69648DE58492E2E7641AF93DF26C7C9C /* CapacitorCordova.podspec */ = {isa = PBXFileReference; explicitFileType = text.script.ruby; includeInIndex = 1; indentWidth = 2; lastKnownFileType = text; path = CapacitorCordova.podspec; sourceTree = "<group>"; tabWidth = 2; xcLanguageSpecificationIdentifier = xcode.lang.ruby; };
		6CA66746F86BE55810CFE7AD03678EED /* Foundation.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Foundation.framework; path = Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.0.sdk/System/Library/Frameworks/Foundation.framework; sourceTree = DEVELOPER_DIR; };
		6D748E1EB3CD22A59197E91481E3FBE9 /* Pods-App-dummy.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; path = "Pods-App-dummy.m"; sourceTree = "<group>"; };
		6E3D0A49ED6BC62D433B9A90EA2CA375 /* CapacitorCordova.modulemap */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.module; name = CapacitorCordova.modulemap; path = CapacitorCordova/CapacitorCordova/CapacitorCordova.modulemap; sourceTree = "<group>"; };
		6F5CDA8547582EEB988682D47A7159A5 /* DefaultPlugins.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; path = DefaultPlugins.m; sourceTree = "<group>"; };
		6F6A52EC154CAE91F8C02DE230F2FA19 /* CDVScreenOrientationDelegate.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = CDVScreenOrientationDelegate.h; sourceTree = "<group>"; };
		7053BC2BEE366AA04B1BA32E2E9B947C /* CDVURLProtocol.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; path = CDVURLProtocol.m; sourceTree = "<group>"; };
		708C634B883A31A16740514A42283091 /* Router.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = Router.swift; path = Capacitor/Capacitor/Router.swift; sourceTree = "<group>"; };
		71B00CA3A86D287878E8C98C0B7114D4 /* CDV.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = CDV.h; sourceTree = "<group>"; };
		745C6CCCF231F2FF9EF803A99D63F3BC /* CDVURLProtocol.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = CDVURLProtocol.h; sourceTree = "<group>"; };
		76836226476D35BC62A098CF501DF10B /* Pods-App.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = "Pods-App.release.xcconfig"; sourceTree = "<group>"; };
		769272A43927FDDD328FFEAC8D7FCC23 /* WebViewDelegationHandler.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = WebViewDelegationHandler.swift; path = Capacitor/Capacitor/WebViewDelegationHandler.swift; sourceTree = "<group>"; };
		77E979CB60533514AD86F35E580AACE7 /* NSDictionary+CordovaPreferences.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "NSDictionary+CordovaPreferences.h"; sourceTree = "<group>"; };
		7917C20F55CF50D6525B1AC071D74EAD /* CAPPluginCall.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = CAPPluginCall.swift; path = Capacitor/Capacitor/CAPPluginCall.swift; sourceTree = "<group>"; };
		79D7A380E2CBB08C55B18415CD1215CB /* Pods-App-acknowledgements.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "Pods-App-acknowledgements.plist"; sourceTree = "<group>"; };
		7AF7373F8D78F711D471B6AE390F658A /* Capacitor.modulemap */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.module; path = Capacitor.modulemap; sourceTree = "<group>"; };
		7C065DA858DC1F1887CD3196A97AB78D /* CAPPlugin+LoadInstance.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "CAPPlugin+LoadInstance.swift"; path = "Capacitor/Capacitor/CAPPlugin+LoadInstance.swift"; sourceTree = "<group>"; };
		7CEAEDC15D6FF65D93022C61120DBC6B /* CAPBridge.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = CAPBridge.swift; path = Capacitor/Capacitor/CAPBridge.swift; sourceTree = "<group>"; };
		81CB858857E087CEA11597A4E9128AAA /* KeyPath.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = KeyPath.swift; path = Capacitor/Capacitor/KeyPath.swift; sourceTree = "<group>"; };
		81E3E1B306511994219F4A25B7D23B3D /* CAPBridgedPlugin.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = CAPBridgedPlugin.h; path = Capacitor/Capacitor/CAPBridgedPlugin.h; sourceTree = "<group>"; };
		8610A36BA9032C643686178252D013A1 /* CapacitorCordova-Info.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "CapacitorCordova-Info.plist"; sourceTree = "<group>"; };
		8773B2E5D7F8A99B50F36D80B9AF21E1 /* Pods-App.modulemap */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.module; path = "Pods-App.modulemap"; sourceTree = "<group>"; };
		890A05938EAF51E0B361AF5D1F83C1DB /* CapacitorUrlRequest.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = CapacitorUrlRequest.swift; sourceTree = "<group>"; };
		8AAB9186EE1FC72AD95AF9C0CA9AF2B0 /* CDVCommandDelegate.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = CDVCommandDelegate.h; sourceTree = "<group>"; };
		8AE3AF2DD907B0897188777CC3917214 /* CDVWebViewProcessPoolFactory.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; path = CDVWebViewProcessPoolFactory.m; sourceTree = "<group>"; };
		8B7410417BE6C26643106131C76444C6 /* DocLinks.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = DocLinks.swift; path = Capacitor/Capacitor/DocLinks.swift; sourceTree = "<group>"; };
		8C1A1AACE684A589816FAD3458DB610B /* JSExport.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = JSExport.swift; path = Capacitor/Capacitor/JSExport.swift; sourceTree = "<group>"; };
		8E75B29AE1472628C750F928572B5885 /* Pods-App-umbrella.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "Pods-App-umbrella.h"; sourceTree = "<group>"; };
		8EE3B1188829A53DF610268CF433A773 /* Array+Capacitor.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "Array+Capacitor.swift"; path = "Capacitor/Capacitor/Array+Capacitor.swift"; sourceTree = "<group>"; };
		94D304B8016A5D6F055983F2229FDC15 /* CAPPluginMethod.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = CAPPluginMethod.swift; path = Capacitor/Capacitor/CAPPluginMethod.swift; sourceTree = "<group>"; };
		972A8A6FA6ACB070EACA121A73C1E795 /* CAPApplicationDelegateProxy.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = CAPApplicationDelegateProxy.swift; path = Capacitor/Capacitor/CAPApplicationDelegateProxy.swift; sourceTree = "<group>"; };
		9BAD47686D5F2B6C45F82A8346F74E1D /* JSValueDecoder.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = JSValueDecoder.swift; sourceTree = "<group>"; };
		9BCA041504820B441CADA508303E04A8 /* AppDelegate.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; path = AppDelegate.m; sourceTree = "<group>"; };
		9D940727FF8FB9C785EB98E56350EF41 /* Podfile */ = {isa = PBXFileReference; explicitFileType = text.script.ruby; includeInIndex = 1; indentWidth = 2; lastKnownFileType = text; name = Podfile; path = ../Podfile; sourceTree = SOURCE_ROOT; tabWidth = 2; xcLanguageSpecificationIdentifier = xcode.lang.ruby; };
		A559716444AE63CDF6B9D40BEF726E5D /* CAPBridgeViewController+CDVScreenOrientationDelegate.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = "CAPBridgeViewController+CDVScreenOrientationDelegate.m"; path = "Capacitor/Capacitor/CAPBridgeViewController+CDVScreenOrientationDelegate.m"; sourceTree = "<group>"; };
		AB9E24BDF1F703FBB176B95695E72CDD /* CapacitorCordova.modulemap */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.module; path = CapacitorCordova.modulemap; sourceTree = "<group>"; };
		ABA5DA78C55B1F05E4F2A0558AD9A891 /* CAPPlugin.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = CAPPlugin.m; path = Capacitor/Capacitor/CAPPlugin.m; sourceTree = "<group>"; };
		B27BFFD821EE4016CC068636C0046916 /* CDVPluginResult.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = CDVPluginResult.h; sourceTree = "<group>"; };
		B28607DF31712199DB80866E7DBC0698 /* PluginCallResult.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = PluginCallResult.swift; path = Capacitor/Capacitor/PluginCallResult.swift; sourceTree = "<group>"; };
		B6C3D6A65487081AB12082A4036DF4CA /* CapacitorCordova.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = CapacitorCordova.release.xcconfig; sourceTree = "<group>"; };
		B7094E5121E81F9505A500C89C3861B5 /* CDVPlugin.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; path = CDVPlugin.m; sourceTree = "<group>"; };
		B76D7FFEF4333335EAFA0B2827258173 /* Pods-App-Info.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "Pods-App-Info.plist"; sourceTree = "<group>"; };
		BBFE193B36A52BF11BD972361D2F5488 /* CAPInstanceConfiguration.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = CAPInstanceConfiguration.swift; path = Capacitor/Capacitor/CAPInstanceConfiguration.swift; sourceTree = "<group>"; };
		BE217A9B1E8D8424071BE90A5EF9A56B /* KeyValueStore.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = KeyValueStore.swift; path = Capacitor/Capacitor/KeyValueStore.swift; sourceTree = "<group>"; };
		C0477FB8DC1ABA464A9FD6DDA3E41565 /* CAPInstanceDescriptor.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = CAPInstanceDescriptor.h; path = Capacitor/Capacitor/CAPInstanceDescriptor.h; sourceTree = "<group>"; };
		C09281FABDD3BA660F59803DC7161FCF /* CAPPluginCall.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = CAPPluginCall.m; path = Capacitor/Capacitor/CAPPluginCall.m; sourceTree = "<group>"; };
		C0FB4BA7C70938D31B605A88FFF61EA1 /* CapacitorCordova.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = CapacitorCordova.h; path = CapacitorCordova/CapacitorCordova/CapacitorCordova.h; sourceTree = "<group>"; };
		C15C8317F65B6E822297930918CF1AB9 /* CDVConfigParser.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = CDVConfigParser.h; sourceTree = "<group>"; };
		C7FE226CA943FDC3E86BED790E0B7531 /* Capacitor-prefix.pch */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "Capacitor-prefix.pch"; sourceTree = "<group>"; };
		CA56855168CB521A9C44917D380C3387 /* CDVPluginManager.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; path = CDVPluginManager.m; sourceTree = "<group>"; };
		CA72F52F7878008518B2EB0018D2816B /* WebView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = WebView.swift; sourceTree = "<group>"; };
		CAC58A84F8B7BE0D8FAE1CACCA36739A /* CapacitorCookies.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = CapacitorCookies.swift; sourceTree = "<group>"; };
		CD982DA41ACEA4C94648180FAF4680DA /* Data+Capacitor.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "Data+Capacitor.swift"; path = "Capacitor/Capacitor/Data+Capacitor.swift"; sourceTree = "<group>"; };
		CEF51C8471224BC2655BD348DEAB9DF3 /* CapacitorExtension.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = CapacitorExtension.swift; path = Capacitor/Capacitor/CapacitorExtension.swift; sourceTree = "<group>"; };
		CF07BD63D9C5EA55B4B7E9E538E996E1 /* Pods-App-acknowledgements.markdown */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text; path = "Pods-App-acknowledgements.markdown"; sourceTree = "<group>"; };
		CFA478543667D2B3E6B1CE754A8436A5 /* NotificationHandlerProtocol.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = NotificationHandlerProtocol.swift; path = Capacitor/Capacitor/NotificationHandlerProtocol.swift; sourceTree = "<group>"; };
		D2277C9066695A746C2B03854C4E9A01 /* TmpViewController.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = TmpViewController.swift; path = Capacitor/Capacitor/TmpViewController.swift; sourceTree = "<group>"; };
		D26F52FAB94CF3750820A443D06D7DF3 /* CDVViewController.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = CDVViewController.h; sourceTree = "<group>"; };
		D6C369B170A1A8D5EEC5ADDAC8BF5A0B /* Capacitor-Info.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "Capacitor-Info.plist"; sourceTree = "<group>"; };
		DA242E3BD635269D5803BE2C82F00DB7 /* CAPBridgedPlugin+getMethod.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "CAPBridgedPlugin+getMethod.swift"; path = "Capacitor/Capacitor/CAPBridgedPlugin+getMethod.swift"; sourceTree = "<group>"; };
		DA885063C838E5DCF7137AB975B61F1C /* CDVViewController.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; path = CDVViewController.m; sourceTree = "<group>"; };
		DC6C89520A36FA0E491595008ABF29DA /* CDVCommandDelegateImpl.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = CDVCommandDelegateImpl.h; sourceTree = "<group>"; };
		DD82EC12D409DBBF114A7E2708EF5AC5 /* CDVPluginResult.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; path = CDVPluginResult.m; sourceTree = "<group>"; };
		E3B0D7FA2D80A1F2B86D0FF5A842D4E6 /* CAPInstanceConfiguration.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = CAPInstanceConfiguration.m; path = Capacitor/Capacitor/CAPInstanceConfiguration.m; sourceTree = "<group>"; };
		E62EEDAEFC5F4EAC20E17747D55E1CE7 /* JS.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = JS.swift; path = Capacitor/Capacitor/JS.swift; sourceTree = "<group>"; };
		E9DB92071E7B0A1EA013B99A118DD2F8 /* CDVInvokedUrlCommand.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; path = CDVInvokedUrlCommand.m; sourceTree = "<group>"; };
		EAAC1BE5EEB7D923B613A042F83A241E /* CAPBridgeViewController+CDVScreenOrientationDelegate.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = "CAPBridgeViewController+CDVScreenOrientationDelegate.h"; path = "Capacitor/Capacitor/CAPBridgeViewController+CDVScreenOrientationDelegate.h"; sourceTree = "<group>"; };
		EB4DCD3E7C676AD822D90E6696B84A4B /* CAPFile.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = CAPFile.swift; path = Capacitor/Capacitor/CAPFile.swift; sourceTree = "<group>"; };
		ECA4C90DF34492CBD0454C1916F0D836 /* HttpRequestHandler.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = HttpRequestHandler.swift; sourceTree = "<group>"; };
		EDB8C9ACBBD85B912C1048908BCC00F8 /* WebKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = WebKit.framework; path = Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.0.sdk/System/Library/Frameworks/WebKit.framework; sourceTree = DEVELOPER_DIR; };
		EEF5AC4ED432132007FD05D2492B22F1 /* AppUUID.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = AppUUID.swift; path = Capacitor/Capacitor/AppUUID.swift; sourceTree = "<group>"; };
		F3ADF50771FC4E72AB3EDEFD5537A6A7 /* CDVPluginManager.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = CDVPluginManager.h; sourceTree = "<group>"; };
		F42BE94011D8DFBD0B6DC8E15970737B /* CDVInvokedUrlCommand.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = CDVInvokedUrlCommand.h; sourceTree = "<group>"; };
		F47E17E260BCAECBD710DEA38EE35CCC /* CapacitorCookieManager.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; path = CapacitorCookieManager.swift; sourceTree = "<group>"; };
		F762FE8707B45CFE2FD2B2B55003ACA3 /* WKWebView+Capacitor.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "WKWebView+Capacitor.swift"; path = "Capacitor/Capacitor/WKWebView+Capacitor.swift"; sourceTree = "<group>"; };
		FA84198FE2DD80BFF71F1DD2528547E3 /* CapacitorCordova-dummy.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; path = "CapacitorCordova-dummy.m"; sourceTree = "<group>"; };
		FDC74997E9D0D188436EEDB93C32D640 /* CAPPluginMethod.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = CAPPluginMethod.h; path = Capacitor/Capacitor/CAPPluginMethod.h; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		1600718E35582DB91D57ED5F9145E7C3 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				554DA2CF416BCB2AEA3551E11AA55826 /* Foundation.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		391177040D71C264C52B12F30B18FDDB /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				289BEDF882C1482107311C269CB892B5 /* Foundation.framework in Frameworks */,
				0E8E880BEF966C7DFF96FB0C0A2EA603 /* WebKit.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		6A2E4D809C4624DEDDEA1833673B9DE4 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				0AE0D6CA16F0A3992160BE9AE8725986 /* Foundation.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		03ABD408BA14340269A4AB0EEDB515CB /* CapacitorCordova */ = {
			isa = PBXGroup;
			children = (
				C0FB4BA7C70938D31B605A88FFF61EA1 /* CapacitorCordova.h */,
				46D3F2AEF5794DF6621084FAA11246C6 /* PrivacyInfo.xcprivacy */,
				DA6F85AEB9E3EBC6D9282BADBF2BD940 /* Classes */,
				51CF5AAA51B6034F0DE0E794CA8C616F /* Pod */,
				DC7239050AA5019801B1EF5F577088AD /* Support Files */,
			);
			name = CapacitorCordova;
			path = "../../../node_modules/@capacitor/ios";
			sourceTree = "<group>";
		};
		12770DEFC1734EFB039105E9C2D060AC /* Capacitor */ = {
			isa = PBXGroup;
			children = (
				EEF5AC4ED432132007FD05D2492B22F1 /* AppUUID.swift */,
				8EE3B1188829A53DF610268CF433A773 /* Array+Capacitor.swift */,
				53E0B777B61322799639839C77AB004B /* Capacitor.h */,
				4929D8051C4DF669DC63415396C702D5 /* CapacitorBridge.swift */,
				CEF51C8471224BC2655BD348DEAB9DF3 /* CapacitorExtension.swift */,
				972A8A6FA6ACB070EACA121A73C1E795 /* CAPApplicationDelegateProxy.swift */,
				7CEAEDC15D6FF65D93022C61120DBC6B /* CAPBridge.swift */,
				426AF87D355DC8AE2E685975ADDB239F /* CAPBridgeDelegate.swift */,
				585B0BFD483847A0D2622B293B4E6347 /* CAPBridgedJSTypes.h */,
				459D656A1194F653B5A53CE8FB17749C /* CAPBridgedJSTypes.m */,
				81E3E1B306511994219F4A25B7D23B3D /* CAPBridgedPlugin.h */,
				DA242E3BD635269D5803BE2C82F00DB7 /* CAPBridgedPlugin+getMethod.swift */,
				2787E488114E74FD1E5DB1F560216939 /* CAPBridgeProtocol.swift */,
				2E554BED55665EA8CDC92D09BEB2C1CF /* CAPBridgeViewController.swift */,
				EAAC1BE5EEB7D923B613A042F83A241E /* CAPBridgeViewController+CDVScreenOrientationDelegate.h */,
				A559716444AE63CDF6B9D40BEF726E5D /* CAPBridgeViewController+CDVScreenOrientationDelegate.m */,
				EB4DCD3E7C676AD822D90E6696B84A4B /* CAPFile.swift */,
				013F78B63A7411CA87743EDE704CF770 /* CAPInstanceConfiguration.h */,
				E3B0D7FA2D80A1F2B86D0FF5A842D4E6 /* CAPInstanceConfiguration.m */,
				BBFE193B36A52BF11BD972361D2F5488 /* CAPInstanceConfiguration.swift */,
				C0477FB8DC1ABA464A9FD6DDA3E41565 /* CAPInstanceDescriptor.h */,
				06F1BA4FDEEDEBBD021A81DD339BB427 /* CAPInstanceDescriptor.m */,
				0AC116CC7DE4F7BBD9F7BC76F5B94143 /* CAPInstanceDescriptor.swift */,
				5ED16C4FA5E807EDF9EB4B1B91AFE650 /* CAPInstancePlugin.swift */,
				0B043939F309B458C44B7B48CD6A8DD4 /* CAPLog.swift */,
				4C53D1E0413B7AA59CC2CCD1C94D8E1B /* CAPNotifications.swift */,
				1371FB7BD11014FC9B2031B9A86858BD /* CAPPlugin.h */,
				ABA5DA78C55B1F05E4F2A0558AD9A891 /* CAPPlugin.m */,
				7C065DA858DC1F1887CD3196A97AB78D /* CAPPlugin+LoadInstance.swift */,
				013C2007ADB883DDE88B9CF3BED780AC /* CAPPluginCall.h */,
				C09281FABDD3BA660F59803DC7161FCF /* CAPPluginCall.m */,
				7917C20F55CF50D6525B1AC071D74EAD /* CAPPluginCall.swift */,
				FDC74997E9D0D188436EEDB93C32D640 /* CAPPluginMethod.h */,
				3CECE922154B20FAFEA56E7B1AFCFF90 /* CAPPluginMethod.m */,
				94D304B8016A5D6F055983F2229FDC15 /* CAPPluginMethod.swift */,
				CD982DA41ACEA4C94648180FAF4680DA /* Data+Capacitor.swift */,
				8B7410417BE6C26643106131C76444C6 /* DocLinks.swift */,
				E62EEDAEFC5F4EAC20E17747D55E1CE7 /* JS.swift */,
				8C1A1AACE684A589816FAD3458DB610B /* JSExport.swift */,
				483A7E917F85201415428100D1902E50 /* JSTypes.swift */,
				81CB858857E087CEA11597A4E9128AAA /* KeyPath.swift */,
				BE217A9B1E8D8424071BE90A5EF9A56B /* KeyValueStore.swift */,
				CFA478543667D2B3E6B1CE754A8436A5 /* NotificationHandlerProtocol.swift */,
				1DDCB2840C3FA044AB8A00AF68BC4619 /* NotificationRouter.swift */,
				B28607DF31712199DB80866E7DBC0698 /* PluginCallResult.swift */,
				3FF407535B5DDA63FAE43E6A5013DB57 /* PluginConfig.swift */,
				5424997D198A8CE6A865229C43788D92 /* PrivacyInfo.xcprivacy */,
				708C634B883A31A16740514A42283091 /* Router.swift */,
				D2277C9066695A746C2B03854C4E9A01 /* TmpViewController.swift */,
				11F645258146D17F4F0D79CB4C83C714 /* UIColor.swift */,
				4E6308568F081B738DF401762B76350F /* UIStatusBarManager+CAPHandleTapAction.m */,
				5D3B51661F79F3A51DBE4FCD5F494ABD /* WebViewAssetHandler.swift */,
				769272A43927FDDD328FFEAC8D7FCC23 /* WebViewDelegationHandler.swift */,
				0AB9C4345E74D86AD901E4C4876FF974 /* WKWebView+Capacitor.m */,
				F762FE8707B45CFE2FD2B2B55003ACA3 /* WKWebView+Capacitor.swift */,
				CE901059374969BB68E0A82862E1CECE /* assets */,
				6BA5673AAA730EF381C67FB780373785 /* Codable */,
				C2185AD8DC6B264627DA31077D50DBA1 /* Plugins */,
				6C8F927EA1D1355F2BC1A387245A84AD /* Pod */,
				87DDFCC3DC663E8EDC9DC997DF429602 /* Support Files */,
			);
			name = Capacitor;
			path = "../../../node_modules/@capacitor/ios";
			sourceTree = "<group>";
		};
		1628BF05B4CAFDCC3549A101F5A10A17 /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				D436BFCF28DC3F91BC057EA7A5D1DD60 /* iOS */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		2B0C5543E5383F22FA8C61A5411496E9 /* Public */ = {
			isa = PBXGroup;
			children = (
				57CB45540AF8E78111F5CEB9D8330076 /* AppDelegate.h */,
				9BCA041504820B441CADA508303E04A8 /* AppDelegate.m */,
				71B00CA3A86D287878E8C98C0B7114D4 /* CDV.h */,
				407F9CAE07317EEB499C379F617AA867 /* CDVAvailability.h */,
				8AAB9186EE1FC72AD95AF9C0CA9AF2B0 /* CDVCommandDelegate.h */,
				DC6C89520A36FA0E491595008ABF29DA /* CDVCommandDelegateImpl.h */,
				0293F0D768018F82F2F82CF064B18FAD /* CDVCommandDelegateImpl.m */,
				C15C8317F65B6E822297930918CF1AB9 /* CDVConfigParser.h */,
				6557620450A23B04FDAB81D12512E616 /* CDVConfigParser.m */,
				F42BE94011D8DFBD0B6DC8E15970737B /* CDVInvokedUrlCommand.h */,
				E9DB92071E7B0A1EA013B99A118DD2F8 /* CDVInvokedUrlCommand.m */,
				1794276BD482FF380AF10B0B573B2460 /* CDVPlugin.h */,
				B7094E5121E81F9505A500C89C3861B5 /* CDVPlugin.m */,
				3638898D77C605535B6198D07648DA7A /* CDVPlugin+Resources.h */,
				4367997E0109CC6C8B58D9C7D537B0F9 /* CDVPlugin+Resources.m */,
				F3ADF50771FC4E72AB3EDEFD5537A6A7 /* CDVPluginManager.h */,
				CA56855168CB521A9C44917D380C3387 /* CDVPluginManager.m */,
				B27BFFD821EE4016CC068636C0046916 /* CDVPluginResult.h */,
				DD82EC12D409DBBF114A7E2708EF5AC5 /* CDVPluginResult.m */,
				6F6A52EC154CAE91F8C02DE230F2FA19 /* CDVScreenOrientationDelegate.h */,
				745C6CCCF231F2FF9EF803A99D63F3BC /* CDVURLProtocol.h */,
				7053BC2BEE366AA04B1BA32E2E9B947C /* CDVURLProtocol.m */,
				D26F52FAB94CF3750820A443D06D7DF3 /* CDVViewController.h */,
				DA885063C838E5DCF7137AB975B61F1C /* CDVViewController.m */,
				5F18641E719663C845313D1168AA0DB2 /* CDVWebViewProcessPoolFactory.h */,
				8AE3AF2DD907B0897188777CC3917214 /* CDVWebViewProcessPoolFactory.m */,
				77E979CB60533514AD86F35E580AACE7 /* NSDictionary+CordovaPreferences.h */,
				3A2026FFE2A3B41768B471901CB09A4E /* NSDictionary+CordovaPreferences.m */,
			);
			name = Public;
			path = Public;
			sourceTree = "<group>";
		};
		31D788C32A0BE499E0000FDE0C45B082 /* Development Pods */ = {
			isa = PBXGroup;
			children = (
				12770DEFC1734EFB039105E9C2D060AC /* Capacitor */,
				03ABD408BA14340269A4AB0EEDB515CB /* CapacitorCordova */,
			);
			name = "Development Pods";
			sourceTree = "<group>";
		};
		3BDBF50EC94D03B79F62C5742162999F /* Products */ = {
			isa = PBXGroup;
			children = (
				44D6062E8C618EC2B8F1D7229E8AD501 /* Capacitor */,
				1ABFDA3391AEBA42EFB0FEBF824A6E34 /* CapacitorCordova */,
				5BEF4602752E47C46E8C10FB8B4B57F2 /* Pods-App */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		51CF5AAA51B6034F0DE0E794CA8C616F /* Pod */ = {
			isa = PBXGroup;
			children = (
				6E3D0A49ED6BC62D433B9A90EA2CA375 /* CapacitorCordova.modulemap */,
				69648DE58492E2E7641AF93DF26C7C9C /* CapacitorCordova.podspec */,
			);
			name = Pod;
			sourceTree = "<group>";
		};
		5832643698C9991C79E97F6C009112D1 /* Pods-App */ = {
			isa = PBXGroup;
			children = (
				8773B2E5D7F8A99B50F36D80B9AF21E1 /* Pods-App.modulemap */,
				CF07BD63D9C5EA55B4B7E9E538E996E1 /* Pods-App-acknowledgements.markdown */,
				79D7A380E2CBB08C55B18415CD1215CB /* Pods-App-acknowledgements.plist */,
				6D748E1EB3CD22A59197E91481E3FBE9 /* Pods-App-dummy.m */,
				61B12281162B2669A9CD4FDB2800B3FF /* Pods-App-frameworks.sh */,
				B76D7FFEF4333335EAFA0B2827258173 /* Pods-App-Info.plist */,
				8E75B29AE1472628C750F928572B5885 /* Pods-App-umbrella.h */,
				09209143938B2386BB3906033655559D /* Pods-App.debug.xcconfig */,
				76836226476D35BC62A098CF501DF10B /* Pods-App.release.xcconfig */,
			);
			name = "Pods-App";
			path = "Target Support Files/Pods-App";
			sourceTree = "<group>";
		};
		6BA5673AAA730EF381C67FB780373785 /* Codable */ = {
			isa = PBXGroup;
			children = (
				9BAD47686D5F2B6C45F82A8346F74E1D /* JSValueDecoder.swift */,
				1261413CBFB683BAF1FA11B1CB5CB0FB /* JSValueEncoder.swift */,
			);
			name = Codable;
			path = Capacitor/Capacitor/Codable;
			sourceTree = "<group>";
		};
		6C8F927EA1D1355F2BC1A387245A84AD /* Pod */ = {
			isa = PBXGroup;
			children = (
				27424556CFFCFE6091D7FCE7968135EB /* Capacitor.modulemap */,
				5D210D9A3706D1B22A17C8C42EE0E8B4 /* Capacitor.podspec */,
			);
			name = Pod;
			sourceTree = "<group>";
		};
		7C6D044F0CD972C5CAB37A7A0E4EE782 /* Targets Support Files */ = {
			isa = PBXGroup;
			children = (
				5832643698C9991C79E97F6C009112D1 /* Pods-App */,
			);
			name = "Targets Support Files";
			sourceTree = "<group>";
		};
		87DDFCC3DC663E8EDC9DC997DF429602 /* Support Files */ = {
			isa = PBXGroup;
			children = (
				7AF7373F8D78F711D471B6AE390F658A /* Capacitor.modulemap */,
				595B646866811594D1A406528F65E069 /* Capacitor-dummy.m */,
				D6C369B170A1A8D5EEC5ADDAC8BF5A0B /* Capacitor-Info.plist */,
				C7FE226CA943FDC3E86BED790E0B7531 /* Capacitor-prefix.pch */,
				575150CBDD80D09F5831F0F3E92BE1F0 /* Capacitor.debug.xcconfig */,
				254FC3D0CF581D9256427E9CBAA97934 /* Capacitor.release.xcconfig */,
			);
			name = "Support Files";
			path = "../../../ios/App/Pods/Target Support Files/Capacitor";
			sourceTree = "<group>";
		};
		C2185AD8DC6B264627DA31077D50DBA1 /* Plugins */ = {
			isa = PBXGroup;
			children = (
				F47E17E260BCAECBD710DEA38EE35CCC /* CapacitorCookieManager.swift */,
				CAC58A84F8B7BE0D8FAE1CACCA36739A /* CapacitorCookies.swift */,
				2DE386678C539B0C359C815458F21C21 /* CapacitorHttp.swift */,
				890A05938EAF51E0B361AF5D1F83C1DB /* CapacitorUrlRequest.swift */,
				06376241E440BC59D068F16FFE956BB4 /* Console.swift */,
				6F5CDA8547582EEB988682D47A7159A5 /* DefaultPlugins.m */,
				ECA4C90DF34492CBD0454C1916F0D836 /* HttpRequestHandler.swift */,
				CA72F52F7878008518B2EB0018D2816B /* WebView.swift */,
			);
			name = Plugins;
			path = Capacitor/Capacitor/Plugins;
			sourceTree = "<group>";
		};
		CE901059374969BB68E0A82862E1CECE /* assets */ = {
			isa = PBXGroup;
			children = (
				62314F6490651760FCAF24722F104957 /* native-bridge.js */,
			);
			name = assets;
			path = Capacitor/Capacitor/assets;
			sourceTree = "<group>";
		};
		CF1408CF629C7361332E53B88F7BD30C = {
			isa = PBXGroup;
			children = (
				9D940727FF8FB9C785EB98E56350EF41 /* Podfile */,
				31D788C32A0BE499E0000FDE0C45B082 /* Development Pods */,
				1628BF05B4CAFDCC3549A101F5A10A17 /* Frameworks */,
				3BDBF50EC94D03B79F62C5742162999F /* Products */,
				7C6D044F0CD972C5CAB37A7A0E4EE782 /* Targets Support Files */,
			);
			sourceTree = "<group>";
		};
		D436BFCF28DC3F91BC057EA7A5D1DD60 /* iOS */ = {
			isa = PBXGroup;
			children = (
				6CA66746F86BE55810CFE7AD03678EED /* Foundation.framework */,
				EDB8C9ACBBD85B912C1048908BCC00F8 /* WebKit.framework */,
			);
			name = iOS;
			sourceTree = "<group>";
		};
		DA6F85AEB9E3EBC6D9282BADBF2BD940 /* Classes */ = {
			isa = PBXGroup;
			children = (
				2B0C5543E5383F22FA8C61A5411496E9 /* Public */,
			);
			name = Classes;
			path = CapacitorCordova/CapacitorCordova/Classes;
			sourceTree = "<group>";
		};
		DC7239050AA5019801B1EF5F577088AD /* Support Files */ = {
			isa = PBXGroup;
			children = (
				AB9E24BDF1F703FBB176B95695E72CDD /* CapacitorCordova.modulemap */,
				FA84198FE2DD80BFF71F1DD2528547E3 /* CapacitorCordova-dummy.m */,
				8610A36BA9032C643686178252D013A1 /* CapacitorCordova-Info.plist */,
				5ED1C08B4DD9D6BE03603691078E4F55 /* CapacitorCordova-prefix.pch */,
				582BE78B8628846F9AE5B4DB49FD2666 /* CapacitorCordova.debug.xcconfig */,
				B6C3D6A65487081AB12082A4036DF4CA /* CapacitorCordova.release.xcconfig */,
			);
			name = "Support Files";
			path = "../../../ios/App/Pods/Target Support Files/CapacitorCordova";
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXHeadersBuildPhase section */
		0C24656CDB6DC2B743CFDC11C0ED13B8 /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = 2147483647;
			files = (
				79414E379F0F40F185D78CBE2F00EDD7 /* Capacitor.h in Headers */,
				E283743C6AE551632BF1CA4123C7D3E2 /* CAPBridgedJSTypes.h in Headers */,
				537E3799CE5EC6A72C89175FE34D148D /* CAPBridgedPlugin.h in Headers */,
				98D195B9562C05FC903187C2E4DC5AF3 /* CAPBridgeViewController+CDVScreenOrientationDelegate.h in Headers */,
				399DAAD86DDC3E2E76112004922C2766 /* CAPInstanceConfiguration.h in Headers */,
				53E87210D102416A0A80FAB50ACD83D3 /* CAPInstanceDescriptor.h in Headers */,
				2C353DDB806C9F4E6A95E60EE92D640E /* CAPPlugin.h in Headers */,
				1E0DCD5A20E4CBC197A0935086584D5A /* CAPPluginCall.h in Headers */,
				9DC22C515EB3098349A1B59B2F2B92CA /* CAPPluginMethod.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		6E200C59286E517866877E978BDD1E8D /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = 2147483647;
			files = (
				******************************** /* AppDelegate.h in Headers */,
				589EF4ADAB147A9AC7BD177F540578B0 /* CapacitorCordova.h in Headers */,
				54BAB1D0C95B9A62E9787B57AB8AC536 /* CDV.h in Headers */,
				F461ADD5B52B2FAD506BEB9156338303 /* CDVAvailability.h in Headers */,
				B6AE510BA5E025E893C38FD913011639 /* CDVCommandDelegate.h in Headers */,
				41B2A309D94A219FC9C6DE83EC12D149 /* CDVCommandDelegateImpl.h in Headers */,
				33ADA954019B2B8379813598247766EC /* CDVConfigParser.h in Headers */,
				10502F7DE7D1017FDCA7208175DD15D9 /* CDVInvokedUrlCommand.h in Headers */,
				AF84643D0E3EDDFD92AB1B18570C76B1 /* CDVPlugin.h in Headers */,
				3B531711DB6122B52F4C05735A9895BC /* CDVPlugin+Resources.h in Headers */,
				B454EE4626E107A57DCBB091D32D88E9 /* CDVPluginManager.h in Headers */,
				F3CB2898585CE47A7D157676DC14D7B5 /* CDVPluginResult.h in Headers */,
				117AD9BED875805A9EB89A6972251FA6 /* CDVScreenOrientationDelegate.h in Headers */,
				F3C678E7D36698F6CD083B5DF56319CA /* CDVURLProtocol.h in Headers */,
				A6C49C2C77A81F24ED6F60B3E4435CCD /* CDVViewController.h in Headers */,
				D046E4CE27CA3585CE9626F9938654E6 /* CDVWebViewProcessPoolFactory.h in Headers */,
				405EEA7E03B536DBF98A53AF01FFF824 /* NSDictionary+CordovaPreferences.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		F2E75D1A0C63B15E0D85A8BB6262B8AC /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = 2147483647;
			files = (
				DCFE5B79E2E68FF6A3AAA7A01995CF5E /* Pods-App-umbrella.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXHeadersBuildPhase section */

/* Begin PBXNativeTarget section */
		0ECF3D6BFCC08377AE23B027EE1D4371 /* Capacitor */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 42FF5AF43E95B0CD0B466F12B8B94C53 /* Build configuration list for PBXNativeTarget "Capacitor" */;
			buildPhases = (
				0C24656CDB6DC2B743CFDC11C0ED13B8 /* Headers */,
				0E08EE4F33AFDC3E843C0D08AE219165 /* Sources */,
				1600718E35582DB91D57ED5F9145E7C3 /* Frameworks */,
				3461ACEC0AAE5125DF3819EF28E4535B /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				A7D8DFA8555CA7273F6962837E0353F7 /* PBXTargetDependency */,
			);
			name = Capacitor;
			productName = Capacitor;
			productReference = 44D6062E8C618EC2B8F1D7229E8AD501 /* Capacitor */;
			productType = "com.apple.product-type.framework";
		};
		400AE44335852A2D8D746557E21E8EB0 /* CapacitorCordova */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = E376B2C2CD383C54EB4C24475CBA4673 /* Build configuration list for PBXNativeTarget "CapacitorCordova" */;
			buildPhases = (
				6E200C59286E517866877E978BDD1E8D /* Headers */,
				25C71C0D1D5B7245CD20B14F3F518DB2 /* Sources */,
				391177040D71C264C52B12F30B18FDDB /* Frameworks */,
				59D6446271A69BE9940F313F0E5E582D /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = CapacitorCordova;
			productName = Cordova;
			productReference = 1ABFDA3391AEBA42EFB0FEBF824A6E34 /* CapacitorCordova */;
			productType = "com.apple.product-type.framework";
		};
		5C415F33D02A6F89DB713EFB9D75A3FB /* Pods-App */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 8D05BCBF058E4A6724E143B06E8A27E8 /* Build configuration list for PBXNativeTarget "Pods-App" */;
			buildPhases = (
				F2E75D1A0C63B15E0D85A8BB6262B8AC /* Headers */,
				34FD2C3A5F36E0BE288C7B0329DA9564 /* Sources */,
				6A2E4D809C4624DEDDEA1833673B9DE4 /* Frameworks */,
				4940317B7D529EC55324E11ADF077E52 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				171FA73FE3ED43E7F9079F2D3BDA1F14 /* PBXTargetDependency */,
				4BC97CF1186DD15ED522B5A5715A8CC9 /* PBXTargetDependency */,
			);
			name = "Pods-App";
			productName = Pods_App;
			productReference = 5BEF4602752E47C46E8C10FB8B4B57F2 /* Pods-App */;
			productType = "com.apple.product-type.framework";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		BFDFE7DC352907FC980B868725387E98 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				LastSwiftUpdateCheck = 1600;
				LastUpgradeCheck = 1600;
			};
			buildConfigurationList = 4821239608C13582E20E6DA73FD5F1F9 /* Build configuration list for PBXProject "Pods" */;
			compatibilityVersion = "Xcode 8.0";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				Base,
				en,
			);
			mainGroup = CF1408CF629C7361332E53B88F7BD30C;
			minimizedProjectReferenceProxies = 0;
			preferredProjectObjectVersion = 77;
			productRefGroup = 3BDBF50EC94D03B79F62C5742162999F /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				0ECF3D6BFCC08377AE23B027EE1D4371 /* Capacitor */,
				400AE44335852A2D8D746557E21E8EB0 /* CapacitorCordova */,
				5C415F33D02A6F89DB713EFB9D75A3FB /* Pods-App */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		3461ACEC0AAE5125DF3819EF28E4535B /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				1C1591A44C89E03BD7285E4CDF7507B0 /* native-bridge.js in Resources */,
				9E0D5E29E18F0B17C8EC90A084CE7778 /* PrivacyInfo.xcprivacy in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		4940317B7D529EC55324E11ADF077E52 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		59D6446271A69BE9940F313F0E5E582D /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				843E2CE985D0A6203D1057006417118E /* PrivacyInfo.xcprivacy in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		0E08EE4F33AFDC3E843C0D08AE219165 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				961067F66821FC49DB3473A6E4B84E94 /* AppUUID.swift in Sources */,
				42E58594342FC0BF6237043C4F53BE7B /* Array+Capacitor.swift in Sources */,
				4952FB2AD73A65174074014264BEEC19 /* Capacitor-dummy.m in Sources */,
				8D3AC0C0367991F8D6F1145AE0CFFEA9 /* CapacitorBridge.swift in Sources */,
				160DDB1B498FDA3434F0609495709CEE /* CapacitorCookieManager.swift in Sources */,
				9DC210339C97CC020F47372921ED4515 /* CapacitorCookies.swift in Sources */,
				048D5F25A0BC829307C92C327E8327F6 /* CapacitorExtension.swift in Sources */,
				C30005EBEC8A238A420481DE95E5B254 /* CapacitorHttp.swift in Sources */,
				EACC4EB222690A8EA82FCCE32B0EF331 /* CapacitorUrlRequest.swift in Sources */,
				72A876E5948B92D74B3611C701CCEB7F /* CAPApplicationDelegateProxy.swift in Sources */,
				05B1023C6FDAE9FD0F512581C05A83B4 /* CAPBridge.swift in Sources */,
				323BDB40840A0CB2B8A79018B66F9DC4 /* CAPBridgeDelegate.swift in Sources */,
				CCEA3FF8FAB47DB8DCFB66322D3DFF15 /* CAPBridgedJSTypes.m in Sources */,
				13479EFCC90FF58AD01CDE4A78144DEC /* CAPBridgedPlugin+getMethod.swift in Sources */,
				D300648B6DF295685032E531A3AA0CE1 /* CAPBridgeProtocol.swift in Sources */,
				BD97C80AD69BEA86BDB34B38C93A3BEB /* CAPBridgeViewController.swift in Sources */,
				835F507A32CA5C33886433667B50C991 /* CAPBridgeViewController+CDVScreenOrientationDelegate.m in Sources */,
				184E2616B15277B8C336AE461DE3120B /* CAPFile.swift in Sources */,
				79FC484DFA6F276CF629E49E54135434 /* CAPInstanceConfiguration.m in Sources */,
				CCB4C4C5745844FFC8B825B1E88C5D20 /* CAPInstanceConfiguration.swift in Sources */,
				C4DF33BE3379E21421B319114E15061B /* CAPInstanceDescriptor.m in Sources */,
				DE7D610458280A4D690BE1FB362BCEE0 /* CAPInstanceDescriptor.swift in Sources */,
				BF2EF986989EC3148A44D2688569A292 /* CAPInstancePlugin.swift in Sources */,
				CB9E20A024AAC579D5BC41C316DB7D5E /* CAPLog.swift in Sources */,
				EACD8E483B6D3D3094F0B336E20552E8 /* CAPNotifications.swift in Sources */,
				581B691E7A4C48D2B5D0BF1B85C35356 /* CAPPlugin.m in Sources */,
				F08861E1A08953274BECA5F3F2C488B6 /* CAPPlugin+LoadInstance.swift in Sources */,
				9A7981042BD0247C666F73DDA307674E /* CAPPluginCall.m in Sources */,
				1C0C08D1F8D7204BE175E284D8374CA3 /* CAPPluginCall.swift in Sources */,
				8ACC35F45A404098A6CB0D9BA2072944 /* CAPPluginMethod.m in Sources */,
				CFB593EFB4AE4CB646963872311BD957 /* CAPPluginMethod.swift in Sources */,
				84304B5535036D3DC79FE810507502CE /* Console.swift in Sources */,
				174520D52CAF7D96D08DAC7229C1A9D2 /* Data+Capacitor.swift in Sources */,
				22C5D969A8431EB8B4AD90BAB22469D3 /* DefaultPlugins.m in Sources */,
				A6952C4E25E041CD0803140AB69202F1 /* DocLinks.swift in Sources */,
				0ED62D6A4B18BEF1218E763D644F72A2 /* HttpRequestHandler.swift in Sources */,
				1F9853A4A505AD77876819A90FC79EF3 /* JS.swift in Sources */,
				E6E4D54F839A26790C76FF5B086EAA46 /* JSExport.swift in Sources */,
				226A16ECFF4872E1A37853017E6876C4 /* JSTypes.swift in Sources */,
				CFD8F1153D96D05DB6C0456B2707F6FC /* JSValueDecoder.swift in Sources */,
				522A7B978F3543240D0D9A292A226C94 /* JSValueEncoder.swift in Sources */,
				69AEC8938C583ED3E053A55FA6F18E70 /* KeyPath.swift in Sources */,
				2EE14A75353535491AAA5BB580540E5E /* KeyValueStore.swift in Sources */,
				41021200A7A22592A5C2A37864D2E891 /* NotificationHandlerProtocol.swift in Sources */,
				C4C16EE9E04F827A52474FB8A379E813 /* NotificationRouter.swift in Sources */,
				438CAEBDB8751AF9F81C9FF322EC6809 /* PluginCallResult.swift in Sources */,
				A20710404F7DB197F5E6205BB5A63F29 /* PluginConfig.swift in Sources */,
				347FA990421572B787D061BF8006773A /* Router.swift in Sources */,
				051D26D34EF77E9280B4DAAF7201F907 /* TmpViewController.swift in Sources */,
				919E27170BC8FF6001A14D4A9A13D140 /* UIColor.swift in Sources */,
				6FAE407747D49868BC690636FE805D76 /* UIStatusBarManager+CAPHandleTapAction.m in Sources */,
				5214ABCD2A694C1BF72181686FB1717F /* WebView.swift in Sources */,
				10B5B177925E7E326DBB61DA6E358E27 /* WebViewAssetHandler.swift in Sources */,
				360977DA24B5AA5503059C5EA6456B96 /* WebViewDelegationHandler.swift in Sources */,
				72F7D1919CB887F2EE766E1347EC112D /* WKWebView+Capacitor.m in Sources */,
				7BBFA2A46B1706161DCF7246DA0EB3D9 /* WKWebView+Capacitor.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		25C71C0D1D5B7245CD20B14F3F518DB2 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				595DAE4CD8156E7FEE2652457F53042F /* AppDelegate.m in Sources */,
				8A6F6D00589BD8EA57B78C77063902FD /* CapacitorCordova-dummy.m in Sources */,
				0A0232D7A9E79A4B498D7917E22A70CC /* CDVCommandDelegateImpl.m in Sources */,
				D39A0BD0C9B25E98D3AA4648F2A42DAC /* CDVConfigParser.m in Sources */,
				C3DC227F5D040C2DD9D3D54133EBAD2E /* CDVInvokedUrlCommand.m in Sources */,
				9CE0F011BFE274075507C37C7B8928D0 /* CDVPlugin.m in Sources */,
				9C4D07DAEAE68399F2594CAFA92009A4 /* CDVPlugin+Resources.m in Sources */,
				80BFC0B4B8FF3FC417EE811D9DAD7CC0 /* CDVPluginManager.m in Sources */,
				B3AE88FB10FE19CF223B283787FCD578 /* CDVPluginResult.m in Sources */,
				C1002D0D7C277F31743E05CAEF5499BA /* CDVURLProtocol.m in Sources */,
				C6C3B156184692CC45BFDC15D6739D45 /* CDVViewController.m in Sources */,
				710096C917E692BB98E178E392042A08 /* CDVWebViewProcessPoolFactory.m in Sources */,
				7B07578247E6AEB7ABFC98F178D5A144 /* NSDictionary+CordovaPreferences.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		34FD2C3A5F36E0BE288C7B0329DA9564 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				C896023861D7B8790C651E1D61708129 /* Pods-App-dummy.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		171FA73FE3ED43E7F9079F2D3BDA1F14 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = Capacitor;
			target = 0ECF3D6BFCC08377AE23B027EE1D4371 /* Capacitor */;
			targetProxy = AAC5B4C5E2673D810C1EA0226B8C61A3 /* PBXContainerItemProxy */;
		};
		4BC97CF1186DD15ED522B5A5715A8CC9 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = CapacitorCordova;
			target = 400AE44335852A2D8D746557E21E8EB0 /* CapacitorCordova */;
			targetProxy = 42C9FB0746E2DF32F3060178595BCFB1 /* PBXContainerItemProxy */;
		};
		A7D8DFA8555CA7273F6962837E0353F7 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = CapacitorCordova;
			target = 400AE44335852A2D8D746557E21E8EB0 /* CapacitorCordova */;
			targetProxy = A23AF702DEC4D912BAF6D65BEBEF7AC2 /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		0E8C6267673702B754AE012C35A5D109 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 09209143938B2386BB3906033655559D /* Pods-App.debug.xcconfig */;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = NO;
				ARCHS = "$(ARCHS_STANDARD_64_BIT)";
				CLANG_ENABLE_OBJC_WEAK = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				INFOPLIST_FILE = "Target Support Files/Pods-App/Pods-App-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 14.0;
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks @loader_path/Frameworks";
				MACH_O_TYPE = staticlib;
				MODULEMAP_FILE = "Target Support Files/Pods-App/Pods-App.modulemap";
				OTHER_LDFLAGS = "";
				OTHER_LIBTOOLFLAGS = "";
				PODS_ROOT = "$(SRCROOT)";
				PRODUCT_BUNDLE_IDENTIFIER = "org.cocoapods.${PRODUCT_NAME:rfc1034identifier}";
				PRODUCT_NAME = "$(TARGET_NAME:c99extidentifier)";
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Debug;
		};
		4BC7450F9457737EE3E637BA155B56F7 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"POD_CONFIGURATION_DEBUG=1",
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 14.0;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				PRODUCT_NAME = "$(TARGET_NAME)";
				STRIP_INSTALLED_PRODUCT = NO;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = DEBUG;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				SYMROOT = "${SRCROOT}/../build";
			};
			name = Debug;
		};
		4C39A9CEE20CE551888338F859F99109 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 254FC3D0CF581D9256427E9CBAA97934 /* Capacitor.release.xcconfig */;
			buildSettings = {
				ARCHS = "$(ARCHS_STANDARD_64_BIT)";
				CLANG_ENABLE_OBJC_WEAK = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GCC_PREFIX_HEADER = "Target Support Files/Capacitor/Capacitor-prefix.pch";
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = "Target Support Files/Capacitor/Capacitor-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 14.0;
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks @loader_path/Frameworks";
				MODULEMAP_FILE = "Target Support Files/Capacitor/Capacitor.modulemap";
				PRODUCT_MODULE_NAME = Capacitor;
				PRODUCT_NAME = Capacitor;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_INSTALL_OBJC_HEADER = YES;
				SWIFT_VERSION = 5.1;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Release;
		};
		67FC67410E3C0C56E9C6F25FA9605030 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = B6C3D6A65487081AB12082A4036DF4CA /* CapacitorCordova.release.xcconfig */;
			buildSettings = {
				ARCHS = "$(ARCHS_STANDARD_64_BIT)";
				CLANG_ENABLE_OBJC_WEAK = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GCC_PREFIX_HEADER = "Target Support Files/CapacitorCordova/CapacitorCordova-prefix.pch";
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = "Target Support Files/CapacitorCordova/CapacitorCordova-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 14.0;
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks @loader_path/Frameworks";
				MODULEMAP_FILE = "Target Support Files/CapacitorCordova/CapacitorCordova.modulemap";
				PRODUCT_MODULE_NAME = Cordova;
				PRODUCT_NAME = Cordova;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_INSTALL_OBJC_HEADER = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Release;
		};
		6F1309A6AF5717E3D768CE9AF652B1CD /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 575150CBDD80D09F5831F0F3E92BE1F0 /* Capacitor.debug.xcconfig */;
			buildSettings = {
				ARCHS = "$(ARCHS_STANDARD_64_BIT)";
				CLANG_ENABLE_OBJC_WEAK = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GCC_PREFIX_HEADER = "Target Support Files/Capacitor/Capacitor-prefix.pch";
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = "Target Support Files/Capacitor/Capacitor-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 14.0;
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks @loader_path/Frameworks";
				MODULEMAP_FILE = "Target Support Files/Capacitor/Capacitor.modulemap";
				PRODUCT_MODULE_NAME = Capacitor;
				PRODUCT_NAME = Capacitor;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_INSTALL_OBJC_HEADER = YES;
				SWIFT_VERSION = 5.1;
				TARGETED_DEVICE_FAMILY = "1,2";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Debug;
		};
		7A998340CC742519BE78AACAAA7AE3A1 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 582BE78B8628846F9AE5B4DB49FD2666 /* CapacitorCordova.debug.xcconfig */;
			buildSettings = {
				ARCHS = "$(ARCHS_STANDARD_64_BIT)";
				CLANG_ENABLE_OBJC_WEAK = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GCC_PREFIX_HEADER = "Target Support Files/CapacitorCordova/CapacitorCordova-prefix.pch";
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = "Target Support Files/CapacitorCordova/CapacitorCordova-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 14.0;
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks @loader_path/Frameworks";
				MODULEMAP_FILE = "Target Support Files/CapacitorCordova/CapacitorCordova.modulemap";
				PRODUCT_MODULE_NAME = Cordova;
				PRODUCT_NAME = Cordova;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_INSTALL_OBJC_HEADER = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Debug;
		};
		8B5A46FF8D3C1289CDEE3BAFACABCD2A /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"POD_CONFIGURATION_RELEASE=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 14.0;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				PRODUCT_NAME = "$(TARGET_NAME)";
				STRIP_INSTALLED_PRODUCT = NO;
				SWIFT_COMPILATION_MODE = wholemodule;
				SWIFT_OPTIMIZATION_LEVEL = "-O";
				SWIFT_VERSION = 5.0;
				SYMROOT = "${SRCROOT}/../build";
			};
			name = Release;
		};
		E50ED384CA5277E3472C243E89E2EF0C /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 76836226476D35BC62A098CF501DF10B /* Pods-App.release.xcconfig */;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = NO;
				ARCHS = "$(ARCHS_STANDARD_64_BIT)";
				CLANG_ENABLE_OBJC_WEAK = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				INFOPLIST_FILE = "Target Support Files/Pods-App/Pods-App-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 14.0;
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks @loader_path/Frameworks";
				MACH_O_TYPE = staticlib;
				MODULEMAP_FILE = "Target Support Files/Pods-App/Pods-App.modulemap";
				OTHER_LDFLAGS = "";
				OTHER_LIBTOOLFLAGS = "";
				PODS_ROOT = "$(SRCROOT)";
				PRODUCT_BUNDLE_IDENTIFIER = "org.cocoapods.${PRODUCT_NAME:rfc1034identifier}";
				PRODUCT_NAME = "$(TARGET_NAME:c99extidentifier)";
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		42FF5AF43E95B0CD0B466F12B8B94C53 /* Build configuration list for PBXNativeTarget "Capacitor" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				6F1309A6AF5717E3D768CE9AF652B1CD /* Debug */,
				4C39A9CEE20CE551888338F859F99109 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		4821239608C13582E20E6DA73FD5F1F9 /* Build configuration list for PBXProject "Pods" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				4BC7450F9457737EE3E637BA155B56F7 /* Debug */,
				8B5A46FF8D3C1289CDEE3BAFACABCD2A /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		8D05BCBF058E4A6724E143B06E8A27E8 /* Build configuration list for PBXNativeTarget "Pods-App" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				0E8C6267673702B754AE012C35A5D109 /* Debug */,
				E50ED384CA5277E3472C243E89E2EF0C /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		E376B2C2CD383C54EB4C24475CBA4673 /* Build configuration list for PBXNativeTarget "CapacitorCordova" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				7A998340CC742519BE78AACAAA7AE3A1 /* Debug */,
				67FC67410E3C0C56E9C6F25FA9605030 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = BFDFE7DC352907FC980B868725387E98 /* Project object */;
}
