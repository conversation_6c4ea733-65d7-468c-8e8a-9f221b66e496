<template>
  <div
    v-if="isVisible"
    class="fixed top-0 right-0 bottom-0 left-0 z-40 flex items-center"
  >
    <!-- Dark overlay background -->
    <div class="absolute inset-0 bg-sidebar-background opacity-25"></div>
    <div
      class="relative bg-card-background text-card-text h-4/5 overflow-y-scroll w-11/12 rounded-xl p-4 flex flex-col gap-4 mx-auto lg:overflow-hidden lg:w-2/5 lg:h-3/5"
    >
      <div
        class="h-auto w-full pb-3 flex flex-row items-center justify-between border-b-2 border-light-border"
      >
        <h3 class="text-lg font-bold">{{ title }}</h3>
        <span class="cursor-pointer" @click="closeModal">
          <SvgIcon icon="closeModalIcon" />
        </span>
      </div>
      <p class="text-xs text-center">
        You can add Users from existing list or create a new user by clicking on
        <span class="text-link">“Add New User”</span> if you can not find out
        any from the existing ones.
      </p>
      <el-select
        multiple
        filterable
        remote
        reserve-keyword
        placeholder="Please enter a keyword"
        :remote-method="onSearch"
        :loading="loadingSearch"
        v-model="selectedUserIds"
      >
        <template #header v-if="!loadingSearch">
          <div class="h-auto w-fullflex flex-row items-center justify-start">
            <el-checkbox
              v-if="userList.length !== 0"
              v-model="checkAll"
              @change="handleCheckAll"
              class="h-auto w-full assign-user-select check-box-add-all py-3 px-5"
            >
              <div class="font-bold text-link">
                Add all {{ `${userList.length} members` }}
              </div>
            </el-checkbox>
          </div>
        </template>
        <el-option
          class="assign-user-select select-option px-5"
          v-for="item in userList"
          :key="item.id"
          :label="`${item.firstName} ${item.lastName}`"
          :value="item.id"
          @click="handleSelectOption(item)"
          ><div class="flex flex-col py-4 cursor-pointer">
            <div class="flex flex-row items-center gap-2">
              <img
                class="h-11 w-11 rounded-full"
                alt="Pic"
                :src="item?.avatar || '/media/avatars/blank.png'"
              />
              <div class="flex flex-col items-start group">
                <div class="font-bold text-link group-hover:text-link-hover">
                  {{ `${item?.firstName} ${item?.lastName}` }}
                </div>

                <div class="font-semibold text-link group-hover:text-link-hover">
                  {{ item?.email }}
                </div>
              </div>
            </div>
          </div></el-option
        >
      </el-select>
      <div class="h-4/5 w-full overflow-y-scroll mt-4">
        <template v-for="user in selectedUsers" :key="user?.id">
          <div
            class="h-auto w-full flex flex-row items-center justify-evenly gap-3 py-2 first:pt-0"
          >
            <img
              class="h-11 w-11 rounded-full"
              alt="Pic"
              :src="user?.avatar || '/media/avatars/blank.png'"
            />
            <div class="flex flex-col items-center">
              <div class="flex flex-col">
                <a
                  href="#"
                  class="font-bold"
                  >{{ `${user?.firstName} ${user?.lastName}` }}</a
                >

                <div class="font-semibold">
                  {{ user?.email }}
                </div>
              </div>
            </div>
            <button
              class="bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover font-semibold h-8 w-8 rounded-lg px-1.5 py-0.5"
              @click="handleDelete(user?.id ?? '')"
            >
              <span class="text-danger">
                <SvgIcon icon="trashIcon" />
              </span>
            </button>
          </div>
        </template>
      </div>

      <div class="h-auto w-full flex flex-row items-center justify-between">
        <button
          class="bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover rounded-md px-4 py-2 font-semibold"
          type="button"
          @click="onClickAddNewUser"
          :disabled="loading"
        >
          <span class="indicator-label"> Add New User </span>
        </button>
        <button
          class="bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover rounded-md px-4 py-2 font-semibold"
          type="button"
          :disabled="loading"
          @click="onClickAdd"
        >
          <span v-if="!loading" class="indicator-label"> Add </span>
          <span v-if="loading" class="indicator-progress">
            Please wait...
            <span
              class="spinner-border spinner-border-sm align-middle ms-2"
            ></span>
          </span>
        </button>
      </div>

      <UserModal
        :isVisible="isModalVisible"
        :close="closeNewUser"
        ref="userModal"
        :loadPage="afterAddNewUser"
      />
    </div>
  </div>
</template>

<script lang="ts">
import SvgIcon from "../constants/SvgIcon.vue";
import { rolesOptions } from "../constants/user";
import AlertService from "../services/AlertService";
import UserModal from "../views/user-management/overview/UserModal.vue";
import type { CheckboxValueType } from "element-plus";
import { defineComponent, ref, watch, type Ref } from "vue";

export default defineComponent({
  name: "assign-user-modal",
  components: { SvgIcon, UserModal },
  props: {
    title: {
      type: String,
      required: true,
    },
    initialAddUserValue: {
      type: Object,
      required: false,
    },
    userList: {
      type: Array<User.Info>,
      required: true,
    },
    onAdd: {
      type: Function,
      required: true,
    },
    onSearch: {
      type: Function,
      required: true,
    },
    loadingSearch: {
      type: Boolean,
      required: false,
    },
    isVisible: {
      type: Boolean,
      required: true,
    },
    close: {
      type: Function,
      required: true,
    },
  },
  setup(props) {
    const checkAll = ref(false);
    const modal = ref(false);
    const userModal: Ref<any> = ref<typeof UserModal | null>(null);
    const loading = ref<boolean>(false);
    const selectedUserIds = ref<string[]>([]);
    const selectedUsers = ref<User.Info[]>([]);
    const isModalVisible = ref(false);

    watch(selectedUserIds, (val) => {
      checkAll.value = props.userList.every((user) =>
        val.some((id) => id === user.id)
      );
    });

    watch(modal, (newValue) => {
      if (newValue === false) {
        hide();
      }
    });

    const handleSelectOption = (user: User.Info) => {
      if (selectedUsers.value.some((item) => item.id === user.id)) {
        selectedUsers.value = selectedUsers.value.filter(
          (item) => item.id !== user.id
        );
      } else {
        selectedUsers.value.push(user);
      }
    };

    const show = () => {
      modal.value = true;
    };

    const closeModal = () => {
      props.close();
      loading.value = false;
      hide();
    };

    const hide = () => {
      modal.value = false;
      selectedUsers.value = [];
      selectedUserIds.value = [];
    };

    const onClickAddNewUser = (): void => {
      if (props?.initialAddUserValue) {
        userModal?.value?.setInitialValue(props?.initialAddUserValue);
      }
      isModalVisible.value = !isModalVisible.value;
    };

    const closeNewUser = (): void => {
      isModalVisible.value = !isModalVisible.value;
    };

    const onClickAdd = (): void => {
      loading.value = true;
      props.onAdd(selectedUsers.value.map((user) => user.id));
    };

    const handleDelete = (id: string) => {
      selectedUsers.value = selectedUsers.value.filter(
        (user) => user.id !== id
      );
      selectedUserIds.value = selectedUserIds.value.filter(
        (userId) => userId !== id
      );
    };

    const handleCheckAll = (val: CheckboxValueType) => {
      if (val) {
        selectedUsers.value = Array.from(
          new Set(selectedUsers.value.concat(props.userList))
        );
        selectedUserIds.value = Array.from(
          new Set(
            selectedUserIds.value.concat(props.userList.map((item) => item.id!))
          )
        );
      } else {
        selectedUsers.value = selectedUsers.value.filter(
          (item) => !props.userList.some((user) => user.id === item.id)
        );
        selectedUserIds.value = selectedUserIds.value.filter(
          (id) => !props.userList.some((user) => user.id === id)
        );
      }
    };

    const afterAddNewUser = (newUserId: string) => {
      selectedUsers.value.push({ id: newUserId });
      onClickAdd();
      AlertService.toast(
        "Added new user Successfully!",
        "success",
        "top-right"
      );
    };

    return {
      selectedUserIds,
      checkAll,
      selectedUsers,
      modal,
      loading,
      rolesOptions,
      userModal,
      isModalVisible,
      show,
      hide,
      closeModal,
      handleDelete,
      onClickAddNewUser,
      onClickAdd,
      handleCheckAll,
      handleSelectOption,
      afterAddNewUser,
      closeNewUser,
    };
  },
});
</script>

<style>
.el-select {
  max-height: 32px !important;
}

.el-select-dropdown:has(.assign-user-select) {
  .el-select-dropdown__header {
    padding-left: 0;
    padding-right: 0;
    border-bottom: none;

    .el-checkbox__label {
      padding: 0 1.25rem;
    }

    .check-box-add-all {
      background-color: #f3f6f9;
      height: unset;
    }
  }

  .el-scrollbar {
    padding: 0 1.25rem;

    .select-option {
      height: unset !important;
    }
  }
}

.assign-user-select-wrapper {
  max-height: 200px;
}

.el-dialog {
  overflow: visible;
}

.el-dialog__header {
  border-radius: 12px 12px 0px 0px;
  border-bottom: 1px solid #f4f4f4;
}

.el-dialog__body {
  border-radius: 0px 0px 12px 12px;
}

.add-admin-description {
  font-size: 12px;
  text-align: center;
  padding: 0px 20px 10px;
}

.el-select-dropdown__item {
  line-height: unset;
}
</style>
