import{d as r,_ as s,c as n,a as e,b as a,w as m,r as d,o as i,B as l}from"./index-BmHWvWFS.js";const c=r({name:"error-500",components:{},setup(){return{}}}),p="/media/auth/500-error.png",x={class:"bg-screen-background text-card-text-dark min-h-screen max-w-screen flex flex-col items-center justify-center md:text-lg"},u={class:"mb-0"};function b(f,t,g,_,h,v){const o=d("router-link");return i(),n("div",x,[t[1]||(t[1]=e("h1",{class:"font-extrabold mb-4"},"System Error",-1)),t[2]||(t[2]=e("div",{class:"font-semibold mb-7"}," Something went wrong! Please try again later. ",-1)),t[3]||(t[3]=e("div",{class:"mb-3"},[e("img",{src:p,class:"mw-100 mh-300px theme-light-show",alt:""})],-1)),e("div",u,[a(o,{to:"/",class:"bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover rounded-md px-4 py-2 font-semibold md:px-4 md:py-3 md:text-xl"},{default:m(()=>t[0]||(t[0]=[l("Return Home")])),_:1})])])}const y=s(c,[["render",b]]);export{y as default};
