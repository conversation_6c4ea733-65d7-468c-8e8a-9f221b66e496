<template>
  <div class="drill-string">
    <div v-if="loading" class="text-center">
      <div class="spinner-border text-primary" role="status">
        <span class="sr-only">Loading...</span>
      </div>
    </div>
    <template v-else>
      <NoEntries
        v-if="drillStringList.length === 0"
        :addNew="toggleNewDrillStringModal"
      />
      <div v-else class="grid grid-cols-1 gap-4 md:grid-cols-2 md:gap-7 lg:grid-cols-3">
        <div
          v-for="item in drillStringList"
          :key="item?.id"
          class="relative h-auto w-full bg-minicard-background text-minicard-text-light flex flex-col items-center p-4 rounded-xl border-t-2 border-active my-2 first:mt-3 last:mb-5 font-semibold md:first:mt-0 md:last:mb-0 md:m-0"
        >
          <div class="h-auto w-full flex flex-col gap-3">
            <h5 class="text-xl font-bold">{{ item?.description }}</h5>
            <div
              class="flex items-center justify-between border-dashed border-b-[1px] py-3"
            >
              <span>Tool Joint OD (Outside Diameter)</span>
              <span>
                {{ `${numberWithCommas(item?.outsideDiameter)} (in)` }}
              </span>
            </div>
            <div
              class="flex items-center justify-between border-dashed border-b-[1px] py-3"
            >
              <span>Tool Joint ID (Inside Diameter)</span>
              <span>
                {{ `${numberWithCommas(item?.insideDiameter)} (in)` }}
              </span>
            </div>
            <div
              class="flex items-center justify-between border-dashed border-b-[1px] py-3"
            >
              <span>Length</span>
              <span>
                {{ `${numberWithCommas(item?.length)} (ft)` }}
              </span>
            </div>

            <div class="flex flex-wrap items-center gap-2">
              <div
                class="flex items-center gap-2 border border-gray-300 border-dashed rounded py-3 px-4"
              >
                <div>
                  {{ `${numberWithCommas(item?.outsideDiameter)} (in)` }}
                </div>
                <div class="font-semibold text-danger">OD</div>
              </div>
              <div
                class="flex items-center gap-2 border border-gray-300 border-dashed rounded py-3 px-4"
              >
                <div>
                  {{ `${numberWithCommas(item?.insideDiameter)} (in)` }}
                </div>
                <div>ID</div>
              </div>
              <div
                class="flex items-center gap-2 border border-gray-300 border-dashed rounded py-3 px-4"
              >
                <div>
                  {{ `${numberWithCommas(item?.weight)} (lb/ft)` }}
                </div>
                <div class="font-semibold text-success">Wt</div>
              </div>
            </div>
          </div>
          <div
            class="flex flex-row items-center gap-2 absolute top-0 end-0 -translate-y-1/2"
          >
            <button
              class="p-2 rounded-full bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover"
              @click="toggleEditDrillStringModal(item?.id.toString())"
            >
              <span class="svg-icon svg-icon-3">
                <SvgIcon icon="pencilIcon" />
              </span>
            </button>
            <button
              class="p-2 rounded-full bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover"
              @click="deleteDrillString(item?.id.toString())"
            >
              <span class="svg-icon svg-icon-3 text-danger">
                <SvgIcon icon="trashIcon" />
              </span>
            </button>
          </div>
        </div>
      </div>
    </template>
  </div>
  <BottomTool
    :addNew="toggleNewDrillStringModal"
    :showHelpWindow="showCustomize"
  />
  <DrillStringModal
    :isVisible="isModalVisible"
    :close="toggleNewDrillStringModal"
    ref="drillStringModal"
    :loadPage="getDrillStrings"
  />
</template>

<script lang="ts">
import NoEntries from "@/components/NoEntries.vue";
import SvgIcon from "@/constants/SvgIcon.vue";
import { numberWithCommas } from "@/utils/numberFormatter";
import AlertService from "@/services/AlertService";
import { useDrillStringStore } from "@/stores/drill-string";
import { defineComponent, inject, onMounted, ref, type Ref } from "vue";
import BottomTool from "@/components/common/BottomTool.vue";
import DrillStringModal from "./DrillStringModal.vue";
import type { Provide } from "@/types/injection-types";

export default defineComponent({
  name: "drill-string",
  components: {
    SvgIcon,
    BottomTool,
    DrillStringModal,
    NoEntries,
  },
  props: {
    showCustomize: {
      type: Function,
      required: false,
    },
  },
  setup(_props) {
    const drillStringModal: Ref<any> = ref<typeof DrillStringModal | null>(
      null
    );
    const loading = ref(false);
    const drillStringStore = useDrillStringStore();
    const drillStringList = ref<any>([]);
    const dailyReportProvide = inject<Provide.DailyReport>("dailyReport");
    const isModalVisible = ref(false);

    onMounted(() => {
      if (dailyReportProvide?.getDailyReportId()) {
        getDrillStrings();
      }
    });

    const getDrillStrings = async (
      params = {
        dailyReportId: dailyReportProvide?.getDailyReportId(),
        page: 1,
        limit: 200,
      }
    ): Promise<void> => {
      loading.value = true;

      drillStringStore.getDrillStrings({
        params: params,
        callback: {
          onSuccess: (res: any) => {
            drillStringList.value = res?.items;
          },
          onFinish: (_err: any) => {
            loading.value = false;
          },
        },
      });
    };

    const toggleNewDrillStringModal = () => {
      isModalVisible.value = !isModalVisible.value;
    };

    const toggleEditDrillStringModal = (id: string): void => {
      drillStringModal?.value?.setId(id);
      isModalVisible.value = !isModalVisible.value;
    };

    const deleteDrillString = (id: string) => {
      AlertService.deletionAlert({
        onConfirmed: () => {
          deleteDrillStringById(id);
        },
      });
    };

    const deleteDrillStringById = async (id: string): Promise<void> => {
      loading.value = true;
      drillStringStore.deleteDrillString({
        id: id,
        callback: {
          onSuccess: (_res: any) => {
            getDrillStrings();
          },
          onFinish: (_err: any) => {
            loading.value = false;
          },
        },
      });
    };

    return {
      loading,
      drillStringList,
      drillStringModal,
      isModalVisible,
      getDrillStrings,
      numberWithCommas,
      deleteDrillString,
      toggleEditDrillStringModal,
      toggleNewDrillStringModal,
    };
  },
});
</script>
