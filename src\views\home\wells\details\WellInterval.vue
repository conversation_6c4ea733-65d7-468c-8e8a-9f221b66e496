<template>
  <div class="h-auto w-full rounded-b-xl p-b-8 py-4 md:text-lg">
    <!--begin::Card header-->
    <div class="h-auto w-full flex flex-row p-4 items-center justify-between">
      <h1 class="text-lg font-bold">Interval</h1>
      <div
        class="bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover rounded-md px-4 py-2 font-semibold flex flex-row items-center gap-2 justify-between md:px-4 md:py-3 md:text-xl"
        @click="toggleNewInterval"
      >
        <SvgIcon icon="addIcon" />
        New Interval
      </div>
    </div>
    <!--end::Card header-->

    <div v-if="loading" class="text-center">
      <div class="spinner-border text-primary" role="status">
        <span class="sr-only">Loading...</span>
      </div>
    </div>

    <el-empty v-else-if="intervalList.length === 0" description="No Data" />

    <div v-else class="h-auto w-full overflow-x-scroll p-4 md:overflow-hidden">
      <!--begin::Table-->
      <table class="md:w-11/12 md:mx-auto md:text-center w-full table-fixed">
        <!--begin::Table head-->
        <thead class="font-bold">
          <tr>
            <th class="p-4 w-1/5">Interval</th>
            <th class="p-4 w-1/2">Note</th>
            <th class="p-4 w-1/3">Actions</th>
          </tr>
        </thead>
        <!--end::Table head-->

        <!--begin::Table body-->
        <tbody>
          <template v-for="(item, index) in intervalList" :key="item.id">
            <tr>
              <td class="h-auto w-1/5 p-4 text-center font-semibold">
                {{ item?.interval }}
              </td>
              <td class="h-auto w-1/2 p-4">
                <div class="max-h-30 w-full line-clamp-4 text-left">
                  {{ item?.notes }}
                </div>
              </td>
              <td class="h-auto w-1/3 p-4">
                <div class="flex items-center justify-center gap-2">
                  <button
                    type="button"
                    class="bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover font-semibold h-8 w-8 flex items-center justify-center rounded-lg pl-[7.5px] pt-[1px] md:h-10 md:w-10 md:pl-[3px] md:pt-[2px]"
                    @click="toggleEditInterval(item?.id?.toString() || '')"
                  >
                    <SvgIcon icon="newReportIcon" classname="md:h-6 md:w-6" />
                  </button>
                  <button
                    type="button"
                    class="bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover font-semibold h-8 w-8 flex items-center justify-center rounded-lg pl-[7.5px] pt-[1px] md:h-10 md:w-10 md:pl-[3px] md:pb-[3.75px]"
                    @click="deleteInterval(item?.id?.toString() || '')"
                  >
                    <span class="text-danger">
                      <SvgIcon icon="trashIcon" classname="md:h-7 md:w-7" />
                    </span>
                  </button>
                  <button
                    type="button"
                    class="bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover font-semibold h-8 w-8 flex items-center justify-center rounded-lg pl-[7.5px] pt-[1px] md:h-10 md:w-10 md:pl-[3px] md:pb-[3.75px]"
                    :disabled="index == intervalList.length - 1"
                    @click="changePosition(index, index + 1)"
                  >
                    <span class="text-icon-primary">
                      <SvgIcon icon="arrowDown" classname="md:h-7 md:w-7" />
                    </span>
                  </button>
                  <button
                    type="button"
                    class="bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover font-semibold h-8 w-8 flex items-center justify-center rounded-lg pl-[7.5px] pt-[1px] md:h-10 md:w-10 md:pl-[3px] md:pb-[3.75px]"
                    :disabled="index == 0"
                    @click="changePosition(index, index - 1)"
                  >
                    <span class="text-icon-primary">
                      <SvgIcon icon="arrowUp" classname="md:h-7 md:w-7" />
                    </span>
                  </button>
                </div>
              </td>
            </tr>
          </template>
        </tbody>
        <!--end::Table body-->
      </table>
      <!--end::Table-->
    </div>
    <div class="h-auto w-11/12 flex flex-col items-center mx-auto my-5">
      <div v-if="intervalList.length" class="font-semibold">
        {{
          `Showing ${(currentPage - 1) * 10 + 1} to ${
            intervalList.length
          } of ${totalElements} entries`
        }}
      </div>
      <TablePagination
        v-if="pageCount >= 1"
        :total-pages="pageCount"
        :total="totalElements"
        :per-page="10"
        :current-page="currentPage"
        @page-change="pageChange"
      />
    </div>
    <!--end::Card body-->
  </div>
  <IntervalModal
    :isVisible="isModalVisible"
    :close="toggleNewInterval"
    ref="intervalModal"
    :loadPage="getIntervals"
    :wellId="id"
  />
</template>

<script lang="ts">
import TablePagination from "@/components/common/TablePagination.vue";
import SvgIcon from "@/constants/SvgIcon.vue";
import AlertService from "@/services/AlertService";
import { useIntervalStore } from "@/stores/interval";
import { defineComponent, onMounted, ref, watch, type Ref } from "vue";
import IntervalModal from "./IntervalModal.vue";

export default defineComponent({
  name: "well-interval",
  props: {
    id: {
      type: String,
      required: true,
    },
  },
  components: {
    SvgIcon,
    TablePagination,
    IntervalModal,
  },
  setup(props) {
    const intervalStore = useIntervalStore();
    const intervalList = ref<Interval.Info[]>([]);
    const pageCount = ref(0);
    const totalElements = ref(0);
    const currentPage = ref(1);
    const loading = ref(false);
    const intervalModal: Ref<any> = ref<typeof IntervalModal | null>(null);
    const isModalVisible = ref(false);

    onMounted(() => {
      getIntervals();
    });

    watch(currentPage, () => {
      getIntervals();
    });

    const getIntervals = async (
      params: Interval.GetListParams = {
        wellId: props?.id,
        page: currentPage.value,
        limit: 10,
      }
    ): Promise<void> => {
      loading.value = true;

      intervalStore.getIntervals({
        params: params,
        callback: {
          onSuccess: (res: any) => {
            pageCount.value = res?.totalPage;
            totalElements.value = res?.total;
            currentPage.value = res?.page;
            intervalList.value = res?.items;
          },
          onFinish: (_err: any) => {
            loading.value = false;
          },
        },
      });
    };

    const changePosition = (oldPosition: number, newPosition: number) => {
      const tem = intervalList.value[newPosition];
      intervalList.value[newPosition] = intervalList.value[oldPosition];
      intervalList.value[oldPosition] = tem;
    };

    const pageChange = (newPage: number) => {
      currentPage.value = newPage;
    };

    const toggleNewInterval = (): void => {
      isModalVisible.value = !isModalVisible.value;
    };

    const toggleEditInterval = (id: string): void => {
      intervalModal?.value?.setId(id);
      isModalVisible.value = !isModalVisible.value;
    };

    const deleteInterval = (id: string) => {
      AlertService.deletionAlert({
        onConfirmed: () => {
          deleteIntervalById(id);
        },
      });
    };

    const deleteIntervalById = async (id: string): Promise<void> => {
      loading.value = true;

      intervalStore.deleteInterval({
        id: id,
        callback: {
          onSuccess: (_res: any) => {
            getIntervals();
          },
          onFinish: (_err: any) => {
            loading.value = false;
          },
        },
      });
    };

    return {
      changePosition,
      toggleNewInterval,
      toggleEditInterval,
      deleteInterval,
      getIntervals,
      pageChange,
      intervalList,
      pageCount,
      currentPage,
      totalElements,
      intervalModal,
      loading,
      isModalVisible,
    };
  },
});
</script>
