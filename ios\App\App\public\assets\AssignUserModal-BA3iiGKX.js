import{S as M}from"./SvgIcon-CfrWCA-H.js";import{d as q,q as c,A as N,Z as I,E as B,_ as P,c as u,l as p,o as i,a as t,b as y,t as f,r as g,B as U,a4 as j,w as k,m as V,F as S,k as $}from"./index-DalLS0_6.js";import{U as D}from"./UserModal-DfH6xbe7.js";const F=q({name:"assign-user-modal",components:{SvgIcon:M,UserModal:D},props:{title:{type:String,required:!0},initialAddUserValue:{type:Object,required:!1},userList:{type:Array,required:!0},onAdd:{type:Function,required:!0},onSearch:{type:Function,required:!0},loadingSearch:{type:Boolean,required:!1},isVisible:{type:Boolean,required:!0},close:{type:Function,required:!0}},setup(l){const o=c(!1),h=c(!1),v=c(null),m=c(!1),d=c([]),a=c([]),r=c(!1);N(d,s=>{o.value=l.userList.every(n=>s.some(b=>b===n.id))}),N(h,s=>{s===!1&&e()});const w=s=>{a.value.some(n=>n.id===s.id)?a.value=a.value.filter(n=>n.id!==s.id):a.value.push(s)},x=()=>{h.value=!0},A=()=>{l.close(),m.value=!1,e()},e=()=>{h.value=!1,a.value=[],d.value=[]},_=()=>{var s;l!=null&&l.initialAddUserValue&&((s=v==null?void 0:v.value)==null||s.setInitialValue(l==null?void 0:l.initialAddUserValue)),r.value=!r.value},L=()=>{r.value=!r.value},C=()=>{m.value=!0,l.onAdd(a.value.map(s=>s.id))};return{selectedUserIds:d,checkAll:o,selectedUsers:a,modal:h,loading:m,rolesOptions:I,userModal:v,isModalVisible:r,show:x,hide:e,closeModal:A,handleDelete:s=>{a.value=a.value.filter(n=>n.id!==s),d.value=d.value.filter(n=>n!==s)},onClickAddNewUser:_,onClickAdd:C,handleCheckAll:s=>{s?(a.value=Array.from(new Set(a.value.concat(l.userList))),d.value=Array.from(new Set(d.value.concat(l.userList.map(n=>n.id))))):(a.value=a.value.filter(n=>!l.userList.some(b=>b.id===n.id)),d.value=d.value.filter(n=>!l.userList.some(b=>b.id===n)))},handleSelectOption:w,afterAddNewUser:s=>{a.value.push({id:s}),C(),B.toast("Added new user Successfully!","success","top-right")},closeNewUser:L}}}),O={key:0,class:"fixed top-0 right-0 bottom-0 left-0 z-40 flex items-center"},E={class:"relative bg-card-background text-card-text h-4/5 overflow-y-scroll w-11/12 rounded-xl p-4 flex flex-col gap-4 mx-auto lg:overflow-hidden lg:w-2/5 lg:h-3/5"},z={class:"h-auto w-full pb-3 flex flex-row items-center justify-between border-b-2 border-light-border"},T={class:"text-lg font-bold"},Y={class:"h-auto w-fullflex flex-row items-center justify-start"},Z={class:"font-bold text-link"},G={class:"flex flex-col py-4 cursor-pointer"},H={class:"flex flex-row items-center gap-2"},J=["src"],K={class:"flex flex-col items-start group"},Q={class:"font-bold text-link group-hover:text-link-hover"},R={class:"font-semibold text-link group-hover:text-link-hover"},W={class:"h-4/5 w-full overflow-y-scroll mt-4"},X=["src"],ee={class:"flex flex-col items-center"},le={class:"flex flex-col"},te={href:"#",class:"font-bold"},oe={class:"font-semibold"},se=["onClick"],ae={class:"text-danger"},ne={class:"h-auto w-full flex flex-row items-center justify-between"},de=["disabled"],ie=["disabled"],re={key:0,class:"indicator-label"},ce={key:1,class:"indicator-progress"};function ue(l,o,h,v,m,d){const a=g("SvgIcon"),r=g("el-checkbox"),w=g("el-option"),x=g("el-select"),A=g("UserModal");return l.isVisible?(i(),u("div",O,[o[8]||(o[8]=t("div",{class:"absolute inset-0 bg-sidebar-background opacity-25"},null,-1)),t("div",E,[t("div",z,[t("h3",T,f(l.title),1),t("span",{class:"cursor-pointer",onClick:o[0]||(o[0]=(...e)=>l.closeModal&&l.closeModal(...e))},[y(a,{icon:"closeModalIcon"})])]),o[7]||(o[7]=t("p",{class:"text-xs text-center"},[U(" You can add Users from existing list or create a new user by clicking on "),t("span",{class:"text-link"},"“Add New User”"),U(" if you can not find out any from the existing ones. ")],-1)),y(x,{multiple:"",filterable:"",remote:"","reserve-keyword":"",placeholder:"Please enter a keyword","remote-method":l.onSearch,loading:l.loadingSearch,modelValue:l.selectedUserIds,"onUpdate:modelValue":o[2]||(o[2]=e=>l.selectedUserIds=e)},j({default:k(()=>[(i(!0),u(S,null,$(l.userList,e=>(i(),V(w,{class:"assign-user-select select-option px-5",key:e.id,label:`${e.firstName} ${e.lastName}`,value:e.id,onClick:_=>l.handleSelectOption(e)},{default:k(()=>[t("div",G,[t("div",H,[t("img",{class:"h-11 w-11 rounded-full",alt:"Pic",src:(e==null?void 0:e.avatar)||"/media/avatars/blank.png"},null,8,J),t("div",K,[t("div",Q,f(`${e==null?void 0:e.firstName} ${e==null?void 0:e.lastName}`),1),t("div",R,f(e==null?void 0:e.email),1)])])])]),_:2},1032,["label","value","onClick"]))),128))]),_:2},[l.loadingSearch?void 0:{name:"header",fn:k(()=>[t("div",Y,[l.userList.length!==0?(i(),V(r,{key:0,modelValue:l.checkAll,"onUpdate:modelValue":o[1]||(o[1]=e=>l.checkAll=e),onChange:l.handleCheckAll,class:"h-auto w-full assign-user-select check-box-add-all py-3 px-5"},{default:k(()=>[t("div",Z," Add all "+f(`${l.userList.length} members`),1)]),_:1},8,["modelValue","onChange"])):p("",!0)])]),key:"0"}]),1032,["remote-method","loading","modelValue"]),t("div",W,[(i(!0),u(S,null,$(l.selectedUsers,e=>(i(),u("div",{key:e==null?void 0:e.id,class:"h-auto w-full flex flex-row items-center justify-evenly gap-3 py-2 first:pt-0"},[t("img",{class:"h-11 w-11 rounded-full",alt:"Pic",src:(e==null?void 0:e.avatar)||"/media/avatars/blank.png"},null,8,X),t("div",ee,[t("div",le,[t("a",te,f(`${e==null?void 0:e.firstName} ${e==null?void 0:e.lastName}`),1),t("div",oe,f(e==null?void 0:e.email),1)])]),t("button",{class:"bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover font-semibold h-8 w-8 rounded-lg px-1.5 py-0.5",onClick:_=>l.handleDelete((e==null?void 0:e.id)??"")},[t("span",ae,[y(a,{icon:"trashIcon"})])],8,se)]))),128))]),t("div",ne,[t("button",{class:"bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover rounded-md px-4 py-2 font-semibold",type:"button",onClick:o[3]||(o[3]=(...e)=>l.onClickAddNewUser&&l.onClickAddNewUser(...e)),disabled:l.loading},o[5]||(o[5]=[t("span",{class:"indicator-label"}," Add New User ",-1)]),8,de),t("button",{class:"bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover rounded-md px-4 py-2 font-semibold",type:"button",disabled:l.loading,onClick:o[4]||(o[4]=(...e)=>l.onClickAdd&&l.onClickAdd(...e))},[l.loading?p("",!0):(i(),u("span",re," Add ")),l.loading?(i(),u("span",ce,o[6]||(o[6]=[U(" Please wait... "),t("span",{class:"spinner-border spinner-border-sm align-middle ms-2"},null,-1)]))):p("",!0)],8,ie)]),y(A,{isVisible:l.isModalVisible,close:l.closeNewUser,ref:"userModal",loadPage:l.afterAddNewUser},null,8,["isVisible","close","loadPage"])])])):p("",!0)}const pe=P(F,[["render",ue]]);export{pe as A};
