<template>
  <div
    class="bg-screen-background text-card-text-dark min-h-screen max-w-screen flex flex-col items-center justify-center text-center md:text-lg"
  >
    <!--begin::Title-->
    <h1 class="font-extrabold mb-4">Oops!</h1>
    <!--end::Title-->
    <!--begin::Text-->
    <div class="font-semibold mb-7 w-4/5">
      You don’t have any roles assigned. Please contact your admin to get
      access.
    </div>
    <!--end::Text-->
    <!--begin::Illustration-->
    <div class="mb-3">
      <img src="/media/auth/agency.png" class="w-4/5 h-auto mx-auto" alt="" />
      <!-- <img
              src="/media/auth/agency-dark.png"
              class="mw-100 mh-300px theme-dark-show"
              alt=""
            /> -->
    </div>
    <!--end::Illustration-->
    <!--begin::Link-->
    <div class="mb-0">
      <button
        type="button"
        class="bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover rounded-md px-4 py-2 font-semibold md:px-4 md:py-3 md:text-xl"
        @click="signOut"
      >
        Log out
      </button>
    </div>
    <!--end::Link-->
  </div>
</template>

<script lang="ts">
import { useAuthStore } from "@/stores/auth";
import { defineComponent } from "vue";
import { useRouter } from "vue-router";

export default defineComponent({
  name: "no-role",
  components: {},
  setup() {
    const store = useAuthStore();
    const router = useRouter();
    // const themeMode = computed(() => {
    //   return storeTheme.mode;
    // });
    // const bgImage =
    //   themeMode.value !== "dark"
    //     ? getAssetPath("media/auth/bg1.jpg")
    //     : getAssetPath("media/auth/bg1-dark.jpg");

    // onMounted(() => {
    //   LayoutService.emptyElementClassesAndAttributes(document.body);

    //   storeBody.addBodyClassname("bg-body");
    //   storeBody.addBodyAttribute({
    //     qualifiedName: "style",
    //     value: `background-image: url("${bgImage}")`,
    //   });
    // });

    const signOut = () => {
      store.logout();
      router.push({ name: "sign-in" });
    };

    return {
      signOut,
    };
  },
});
</script>
