import{d as ee,q as l,j as fe,$ as H,x as ye,A as O,J,E as Y,e as pe,_ as oe,c as r,o as a,a as t,b as c,m as z,w as N,r as m,p as Ce,l as M,B as q,H as W,M as X,t as A,F as K,k as te,n as ke,Z as Ue,a0 as Ve,a1 as Ee,U as F,a2 as we,R as De}from"./index-BmHWvWFS.js";import{C as Te}from"./CompanyModal-C5rLKitg.js";import{P as xe}from"./PageHeader-Sj9hJFB8.js";import{T as $e}from"./TablePagination-lslz6s2e.js";import{S as ge}from"./SvgIcon-DYvlNVZf.js";import{T as Ae}from"./TableHeader-DGdsllig.js";import{S as Z,a as _e}from"./table-bhK9qpe4.js";import{f as Ie}from"./date-CKteeARj.js";import{u as Ne}from"./customer-2JcOh_4Q.js";import{C as Pe}from"./CustomerModal-DQWo9a5A.js";import{u as Se}from"./user-CVSNmFaf.js";import{c as Le,a as Re}from"./index.esm-C4vtr4xS.js";import{A as Fe}from"./AssignUserModal-kk-OATZ2.js";import{u as Be}from"./company-KQxnMnUF.js";const je=ee({name:"customer-list",components:{PageHeader:xe,SvgIcon:ge,TablePagination:$e,CustomerModal:Pe,TableHeader:Ae},setup(){const e=Ne(),o=fe(),_=pe(),U=l([]),S=l(!1),C=l(0),d=l(0),y=l(1),x=l(!1),b=l(""),v=l(!1),u=l(null),V=l([]),$=l([]),p=o.name==="my-company",E=l({sortDirection:_e.ASC,sortBy:Z.CustomerName}),I=l(!1),h=l({}),n=[{label:"",class:"w-25px",display:H()},{label:"FULL NAME",sortBy:Z.CustomerName,class:"min-w-150px"},{label:"NOTE",class:"min-w-150px"},{label:"CREATED DATE",class:"min-w-150px"},{label:"ACTIONS",class:"min-w-60px",display:H()}];ye(()=>{f()}),O(U,g=>{S.value=$.value.length!==0&&g.length===$.value.length}),O(y,()=>{f()});const f=async()=>{var g,P,B;x.value=!0,e.getCustomers({params:{name:b.value.trim()||null,companyId:p?(g=J.getUserInfo())==null?void 0:g.companyId:(B=(P=o.params)==null?void 0:P.id)==null?void 0:B.toString(),page:y.value,limit:10,...E.value},callback:{onSuccess:D=>{$.value=[...D==null?void 0:D.items],C.value=D==null?void 0:D.totalPage,d.value=D==null?void 0:D.total,y.value=D==null?void 0:D.page},onFinish:()=>{x.value=!1}}})},s=g=>{E.value={...g},f()},L=()=>{y.value!==1?y.value=1:f()},se=()=>{I.value=!I.value},R=g=>{var P;I.value=!I.value,(P=u==null?void 0:u.value)==null||P.setId(g)},ne=()=>{Y.deletionAlert({onConfirmed:()=>{Q(U.value,!0)}})},G=g=>{Y.deletionAlert({onConfirmed:()=>{Q([g])}})},le=g=>{y.value=g},ae=()=>{v.value=!v.value},re=g=>{var P;(P=g==null?void 0:g.target)!=null&&P.checked?U.value=$.value.map(B=>B.id):U.value=[]},ie=g=>{_.push({path:`/users/${g}`})},de=g=>o.name==="company-detail"?`/companies/${o.params.id}/customer/${g}`:o.name==="my-company"?`/my-company/customer/${g}`:"#",Q=async(g,P=!1)=>{x.value=!0,e.removeCustomers({customerIds:g,callback:{onSuccess:B=>{P&&(U.value=[]),f()},onFinish:()=>{x.value=!1}}})};return{sortParams:E,tableHeader:n,search:b,loading:x,customerModal:u,checkedRows:U,checkAll:S,customerList:$,currentPage:y,totalElements:d,pageCount:C,isShowModal:v,searchList:V,isModalVisible:I,expandedNotes:h,pageChange:le,toggleFilter:ae,deleteCustomer:G,formatDate:Ie,view:ie,onRemove:ne,toggleNewCustomer:se,toggleEditCustomer:R,isAdmin:H,getCustomerDetailPath:de,onToggleCheckAll:re,getCompanyCustomer:f,searchCustomer:L,onSort:s,toggleNoteExpansion:g=>{h.value[g]=!h.value[g]}}}}),He={class:"bg-card-background text-card-text rounded-lg p-4 lg:w-4/5 lg:mx-auto lg:min-w-[1560px]"},qe={class:"h-auto w-full flex flex-col gap-2 items-start md:flex-row md:gap-4 md:items-center md:p-4"},Oe={class:"h-auto w-full"},Ye={class:"h-auto w-full flex flex-row justify-end gap-4"},ze={key:0,class:"text-center p-5"},Ke={key:2,class:"h-auto w-full overflow-x-scroll p-4 md:overflow-hidden"},Je={class:"md:w-11/12 md:mx-auto md:text-center"},Ze={class:"font-bold whitespace-nowrap"},Ge={class:"font-bold"},Qe={key:0},We={class:"form-check form-check-sm form-check-custom form-check-solid"},Xe={key:1,class:"p-4"},eo={key:0},oo=["value"],to={class:"w-36 p-4"},so={class:"flex flex-row items-center gap-3"},no={class:"p-4"},lo=["onClick"],ao={class:"font-semibold"},ro={key:1},io={class:"h-auto w-full flex flex-row items-center justify-evenly gap-2"},uo=["onClick"],co=["onClick"],mo={class:"text-danger"},po={class:"flex flex-col items-center my-5"},go={key:0,class:"font-semibold"};function vo(e,o,_,U,S,C){var I,h;const d=m("SvgIcon"),y=m("el-icon"),x=m("el-input"),b=m("el-form-item"),v=m("el-form"),u=m("el-empty"),V=m("TableHeader"),$=m("router-link"),p=m("TablePagination"),E=m("CustomerModal");return a(),r(K,null,[t("div",He,[t("div",qe,[o[7]||(o[7]=t("h1",{class:"text-lg font-bold"},"Customer",-1)),t("div",Oe,[c(v,{onSubmit:Ce(e.searchCustomer,["prevent"])},{default:N(()=>[c(b,{class:"mb-0"},{default:N(()=>[c(x,{placeholder:"Search",modelValue:e.search,"onUpdate:modelValue":o[0]||(o[0]=n=>e.search=n),name:"search",size:"large"},{prefix:N(()=>[c(y,{class:"el-input__icon"},{default:N(()=>[c(d,{icon:"searchIcon"})]),_:1})]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["onSubmit"])]),t("div",Ye,[e.checkedRows.length!==0&&e.isAdmin()?(a(),r("button",{key:0,class:"bg-danger rounded-md px-4 py-2 font-semibold",onClick:o[1]||(o[1]=(...n)=>e.onRemove&&e.onRemove(...n))}," Remove ")):M("",!0),e.isAdmin()?(a(),r("button",{key:1,class:"bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover rounded-md px-4 py-2 font-semibold flex flex-row items-center gap-2 justify-between",onClick:o[2]||(o[2]=(...n)=>e.toggleNewCustomer&&e.toggleNewCustomer(...n))},[c(d,{icon:"addIcon"}),o[6]||(o[6]=q(" New "))])):M("",!0)])]),e.loading?(a(),r("div",ze,o[8]||(o[8]=[t("div",{class:"spinner-border text-primary",role:"status"},[t("span",{class:"sr-only"},"Loading...")],-1)]))):e.customerList.length===0?(a(),z(u,{key:1,description:"No Data"})):(a(),r("div",Ke,[t("table",Je,[t("thead",Ze,[t("tr",Ge,[c(V,{headers:e.tableHeader,sortBy:e.sortParams.sortBy,sortDirection:e.sortParams.sortDirection,onSort:e.onSort},{customHeader:N(({header:n})=>[n.label===""?(a(),r("div",Qe,[t("div",We,[W(t("input",{class:"h-4 w-4",type:"checkbox","onUpdate:modelValue":o[3]||(o[3]=f=>e.checkAll=f),onChange:o[4]||(o[4]=(...f)=>e.onToggleCheckAll&&e.onToggleCheckAll(...f))},null,544),[[X,e.checkAll]])])])):(a(),r("div",Xe,A(n.label),1))]),_:1},8,["headers","sortBy","sortDirection","onSort"])])]),t("tbody",null,[(a(!0),r(K,null,te(e.customerList,n=>(a(),r("tr",{key:n.id,class:"font-bold my-2 text-center border-b-[1px] border-dashed border-light-border"},[e.isAdmin()?(a(),r("td",eo,[W(t("input",{class:"h-4 w-4",type:"checkbox",value:n.id,"onUpdate:modelValue":o[5]||(o[5]=f=>e.checkedRows=f)},null,8,oo),[[X,e.checkedRows]])])):M("",!0),t("td",to,[t("div",so,[c($,{to:e.getCustomerDetailPath(n.id),class:"font-semibold hover:text-primary"},{default:N(()=>[q(A(n==null?void 0:n.customerName),1)]),_:2},1032,["to"])])]),t("td",no,[t("span",{class:ke(["font-semibold line-clamp-4 hover:line-clamp-none cursor-pointer border-b-[1px]",{"line-clamp-none border-none":e.expandedNotes[n.id]}]),onClick:f=>e.toggleNoteExpansion(n.id)},A(n==null?void 0:n.notes),11,lo)]),t("td",null,[t("span",ao,A(e.formatDate(n==null?void 0:n.createdAt,"MMM DD, YYYY")),1)]),e.isAdmin()?(a(),r("td",ro,[t("div",io,[t("button",{class:"bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover font-semibold h-8 w-8 flex items-center justify-center rounded-lg pl-[7.5px] pt-[1px] md:h-10 md:w-10 md:pl-[3px] md:pt-[5px] md:pb-[3.75px]",onClick:f=>e.toggleEditCustomer(n==null?void 0:n.id)},[c(d,{icon:"newReportIcon",classname:"md:h-6 md:w-6"})],8,uo),t("button",{class:"bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover font-semibold h-8 w-8 flex items-center justify-center rounded-lg pl-[7.5px] pt-[1px] md:h-10 md:w-10 md:pl-[3px] md:pb-[6px]",onClick:f=>e.deleteCustomer(n==null?void 0:n.id)},[t("span",mo,[c(d,{icon:"trashIcon",classname:"md:h-7 md:w-7"})])],8,co)])])):M("",!0)]))),128))])])])),t("div",po,[(I=e.customerList)!=null&&I.length?(a(),r("div",go,A(`Showing ${(e.currentPage-1)*10+1} to ${(h=e.customerList)==null?void 0:h.length} of ${e.totalElements} entries`),1)):M("",!0),e.pageCount>=1?(a(),z(p,{key:1,"total-pages":e.pageCount,total:e.totalElements,"per-page":10,"current-page":e.currentPage,onPageChange:e.pageChange},null,8,["total-pages","total","current-page","onPageChange"])):M("",!0)])]),c(E,{isVisible:e.isModalVisible,close:e.toggleNewCustomer,ref:"customerModal",loadPage:e.getCompanyCustomer},null,8,["isVisible","close","loadPage"])],64)}const bo=oe(je,[["render",vo]]),ho=ee({name:"user-modal",components:{SvgIcon:ge},props:{isVisible:{type:Boolean,required:!0},close:{type:Function,required:!0},loadPage:{type:Function,required:!1},companyId:{type:String,required:!0}},setup(e){const o=Se(),_=l(!1),U=l([]),S=l(""),C=l(""),d=l([]),y=l([]),x=l(),b=Le().shape({email:Re().email("Invalid email address").required("Email is required").test("is-unique","Email is already taken",h=>!d.value.includes(h))});O(()=>e.isVisible,h=>{h===!1&&E()});const v=h=>{d.value.splice(d.value.indexOf(h),1),e.close()},u=()=>{e.close()},V=()=>{Ve(()=>{x.value.input.focus()})},$=()=>{C.value&&p(C.value)},p=h=>{S.value="",b.validate({email:h}).then(()=>{d.value.push(h),C.value=""}).catch(n=>{var f;S.value=((f=n==null?void 0:n.errors)==null?void 0:f[0])||"Invalid email address"})},E=()=>{d.value=[],y.value=[],S.value="",C.value=""};return{roles:y,loading:_,rolesOptions:Ue,options:U,inputValue:C,emails:d,InputRef:x,inputEmailError:S,sendInvitation:async()=>{if(!d.value.length){S.value="Please type Email";return}_.value=!0,o.inviteUsers({params:{emails:d.value,userRoles:y.value,companyId:e==null?void 0:e.companyId},callback:{onSuccess:h=>{e.close(),Y.toast("Invited successfully","success","top-right")},onFinish:()=>{_.value=!1}}})},handleClose:v,showInput:V,handleInputConfirm:$,reset:E,closeModal:u}}}),fo={key:0,class:"fixed top-0 right-0 bottom-0 left-0 z-40 flex items-center"},yo={class:"relative bg-card-background text-card-text h-auto w-11/12 flex flex-col items-center gap-3 mx-auto p-5 rounded-xl md:w-4/5 md:text-lg lg:w-2/5"},Co={class:"h-auto w-full flex flex-row items-center justify-between"},ko={class:"h-auto w-full flex flex-col gap-4"},wo={class:"flex flex-col gap-2"},xo={class:"flex flex-col gap-2"},$o={class:"text-end mt-4"},Ao=["disabled"],_o={key:0,class:"indicator-label"},Io={key:1,class:"indicator-progress"};function So(e,o,_,U,S,C){const d=m("SvgIcon"),y=m("el-tag"),x=m("el-input"),b=m("el-text"),v=m("el-select-v2");return e.isVisible?(a(),r("div",fo,[o[9]||(o[9]=t("div",{class:"absolute inset-0 bg-sidebar-background opacity-25"},null,-1)),t("div",yo,[t("div",Co,[o[4]||(o[4]=t("h3",{class:"text-lg font-bold"},"Send Invitation",-1)),t("span",{class:"cursor-pointer",onClick:o[0]||(o[0]=(...u)=>e.closeModal&&e.closeModal(...u))},[c(d,{icon:"closeModalIcon"})])]),o[8]||(o[8]=t("p",{class:"text-xs text-center md:text-lg"}," An email will be sent out to the employees email that you input. Your employee will need to access to the link to create their account to use the Muddy Software under your company ",-1)),t("div",ko,[t("div",wo,[o[5]||(o[5]=t("label",{class:"font-semibold"},"Emails",-1)),(a(!0),r(K,null,te(e.emails,u=>(a(),z(y,{size:"large",key:u,closable:"",class:"me-2","disable-transitions":!1,onClose:V=>e.handleClose(u)},{default:N(()=>[q(A(u),1)]),_:2},1032,["onClose"]))),128)),c(x,{ref:"InputRef",modelValue:e.inputValue,"onUpdate:modelValue":o[1]||(o[1]=u=>e.inputValue=u),size:"large",placeholder:"Enter email",onKeyup:Ee(e.handleInputConfirm,["enter"]),onBlur:e.handleInputConfirm},null,8,["modelValue","onKeyup","onBlur"]),c(b,{class:"mx-1 align-self-start",type:"danger"},{default:N(()=>[q(A(e.inputEmailError),1)]),_:1})]),t("div",xo,[o[6]||(o[6]=t("label",{class:"font-semibold"},"Roles",-1)),c(v,{size:"large",modelValue:e.roles,"onUpdate:modelValue":o[2]||(o[2]=u=>e.roles=u),options:e.rolesOptions,placeholder:"Roles",multiple:"",clearable:"",name:"roles"},null,8,["modelValue","options"])])]),t("div",$o,[t("button",{class:"bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover rounded-md px-4 py-2 font-semibold",type:"button",disabled:e.loading,onClick:o[3]||(o[3]=(...u)=>e.sendInvitation&&e.sendInvitation(...u))},[e.loading?M("",!0):(a(),r("span",_o," Send Invitation ")),e.loading?(a(),r("span",Io,o[7]||(o[7]=[q(" Please wait... "),t("span",{class:"spinner-border spinner-border-sm align-middle ms-2"},null,-1)]))):M("",!0)],8,Ao)])])])):M("",!0)}const Mo=oe(ho,[["render",So]]),ue=[{value:F.CompanyAdmin,key:"Admins",titleModal:"Add Company Admins",initialAddUserValue:{roles:[F.CompanyAdmin]}},{value:F.Supervisor,key:"Supervisors",titleModal:"Add Company Supervisors",initialAddUserValue:{roles:[F.Supervisor]}},{value:F.Engineer,key:"Engineers",titleModal:"Add Company Engineers",initialAddUserValue:{roles:[F.Engineer]}},{value:5,key:we.Customer,titleModal:"Add Customer"}],Uo=ee({name:"user-list",components:{PageHeader:xe,SvgIcon:ge,TablePagination:$e,AssignUserModal:Fe,TableHeader:Ae},props:{listType:{type:Number,required:!0}},setup(e){const o=fe(),_=Be(),U=Se(),S=pe(),C=l([]),d=l(!1),y=l(0),x=l(0),b=l(1),v=l(!1),u=l(!1),V=l(""),$=l(null),p=l(!1),E=l([]),I=l(ue.find(i=>i.value===e.listType)),h=l([]),n=o.name==="my-company",f=l({sortDirection:_e.ASC,sortBy:Z.Name}),s=l(!1),L=[{label:"",class:"w-25px",display:H()},{label:"FULL NAME",sortBy:Z.Name,class:"min-w-150px"},{label:"MOBILE NUMBER",class:"min-w-120px"},{label:"OFFICE NUMBER",class:"min-w-120px"},{label:"ADDED DATE",sortBy:Z.AssignedDate,class:"min-w-120px"},{label:"ACTIONS",class:"min-w-60px",display:H()}];ye(()=>{R(),G()});const se=De(()=>{var i,k,T,w,j;return{companyId:n?(i=J.getUserInfo())==null?void 0:i.companyId:(T=(k=o.params)==null?void 0:k.id)==null?void 0:T.toString(),userRoles:((j=(w=I==null?void 0:I.value)==null?void 0:w.initialAddUserValue)==null?void 0:j.roles)||[]}});O(e,i=>{I.value=ue.find(k=>k.value===i.listType),C.value=[],V.value="",b.value=1,R()}),O(C,i=>{d.value=h.value.length!==0&&i.length===h.value.length}),O(b,()=>{R()});const R=async()=>{var i,k,T;v.value=!0,_.getCompanyUsers({params:{name:V.value.trim()||null,companyId:n?(i=J.getUserInfo())==null?void 0:i.companyId:(T=(k=o.params)==null?void 0:k.id)==null?void 0:T.toString(),role:e.listType,page:b.value,limit:10,...f.value},callback:{onSuccess:w=>{h.value=[...w==null?void 0:w.items],y.value=w==null?void 0:w.totalPage,x.value=w==null?void 0:w.total,b.value=w==null?void 0:w.page},onFinish:()=>{v.value=!1}}})},ne=i=>{f.value={...i},R()},G=async(i="")=>{var k,T,w;p.value=!0,U.getUsers({params:{name:i.trim()||null,companyId:n?(k=J.getUserInfo())==null?void 0:k.companyId:(w=(T=o.params)==null?void 0:T.id)==null?void 0:w.toString(),page:1,limit:500},callback:{onSuccess:j=>{E.value=[...j==null?void 0:j.items]},onFinish:()=>{p.value=!1}}})},le=()=>{s.value=!s.value},ae=()=>{s.value=!s.value},re=i=>{b.value=i},ie=()=>{u.value=!u.value},de=i=>{var k;(k=i==null?void 0:i.target)!=null&&k.checked?C.value=h.value.map(T=>T.id):C.value=[]},Q=i=>{S.push({path:`/users/${i}`})},ve=i=>{var k;Me(i),(k=$==null?void 0:$.value)==null||k.closeModal()},g=i=>{G(i)},P=()=>{b.value!==1?b.value=1:R()},B=()=>{Y.deletionAlert({onConfirmed:()=>{be(C.value,!0)}})},D=i=>{Y.deletionAlert({onConfirmed:()=>{be([i])}})},be=async(i,k=!1)=>{v.value=!0,_.removeUserListOfCompany({params:{userIds:i,role:e.listType},callback:{onSuccess:T=>{k&&(C.value=[]),R()},onFinish:()=>{v.value=!1}}})},Me=async i=>{var k,T,w;v.value=!0,_.addUserListToCompany({params:{companyId:n?(k=J.getUserInfo())==null?void 0:k.companyId:(w=(T=o.params)==null?void 0:T.id)==null?void 0:w.toString(),role:e.listType,userIds:i},callback:{onSuccess:j=>{R(),Y.toast("Added successfully","success","top-right")},onFinish:()=>{v.value=!1}}})};return{sortParams:f,tableHeader:L,search:V,loading:v,checkedRows:C,checkAll:d,userList:h,currentPage:b,totalElements:x,pageCount:y,isShowModal:u,addUserModal:$,userListForAddUserModal:E,tabs:ue,currentTab:I,initialAddUserValue:se,loadingUserListForAddUserModal:p,isModalVisible:s,onSort:ne,pageChange:re,toggleFilter:ie,deleteUser:D,toggleNewUser:le,formatDate:Ie,view:Q,onAdd:ve,onSearchForAddUserModal:g,onRemove:B,isAdmin:H,onToggleCheckAll:de,searchUser:P,toggleModal:ae}}}),Vo={class:"bg-card-background text-card-text rounded-lg p-4 lg:w-4/5 lg:mx-auto lg:min-w-[1560px]"},Eo={class:"h-auto w-full flex flex-col gap-2 items-start md:flex-row md:gap-4 md:items-center md:p-4"},Do={class:"text-lg font-bold whitespace-nowrap"},To={class:"h-auto w-full"},No={class:"h-auto w-full flex flex-row justify-end gap-4"},Po={key:0,class:"text-center p-5"},Lo={key:2,class:"h-auto w-full overflow-x-scroll p-4 md:overflow-hidden"},Ro={class:"md:w-11/12 md:mx-auto md:text-center"},Fo={class:"font-bold whitespace-nowrap"},Bo={key:0},jo={key:1,class:"p-4"},Ho={key:0},qo=["value"],Oo={class:"w-36 p-4"},Yo={class:"flex flex-row items-center gap-3"},zo=["src"],Ko={class:"flex flex-col text-start truncate"},Jo={class:"font-semibold"},Zo={class:"font-semibold"},Go={class:"font-semibold"},Qo={class:"font-semibold"},Wo={key:1},Xo={class:"h-auto w-full flex flex-row items-center justify-evenly gap-2"},et=["onClick"],ot=["onClick"],tt={class:"text-danger"},st={class:"flex flex-col items-center my-5"},nt={key:0,class:"font-semibold"};function lt(e,o,_,U,S,C){var I,h,n,f;const d=m("SvgIcon"),y=m("el-icon"),x=m("el-input"),b=m("el-form-item"),v=m("el-form"),u=m("el-empty"),V=m("TableHeader"),$=m("router-link"),p=m("TablePagination"),E=m("AssignUserModal");return a(),r(K,null,[t("div",Vo,[t("div",Eo,[t("h1",Do,A(`Company ${(I=e.currentTab)==null?void 0:I.key}`),1),t("div",To,[c(v,{onSubmit:Ce(e.searchUser,["prevent"])},{default:N(()=>[c(b,{class:"m-0"},{default:N(()=>[c(x,{placeholder:"Search",modelValue:e.search,"onUpdate:modelValue":o[0]||(o[0]=s=>e.search=s),name:"search",size:"large"},{prefix:N(()=>[c(y,{class:"el-input__icon"},{default:N(()=>[c(d,{icon:"searchIcon"})]),_:1})]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["onSubmit"])]),t("div",No,[e.checkedRows.length!==0&&e.isAdmin()?(a(),r("button",{key:0,class:"bg-danger rounded-md px-4 py-2 font-semibold",onClick:o[1]||(o[1]=(...s)=>e.onRemove&&e.onRemove(...s))}," Remove ")):M("",!0),e.isAdmin()?(a(),r("button",{key:1,class:"bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover rounded-md px-4 py-2 font-semibold flex flex-row items-center gap-2 justify-between md:px-4 md:py-3 md:text-xl",onClick:o[2]||(o[2]=(...s)=>e.toggleNewUser&&e.toggleNewUser(...s))},[c(d,{icon:"addIcon"}),o[6]||(o[6]=q(" Add "))])):M("",!0)])]),e.loading?(a(),r("div",Po,o[7]||(o[7]=[t("div",{class:"spinner-border text-primary",role:"status"},[t("span",{class:"sr-only"},"Loading...")],-1)]))):e.userList.length===0?(a(),z(u,{key:1,description:"No Data"})):(a(),r("div",Lo,[t("table",Ro,[t("thead",null,[t("tr",Fo,[c(V,{headers:e.tableHeader,sortBy:e.sortParams.sortBy,sortDirection:e.sortParams.sortDirection,onSort:e.onSort},{customHeader:N(({header:s})=>[s.label===""?(a(),r("div",Bo,[W(t("input",{class:"h-4 w-4",type:"checkbox","onUpdate:modelValue":o[3]||(o[3]=L=>e.checkAll=L),onChange:o[4]||(o[4]=(...L)=>e.onToggleCheckAll&&e.onToggleCheckAll(...L))},null,544),[[X,e.checkAll]])])):(a(),r("div",jo,A(s.label),1))]),_:1},8,["headers","sortBy","sortDirection","onSort"])])]),t("tbody",null,[(a(!0),r(K,null,te(e.userList,s=>(a(),r("tr",{key:s.id,class:"font-bold my-2 text-center border-b-[1px] border-light-border border-dashed"},[e.isAdmin()?(a(),r("td",Ho,[W(t("input",{class:"h-4 w-4",type:"checkbox",value:s.id,"onUpdate:modelValue":o[5]||(o[5]=L=>e.checkedRows=L)},null,8,qo),[[X,e.checkedRows]])])):M("",!0),t("td",Oo,[t("div",Yo,[t("img",{class:"h-11 w-11 rounded-full",src:(s==null?void 0:s.avatar)||"/media/avatars/blank.png",alt:""},null,8,zo),t("div",Ko,[c($,{to:`/users/${s==null?void 0:s.id}`,class:"text-link hover:text-link-hover font-semibold"},{default:N(()=>[q(A(`${s==null?void 0:s.firstName} ${s==null?void 0:s.lastName}`),1)]),_:2},1032,["to"]),t("span",Jo,A(s==null?void 0:s.email),1)])])]),t("td",null,[t("span",Zo,A(s==null?void 0:s.mobilePhone),1)]),t("td",null,[t("span",Go,A(s==null?void 0:s.officePhone),1)]),t("td",null,[t("span",Qo,A(e.formatDate(s==null?void 0:s.assignedDate,"MMM DD, YYYY")),1)]),e.isAdmin()?(a(),r("td",Wo,[t("div",Xo,[t("button",{class:"bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover font-semibold h-8 w-8 flex items-center justify-center rounded-lg pl-[7.5px] pt-[1px] md:h-10 md:w-10 md:pl-[3px] md:pt-[5px] md:pb-[3.75px]",onClick:L=>e.view((s==null?void 0:s.id)??""),"item.addedDate":""},[c(d,{icon:"eyeIcon",classname:"md:h-6 md:w-6"})],8,et),t("button",{class:"bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover font-semibold h-8 w-8 flex items-center justify-center rounded-lg pl-[7.5px] pt-[1px] md:h-10 md:w-10 md:pl-[3px] md:pb-[6px]",onClick:L=>e.deleteUser(s==null?void 0:s.id)},[t("span",tt,[c(d,{icon:"trashIcon",classname:"md:h-7 md:w-7"})])],8,ot)])])):M("",!0)]))),128))])])])),t("div",st,[(h=e.userList)!=null&&h.length?(a(),r("div",nt,A(`Showing ${(e.currentPage-1)*10+1} to ${(n=e.userList)==null?void 0:n.length} of ${e.totalElements} entries`),1)):M("",!0),e.pageCount>=1?(a(),z(p,{key:1,"total-pages":e.pageCount,total:e.totalElements,"per-page":10,"current-page":e.currentPage,onPageChange:e.pageChange},null,8,["total-pages","total","current-page","onPageChange"])):M("",!0)])]),c(E,{ref:"addUserModal",title:((f=e.currentTab)==null?void 0:f.titleModal)||"",userList:e.userListForAddUserModal,onAdd:e.onAdd,onSearch:e.onSearchForAddUserModal,initialAddUserValue:e.initialAddUserValue,loadingSearch:e.loadingUserListForAddUserModal,isVisible:e.isModalVisible,close:e.toggleModal},null,8,["title","userList","onAdd","onSearch","initialAddUserValue","loadingSearch","isVisible","close"])],64)}const at=oe(Uo,[["render",lt]]),he=[{value:F.CompanyAdmin,key:"Administration"},{value:F.Supervisor,key:"Supervisors"},{value:F.Engineer,key:"Engineers"},{value:we.Customer,key:"Customer"}],ce=l(!1),me=l(!1),rt=ee({name:"company-detail",components:{UserList:at,CompanyModal:Te,InvitationModal:Mo,CustomerList:bo},props:{companyDetail:{type:Object,required:!0},reloadCompanyData:{type:Function,required:!0}},setup(e){const o=pe(),_=l(null),U=l(null),S=l(he[0].value);return{companyModal:_,invitationModal:U,tabs:he,tabIndex:S,isCompanyModalVisible:ce,isInvitationModalVisible:me,back:()=>{o.go(-1)},setActiveTab:b=>{const v=b.target;S.value=Number(v.getAttribute("data-tab-index"))},toggleEditCompany:()=>{var b,v;(v=_==null?void 0:_.value)==null||v.setId((b=e==null?void 0:e.companyDetail)==null?void 0:b.id),ce.value=!ce.value},toggleInvitation:()=>{me.value=!me.value},isAdmin:H}}}),it={class:"h-auto w-11/12 mx-auto my-4 flex flex-col items-center gap-3"},dt={class:"bg-card-background text-card-text rounded-xl h-auto w-full p-4 lg:w-4/5 lg:min-w-[1560px]"},ut={class:"h-auto w-full py-4 flex flex-row items-center justify-start gap-3 overflow-x-scroll md:overflow-hidden"},ct=["data-tab-index"],mt={class:"pb-4 flex flex-col gap-2 items-start border-b-2 border-solid border-light-border"},pt={key:0,class:"h-auto w-full flex flex-row justify-between items-center md:justify-start md:gap-4"},gt={class:"p-4 text-sm"},vt={class:"flex flex-col gap-2 md:flex-row md:justify-evenly"},bt={class:"hover:text-card-text-hover"},ht={class:"hover:text-card-text-light"},ft={class:"hover:text-card-text-hover"},yt={key:0,class:"h-auto w-full"},Ct={key:1,class:"h-auto w-full"};function kt(e,o,_,U,S,C){var v,u,V,$;const d=m("UserList"),y=m("CustomerList"),x=m("CompanyModal"),b=m("InvitationModal");return a(),r("div",it,[t("div",dt,[t("ul",ut,[(a(!0),r(K,null,te(e.tabs,p=>(a(),r("li",{key:p.value},[t("div",{class:ke(["cursor-pointer font-semibold hover:text-active-hover hover:border-b-2 hover:border-active-border-hover",{active:e.tabIndex===p.value,"text-active border-b-2 border-active-border":e.tabIndex===(p==null?void 0:p.value),"text-inactive border-b-2 border-inactive-border":e.tabIndex!==(p==null?void 0:p.value)}]),onClick:o[0]||(o[0]=E=>e.setActiveTab(E)),"data-tab-index":p.value,role:"tab"},A(p.key),11,ct)]))),128))]),t("div",mt,[o[4]||(o[4]=t("h1",{class:"font-bold text-lg"},"Company Overview",-1)),e.isAdmin()?(a(),r("div",pt,[t("button",{class:"bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover rounded-md px-4 py-2 font-semibold",onClick:o[1]||(o[1]=(...p)=>e.toggleInvitation&&e.toggleInvitation(...p))}," Send Invitation "),t("button",{class:"bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover rounded-md px-4 py-2 font-semibold",onClick:o[2]||(o[2]=(...p)=>e.toggleEditCompany&&e.toggleEditCompany(...p))}," Edit "),t("button",{type:"button",class:"bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover rounded-md px-4 py-2 font-semibold",onClick:o[3]||(o[3]=(...p)=>e.back&&e.back(...p))}," Back ")])):M("",!0)]),t("div",gt,[t("div",vt,[t("div",bt,[o[5]||(o[5]=t("h4",{class:"text-md font-bold"},"Company ID",-1)),t("p",null,A(((v=e.companyDetail)==null?void 0:v.id)||""),1)]),t("div",null,[o[6]||(o[6]=t("h4",{class:"text-md font-bold"},"Company Name",-1)),t("p",ht,A(((u=e.companyDetail)==null?void 0:u.name)||""),1)]),t("div",null,[o[7]||(o[7]=t("h4",{class:"text-md font-bold"},"Description",-1)),t("p",ft,A(((V=e.companyDetail)==null?void 0:V.description)||""),1)])])])]),e.tabIndex!==e.tabs[3].value?(a(),r("div",yt,[c(d,{listType:e.tabIndex},null,8,["listType"])])):(a(),r("div",Ct,[c(y)])),c(x,{isVisible:e.isCompanyModalVisible,close:e.toggleEditCompany,ref:"companyModal",loadPage:e.reloadCompanyData},null,8,["isVisible","close","loadPage"]),($=e.companyDetail)!=null&&$.id?(a(),z(b,{key:2,isVisible:e.isInvitationModalVisible,close:e.toggleInvitation,ref:"invitationModal",companyId:e.companyDetail.id},null,8,["isVisible","close","companyId"])):M("",!0)])}const Pt=oe(rt,[["render",kt]]);export{Pt as C};
