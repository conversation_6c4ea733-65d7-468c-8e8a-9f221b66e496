<template>
  <div class="flex flex-col gap-3 text-card-text-light md:text-lg md:gap-5">
    <div class="flex flex-row flex-wrap items-center justify-between">
      <span class="font-bold text-lg">Sample from:</span>
      <span class="font-light">{{
        getOption(sample?.sampleFrom, sampleFromOptions)?.label || ""
      }}</span>
    </div>
    <div class="flex flex-row flex-wrap items-center justify-between">
      <span class="font-bold">Time:</span>
      <span class="font-light">
        {{ formatTime(sample?.timeSampleTaken) }}
      </span>
    </div>
    <div class="flex flex-row flex-wrap items-center justify-between">
      <span class="font-bold">MW (ppg or lbs/gal):</span>
      <span class="font-light">{{
        sample?.weightedMud ? "Active" : "Inactive"
      }}</span>
    </div>
    <div class="flex flex-row flex-wrap items-center justify-between">
      <span class="font-bold">Funnel Viscosity (sec/qt):</span>
      <span class="font-light">{{
        numberWithCommas(sample?.funnelViscosity)
      }}</span>
    </div>
    <div class="flex flex-row flex-wrap items-center justify-between">
      <span class="font-bold">PV (Plastic Viscosity) (cP):</span>
      <span class="font-light">{{
        numberWithCommas(sample?.plasticViscosity)
      }}</span>
    </div>
    <div class="flex flex-row flex-wrap items-center justify-between">
      <span class="font-bold">YP (Yield Point) (lbf/100ft2): </span>
      <span class="font-light">{{ numberWithCommas(sample?.yieldPoint) }}</span>
    </div>
    <div class="flex flex-row flex-wrap items-center justify-between">
      <span class="font-bold">6 rpm:</span>
      <span class="font-light">{{ numberWithCommas(sample?.shearRate6) }}</span>
    </div>
    <div class="flex flex-row flex-wrap items-center justify-between">
      <span class="font-bold">API filtrate (ml/30min):</span>
      <span class="font-light">{{
        numberWithCommas(sample?.apiFiltrate)
      }}</span>
    </div>
    <div class="flex flex-row flex-wrap items-center justify-between">
      <span class="font-bold">API Cake: </span>
      <span class="font-light">{{
        numberWithCommas(sample?.apiCakeThickness)
      }}</span>
    </div>
    <div class="flex flex-row flex-wrap items-center justify-between">
      <span class="font-bold">pH:</span>
      <span class="font-light">{{ numberWithCommas(sample?.pH) }}</span>
    </div>
    <div class="flex flex-row flex-wrap items-center justify-between">
      <span class="font-bold">Mud Alkalinity (Pm) (ml): </span>
      <span class="font-light">{{
        numberWithCommas(sample?.mudAlkalinity)
      }}</span>
    </div>
    <div class="flex flex-row flex-wrap items-center justify-between">
      <span class="font-bold">Filtrate Alkalinity (Pf) (ml):</span>
      <span class="font-light">{{
        numberWithCommas(sample?.filtrateAlkalinity)
      }}</span>
    </div>
    <div class="flex flex-row flex-wrap items-center justify-between">
      <span class="font-bold">Chlorides (mg/L):</span>
      <span class="font-light">{{ numberWithCommas(sample?.chlorides) }}</span>
    </div>
    <div class="flex flex-row flex-wrap items-center justify-between">
      <span class="font-bold">Total Hardness (mg/L):</span>
      <span class="font-light">{{
        numberWithCommas(sample?.totalHardness)
      }}</span>
    </div>
    <div class="flex flex-row flex-wrap items-center justify-between">
      <span class="font-bold">Linear Gel Strength (LGS) (%):</span>
      <span class="font-light">{{
        numberWithCommas(sample?.linearGelStrengthPercent)
      }}</span>
    </div>
  </div>
  <h3 class="font-bold text-lgpy-3">Cost</h3>
  <div class="flex flex-row flex-wrap items-center justify-between">
    <span class="font-bold">Daily Cost:</span>
    <span class="font-light">{{
      numberWithCommas(costSummary?.dailyCost)
    }}</span>
  </div>
  <div class="flex flex-row flex-wrap items-center justify-between">
    <span class="font-bold">Cumulative Cost:</span>
    <span class="font-light">{{
      numberWithCommas(costSummary?.cumulativeCost)
    }}</span>
  </div>
  <div>
    <p class="font-semibold">Rheological Properties</p>
    <apexchart
      ref="chartRef"
      type="area"
      :options="chart"
      :series="series"
    ></apexchart>
  </div>
  <div class="flex flex-col gap-3 md:text-lg md:gap-5">
    <h3 class="font-bold text-lg py-3">Volumes</h3>
    <div class="d-flex flex-wrap align-items-center justify-content-between">
      <span class="font-bold">Mud Lease/Consignment Volume (bbl)</span>
      <span class="font-light">{{
        numberWithCommas(sample?.mudLeaseOrConsignmentVolume)
      }}</span>
    </div>
    <div class="d-flex flex-wrap align-items-center justify-content-between">
      <span class="font-bold">Mud Volume on Location (bbl)</span>
      <span class="font-light">{{
        numberWithCommas(sample?.mudVolumeOnLocation)
      }}</span>
    </div>
    <div class="d-flex flex-wrap align-items-center justify-content-between">
      <span class="font-bold">Mud Volume in Storage (bbl)</span>
      <span class="font-light">{{
        numberWithCommas(sample?.mudVolumeInStorage)
      }}</span>
    </div>
    <div class="d-flex flex-wrap align-items-center justify-content-between">
      <span class="font-bold">Over/Under Consigned Volume (bbl)</span>
      <span class="font-light">{{
        numberWithCommas(sample?.overOrUnderConsignedVolume)
      }}</span>
    </div>
    <div class="d-flex flex-wrap align-items-center justify-content-between">
      <span class="font-bold">Total On Location (gal)</span>
      <span class="font-light">{{
        numberWithCommas(sample?.totalOnLocation)
      }}</span>
    </div>
    <div class="d-flex flex-wrap align-items-center justify-content-between">
      <span class="font-bold">Daily Usage (gal)</span>
      <span class="font-light">{{
        numberWithCommas(sample?.dailyUsage)
      }}</span>
    </div>
    <div class="d-flex flex-wrap align-items-center justify-content-between">
      <span class="font-bold">Total Usage (gal)</span>
      <span class="font-light">{{
        numberWithCommas(sample?.totalUsage)
      }}</span>
    </div>
    <div class="d-flex flex-wrap align-items-center justify-content-between">
      <span class="font-bold">Total Daily Dilution (bbl)</span>
      <span class="font-light">{{
        numberWithCommas(sample?.totalDailyDilution)
      }}</span>
    </div>
    <div class="d-flex flex-wrap align-items-center justify-content-between">
      <span class="font-bold">New Hole Volume (bbl)</span>
      <span class="font-light">{{
        numberWithCommas(sample?.newHoleVolume)
      }}</span>
    </div>
    <div class="d-flex flex-wrap align-items-center justify-content-between">
      <span class="font-bold">Dilution / New Hole Ratio</span>
      <span class="font-light">{{
        numberWithCommas(sample?.dilutionOrNewHoleRatio)
      }}</span>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, onMounted, onBeforeMount } from "vue";
import SvgIcon from "@/constants/SvgIcon.vue";
import { formatDate, formatTime } from "@/utils/date";
import { numberWithCommas } from "@/utils/numberFormatter";
import { getCSSVariableValue } from "@/utils/getCSSVariable";
import type { ApexOptions } from "apexcharts";
import type VueApexCharts from "vue3-apexcharts";
import { getOption } from "@/utils/option";
import { sampleFromOptions } from "@/constants/sample";

export default defineComponent({
  name: "sample-info",
  components: { SvgIcon },
  props: {
    sample: {
      type: Object,
      required: true,
    },
    costSummary: {
      type: Object,
      required: true,
    },
  },
  setup() {
    const sampleData = ref();
    const chartRef = ref<typeof VueApexCharts | null>(null);
    let chart: ApexOptions = {};

    const series = [
      {
        name: "Shear Stress",
        data: [30, 40, 40, 90, 90, 70, 70],
      },
    ];

    onBeforeMount(() => {
      Object.assign(chart, chartOptions());
    });

    onMounted(() => {});

    return {
      chart,
      series,
      chartRef,
      sampleData,
      formatDate,
      getOption,
      formatTime,
      numberWithCommas,
      sampleFromOptions,
    };
  },
});

const chartOptions = (): ApexOptions => {
  const labelColor = getCSSVariableValue("--color-label-color");
  const borderColor = getCSSVariableValue("--color-border-color");
  const baseColor = getCSSVariableValue("--color-base-color");
  const lightColor = getCSSVariableValue("--color-light-color");

  return {
    chart: {
      fontFamily: "inherit",
      type: "area",
      height: 350,
      toolbar: {
        show: false,
      },
    },
    plotOptions: {},
    legend: {
      show: false,
    },
    dataLabels: {
      enabled: false,
    },
    fill: {
      type: "solid",
      opacity: 1,
    },
    stroke: {
      curve: "smooth",
      show: true,
      width: 3,
      colors: [baseColor],
    },
    xaxis: {
      categories: ["1", "2", "3", "4", "5", "6", "7"],
      axisBorder: {
        show: false,
      },
      axisTicks: {
        show: false,
      },
      labels: {
        style: {
          colors: labelColor,
          fontSize: "12px",
        },
      },
      crosshairs: {
        position: "front",
        stroke: {
          color: baseColor,
          width: 1,
          dashArray: 3,
        },
      },
      tooltip: {
        enabled: false,
      },
      title: {
        text: "Shear Rate",
      },
    },
    yaxis: {
      labels: {
        style: {
          colors: labelColor,
          fontSize: "12px",
        },
      },
      title: {
        text: "Shear Stress",
      },
    },
    states: {
      hover: {
        filter: {
          type: "none",
        },
      },
      active: {
        allowMultipleDataPointsSelection: false,
        filter: {
          type: "none",
        },
      },
    },
    tooltip: {
      style: {
        fontSize: "12px",
      },
      y: {
        formatter: function (val) {
          return val.toString();
        },
      },
    },
    colors: [lightColor],
    grid: {
      borderColor: borderColor,
      strokeDashArray: 4,
      yaxis: {
        lines: {
          show: true,
        },
      },
    },
    markers: {
      strokeColors: baseColor,
      strokeWidth: 3,
    },
  };
};
</script>
