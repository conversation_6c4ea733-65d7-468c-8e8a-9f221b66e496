import{P as j}from"./PageHeader-Sj9hJFB8.js";import{T as q}from"./TablePagination-lslz6s2e.js";import{S as z}from"./SvgIcon-DYvlNVZf.js";import{S as U,a as _}from"./table-bhK9qpe4.js";import{f as G}from"./date-CKteeARj.js";import{d as J,q as r,x as K,A as B,R as Q,L as W,E as X,_ as Z,c as m,b as a,a as e,m as R,w as y,p as oo,B as T,F as H,k as eo,l as E,t as C,r as d,o as c}from"./index-BmHWvWFS.js";import{u as to}from"./company-KQxnMnUF.js";import{C as so}from"./CompanyModal-C5rLKitg.js";import"./handleFailure-WBgBpurp.js";const no=J({name:"companies-overview",components:{PageHeader:j,SvgIcon:z,TablePagination:q,CompanyModal:so},setup(){const t=to(),n=["Companies","Overview"],w=r([]),I=r(!1),$=r(0),V=r(0),u=r(1),l=r(!1),k=r(""),v=r(null),h=r(!1),f=r([]),i=r(_.ASC);K(()=>{g()}),B(u,()=>{g()}),B(i,()=>{g()});const x=Q(()=>i.value===_.ASC?"sortArrowAsc":"sortArrowDesc"),L=()=>{i.value===_.ASC?i.value=_.DESC:i.value=_.ASC},g=async()=>{l.value=!0,t.getCompanies({params:{sortBy:U.Name,sortDirection:i.value,page:u.value,limit:10,keyword:k.value.trim()||null},callback:{onSuccess:s=>{f.value=[...s==null?void 0:s.items],$.value=s==null?void 0:s.totalPage,V.value=s==null?void 0:s.total,u.value=s==null?void 0:s.page},onFinish:()=>{l.value=!1}}})},S=()=>{u.value=1,g()},A=s=>{u.value=s},o=()=>{h.value=!h.value},P=s=>{var p;(p=v==null?void 0:v.value)==null||p.setId(s),h.value=!h.value};B(w,s=>{I.value=f.value.length!==0&&s.length===f.value.length});const M=s=>{var p;(p=s==null?void 0:s.target)!=null&&p.checked?w.value=f.value.map(b=>b.id):w.value=[]},D=s=>{X.deletionAlert({onConfirmed:()=>{N(s)}})},N=async s=>{l.value=!0,t.deleteCompanyById({id:s,callback:{onSuccess:p=>{g()},onFinish:()=>{l.value=!1}}})};return{sortDirection:i,loading:l,search:k,breadcrumbs:n,checkedRows:w,checkAll:I,companyList:f,currentPage:u,totalElements:V,pageCount:$,companyModal:v,isModalVisible:h,isSystemAdmin:W,arrowPath:x,formatDate:G,toggleModal:o,pageChange:A,deleteCompany:D,toggleEditCompany:P,onToggleCheckAll:M,onClickSort:L,searchCompanies:S,getCompanyList:g}}}),ao={class:"bg-card-background text-card-text-light h-auto w-11/12 mx-auto my-4 rounded-xl flex flex-col items-center lg:w-4/5 lg:min-w-[1560px]"},lo={class:"h-auto w-11/12 flex flex-col items-start my-4 px-8 gap-3"},ro={key:0,class:"text-center p-5"},co={key:2,class:"h-auto w-11/12 overflow-x-scroll md:overflow-hidden"},io={class:"lg:mx-auto"},po={class:"font-bold whitespace-nowrap"},uo={class:"p-4"},go={class:"h-auto w-full flex items-center bg-blue gap-2"},mo={class:"font-semibold"},ho={class:"p-4"},fo={class:"w-36 p-4"},vo={key:0,class:"flex flex-row items-center gap-3"},bo=["src"],yo={class:"flex flex-col text-start overflow-auto"},Co={class:"font-semibold truncate"},wo={key:0,class:"p-4"},_o={class:"font-semibold whitespace-nowrap"},ko={class:"p-4"},xo={class:"h-auto w-full flex flex-row gap-2 items-center justify-evenly"},So=["onClick"],Ao=["onClick"],Po={class:"text-danger"},Mo={class:"flex flex-col items-center my-5"},Do={key:0,class:"font-semibold"};function No(t,n,w,I,$,V){var S,A;const u=d("PageHeader"),l=d("SvgIcon"),k=d("el-icon"),v=d("el-input"),h=d("el-form-item"),f=d("el-form"),i=d("el-empty"),x=d("router-link"),L=d("TablePagination"),g=d("CompanyModal");return c(),m(H,null,[a(u,{title:"Companies",breadcrumbs:t.breadcrumbs},null,8,["breadcrumbs"]),e("div",ao,[e("div",lo,[n[4]||(n[4]=e("h1",{class:"font-bold"},"Companies",-1)),a(f,{onSubmit:oo(t.searchCompanies,["prevent"]),class:"h-auto w-full"},{default:y(()=>[a(h,null,{default:y(()=>[a(v,{placeholder:"Search",modelValue:t.search,"onUpdate:modelValue":n[0]||(n[0]=o=>t.search=o),name:"search",size:"large"},{prefix:y(()=>[a(k,{class:"el-input__icon"},{default:y(()=>[a(l,{icon:"searchIcon"})]),_:1})]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["onSubmit"]),e("button",{class:"bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover rounded-md px-4 py-2 font-semibold flex flex-row items-center gap-2 justify-between",onClick:n[1]||(n[1]=(...o)=>t.toggleModal&&t.toggleModal(...o))},[a(l,{icon:"addIcon"}),n[3]||(n[3]=T(" New "))])]),t.loading?(c(),m("div",ro,n[5]||(n[5]=[e("div",{class:"spinner-border text-primary",role:"status"},[e("span",{class:"sr-only"},"Loading...")],-1)]))):t.companyList.length===0?(c(),R(i,{key:1,description:"No Data"})):(c(),m("div",co,[e("table",io,[e("thead",null,[e("tr",po,[n[7]||(n[7]=e("th",{class:"p-4"},"COMPANY ID",-1)),e("th",uo,[e("div",go,[n[6]||(n[6]=e("span",null,"COMPANY NAME",-1)),e("span",{onClick:n[2]||(n[2]=(...o)=>t.onClickSort&&t.onClickSort(...o))},[a(l,{icon:t.arrowPath},null,8,["icon"])])])]),n[8]||(n[8]=e("th",{class:"p-4"},"COMPANY ADMIN",-1)),n[9]||(n[9]=e("th",{class:"p-4"},"CREATED DATE",-1)),n[10]||(n[10]=e("th",{class:"p-4"},"ACTIONS",-1))])]),e("tbody",null,[(c(!0),m(H,null,eo(t.companyList,o=>{var P,M,D,N,s,p;return c(),m("tr",{key:o.id,class:"font-bold my-2 text-center border-b-[1px] border-light-border border-dashed"},[e("td",null,[e("span",mo,C((o==null?void 0:o.id)||""),1)]),e("td",ho,[a(x,{to:`/companies/${o.id}`,class:"font-bold text-hover-primary underline underline-offset-[3px] decoration-2"},{default:y(()=>[T(C(o==null?void 0:o.name),1)]),_:2},1032,["to"])]),e("td",fo,[o!=null&&o.users&&(o!=null&&o.users.length)?(c(),m("div",vo,[e("img",{class:"h-11 w-11 rounded-full",src:((M=(P=o==null?void 0:o.users)==null?void 0:P[0])==null?void 0:M.avatar)||"/media/avatars/blank.png",alt:"Avatar"},null,8,bo),e("div",yo,[a(x,{to:`/users/${(N=(D=o==null?void 0:o.users)==null?void 0:D[0])==null?void 0:N.id}`,class:"text-link font-semi hover:text-primary"},{default:y(()=>{var b,Y,O,F;return[T(C(`${((Y=(b=o==null?void 0:o.users)==null?void 0:b[0])==null?void 0:Y.firstName)||""} ${((F=(O=o==null?void 0:o.users)==null?void 0:O[0])==null?void 0:F.lastName)||""}`),1)]}),_:2},1032,["to"]),e("span",Co,C(((p=(s=o==null?void 0:o.users)==null?void 0:s[0])==null?void 0:p.email)||""),1)])])):E("",!0)]),t.isSystemAdmin()?(c(),m("td",wo,[e("span",_o,C(t.formatDate(o==null?void 0:o.createdAt,"MMM DD, YYYY")),1)])):E("",!0),e("td",ko,[e("div",xo,[e("button",{class:"bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover h-8 w-8 rounded-lg pl-[7.5px] pt-[1px] md:h-10 md:w-10 md:pl-[6.52px] md:pt-[2px]",onClick:b=>t.toggleEditCompany(o==null?void 0:o.id)},[a(l,{icon:"newReportIcon",classname:"md:h-6 md:w-6"})],8,So),e("button",{class:"bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover h-8 w-8 rounded-lg pl-[7.5px] pt-[1px] md:h-10 md:w-10 md:pl-[6.52px] md:pb-[5px]",onClick:b=>t.deleteCompany(o==null?void 0:o.id)},[e("span",Po,[a(l,{icon:"trashIcon",classname:"md:h-7 md:w-7"})])],8,Ao)])])])}),128))])])])),e("div",Mo,[(S=t.companyList)!=null&&S.length?(c(),m("div",Do,C(`Showing ${(t.currentPage-1)*10+1} to ${(A=t.companyList)==null?void 0:A.length} of ${t.totalElements} entries`),1)):E("",!0),t.pageCount>=1?(c(),R(L,{key:1,"total-pages":t.pageCount,total:t.totalElements,"per-page":10,"current-page":t.currentPage,onPageChange:t.pageChange},null,8,["total-pages","total","current-page","onPageChange"])):E("",!0)])]),a(g,{isVisible:t.isModalVisible,close:t.toggleModal,loadPage:t.getCompanyList,ref:"companyModal"},null,8,["isVisible","close","loadPage"])],64)}const Fo=Z(no,[["render",No]]);export{Fo as default};
