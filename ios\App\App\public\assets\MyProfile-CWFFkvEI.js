import{P as f}from"./PageHeader-l8HvxxsN.js";import{u as y}from"./user-UjS69U41.js";import{U as g}from"./UserDetail-ClZeg5BK.js";import{d as b,q as l,x as D,i as P,_ as U,c as m,l as p,b as M,m as _,a as c,F as k,r as u,o as n}from"./index-DalLS0_6.js";import"./handleFailure-DrOe_u9W.js";import"./SvgIcon-CfrWCA-H.js";import"./Overview-XX3mNy1g.js";import"./validator-BJ5Qi8qK.js";import"./index.esm-C3uaQ3c9.js";import"./company-DGE9srvS.js";import"./regex-BLjctcPP.js";import"./AssignUserModal-BA3iiGKX.js";import"./UserModal-DfH6xbe7.js";import"./TablePagination-BmkwndgK.js";import"./TableHeader-DGMH-x_O.js";import"./table-bhK9qpe4.js";import"./date-CCTVzEJd.js";const v=b({name:"my-profile",components:{UserDetail:g,PageHeader:f},setup(){const e=y(),r=l(),o=l(!1),i=["My Profile","Settings"],t=P("userInfo");D(()=>{a()});const a=async()=>{o.value=!0,e.getMyProfile({callback:{onSuccess:s=>{t==null||t.updateUserInfo({...s}),r.value=s},onFinish:()=>{o.value=!1}}})};return{loading:o,userDetail:r,breadcrumbs:i,getMyProfile:a}}}),B={key:0,class:"text-center my-auto"};function S(e,r,o,i,t,a){const s=u("PageHeader"),d=u("UserDetail");return n(),m(k,null,[e.loading?(n(),m("div",B,r[0]||(r[0]=[c("div",{class:"spinner-border text-primary",role:"status"},[c("span",{class:"sr-only"},"Loading...")],-1)]))):p("",!0),M(s,{title:"My Profile",breadcrumbs:e.breadcrumbs},null,8,["breadcrumbs"]),!e.loading&&e.userDetail?(n(),_(d,{key:1,userDetail:e.userDetail,reloadUserData:e.getMyProfile},null,8,["userDetail","reloadUserData"])):p("",!0)],64)}const J=U(v,[["render",S]]);export{J as default};
