<template>
  <div
    class="h-auto w-11/12 mx-auto bg-card-background text-card-text rounded-lg p-4 lg:w-4/5 lg:min-w-[1560px]"
  >
    <!--begin::Card header-->
    <div class="h-auto w-full flex flex-col gap-4 items-start">
      <h1 class="text-lg font-bold">Customer Contacts</h1>
      <div class="h-auto w-full flex items-center">
        <el-form @submit.prevent="searchCustomerContacts" class="w-full">
          <el-form-item class="w-full">
            <el-input
              placeholder="Search"
              v-model="search"
              name="search"
              size="large"
              ><template #prefix>
                <el-icon class="el-input__icon">
                  <SvgIcon icon="searchIcon" />
                </el-icon> </template
            ></el-input> </el-form-item
        ></el-form>
      </div>
      <div class="h-auto w-full flex flex-row justify-end gap-4">
        <button
          class="bg-danger rounded-md px-4 py-2 font-semibold"
          v-if="checkedRows.length !== 0 && isAdmin()"
          @click="onRemove"
        >
          Remove
        </button>
        <button
          class="bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover rounded-md px-4 py-2 font-semibold flex flex-row items-center gap-2 justify-between"
          @click="toggleNewCustomer"
          v-if="isAdmin()"
        >
          <SvgIcon icon="addIcon" />
          New
        </button>
      </div>

      <div v-if="loading" class="text-center p-5">
        <div class="spinner-border text-primary" role="status">
          <span class="sr-only">Loading...</span>
        </div>
      </div>
      <el-empty v-else-if="contactList.length === 0" description="No Data" />
      <div v-else class="h-auto w-full overflow-x-scroll px-4 lg:overflow-hidden lg:min-w-fit">
        <!--begin::Table-->
        <table class="lg:mx-auto">
          <!--begin::Table head-->
          <thead class="font-bold whitespace-nowrap">
            <tr class="font-bold">
              <TableHeader
                :headers="tableHeader"
                :sortBy="sortParams.sortBy!"
                :sortDirection="sortParams.sortDirection!"
                :onSort="onSort"
                ><template v-slot:customHeader="{ header }">
                  <div v-if="header.label === ''">
                    <div
                      class="form-check form-check-sm form-check-custom form-check-solid"
                    >
                      <input
                        class="h-4 w-4"
                        type="checkbox"
                        v-model="checkAll"
                        @change="onToggleCheckAll"
                      />
                    </div>
                  </div>
                  <div v-else class="p-4">
                    {{ header.label }}
                  </div>
                </template></TableHeader
              >
            </tr>
          </thead>
          <!--end::Table head-->

          <!--begin::Table body-->
          <tbody>
            <template v-for="item in contactList" :key="item.id">
              <tr
                class="font-bold my-2 text-center border-b-[1px] border-dashed border-light-border"
              >
                <td v-if="isAdmin()">
                  <div
                    class="form-check form-check-sm form-check-custom form-check-solid"
                  >
                    <input
                      class="h-4 w-4"
                      type="checkbox"
                      :value="item.id"
                      v-model="checkedRows"
                    />
                  </div>
                </td>

                <td class="w-36 p-4">
                  <span class="font-semibold whitespace-nowrap">
                    {{ item?.name }}
                  </span>
                </td>

                <td class="w-36 p-4">
                  <span class="font-semibold whitespace-nowrap">{{
                    item?.address
                  }}</span>
                </td>
                <td class="w-36 p-4">
                  <span class="font-semibold whitespace-nowrap">
                    {{ item?.mobilePhone }}
                  </span>
                </td>
                <td class="w-36 p-4">
                  <span class="font-semibold whitespace-nowrap">
                    {{ item?.officePhone }}
                  </span>
                </td>
                <td class="w-36 p-4">
                  <span class="font-semibold whitespace-nowrap">
                    {{ item?.emailAddress }}
                  </span>
                </td>
                <td class="w-36 p-4">
                  <div class="flex justify-center">
                    <input
                      class="h-4 w-4"
                      type="checkbox"
                      :checked="item?.primaryContact"
                      disabled
                    />
                  </div>
                </td>
                <td class="w-36 p-4">
                  <div class="flex justify-center">
                    <input
                      class="h-4 w-4"
                      type="checkbox"
                      :checked="item?.notifyOnNewReport"
                      disabled
                    />
                  </div>
                </td>

                <td v-if="isAdmin()">
                  <div
                    class="h-auto w-full flex flex-row items-center justify-evenly gap-2"
                  >
                    <button
                      class="bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover font-semibold h-8 w-8 flex items-center justify-center rounded-lg pl-[7.5px] pt-[1px] md:h-10 md:w-10 md:pl-[3px] md:pt-[5px] md:pb-[3.75px]"
                      @click="toggleEditCustomer(item?.id ?? '')"
                    >
                      <SvgIcon icon="newReportIcon" classname="md:h-6 md:w-6" />
                    </button>
                    <button
                      class="bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover font-semibold h-8 w-8 flex items-center justify-center rounded-lg pl-[7.5px] pt-[1px] md:h-10 md:w-10 md:pl-[3px] md:pb-[6px]"
                      @click="deleteEngineer(item?.id!)"
                    >
                      <span class="text-danger">
                        <SvgIcon icon="trashIcon" classname="md:h-7 md:w-7" />
                      </span>
                    </button>
                  </div>
                </td>
              </tr>
            </template>
          </tbody>
          <!--end::Table body-->
        </table>
        <!--end::Table-->
      </div>
      <div class="h-auto w-full flex flex-col items-center my-5">
        <div v-if="contactList?.length" class="font-semibold">
          {{
            `Showing ${(currentPage - 1) * 10 + 1} to ${
              contactList?.length
            } of ${totalElements} entries`
          }}
        </div>
        <TablePagination
          v-if="pageCount >= 1"
          :total-pages="pageCount"
          :total="totalElements"
          :per-page="10"
          :current-page="currentPage"
          @page-change="pageChange"
        />
      </div>
    </div>
    <ContactModal
      :isVisible="isModalVisible"
      :close="toggleNewCustomer"
      ref="contactModal"
      :loadPage="getCustomerContacts"
    />
  </div>
</template>

<script lang="ts">
import PageHeader from "@/components/PageHeader.vue";
import TablePagination from "@/components/common/TablePagination.vue";
import SvgIcon from "@/constants/SvgIcon.vue";
import TableHeader from "@/components/common/TableHeader.vue";
import { SortByEnum, SortDirectionEnum } from "@/constants/table";
import { formatDate } from "@/utils/date";
import AlertService from "@/services/AlertService";
import { isAdmin } from "@/services/JwtService";
import { useCustomerContactStore } from "@/stores/customerContact";
import { defineComponent, onMounted, ref, type Ref, watch } from "vue";
import { useRoute, useRouter } from "vue-router";
import ContactModal from "./ContactModal.vue";

export default defineComponent({
  name: "customer-list",
  components: {
    PageHeader,
    SvgIcon,
    TablePagination,
    ContactModal,
    TableHeader,
  },
  setup() {
    const customerContactStore = useCustomerContactStore();
    const route = useRoute();
    const router = useRouter();
    const checkedRows = ref<Array<string>>([]);
    const checkAll = ref<boolean>(false);
    const pageCount = ref(0);
    const totalElements = ref(0);
    const currentPage = ref(1);
    const loading = ref(false);
    const search = ref<string>("");
    const showContactModal = ref(false);
    const contactList = ref<Customer.Contact[]>([]);
    const contactModal: Ref<any> = ref<typeof ContactModal | null>(null);
    const customerId = route.params?.customerId?.toString() || "";
    const isModalVisible = ref(false);

    const sortParams = ref<Filter.FilterForm>({
      sortDirection: SortDirectionEnum.ASC,
      sortBy: SortByEnum.Name,
    });

    const tableHeader: Table.ColumnHeader[] = [
      { label: "", class: "w-25px", display: isAdmin() },
      { label: "FULL NAME", sortBy: SortByEnum.Name, class: "min-w-150px" },
      { label: "ADDRESS", class: "min-w-150px" },
      { label: "MOBILE NUMBER", class: "min-w-120px" },
      { label: "OFFICE NUMBER", class: "min-w-120px" },
      { label: "EMAIL", class: "min-w-150px" },
      { label: "PRIMARY CONTACT", class: "min-w-150px" },
      { label: "REPORT NOTIFICATION", class: "min-w-120px" },
      { label: "ACTIONS", class: "min-w-60px", display: isAdmin() },
    ];

    onMounted(() => {
      getCustomerContacts();
    });

    watch(currentPage, () => {
      getCustomerContacts();
    });

    watch(checkedRows, (newValue) => {
      checkAll.value =
        contactList.value.length !== 0 &&
        newValue.length === contactList.value.length;
    });

    const getCustomerContacts = async (): Promise<void> => {
      loading.value = true;
      customerContactStore.getCustomerContacts({
        params: {
          customerId,
          name: search.value.trim() || null,
          page: currentPage.value,
          limit: 10,
          ...sortParams.value,
        },
        callback: {
          onSuccess: (res: any) => {
            contactList.value = [...res?.items];
            pageCount.value = res?.totalPage;
            totalElements.value = res?.total;
            currentPage.value = res?.page;
          },
          onFinish: () => {
            loading.value = false;
          },
        },
      });
    };

    const onSort = (newSortValue: Filter.FilterForm) => {
      sortParams.value = { ...newSortValue };

      getCustomerContacts();
    };

    const searchCustomerContacts = () => {
      if (currentPage.value !== 1) {
        currentPage.value = 1;
      } else {
        getCustomerContacts();
      }
    };

    const toggleNewCustomer = (): void => {
      isModalVisible.value = !isModalVisible.value;
    };

    const toggleEditCustomer = (item: string): void => {
      contactModal?.value?.setId(item);
      isModalVisible.value = !isModalVisible.value;
    };

    const onRemove = () => {
      AlertService.deletionAlert({
        onConfirmed: () => {
          removeContacts(checkedRows.value, true);
        },
      });
    };

    const deleteEngineer = (id: string) => {
      AlertService.deletionAlert({
        onConfirmed: () => {
          removeContacts([id]);
        },
      });
    };

    const pageChange = (newPage: number) => {
      currentPage.value = newPage;
    };
    const toggleFilter = (): void => {
      showContactModal.value = !showContactModal.value;
    };

    const onToggleCheckAll = (e: any) => {
      if (e?.target?.checked) {
        checkedRows.value = contactList.value.map((user) => user.id!);
      } else {
        checkedRows.value = [];
      }
    };

    const view = (id: string) => {
      router.push({ path: `/users/${id}` });
    };

    const removeContacts = async (
      ids: string[],
      clearRemoveList = false
    ): Promise<void> => {
      loading.value = true;

      customerContactStore.removeContact({
        ids,
        callback: {
          onSuccess: (_res: any) => {
            if (clearRemoveList) {
              checkedRows.value = [];
            }
            getCustomerContacts();
          },
          onFinish: () => {
            loading.value = false;
          },
        },
      });
    };

    return {
      sortParams,
      tableHeader,
      search,
      loading,
      contactModal,
      checkedRows,
      checkAll,
      contactList,
      currentPage,
      totalElements,
      pageCount,
      showContactModal,
      isModalVisible,
      searchCustomerContacts,
      pageChange,
      toggleFilter,
      deleteEngineer,
      formatDate,
      view,
      onRemove,
      toggleNewCustomer,
      toggleEditCustomer,
      isAdmin,
      onToggleCheckAll,
      getCustomerContacts,
      onSort,
    };
  },
});
</script>

<style>
.el-form-item {
  margin-bottom: 0px;
}
</style>
