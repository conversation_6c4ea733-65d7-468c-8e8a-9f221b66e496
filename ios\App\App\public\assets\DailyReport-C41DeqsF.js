import{u as vt,a as ht,B as pe,s as st,f as lt,b as dt}from"./sample-VKOSqYFu.js";import{d as G,q as y,s as us,v as cs,P as g,R as ot,_ as Q,c as V,o as $,a as t,t as I,b as i,r as S,l as U,m as W,w as c,B as L,A as J,x as ie,j as re,i as ee,k as Z,F as B,p as de,E as ne,O as ae,Q as P,J as De,U as et,T as yt,W as wt,n as _e,S as rt,H as $t,M as St,h as tt,X as fs}from"./index-CGNRhvz7.js";import{P as ms}from"./PageHeader-3hadTn26.js";import{S as X}from"./SvgIcon-CMhyaXWN.js";import{a as fe,b as Ie,g as ps,m as gs,c as ve,d as bs}from"./daily-report-BYo3Yy2S.js";import{f as ke,h as vs,a as _t,i as hs}from"./date-CvSHk5ED.js";import{n as ge}from"./numberFormatter-C7uP7NWj.js";import{u as ys,a as ws}from"./product-Dl6kto8A.js";import{u as kt}from"./well-Caso2ZGG.js";import{h as E}from"./handleFailure-DtTpu7r3.js";import{s as Dt}from"./navigation-guard-BSVpYbbP.js";import{u as $s}from"./user-KFDu8xJF.js";import{T as Ss}from"./TablePagination-BmVxunEG.js";import"./d-left-arrow-079-B3YbfCzd.js";const _s=G({__name:"DailyReportProvide",setup(s){const e=y("");return cs("dailyReport",{createDailyReport:({wellId:l,callback:o})=>{const a=g.get(o,"onSuccess",g.noop);e.value?a(e.value):vt().createDailyReport({wellId:l,callback:{onSuccess:n=>{e.value=(n==null?void 0:n.id)||"",a(n==null?void 0:n.id)}}})},resetDailyReportId:()=>{e.value=""},getDailyReportId:()=>e.value,setDailyReportId:l=>{e.value=l}}),(l,o)=>us(l.$slots,"default")}}),ks=[{value:fe.CasedHole,title:"What is a Cased Hole?",content:'<p class="mb-0"> Cased holes are sections of a wellbore where casing (metallic or non-metallic pipe) is installed to provide structural support, isolation, and control of fluids. The type of cased hole depends on its specific purpose and location within the well. Here are some common types of cased holes that you may encounter:<br/><br/>Conductor Casing: Conductor casing is the first casing string set in a well, and it extends from the surface to a certain depth below the ground or seabed. Its primary function is to provide structural support for the wellbore and prevent collapse of the hole near the surface.<br/><br/>Surface Casing: Surface casing is set below the conductor casing and typically extends to a shallower depth. It serves to isolate shallow, potentially unstable formations, protect freshwater aquifers, and provide a foundation for the subsequent casing strings.<br/><br/>Intermediate Casing: Intermediate casing is set at intermediate depths within the wellbore. Its purpose varies but may include isolating troublesome zones, controlling lost circulation, or providing additional structural support.<br/><br/>Production Casing: Production casing is set to isolate and support the production zone or reservoir. It serves as a conduit for hydrocarbon production from the reservoir to the surface. Its size and specifications depend on the production requirements.<br/><br/>Liner: A liner is a casing string that is installed inside another casing string, typically in the lower part of the well. It may be used to extend the casing to a greater depth, isolate problematic zones, or provide additional support without reaching the surface.<br/><br/>Scab Liner: A scab liner is a relatively short casing string used to repair a section of damaged or corroded casing. It is typically installed inside the damaged section to restore well integrity.<br/><br/>Cemented Casing: This refers to casing strings that have been cemented in place to ensure wellbore integrity, isolation of formations, and zonal isolation.<br/><br/>Slotted Liner or Screen: Slotted liners or screens are used in wells to allow fluid flow from the formation while preventing the ingress of sand or other debris. They are often used in open-hole completions.<br/><br/>Hanger Casing: Hanger casing is used in wells with subsea completions, where a casing hanger supports the casing strings within a subsea wellhead assembly.<br/><br/>Packer Casing: Packer casing is used in wells with packer systems, which are devices that create seals or isolate zones within the wellbore.</p>'},{value:fe.OpenHold,title:"What is an Open Hole?",content:`<p class="mb-0"> An "Open Hole" refers to the section of a wellbore that has been drilled but is not yet cased or lined with casing or tubing. In open-hole drilling, the wellbore is left in its natural state, allowing for direct contact with the surrounding geological formations. Open holes are typically encountered during the drilling process before casing is installed. The purpose of open-hole sections varies and may include geological evaluation, wellbore stability assessment, and the potential for hydrocarbon discovery.<br/><br/>Types of Open Hole:<br/><br/>Different types of open holes can be encountered during drilling operations, depending on the well's objectives and geological conditions. Some common types of open holes include:<br/><br/>Exploratory Open Hole: This is the initial open-hole section drilled to explore the subsurface and evaluate geological formations for potential hydrocarbon reservoirs.<br/><br/>Production Open Hole: In some cases, a well may have an open-hole production section where hydrocarbons are produced directly from the formation without casing.<br/><br/>Logging Open Hole: An open-hole section may be specifically drilled to facilitate logging tools, such as wireline or logging while drilling (LWD/MWD) tools, to collect formation evaluation data.<br/><br/>Evaluation Open Hole: Open holes are often drilled for the purpose of evaluating formation properties, including porosity, permeability, and lithology.<br/><br/>Directional Open Hole: In directional drilling, open-hole sections may be drilled with specific inclinations and azimuths to intersect target zones.<br/><br/>Geotechnical Open Hole: Open-hole sections may be drilled for geotechnical purposes, such as assessing rock and soil properties for civil engineering projects.</p>`},{value:fe.DrillString,title:"What is a Drill String?",content:'<p class="mb-0">A "Drill String" refers to the assembly of drilling tools, drill pipes, and other components used in drilling operations to advance the wellbore into the subsurface. The drill string is typically composed of multiple sections of drill pipe, each joined by tool joints, and may include other specialized tools and equipment.<br/><br/>Types of Drill Strings:<br/><br/>Different types of drill strings may be used in drilling operations, depending on the drilling objectives, well design, and geological conditions. Some common types of drill strings include:<br/><br/>Standard Drill String: This is a typical drill string configuration consisting of drill pipe sections joined by tool joints. It is commonly used in various drilling applications.<br/><br/>Heavyweight Drill String: Heavyweight drill strings include heavyweight drill pipe or drill collars to provide additional weight on bit (WOB) and stability in challenging drilling conditions.<br/><br/>irectional Drill String: Directional drilling often involves specialized drill string configurations designed to achieve specific wellbore trajectories, including bent sub assemblies and steerable tools.<br/><br/>Slim Hole Drill String: Slim hole drill strings are used in narrow wellbores or when smaller-diameter wells are desired, such as in slim hole drilling or geothermal drilling.<br/><br/>Wireline Drill String: Wireline drill strings are used in wireline coring and well logging operations, where tools are lowered into the wellbore on a wireline cable.<br/><br/>Coiled Tubing: Coiled tubing drill strings consist of continuous tubing wound on a reel and are used in various well intervention and completion operations.</p>'},{value:fe.Bits,title:"What is a Drill Bit?",content:`<p class="mb-0">A "Drill Bit" is a cutting tool used in drilling operations to create a hole in the earth's subsurface. Drill bits are attached to the bottom of the drill string and are responsible for breaking and removing rock or other materials as the wellbore is advanced. Drill bits are crucial components in the drilling process and come in various types and designs to suit different geological conditions and drilling objectives.<br/><br/>Types of Drill Bits:<br/><br/>There are several types of drill bits used in drilling operations, each designed for specific applications and geological conditions. Some common types of drill bits include:<br/><br/>PDC (Polycrystalline Diamond Compact) Bits: PDC bits use diamond-impregnated cutters and are known for their efficiency in drilling through soft to medium-hard formations.<br/><br/>Roller Cone Bits: Roller cone bits have rotating cones with hardened teeth that crush and cut the rock. They are versatile and can be used in a wide range of formations.<br/><br/>Diamond-Impregnated Bits: These bits have a matrix embedded with industrial diamonds. They are suitable for drilling through hard and abrasive formations.<br/><br/>Fixed Cutter Bits: Fixed cutter bits, such as diamond bits and PDC bits, have non-rotating cutting elements that shear or scrape the rock. They are used in various applications, including oil and gas drilling and mining.<br/><br/>Tri-cone Bits: Tri-cone bits have three cones with different tooth designs and are often used in medium to hard formations.<br/><br/>Reaming Bits: Reaming bits are used to enlarge the hole diameter after the initial drilling and can be essential for well completion and casing installation.<br/><br/>Directional and Steerable Bits: These bits are designed for directional drilling and are equipped with mechanisms that allow them to control the wellbore's trajectory.<br/><br/>Underreamer Bits: Underreamer bits are used to enlarge the hole diameter below a restriction in the wellbore, such as a previously set casing.<br/><br/>The choice of drill bit type depends on the geological formations, drilling objectives, and wellbore conditions. Drill bit selection is a critical factor in drilling efficiency and wellbore integrity.</p>`},{value:fe.Nozzles,title:"What is a Nozzle?",content:'<p class="mb-0">In the context of drilling operations, a "Nozzle" refers to a specialized component that directs the flow of drilling fluids, such as drilling mud or drilling fluid additives, from the mud pumps into the wellbore. Nozzles are typically located in the drill string or drill pipe near the bottom of the well, and they play a crucial role in various aspects of drilling, including cleaning the bottom of the hole, cooling the drill bit, and optimizing fluid flow.<br/><br/>Types of Nozzles:<br/><br/>There are several types of nozzles used in drilling operations, each designed for specific purposes and fluid flow characteristics. Some common types of nozzles include:<br/><br/>Fan Nozzles: These nozzles emit a fan-shaped spray pattern, which is useful for cleaning the bottom of the wellbore and distributing drilling fluids evenly.<br/><br/>Jet Nozzles: Jet nozzles produce a high-velocity, focused stream of drilling fluid. They are often used for drilling hard formations and cutting through tough materials.<br/><br/>Crossflow Nozzles: Crossflow nozzles have multiple orifices that emit fluid in multiple directions simultaneously, improving hole cleaning efficiency.<br/><br/>Variable Flow Nozzles: These nozzles allow for adjustable flow rates, enabling operators to control the amount of fluid being pumped through them.<br/><br/>Solid Nozzles: Solid nozzles have a single, fixed orifice and are commonly used for specific applications, such as fluid sampling or pressure testing.<br/><br/>Replaceable Nozzles: Some nozzles are designed to be easily replaceable, allowing for quick changes to optimize drilling performance.<br/><br/>The choice of nozzle type and size depends on the drilling objectives, wellbore conditions, and the specific requirements of the drilling operation. Proper nozzle selection is important for efficient drilling fluid management and hole cleaning.</p>'},{value:Ie.Solids,title:"What is Solid?",content:`<p class="mb-0">Characterization and measurement of various solid components present in the drilling mud. It includes parameters related to the composition and properties of solids, which can significantly impact the mud's performance and behavior.</p>`}],Ds=G({name:"customize-layout",props:{currentTab:{type:Number,required:!0}},components:{SvgIcon:X},setup(s){return{currentInfo:ot(()=>ks.filter(v=>v.value===s.currentTab)[0]||null)}}}),Is={id:"kt_app_layout_builder",class:"bg-transparent drawer drawer-end","data-kt-drawer":"true","data-kt-drawer-name":"app-settings","data-kt-drawer-activate":"true","data-kt-drawer-overlay":"true","data-kt-drawer-width":"{default:'400px', 'lg': '680px'}","data-kt-drawer-direction":"end","data-kt-drawer-toggle":"#kt_app_layout_builder_toggle","data-kt-drawer-close":"#kt_app_layout_builder_close"},Vs={class:"card border-0 shadow-none rounded-0 w-100 bg-transparent position-relative"},Ps={class:"card bg-white position-absolute card-pop-up mh-75 overflow-auto"},Cs={class:"card-header d-flex align-items-center justify-content-between border-bottom border-bottom-1"},Ts={class:"mb-0 fw-normal poppins"},Fs={class:"cursor-pointer ms-auto",id:"kt_app_layout_builder_close"},xs={class:"card-body roboto text-dark"},Ns=["innerHTML"];function As(s,e,v,b,f,r){var o,a;const l=S("SvgIcon");return $(),V("div",Is,[t("div",Vs,[t("div",Ps,[t("div",Cs,[t("h2",Ts,I(((o=s.currentInfo)==null?void 0:o.title)||""),1),t("span",Fs,[i(l,{icon:"closeModalIcon"})])]),t("div",xs,[(a=s.currentInfo)!=null&&a.content?($(),V("div",{key:0,innerHTML:s.currentInfo.content},null,8,Ns)):U("",!0)])])])])}const Hs=Q(Ds,[["render",As],["__scopeId","data-v-6d314ca2"]]),qs=G({name:"no-entries",components:{},props:{addNew:{type:Function,default:()=>{},required:!0}},setup(){return{}}});function Ms(s,e,v,b,f,r){const l=S("el-button"),o=S("el-empty");return $(),W(o,null,{default:c(()=>[i(l,{onClick:s.addNew,type:"primary",description:"No entries"},{default:c(()=>e[0]||(e[0]=[L("Click to add new")])),_:1},8,["onClick"])]),_:1})}const be=Q(qs,[["render",Ms]]),js=G({name:"cost-modal",components:{SvgIcon:X},props:{loadPage:{type:Function,default:()=>{},required:!0}},setup(s){const e=re(),v=kt(),b=ys(),f=ht(),r=y(!1),l=y({costSettingId:"",unit:null,quantity:null}),o=y(null),a=y(!1),d=y(!1),n=y(""),u=y([]),m=y(""),p=ee("dailyReport");J(n,x=>{x!==""&&(N(),m.value&&k())}),J(r,x=>{var A;x===!1&&(n.value="",j(),(A=o==null?void 0:o.value)==null||A.resetFields())}),ie(()=>{D()});const D=async()=>{v.getWellDetails({wellId:e.params.id,callback:{onSuccess:x=>{var A;m.value=(A=x==null?void 0:x.company)==null?void 0:A.id,k()},onFinish:x=>{a.value=!1}}})},k=async()=>{m.value&&(d.value=!0,b.getCostSettings({params:{page:1,limit:200,companyId:m.value},callback:{onSuccess:x=>{var A;u.value=(A=x==null?void 0:x.items)==null?void 0:A.map(M=>({value:M==null?void 0:M.id,label:M==null?void 0:M.name}))},onFinish:x=>{d.value=!1}}}))},N=async()=>{f.getCostDetails({id:n.value,callback:{onSuccess:x=>{var A;l.value={...x,costSettingId:(A=x==null?void 0:x.costSetting)==null?void 0:A.id}}}})},T=async x=>{a.value=!0,f.updateCost({id:n.value,params:x,callback:{onSuccess:A=>{s!=null&&s.loadPage&&(s==null||s.loadPage()),_()},onFinish:A=>{a.value=!1}}})},w=async x=>{a.value=!0,f.createCost({params:x,callback:{onSuccess:A=>{s!=null&&s.loadPage&&(s==null||s.loadPage()),_()},onFinish:A=>{a.value=!1}}})},h=()=>{r.value=!0},_=()=>{r.value=!1},H=x=>{n.value=x.toString()},C=y({costSettingId:[{required:!0,message:"Please select Description",trigger:"change"}],unit:[{required:!0,message:"Please type Unit",trigger:"blur"}],quantity:[{required:!0,message:"Please type Quantity",trigger:"blur"}]}),F=()=>{o.value&&o.value.validate(x=>{var A,M,z;if(x){const q={...l==null?void 0:l.value,unit:Number((A=l==null?void 0:l.value)==null?void 0:A.unit),quantity:Number((M=l==null?void 0:l.value)==null?void 0:M.quantity)};n!=null&&n.value?T({...q,dailyReportId:p==null?void 0:p.getDailyReportId()}):p==null||p.createDailyReport({wellId:(z=e==null?void 0:e.params)==null?void 0:z.id,callback:{onSuccess:R=>{w({...q,dailyReportId:p==null?void 0:p.getDailyReportId()})}}})}})},j=()=>{l.value={costSettingId:"",unit:null,quantity:null}};return{id:n,modal:r,rules:C,loading:a,targetData:l,formRef:o,loadingDescriptionList:d,descriptionOptions:u,show:h,hide:_,submit:F,setId:H,reset:j}}}),zs={class:"d-flex align-items-center w-100"},Es={class:"modal-title"},Ls={class:"row g-7 mb-3"},Rs={class:"col-12 fv-row"},Bs={class:"row g-7 mb-3"},Os={class:"col-12 fv-row"},Us={class:"row g-7 mb-3"},Ws={class:"col-12 fv-row"},Gs={class:"modal-footer d-flex justify-content-center align-items-center"},Ys=["disabled"],Js=["data-kt-indicator","disabled"],Qs={key:0,class:"indicator-label"},Ks={key:1,class:"indicator-progress"};function Xs(s,e,v,b,f,r){const l=S("SvgIcon"),o=S("el-option"),a=S("el-select"),d=S("el-form-item"),n=S("el-input"),u=S("el-form"),m=S("el-dialog");return $(),W(m,{modelValue:s.modal,"onUpdate:modelValue":e[5]||(e[5]=p=>s.modal=p),"show-close":!1,width:"500","align-center":""},{header:c(()=>[t("div",zs,[t("h3",Es,I(`${s.id?"Edit Cost":"New Cost"}`),1),t("span",{class:"cursor-pointer ms-auto",onClick:e[0]||(e[0]=(...p)=>s.hide&&s.hide(...p))},[i(l,{icon:"closeModalIcon"})])])]),default:c(()=>[t("div",null,[i(u,{id:"cost_form",onSubmit:de(s.submit,["prevent"]),model:s.targetData,rules:s.rules,ref:"formRef",class:"form"},{default:c(()=>[t("div",Ls,[t("div",Rs,[e[6]||(e[6]=t("label",{class:"d-flex align-items-center fs-6 fw-semibold mb-2 text-break required"},"Description ",-1)),i(d,{prop:"costSettingId",class:"mt-auto"},{default:c(()=>[i(a,{modelValue:s.targetData.costSettingId,"onUpdate:modelValue":e[1]||(e[1]=p=>s.targetData.costSettingId=p),placeholder:"Select Description",class:"w-100",clearable:"",loading:s.loadingDescriptionList},{default:c(()=>[($(!0),V(B,null,Z(s.descriptionOptions,p=>($(),W(o,{key:p==null?void 0:p.value,label:p==null?void 0:p.label,value:p==null?void 0:p.value,name:"costSettingId"},null,8,["label","value"]))),128))]),_:1},8,["modelValue","loading"])]),_:1})])]),t("div",Bs,[t("div",Os,[e[7]||(e[7]=t("label",{class:"d-flex align-items-center fs-6 fw-semibold mb-2 required"},"Unit",-1)),i(d,{prop:"unit"},{default:c(()=>[i(n,{type:"number",class:"w-100",controls:!1,step:"any",modelValue:s.targetData.unit,"onUpdate:modelValue":e[2]||(e[2]=p=>s.targetData.unit=p),placeholder:"",name:"unit"},null,8,["modelValue"])]),_:1})])]),t("div",Us,[t("div",Ws,[e[8]||(e[8]=t("label",{class:"d-flex align-items-center fs-6 fw-semibold mb-2 required"}," Quantity ",-1)),i(d,{prop:"quantity"},{default:c(()=>[i(n,{type:"number",class:"w-100",controls:!1,step:"any",modelValue:s.targetData.quantity,"onUpdate:modelValue":e[3]||(e[3]=p=>s.targetData.quantity=p),placeholder:"",name:"quantity"},null,8,["modelValue"])]),_:1})])]),t("div",Gs,[t("button",{type:"button",id:"kt_modal_add_customer_cancel",class:"btn text-gray-700 btn-sm me-3 btn-blue",onClick:e[4]||(e[4]=(...p)=>s.hide&&s.hide(...p)),disabled:s.loading}," Discard ",8,Ys),t("button",{"data-kt-indicator":s.loading?"on":null,class:"btn btn-sm btn-primary",type:"submit",disabled:s.loading},[s.loading?U("",!0):($(),V("span",Qs," Save ")),s.loading?($(),V("span",Ks,e[9]||(e[9]=[L(" Please wait... "),t("span",{class:"spinner-border spinner-border-sm align-middle ms-2"},null,-1)]))):U("",!0)],8,Js)])]),_:1},8,["onSubmit","model","rules"])])]),_:1},8,["modelValue"])}const Zs=Q(js,[["render",Xs]]),el=G({name:"costs",components:{SvgIcon:X,BottomTool:pe,CostModal:Zs,NoEntries:be},props:{setChildActiveTab:{type:Function,required:!1,default:()=>{}}},setup(s){const e=ht(),v=y(null),b=y(!1),f=y([]),r=ee("dailyReport");ie(()=>{r!=null&&r.getDailyReportId()&&l()});const l=async(u={dailyReportId:r==null?void 0:r.getDailyReportId(),page:1,limit:200})=>{b.value=!0,e.getCosts({params:u,callback:{onSuccess:m=>{f.value=JSON.parse(JSON.stringify(m==null?void 0:m.items))},onFinish:m=>{b.value=!1}}})},o=()=>{var u;(u=v==null?void 0:v.value)==null||u.show()},a=u=>{var m,p;(m=v==null?void 0:v.value)==null||m.setId(u),(p=v==null?void 0:v.value)==null||p.show()},d=u=>{ne.deletionAlert({onConfirmed:()=>{n(u)}})},n=u=>{b.value=!0,e.deleteCost({id:u,callback:{onSuccess:m=>{l()},onFinish:m=>{b.value=!1}}})};return{loading:b,costList:f,costModal:v,numberWithCommas:ge,toggleAddCostModal:o,toggleEditCostModal:a,deleteCost:d,formatDate:ke,getCosts:l}}}),tl={class:"card h-100 my-8 costs"},sl={class:"card-body"},ll={key:0,class:"text-center"},ol={key:1},nl={key:1,class:"row gap-10"},al={class:"card-body gap-5 d-flex flex-column"},il={class:"mb-0 text-primary fs-4"},dl={class:"d-flex flex-column gap-3"},rl={class:"d-flex flex-wrap align-items-center justify-content-between border-bottom-dashed border-bottom-1 border-blue-light pb-3"},ul={class:"fw-700 fs-5 text-gray-700"},cl={class:"d-flex flex-wrap align-items-center justify-content-between border-bottom-dashed border-bottom-1 border-blue-light pb-3"},fl={class:"fw-700 fs-5 text-gray-700"},ml={class:"d-flex flex-wrap align-items-center justify-content-between border-bottom-dashed border-bottom-1 border-blue-light pb-3"},pl={class:"fw-700 fs-5 text-gray-700"},gl={class:"d-flex flex-wrap align-items-center justify-content-between"},bl={class:"fw-700 fs-5 text-gray-700"},vl={class:"d-flex align-items-center gap-2 position-absolute top-0 end-0 transform-top-50"},hl=["onClick"],yl={class:"svg-icon svg-icon-3"},wl=["onClick"],$l={class:"svg-icon svg-icon-3 text-danger"};function Sl(s,e,v,b,f,r){const l=S("NoEntries"),o=S("SvgIcon"),a=S("BottomTool"),d=S("CostModal");return $(),V(B,null,[t("div",tl,[e[5]||(e[5]=t("div",{class:"card-header d-flex flex-wrap align-items-center"},[t("h1",{class:"mb-0 me-4 text-gray-900"},"Costs")],-1)),t("div",sl,[s.loading?($(),V("div",ll,e[0]||(e[0]=[t("div",{class:"spinner-border text-primary",role:"status"},[t("span",{class:"sr-only"},"Loading...")],-1)]))):($(),V("div",ol,[s.costList.length===0?($(),W(l,{key:0,addNew:s.toggleAddCostModal},null,8,["addNew"])):($(),V("div",nl,[($(!0),V(B,null,Z(s.costList,n=>{var u,m;return $(),V("div",{key:n==null?void 0:n.id,class:"col-4 card rounded-3 shadow-sm border-top border-top-2 border-primary position-relative card-item"},[t("div",al,[t("h5",il,I((u=n==null?void 0:n.costSetting)==null?void 0:u.name),1),t("div",dl,[t("div",rl,[e[1]||(e[1]=t("span",{class:"fw-semibold fs-5 text-gray-700"},"Created Date",-1)),t("span",ul,I(`${s.formatDate(n==null?void 0:n.createdAt,"DD MMM YYYY")}`),1)]),t("div",cl,[e[2]||(e[2]=t("span",{class:"fw-semibold fs-5 text-gray-700"},"Unit",-1)),t("span",fl,I(n==null?void 0:n.unit),1)]),t("div",ml,[e[3]||(e[3]=t("span",{class:"fw-semibold fs-5 text-gray-700"},"Quantity",-1)),t("span",pl,I(s.numberWithCommas(n==null?void 0:n.quantity)),1)]),t("div",gl,[e[4]||(e[4]=t("span",{class:"fw-semibold fs-5 text-gray-700"},"Cost",-1)),t("span",bl,I(s.numberWithCommas((m=n==null?void 0:n.costSetting)==null?void 0:m.cost)),1)])])]),t("div",vl,[t("button",{class:"btn rounded-circle btn-icon btn-sm btn-action bg-light btn-edit",onClick:p=>s.toggleEditCostModal(n==null?void 0:n.id)},[t("span",yl,[i(o,{icon:"pencilIcon"})])],8,hl),t("button",{class:"btn rounded-circle btn-icon btn-sm btn-action bg-light btn-delete",onClick:p=>s.deleteCost(n==null?void 0:n.id)},[t("span",$l,[i(o,{icon:"trashIcon"})])],8,wl)])])}),128))]))]))])]),i(a,{addNew:s.toggleAddCostModal,showHelpInfo:!1},null,8,["addNew"]),i(d,{ref:"costModal",loadPage:s.getCosts},null,8,["loadPage"])],64)}const It=Q(el,[["render",Sl]]),Vt=ae("drillBit",()=>({getDrillBits:async({params:r,callback:l})=>{var d;const o=g.get(l,"onSuccess",g.noop),a=g.get(l,"onFinish",g.noop);try{P.setHeader();const n=await P.getWithParams("drillBits",r);o(((d=n.data)==null?void 0:d.data)||n.data)}catch(n){E(n,l)}finally{a()}},getDrillBitDetails:async({id:r,callback:l})=>{var d;const o=g.get(l,"onSuccess",g.noop),a=g.get(l,"onFinish",g.noop);try{P.setHeader();const n=await P.get(`drillBits/${r}`);o(((d=n.data)==null?void 0:d.data)||n.data)}catch(n){E(n,l)}finally{a()}},updateDrillBit:async({id:r,params:l,callback:o})=>{var n;const a=g.get(o,"onSuccess",g.noop),d=g.get(o,"onFinish",g.noop);try{P.setHeader();const u=await P.put(`drillBits/${r}`,l);a(((n=u.data)==null?void 0:n.data)||u.data)}catch(u){E(u,o)}finally{d()}},deleteDrillBit:async({id:r,callback:l})=>{var d;const o=g.get(l,"onSuccess",g.noop),a=g.get(l,"onFinish",g.noop);try{P.setHeader();const n=await P.delete(`drillBits/${r}`);o(((d=n.data)==null?void 0:d.data)||n.data)}catch(n){E(n,l)}finally{a()}},createDrillBit:async({params:r,callback:l})=>{var d;const o=g.get(l,"onSuccess",g.noop),a=g.get(l,"onFinish",g.noop);try{P.setHeader();const n=await P.post("drillBits",r);o(((d=n.data)==null?void 0:d.data)||n.data)}catch(n){E(n,l)}finally{a()}}})),_l=G({name:"bit-modal",components:{SvgIcon:X},props:{loadPage:{type:Function,default:()=>{},required:!0}},setup(s){const e=re(),v=Vt(),b=y(!1),f=y({bitNo:"",type:"",iadcType:"",bitSize:null,depth:null,bitRunDuration:null}),r=y(null),l=y(!1),o=y(""),a=ee("dailyReport");J(o,w=>{w!==""&&d()}),J(b,w=>{var h;w===!1&&(o.value="",T(),(h=r==null?void 0:r.value)==null||h.resetFields())});const d=async()=>{v.getDrillBitDetails({id:o.value,callback:{onSuccess:w=>{f.value=w}}})},n=async w=>{l.value=!0,v.updateDrillBit({id:o.value,params:w,callback:{onSuccess:h=>{s!=null&&s.loadPage&&(s==null||s.loadPage()),p()},onFinish:h=>{l.value=!1}}})},u=async w=>{l.value=!0,v.createDrillBit({params:w,callback:{onSuccess:h=>{s!=null&&s.loadPage&&(s==null||s.loadPage()),p()},onFinish:h=>{l.value=!1}}})},m=()=>{b.value=!0},p=()=>{b.value=!1},D=w=>{o.value=w.toString()},k=y({bitNo:[{required:!0,message:"Please type Bit No.",trigger:"blur"}]}),N=()=>{r.value&&r.value.validate(w=>{var h,_,H,C,F,j,x,A,M;if(w){const z={bitNo:(h=f==null?void 0:f.value)==null?void 0:h.bitNo,type:(_=f==null?void 0:f.value)==null?void 0:_.type,iadcType:(H=f==null?void 0:f.value)==null?void 0:H.iadcType,bitSize:Number((C=f==null?void 0:f.value)==null?void 0:C.bitSize),depth:(F=f==null?void 0:f.value)!=null&&F.depth?Number((j=f==null?void 0:f.value)==null?void 0:j.depth):null,bitRunDuration:(x=f==null?void 0:f.value)!=null&&x.bitRunDuration?Number((A=f==null?void 0:f.value)==null?void 0:A.bitRunDuration):null};o!=null&&o.value?n({...z,dailyReportId:a==null?void 0:a.getDailyReportId()}):a==null||a.createDailyReport({wellId:(M=e==null?void 0:e.params)==null?void 0:M.id,callback:{onSuccess:q=>{u({...z,dailyReportId:q})}}})}})},T=()=>{f.value={bitNo:"",type:"",iadcType:"",bitSize:null,depth:null,bitRunDuration:null}};return{id:o,modal:b,rules:k,loading:l,targetData:f,formRef:r,show:m,hide:p,submit:N,setId:D,reset:T}}}),kl={class:"d-flex align-items-center w-100"},Dl={class:"modal-title"},Il={class:"row g-7 mb-3"},Vl={class:"col-sm-6 fv-row"},Pl={class:"d-flex align-items-center fs-6 fw-semibold mb-2"},Cl={class:"col-sm-6 fv-row"},Tl={class:"d-flex align-items-center fs-6 fw-semibold mb-2"},Fl={class:"row g-7 mb-3"},xl={class:"col-sm-6 fv-row"},Nl={class:"d-flex align-items-center fs-6 fw-semibold mb-2"},Al={class:"col-sm-6 fv-row"},Hl={class:"d-flex align-items-center fs-6 fw-semibold mb-2"},ql={class:"row g-7 mb-3"},Ml={class:"col-sm-6 fv-row"},jl={class:"d-flex align-items-center fs-6 fw-semibold mb-2"},zl={class:"col-sm-6 fv-row"},El={class:"d-flex align-items-center fs-6 fw-semibold mb-2"},Ll={class:"modal-footer d-flex justify-content-center align-items-center"},Rl=["disabled"],Bl=["data-kt-indicator","disabled"],Ol={key:0,class:"indicator-label"},Ul={key:1,class:"indicator-progress"};function Wl(s,e,v,b,f,r){const l=S("SvgIcon"),o=S("el-popover"),a=S("el-input"),d=S("el-form-item"),n=S("el-form"),u=S("el-dialog");return $(),W(u,{modelValue:s.modal,"onUpdate:modelValue":e[8]||(e[8]=m=>s.modal=m),"show-close":!1,width:"800","align-center":""},{header:c(()=>[t("div",kl,[t("h3",Dl,I(`${s.id?"Edit Bit":"New Bit"}`),1),t("span",{class:"cursor-pointer ms-auto",onClick:e[0]||(e[0]=(...m)=>s.hide&&s.hide(...m))},[i(l,{icon:"closeModalIcon"})])])]),default:c(()=>[t("div",null,[i(n,{id:"bit_form",onSubmit:de(s.submit,["prevent"]),model:s.targetData,rules:s.rules,ref:"formRef",class:"form"},{default:c(()=>[t("div",Il,[t("div",Vl,[t("label",Pl,[e[11]||(e[11]=t("span",{class:"required"},"Bit No.",-1)),i(o,{placement:"top",width:300,trigger:"hover"},{reference:c(()=>e[9]||(e[9]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7"},null,-1)])),default:c(()=>[e[10]||(e[10]=t("span",null,'The "Bit No." is a unique identifier or serial number assigned to the drill bit. It helps track the usage and performance history of individual bits. ',-1))]),_:1})]),i(d,{prop:"bitNo",class:"mt-auto"},{default:c(()=>[i(a,{modelValue:s.targetData.bitNo,"onUpdate:modelValue":e[1]||(e[1]=m=>s.targetData.bitNo=m),placeholder:"",name:"bitNo"},null,8,["modelValue"])]),_:1})]),t("div",Cl,[t("label",Tl,[e[14]||(e[14]=L("Type")),i(o,{placement:"top",width:300,trigger:"hover"},{reference:c(()=>e[12]||(e[12]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7"},null,-1)])),default:c(()=>[e[13]||(e[13]=t("span",null,`The "Type" of drill bit refers to the specific design and characteristics of the bit. It can include information about the bit's cutting structure, materials, and intended use. Examples of types include PDC (Polycrystalline Diamond Compact), roller cone, diamond-impregnated, and others. `,-1))]),_:1})]),i(d,{prop:"type"},{default:c(()=>[i(a,{modelValue:s.targetData.type,"onUpdate:modelValue":e[2]||(e[2]=m=>s.targetData.type=m),placeholder:"",name:"bitNo"},null,8,["modelValue"])]),_:1})])]),t("div",Fl,[t("div",xl,[t("label",Nl,[e[17]||(e[17]=L("IADC Type")),i(o,{placement:"top",width:300,trigger:"hover"},{reference:c(()=>e[15]||(e[15]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7"},null,-1)])),default:c(()=>[e[16]||(e[16]=t("span",null,"Bit Type (IADC Classification): The IADC (International Association of Drilling Contractors) classification code, if applicable, which provides standardized codes for various drill bit types. ",-1))]),_:1})]),i(d,{prop:"iadcType",class:"mt-auto"},{default:c(()=>[i(a,{modelValue:s.targetData.iadcType,"onUpdate:modelValue":e[3]||(e[3]=m=>s.targetData.iadcType=m),placeholder:"",name:"iadcType"},null,8,["modelValue"])]),_:1})]),t("div",Al,[t("label",Hl,[e[20]||(e[20]=L("Bit Size (inches)")),i(o,{placement:"top",width:300,trigger:"hover"},{reference:c(()=>e[18]||(e[18]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7"},null,-1)])),default:c(()=>[e[19]||(e[19]=t("span",null,"The size of the drill bit, often specified by its diameter in inches or millimeters. Bit size affects the hole diameter and drilling efficiency. ",-1))]),_:1})]),i(d,{prop:"bitSize"},{default:c(()=>[i(a,{type:"number",class:"w-100",controls:!1,step:"any",modelValue:s.targetData.bitSize,"onUpdate:modelValue":e[4]||(e[4]=m=>s.targetData.bitSize=m),placeholder:"",name:"bitSize"},null,8,["modelValue"])]),_:1})])]),t("div",ql,[t("div",Ml,[t("label",jl,[e[23]||(e[23]=L("Depth")),i(o,{placement:"top",width:300,trigger:"hover"},{reference:c(()=>e[21]||(e[21]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7"},null,-1)])),default:c(()=>[e[22]||(e[22]=t("span",null,'The "Depth" represents the depth at which the drill bit is used or the depth at which it was pulled out of the wellbore. It is typically measured in feet or meters. ',-1))]),_:1})]),i(d,{prop:"depth",class:"mt-auto"},{default:c(()=>[i(a,{type:"number",class:"w-100",controls:!1,step:"any",modelValue:s.targetData.depth,"onUpdate:modelValue":e[5]||(e[5]=m=>s.targetData.depth=m),placeholder:"",name:"depth"},null,8,["modelValue"])]),_:1})]),t("div",zl,[t("label",El,[e[26]||(e[26]=L("Bit Run Duration")),i(o,{placement:"top",width:300,trigger:"hover"},{reference:c(()=>e[24]||(e[24]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7"},null,-1)])),default:c(()=>[e[25]||(e[25]=t("span",null,"The amount of time or footage the drill bit was used before it was pulled out of the hole. It helps assess the bit's performance and longevity. ",-1))]),_:1})]),i(d,{prop:"bitRunDuration"},{default:c(()=>[i(a,{type:"number",class:"w-100",controls:!1,step:"any",modelValue:s.targetData.bitRunDuration,"onUpdate:modelValue":e[6]||(e[6]=m=>s.targetData.bitRunDuration=m),placeholder:"",name:"bitRunDuration"},null,8,["modelValue"])]),_:1})])]),t("div",Ll,[t("button",{type:"button",id:"kt_modal_add_customer_cancel",class:"btn text-gray-700 btn-sm me-3 btn-blue",onClick:e[7]||(e[7]=(...m)=>s.hide&&s.hide(...m)),disabled:s.loading}," Discard ",8,Rl),t("button",{"data-kt-indicator":s.loading?"on":null,class:"btn btn-sm btn-primary",type:"submit",disabled:s.loading},[s.loading?U("",!0):($(),V("span",Ol," Save ")),s.loading?($(),V("span",Ul,e[27]||(e[27]=[L(" Please wait... "),t("span",{class:"spinner-border spinner-border-sm align-middle ms-2"},null,-1)]))):U("",!0)],8,Bl)])]),_:1},8,["onSubmit","model","rules"])])]),_:1},8,["modelValue"])}const Gl=Q(_l,[["render",Wl]]),Yl=G({name:"bits",components:{SvgIcon:X,BottomTool:pe,BitModal:Gl,NoEntries:be},setup(s){const e=Vt(),v=y(null),b=y(!1),f=y([]),r=ee("dailyReport");ie(()=>{r!=null&&r.getDailyReportId()&&l()});const l=async(u={dailyReportId:r==null?void 0:r.getDailyReportId(),page:1,limit:200})=>{b.value=!0,e.getDrillBits({params:u,callback:{onSuccess:m=>{f.value=m==null?void 0:m.items},onFinish:m=>{b.value=!1}}})},o=()=>{var u;(u=v==null?void 0:v.value)==null||u.show()},a=u=>{var m,p;(m=v==null?void 0:v.value)==null||m.setId(u),(p=v==null?void 0:v.value)==null||p.show()},d=u=>{ne.deletionAlert({onConfirmed:()=>{n(u)}})},n=async u=>{b.value=!0,e.deleteDrillBit({id:u,callback:{onSuccess:m=>{l()},onFinish:m=>{b.value=!1}}})};return{loading:b,bitModal:v,drillBitList:f,getDrillBits:l,numberWithCommas:ge,deleteDrillBit:d,toggleEditBitModal:a,toggleNewBitModal:o}}}),Jl={class:"bits"},Ql={class:"row gap-10"},Kl={key:0,class:"text-center"},Xl={class:"card-body gap-5 d-flex flex-column"},Zl={class:"mb-0 text-primary"},eo={class:"d-flex flex-column gap-3"},to={class:"d-flex flex-wrap align-items-center justify-content-between border-bottom-dashed border-bottom-1 border-blue-light pb-3"},so={class:"fw-700 fs-7 text-dark"},lo={class:"d-flex flex-wrap align-items-center justify-content-between"},oo={class:"fw-700 fs-7 text-dark"},no={class:"d-flex"},ao={class:"d-flex flex-wrap gap-3 align-items-center"},io={class:"border border-gray-300 border-dashed rounded py-3 px-4"},ro={class:"fw-700 fs-6 text-gray-700"},uo={class:"border border-gray-300 border-dashed rounded py-3 px-4"},co={class:"fw-700 fs-6 text-gray-700"},fo={class:"border border-gray-300 border-dashed rounded py-3 px-4"},mo={class:"fw-700 fs-6 text-gray-700"},po={class:"d-flex align-items-center gap-2 position-absolute top-0 end-0 transform-top-50"},go=["onClick"],bo={class:"svg-icon svg-icon-3"},vo=["onClick"],ho={class:"svg-icon svg-icon-3 text-danger"};function yo(s,e,v,b,f,r){const l=S("NoEntries"),o=S("SvgIcon"),a=S("BottomTool"),d=S("BitModal");return $(),V(B,null,[t("div",Jl,[t("div",Ql,[s.loading?($(),V("div",Kl,e[0]||(e[0]=[t("div",{class:"spinner-border text-primary",role:"status"},[t("span",{class:"sr-only"},"Loading...")],-1)]))):($(),V(B,{key:1},[s.drillBitList.length===0?($(),W(l,{key:0,addNew:s.toggleNewBitModal},null,8,["addNew"])):($(!0),V(B,{key:1},Z(s.drillBitList,n=>($(),V("div",{key:n.id,class:"col-4 card rounded-3 shadow-sm border-top border-top-2 border-primary position-relative card-item"},[t("div",Xl,[t("h5",Zl,I(n.bitNo),1),t("div",eo,[t("div",to,[e[1]||(e[1]=t("span",{class:"fw-semibold fs-7 text-gray-700"},"Type",-1)),t("span",so,I(n.type),1)]),t("div",lo,[e[2]||(e[2]=t("span",{class:"fw-semibold fs-7 text-gray-700"},"IADC type",-1)),t("span",oo,I(n.iadcType),1)])]),t("div",no,[t("div",ao,[t("div",io,[t("div",ro,I(`${s.numberWithCommas(n.bitSize)} (in)`),1),e[3]||(e[3]=t("div",{class:"fw-semibold fs-6 text-danger"},"Bit Size",-1))]),t("div",uo,[t("div",co,I(`${s.numberWithCommas(n.depth)} (in)`),1),e[4]||(e[4]=t("div",{class:"fw-semibold fs-6 text-primary"},"Depth",-1))]),t("div",fo,[t("div",mo,I(`${s.numberWithCommas(n.bitRunDuration)} (ft)`),1),e[5]||(e[5]=t("div",{class:"fw-semibold fs-6 text-success"}," Bit Run Duration ",-1))])])])]),t("div",po,[t("button",{class:"btn rounded-circle btn-icon btn-sm btn-action bg-light btn-edit",onClick:u=>s.toggleEditBitModal(n.id.toString())},[t("span",bo,[i(o,{icon:"pencilIcon"})])],8,go),t("button",{class:"btn rounded-circle btn-icon btn-sm btn-action bg-light btn-delete",onClick:u=>s.deleteDrillBit(n.id.toString())},[t("span",ho,[i(o,{icon:"trashIcon"})])],8,vo)])]))),128))],64))])]),i(a,{addNew:s.toggleNewBitModal},null,8,["addNew"]),i(d,{ref:"bitModal",loadPage:s.getDrillBits},null,8,["loadPage"])],64)}const Pt=Q(Yl,[["render",yo]]),Ct=ae("casedHole",()=>({getCasedHoles:async({params:r,callback:l})=>{var d;const o=g.get(l,"onSuccess",g.noop),a=g.get(l,"onFinish",g.noop);try{P.setHeader();const n=await P.getWithParams("casedHoles",r);o(((d=n.data)==null?void 0:d.data)||n.data)}catch(n){E(n,l)}finally{a()}},getCasedHoleDetails:async({id:r,callback:l})=>{var d;const o=g.get(l,"onSuccess",g.noop),a=g.get(l,"onFinish",g.noop);try{P.setHeader();const n=await P.get(`casedHoles/${r}`);o(((d=n.data)==null?void 0:d.data)||n.data)}catch(n){E(n,l)}finally{a()}},updateCasedHole:async({id:r,params:l,callback:o})=>{var n;const a=g.get(o,"onSuccess",g.noop),d=g.get(o,"onFinish",g.noop);try{P.setHeader();const u=await P.put(`casedHoles/${r}`,l);a(((n=u.data)==null?void 0:n.data)||u.data)}catch(u){E(u,o)}finally{d()}},deleteCasedHole:async({id:r,callback:l})=>{var d;const o=g.get(l,"onSuccess",g.noop),a=g.get(l,"onFinish",g.noop);try{P.setHeader();const n=await P.delete(`casedHoles/${r}`);o(((d=n.data)==null?void 0:d.data)||n.data)}catch(n){E(n,l)}finally{a()}},createCasedHole:async({params:r,callback:l})=>{var d;const o=g.get(l,"onSuccess",g.noop),a=g.get(l,"onFinish",g.noop);try{P.setHeader();const n=await P.post("casedHoles",r);o(((d=n.data)==null?void 0:d.data)||n.data)}catch(n){E(n,l)}finally{a()}}})),wo=G({name:"cased-hole-modal",components:{SvgIcon:X},props:{loadPage:{type:Function,default:()=>{},required:!0}},setup(s){const e=re(),v=Ct(),b=y(!1),f=y({description:"",topDepth:null,casingShoeDepth:null,casingLength:null,outsideDiameter:null,insideDiameter:null,weight:null}),r=y(null),l=y(!1),o=y(""),a=ee("dailyReport");J(o,w=>{w!==""&&d()}),J(b,w=>{var h;w===!1&&(o.value="",T(),(h=r==null?void 0:r.value)==null||h.resetFields())});const d=async()=>{v.getCasedHoleDetails({id:o.value,callback:{onSuccess:w=>{f.value=w}}})},n=async w=>{l.value=!0,v.updateCasedHole({id:o.value,params:w,callback:{onSuccess:h=>{s!=null&&s.loadPage&&(s==null||s.loadPage()),p()},onFinish:h=>{l.value=!1}}})},u=async w=>{l.value=!0,v.createCasedHole({params:w,callback:{onSuccess:h=>{s!=null&&s.loadPage&&(s==null||s.loadPage()),p()},onFinish:h=>{l.value=!1}}})},m=()=>{b.value=!0},p=()=>{b.value=!1},D=w=>{o.value=w.toString()},k=y({description:[{required:!0,message:"Please type Description",trigger:"blur"}]}),N=()=>{r.value&&r.value.validate(w=>{var h,_,H,C,F,j,x,A,M;if(w){const z={description:(h=f==null?void 0:f.value)==null?void 0:h.description,topDepth:(_=f==null?void 0:f.value)!=null&&_.topDepth?Number((H=f==null?void 0:f.value)==null?void 0:H.topDepth):null,casingShoeDepth:Number((C=f==null?void 0:f.value)==null?void 0:C.casingShoeDepth),casingLength:Number((F=f==null?void 0:f.value)==null?void 0:F.casingLength),outsideDiameter:Number((j=f==null?void 0:f.value)==null?void 0:j.outsideDiameter),insideDiameter:Number((x=f==null?void 0:f.value)==null?void 0:x.insideDiameter),weight:Number((A=f==null?void 0:f.value)==null?void 0:A.weight)};o!=null&&o.value?n({...z,dailyReportId:a==null?void 0:a.getDailyReportId()}):a==null||a.createDailyReport({wellId:(M=e==null?void 0:e.params)==null?void 0:M.id,callback:{onSuccess:q=>{u({...z,dailyReportId:q})}}})}})},T=()=>{f.value={description:"",topDepth:null,casingShoeDepth:null,casingLength:null,outsideDiameter:null,insideDiameter:null,weight:null}};return{id:o,modal:b,rules:k,loading:l,targetData:f,formRef:r,show:m,hide:p,submit:N,setId:D,reset:T}}}),$o={class:"d-flex align-items-center w-100"},So={class:"modal-title"},_o={class:"row g-7 mb-3"},ko={class:"col-sm-6 fv-row"},Do={class:"col-sm-6 fv-row"},Io={class:"d-flex align-items-center fs-6 fw-semibold mb-2"},Vo={class:"row g-7 mb-3"},Po={class:"col-sm-6 fv-row"},Co={class:"d-flex align-items-center fs-6 fw-semibold mb-2"},To={class:"col-sm-6 fv-row"},Fo={class:"d-flex align-items-center fs-6 fw-semibold mb-2"},xo={class:"row g-7 mb-3"},No={class:"col-sm-6 fv-row"},Ao={class:"d-flex align-items-center fs-6 fw-semibold mb-2"},Ho={class:"col-sm-6 fv-row"},qo={class:"d-flex align-items-center fs-6 fw-semibold mb-2"},Mo={class:"row g-7 mb-3"},jo={class:"col-sm-6 fv-row"},zo={class:"d-flex align-items-center fs-6 fw-semibold mb-2"},Eo={class:"modal-footer d-flex justify-content-center align-items-center"},Lo=["disabled"],Ro=["data-kt-indicator","disabled"],Bo={key:0,class:"indicator-label"},Oo={key:1,class:"indicator-progress"};function Uo(s,e,v,b,f,r){const l=S("SvgIcon"),o=S("el-input"),a=S("el-form-item"),d=S("el-popover"),n=S("el-form"),u=S("el-dialog");return $(),W(u,{modelValue:s.modal,"onUpdate:modelValue":e[9]||(e[9]=m=>s.modal=m),"show-close":!1,width:"800","align-center":""},{header:c(()=>[t("div",$o,[t("h3",So,I(`${s.id?"Edit Cased Hole":"New Cased Hole"}`),1),t("span",{class:"cursor-pointer ms-auto",onClick:e[0]||(e[0]=(...m)=>s.hide&&s.hide(...m))},[i(l,{icon:"closeModalIcon"})])])]),default:c(()=>[t("div",null,[i(n,{id:"cased_hole_form",onSubmit:de(s.submit,["prevent"]),model:s.targetData,rules:s.rules,ref:"formRef",class:"form"},{default:c(()=>[t("div",_o,[t("div",ko,[e[10]||(e[10]=t("label",{class:"d-flex align-items-center fs-6 fw-semibold mb-2 text-break required"},"Description ",-1)),i(a,{prop:"description",class:"mt-auto"},{default:c(()=>[i(o,{modelValue:s.targetData.description,"onUpdate:modelValue":e[1]||(e[1]=m=>s.targetData.description=m),placeholder:"",name:"description"},null,8,["modelValue"])]),_:1})]),t("div",Do,[t("label",Io,[e[13]||(e[13]=L("Top (Top Depth) (ft)")),i(d,{placement:"top",width:300,trigger:"hover"},{reference:c(()=>e[11]||(e[11]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7"},null,-1)])),default:c(()=>[e[12]||(e[12]=t("span",null,"The Top Depth refers to the depth at which the casing is set or begins in the wellbore.",-1))]),_:1})]),i(a,{prop:"topDepth"},{default:c(()=>[i(o,{type:"number",class:"w-100",controls:!1,step:"any",modelValue:s.targetData.topDepth,"onUpdate:modelValue":e[2]||(e[2]=m=>s.targetData.topDepth=m),placeholder:"",name:"topDepth"},null,8,["modelValue"])]),_:1})])]),t("div",Vo,[t("div",Po,[t("label",Co,[e[16]||(e[16]=L(" OD (Outside Diameter) (in)")),i(d,{placement:"top",width:300,trigger:"hover"},{reference:c(()=>e[14]||(e[14]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7"},null,-1)])),default:c(()=>[e[15]||(e[15]=t("span",null,"The Outside Diameter is the outer measurement of the casing pipe, typically in inches or millimeters.",-1))]),_:1})]),i(a,{prop:"outsideDiameter"},{default:c(()=>[i(o,{type:"number",class:"w-100",controls:!1,step:"any",modelValue:s.targetData.outsideDiameter,"onUpdate:modelValue":e[3]||(e[3]=m=>s.targetData.outsideDiameter=m),placeholder:"",name:"outsideDiameter"},null,8,["modelValue"])]),_:1})]),t("div",To,[t("label",Fo,[e[19]||(e[19]=L("Wt (Weight) (lb/ft)")),i(d,{placement:"top",width:300,trigger:"hover"},{reference:c(()=>e[17]||(e[17]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7"},null,-1)])),default:c(()=>[e[18]||(e[18]=t("span",null,"The Weight of the casing pipe is typically expressed in pounds per foot (lb/ft) and represents the mass of the casing material per linear foot.",-1))]),_:1})]),i(a,{prop:"weight"},{default:c(()=>[i(o,{type:"number",class:"w-100",controls:!1,step:"any",modelValue:s.targetData.weight,"onUpdate:modelValue":e[4]||(e[4]=m=>s.targetData.weight=m),placeholder:"",name:"weight"},null,8,["modelValue"])]),_:1})])]),t("div",xo,[t("div",No,[t("label",Ao,[e[22]||(e[22]=L(" ID (Inside Diameter) (in)")),i(d,{placement:"top",width:300,trigger:"hover"},{reference:c(()=>e[20]||(e[20]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7"},null,-1)])),default:c(()=>[e[21]||(e[21]=t("span",null,"The Inside Diameter is the inner measurement of the casing pipe, typically in inches or millimeters.",-1))]),_:1})]),i(a,{prop:"insideDiameter"},{default:c(()=>[i(o,{type:"number",class:"w-100",controls:!1,step:"any",modelValue:s.targetData.insideDiameter,"onUpdate:modelValue":e[5]||(e[5]=m=>s.targetData.insideDiameter=m),placeholder:"",name:"insideDiameter"},null,8,["modelValue"])]),_:1})]),t("div",Ho,[t("label",qo,[e[25]||(e[25]=L("Casing Shoe Depth (ft)")),i(d,{placement:"top",width:300,trigger:"hover"},{reference:c(()=>e[23]||(e[23]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7"},null,-1)])),default:c(()=>[e[24]||(e[24]=t("span",null,"The depth at which the casing shoe is located. This is the point where the casing is designed to support the weight of the wellbore fluids.",-1))]),_:1})]),i(a,{prop:"casingShoeDepth"},{default:c(()=>[i(o,{type:"number",class:"w-100",controls:!1,step:"any",modelValue:s.targetData.casingShoeDepth,"onUpdate:modelValue":e[6]||(e[6]=m=>s.targetData.casingShoeDepth=m),placeholder:"",name:"casingShoeDepth"},null,8,["modelValue"])]),_:1})])]),t("div",Mo,[t("div",jo,[t("label",zo,[e[28]||(e[28]=L(" Casing Length (ft)")),i(d,{placement:"top",width:300,trigger:"hover"},{reference:c(()=>e[26]||(e[26]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7"},null,-1)])),default:c(()=>[e[27]||(e[27]=t("span",null,"The total length of the casing string in the wellbore, which may include multiple casing joints connected together.",-1))]),_:1})]),i(a,{prop:"casingLength"},{default:c(()=>[i(o,{type:"number",class:"w-100",controls:!1,step:"any",modelValue:s.targetData.casingLength,"onUpdate:modelValue":e[7]||(e[7]=m=>s.targetData.casingLength=m),placeholder:"",name:"casingLength"},null,8,["modelValue"])]),_:1})])]),t("div",Eo,[t("button",{type:"button",id:"kt_modal_add_customer_cancel",class:"btn text-gray-700 btn-sm me-3 btn-blue",onClick:e[8]||(e[8]=(...m)=>s.hide&&s.hide(...m)),disabled:s.loading}," Discard ",8,Lo),t("button",{"data-kt-indicator":s.loading?"on":null,class:"btn btn-sm btn-primary",type:"submit",disabled:s.loading},[s.loading?U("",!0):($(),V("span",Bo," Save ")),s.loading?($(),V("span",Oo,e[29]||(e[29]=[L(" Please wait... "),t("span",{class:"spinner-border spinner-border-sm align-middle ms-2"},null,-1)]))):U("",!0)],8,Ro)])]),_:1},8,["onSubmit","model","rules"])])]),_:1},8,["modelValue"])}const Wo=Q(wo,[["render",Uo]]),Go=G({name:"cased-hole",components:{SvgIcon:X,BottomTool:pe,CasedHoleModal:Wo,NoEntries:be},setup(){const s=y(null),e=y(!1),v=Ct(),b=y([]),f=ee("dailyReport");ie(()=>{f!=null&&f.getDailyReportId()&&r()});const r=async(n={dailyReportId:f==null?void 0:f.getDailyReportId(),page:1,limit:200})=>{e.value=!0,v.getCasedHoles({params:n,callback:{onSuccess:u=>{b.value=u==null?void 0:u.items},onFinish:u=>{e.value=!1}}})},l=()=>{var n;(n=s==null?void 0:s.value)==null||n.show()},o=n=>{var u,m;(u=s==null?void 0:s.value)==null||u.setId(n),(m=s==null?void 0:s.value)==null||m.show()},a=n=>{ne.deletionAlert({onConfirmed:()=>{d(n)}})},d=async n=>{e.value=!0,v.deleteCasedHole({id:n,callback:{onSuccess:u=>{r()},onFinish:u=>{e.value=!1}}})};return{loading:e,casedHoleList:b,casedHoleModal:s,numberWithCommas:ge,getCasedHoles:r,deleteCasedHole:a,toggleEditCasedHoleModal:o,toggleNewCasedHoleModal:l}}}),Yo={class:"cased-hole"},Jo={class:"row gap-10"},Qo={key:0,class:"text-center"},Ko={class:"card-body gap-5 d-flex flex-column"},Xo={class:"mb-0 text-primary"},Zo={class:"d-flex flex-column gap-3"},en={class:"d-flex flex-wrap align-items-center justify-content-between border-bottom-dashed border-bottom-1 border-blue-light pb-3"},tn={class:"fw-700 fs-7 text-dark"},sn={class:"d-flex flex-wrap align-items-center justify-content-between border-bottom-dashed border-bottom-1 border-blue-light pb-3"},ln={class:"fw-700 fs-7 text-dark"},on={class:"d-flex flex-wrap align-items-center justify-content-between"},nn={class:"fw-700 fs-7 text-dark"},an={class:"d-flex justify-content-center"},dn={class:"d-flex flex-wrap gap-3 align-items-center"},rn={class:"border border-gray-300 border-dashed rounded py-3 px-4"},un={class:"fw-700 fs-6 text-gray-700"},cn={class:"border border-gray-300 border-dashed rounded py-3 px-4"},fn={class:"fw-700 fs-6 text-gray-700"},mn={class:"border border-gray-300 border-dashed rounded py-3 px-4"},pn={class:"fw-700 fs-6 text-gray-700"},gn={class:"d-flex align-items-center gap-2 position-absolute top-0 end-0 transform-top-50"},bn=["onClick"],vn={class:"svg-icon svg-icon-3"},hn=["onClick"],yn={class:"svg-icon svg-icon-3 text-danger"};function wn(s,e,v,b,f,r){const l=S("NoEntries"),o=S("SvgIcon"),a=S("BottomTool"),d=S("CasedHoleModal");return $(),V(B,null,[t("div",Yo,[t("div",Jo,[s.loading?($(),V("div",Qo,e[0]||(e[0]=[t("div",{class:"spinner-border text-primary",role:"status"},[t("span",{class:"sr-only"},"Loading...")],-1)]))):($(),V(B,{key:1},[s.casedHoleList.length===0?($(),W(l,{key:0,addNew:s.toggleNewCasedHoleModal},null,8,["addNew"])):($(!0),V(B,{key:1},Z(s.casedHoleList,n=>($(),V("div",{key:n.id,class:"col-4 card rounded-3 shadow-sm border-top border-top-2 border-primary position-relative card-item"},[t("div",Ko,[t("h5",Xo,I(n.description),1),t("div",Zo,[t("div",en,[e[1]||(e[1]=t("span",{class:"fw-semibold fs-7 text-gray-700"},"Top Depth",-1)),t("span",tn,I(`${n.topDepth} (ft)`),1)]),t("div",sn,[e[3]||(e[3]=t("span",{class:"fw-semibold fs-7 text-gray-700"},"Casing Shoe Depth",-1)),t("span",ln,[L(I(s.numberWithCommas(n.casingShoeDepth))+" ",1),e[2]||(e[2]=t("span",{class:"ms-1 fw-normal"},"(in)",-1))])]),t("div",on,[e[5]||(e[5]=t("span",{class:"fw-semibold fs-7 text-gray-700"},"Casing Length",-1)),t("span",nn,[L(I(s.numberWithCommas(n.casingLength))+" ",1),e[4]||(e[4]=t("span",{class:"ms-1 fw-normal"},"(lb/ft)",-1))])])]),t("div",an,[t("div",dn,[t("div",rn,[t("div",un,I(`${s.numberWithCommas(n.outsideDiameter)} (in)`),1),e[6]||(e[6]=t("div",{class:"fw-semibold fs-6 text-danger"},"OD",-1))]),t("div",cn,[t("div",fn,I(`${s.numberWithCommas(n.insideDiameter)} (in)`),1),e[7]||(e[7]=t("div",{class:"fw-semibold fs-6 text-primary"},"ID",-1))]),t("div",mn,[t("div",pn,I(`${s.numberWithCommas(n.weight)} (lb/ft)`),1),e[8]||(e[8]=t("div",{class:"fw-semibold fs-6 text-success"},"Wt",-1))])])])]),t("div",gn,[t("button",{class:"btn rounded-circle btn-icon btn-sm btn-action bg-light btn-edit",onClick:u=>s.toggleEditCasedHoleModal(n.id.toString())},[t("span",vn,[i(o,{icon:"pencilIcon"})])],8,bn),t("button",{class:"btn rounded-circle btn-icon btn-sm btn-action bg-light btn-delete",onClick:u=>{var m;return s.deleteCasedHole((m=n==null?void 0:n.id)==null?void 0:m.toString())}},[t("span",yn,[i(o,{icon:"trashIcon"})])],8,hn)])]))),128))],64))])]),i(a,{addNew:s.toggleNewCasedHoleModal},null,8,["addNew"]),i(d,{ref:"casedHoleModal",loadPage:s.getCasedHoles},null,8,["loadPage"])],64)}const Tt=Q(Go,[["render",wn]]),Ft=ae("drillString",()=>({getDrillStrings:async({params:r,callback:l})=>{var d;const o=g.get(l,"onSuccess",g.noop),a=g.get(l,"onFinish",g.noop);try{P.setHeader();const n=await P.getWithParams("drillStrings",r);o(((d=n.data)==null?void 0:d.data)||n.data)}catch(n){E(n,l)}finally{a()}},getDrillStringDetails:async({id:r,callback:l})=>{var d;const o=g.get(l,"onSuccess",g.noop),a=g.get(l,"onFinish",g.noop);try{P.setHeader();const n=await P.get(`drillStrings/${r}`);o(((d=n.data)==null?void 0:d.data)||n.data)}catch(n){E(n,l)}finally{a()}},updateDrillString:async({id:r,params:l,callback:o})=>{var n;const a=g.get(o,"onSuccess",g.noop),d=g.get(o,"onFinish",g.noop);try{P.setHeader();const u=await P.put(`drillStrings/${r}`,l);a(((n=u.data)==null?void 0:n.data)||u.data)}catch(u){E(u,o)}finally{d()}},deleteDrillString:async({id:r,callback:l})=>{var d;const o=g.get(l,"onSuccess",g.noop),a=g.get(l,"onFinish",g.noop);try{P.setHeader();const n=await P.delete(`drillStrings/${r}`);o(((d=n.data)==null?void 0:d.data)||n.data)}catch(n){E(n,l)}finally{a()}},createDrillString:async({params:r,callback:l})=>{var d;const o=g.get(l,"onSuccess",g.noop),a=g.get(l,"onFinish",g.noop);try{P.setHeader();const n=await P.post("drillStrings",r);o(((d=n.data)==null?void 0:d.data)||n.data)}catch(n){E(n,l)}finally{a()}}})),$n=G({name:"drill-string-modal",components:{SvgIcon:X},props:{loadPage:{type:Function,default:()=>{},required:!0}},setup(s){const e=re(),v=Ft(),b=y(!1),f=y({description:"",insideDiameter:null,outsideDiameter:null,length:null,weight:null}),r=y(null),l=y(!1),o=y(""),a=ee("dailyReport");J(o,w=>{w!==""&&d()}),J(b,w=>{var h;w===!1&&(o.value="",T(),(h=r==null?void 0:r.value)==null||h.resetFields())});const d=async()=>{v.getDrillStringDetails({id:o.value,callback:{onSuccess:w=>{f.value=w}}})},n=async w=>{l.value=!0,v.updateDrillString({id:o.value,params:w,callback:{onSuccess:h=>{s!=null&&s.loadPage&&(s==null||s.loadPage()),p()},onFinish:h=>{l.value=!1}}})},u=async w=>{l.value=!0,v.createDrillString({params:w,callback:{onSuccess:h=>{s!=null&&s.loadPage&&(s==null||s.loadPage()),p()},onFinish:h=>{l.value=!1}}})},m=()=>{b.value=!0},p=()=>{b.value=!1},D=w=>{o.value=w.toString()},k=y({description:[{required:!0,message:"Please type Description",trigger:"blur"}]}),N=()=>{r.value&&r.value.validate(w=>{var h,_,H,C,F,j;if(w){const x={description:(h=f==null?void 0:f.value)==null?void 0:h.description,insideDiameter:Number((_=f==null?void 0:f.value)==null?void 0:_.insideDiameter),outsideDiameter:Number((H=f==null?void 0:f.value)==null?void 0:H.outsideDiameter),length:Number((C=f==null?void 0:f.value)==null?void 0:C.length),weight:Number((F=f==null?void 0:f.value)==null?void 0:F.weight)};o!=null&&o.value?n({...x,dailyReportId:a==null?void 0:a.getDailyReportId()}):a==null||a.createDailyReport({wellId:(j=e==null?void 0:e.params)==null?void 0:j.id,callback:{onSuccess:A=>{u({...x,dailyReportId:A})}}})}})},T=()=>{f.value={description:"",insideDiameter:null,outsideDiameter:null,length:null,weight:null}};return{id:o,modal:b,rules:k,loading:l,targetData:f,formRef:r,show:m,hide:p,submit:N,setId:D,reset:T}}}),Sn={class:"d-flex align-items-center w-100"},_n={class:"modal-title"},kn={class:"row g-7 mb-3"},Dn={class:"col-sm-6 fv-row"},In={class:"col-sm-6 fv-row"},Vn={class:"d-flex align-items-center fs-6 fw-semibold mb-2"},Pn={class:"row g-7 mb-3"},Cn={class:"col-sm-6 fv-row"},Tn={class:"d-flex align-items-center fs-6 fw-semibold mb-2"},Fn={class:"col-sm-6 fv-row"},xn={class:"d-flex align-items-center fs-6 fw-semibold mb-2"},Nn={class:"row g-7 mb-3"},An={class:"col-sm-6 fv-row"},Hn={class:"d-flex align-items-center fs-6 fw-semibold mb-2"},qn={class:"modal-footer d-flex justify-content-center align-items-center"},Mn=["disabled"],jn=["data-kt-indicator","disabled"],zn={key:0,class:"indicator-label"},En={key:1,class:"indicator-progress"};function Ln(s,e,v,b,f,r){const l=S("SvgIcon"),o=S("el-input"),a=S("el-form-item"),d=S("el-popover"),n=S("el-form"),u=S("el-dialog");return $(),W(u,{modelValue:s.modal,"onUpdate:modelValue":e[7]||(e[7]=m=>s.modal=m),"show-close":!1,width:"800","align-center":""},{header:c(()=>[t("div",Sn,[t("h3",_n,I(`${s.id?"Edit Drill String":"New Drill String"}`),1),t("span",{class:"cursor-pointer ms-auto",onClick:e[0]||(e[0]=(...m)=>s.hide&&s.hide(...m))},[i(l,{icon:"closeModalIcon"})])])]),default:c(()=>[t("div",null,[i(n,{id:"drill_string_form",onSubmit:de(s.submit,["prevent"]),model:s.targetData,rules:s.rules,ref:"formRef",class:"form"},{default:c(()=>[t("div",kn,[t("div",Dn,[e[8]||(e[8]=t("label",{class:"d-flex align-items-center fs-6 fw-semibold mb-2 text-break required"},"Description ",-1)),i(a,{prop:"description",class:"mt-auto"},{default:c(()=>[i(o,{modelValue:s.targetData.description,"onUpdate:modelValue":e[1]||(e[1]=m=>s.targetData.description=m),placeholder:"",name:"description"},null,8,["modelValue"])]),_:1})]),t("div",In,[t("label",Vn,[e[11]||(e[11]=L("Wt. (Weight) (lb/ft)")),i(d,{placement:"top",width:300,trigger:"hover"},{reference:c(()=>e[9]||(e[9]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7"},null,-1)])),default:c(()=>[e[10]||(e[10]=t("span",null,"The Weight of the drill pipe or tool is typically expressed in pounds per foot (lb/ft) and represents the mass of the component per linear foot. It helps determine the buoyancy and load-bearing capacity of the drill string. It is an input and doesn't require additional information. ",-1))]),_:1})]),i(a,{prop:"weight"},{default:c(()=>[i(o,{type:"number",class:"w-100",controls:!1,step:"any",modelValue:s.targetData.weight,"onUpdate:modelValue":e[2]||(e[2]=m=>s.targetData.weight=m),placeholder:"",name:"weight"},null,8,["modelValue"])]),_:1})])]),t("div",Pn,[t("div",Cn,[t("label",Tn,[e[14]||(e[14]=L("Tool Joint OD (Outside Diameter) (in)")),i(d,{placement:"top",width:300,trigger:"hover"},{reference:c(()=>e[12]||(e[12]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7"},null,-1)])),default:c(()=>[e[13]||(e[13]=t("span",null,"Tool joints are specialized connectors used to join sections of drill pipe. Tool Joint OD is the outer diameter of the tool joint, typically larger than the drill pipe OD. It is an input and doesn't require additional information. ",-1))]),_:1})]),i(a,{prop:"outsideDiameter"},{default:c(()=>[i(o,{type:"number",class:"w-100",controls:!1,step:"any",modelValue:s.targetData.outsideDiameter,"onUpdate:modelValue":e[3]||(e[3]=m=>s.targetData.outsideDiameter=m),placeholder:"",name:"outsideDiameter"},null,8,["modelValue"])]),_:1})]),t("div",Fn,[t("label",xn,[e[17]||(e[17]=L(" Tool Joint ID (Inside Diameter) (in)")),i(d,{placement:"top",width:300,trigger:"hover"},{reference:c(()=>e[15]||(e[15]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7"},null,-1)])),default:c(()=>[e[16]||(e[16]=t("span",null,"Tool Joint ID is the inner diameter of the tool joint, representing the clear passage inside the tool joint. It is an input and doesn't require additional information. ",-1))]),_:1})]),i(a,{prop:"insideDiameter"},{default:c(()=>[i(o,{type:"number",class:"w-100",controls:!1,step:"any",modelValue:s.targetData.insideDiameter,"onUpdate:modelValue":e[4]||(e[4]=m=>s.targetData.insideDiameter=m),placeholder:"",name:"insideDiameter"},null,8,["modelValue"])]),_:1})])]),t("div",Nn,[t("div",An,[t("label",Hn,[e[20]||(e[20]=L("Length (ft)")),i(d,{placement:"top",width:300,trigger:"hover"},{reference:c(()=>e[18]||(e[18]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7"},null,-1)])),default:c(()=>[e[19]||(e[19]=t("span",null,"The Length represents the total length of the drill string component, such as a section of drill pipe or a tool. ",-1))]),_:1})]),i(a,{prop:"length"},{default:c(()=>[i(o,{type:"number",class:"w-100",controls:!1,step:"any",modelValue:s.targetData.length,"onUpdate:modelValue":e[5]||(e[5]=m=>s.targetData.length=m),placeholder:"",name:"length"},null,8,["modelValue"])]),_:1})])]),t("div",qn,[t("button",{type:"button",id:"kt_modal_add_customer_cancel",class:"btn text-gray-700 btn-sm me-3 btn-blue",onClick:e[6]||(e[6]=(...m)=>s.hide&&s.hide(...m)),disabled:s.loading}," Discard ",8,Mn),t("button",{"data-kt-indicator":s.loading?"on":null,class:"btn btn-sm btn-primary",type:"submit",disabled:s.loading},[s.loading?U("",!0):($(),V("span",zn," Save ")),s.loading?($(),V("span",En,e[21]||(e[21]=[L(" Please wait... "),t("span",{class:"spinner-border spinner-border-sm align-middle ms-2"},null,-1)]))):U("",!0)],8,jn)])]),_:1},8,["onSubmit","model","rules"])])]),_:1},8,["modelValue"])}const Rn=Q($n,[["render",Ln]]),Bn=G({name:"drill-string",components:{SvgIcon:X,BottomTool:pe,DrillStringModal:Rn,NoEntries:be},setup(s){const e=y(null),v=y(!1),b=Ft(),f=y([]),r=ee("dailyReport");ie(()=>{r!=null&&r.getDailyReportId()&&l()});const l=async(u={dailyReportId:r==null?void 0:r.getDailyReportId(),page:1,limit:200})=>{v.value=!0,b.getDrillStrings({params:u,callback:{onSuccess:m=>{f.value=m==null?void 0:m.items},onFinish:m=>{v.value=!1}}})},o=()=>{var u;(u=e==null?void 0:e.value)==null||u.show()},a=u=>{var m,p;(m=e==null?void 0:e.value)==null||m.setId(u),(p=e==null?void 0:e.value)==null||p.show()},d=u=>{ne.deletionAlert({onConfirmed:()=>{n(u)}})},n=async u=>{v.value=!0,b.deleteDrillString({id:u,callback:{onSuccess:m=>{l()},onFinish:m=>{v.value=!1}}})};return{loading:v,drillStringList:f,drillStringModal:e,getDrillStrings:l,numberWithCommas:ge,deleteDrillString:d,toggleEditDrillStringModal:a,toggleNewDrillStringModal:o}}}),On={class:"drill-string"},Un={class:"row gap-10"},Wn={key:0,class:"text-center"},Gn={class:"card-body gap-5 d-flex flex-column"},Yn={class:"mb-0 text-primary"},Jn={class:"d-flex flex-column gap-3"},Qn={class:"d-flex flex-wrap align-items-center justify-content-between border-bottom-dashed border-bottom-1 border-blue-light pb-3"},Kn={class:"fw-700 fs-7 text-dark"},Xn={class:"d-flex flex-wrap align-items-center justify-content-between border-bottom-dashed border-bottom-1 border-blue-light pb-3"},Zn={class:"fw-700 fs-7 text-dark"},ea={class:"d-flex flex-wrap align-items-center justify-content-between"},ta={class:"fw-700 fs-7 text-dark"},sa={class:"d-flex justify-content-center"},la={class:"d-flex flex-wrap gap-3 align-items-center"},oa={class:"border border-gray-300 border-dashed rounded py-3 px-4"},na={class:"fw-700 fs-6 text-gray-700"},aa={class:"border border-gray-300 border-dashed rounded py-3 px-4"},ia={class:"fw-700 fs-6 text-gray-700"},da={class:"border border-gray-300 border-dashed rounded py-3 px-4"},ra={class:"fw-700 fs-6 text-gray-700"},ua={class:"d-flex align-items-center gap-2 position-absolute top-0 end-0 transform-top-50"},ca=["onClick"],fa={class:"svg-icon svg-icon-3"},ma=["onClick"],pa={class:"svg-icon svg-icon-3 text-danger"};function ga(s,e,v,b,f,r){const l=S("NoEntries"),o=S("SvgIcon"),a=S("BottomTool"),d=S("DrillStringModal");return $(),V(B,null,[t("div",On,[t("div",Un,[s.loading?($(),V("div",Wn,e[0]||(e[0]=[t("div",{class:"spinner-border text-primary",role:"status"},[t("span",{class:"sr-only"},"Loading...")],-1)]))):($(),V(B,{key:1},[s.drillStringList.length===0?($(),W(l,{key:0,addNew:s.toggleNewDrillStringModal},null,8,["addNew"])):($(!0),V(B,{key:1},Z(s.drillStringList,n=>($(),V("div",{key:n==null?void 0:n.id,class:"col-4 card rounded-3 shadow-sm border-top border-top-2 border-primary position-relative card-item"},[t("div",Gn,[t("h5",Yn,I(n==null?void 0:n.description),1),t("div",Jn,[t("div",Qn,[e[1]||(e[1]=t("span",{class:"fw-semibold fs-7 text-gray-700"},"Tool Joint OD (Outside Diameter)",-1)),t("span",Kn,I(`${s.numberWithCommas(n==null?void 0:n.outsideDiameter)} (in)`),1)]),t("div",Xn,[e[2]||(e[2]=t("span",{class:"fw-semibold fs-7 text-gray-700"},"Tool Joint ID (Inside Diameter)",-1)),t("span",Zn,I(`${s.numberWithCommas(n==null?void 0:n.insideDiameter)} (in)`),1)]),t("div",ea,[e[3]||(e[3]=t("span",{class:"fw-semibold fs-7 text-gray-700"},"Length",-1)),t("span",ta,I(`${s.numberWithCommas(n==null?void 0:n.length)} (ft)`),1)])]),t("div",sa,[t("div",la,[t("div",oa,[t("div",na,I(`${s.numberWithCommas(n==null?void 0:n.outsideDiameter)} (in)`),1),e[4]||(e[4]=t("div",{class:"fw-semibold fs-6 text-danger"},"OD",-1))]),t("div",aa,[t("div",ia,I(`${s.numberWithCommas(n==null?void 0:n.insideDiameter)} (in)`),1),e[5]||(e[5]=t("div",{class:"fw-semibold fs-6 text-primary"},"ID",-1))]),t("div",da,[t("div",ra,I(`${s.numberWithCommas(n==null?void 0:n.weight)} (lb/ft)`),1),e[6]||(e[6]=t("div",{class:"fw-semibold fs-6 text-success"},"Wt",-1))])])])]),t("div",ua,[t("button",{class:"btn rounded-circle btn-icon btn-sm btn-action bg-light btn-edit",onClick:u=>s.toggleEditDrillStringModal(n==null?void 0:n.id.toString())},[t("span",fa,[i(o,{icon:"pencilIcon"})])],8,ca),t("button",{class:"btn rounded-circle btn-icon btn-sm btn-action bg-light btn-delete",onClick:u=>s.deleteDrillString(n==null?void 0:n.id.toString())},[t("span",pa,[i(o,{icon:"trashIcon"})])],8,ma)])]))),128))],64))])]),i(a,{addNew:s.toggleNewDrillStringModal},null,8,["addNew"]),i(d,{ref:"drillStringModal",loadPage:s.getDrillStrings},null,8,["loadPage"])],64)}const xt=Q(Bn,[["render",ga]]),Nt=ae("nozzle",()=>({getNozzles:async({params:l,callback:o})=>{var n;const a=g.get(o,"onSuccess",g.noop),d=g.get(o,"onFinish",g.noop);try{P.setHeader();const u=await P.getWithParams("nozzles",l);a(((n=u.data)==null?void 0:n.data)||u.data)}catch(u){E(u,o)}finally{d()}},getNozzleDetails:async({id:l,callback:o})=>{var n;const a=g.get(o,"onSuccess",g.noop),d=g.get(o,"onFinish",g.noop);try{P.setHeader();const u=await P.get(`nozzles/${l}`);a(((n=u.data)==null?void 0:n.data)||u.data)}catch(u){E(u,o)}finally{d()}},updateNozzle:async({id:l,params:o,callback:a})=>{var u;const d=g.get(a,"onSuccess",g.noop),n=g.get(a,"onFinish",g.noop);try{P.setHeader();const m=await P.put(`nozzles/${l}`,o);d(((u=m.data)==null?void 0:u.data)||m.data)}catch(m){E(m,a)}finally{n()}},deleteNozzle:async({id:l,callback:o})=>{var n;const a=g.get(o,"onSuccess",g.noop),d=g.get(o,"onFinish",g.noop);try{P.setHeader();const u=await P.delete(`nozzles/${l}`);a(((n=u.data)==null?void 0:n.data)||u.data)}catch(u){E(u,o)}finally{d()}},createNozzle:async({params:l,callback:o})=>{var n;const a=g.get(o,"onSuccess",g.noop),d=g.get(o,"onFinish",g.noop);try{P.setHeader();const u=await P.post("nozzles",l);a(((n=u.data)==null?void 0:n.data)||u.data)}catch(u){E(u,o)}finally{d()}},getNozzleTFA:async({dailyReportId:l,callback:o})=>{var n,u,m;const a=g.get(o,"onSuccess",g.noop),d=g.get(o,"onFinish",g.noop);try{P.setHeader();const p=await P.get(`nozzles/tfa/${l}`);a(((n=p.data)==null?void 0:n.data)!==void 0&&((u=p.data)==null?void 0:u.data)!==null?(m=p.data)==null?void 0:m.data:p.data)}catch(p){E(p,o)}finally{d()}}})),ba=G({name:"nozzle-modal",components:{SvgIcon:X},props:{loadPage:{type:Function,default:()=>{},required:!0}},setup(s){const e=re(),v=Nt(),b=y(!1),f=y({identificationNumber:"",orificeSize:null}),r=y(null),l=y(!1),o=y(""),a=ee("dailyReport");J(o,w=>{w!==""&&d()}),J(b,w=>{var h;w===!1&&(o.value="",T(),(h=r==null?void 0:r.value)==null||h.resetFields())});const d=async()=>{v.getNozzleDetails({id:o.value,callback:{onSuccess:w=>{f.value=w}}})},n=async w=>{l.value=!0,v.updateNozzle({id:o.value,params:w,callback:{onSuccess:h=>{s!=null&&s.loadPage&&(s==null||s.loadPage()),p()},onFinish:h=>{l.value=!1}}})},u=async w=>{l.value=!0,v.createNozzle({params:w,callback:{onSuccess:h=>{s!=null&&s.loadPage&&(s==null||s.loadPage()),p()},onFinish:h=>{l.value=!1}}})},m=()=>{b.value=!0},p=()=>{b.value=!1},D=w=>{o.value=w.toString()},k=y({identificationNumber:[{required:!0,message:"Please type Identification Number",trigger:"blur"}]}),N=()=>{r.value&&r.value.validate(w=>{var h,_,H;if(w){l.value=!0;const C={identificationNumber:(h=f==null?void 0:f.value)==null?void 0:h.identificationNumber,orificeSize:Number((_=f==null?void 0:f.value)==null?void 0:_.orificeSize)};o!=null&&o.value?n({...C,dailyReportId:a==null?void 0:a.getDailyReportId()}):a==null||a.createDailyReport({wellId:(H=e==null?void 0:e.params)==null?void 0:H.id,callback:{onSuccess:F=>{u({...C,dailyReportId:F})}}})}})},T=()=>{f.value={identificationNumber:"",orificeSize:null}};return{id:o,modal:b,rules:k,loading:l,targetData:f,formRef:r,show:m,hide:p,submit:N,setId:D,reset:T}}}),va={class:"d-flex align-items-center w-100"},ha={class:"modal-title"},ya={class:"row g-7 mb-3"},wa={class:"col-12 fv-row"},$a={class:"d-flex align-items-center fs-6 fw-semibold mb-2"},Sa={class:"row g-7 mb-3"},_a={class:"col-12 fv-row"},ka={class:"d-flex align-items-center fs-6 fw-semibold mb-2"},Da={class:"modal-footer d-flex justify-content-center align-items-center"},Ia=["disabled"],Va=["data-kt-indicator","disabled"],Pa={key:0,class:"indicator-label"},Ca={key:1,class:"indicator-progress"};function Ta(s,e,v,b,f,r){const l=S("SvgIcon"),o=S("el-popover"),a=S("el-input"),d=S("el-form-item"),n=S("el-form"),u=S("el-dialog");return $(),W(u,{modelValue:s.modal,"onUpdate:modelValue":e[4]||(e[4]=m=>s.modal=m),"show-close":!1,width:"500","align-center":""},{header:c(()=>[t("div",va,[t("h3",ha,I(`${s.id?"Edit Nozzle":"New Nozzle"}`),1),t("span",{class:"cursor-pointer ms-auto",onClick:e[0]||(e[0]=(...m)=>s.hide&&s.hide(...m))},[i(l,{icon:"closeModalIcon"})])])]),default:c(()=>[t("div",null,[i(n,{id:"nozzle_form",onSubmit:de(s.submit,["prevent"]),model:s.targetData,rules:s.rules,ref:"formRef",class:"form"},{default:c(()=>[t("div",ya,[t("div",wa,[t("label",$a,[e[7]||(e[7]=t("span",{class:"required"},"Identification Number",-1)),i(o,{placement:"top",width:300,trigger:"hover"},{reference:c(()=>e[5]||(e[5]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7"},null,-1)])),default:c(()=>[e[6]||(e[6]=t("span",null,'The "No." or Number refers to the identification or serial number assigned to the nozzle. It helps track the specific nozzle being used or replaced. ',-1))]),_:1})]),i(d,{prop:"identificationNumber",class:"mt-auto"},{default:c(()=>[i(a,{modelValue:s.targetData.identificationNumber,"onUpdate:modelValue":e[1]||(e[1]=m=>s.targetData.identificationNumber=m),placeholder:"",name:"identificationNumber"},null,8,["modelValue"])]),_:1})])]),t("div",Sa,[t("div",_a,[t("label",ka,[e[10]||(e[10]=t("span",null,"Orifice Size",-1)),i(o,{placement:"top",width:300,trigger:"hover"},{reference:c(()=>e[8]||(e[8]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7"},null,-1)])),default:c(()=>[e[9]||(e[9]=t("span",null,`The "Orifice Size" represents the diameter of the nozzle's orifice, typically measured in 1/32-inch increments. The nozzle size is crucial as it affects the flow rate and pressure of the drilling fluid being pumped through it. `,-1))]),_:1})]),i(d,{prop:"orificeSize"},{default:c(()=>[i(a,{type:"number",class:"w-100",controls:!1,step:"any",modelValue:s.targetData.orificeSize,"onUpdate:modelValue":e[2]||(e[2]=m=>s.targetData.orificeSize=m),placeholder:"",name:"orificeSize"},null,8,["modelValue"])]),_:1})])]),t("div",Da,[t("button",{type:"button",id:"kt_modal_add_customer_cancel",class:"btn text-gray-700 btn-sm me-3 btn-blue",onClick:e[3]||(e[3]=(...m)=>s.hide&&s.hide(...m)),disabled:s.loading}," Discard ",8,Ia),t("button",{"data-kt-indicator":s.loading?"on":null,class:"btn btn-sm btn-primary",type:"submit",disabled:s.loading},[s.loading?U("",!0):($(),V("span",Pa," Save ")),s.loading?($(),V("span",Ca,e[11]||(e[11]=[L(" Please wait... "),t("span",{class:"spinner-border spinner-border-sm align-middle ms-2"},null,-1)]))):U("",!0)],8,Va)])]),_:1},8,["onSubmit","model","rules"])])]),_:1},8,["modelValue"])}const Fa=Q(ba,[["render",Ta]]),xa=G({name:"nozzles",components:{SvgIcon:X,BottomTool:pe,NozzleModal:Fa,NoEntries:be},setup(){const s=y(null),e=Nt(),v=y(!1),b=y(!1),f=y([]),r=y(""),l=ee("dailyReport");ie(()=>{l!=null&&l.getDailyReportId()&&o()});const o=()=>{a(),d()},a=async()=>{const D={dailyReportId:l==null?void 0:l.getDailyReportId(),page:1,limit:200};v.value=!0,e.getNozzles({params:D,callback:{onSuccess:k=>{f.value=k==null?void 0:k.items},onFinish:k=>{v.value=!1}}})},d=async()=>{l!=null&&l.getDailyReportId()&&(b.value=!0,e.getNozzleTFA({dailyReportId:l==null?void 0:l.getDailyReportId(),callback:{onSuccess:D=>{r.value=D},onFinish:D=>{b.value=!1}}}))},n=()=>{var D;(D=s==null?void 0:s.value)==null||D.show()},u=D=>{var k,N;(k=s==null?void 0:s.value)==null||k.setId(D),(N=s==null?void 0:s.value)==null||N.show()},m=D=>{ne.deletionAlert({onConfirmed:()=>{p(D)}})},p=async D=>{v.value=!0,e.deleteNozzle({id:D,callback:{onSuccess:k=>{a()},onFinish:k=>{v.value=!1}}})};return{tfa:r,loading:v,loadingTFA:b,nozzleList:f,nozzleModal:s,loadPage:o,deleteNozzle:m,numberWithCommas:ge,toggleEditNozzleModal:u,toggleNewNozzleModal:n}}}),Na={class:"nozzles"},Aa={class:"bg-light-primary rounded border-primary border border-dashed p-7 gap-3"},Ha={class:"d-flex flex-wrap flex-md-nowrap gap-5"},qa={class:"flex-fill bg-white rounded border-blue-light border border-dashed p-7 d-flex flex-column gap-3"},Ma={key:0,class:"text-center"},ja={class:"mb-0 text-gray-800 fw-700"},za={class:"row gap-10 mt-5"},Ea={key:0,class:"text-center"},La={class:"card-body gap-5 d-flex flex-column"},Ra={class:"mb-0 text-primary"},Ba={class:"d-flex flex-column gap-3"},Oa={class:"d-flex flex-wrap align-items-center justify-content-between pb-3"},Ua={class:"fw-700 fs-7 text-dark"},Wa={class:"d-flex align-items-center gap-2 position-absolute top-0 end-0 transform-top-50"},Ga=["onClick"],Ya={class:"svg-icon svg-icon-3"},Ja=["onClick"],Qa={class:"svg-icon svg-icon-3 text-danger"};function Ka(s,e,v,b,f,r){const l=S("NoEntries"),o=S("SvgIcon"),a=S("BottomTool"),d=S("NozzleModal");return $(),V(B,null,[t("div",Na,[t("div",Aa,[e[3]||(e[3]=t("h4",{class:"text-gray-800 fw-700"},"Detail",-1)),t("div",Ha,[t("div",qa,[s.loadingTFA?($(),V("div",Ma,e[0]||(e[0]=[t("div",{class:"spinner-border text-primary",role:"status"},[t("span",{class:"sr-only"},"Loading...")],-1)]))):($(),V(B,{key:1},[e[1]||(e[1]=t("h4",{class:"mb-0 text-gray-800 fw-700"}," TFA (Total Flow Area) (in^2) ",-1)),e[2]||(e[2]=t("div",{class:"text-gray-400 fw-semibold fs-7"}," Calculated - Sum of areas of Nozzel section orifice size diameters = sum(pi*(d/2)^2) ",-1)),t("h3",ja,I(`${s.tfa||0} (in^2)`),1)],64))])])]),t("div",za,[s.loading?($(),V("div",Ea,e[4]||(e[4]=[t("div",{class:"spinner-border text-primary",role:"status"},[t("span",{class:"sr-only"},"Loading...")],-1)]))):($(),V(B,{key:1},[s.nozzleList.length===0?($(),W(l,{key:0,addNew:s.toggleNewNozzleModal},null,8,["addNew"])):($(!0),V(B,{key:1},Z(s.nozzleList,n=>($(),V("div",{key:n.id,class:"col-4 card rounded-3 shadow-sm border-top border-top-2 border-primary position-relative card-item"},[t("div",La,[t("h5",Ra,I(n.identificationNumber),1),t("div",Ba,[t("div",Oa,[e[5]||(e[5]=t("span",{class:"fw-semibold fs-7 text-gray-700"},"Orifice size",-1)),t("span",Ua,I(`${s.numberWithCommas(n.orificeSize)} (1/32in)`),1)])])]),t("div",Wa,[t("button",{class:"btn rounded-circle btn-icon btn-sm btn-action bg-light btn-edit",onClick:u=>s.toggleEditNozzleModal(n.id.toString())},[t("span",Ya,[i(o,{icon:"pencilIcon"})])],8,Ga),t("button",{class:"btn rounded-circle btn-icon btn-sm btn-action bg-light btn-delete",onClick:u=>s.deleteNozzle(n.id.toString())},[t("span",Qa,[i(o,{icon:"trashIcon"})])],8,Ja)])]))),128))],64))])]),i(a,{addNew:s.toggleNewNozzleModal},null,8,["addNew"]),i(d,{ref:"nozzleModal",loadPage:s.loadPage},null,8,["loadPage"])],64)}const At=Q(xa,[["render",Ka]]),Ht=ae("openHole",()=>({getOpenHoles:async({params:r,callback:l})=>{var d;const o=g.get(l,"onSuccess",g.noop),a=g.get(l,"onFinish",g.noop);try{P.setHeader();const n=await P.getWithParams("openHoles",r);o(((d=n.data)==null?void 0:d.data)||n.data)}catch(n){E(n,l)}finally{a()}},getOpenHoleDetails:async({id:r,callback:l})=>{var d;const o=g.get(l,"onSuccess",g.noop),a=g.get(l,"onFinish",g.noop);try{P.setHeader();const n=await P.get(`openHoles/${r}`);o(((d=n.data)==null?void 0:d.data)||n.data)}catch(n){E(n,l)}finally{a()}},updateOpenHole:async({id:r,params:l,callback:o})=>{var n;const a=g.get(o,"onSuccess",g.noop),d=g.get(o,"onFinish",g.noop);try{P.setHeader();const u=await P.put(`openHoles/${r}`,l);a(((n=u.data)==null?void 0:n.data)||u.data)}catch(u){E(u,o)}finally{d()}},deleteOpenHole:async({id:r,callback:l})=>{var d;const o=g.get(l,"onSuccess",g.noop),a=g.get(l,"onFinish",g.noop);try{P.setHeader();const n=await P.delete(`openHoles/${r}`);o(((d=n.data)==null?void 0:d.data)||n.data)}catch(n){E(n,l)}finally{a()}},createOpenHole:async({params:r,callback:l})=>{var d;const o=g.get(l,"onSuccess",g.noop),a=g.get(l,"onFinish",g.noop);try{P.setHeader();const n=await P.post("openHoles",r);o(((d=n.data)==null?void 0:d.data)||n.data)}catch(n){E(n,l)}finally{a()}}})),Xa=G({name:"open-hole-modal",components:{SvgIcon:X},props:{loadPage:{type:Function,default:()=>{},required:!0}},setup(s){const e=re(),v=Ht(),b=y(!1),f=y({description:"",measuredDepth:null,insideDiameter:null,washout:null}),r=y(null),l=y(!1),o=y(""),a=ee("dailyReport");J(o,w=>{w!==""&&d()}),J(b,w=>{var h;w===!1&&(o.value="",T(),(h=r==null?void 0:r.value)==null||h.resetFields())});const d=async()=>{v.getOpenHoleDetails({id:o.value,callback:{onSuccess:w=>{f.value={...w}}}})},n=async w=>{l.value=!0,v.updateOpenHole({id:o.value,params:w,callback:{onSuccess:h=>{s!=null&&s.loadPage&&(s==null||s.loadPage()),p()},onFinish:h=>{l.value=!1}}})},u=async w=>{l.value=!0,v.createOpenHole({params:w,callback:{onSuccess:h=>{s!=null&&s.loadPage&&(s==null||s.loadPage()),p()},onFinish:h=>{l.value=!1}}})},m=()=>{b.value=!0},p=()=>{b.value=!1},D=w=>{o.value=w.toString()},k=y({description:[{required:!0,message:"Please type Description",trigger:"blur"}]}),N=()=>{r.value&&r.value.validate(w=>{var h,_,H,C,F;if(w){const j={description:(h=f==null?void 0:f.value)==null?void 0:h.description,measuredDepth:Number((_=f==null?void 0:f.value)==null?void 0:_.measuredDepth),insideDiameter:Number((H=f==null?void 0:f.value)==null?void 0:H.insideDiameter),washout:Number((C=f==null?void 0:f.value)==null?void 0:C.washout)};o!=null&&o.value?n({...j,dailyReportId:a==null?void 0:a.getDailyReportId()}):a==null||a.createDailyReport({wellId:(F=e==null?void 0:e.params)==null?void 0:F.id,callback:{onSuccess:x=>{u({...j,dailyReportId:x})}}})}})},T=()=>{f.value={description:"",measuredDepth:null,insideDiameter:null,washout:null}};return{id:o,modal:b,rules:k,loading:l,targetData:f,formRef:r,show:m,hide:p,submit:N,setId:D,reset:T}}}),Za={class:"d-flex align-items-center w-100"},ei={class:"modal-title"},ti={class:"row g-7 mb-3"},si={class:"col-sm-6 fv-row"},li={class:"col-sm-6 fv-row"},oi={class:"d-flex align-items-center fs-6 fw-semibold mb-2"},ni={class:"row g-7 mb-3"},ai={class:"col-sm-6 fv-row"},ii={class:"d-flex align-items-center fs-6 fw-semibold mb-2"},di={class:"col-sm-6 fv-row"},ri={class:"d-flex align-items-center fs-6 fw-semibold mb-2"},ui={class:"modal-footer d-flex justify-content-center align-items-center"},ci=["disabled"],fi=["data-kt-indicator","disabled"],mi={key:0,class:"indicator-label"},pi={key:1,class:"indicator-progress"};function gi(s,e,v,b,f,r){const l=S("SvgIcon"),o=S("el-input"),a=S("el-form-item"),d=S("el-popover"),n=S("el-form"),u=S("el-dialog");return $(),W(u,{modelValue:s.modal,"onUpdate:modelValue":e[6]||(e[6]=m=>s.modal=m),"show-close":!1,width:"800","align-center":""},{header:c(()=>[t("div",Za,[t("h3",ei,I(`${s.id?"Edit Open Hole":"New Open Hole"}`),1),t("span",{class:"cursor-pointer ms-auto",onClick:e[0]||(e[0]=(...m)=>s.hide&&s.hide(...m))},[i(l,{icon:"closeModalIcon"})])])]),default:c(()=>[t("div",null,[i(n,{id:"open_hole_form",onSubmit:de(s.submit,["prevent"]),model:s.targetData,rules:s.rules,ref:"formRef",class:"form"},{default:c(()=>[t("div",ti,[t("div",si,[e[7]||(e[7]=t("label",{class:"d-flex align-items-center fs-6 fw-semibold mb-2 text-break required"},"Description ",-1)),i(a,{prop:"description",class:"mt-auto"},{default:c(()=>[i(o,{modelValue:s.targetData.description,"onUpdate:modelValue":e[1]||(e[1]=m=>s.targetData.description=m),placeholder:"",name:"description"},null,8,["modelValue"])]),_:1})]),t("div",li,[t("label",oi,[e[10]||(e[10]=L("ID (Inside Diameter) (inches)")),i(d,{placement:"top",width:300,trigger:"hover"},{reference:c(()=>e[8]||(e[8]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7"},null,-1)])),default:c(()=>[e[9]||(e[9]=t("span",null,"The Inside Diameter is the inner measurement of the open hole or wellbore, typically measured in inches or millimeters. ",-1))]),_:1})]),i(a,{prop:"insideDiameter"},{default:c(()=>[i(o,{type:"number",class:"w-100",controls:!1,step:"any",modelValue:s.targetData.insideDiameter,"onUpdate:modelValue":e[2]||(e[2]=m=>s.targetData.insideDiameter=m),placeholder:"",name:"insideDiameter"},null,8,["modelValue"])]),_:1})])]),t("div",ni,[t("div",ai,[t("label",ii,[e[13]||(e[13]=L("MD (Measured Depth) (ft)")),i(d,{placement:"top",width:300,trigger:"hover"},{reference:c(()=>e[11]||(e[11]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7"},null,-1)])),default:c(()=>[e[12]||(e[12]=t("span",null,"Measured Depth represents the length of the open hole from the surface to a specific depth. ",-1))]),_:1})]),i(a,{prop:"measuredDepth"},{default:c(()=>[i(o,{type:"number",class:"w-100",controls:!1,step:"any",modelValue:s.targetData.measuredDepth,"onUpdate:modelValue":e[3]||(e[3]=m=>s.targetData.measuredDepth=m),placeholder:"",name:"measuredDepth"},null,8,["modelValue"])]),_:1})]),t("div",di,[t("label",ri,[e[16]||(e[16]=L(" Washout (%)")),i(d,{placement:"top",width:300,trigger:"hover"},{reference:c(()=>e[14]||(e[14]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7"},null,-1)])),default:c(()=>[e[15]||(e[15]=t("span",null,"Washout refers to the enlargement of the open hole diameter due to erosion or other factors. It is expressed as a percentage and can vary at different depths. It is an input, and additional information may include the depth intervals with washout and the severity of washout (e.g., mild, moderate, severe). ",-1))]),_:1})]),i(a,{prop:"washout"},{default:c(()=>[i(o,{type:"number",class:"w-100",controls:!1,step:"any",modelValue:s.targetData.washout,"onUpdate:modelValue":e[4]||(e[4]=m=>s.targetData.washout=m),placeholder:"",name:"washout"},null,8,["modelValue"])]),_:1})])]),t("div",ui,[t("button",{type:"button",id:"kt_modal_add_customer_cancel",class:"btn text-gray-700 btn-sm me-3 btn-blue",onClick:e[5]||(e[5]=(...m)=>s.hide&&s.hide(...m)),disabled:s.loading}," Discard ",8,ci),t("button",{"data-kt-indicator":s.loading?"on":null,class:"btn btn-sm btn-primary",type:"submit",disabled:s.loading},[s.loading?U("",!0):($(),V("span",mi," Save ")),s.loading?($(),V("span",pi,e[17]||(e[17]=[L(" Please wait... "),t("span",{class:"spinner-border spinner-border-sm align-middle ms-2"},null,-1)]))):U("",!0)],8,fi)])]),_:1},8,["onSubmit","model","rules"])])]),_:1},8,["modelValue"])}const bi=Q(Xa,[["render",gi]]),vi=G({name:"open-hole",components:{SvgIcon:X,BottomTool:pe,OpenHoleModal:bi,NoEntries:be},setup(){const s=y(null),e=y(!1),v=Ht(),b=y([]),f=ee("dailyReport");ie(()=>{f!=null&&f.getDailyReportId()&&r()});const r=async(n={dailyReportId:f==null?void 0:f.getDailyReportId(),page:1,limit:200})=>{e.value=!0,v.getOpenHoles({params:n,callback:{onSuccess:u=>{b.value=u==null?void 0:u.items},onFinish:u=>{e.value=!1}}})},l=()=>{var n;(n=s==null?void 0:s.value)==null||n.show()},o=n=>{var u,m;(u=s==null?void 0:s.value)==null||u.setId(n),(m=s==null?void 0:s.value)==null||m.show()},a=n=>{ne.deletionAlert({onConfirmed:()=>{d(n)}})},d=async n=>{e.value=!0,v.deleteOpenHole({id:n,callback:{onSuccess:u=>{r()},onFinish:u=>{e.value=!1}}})};return{loading:e,openHoleList:b,openHoleModal:s,getOpenHoles:r,numberWithCommas:ge,deleteOpenHole:a,toggleEditOpenHoleModal:o,toggleNewOpenHoleModal:l}}}),hi={class:"open-hole"},yi={class:"row gap-10"},wi={key:0,class:"text-center"},$i={class:"card-body gap-5 d-flex flex-column"},Si={class:"mb-0 text-primary"},_i={class:"d-flex flex-column gap-3"},ki={class:"d-flex flex-wrap align-items-center justify-content-between border-bottom-dashed border-bottom-1 border-blue-light pb-3"},Di={class:"fw-700 fs-7 text-dark"},Ii={class:"d-flex"},Vi={class:"d-flex flex-wrap gap-3 align-items-center"},Pi={class:"border border-gray-300 border-dashed rounded py-3 px-4"},Ci={class:"fw-700 fs-6 text-gray-700"},Ti={class:"border border-gray-300 border-dashed rounded py-3 px-4"},Fi={class:"fw-700 fs-6 text-danger"},xi={class:"d-flex align-items-center gap-2 position-absolute top-0 end-0 transform-top-50"},Ni=["onClick"],Ai={class:"svg-icon svg-icon-3"},Hi=["onClick"],qi={class:"svg-icon svg-icon-3 text-danger"};function Mi(s,e,v,b,f,r){const l=S("NoEntries"),o=S("SvgIcon"),a=S("BottomTool"),d=S("OpenHoleModal");return $(),V(B,null,[t("div",hi,[t("div",yi,[s.loading?($(),V("div",wi,e[0]||(e[0]=[t("div",{class:"spinner-border text-primary",role:"status"},[t("span",{class:"sr-only"},"Loading...")],-1)]))):($(),V(B,{key:1},[s.openHoleList.length===0?($(),W(l,{key:0,addNew:s.toggleNewOpenHoleModal},null,8,["addNew"])):($(!0),V(B,{key:1},Z(s.openHoleList,n=>($(),V("div",{key:n.id,class:"col-4 card rounded-3 shadow-sm border-top border-top-2 border-primary position-relative card-item"},[t("div",$i,[t("h5",Si,I(n.description),1),t("div",_i,[t("div",ki,[e[1]||(e[1]=t("span",{class:"fw-semibold fs-7 text-gray-700"},"MD (Measured Depth) (ft)",-1)),t("span",Di,I(`${s.numberWithCommas(n.measuredDepth)} (ft)`),1)])]),t("div",Ii,[t("div",Vi,[t("div",Pi,[t("div",Ci,I(`${s.numberWithCommas(n.insideDiameter)} (in)`),1),e[2]||(e[2]=t("div",{class:"fw-semibold fs-6 text-primary"},"ID",-1))]),t("div",Ti,[t("div",Fi,I(`${s.numberWithCommas(n.washout)}%`),1),e[3]||(e[3]=t("div",{class:"fw-semibold fs-6 text-info"},"Washout",-1))])])])]),t("div",xi,[t("button",{class:"btn rounded-circle btn-icon btn-sm btn-action bg-light btn-edit",onClick:u=>s.toggleEditOpenHoleModal(n.id.toString())},[t("span",Ai,[i(o,{icon:"pencilIcon"})])],8,Ni),t("button",{class:"btn rounded-circle btn-icon btn-sm btn-action bg-light btn-delete",onClick:u=>s.deleteOpenHole(n.id.toString())},[t("span",qi,[i(o,{icon:"trashIcon"})])],8,Hi)])]))),128))],64))])]),i(a,{addNew:s.toggleNewOpenHoleModal},null,8,["addNew"]),i(d,{ref:"openHoleModal",loadPage:s.getOpenHoles},null,8,["loadPage"])],64)}const qt=Q(vi,[["render",Mi]]),ji=ae("wellInformation",()=>({updateWellInformation:async({id:r,params:l,callback:o})=>{var n;const a=g.get(o,"onSuccess",g.noop),d=g.get(o,"onFinish",g.noop);try{P.setHeader();const u=await P.put(`wellInformations/${r}`,l);a(((n=u.data)==null?void 0:n.data)||u.data)}catch(u){E(u,o)}finally{d()}},deleteWellInformation:async({id:r,callback:l})=>{var d;const o=g.get(l,"onSuccess",g.noop),a=g.get(l,"onFinish",g.noop);try{P.setHeader();const n=await P.delete(`wellInformations/${r}`);o(((d=n.data)==null?void 0:d.data)||n.data)}catch(n){E(n,l)}finally{a()}},getWellInformation:async({params:r,callback:l})=>{var d;const o=g.get(l,"onSuccess",g.noop),a=g.get(l,"onFinish",g.noop);try{P.setHeader();const n=await P.getWithParams("wellInformations",r);o(((d=n.data)==null?void 0:d.data)||n.data)}catch(n){E(n,l)}finally{a()}},getWellInfoToday:async({dailyReportId:r,callback:l})=>{var d;const o=g.get(l,"onSuccess",g.noop),a=g.get(l,"onFinish",g.noop);try{P.setHeader();const n=await P.get(`/wellInformations/wellInformationToday/${r}`);o(((d=n.data)==null?void 0:d.data)||n.data)}catch(n){E(n,l)}finally{a()}},createWellInformation:async({params:r,callback:l})=>{var d;const o=g.get(l,"onSuccess",g.noop),a=g.get(l,"onFinish",g.noop);try{P.setHeader();const n=await P.post("wellInformations",r);o(((d=n.data)==null?void 0:d.data)||n.data)}catch(n){E(n,l)}finally{a()}}})),zi=G({name:"well-information",components:{SvgIcon:X},setup(){var A,M,z;const s=re(),e=$s(),v=ji(),b=vt(),f=y(),r=y([]),l=y(!1),o=y(!1),a=y(),d=(A=s==null?void 0:s.params)==null?void 0:A.id,n=y(!0),u=ee("dailyReport"),m=y({dailyReportId:u==null?void 0:u.getDailyReportId(),reportedAt:ke(vs().toDate(),"YYYY-MM-DD HH:mm:ss"),engineerId:!(u!=null&&u.getDailyReportId())&&De.checkRole(et.Engineer)?(M=De.getUserInfo())==null?void 0:M.id:null,activity:"",measuredDepth:null,trueVerticalDepth:null,inclination:null,azimuth:null,weightOnBit:null,rotaryWeight:null,standOffWeight:null,pullUpWeight:null,revolutionsPerMinute:null,rateOfPenetration:null,drillingInterval:"",formation:"",depthDrilled:null,totalStringLength:null,totalLength:null,tfa:null}),p=y({...m.value}),D=y(null),k=y(),N=y({reportedAt:[{required:!0,message:"Please select Date",trigger:"change"}],engineerId:[{required:!0,message:"Please select Engineer",trigger:"change"}]});yt((q,R,Y)=>{Dt(q,R,Y,o.value||C())}),J(()=>p.value.measuredDepth,q=>{var R,Y,se;if(q&&!isNaN(q)){const oe=Number(((R=p.value)==null?void 0:R.measuredDepth)||0)-Number(((se=(Y=a==null?void 0:a.value)==null?void 0:Y.wellInformation)==null?void 0:se.measuredDepth)||0);!n.value||!(u!=null&&u.getDailyReportId())?(m.value={...m.value,depthDrilled:oe},p.value={...p.value,depthDrilled:oe}):n.value=!1}else m.value={...m.value,depthDrilled:null},p.value={...p.value,depthDrilled:null}}),J(()=>u==null?void 0:u.getDailyReportId(),q=>{q&&w()}),ie(()=>{h(),T(),u!=null&&u.getDailyReportId()&&w()});const T=async()=>{e.getUsers({params:{role:et.Engineer,page:1,limit:500},callback:{onSuccess:q=>{r.value=q==null?void 0:q.items.map(R=>({label:`${R==null?void 0:R.firstName} ${R==null?void 0:R.lastName}`,value:R.id}))}}})},w=async()=>{u!=null&&u.getDailyReportId()&&b.getDailyReportById({id:u==null?void 0:u.getDailyReportId(),callback:{onSuccess:q=>{var se,oe;const R=q==null?void 0:q.wellInformation;k.value=R;let Y;R?Y={...R,dailyReportId:u==null?void 0:u.getDailyReportId(),engineerId:(se=R==null?void 0:R.engineer)==null?void 0:se.id}:Y={...p.value,engineerId:De.checkRole(et.Engineer)?(oe=De.getUserInfo())==null?void 0:oe.id:null},m.value=JSON.parse(JSON.stringify(Y)),p.value=JSON.parse(JSON.stringify(Y))}}})},h=async()=>{d&&(l.value=!0,b.getWellInformationTab({wellId:d,callback:{onSuccess:q=>{var se,oe;const[R,Y]=q;f.value=(se=R==null?void 0:R.data)==null?void 0:se.data,a.value=(oe=Y==null?void 0:Y.data)==null?void 0:oe.data},onFinish:()=>{l.value=!1}}}))},_=async q=>{v.createWellInformation({params:q,callback:{onSuccess:R=>{h(),u!=null&&u.getDailyReportId()&&w()},onFinish:()=>{o.value=!1}}})},H=async q=>{var R;o.value=!0,v.updateWellInformation({id:(R=k==null?void 0:k.value)==null?void 0:R.id,params:q,callback:{onSuccess:Y=>{h(),u!=null&&u.getDailyReportId()&&w()},onFinish:()=>{o.value=!1}}})},C=()=>!wt.isEqual(p.value,m.value);return{submit:()=>{D.value&&D.value.validate(q=>{var R;if(q){if(o.value||!C())return;const Y={...p.value,measuredDepth:Number(p.value.measuredDepth),trueVerticalDepth:Number(p.value.trueVerticalDepth),inclination:Number(p.value.inclination),azimuth:Number(p.value.azimuth),weightOnBit:Number(p.value.weightOnBit),rotaryWeight:Number(p.value.rotaryWeight),standOffWeight:Number(p.value.standOffWeight),pullUpWeight:Number(p.value.pullUpWeight),revolutionsPerMinute:Number(p.value.revolutionsPerMinute),rateOfPenetration:Number(p.value.rateOfPenetration),depthDrilled:(R=p.value)!=null&&R.depthDrilled?Number(p.value.depthDrilled):null,totalStringLength:Number(p.value.totalStringLength),totalLength:Number(p.value.totalLength)};u!=null&&u.getDailyReportId()?k.value?H({...Y,dailyReportId:u==null?void 0:u.getDailyReportId()}):_({...Y,dailyReportId:u==null?void 0:u.getDailyReportId()}):(o.value=!0,u==null||u.createDailyReport({wellId:d,callback:{onSuccess:se=>{_({...Y,dailyReportId:se})},onFailure:()=>{o.value=!1}}}))}})},cancel:()=>{ne.incompleteFormAlert({onConfirmed:()=>{p.value=JSON.parse(JSON.stringify(m.value))}},"Cancel changes on this section?","Yes")},isFormDirty:C,isValidForm:async()=>{var R;return await((R=D==null?void 0:D.value)==null?void 0:R.validate(Y=>Y))},loading:l,submitting:o,wellInfo:f,rules:N,engineerList:r,formRef:D,targetData:p,isSystemAdmin:De.checkRole(et.SystemAdmin),dailyReportId:(z=s==null?void 0:s.params)==null?void 0:z.dailyReportId}}}),Ei={key:0,class:"text-center"},Li={key:1},Ri={class:"row g-9 mb-3"},Bi={class:"col-12 d-flex align-items-center justify-content-end gap-3"},Oi=["disabled"],Ui=["disabled"],Wi={key:0,class:"indicator-label"},Gi={key:1,class:"indicator-progress"},Yi={class:"row g-9 mb-3"},Ji={class:"col-md-3 col-lg-4 fv-row d-flex flex-column justify-content-stretch"},Qi={key:0,class:"col-md-3 col-lg-4 fv-row d-flex flex-column justify-content-stretch"},Ki={class:"col-md-3 col-lg-4 fv-row d-flex flex-column justify-content-stretch"},Xi={class:"col-md-3 col-lg-4 fv-row d-flex flex-column justify-content-stretch"},Zi={class:"row g-9 mb-3"},ed={class:"col-12 fv-row d-flex flex-column justify-content-stretch"},td={class:"row g-9 mb-3"},sd={class:"col-md-6 col-lg-3 fv-row d-flex flex-column justify-content-stretch"},ld={class:"d-flex align-items-center fs-6 fw-semibold mb-2 text-break"},od={class:"col-md-6 col-lg-3 fv-row d-flex flex-column justify-content-stretch"},nd={class:"d-flex align-items-center fs-6 fw-semibold mb-2 text-break"},ad={class:"col-md-6 col-lg-3 fv-row d-flex flex-column justify-content-stretch"},id={class:"d-flex align-items-center fs-6 fw-semibold mb-2 text-break"},dd={class:"col-md-6 col-lg-3 fv-row d-flex flex-column justify-content-stretch"},rd={class:"d-flex align-items-center fs-6 fw-semibold mb-2 text-break"},ud={class:"row g-9 mb-3"},cd={class:"col-md-6 col-lg-3 fv-row d-flex flex-column justify-content-stretch"},fd={class:"d-flex align-items-center fs-6 fw-semibold mb-2 text-break"},md={class:"col-md-6 col-lg-3 fv-row d-flex flex-column justify-content-stretch"},pd={class:"d-flex align-items-center fs-6 fw-semibold mb-2 text-break"},gd={class:"col-md-6 col-lg-3 fv-row d-flex flex-column justify-content-stretch"},bd={class:"d-flex align-items-center fs-6 fw-semibold mb-2 text-break"},vd={class:"col-md-6 col-lg-3 fv-row d-flex flex-column justify-content-stretch"},hd={class:"d-flex align-items-center fs-6 fw-semibold mb-2 text-break"},yd={class:"row g-9 mb-3"},wd={class:"col-md-6 col-lg-3 fv-row d-flex flex-column justify-content-stretch"},$d={class:"d-flex align-items-center fs-6 fw-semibold mb-2 text-break"},Sd={class:"col-md-6 col-lg-3 fv-row d-flex flex-column justify-content-stretch"},_d={class:"d-flex align-items-center fs-6 fw-semibold mb-2 text-break"},kd={class:"col-md-6 col-lg-3 fv-row d-flex flex-column justify-content-stretch"},Dd={class:"d-flex align-items-center fs-6 fw-semibold mb-2 text-break"},Id={class:"col-md-6 col-lg-3 fv-row d-flex flex-column justify-content-stretch"},Vd={class:"d-flex align-items-center fs-6 fw-semibold mb-2 text-break"},Pd={class:"row g-9 mb-3"},Cd={class:"col-md-6 col-lg-3 fv-row d-flex flex-column justify-content-stretch"},Td={class:"d-flex align-items-center fs-6 fw-semibold mb-2 text-break"},Fd={class:"row g-9"},xd={class:"col-md-6 col-lg-3 fv-row d-flex flex-column justify-content-stretch"},Nd={class:"d-flex align-items-center fs-6 fw-semibold mb-2 text-break"},Ad={class:"col-md-6 col-lg-3 fv-row d-flex flex-column justify-content-stretch"},Hd={class:"d-flex align-items-center fs-6 fw-semibold mb-2 text-break"},qd={class:"col-md-6 col-lg-3 fv-row d-flex flex-column justify-content-stretch"},Md={class:"d-flex align-items-center fs-6 fw-semibold mb-2 text-break"};function jd(s,e,v,b,f,r){const l=S("el-input"),o=S("el-form-item"),a=S("el-date-picker"),d=S("el-select-v2"),n=S("el-popover"),u=S("el-form");return $(),W(u,{onSubmit:de(s.submit,["prevent"]),model:s.targetData,rules:s.rules,ref:"formRef",class:"form new-report-form"},{default:c(()=>[s.loading?($(),V("div",Ei,e[20]||(e[20]=[t("div",{class:"spinner-border text-primary",role:"status"},[t("span",{class:"sr-only"},"Loading...")],-1)]))):($(),V("div",Li,[t("div",Ri,[t("div",Bi,[t("button",{type:"button",class:"btn text-gray-700 btn-sm btn-blue",onClick:e[0]||(e[0]=(...m)=>s.cancel&&s.cancel(...m)),disabled:s.isFormDirty()!==!0||s.submitting}," Cancel ",8,Oi),t("button",{class:"btn btn-success btn-sm fw-semibold",type:"submit",disabled:s.isFormDirty()!==!0||s.submitting},[s.submitting?U("",!0):($(),V("span",Wi," Save ")),s.submitting?($(),V("span",Gi,e[21]||(e[21]=[L(" Please wait... "),t("span",{class:"spinner-border spinner-border-sm align-middle ms-2"},null,-1)]))):U("",!0)],8,Ui)])]),t("div",Yi,[t("div",Ji,[e[22]||(e[22]=t("label",{class:"d-flex align-items-center fs-6 fw-semibold mb-2 text-break"},"Well name",-1)),i(o,{prop:"nameOrNo"},{default:c(()=>{var m;return[i(l,{value:(m=s.wellInfo)==null?void 0:m.nameOrNo,placeholder:"",name:"nameOrNo",disabled:""},null,8,["value"])]}),_:1})]),s.isSystemAdmin?($(),V("div",Qi,[e[23]||(e[23]=t("label",{class:"d-flex align-items-center fs-6 fw-semibold mb-2 text-break"},"Company",-1)),i(o,null,{default:c(()=>{var m,p;return[i(l,{value:(p=(m=s.wellInfo)==null?void 0:m.company)==null?void 0:p.name,disabled:""},null,8,["value"])]}),_:1})])):U("",!0),t("div",Ki,[e[24]||(e[24]=t("label",{class:"d-flex align-items-center fs-6 fw-semibold mb-2 text-break required"}," Date Time ",-1)),i(o,{prop:"reportedAt",class:"w-100"},{default:c(()=>[i(a,{class:"w-100 date-picker-customize",type:"datetime",modelValue:s.targetData.reportedAt,"onUpdate:modelValue":e[1]||(e[1]=m=>s.targetData.reportedAt=m),placeholder:"MM/DD/YYYY",format:"MM/DD/YYYY HH:mm",formatValue:"YYYY-MM-DD HH:mm:ss",name:"reportedAt",disabled:!!s.dailyReportId},null,8,["modelValue","disabled"])]),_:1})]),t("div",Xi,[e[25]||(e[25]=t("label",{class:"d-flex align-items-center fs-6 fw-semibold mb-2 text-break required"}," Engineers ",-1)),i(o,{prop:"engineerId"},{default:c(()=>[i(d,{class:"w-100",modelValue:s.targetData.engineerId,"onUpdate:modelValue":e[2]||(e[2]=m=>s.targetData.engineerId=m),options:s.engineerList,placeholder:"Search Engineer",filterable:"",clearable:"",name:"engineerId"},null,8,["modelValue","options"])]),_:1})])]),t("div",Zi,[t("div",ed,[e[26]||(e[26]=t("label",{class:"d-flex align-items-center fs-6 fw-semibold mb-2 text-break"},"Activity",-1)),i(o,{prop:"activity"},{default:c(()=>[i(l,{modelValue:s.targetData.activity,"onUpdate:modelValue":e[3]||(e[3]=m=>s.targetData.activity=m),type:"textarea",rows:"2",name:"activity",placeholder:""},null,8,["modelValue"])]),_:1})])]),t("div",td,[t("div",sd,[t("label",ld,[e[29]||(e[29]=t("span",null,"MD (Measured Depth) (ft)",-1)),i(n,{placement:"top",width:400,trigger:"hover"},{reference:c(()=>e[27]||(e[27]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7"},null,-1)])),default:c(()=>[e[28]||(e[28]=t("span",null," Measured Depth represents the length of the wellbore from the surface to the current drilling depth. It is an input and doesn't require a calculation. ",-1))]),_:1})]),i(o,{prop:"measuredDepth",class:"mt-auto"},{default:c(()=>[i(l,{type:"number",class:"w-100",controls:!1,step:"any",modelValue:s.targetData.measuredDepth,"onUpdate:modelValue":e[4]||(e[4]=m=>s.targetData.measuredDepth=m),placeholder:"",name:"measuredDepth"},null,8,["modelValue"])]),_:1})]),t("div",od,[t("label",nd,[e[32]||(e[32]=t("span",null,"TVD (True Vertical Depth) (ft)",-1)),i(n,{placement:"top",width:400,trigger:"hover"},{reference:c(()=>e[30]||(e[30]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7"},null,-1)])),default:c(()=>[e[31]||(e[31]=t("span",null," True Vertical Depth represents the vertical distance from the wellbore to the target or the depth below the surface at which the wellbore reaches the target zone. TVD may require calculation based on the well's inclination and azimuth. ",-1))]),_:1})]),i(o,{prop:"trueVerticalDepth",class:"mt-auto"},{default:c(()=>[i(l,{type:"number",class:"w-100",controls:!1,step:"any",modelValue:s.targetData.trueVerticalDepth,"onUpdate:modelValue":e[5]||(e[5]=m=>s.targetData.trueVerticalDepth=m),placeholder:"",name:"trueVerticalDepth"},null,8,["modelValue"])]),_:1})]),t("div",ad,[t("label",id,[e[35]||(e[35]=t("span",null,"Inc. (Inclination) (deg)",-1)),i(n,{placement:"top",width:400,trigger:"hover"},{reference:c(()=>e[33]||(e[33]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7"},null,-1)])),default:c(()=>[e[34]||(e[34]=t("span",null," Inclination is the angle at which the wellbore deviates from vertical. It is an input and doesn't require a calculation. ",-1))]),_:1})]),i(o,{prop:"inclination",class:"mt-auto"},{default:c(()=>[i(l,{type:"number",class:"w-100",controls:!1,step:"any",modelValue:s.targetData.inclination,"onUpdate:modelValue":e[6]||(e[6]=m=>s.targetData.inclination=m),placeholder:"",name:"inclination"},null,8,["modelValue"])]),_:1})]),t("div",dd,[t("label",rd,[e[38]||(e[38]=t("span",null,"Azi (Azimuth) (deg)",-1)),i(n,{placement:"top",width:400,trigger:"hover"},{reference:c(()=>e[36]||(e[36]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7"},null,-1)])),default:c(()=>[e[37]||(e[37]=t("span",null," Azimuth is the compass direction in which the wellbore is drilled horizontally. It is an input and doesn't require a calculation. ",-1))]),_:1})]),i(o,{prop:"azimuth",class:"mt-auto"},{default:c(()=>[i(l,{type:"number",class:"w-100",controls:!1,step:"any",modelValue:s.targetData.azimuth,"onUpdate:modelValue":e[7]||(e[7]=m=>s.targetData.azimuth=m),placeholder:"",name:"azimuth"},null,8,["modelValue"])]),_:1})])]),t("div",ud,[t("div",cd,[t("label",fd,[e[41]||(e[41]=t("span",null,"WOB (Weight on Bit) (lbf)",-1)),i(n,{placement:"top",width:400,trigger:"hover"},{reference:c(()=>e[39]||(e[39]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7"},null,-1)])),default:c(()=>[e[40]||(e[40]=t("span",null," Weight on Bit is the downward force applied to the drill bit to aid in drilling. It is an input and doesn't require a calculation. ",-1))]),_:1})]),i(o,{prop:"weightOnBit",class:"mt-auto"},{default:c(()=>[i(l,{type:"number",class:"w-100",controls:!1,step:"any",modelValue:s.targetData.weightOnBit,"onUpdate:modelValue":e[8]||(e[8]=m=>s.targetData.weightOnBit=m),placeholder:"",name:"weightOnBit"},null,8,["modelValue"])]),_:1})]),t("div",md,[t("label",pd,[e[44]||(e[44]=t("span",null,"Rot. Wt. (Rotary Weight) (lbf)",-1)),i(n,{placement:"top",width:400,trigger:"hover"},{reference:c(()=>e[42]||(e[42]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7"},null,-1)])),default:c(()=>[e[43]||(e[43]=t("span",null," Rotary Weight represents the weight applied to the drill bit due to the rotation of the drill string. It is an input and doesn't require a calculation. ",-1))]),_:1})]),i(o,{prop:"rotaryWeight",class:"mt-auto"},{default:c(()=>[i(l,{type:"number",class:"w-100",controls:!1,step:"any",modelValue:s.targetData.rotaryWeight,"onUpdate:modelValue":e[9]||(e[9]=m=>s.targetData.rotaryWeight=m),placeholder:"",name:"rotaryWeight"},null,8,["modelValue"])]),_:1})]),t("div",gd,[t("label",bd,[e[47]||(e[47]=t("span",null,"S/O Wt. (Standoff Weight) (lbf)",-1)),i(n,{placement:"top",width:400,trigger:"hover"},{reference:c(()=>e[45]||(e[45]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7"},null,-1)])),default:c(()=>[e[46]||(e[46]=t("span",null," Standoff Weight is the weight applied to the drill bit due to the hydraulic pressure exerted by the drilling mud. It is an input and doesn't require a calculation. ",-1))]),_:1})]),i(o,{prop:"standOffWeight",class:"mt-auto"},{default:c(()=>[i(l,{type:"number",class:"w-100",controls:!1,step:"any",modelValue:s.targetData.standOffWeight,"onUpdate:modelValue":e[10]||(e[10]=m=>s.targetData.standOffWeight=m),placeholder:"",name:"standOffWeight"},null,8,["modelValue"])]),_:1})]),t("div",vd,[t("label",hd,[e[50]||(e[50]=t("span",null,"P/U Wt. (Pull-Up Weight) (lbf)",-1)),i(n,{placement:"top",width:400,trigger:"hover"},{reference:c(()=>e[48]||(e[48]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7"},null,-1)])),default:c(()=>[e[49]||(e[49]=t("span",null," Pull-Up Weight is the weight applied to the drill bit during the tripping process when the drill string is pulled out of the hole. It is an input and doesn't require a calculation. ",-1))]),_:1})]),i(o,{prop:"pullUpWeight",class:"mt-auto"},{default:c(()=>[i(l,{type:"number",class:"w-100",controls:!1,step:"any",modelValue:s.targetData.pullUpWeight,"onUpdate:modelValue":e[11]||(e[11]=m=>s.targetData.pullUpWeight=m),placeholder:"",name:"pullUpWeight"},null,8,["modelValue"])]),_:1})])]),t("div",yd,[t("div",wd,[t("label",$d,[e[53]||(e[53]=t("span",null,"RPM (Revolutions Per Minute) (rpm)",-1)),i(n,{placement:"top",width:400,trigger:"hover"},{reference:c(()=>e[51]||(e[51]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7"},null,-1)])),default:c(()=>[e[52]||(e[52]=t("span",null," RPM represents the rotational speed of the drill bit. It is an input and doesn't require a calculation. ",-1))]),_:1})]),i(o,{prop:"revolutionsPerMinute",class:"mt-auto"},{default:c(()=>[i(l,{type:"number",class:"w-100",controls:!1,step:"any",modelValue:s.targetData.revolutionsPerMinute,"onUpdate:modelValue":e[12]||(e[12]=m=>s.targetData.revolutionsPerMinute=m),placeholder:"",name:"revolutionsPerMinute"},null,8,["modelValue"])]),_:1})]),t("div",Sd,[t("label",_d,[e[56]||(e[56]=t("span",null,"ROP (Rate of Penetration) (ft/hr)",-1)),i(n,{placement:"top",width:400,trigger:"hover"},{reference:c(()=>e[54]||(e[54]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7"},null,-1)])),default:c(()=>[e[55]||(e[55]=t("span",null," Rate of Penetration is the speed at which the drill bit advances in feet per hour. It is a calculation based on the depth drilled (ft) and the drilling interval (time). ",-1))]),_:1})]),i(o,{prop:"rateOfPenetration",class:"mt-auto"},{default:c(()=>[i(l,{type:"number",class:"w-100",controls:!1,step:"any",modelValue:s.targetData.rateOfPenetration,"onUpdate:modelValue":e[13]||(e[13]=m=>s.targetData.rateOfPenetration=m),placeholder:"",name:"rateOfPenetration"},null,8,["modelValue"])]),_:1})]),t("div",kd,[t("label",Dd,[e[59]||(e[59]=t("span",null,"Drilling Interval",-1)),i(n,{placement:"top",width:400,trigger:"hover"},{reference:c(()=>e[57]||(e[57]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7"},null,-1)])),default:c(()=>[e[58]||(e[58]=t("span",null," The drilling interval is the time it takes to drill a certain depth. It is an input and can be used in the ROP calculation. ",-1))]),_:1})]),i(o,{prop:"drillingInterval",class:"mt-auto"},{default:c(()=>[i(l,{class:"w-100",modelValue:s.targetData.drillingInterval,"onUpdate:modelValue":e[14]||(e[14]=m=>s.targetData.drillingInterval=m),placeholder:"",name:"drillingInterval"},null,8,["modelValue"])]),_:1})]),t("div",Id,[t("label",Vd,[e[62]||(e[62]=t("span",null,"Formation",-1)),i(n,{placement:"top",width:400,trigger:"hover"},{reference:c(()=>e[60]||(e[60]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7"},null,-1)])),default:c(()=>[e[61]||(e[61]=t("span",null," Formation refers to the geological strata or rock types encountered while drilling. It is an input and doesn't require a calculation. ",-1))]),_:1})]),i(o,{prop:"formation",class:"mt-auto"},{default:c(()=>[i(l,{class:"w-100",modelValue:s.targetData.formation,"onUpdate:modelValue":e[15]||(e[15]=m=>s.targetData.formation=m),placeholder:"",name:"formation"},null,8,["modelValue"])]),_:1})])]),t("div",Pd,[t("div",Cd,[t("label",Td,[e[65]||(e[65]=t("span",null,"Depth Drilled (ft)",-1)),i(n,{placement:"top",width:400,trigger:"hover"},{reference:c(()=>e[63]||(e[63]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7"},null,-1)])),default:c(()=>[e[64]||(e[64]=t("span",null," Depth Drilled represents the amount of wellbore drilled during a specific drilling operation. It is an input and can be used in the ROP calculation. ",-1))]),_:1})]),i(o,{prop:"depthDrilled",class:"mt-auto"},{default:c(()=>[i(l,{type:"number",class:"w-100",controls:!1,step:"any",modelValue:s.targetData.depthDrilled,"onUpdate:modelValue":e[16]||(e[16]=m=>s.targetData.depthDrilled=m),placeholder:"",name:"depthDrilled",disabled:""},null,8,["modelValue"])]),_:1})])]),t("div",Fd,[t("div",xd,[t("label",Nd,[e[68]||(e[68]=t("span",null,"Total String Length = MD",-1)),i(n,{placement:"top",width:400,trigger:"hover"},{reference:c(()=>e[66]||(e[66]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7"},null,-1)])),default:c(()=>[e[67]||(e[67]=t("span",null," Total String Length is the overall length of the drill string, which is typically equal to the Measured Depth (MD). It is an input and doesn't require a separate calculation. ",-1))]),_:1})]),i(o,{prop:"totalStringLength",class:"mt-auto"},{default:c(()=>[i(l,{type:"number",class:"w-100",controls:!1,step:"any",modelValue:s.targetData.totalStringLength,"onUpdate:modelValue":e[17]||(e[17]=m=>s.targetData.totalStringLength=m),placeholder:"",name:"totalStringLength"},null,8,["modelValue"])]),_:1})]),t("div",Ad,[t("label",Hd,[e[71]||(e[71]=t("span",null,"Total Length = MD",-1)),i(n,{placement:"top",width:400,trigger:"hover"},{reference:c(()=>e[69]||(e[69]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7"},null,-1)])),default:c(()=>[e[70]||(e[70]=t("span",null," Total Length can refer to the overall length of the wellbore from the surface to the target zone. It may require a calculation based on MD and TVD: Total Length = √(MD^2 + TVD^2) ",-1))]),_:1})]),i(o,{prop:"totalLength",class:"mt-auto"},{default:c(()=>[i(l,{type:"number",class:"w-100",controls:!1,step:"any",modelValue:s.targetData.totalLength,"onUpdate:modelValue":e[18]||(e[18]=m=>s.targetData.totalLength=m),placeholder:"",name:"totalLength"},null,8,["modelValue"])]),_:1})]),t("div",qd,[t("label",Md,[e[74]||(e[74]=t("span",null,"TFA (Total Flow Area) (in^2)",-1)),i(n,{placement:"top",width:400,trigger:"hover"},{reference:c(()=>e[72]||(e[72]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7"},null,-1)])),default:c(()=>[e[73]||(e[73]=t("span",null,[L(' "Total Flow Area" measured in square inches (in^2). Total Flow Area in this context typically pertains to the collective cross-sectional area available for the passage of drilling mud or fluids through the components of the drill string, such as drill pipes, drill bits, and nozzles.'),t("br"),t("br"),L(" Total Flow Area is an essential parameter to consider when designing and optimizing the drill string and associated equipment. It affects the flow rate, fluid velocity, and pressure drop within the drill string. Properly managing TFA is important for maintaining effective drilling fluid circulation, ensuring efficient cooling of the drill bit, and carrying drilled cuttings and formation samples to the surface for mud logging and analysis. Adjusting TFA, for example by selecting appropriate nozzles or pipe sizes, can help optimize drilling performance and hole cleaning. ")],-1))]),_:1})]),i(o,{prop:"tfa",class:"mt-auto"},{default:c(()=>[i(l,{type:"number",class:"w-100",controls:!1,step:"any",modelValue:s.targetData.tfa,"onUpdate:modelValue":e[19]||(e[19]=m=>s.targetData.tfa=m),placeholder:"",name:"tfa",disabled:""},null,8,["modelValue"])]),_:1})])])]))]),_:1},8,["onSubmit","model","rules"])}const Mt=Q(zi,[["render",jd],["__scopeId","data-v-1c3b790e"]]),zd={[fe.WellInformation]:Mt,[fe.CasedHole]:Tt,[fe.OpenHold]:qt,[fe.DrillString]:xt,[fe.Bits]:Pt,[fe.Nozzles]:At},Ed=G({name:"daily-report-general",components:{WellInformation:Mt,CasedHole:Tt,OpenHold:qt,DrillString:xt,Bits:Pt,Nozzles:At},props:{setChildActiveTab:{type:Function,required:!1,default:()=>{}}},setup(s){const e=y(fe.WellInformation),v=y(null),b=ot(()=>zd[e.value]),f=o=>{const a=o.target;e.value=Number(a.getAttribute("data-tab-index")),s!=null&&s.setChildActiveTab&&s.setChildActiveTab(e.value)},r=async o=>{e.value!==fe.WellInformation?f(o):l()?ne.incompleteFormAlert({onConfirmed:()=>{f(o)}},"You have unsaved changes. Are you sure you want to leave?"):f(o)},l=()=>{var o,a;return v!=null&&v.value&&((o=v==null?void 0:v.value)!=null&&o.isFormDirty)?(a=v==null?void 0:v.value)==null?void 0:a.isFormDirty():!1};return{handleActiveTab:r,isFormOfChildTabDirty:l,currentChildTab:v,EGeneralTab:fe,generalTabs:ps,tabIndex:e,currentComponent:b}}}),Ld={class:"card h-100 my-8"},Rd={class:"card-header"},Bd={class:"d-flex align-items-stretch gap-2 gap-lg-3 xxl:mt-0 me-5"},Od={class:"nav nav-stretch nav-line-tabs border-0 daily-nav",role:"tablist",id:"kt_layout_builder_tabs",ref:"kt_layout_builder_tabs"},Ud=["data-tab-index"],Wd={class:"card-body"};function Gd(s,e,v,b,f,r){return $(),V("div",Ld,[t("div",Rd,[t("div",Bd,[t("ul",Od,[($(!0),V(B,null,Z(s.generalTabs,l=>($(),V("li",{class:"nav-item",key:l.value},[t("div",{class:_e(["nav-link py-4 cursor-pointer text-active-primary fw-semibold text-hover-primary fs-6",{active:s.tabIndex===l.value}]),onClick:e[0]||(e[0]=o=>s.handleActiveTab(o)),"data-tab-index":l.value,role:"tab"},I(l.label),11,Ud)]))),128))],512)])]),t("div",Wd,[($(),W(rt(s.currentComponent),{ref:"currentChildTab"},null,512))])])}const jt=Q(Ed,[["render",Gd],["__scopeId","data-v-32972bbd"]]),zt=ae("targetProperty",()=>({getTargetPropertyDetails:async({wellId:v,callback:b})=>{var l;const f=g.get(b,"onSuccess",g.noop),r=g.get(b,"onFinish",g.noop);try{P.setHeader();const o=await P.get(`targetProperties/${v}`);f(((l=o.data)==null?void 0:l.data)||o.data)}catch(o){E(o,b)}finally{r()}},updateTargetProperty:async({id:v,params:b,callback:f})=>{var o;const r=g.get(f,"onSuccess",g.noop),l=g.get(f,"onFinish",g.noop);try{P.setHeader();const a=await P.put(`targetProperties/${v}`,b);r(((o=a.data)==null?void 0:o.data)||a.data)}catch(a){E(a,f)}finally{l()}}})),Yd=G({name:"edit-targeted-properties-modal",components:{SvgIcon:X},props:{loadPage:{type:Function,default:()=>{},required:!0},wellId:{type:String,required:!0}},setup(s){const e=zt(),v=y(!1),b={fluidType:null,mudWeight:null,funnelViscosity:null,plasticViscosity:null,yieldPoint:null,apiFiltrate:null,apiCakeThickness:null,pH:null,mudAlkalinity:null,filtrateAlkalinity:null,chlorides:null,totalHardness:null,linearGelStrengthPercent:null,rpm:null},f=y(JSON.parse(JSON.stringify(b))),r=y(null),l=y(!1),o=y("");J(v,N=>{var T;N==!0?a():(k(),(T=r==null?void 0:r.value)==null||T.resetFields())});const a=async()=>{l.value=!0,e.getTargetPropertyDetails({wellId:s==null?void 0:s.wellId,callback:{onSuccess:N=>{f.value=JSON.parse(JSON.stringify(N))},onFinish:N=>{l.value=!1}}})},d=async N=>{var T;l.value=!0,e.updateTargetProperty({id:((T=f==null?void 0:f.value)==null?void 0:T.id)||"",params:N,callback:{onSuccess:w=>{s!=null&&s.loadPage&&(s==null||s.loadPage()),m()},onFinish:w=>{l.value=!1}}})},n=N=>{o.value=N.toString()},u=()=>{v.value=!0},m=()=>{v.value=!1},p=y({fluidType:[{required:!0,message:"Please select Fluid Type",trigger:"change"}]}),D=()=>{r.value&&r.value.validate(N=>{var T,w,h,_,H,C,F,j,x,A,M,z,q,R;if(N){const Y={fluidType:Number((T=f==null?void 0:f.value)==null?void 0:T.fluidType),mudWeight:Number((w=f==null?void 0:f.value)==null?void 0:w.mudWeight),funnelViscosity:Number((h=f==null?void 0:f.value)==null?void 0:h.funnelViscosity),plasticViscosity:Number((_=f==null?void 0:f.value)==null?void 0:_.plasticViscosity),yieldPoint:Number((H=f==null?void 0:f.value)==null?void 0:H.yieldPoint),apiFiltrate:Number((C=f==null?void 0:f.value)==null?void 0:C.apiFiltrate),apiCakeThickness:Number((F=f==null?void 0:f.value)==null?void 0:F.apiCakeThickness),pH:Number((j=f==null?void 0:f.value)==null?void 0:j.pH),mudAlkalinity:Number((x=f==null?void 0:f.value)==null?void 0:x.mudAlkalinity),filtrateAlkalinity:Number((A=f==null?void 0:f.value)==null?void 0:A.filtrateAlkalinity),chlorides:Number((M=f==null?void 0:f.value)==null?void 0:M.chlorides),totalHardness:Number((z=f==null?void 0:f.value)==null?void 0:z.totalHardness),linearGelStrengthPercent:Number((q=f==null?void 0:f.value)==null?void 0:q.linearGelStrengthPercent),rpm:Number((R=f==null?void 0:f.value)==null?void 0:R.rpm)};d(Y)}})},k=()=>{f.value=JSON.parse(JSON.stringify(b))};return{modal:v,rules:p,loading:l,targetData:f,fluidTypeOptions:lt,sampleFromOptions:st,formRef:r,setId:n,show:u,hide:m,submit:D,reset:k}}}),Jd={class:"d-flex align-items-center w-100"},Qd={class:"row g-7 mb-3"},Kd={class:"col-sm-6 fv-row d-flex flex-column justify-content-stretch"},Xd={class:"d-flex align-items-center fs-6 fw-semibold mb-2 text-break"},Zd={class:"col-sm-6 fv-row d-flex flex-column justify-content-stretch"},er={class:"d-flex align-items-center fs-6 fw-semibold mb-2 text-break"},tr={class:"row g-7 mb-3"},sr={class:"col-sm-6 fv-row d-flex flex-column justify-content-stretch"},lr={class:"d-flex align-items-center fs-6 fw-semibold mb-2 text-break"},or={class:"col-sm-6 fv-row d-flex flex-column justify-content-stretch"},nr={class:"d-flex align-items-center fs-6 fw-semibold mb-2 text-break"},ar={class:"row g-7 mb-3"},ir={class:"col-sm-6 fv-row d-flex flex-column justify-content-stretch"},dr={class:"d-flex align-items-center fs-6 fw-semibold mb-2 text-break"},rr={class:"col-sm-6 fv-row d-flex flex-column justify-content-stretch"},ur={class:"row g-7 mb-3"},cr={class:"col-sm-6 fv-row d-flex flex-column justify-content-stretch"},fr={class:"d-flex align-items-center fs-6 fw-semibold mb-2 text-break"},mr={class:"col-sm-6 fv-row d-flex flex-column justify-content-stretch"},pr={class:"d-flex align-items-center fs-6 fw-semibold mb-2 text-break"},gr={class:"row g-7 mb-3"},br={class:"col-sm-6 fv-row d-flex flex-column justify-content-stretch"},vr={class:"d-flex align-items-center fs-6 fw-semibold mb-2 text-break"},hr={class:"col-sm-6 fv-row d-flex flex-column justify-content-stretch"},yr={class:"d-flex align-items-center fs-6 fw-semibold mb-2 text-break"},wr={class:"row g-7 mb-3"},$r={class:"col-sm-6 fv-row d-flex flex-column justify-content-stretch"},Sr={class:"d-flex align-items-center fs-6 fw-semibold mb-2 text-break"},_r={class:"col-sm-6 fv-row d-flex flex-column justify-content-stretch"},kr={class:"d-flex align-items-center fs-6 fw-semibold mb-2 text-break"},Dr={class:"row g-7 mb-3"},Ir={class:"col-sm-6 fv-row d-flex flex-column justify-content-stretch"},Vr={class:"d-flex align-items-center fs-6 fw-semibold mb-2 text-break"},Pr={class:"col-sm-6 fv-row d-flex flex-column justify-content-stretch"},Cr={class:"d-flex align-items-center fs-6 fw-semibold mb-2 text-break"},Tr={class:"row g-7"},Fr={class:"col-sm-6 fv-row d-flex flex-column justify-content-stretch"},xr={class:"d-flex align-items-center fs-6 fw-semibold mb-2 text-break"},Nr={class:"modal-footer d-flex justify-content-center align-items-center"},Ar=["disabled"],Hr=["data-kt-indicator","disabled"],qr={key:0,class:"indicator-label"},Mr={key:1,class:"indicator-progress"};function jr(s,e,v,b,f,r){const l=S("SvgIcon"),o=S("el-popover"),a=S("el-option"),d=S("el-select"),n=S("el-form-item"),u=S("el-input"),m=S("el-form"),p=S("el-dialog");return $(),W(p,{modelValue:s.modal,"onUpdate:modelValue":e[17]||(e[17]=D=>s.modal=D),"show-close":!1,width:"800","align-center":""},{header:c(()=>[t("div",Jd,[e[18]||(e[18]=t("h3",{class:"modal-title"},"Edit Targeted Properties",-1)),t("span",{class:"cursor-pointer ms-auto",onClick:e[0]||(e[0]=(...D)=>s.hide&&s.hide(...D))},[i(l,{icon:"closeModalIcon"})])])]),default:c(()=>[t("div",null,[i(m,{id:"edit_targeted_properties_form",onSubmit:de(s.submit,["prevent"]),model:s.targetData,rules:s.rules,ref:"formRef",class:"form"},{default:c(()=>[t("div",Qd,[t("div",Kd,[t("label",Xd,[e[21]||(e[21]=t("span",{class:"required"},"Fluid Type",-1)),i(o,{placement:"top",width:400,trigger:"hover"},{reference:c(()=>e[19]||(e[19]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7"},null,-1)])),default:c(()=>[e[20]||(e[20]=t("span",null,[L(" Water-based drilling muds are the most common type. They use water as the base fluid and may contain various additives. The key differences for WBM include water quality, filtration properties, and clay content."),t("br"),t("br"),L(" Oil-based drilling muds use oil as the base fluid. Key considerations for OBM include oil composition, oil/water ratio, and rheological properties."),t("br"),t("br"),L(" Synthetic-based drilling muds use synthetic fluids as the base, such as esters or olefins. Specific data requirements depend on the type of synthetic fluid used. ")],-1))]),_:1})]),i(n,{prop:"fluidType",class:"mt-auto"},{default:c(()=>[i(d,{modelValue:s.targetData.fluidType,"onUpdate:modelValue":e[1]||(e[1]=D=>s.targetData.fluidType=D),placeholder:"",class:"w-100",clearable:""},{default:c(()=>[($(!0),V(B,null,Z(s.fluidTypeOptions,D=>($(),W(a,{key:D.value,label:D.label,value:D.value,name:"fluidType"},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),t("div",Zd,[t("label",er,[e[24]||(e[24]=t("span",{class:"fs-6 fw-semibold text-break text-gray-800 required"},"MW (ppg or lbs/gal)",-1)),i(o,{placement:"bottom",width:400,trigger:"hover"},{reference:c(()=>e[22]||(e[22]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7 me-4"},null,-1)])),default:c(()=>[e[23]||(e[23]=t("span",null," Mud Weight, also known as mud density, is the density of the drilling mud, typically measured in pounds per gallon (ppg). For different types of drilling fluids, the density requirements can vary. ",-1))]),_:1})]),i(n,{prop:"mudWeight",class:"mt-auto"},{default:c(()=>[i(u,{type:"number",class:"w-100",controls:!1,step:"any",modelValue:s.targetData.mudWeight,"onUpdate:modelValue":e[2]||(e[2]=D=>s.targetData.mudWeight=D),placeholder:"",name:"mudWeight"},null,8,["modelValue"])]),_:1})])]),t("div",tr,[t("div",sr,[t("label",lr,[e[27]||(e[27]=t("span",null,"Funnel Viscosity (sec/qt) ",-1)),i(o,{placement:"top",width:400,trigger:"hover"},{reference:c(()=>e[25]||(e[25]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7"},null,-1)])),default:c(()=>[e[26]||(e[26]=t("span",null," The funnel viscosity measures the thickness or viscosity of the drilling mud. It is typically measured in seconds per quart (sec/qt). ",-1))]),_:1})]),i(n,{prop:"funnelViscosity",class:"mt-auto"},{default:c(()=>[i(u,{type:"number",class:"w-100",controls:!1,step:"any",modelValue:s.targetData.funnelViscosity,"onUpdate:modelValue":e[3]||(e[3]=D=>s.targetData.funnelViscosity=D),placeholder:"",name:"funnelViscosity"},null,8,["modelValue"])]),_:1})]),t("div",or,[t("label",nr,[e[30]||(e[30]=t("span",null,"PV (Plastic Viscosity) (cP)",-1)),i(o,{placement:"top",width:400,trigger:"hover"},{reference:c(()=>e[28]||(e[28]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7"},null,-1)])),default:c(()=>[e[29]||(e[29]=t("span",null," Plastic Viscosity is a measure of the resistance to flow of the drilling mud. It is typically measured in centipoise (cP). ",-1))]),_:1})]),i(n,{prop:"plasticViscosity",class:"mt-auto"},{default:c(()=>[i(u,{type:"number",class:"w-100",controls:!1,step:"any",modelValue:s.targetData.plasticViscosity,"onUpdate:modelValue":e[4]||(e[4]=D=>s.targetData.plasticViscosity=D),placeholder:"",name:"plasticViscosity"},null,8,["modelValue"])]),_:1})])]),t("div",ar,[t("div",ir,[t("label",dr,[e[33]||(e[33]=t("span",{class:"fs-6 fw-semibold text-break text-gray-800"},"Yield Point (YP) (lbf/100ft2)",-1)),i(o,{placement:"top",width:400,trigger:"hover"},{reference:c(()=>e[31]||(e[31]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7 me-4"},null,-1)])),default:c(()=>[e[32]||(e[32]=t("span",null," Yield Point is the amount of force required to initiate mud flow. It's typically measured in pounds per 100 square feet (lbf/100ft^2). ",-1))]),_:1})]),i(n,{prop:"yieldPoint",class:"mt-auto"},{default:c(()=>[i(u,{type:"number",class:"w-100",controls:!1,step:"any",modelValue:s.targetData.yieldPoint,"onUpdate:modelValue":e[5]||(e[5]=D=>s.targetData.yieldPoint=D),placeholder:"",name:"yieldPoint"},null,8,["modelValue"])]),_:1})]),t("div",rr,[e[34]||(e[34]=t("label",{class:"d-flex align-items-center fs-6 fw-semibold mb-2 text-break"},[t("span",{class:"fs-6 fw-semibold text-break text-gray-800"},"6 rpm")],-1)),i(n,{prop:"rpm",class:"mt-auto"},{default:c(()=>[i(u,{type:"number",class:"w-100",controls:!1,step:"any",modelValue:s.targetData.rpm,"onUpdate:modelValue":e[6]||(e[6]=D=>s.targetData.rpm=D),placeholder:"",name:"rpm"},null,8,["modelValue"])]),_:1})])]),t("div",ur,[t("div",cr,[t("label",fr,[e[37]||(e[37]=t("span",null,"API filtrate (ml/30min)",-1)),i(o,{placement:"top",width:400,trigger:"hover"},{reference:c(()=>e[35]||(e[35]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7"},null,-1)])),default:c(()=>[e[36]||(e[36]=t("span",null,[L(" The volume of mud filtrate that passes through a standard filter paper in 30 minutes, measured in milliliters."),t("br"),t("br"),L(" WBM: API filtrate and cake thickness are commonly measured for Water-Based Mud. The filtration properties may be different for various types of WBM formulations. OBM: Oil-Based Mud may have different filtration characteristics compared to WBM due to the presence of oil. The API filtrate and cake thickness may have specific requirements for OBM. SBM: Synthetic-Based Mud may also have unique filtration properties, and API filtrate and cake thickness measurements should consider the specific synthetic fluid used. ")],-1))]),_:1})]),i(n,{prop:"apiFiltrate",class:"mt-auto"},{default:c(()=>[i(u,{type:"number",class:"w-100",controls:!1,step:"any",modelValue:s.targetData.apiFiltrate,"onUpdate:modelValue":e[7]||(e[7]=D=>s.targetData.apiFiltrate=D),placeholder:"",name:"apiFiltrate"},null,8,["modelValue"])]),_:1})]),t("div",mr,[t("label",pr,[e[40]||(e[40]=t("span",{class:"fs-6 fw-semibold text-break text-gray-800"},"API Cake",-1)),i(o,{placement:"top",width:400,trigger:"hover"},{reference:c(()=>e[38]||(e[38]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7 me-4"},null,-1)])),default:c(()=>[e[39]||(e[39]=t("span",null,[L(" The thickness of the filter cake formed by the drilling mud on the filter paper, measured in 1/32 of an inch."),t("br"),t("br"),L(" WBM: API filtrate and cake thickness are commonly measured for Water-Based Mud. The filtration properties may be different for various types of WBM formulations. OBM: Oil-Based Mud may have different filtration characteristics compared to WBM due to the presence of oil. The API filtrate and cake thickness may have specific requirements for OBM. SBM: Synthetic-Based Mud may also have unique filtration properties, and API filtrate and cake thickness measurements should consider the specific synthetic fluid used. ")],-1))]),_:1})]),i(n,{prop:"apiCakeThickness",class:"mt-auto"},{default:c(()=>[i(u,{type:"number",class:"w-100",controls:!1,step:"any",modelValue:s.targetData.apiCakeThickness,"onUpdate:modelValue":e[8]||(e[8]=D=>s.targetData.apiCakeThickness=D),placeholder:"",name:"apiCakeThickness"},null,8,["modelValue"])]),_:1})])]),t("div",gr,[t("div",br,[t("label",vr,[e[43]||(e[43]=t("span",null,"pH",-1)),i(o,{placement:"top",width:400,trigger:"hover"},{reference:c(()=>e[41]||(e[41]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7"},null,-1)])),default:c(()=>[e[42]||(e[42]=t("span",null," The pH level of the drilling mud, which can affect the performance of mud additives. ",-1))]),_:1})]),i(n,{prop:"pH",class:"mt-auto"},{default:c(()=>[i(u,{type:"number",class:"w-100",controls:!1,step:"any",modelValue:s.targetData.pH,"onUpdate:modelValue":e[9]||(e[9]=D=>s.targetData.pH=D),placeholder:"",name:"pH"},null,8,["modelValue"])]),_:1})]),t("div",hr,[t("label",yr,[e[46]||(e[46]=t("span",{class:"fs-6 fw-semibold text-break text-gray-800"},"Mud Alkalinity (Pm) (ml)",-1)),i(o,{placement:"top",width:400,trigger:"hover"},{reference:c(()=>e[44]||(e[44]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7 me-4"},null,-1)])),default:c(()=>[e[45]||(e[45]=t("span",null," Alkalinity measurements that can provide insights into the mud's pH buffering capacity and stability. ",-1))]),_:1})]),i(n,{prop:"mudAlkalinity",class:"mt-auto"},{default:c(()=>[i(u,{type:"number",class:"w-100",controls:!1,step:"any",modelValue:s.targetData.mudAlkalinity,"onUpdate:modelValue":e[10]||(e[10]=D=>s.targetData.mudAlkalinity=D),placeholder:"",name:"mudAlkalinity"},null,8,["modelValue"])]),_:1})])]),t("div",wr,[t("div",$r,[t("label",Sr,[e[49]||(e[49]=t("span",null,"Filtrate Alkalinity (Pf) (ml)",-1)),i(o,{placement:"top",width:400,trigger:"hover"},{reference:c(()=>e[47]||(e[47]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7"},null,-1)])),default:c(()=>[e[48]||(e[48]=t("span",null," Alkalinity measurements that can provide insights into the mud's pH buffering capacity and stability. ",-1))]),_:1})]),i(n,{prop:"filtrateAlkalinity",class:"mt-auto"},{default:c(()=>[i(u,{type:"number",class:"w-100",controls:!1,step:"any",modelValue:s.targetData.filtrateAlkalinity,"onUpdate:modelValue":e[11]||(e[11]=D=>s.targetData.filtrateAlkalinity=D),placeholder:"",name:"filtrateAlkalinity"},null,8,["modelValue"])]),_:1})]),t("div",_r,[t("label",kr,[e[52]||(e[52]=t("span",null,"Filtrate Alkalinity (Mf) (ml)",-1)),i(o,{placement:"top",width:400,trigger:"hover"},{reference:c(()=>e[50]||(e[50]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7"},null,-1)])),default:c(()=>[e[51]||(e[51]=t("span",null," Alkalinity measurements that can provide insights into the mud's pH buffering capacity and stability. ",-1))]),_:1})]),i(n,{prop:"filtrateAlkalinity",class:"mt-auto"},{default:c(()=>[i(u,{type:"number",class:"w-100",controls:!1,step:"any",modelValue:s.targetData.filtrateAlkalinity,"onUpdate:modelValue":e[12]||(e[12]=D=>s.targetData.filtrateAlkalinity=D),placeholder:"",name:"filtrateAlkalinity"},null,8,["modelValue"])]),_:1})])]),t("div",Dr,[t("div",Ir,[t("label",Vr,[e[55]||(e[55]=t("span",null,"Chlorides (mg/L)",-1)),i(o,{placement:"top",width:400,trigger:"hover"},{reference:c(()=>e[53]||(e[53]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7"},null,-1)])),default:c(()=>[e[54]||(e[54]=t("span",null," Measurements of various ions in the drilling mud, which can affect mud chemistry and performance. ",-1))]),_:1})]),i(n,{prop:"chlorides",class:"mt-auto"},{default:c(()=>[i(u,{type:"number",class:"w-100",controls:!1,step:"any",modelValue:s.targetData.chlorides,"onUpdate:modelValue":e[13]||(e[13]=D=>s.targetData.chlorides=D),placeholder:"",name:"chlorides"},null,8,["modelValue"])]),_:1})]),t("div",Pr,[t("label",Cr,[e[58]||(e[58]=t("span",{class:"fs-6 fw-semibold text-break text-gray-800"},"Total Hardness (mg/L)",-1)),i(o,{placement:"top",width:400,trigger:"hover"},{reference:c(()=>e[56]||(e[56]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7 me-4"},null,-1)])),default:c(()=>[e[57]||(e[57]=t("span",null," Measurements of various ions in the drilling mud, which can affect mud chemistry and performance. ",-1))]),_:1})]),i(n,{prop:"totalHardness",class:"mt-auto"},{default:c(()=>[i(u,{type:"number",class:"w-100",controls:!1,step:"any",modelValue:s.targetData.totalHardness,"onUpdate:modelValue":e[14]||(e[14]=D=>s.targetData.totalHardness=D),placeholder:"",name:"totalHardness"},null,8,["modelValue"])]),_:1})])]),t("div",Tr,[t("div",Fr,[t("label",xr,[e[61]||(e[61]=t("span",null,"Linear Gel Strength (LGS) (%)",-1)),i(o,{placement:"top",width:400,trigger:"hover"},{reference:c(()=>e[59]||(e[59]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7"},null,-1)])),default:c(()=>[e[60]||(e[60]=t("span",null," LGS (%) = (Gel Strength (lbs/100 ft²) / Mud Weight (lbs/gal)) x 100 ",-1))]),_:1})]),i(n,{prop:"linearGelStrengthPercent",class:"mt-auto"},{default:c(()=>[i(u,{type:"number",class:"w-100",controls:!1,step:"any",max:100,min:0,modelValue:s.targetData.linearGelStrengthPercent,"onUpdate:modelValue":e[15]||(e[15]=D=>s.targetData.linearGelStrengthPercent=D),placeholder:"",name:"linearGelStrengthPercent"},null,8,["modelValue"])]),_:1})])]),t("div",Nr,[t("button",{type:"button",id:"kt_modal_add_customer_cancel",class:"btn text-gray-700 btn-sm me-3 btn-blue",onClick:e[16]||(e[16]=(...D)=>s.reset&&s.reset(...D)),disabled:s.loading}," Discard ",8,Ar),t("button",{"data-kt-indicator":s.loading?"on":null,class:"btn btn-sm btn-primary",type:"submit",disabled:s.loading},[s.loading?U("",!0):($(),V("span",qr," Save ")),s.loading?($(),V("span",Mr,e[62]||(e[62]=[L(" Please wait... "),t("span",{class:"spinner-border spinner-border-sm align-middle ms-2"},null,-1)]))):U("",!0)],8,Hr)])]),_:1},8,["onSubmit","model","rules"])])]),_:1},8,["modelValue"])}const zr=Q(Yd,[["render",jr]]),Er=G({name:"sample-modal",components:{SvgIcon:X},props:{loadPage:{type:Function,default:()=>{},required:!0}},setup(s){const e=re(),v=ee("dailyReport"),b=dt(),f=y(!1),r={id:"",dailyReportId:"",fluidType:null,weightedMud:!1,sampleFrom:"",timeSampleTaken:"",flowlineTemperature:null,measuredDepth:null,mudWeight:null,funnelViscosity:null,temperatureForPlasticViscosity:null,plasticViscosity:null,yieldPoint:null,gelStrength10s:null,gelStrength10m:null,gelStrength30m:null,apiFiltrate:null,apiCakeThickness:null,temperatureForHTHP:null,hthpFiltrate:null,hthpCakeThickness:null,solids:null,oil:null,water:null,sandContent:null,mbtCapacity:null,pH:null,mudAlkalinity:null,filtrateAlkalinity:null,calcium:null,chlorides:null,totalHardness:null,excessLime:null,kPlus:null,makeUpWater:null,solidsAdjustedForSalt:null,fineLCM:null,coarseLCM:null,linearGelStrengthPercent:null,linearGelStrengthLbBbl:null,highGelStrengthPercent:null,highGelStrengthLbBbl:null,bentoniteConcentrationPercent:null,bentoniteConcentrationLbBbl:null,drillSolidsConcentrationPercent:null,drillSolidsConcentrationLbBbl:null,drillSolidsToBentoniteRatio:null,averageSpecificGravityOfSolids:null,shearRate600:null,shearRate300:null,shearRate200:null,shearRate100:null,shearRate6:null,shearRate3:null,apparentViscosity:null,shearRate:null,shearStress:null},l=y(JSON.parse(JSON.stringify(r))),o=y(null),a=y(!1),d=y("");J(d,h=>{h!==""&&n()}),J(f,h=>{var _;h===!1&&(d.value="",w(),(_=o==null?void 0:o.value)==null||_.resetFields())});const n=async()=>{b.getSampleDetails({id:d.value,callback:{onSuccess:h=>{l.value={...h}}}})},u=async h=>{a.value=!0,b.updateSample({id:d.value,params:h,callback:{onSuccess:_=>{s!=null&&s.loadPage&&(s==null||s.loadPage()),D()},onFinish:_=>{a.value=!1}}})},m=async h=>{a.value=!0,b.createSample({params:h,callback:{onSuccess:_=>{s!=null&&s.loadPage&&(s==null||s.loadPage()),D()},onFinish:_=>{a.value=!1}}})},p=()=>{f.value=!0},D=()=>{f.value=!1},k=h=>{d.value=h.toString()},N=y({fluidType:[{required:!0,message:"Please select Fluid Type",trigger:["change","blur"]}],sampleFrom:[{required:!0,message:"Please select Sample From",trigger:["change","blur"]}],mudWeight:[{required:!0,message:"Please input MW",trigger:["change","blur"]}],plasticViscosity:[{required:!0,message:"Please input Plastic Viscosity",trigger:["change","blur"]}],solids:[{required:!0,message:"Please input Solids",trigger:["change","blur"]}],oil:[{required:!0,message:"Please input Oil",trigger:["change","blur"]}],water:[{required:!0,message:"Please input Water",trigger:["change","blur"]}],chlorides:[{required:!0,message:"Please input Chlorides",trigger:["change","blur"]}],timeSampleTaken:[{required:!0,message:"Please select Time Sample Taken",trigger:["change","blur"]}]}),T=()=>{o.value&&o.value.validate(h=>{var _,H,C,F,j,x,A,M,z,q,R,Y,se,oe,le,O,K,te,ue,he,ye,$e,Se,we,Ve,Pe,Ce,Te,Fe,xe,Ne,Ae,He,qe,Me,je,ze,Ee,Le,Re,Be,Oe,Ue,We,Ge,Ye,Je,Qe,Ke,Xe,Ze,ft,mt,pt,gt;if(h){const bt={...l.value,sampleFrom:(H=(_=l==null?void 0:l.value)==null?void 0:_.sampleFrom)==null?void 0:H.toString(),fluidType:Number((C=l==null?void 0:l.value)==null?void 0:C.fluidType),flowlineTemperature:Number((F=l==null?void 0:l.value)==null?void 0:F.flowlineTemperature),measuredDepth:Number((j=l==null?void 0:l.value)==null?void 0:j.measuredDepth),mudWeight:Number((x=l==null?void 0:l.value)==null?void 0:x.mudWeight),funnelViscosity:Number((A=l==null?void 0:l.value)==null?void 0:A.funnelViscosity),temperatureForPlasticViscosity:Number((M=l==null?void 0:l.value)==null?void 0:M.temperatureForPlasticViscosity),plasticViscosity:Number((z=l==null?void 0:l.value)==null?void 0:z.plasticViscosity),yieldPoint:Number((q=l==null?void 0:l.value)==null?void 0:q.yieldPoint),gelStrength10s:Number((R=l==null?void 0:l.value)==null?void 0:R.gelStrength10s),gelStrength10m:Number((Y=l==null?void 0:l.value)==null?void 0:Y.gelStrength10m),gelStrength30m:Number((se=l==null?void 0:l.value)==null?void 0:se.gelStrength30m),apiFiltrate:Number((oe=l==null?void 0:l.value)==null?void 0:oe.apiFiltrate),apiCakeThickness:Number((le=l==null?void 0:l.value)==null?void 0:le.apiCakeThickness),temperatureForHTHP:Number((O=l==null?void 0:l.value)==null?void 0:O.temperatureForHTHP),hthpFiltrate:Number((K=l==null?void 0:l.value)==null?void 0:K.hthpFiltrate),hthpCakeThickness:Number((te=l==null?void 0:l.value)==null?void 0:te.hthpCakeThickness),solids:Number((ue=l==null?void 0:l.value)==null?void 0:ue.solids),oil:Number((he=l==null?void 0:l.value)==null?void 0:he.oil),water:Number((ye=l==null?void 0:l.value)==null?void 0:ye.water),sandContent:Number(($e=l==null?void 0:l.value)==null?void 0:$e.sandContent),mbtCapacity:Number((Se=l==null?void 0:l.value)==null?void 0:Se.mbtCapacity),pH:Number((we=l==null?void 0:l.value)==null?void 0:we.pH),mudAlkalinity:Number((Ve=l==null?void 0:l.value)==null?void 0:Ve.mudAlkalinity),filtrateAlkalinity:Number((Pe=l==null?void 0:l.value)==null?void 0:Pe.filtrateAlkalinity),calcium:Number((Ce=l==null?void 0:l.value)==null?void 0:Ce.calcium),chlorides:Number((Te=l==null?void 0:l.value)==null?void 0:Te.chlorides),totalHardness:Number((Fe=l==null?void 0:l.value)==null?void 0:Fe.totalHardness),excessLime:Number((xe=l==null?void 0:l.value)==null?void 0:xe.excessLime),kPlus:Number((Ne=l==null?void 0:l.value)==null?void 0:Ne.kPlus),makeUpWater:Number((Ae=l==null?void 0:l.value)==null?void 0:Ae.makeUpWater),solidsAdjustedForSalt:Number((He=l==null?void 0:l.value)==null?void 0:He.solidsAdjustedForSalt),fineLCM:Number((qe=l==null?void 0:l.value)==null?void 0:qe.fineLCM),coarseLCM:Number((Me=l==null?void 0:l.value)==null?void 0:Me.coarseLCM),linearGelStrengthPercent:Number((je=l==null?void 0:l.value)==null?void 0:je.linearGelStrengthPercent),linearGelStrengthLbBbl:Number((ze=l==null?void 0:l.value)==null?void 0:ze.linearGelStrengthLbBbl),highGelStrengthPercent:Number((Ee=l==null?void 0:l.value)==null?void 0:Ee.highGelStrengthPercent),highGelStrengthLbBbl:Number((Le=l==null?void 0:l.value)==null?void 0:Le.highGelStrengthLbBbl),bentoniteConcentrationPercent:Number((Re=l==null?void 0:l.value)==null?void 0:Re.bentoniteConcentrationPercent),bentoniteConcentrationLbBbl:Number((Be=l==null?void 0:l.value)==null?void 0:Be.bentoniteConcentrationLbBbl),drillSolidsConcentrationPercent:Number((Oe=l==null?void 0:l.value)==null?void 0:Oe.drillSolidsConcentrationPercent),drillSolidsConcentrationLbBbl:Number((Ue=l==null?void 0:l.value)==null?void 0:Ue.drillSolidsConcentrationLbBbl),drillSolidsToBentoniteRatio:Number((We=l==null?void 0:l.value)==null?void 0:We.drillSolidsToBentoniteRatio),averageSpecificGravityOfSolids:Number((Ge=l==null?void 0:l.value)==null?void 0:Ge.averageSpecificGravityOfSolids),shearRate600:Number((Ye=l==null?void 0:l.value)==null?void 0:Ye.shearRate600),shearRate300:Number((Je=l==null?void 0:l.value)==null?void 0:Je.shearRate300),shearRate200:Number((Qe=l==null?void 0:l.value)==null?void 0:Qe.shearRate200),shearRate100:Number((Ke=l==null?void 0:l.value)==null?void 0:Ke.shearRate100),shearRate6:Number((Xe=l==null?void 0:l.value)==null?void 0:Xe.shearRate6),shearRate3:Number((Ze=l==null?void 0:l.value)==null?void 0:Ze.shearRate3),apparentViscosity:Number((ft=l==null?void 0:l.value)==null?void 0:ft.apparentViscosity),shearRate:Number((mt=l==null?void 0:l.value)==null?void 0:mt.shearRate),shearStress:Number((pt=l==null?void 0:l.value)==null?void 0:pt.shearStress)};d!=null&&d.value?u({...bt,dailyReportId:v==null?void 0:v.getDailyReportId()}):v==null||v.createDailyReport({wellId:(gt=e==null?void 0:e.params)==null?void 0:gt.id,callback:{onSuccess:N2=>{m({...bt,dailyReportId:v==null?void 0:v.getDailyReportId()})}}})}})},w=()=>{l.value=JSON.parse(JSON.stringify(r))};return{id:d,modal:f,rules:N,loading:a,targetData:l,formRef:o,fluidTypeOptions:lt,sampleFromOptions:st,show:p,hide:D,submit:T,setId:k,reset:w}}}),Lr={class:"d-flex align-items-center w-100"},Rr={class:"modal-title"},Br={class:"row row-cols-1 row-cols-sm-2 row-cols-md-3 row-cols-lg-5 g-9 mb-3"},Or={class:"col fv-row d-flex flex-column justify-content-stretch"},Ur={class:"d-flex align-items-center fs-6 fw-semibold mb-2 text-break"},Wr={class:"col fv-row d-flex flex-sm-column align-items-center align-items-sm-start justify-content-stretch"},Gr={class:"d-flex align-items-center fs-6 fw-semibold mb-2 text-break"},Yr={class:"col fv-row d-flex flex-column justify-content-stretch"},Jr={class:"d-flex align-items-center fs-6 fw-semibold mb-2 text-break"},Qr={class:"col fv-row d-flex flex-column justify-content-stretch"},Kr={class:"d-flex align-items-center fs-6 fw-semibold mb-2 text-break"},Xr={class:"col fv-row d-flex flex-column justify-content-stretch"},Zr={class:"d-flex align-items-center fs-6 fw-semibold mb-2 text-break"},eu={class:"row row-cols-1 row-cols-sm-2 row-cols-md-3 row-cols-lg-5 g-9 mb-3"},tu={class:"col fv-row d-flex flex-column justify-content-stretch"},su={class:"d-flex align-items-center fs-6 fw-semibold mb-2 text-break"},lu={class:"col fv-row d-flex flex-column justify-content-stretch"},ou={class:"d-flex align-items-center fs-6 fw-semibold mb-2 text-break"},nu={class:"col fv-row d-flex flex-column justify-content-stretch"},au={class:"d-flex align-items-center fs-6 fw-semibold mb-2 text-break"},iu={class:"col fv-row d-flex flex-column justify-content-stretch"},du={class:"d-flex align-items-center fs-6 fw-semibold mb-2 text-break"},ru={class:"row row-cols-1 row-cols-sm-2 row-cols-md-3 row-cols-lg-5 g-9 mb-3"},uu={class:"col fv-row d-flex flex-column justify-content-stretch"},cu={class:"d-flex align-items-center fs-6 fw-semibold mb-2 text-break"},fu={class:"col fv-row d-flex flex-column justify-content-stretch"},mu={class:"d-flex align-items-center fs-6 fw-semibold mb-2 text-break"},pu={class:"col fv-row d-flex flex-column justify-content-stretch"},gu={class:"d-flex align-items-center fs-6 fw-semibold mb-2 text-break"},bu={class:"col fv-row d-flex flex-column justify-content-stretch"},vu={class:"d-flex align-items-center fs-6 fw-semibold mb-2 text-break"},hu={class:"col fv-row d-flex flex-column justify-content-stretch"},yu={class:"d-flex align-items-center fs-6 fw-semibold mb-2 text-break"},wu={class:"row row-cols-1 row-cols-sm-2 row-cols-md-3 row-cols-lg-5 g-9 mb-3"},$u={class:"col fv-row d-flex flex-column justify-content-stretch"},Su={class:"d-flex align-items-center fs-6 fw-semibold mb-2 text-break"},_u={class:"col fv-row d-flex flex-column justify-content-stretch"},ku={class:"d-flex align-items-center fs-6 fw-semibold mb-2 text-break"},Du={class:"col fv-row d-flex flex-column justify-content-stretch"},Iu={class:"d-flex align-items-center fs-6 fw-semibold mb-2 text-break"},Vu={class:"col fv-row d-flex flex-column justify-content-stretch"},Pu={class:"d-flex align-items-center fs-6 fw-semibold mb-2 text-break"},Cu={class:"col fv-row d-flex flex-column justify-content-stretch"},Tu={class:"d-flex align-items-center fs-6 fw-semibold mb-2 text-break"},Fu={class:"row row-cols-1 row-cols-sm-2 row-cols-md-3 row-cols-lg-5 g-9 mb-3"},xu={class:"col fv-row d-flex flex-column justify-content-stretch"},Nu={class:"d-flex align-items-center fs-6 fw-semibold mb-2 text-break"},Au={class:"col fv-row d-flex flex-column justify-content-stretch"},Hu={class:"d-flex align-items-center fs-6 fw-semibold mb-2 text-break"},qu={class:"col fv-row d-flex flex-column justify-content-stretch"},Mu={class:"d-flex align-items-center fs-6 fw-semibold mb-2 text-break"},ju={class:"col fv-row d-flex flex-column justify-content-stretch"},zu={class:"d-flex align-items-center fs-6 fw-semibold mb-2 text-break"},Eu={class:"col fv-row d-flex flex-column justify-content-stretch"},Lu={class:"d-flex align-items-center fs-6 fw-semibold mb-2 text-break"},Ru={class:"row row-cols-1 row-cols-sm-2 row-cols-md-3 row-cols-lg-5 g-9 mb-3"},Bu={class:"col fv-row d-flex flex-column justify-content-stretch"},Ou={class:"d-flex align-items-center fs-6 fw-semibold mb-2 text-break"},Uu={class:"col fv-row d-flex flex-column justify-content-stretch"},Wu={class:"d-flex align-items-center fs-6 fw-semibold mb-2 text-break"},Gu={class:"col fv-row d-flex flex-column justify-content-stretch"},Yu={class:"d-flex align-items-center fs-6 fw-semibold mb-2 text-break"},Ju={class:"col fv-row d-flex flex-column justify-content-stretch"},Qu={class:"d-flex align-items-center fs-6 fw-semibold mb-2 text-break"},Ku={class:"col fv-row d-flex flex-column justify-content-stretch"},Xu={class:"d-flex align-items-center fs-6 fw-semibold mb-2 text-break"},Zu={class:"row row-cols-1 row-cols-sm-2 row-cols-md-3 row-cols-lg-5 g-9 mb-3"},ec={class:"col fv-row d-flex flex-column justify-content-stretch"},tc={class:"d-flex align-items-center fs-6 fw-semibold mb-2 text-break"},sc={class:"col fv-row d-flex flex-column justify-content-stretch"},lc={class:"d-flex align-items-center fs-6 fw-semibold mb-2 text-break"},oc={class:"col fv-row d-flex flex-column justify-content-stretch"},nc={class:"d-flex align-items-center fs-6 fw-semibold mb-2 text-break"},ac={class:"col fv-row d-flex flex-column justify-content-stretch"},ic={class:"d-flex align-items-center fs-6 fw-semibold mb-2 text-break"},dc={class:"col fv-row d-flex flex-column justify-content-stretch"},rc={class:"d-flex align-items-center fs-6 fw-semibold mb-2 text-break"},uc={class:"row row-cols-1 row-cols-sm-2 row-cols-md-3 g-9 mb-3"},cc={class:"col fv-row d-flex flex-column justify-content-stretch"},fc={class:"d-flex align-items-center fs-6 fw-semibold mb-2 text-break"},mc={class:"col fv-row d-flex flex-column justify-content-stretch"},pc={class:"d-flex align-items-center fs-6 fw-semibold mb-2 text-break"},gc={class:"col fv-row d-flex flex-column justify-content-stretch"},bc={class:"d-flex align-items-center fs-6 fw-semibold mb-2 text-break"},vc={class:"row row-cols-1 row-cols-sm-2 row-cols-md-3 g-9 mb-3"},hc={class:"col fv-row d-flex flex-column justify-content-stretch"},yc={class:"col fv-row d-flex flex-column justify-content-stretch"},wc={class:"col fv-row d-flex flex-column justify-content-stretch"},$c={class:"row row-cols-1 row-cols-sm-2 row-cols-md-3 g-9 mb-3"},Sc={class:"col fv-row d-flex flex-column justify-content-stretch"},_c={class:"col fv-row d-flex flex-column justify-content-stretch"},kc={class:"col fv-row d-flex flex-column justify-content-stretch"},Dc={class:"row row-cols-1 row-cols-sm-2 row-cols-md-3 g-9 mb-3"},Ic={class:"col fv-row d-flex flex-column justify-content-stretch"},Vc={class:"d-flex align-items-center fs-6 fw-semibold mb-2 text-break"},Pc={class:"col fv-row d-flex flex-column justify-content-stretch"},Cc={class:"d-flex align-items-center fs-6 fw-semibold mb-2 text-break"},Tc={class:"col fv-row d-flex flex-column justify-content-stretch"},Fc={class:"d-flex align-items-center fs-6 fw-semibold mb-2 text-break"},xc={class:"row row-cols-1 row-cols-sm-2 g-9"},Nc={class:"col fv-row d-flex flex-column justify-content-stretch"},Ac={class:"d-flex align-items-center fs-6 fw-semibold mb-2 text-break"},Hc={class:"col fv-row d-flex flex-column justify-content-stretch"},qc={class:"d-flex align-items-center fs-6 fw-semibold mb-2 text-break"},Mc={class:"modal-footer d-flex justify-content-center align-items-center"},jc=["disabled"],zc=["data-kt-indicator","disabled"],Ec={key:0,class:"indicator-label"},Lc={key:1,class:"indicator-progress"};function Rc(s,e,v,b,f,r){const l=S("SvgIcon"),o=S("el-popover"),a=S("el-option"),d=S("el-select"),n=S("el-form-item"),u=S("el-time-picker"),m=S("el-input"),p=S("el-form"),D=S("el-dialog");return $(),W(D,{modelValue:s.modal,"onUpdate:modelValue":e[50]||(e[50]=k=>s.modal=k),"show-close":!1,width:"1000","align-center":""},{header:c(()=>[t("div",Lr,[t("h3",Rr,I(`${s.id?"Edit Sample":"New Sample"}`),1),t("span",{class:"cursor-pointer ms-auto",onClick:e[0]||(e[0]=(...k)=>s.hide&&s.hide(...k))},[i(l,{icon:"closeModalIcon"})])])]),default:c(()=>[t("div",null,[i(p,{onSubmit:de(s.submit,["prevent"]),model:s.targetData,rules:s.rules,ref:"formRef",class:"form sample-form"},{default:c(()=>[t("div",Br,[t("div",Or,[t("label",Ur,[e[53]||(e[53]=t("span",{class:"required"},"Fluid Type",-1)),i(o,{placement:"top",width:400,trigger:"hover"},{reference:c(()=>e[51]||(e[51]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7"},null,-1)])),default:c(()=>[e[52]||(e[52]=t("span",null,[L(" Water-based drilling muds are the most common type. They use water as the base fluid and may contain various additives. The key differences for WBM include water quality, filtration properties, and clay content."),t("br"),t("br"),L(" Oil-based drilling muds use oil as the base fluid. Key considerations for OBM include oil composition, oil/water ratio, and rheological properties."),t("br"),t("br"),L(" Synthetic-based drilling muds use synthetic fluids as the base, such as esters or olefins. Specific data requirements depend on the type of synthetic fluid used. ")],-1))]),_:1})]),i(n,{prop:"fluidType",class:"mt-auto"},{default:c(()=>[i(d,{modelValue:s.targetData.fluidType,"onUpdate:modelValue":e[1]||(e[1]=k=>s.targetData.fluidType=k),placeholder:"",class:"w-100",clearable:""},{default:c(()=>[($(!0),V(B,null,Z(s.fluidTypeOptions,k=>($(),W(a,{key:k.value,label:k.label,value:k.value,name:"fluidType"},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),t("div",Wr,[t("label",Gr,[e[56]||(e[56]=t("span",{class:"required"},"Weighted Mud",-1)),i(o,{placement:"top",width:400,trigger:"hover"},{reference:c(()=>e[54]||(e[54]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7"},null,-1)])),default:c(()=>[e[55]||(e[55]=t("span",null," Weighted mud refers to drilling fluids that have been weighted with solid materials, such as barite, to increase their density. For weighted mud, you may need to include additional data points related to the type and concentration of weighting agents, as well as their impact on rheological properties and filtration control. ",-1))]),_:1})]),i(n,{prop:"weightedMud",class:"mt-sm-auto mb-0 mb-sm-auto ms-5 ms-sm-0"},{default:c(()=>[$t(t("input",{class:"form-check-input h-20px w-20px",type:"checkbox",placeholder:"",name:"weightedMud","onUpdate:modelValue":e[2]||(e[2]=k=>s.targetData.weightedMud=k)},null,512),[[St,s.targetData.weightedMud]])]),_:1})]),t("div",Yr,[t("label",Jr,[e[59]||(e[59]=t("span",{class:"required"},"Sample From",-1)),i(o,{placement:"top",width:400,trigger:"hover"},{reference:c(()=>e[57]||(e[57]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7"},null,-1)])),default:c(()=>[e[58]||(e[58]=t("span",null," Sample From: Refers to the source or location from which the mud sample was obtained. It indicates where in the drilling system the mud sample was extracted or collected for analysis. ",-1))]),_:1})]),i(n,{prop:"sampleFrom",class:"mt-auto"},{default:c(()=>[i(d,{modelValue:s.targetData.sampleFrom,"onUpdate:modelValue":e[3]||(e[3]=k=>s.targetData.sampleFrom=k),class:"w-100",placeholder:"",clearable:""},{default:c(()=>[($(!0),V(B,null,Z(s.sampleFromOptions,k=>($(),W(a,{key:k.value,label:k.label,value:k.value,name:"sampleFrom"},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),t("div",Qr,[t("label",Kr,[e[62]||(e[62]=t("span",{class:"required"},"Time Sample Taken (hh:mm)",-1)),i(o,{placement:"top",width:400,trigger:"hover"},{reference:c(()=>e[60]||(e[60]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7"},null,-1)])),default:c(()=>[e[61]||(e[61]=t("span",null," The time of day that the sample is taken (24-hour) ",-1))]),_:1})]),i(n,{prop:"timeSampleTaken",class:"mt-auto"},{default:c(()=>[i(u,{modelValue:s.targetData.timeSampleTaken,"onUpdate:modelValue":e[4]||(e[4]=k=>s.targetData.timeSampleTaken=k),placeholder:"hh:mm",name:"timeSampleTaken",format:"HH:mm","value-format":"HH:mm"},null,8,["modelValue"])]),_:1})]),t("div",Xr,[t("label",Zr,[e[65]||(e[65]=t("span",null,"Flowline Temperature (F)",-1)),i(o,{placement:"top",width:400,trigger:"hover"},{reference:c(()=>e[63]||(e[63]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7"},null,-1)])),default:c(()=>[e[64]||(e[64]=t("span",null," The temperature of the drilling mud in the flowline as it returns to the surface. ",-1))]),_:1})]),i(n,{prop:"flowlineTemperature",class:"mt-auto"},{default:c(()=>[i(m,{type:"number",class:"w-100",controls:!1,step:"any",modelValue:s.targetData.flowlineTemperature,"onUpdate:modelValue":e[5]||(e[5]=k=>s.targetData.flowlineTemperature=k),placeholder:"",name:"flowlineTemperature"},null,8,["modelValue"])]),_:1})])]),t("div",eu,[t("div",tu,[t("label",su,[e[68]||(e[68]=t("span",null,"Depth (ft)",-1)),i(o,{placement:"top",width:400,trigger:"hover"},{reference:c(()=>e[66]||(e[66]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7"},null,-1)])),default:c(()=>[e[67]||(e[67]=t("span",null," The depth at which drilling operations are taking place. ",-1))]),_:1})]),i(n,{prop:"measuredDepth",class:"mt-auto"},{default:c(()=>[i(m,{type:"number",class:"w-100",controls:!1,step:"any",modelValue:s.targetData.measuredDepth,"onUpdate:modelValue":e[6]||(e[6]=k=>s.targetData.measuredDepth=k),placeholder:"",name:"measuredDepth"},null,8,["modelValue"])]),_:1})]),t("div",lu,[t("label",ou,[e[71]||(e[71]=t("span",{class:"fs-6 fw-semibold text-break text-gray-800 required"},"MW (ppg or lbs/gal)",-1)),i(o,{placement:"bottom",width:400,trigger:"hover"},{reference:c(()=>e[69]||(e[69]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7 me-4"},null,-1)])),default:c(()=>[e[70]||(e[70]=t("span",null," Mud Weight, also known as mud density, is the density of the drilling mud, typically measured in pounds per gallon (ppg). For different types of drilling fluids, the density requirements can vary. ",-1))]),_:1})]),i(n,{prop:"mudWeight",class:"mt-auto"},{default:c(()=>[i(m,{type:"number",class:"w-100",controls:!1,step:"any",modelValue:s.targetData.mudWeight,"onUpdate:modelValue":e[7]||(e[7]=k=>s.targetData.mudWeight=k),placeholder:"",name:"mudWeight"},null,8,["modelValue"])]),_:1})]),t("div",nu,[t("label",au,[e[74]||(e[74]=t("span",null,"Funnel Viscosity (sec/qt) ",-1)),i(o,{placement:"top",width:400,trigger:"hover"},{reference:c(()=>e[72]||(e[72]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7"},null,-1)])),default:c(()=>[e[73]||(e[73]=t("span",null," The funnel viscosity measures the thickness or viscosity of the drilling mud. It is typically measured in seconds per quart (sec/qt). ",-1))]),_:1})]),i(n,{prop:"funnelViscosity",class:"mt-auto"},{default:c(()=>[i(m,{type:"number",class:"w-100",controls:!1,step:"any",modelValue:s.targetData.funnelViscosity,"onUpdate:modelValue":e[8]||(e[8]=k=>s.targetData.funnelViscosity=k),placeholder:"",name:"funnelViscosity"},null,8,["modelValue"])]),_:1})]),t("div",iu,[t("label",du,[e[77]||(e[77]=t("span",null,"Temperature for Plastic Viscosity (f)",-1)),i(o,{placement:"top",width:400,trigger:"hover"},{reference:c(()=>e[75]||(e[75]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7"},null,-1)])),default:c(()=>[e[76]||(e[76]=t("span",null," The temperature at which Plastic Viscosity (PV) is measured, which affects mud rheology. ",-1))]),_:1})]),i(n,{prop:"temperatureForPlasticViscosity",class:"mt-auto"},{default:c(()=>[i(m,{type:"number",class:"w-100",controls:!1,step:"any",modelValue:s.targetData.temperatureForPlasticViscosity,"onUpdate:modelValue":e[9]||(e[9]=k=>s.targetData.temperatureForPlasticViscosity=k),placeholder:"",name:"temperatureForPlasticViscosity"},null,8,["modelValue"])]),_:1})])]),t("div",ru,[t("div",uu,[t("label",cu,[e[80]||(e[80]=t("span",{class:"required"},"PV (Plastic Viscosity) (cP)",-1)),i(o,{placement:"top",width:400,trigger:"hover"},{reference:c(()=>e[78]||(e[78]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7"},null,-1)])),default:c(()=>[e[79]||(e[79]=t("span",null," Plastic Viscosity is a measure of the resistance to flow of the drilling mud. It is typically measured in centipoise (cP). ",-1))]),_:1})]),i(n,{prop:"plasticViscosity",class:"mt-auto"},{default:c(()=>[i(m,{type:"number",class:"w-100",controls:!1,step:"any",modelValue:s.targetData.plasticViscosity,"onUpdate:modelValue":e[10]||(e[10]=k=>s.targetData.plasticViscosity=k),placeholder:"",name:"plasticViscosity"},null,8,["modelValue"])]),_:1})]),t("div",fu,[t("label",mu,[e[83]||(e[83]=t("span",{class:"fs-6 fw-semibold text-break text-gray-800 required"},"YP (lbf/100ft2)",-1)),i(o,{placement:"top",width:400,trigger:"hover"},{reference:c(()=>e[81]||(e[81]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7 me-4"},null,-1)])),default:c(()=>[e[82]||(e[82]=t("span",null," Yield Point is the amount of force required to initiate mud flow. It's typically measured in pounds per 100 square feet (lbf/100ft^2). ",-1))]),_:1})]),i(n,{prop:"yieldPoint",class:"mt-auto"},{default:c(()=>[i(m,{type:"number",class:"w-100",controls:!1,step:"any",modelValue:s.targetData.yieldPoint,"onUpdate:modelValue":e[11]||(e[11]=k=>s.targetData.yieldPoint=k),placeholder:"",name:"yieldPoint"},null,8,["modelValue"])]),_:1})]),t("div",pu,[t("label",gu,[e[86]||(e[86]=t("span",null,"Gel Str. 10s (lbf/100ft2)",-1)),i(o,{placement:"top",width:400,trigger:"hover"},{reference:c(()=>e[84]||(e[84]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7"},null,-1)])),default:c(()=>[e[85]||(e[85]=t("span",null," Gel Strength measures the mud's ability to suspend cuttings. The numbers (10s, 10m, 30m) indicate the time at which the measurement is taken. ",-1))]),_:1})]),i(n,{prop:"gelStrength10s",class:"mt-auto"},{default:c(()=>[i(m,{type:"number",class:"w-100",controls:!1,step:"any",modelValue:s.targetData.gelStrength10s,"onUpdate:modelValue":e[12]||(e[12]=k=>s.targetData.gelStrength10s=k),placeholder:"",name:"gelStrength10s"},null,8,["modelValue"])]),_:1})]),t("div",bu,[t("label",vu,[e[89]||(e[89]=t("span",null,"Gel Str. 10m (lbf/100ft2)",-1)),i(o,{placement:"top",width:400,trigger:"hover"},{reference:c(()=>e[87]||(e[87]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7"},null,-1)])),default:c(()=>[e[88]||(e[88]=t("span",null," Gel Strength measures the mud's ability to suspend cuttings. The numbers (10s, 10m, 30m) indicate the time at which the measurement is taken. ",-1))]),_:1})]),i(n,{prop:"gelStrength10m",class:"mt-auto"},{default:c(()=>[i(m,{type:"number",class:"w-100",controls:!1,step:"any",modelValue:s.targetData.gelStrength10m,"onUpdate:modelValue":e[13]||(e[13]=k=>s.targetData.gelStrength10m=k),placeholder:"",name:"gelStrength10m"},null,8,["modelValue"])]),_:1})]),t("div",hu,[t("label",yu,[e[92]||(e[92]=t("span",null,"Gel Str. 30m (lbf/100ft2)",-1)),i(o,{placement:"top",width:400,trigger:"hover"},{reference:c(()=>e[90]||(e[90]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7"},null,-1)])),default:c(()=>[e[91]||(e[91]=t("span",null," Gel Strength measures the mud's ability to suspend cuttings. The numbers (10s, 10m, 30m) indicate the time at which the measurement is taken. ",-1))]),_:1})]),i(n,{prop:"gelStrength30m",class:"mt-auto"},{default:c(()=>[i(m,{type:"number",class:"w-100",controls:!1,step:"any",modelValue:s.targetData.gelStrength30m,"onUpdate:modelValue":e[14]||(e[14]=k=>s.targetData.gelStrength30m=k),placeholder:"",name:"gelStrength30m"},null,8,["modelValue"])]),_:1})])]),t("div",wu,[t("div",$u,[t("label",Su,[e[95]||(e[95]=t("span",null,"API filtrate (ml/30min)",-1)),i(o,{placement:"top",width:400,trigger:"hover"},{reference:c(()=>e[93]||(e[93]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7"},null,-1)])),default:c(()=>[e[94]||(e[94]=t("span",null,[L(" The volume of mud filtrate that passes through a standard filter paper in 30 minutes, measured in milliliters."),t("br"),t("br"),L(" WBM: API filtrate and cake thickness are commonly measured for Water-Based Mud. The filtration properties may be different for various types of WBM formulations. OBM: Oil-Based Mud may have different filtration characteristics compared to WBM due to the presence of oil. The API filtrate and cake thickness may have specific requirements for OBM. SBM: Synthetic-Based Mud may also have unique filtration properties, and API filtrate and cake thickness measurements should consider the specific synthetic fluid used. ")],-1))]),_:1})]),i(n,{prop:"apiFiltrate",class:"mt-auto"},{default:c(()=>[i(m,{type:"number",class:"w-100",controls:!1,step:"any",modelValue:s.targetData.apiFiltrate,"onUpdate:modelValue":e[15]||(e[15]=k=>s.targetData.apiFiltrate=k),placeholder:"",name:"apiFiltrate"},null,8,["modelValue"])]),_:1})]),t("div",_u,[t("label",ku,[e[98]||(e[98]=t("span",{class:"fs-6 fw-semibold text-break text-gray-800"},"API cake thickness (1/32in)",-1)),i(o,{placement:"top",width:400,trigger:"hover"},{reference:c(()=>e[96]||(e[96]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7 me-4"},null,-1)])),default:c(()=>[e[97]||(e[97]=t("span",null,[L(" The thickness of the filter cake formed by the drilling mud on the filter paper, measured in 1/32 of an inch."),t("br"),t("br"),L(" WBM: API filtrate and cake thickness are commonly measured for Water-Based Mud. The filtration properties may be different for various types of WBM formulations. OBM: Oil-Based Mud may have different filtration characteristics compared to WBM due to the presence of oil. The API filtrate and cake thickness may have specific requirements for OBM. SBM: Synthetic-Based Mud may also have unique filtration properties, and API filtrate and cake thickness measurements should consider the specific synthetic fluid used. ")],-1))]),_:1})]),i(n,{prop:"apiCakeThickness",class:"mt-auto"},{default:c(()=>[i(m,{type:"number",class:"w-100",controls:!1,step:"any",modelValue:s.targetData.apiCakeThickness,"onUpdate:modelValue":e[16]||(e[16]=k=>s.targetData.apiCakeThickness=k),placeholder:"",name:"apiCakeThickness"},null,8,["modelValue"])]),_:1})]),t("div",Du,[t("label",Iu,[e[101]||(e[101]=t("span",null,"Temperature for HTHP (F)",-1)),i(o,{placement:"top",width:400,trigger:"hover"},{reference:c(()=>e[99]||(e[99]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7"},null,-1)])),default:c(()=>[e[100]||(e[100]=t("span",null," The temperature at which High-Temperature, High-Pressure (HTHP) tests are conducted, which is important for evaluating mud stability under downhole conditions. ",-1))]),_:1})]),i(n,{prop:"temperatureForHTHP",class:"mt-auto"},{default:c(()=>[i(m,{type:"number",class:"w-100",controls:!1,step:"any",modelValue:s.targetData.temperatureForHTHP,"onUpdate:modelValue":e[17]||(e[17]=k=>s.targetData.temperatureForHTHP=k),placeholder:"",name:"temperatureForHTHP"},null,8,["modelValue"])]),_:1})]),t("div",Vu,[t("label",Pu,[e[104]||(e[104]=t("span",null,"HTHP filtrate (ml/30min)",-1)),i(o,{placement:"top",width:400,trigger:"hover"},{reference:c(()=>e[102]||(e[102]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7"},null,-1)])),default:c(()=>[e[103]||(e[103]=t("span",null," Similar to API filtrate, but measured under high-temperature, high-pressure conditions. ",-1))]),_:1})]),i(n,{prop:"hthpFiltrate",class:"mt-auto"},{default:c(()=>[i(m,{type:"number",class:"w-100",controls:!1,step:"any",modelValue:s.targetData.hthpFiltrate,"onUpdate:modelValue":e[18]||(e[18]=k=>s.targetData.hthpFiltrate=k),placeholder:"",name:"hthpFiltrate"},null,8,["modelValue"])]),_:1})]),t("div",Cu,[t("label",Tu,[e[107]||(e[107]=t("span",null,"HTHP cake thickness (1/32in)",-1)),i(o,{placement:"top",width:400,trigger:"hover"},{reference:c(()=>e[105]||(e[105]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7"},null,-1)])),default:c(()=>[e[106]||(e[106]=t("span",null," Similar to API cake thickness, but measured under high-temperature, high-pressure conditions. ",-1))]),_:1})]),i(n,{prop:"hthpCakeThickness",class:"mt-auto"},{default:c(()=>[i(m,{type:"number",class:"w-100",controls:!1,step:"any",modelValue:s.targetData.hthpCakeThickness,"onUpdate:modelValue":e[19]||(e[19]=k=>s.targetData.hthpCakeThickness=k),placeholder:"",name:"hthpCakeThickness"},null,8,["modelValue"])]),_:1})])]),t("div",Fu,[t("div",xu,[t("label",Nu,[e[110]||(e[110]=t("span",{class:"required"},"Solids (%)",-1)),i(o,{placement:"top",width:400,trigger:"hover"},{reference:c(()=>e[108]||(e[108]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7"},null,-1)])),default:c(()=>[e[109]||(e[109]=t("span",null," The percentage of solid materials in the drilling mud, including drill cuttings and additives. ",-1))]),_:1})]),i(n,{prop:"solids",class:"mt-auto"},{default:c(()=>[i(m,{type:"number",class:"w-100",controls:!1,step:"any",modelValue:s.targetData.solids,"onUpdate:modelValue":e[20]||(e[20]=k=>s.targetData.solids=k),placeholder:"",name:"solids"},null,8,["modelValue"])]),_:1})]),t("div",Au,[t("label",Hu,[e[113]||(e[113]=t("span",{class:"fs-6 fw-semibold text-break text-gray-800 required"},"Oil (%)",-1)),i(o,{placement:"top",width:400,trigger:"hover"},{reference:c(()=>e[111]||(e[111]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7 me-4"},null,-1)])),default:c(()=>[e[112]||(e[112]=t("span",null," These percentages represent the proportions of oil in the mud, which can be important for characterizing the mud composition. ",-1))]),_:1})]),i(n,{prop:"oil",class:"mt-auto"},{default:c(()=>[i(m,{type:"number",class:"w-100",controls:!1,step:"any",modelValue:s.targetData.oil,"onUpdate:modelValue":e[21]||(e[21]=k=>s.targetData.oil=k),placeholder:"",name:"oil"},null,8,["modelValue"])]),_:1})]),t("div",qu,[t("label",Mu,[e[116]||(e[116]=t("span",{class:"required"},"Water (%)",-1)),i(o,{placement:"top",width:400,trigger:"hover"},{reference:c(()=>e[114]||(e[114]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7"},null,-1)])),default:c(()=>[e[115]||(e[115]=t("span",null," These percentages represent the proportions of water in the mud, which can be important for characterizing the mud composition. ",-1))]),_:1})]),i(n,{prop:"water",class:"mt-auto"},{default:c(()=>[i(m,{type:"number",class:"w-100",controls:!1,step:"any",modelValue:s.targetData.water,"onUpdate:modelValue":e[22]||(e[22]=k=>s.targetData.water=k),placeholder:"",name:"water"},null,8,["modelValue"])]),_:1})]),t("div",ju,[t("label",zu,[e[119]||(e[119]=t("span",null,"Sand Content (%)",-1)),i(o,{placement:"top",width:400,trigger:"hover"},{reference:c(()=>e[117]||(e[117]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7"},null,-1)])),default:c(()=>[e[118]||(e[118]=t("span",null," These percentages represent the proportions of sand in the mud, which can be important for characterizing the mud composition. ",-1))]),_:1})]),i(n,{prop:"sandContent",class:"mt-auto"},{default:c(()=>[i(m,{type:"number",class:"w-100",controls:!1,step:"any",modelValue:s.targetData.sandContent,"onUpdate:modelValue":e[23]||(e[23]=k=>s.targetData.sandContent=k),placeholder:"",name:"sandContent"},null,8,["modelValue"])]),_:1})]),t("div",Eu,[t("label",Lu,[e[122]||(e[122]=t("span",null,"MBT capacity (lb/bbl)",-1)),i(o,{placement:"top",width:400,trigger:"hover"},{reference:c(()=>e[120]||(e[120]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7"},null,-1)])),default:c(()=>[e[121]||(e[121]=t("span",null," The Mud Balance Test (MBT) measures the mud's density and is used to calculate the mud weight in pounds per barrel (lb/bbl). ",-1))]),_:1})]),i(n,{prop:"mbtCapacity",class:"mt-auto"},{default:c(()=>[i(m,{type:"number",class:"w-100",controls:!1,step:"any",modelValue:s.targetData.mbtCapacity,"onUpdate:modelValue":e[24]||(e[24]=k=>s.targetData.mbtCapacity=k),placeholder:"",name:"mbtCapacity"},null,8,["modelValue"])]),_:1})])]),t("div",Ru,[t("div",Bu,[t("label",Ou,[e[125]||(e[125]=t("span",null,"pH",-1)),i(o,{placement:"top",width:400,trigger:"hover"},{reference:c(()=>e[123]||(e[123]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7"},null,-1)])),default:c(()=>[e[124]||(e[124]=t("span",null," The pH level of the drilling mud, which can affect the performance of mud additives. ",-1))]),_:1})]),i(n,{prop:"pH",class:"mt-auto"},{default:c(()=>[i(m,{type:"number",class:"w-100",controls:!1,step:"any",modelValue:s.targetData.pH,"onUpdate:modelValue":e[25]||(e[25]=k=>s.targetData.pH=k),placeholder:"",name:"pH"},null,8,["modelValue"])]),_:1})]),t("div",Uu,[t("label",Wu,[e[128]||(e[128]=t("span",{class:"fs-6 fw-semibold text-break text-gray-800"},"Mud Alkalinity (Pm) (ml)",-1)),i(o,{placement:"top",width:400,trigger:"hover"},{reference:c(()=>e[126]||(e[126]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7 me-4"},null,-1)])),default:c(()=>[e[127]||(e[127]=t("span",null," Alkalinity measurements that can provide insights into the mud's pH buffering capacity and stability. ",-1))]),_:1})]),i(n,{prop:"mudAlkalinity",class:"mt-auto"},{default:c(()=>[i(m,{type:"number",class:"w-100",controls:!1,step:"any",modelValue:s.targetData.mudAlkalinity,"onUpdate:modelValue":e[26]||(e[26]=k=>s.targetData.mudAlkalinity=k),placeholder:"",name:"mudAlkalinity"},null,8,["modelValue"])]),_:1})]),t("div",Gu,[t("label",Yu,[e[131]||(e[131]=t("span",null,"Filtrate Alkalinity (Pf) (ml)",-1)),i(o,{placement:"top",width:400,trigger:"hover"},{reference:c(()=>e[129]||(e[129]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7"},null,-1)])),default:c(()=>[e[130]||(e[130]=t("span",null," Alkalinity measurements that can provide insights into the mud's pH buffering capacity and stability. ",-1))]),_:1})]),i(n,{prop:"filtrateAlkalinity",class:"mt-auto"},{default:c(()=>[i(m,{type:"number",class:"w-100",controls:!1,step:"any",modelValue:s.targetData.filtrateAlkalinity,"onUpdate:modelValue":e[27]||(e[27]=k=>s.targetData.filtrateAlkalinity=k),placeholder:"",name:"filtrateAlkalinity"},null,8,["modelValue"])]),_:1})]),t("div",Ju,[t("label",Qu,[e[134]||(e[134]=t("span",null,"Filtrate Alkalinity (Mf) (ml)",-1)),i(o,{placement:"top",width:400,trigger:"hover"},{reference:c(()=>e[132]||(e[132]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7"},null,-1)])),default:c(()=>[e[133]||(e[133]=t("span",null," Alkalinity measurements that can provide insights into the mud's pH buffering capacity and stability. ",-1))]),_:1})]),i(n,{prop:"filtrateAlkalinity",class:"mt-auto"},{default:c(()=>[i(m,{type:"number",class:"w-100",controls:!1,step:"any",modelValue:s.targetData.filtrateAlkalinity,"onUpdate:modelValue":e[28]||(e[28]=k=>s.targetData.filtrateAlkalinity=k),placeholder:"",name:"filtrateAlkalinity"},null,8,["modelValue"])]),_:1})]),t("div",Ku,[t("label",Xu,[e[137]||(e[137]=t("span",null,"Calcium (mg/L)",-1)),i(o,{placement:"top",width:400,trigger:"hover"},{reference:c(()=>e[135]||(e[135]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7"},null,-1)])),default:c(()=>[e[136]||(e[136]=t("span",null," Measurements of various ions in the drilling mud, which can affect mud chemistry and performance. ",-1))]),_:1})]),i(n,{prop:"calcium",class:"mt-auto"},{default:c(()=>[i(m,{type:"number",class:"w-100",controls:!1,step:"any",modelValue:s.targetData.calcium,"onUpdate:modelValue":e[29]||(e[29]=k=>s.targetData.calcium=k),placeholder:"",name:"calcium"},null,8,["modelValue"])]),_:1})])]),t("div",Zu,[t("div",ec,[t("label",tc,[e[140]||(e[140]=t("span",{class:"required"},"Chlorides (mg/L)",-1)),i(o,{placement:"top",width:400,trigger:"hover"},{reference:c(()=>e[138]||(e[138]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7"},null,-1)])),default:c(()=>[e[139]||(e[139]=t("span",null," Measurements of various ions in the drilling mud, which can affect mud chemistry and performance. ",-1))]),_:1})]),i(n,{prop:"chlorides",class:"mt-auto"},{default:c(()=>[i(m,{type:"number",class:"w-100",controls:!1,step:"any",modelValue:s.targetData.chlorides,"onUpdate:modelValue":e[30]||(e[30]=k=>s.targetData.chlorides=k),placeholder:"",name:"chlorides"},null,8,["modelValue"])]),_:1})]),t("div",sc,[t("label",lc,[e[143]||(e[143]=t("span",{class:"fs-6 fw-semibold text-break text-gray-800"},"Total Hardness (mg/L)",-1)),i(o,{placement:"top",width:400,trigger:"hover"},{reference:c(()=>e[141]||(e[141]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7 me-4"},null,-1)])),default:c(()=>[e[142]||(e[142]=t("span",null," Measurements of various ions in the drilling mud, which can affect mud chemistry and performance. ",-1))]),_:1})]),i(n,{prop:"totalHardness",class:"mt-auto"},{default:c(()=>[i(m,{type:"number",class:"w-100",controls:!1,step:"any",modelValue:s.targetData.totalHardness,"onUpdate:modelValue":e[31]||(e[31]=k=>s.targetData.totalHardness=k),placeholder:"",name:"totalHardness"},null,8,["modelValue"])]),_:1})]),t("div",oc,[t("label",nc,[e[146]||(e[146]=t("span",null,"Excess Lime (lb/bbl)",-1)),i(o,{placement:"top",width:400,trigger:"hover"},{reference:c(()=>e[144]||(e[144]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7"},null,-1)])),default:c(()=>[e[145]||(e[145]=t("span",null," The amount of excess lime added to the mud to control pH and alkalinity. ",-1))]),_:1})]),i(n,{prop:"excessLime",class:"mt-auto"},{default:c(()=>[i(m,{type:"number",class:"w-100",controls:!1,step:"any",modelValue:s.targetData.excessLime,"onUpdate:modelValue":e[32]||(e[32]=k=>s.targetData.excessLime=k),placeholder:"",name:"excessLime"},null,8,["modelValue"])]),_:1})]),t("div",ac,[t("label",ic,[e[149]||(e[149]=t("span",null,"K+ (mg/L)",-1)),i(o,{placement:"top",width:400,trigger:"hover"},{reference:c(()=>e[147]||(e[147]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7"},null,-1)])),default:c(()=>[e[148]||(e[148]=t("span",null," Measurement of potassium ion concentration in the mud. ",-1))]),_:1})]),i(n,{prop:"kPlus",class:"mt-auto"},{default:c(()=>[i(m,{type:"number",class:"w-100",controls:!1,step:"any",modelValue:s.targetData.kPlus,"onUpdate:modelValue":e[33]||(e[33]=k=>s.targetData.kPlus=k),placeholder:"",name:"kPlus"},null,8,["modelValue"])]),_:1})]),t("div",dc,[t("label",rc,[e[152]||(e[152]=t("span",null,"Make up water: Chlorides (mg/L)",-1)),i(o,{placement:"top",width:400,trigger:"hover"},{reference:c(()=>e[150]||(e[150]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7"},null,-1)])),default:c(()=>[e[151]||(e[151]=t("span",null," Chloride concentration in makeup water used to dilute or maintain the mud's properties. ",-1))]),_:1})]),i(n,{prop:"makeUpWater",class:"mt-auto"},{default:c(()=>[i(m,{type:"number",class:"w-100",controls:!1,step:"any",modelValue:s.targetData.makeUpWater,"onUpdate:modelValue":e[34]||(e[34]=k=>s.targetData.makeUpWater=k),placeholder:"",name:"makeUpWater"},null,8,["modelValue"])]),_:1})])]),t("div",uc,[t("div",cc,[t("label",fc,[e[155]||(e[155]=t("span",null,"Solids adjusted for salt (%)",-1)),i(o,{placement:"top",width:400,trigger:"hover"},{reference:c(()=>e[153]||(e[153]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7"},null,-1)])),default:c(()=>[e[154]||(e[154]=t("span",null," The percentage of solids adjusted for the presence of salt, which can affect the density calculations. ",-1))]),_:1})]),i(n,{prop:"solidsAdjustedForSalt",class:"mt-auto"},{default:c(()=>[i(m,{type:"number",class:"w-100",controls:!1,step:"any",modelValue:s.targetData.solidsAdjustedForSalt,"onUpdate:modelValue":e[35]||(e[35]=k=>s.targetData.solidsAdjustedForSalt=k),placeholder:"",name:"solidsAdjustedForSalt"},null,8,["modelValue"])]),_:1})]),t("div",mc,[t("label",pc,[e[158]||(e[158]=t("span",{class:"fs-6 fw-semibold text-break text-gray-800"},"Fine LCM (lb/bbl)",-1)),i(o,{placement:"top",width:400,trigger:"hover"},{reference:c(()=>e[156]||(e[156]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7 me-4"},null,-1)])),default:c(()=>[e[157]||(e[157]=t("span",null," The amount of fine LCM added to the mud to control lost circulation. ",-1))]),_:1})]),i(n,{prop:"fineLCM",class:"mt-auto"},{default:c(()=>[i(m,{type:"number",class:"w-100",controls:!1,step:"any",modelValue:s.targetData.fineLCM,"onUpdate:modelValue":e[36]||(e[36]=k=>s.targetData.fineLCM=k),placeholder:"",name:"fineLCM"},null,8,["modelValue"])]),_:1})]),t("div",gc,[t("label",bc,[e[161]||(e[161]=t("span",null,"Coarse LCM (lb/bbl)",-1)),i(o,{placement:"top",width:400,trigger:"hover"},{reference:c(()=>e[159]||(e[159]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7"},null,-1)])),default:c(()=>[e[160]||(e[160]=t("span",null," The amount of coarse LCM added to the mud for more severe lost circulation issues. ",-1))]),_:1})]),i(n,{prop:"coarseLCM",class:"mt-auto"},{default:c(()=>[i(m,{type:"number",class:"w-100",controls:!1,step:"any",modelValue:s.targetData.coarseLCM,"onUpdate:modelValue":e[37]||(e[37]=k=>s.targetData.coarseLCM=k),placeholder:"",name:"coarseLCM"},null,8,["modelValue"])]),_:1})])]),e[184]||(e[184]=t("div",{class:"row g-9 mb-3"},[t("div",{class:"col"},[t("h3",{class:"text-black"},"Rheological Properties")])],-1)),t("div",vc,[t("div",hc,[e[162]||(e[162]=t("label",{class:"d-flex align-items-center fs-6 fw-semibold mb-2 text-break"},[t("span",null,"Shear Rate: 600 (sec^-1) (rpm)")],-1)),i(n,{prop:"shearRate600",class:"mt-auto"},{default:c(()=>[i(m,{type:"number",class:"w-100",controls:!1,step:"any",modelValue:s.targetData.shearRate600,"onUpdate:modelValue":e[38]||(e[38]=k=>s.targetData.shearRate600=k),placeholder:"",name:"shearRate600"},null,8,["modelValue"])]),_:1})]),t("div",yc,[e[163]||(e[163]=t("label",{class:"d-flex align-items-center fs-6 fw-semibold mb-2 text-break"},[t("span",{class:"fs-6 fw-semibold text-break text-gray-800"},"Shear Rate: 300 (sec^-1) (rpm)")],-1)),i(n,{prop:"shearRate300",class:"mt-auto"},{default:c(()=>[i(m,{type:"number",class:"w-100",controls:!1,step:"any",modelValue:s.targetData.shearRate300,"onUpdate:modelValue":e[39]||(e[39]=k=>s.targetData.shearRate300=k),placeholder:"",name:"shearRate300"},null,8,["modelValue"])]),_:1})]),t("div",wc,[e[164]||(e[164]=t("label",{class:"d-flex align-items-center fs-6 fw-semibold mb-2 text-break"},[t("span",null,"Shear Rate: 200 (sec^-1) (rpm)")],-1)),i(n,{prop:"shearRate200",class:"mt-auto"},{default:c(()=>[i(m,{type:"number",class:"w-100",controls:!1,step:"any",modelValue:s.targetData.shearRate200,"onUpdate:modelValue":e[40]||(e[40]=k=>s.targetData.shearRate200=k),placeholder:"",name:"shearRate200"},null,8,["modelValue"])]),_:1})])]),t("div",$c,[t("div",Sc,[e[165]||(e[165]=t("label",{class:"d-flex align-items-center fs-6 fw-semibold mb-2 text-break"},[t("span",null,"Shear Rate: 100 (sec^-1) (rpm)")],-1)),i(n,{prop:"shearRate100",class:"mt-auto"},{default:c(()=>[i(m,{type:"number",class:"w-100",controls:!1,step:"any",modelValue:s.targetData.shearRate100,"onUpdate:modelValue":e[41]||(e[41]=k=>s.targetData.shearRate100=k),placeholder:"",name:"shearRate100"},null,8,["modelValue"])]),_:1})]),t("div",_c,[e[166]||(e[166]=t("label",{class:"d-flex align-items-center fs-6 fw-semibold mb-2 text-break"},[t("span",{class:"fs-6 fw-semibold text-break text-gray-800"},"Shear Rate: 6 (sec^-1) (rpm)")],-1)),i(n,{prop:"shearRate6",class:"mt-auto"},{default:c(()=>[i(m,{type:"number",class:"w-100",controls:!1,step:"any",modelValue:s.targetData.shearRate6,"onUpdate:modelValue":e[42]||(e[42]=k=>s.targetData.shearRate6=k),placeholder:"",name:"shearRate6"},null,8,["modelValue"])]),_:1})]),t("div",kc,[e[167]||(e[167]=t("label",{class:"d-flex align-items-center fs-6 fw-semibold mb-2 text-break"},[t("span",null,"Shear Rate: 3 (sec^-1) (rpm)")],-1)),i(n,{prop:"shearRate3",class:"mt-auto"},{default:c(()=>[i(m,{type:"number",class:"w-100",controls:!1,step:"any",modelValue:s.targetData.shearRate3,"onUpdate:modelValue":e[43]||(e[43]=k=>s.targetData.shearRate3=k),placeholder:"",name:"shearRate3"},null,8,["modelValue"])]),_:1})])]),t("div",Dc,[t("div",Ic,[t("label",Vc,[e[170]||(e[170]=t("span",null,"Plastic Viscosity (PV) (cP)",-1)),i(o,{placement:"top",width:400,trigger:"hover"},{reference:c(()=>e[168]||(e[168]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7"},null,-1)])),default:c(()=>[e[169]||(e[169]=t("span",null," Plastic Viscosity is a measure of the resistance to flow of the drilling mud. It is typically measured in centipoise (cP). ",-1))]),_:1})]),i(n,{prop:"plasticViscosity",class:"mt-auto"},{default:c(()=>[i(m,{type:"number",class:"w-100",controls:!1,step:"any",modelValue:s.targetData.plasticViscosity,"onUpdate:modelValue":e[44]||(e[44]=k=>s.targetData.plasticViscosity=k),placeholder:"",name:"plasticViscosity"},null,8,["modelValue"])]),_:1})]),t("div",Pc,[t("label",Cc,[e[173]||(e[173]=t("span",{class:"fs-6 fw-semibold text-break text-gray-800"},"Yield Point (YP) (lbf/100ft2)",-1)),i(o,{placement:"top",width:400,trigger:"hover"},{reference:c(()=>e[171]||(e[171]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7 me-4"},null,-1)])),default:c(()=>[e[172]||(e[172]=t("span",null," Yield Point is the amount of force required to initiate mud flow. It's typically measured in pounds per 100 square feet (lbf/100ft^2). ",-1))]),_:1})]),i(n,{prop:"yieldPoint",class:"mt-auto"},{default:c(()=>[i(m,{type:"number",class:"w-100",controls:!1,step:"any",modelValue:s.targetData.yieldPoint,"onUpdate:modelValue":e[45]||(e[45]=k=>s.targetData.yieldPoint=k),placeholder:"",name:"yieldPoint"},null,8,["modelValue"])]),_:1})]),t("div",Tc,[t("label",Fc,[e[176]||(e[176]=t("span",null,"Apparent Viscosity (AV) (cP)",-1)),i(o,{placement:"top",width:400,trigger:"hover"},{reference:c(()=>e[174]||(e[174]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7"},null,-1)])),default:c(()=>[e[175]||(e[175]=t("span",null," Apparent Viscosity (AV) (cP) is a measure of the overall resistance to flow of the drilling mud. It's the sum of the plastic viscosity and half of the yield point. ",-1))]),_:1})]),i(n,{prop:"apparentViscosity",class:"mt-auto"},{default:c(()=>[i(m,{type:"number",class:"w-100",controls:!1,step:"any",modelValue:s.targetData.apparentViscosity,"onUpdate:modelValue":e[46]||(e[46]=k=>s.targetData.apparentViscosity=k),placeholder:"AV (cP) = PV + YP / 2",name:"apparentViscosity",disabled:""},null,8,["modelValue"])]),_:1})])]),t("div",xc,[t("div",Nc,[t("label",Ac,[e[179]||(e[179]=t("span",null,"Shear Rate",-1)),i(o,{placement:"top",width:400,trigger:"hover"},{reference:c(()=>e[177]||(e[177]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7"},null,-1)])),default:c(()=>[e[178]||(e[178]=t("span",null," Shear rate is a measure of how fast the mud is flowing. ",-1))]),_:1})]),i(n,{prop:"shearRate",class:"mt-auto"},{default:c(()=>[i(m,{type:"number",class:"w-100",controls:!1,step:"any",modelValue:s.targetData.shearRate,"onUpdate:modelValue":e[47]||(e[47]=k=>s.targetData.shearRate=k),placeholder:"γ = (2 x Pump Speed (rpm)) / (Dial Reading (sec^-1))",name:"shearRate",disabled:""},null,8,["modelValue"])]),_:1})]),t("div",Hc,[t("label",qc,[e[182]||(e[182]=t("span",{class:"fs-6 fw-semibold text-break text-gray-800"},"Shear Stress τ (lbf/100ft²)",-1)),i(o,{placement:"top",width:400,trigger:"hover"},{reference:c(()=>e[180]||(e[180]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7 me-4"},null,-1)])),default:c(()=>[e[181]||(e[181]=t("span",null," Shear stress is the force applied per unit area on the mud. ",-1))]),_:1})]),i(n,{prop:"shearStress",class:"mt-auto"},{default:c(()=>[i(m,{type:"number",class:"w-100",controls:!1,step:"any",modelValue:s.targetData.shearStress,"onUpdate:modelValue":e[48]||(e[48]=k=>s.targetData.shearStress=k),placeholder:"τ (lbf/100ft²) = PV + (YP x Shear Rate)",name:"shearStress",disabled:""},null,8,["modelValue"])]),_:1})])]),t("div",Mc,[t("button",{type:"button",id:"kt_modal_add_customer_cancel",class:"btn text-gray-700 btn-sm me-3 btn-blue",onClick:e[49]||(e[49]=(...k)=>s.hide&&s.hide(...k)),disabled:s.loading}," Discard ",8,jc),t("button",{"data-kt-indicator":s.loading?"on":null,class:"btn btn-sm btn-primary",type:"submit",disabled:s.loading},[s.loading?U("",!0):($(),V("span",Ec," Save ")),s.loading?($(),V("span",Lc,e[183]||(e[183]=[L(" Please wait... "),t("span",{class:"spinner-border spinner-border-sm align-middle ms-2"},null,-1)]))):U("",!0)],8,zc)])]),_:1},8,["onSubmit","model","rules"])])]),_:1},8,["modelValue"])}const Bc=Q(Er,[["render",Rc]]),Oc=G({name:"view-sample-modal",components:{SvgIcon:X},emits:["editModal"],setup(s,{emit:e}){var w,h;const v=dt(),b=y(!1),f=y(""),r=y(),l=y(!1),o=y({shearRate:[],shearStress:[]}),a=y([{name:"Shear Stress",data:(w=o==null?void 0:o.value)==null?void 0:w.shearStress}]),d=y({chart:{type:"area",height:350,toolbar:{show:!1}},xaxis:{categories:(h=o==null?void 0:o.value)==null?void 0:h.shearRate,title:{text:"Shear Rate"},tooltip:{enabled:!1}},yaxis:{title:{text:"Shear Stress"}},dataLabels:{enabled:!1},stroke:{curve:"smooth"},tooltip:{x:{show:!1}}});J(f,_=>{_!==""&&(n(),u())});const n=async()=>{l.value=!0,v.getSampleDetails({id:f.value,callback:{onSuccess:_=>{r.value={..._}},onFinish:()=>{l.value=!1}}})},u=async()=>{v.getSampleChartInfo({id:f.value,callback:{onSuccess:_=>{var H,C,F,j,x,A,M;_!=null&&_.length&&_.sort((z,q)=>(z.shearRate??0)-(q.shearRate??0));for(let z=0;z<(_==null?void 0:_.length);z++)((H=_[z])==null?void 0:H.shearRate)>=3&&((j=(C=o==null?void 0:o.value)==null?void 0:C.shearRate)==null||j.push(Math.round(((F=_[z])==null?void 0:F.shearRate)*100)/100),(M=(x=o==null?void 0:o.value)==null?void 0:x.shearStress)==null||M.push(Math.round(((A=_[z])==null?void 0:A.shearStress)*100)/100));m()}}})},m=()=>{var _,H;a.value=[{name:"Shear Stress",data:(_=o==null?void 0:o.value)==null?void 0:_.shearStress}],d.value={...d.value,xaxis:{categories:(H=o==null?void 0:o.value)==null?void 0:H.shearRate,title:{text:"Shear Rate"}}}},p=()=>{b.value=!0},D=()=>{b.value=!1,N()},k=_=>{f.value=_.toString()},N=()=>{f.value="",r.value=null,o.value={shearRate:[],shearStress:[]},m()};return{id:f,modal:b,series:a,sampleData:r,fluidTypeOptions:lt,sampleFromOptions:st,chartOptions:d,show:p,hide:D,setId:k,reset:N,getOption:tt,formatTime:_t,clickEdit:()=>{e("editModal",f.value),D()},loading:l}}}),Uc={class:"d-flex align-items-center w-100"},Wc={key:0,class:"text-center"},Gc={key:1},Yc={class:"row"},Jc={class:"col-12 col-md-6 col-lg-4 gap-5 py-3"},Qc={class:"d-flex flex-column gap-5"},Kc={class:"field"},Xc={class:"field-value"},Zc={class:"field"},ef=["checked"],tf={class:"field"},sf={class:"field-value"},lf={class:"field"},of={class:"fw-bold ms-auto"},nf={class:"field"},af={class:"field-value"},df={class:"field"},rf={class:"field-value"},uf={class:"field"},cf={class:"field-value"},ff={class:"field"},mf={class:"field-value"},pf={class:"field"},gf={class:"field-value"},bf={class:"field"},vf={class:"field-value"},hf={class:"field"},yf={class:"field-value"},wf={class:"field"},$f={class:"field-value"},Sf={class:"field"},_f={class:"field-value"},kf={class:"field"},Df={class:"field-value"},If={class:"field"},Vf={class:"field-value"},Pf={class:"field"},Cf={class:"field-value"},Tf={class:"col-12 col-md-6 col-lg-4 gap-5 py-3"},Ff={class:"d-flex flex-column gap-5"},xf={class:"field"},Nf={class:"field-value"},Af={class:"field"},Hf={class:"field-value"},qf={class:"field"},Mf={class:"field-value"},jf={class:"field"},zf={class:"field-value"},Ef={class:"field"},Lf={class:"field-value"},Rf={class:"field"},Bf={class:"field-value"},Of={class:"field"},Uf={class:"field-value"},Wf={class:"field"},Gf={class:"field-value"},Yf={class:"field"},Jf={class:"field-value"},Qf={class:"field"},Kf={class:"field-value"},Xf={class:"field"},Zf={class:"field-value"},em={class:"field"},tm={class:"field-value"},sm={class:"field"},lm={class:"field-value"},om={class:"field"},nm={class:"field-value"},am={class:"field"},im={class:"field-value"},dm={class:"field"},rm={class:"field-value"},um={class:"col-12 col-md-6 col-lg-4 gap-5 py-3"},cm={class:"d-flex flex-column gap-5"},fm={class:"field"},mm={class:"field-value"},pm={class:"field"},gm={class:"field-value"},bm={class:"field"},vm={class:"field-value"},hm={class:"field"},ym={class:"field-value"},wm={class:"field"},$m={class:"field-value"},Sm={class:"field"},_m={class:"field-title"},km={class:"field-value"},Dm={class:"field"},Im={class:"field-title"},Vm={class:"field-value"},Pm={class:"field"},Cm={class:"field-title"},Tm={class:"field-value"},Fm={class:"field"},xm={class:"field-title"},Nm={class:"field-value"},Am={class:"field"},Hm={class:"field-title"},qm={class:"field-value"},Mm={class:"field"},jm={class:"field-title"},zm={class:"field-value"},Em={class:"field"},Lm={class:"field-title"},Rm={class:"field-value"},Bm={class:"field"},Om={class:"field-title"},Um={class:"field-value"},Wm={class:"field"},Gm={class:"field-title"},Ym={class:"field-value"},Jm={class:"field"},Qm={class:"field-title"},Km={class:"field-value"},Xm={class:"row"},Zm={class:"col-12 col-lg-4 gap-5 py-3"},ep={class:"d-flex flex-column gap-5"},tp={class:"field"},sp={class:"field-value"},lp={class:"field"},op={class:"field-value"},np={class:"field"},ap={class:"field-value"},ip={class:"field"},dp={class:"field-value"},rp={class:"field"},up={class:"field-value"},cp={class:"field"},fp={class:"field-value"},mp={class:"field"},pp={class:"field-value"},gp={class:"field"},bp={class:"field-value"},vp={class:"field"},hp={class:"field-title"},yp={class:"field-value"},wp={class:"field"},$p={class:"field-title"},Sp={class:"field-value"},_p={class:"field"},kp={class:"field-title"},Dp={class:"field-value"},Ip={class:"col-12 col-lg-8"},Vp={class:"modal-footer d-flex justify-content-center align-items-center gap-7"};function Pp(s,e,v,b,f,r){const l=S("SvgIcon"),o=S("el-form-item"),a=S("el-popover"),d=S("apexchart"),n=S("el-dialog");return $(),W(n,{modelValue:s.modal,"onUpdate:modelValue":e[3]||(e[3]=u=>s.modal=u),"show-close":!1,width:"1000","align-center":""},{header:c(()=>[t("div",Uc,[e[4]||(e[4]=t("h3",{class:"modal-title"},"View Sample",-1)),t("span",{class:"cursor-pointer ms-auto",onClick:e[0]||(e[0]=(...u)=>s.hide&&s.hide(...u))},[i(l,{icon:"closeModalIcon"})])])]),footer:c(()=>[t("div",Vp,[t("button",{type:"button",class:"btn text-gray-700 btn-sm btn-blue",onClick:e[1]||(e[1]=(...u)=>s.hide&&s.hide(...u))}," Close "),t("button",{class:"btn btn-sm btn-primary",type:"button",onClick:e[2]||(e[2]=(...u)=>s.clickEdit&&s.clickEdit(...u))}," Edit ")])]),default:c(()=>{var u,m,p,D,k,N,T,w,h,_,H,C,F,j,x,A,M,z,q,R,Y,se,oe,le,O,K,te,ue,he,ye,$e,Se,we,Ve,Pe,Ce,Te,Fe,xe,Ne,Ae,He,qe,Me,je,ze,Ee,Le,Re,Be,Oe,Ue,We,Ge,Ye,Je,Qe,Ke,Xe;return[t("div",null,[s.loading?($(),V("div",Wc,e[5]||(e[5]=[t("div",{class:"spinner-border text-primary",role:"status"},[t("span",{class:"sr-only"},"Loading...")],-1)]))):($(),V("div",Gc,[t("div",Yc,[t("div",Jc,[t("div",Qc,[t("div",Kc,[e[6]||(e[6]=t("span",{class:"field-title"},"Fluid Type: ",-1)),t("span",Xc,I(((m=s.getOption((u=s.sampleData)==null?void 0:u.fluidType,s.fluidTypeOptions))==null?void 0:m.label)||""),1)]),t("div",Zc,[e[7]||(e[7]=t("span",{class:"field-title"},"Weighted Mud",-1)),i(o,{prop:"weightedMud",class:"mt-sm-auto mb-0 mb-sm-auto ms-5 ms-sm-0"},{default:c(()=>{var Ze;return[t("input",{class:"form-check-input h-20px w-20px",type:"checkbox",checked:(Ze=s.sampleData)==null?void 0:Ze.weightedMud,disabled:""},null,8,ef)]}),_:1})]),t("div",tf,[e[8]||(e[8]=t("span",{class:"field-title"},"Sample From",-1)),t("span",sf,I(((D=s.getOption((p=s.sampleData)==null?void 0:p.sampleFrom,s.sampleFromOptions))==null?void 0:D.label)||""),1)]),t("div",lf,[e[9]||(e[9]=t("span",{class:"field-title"},"Time Sample Taken (hh:mm) ",-1)),t("span",of,I(s.formatTime((k=s.sampleData)==null?void 0:k.timeSampleTaken)||""),1)]),t("div",nf,[e[10]||(e[10]=t("span",{class:"field-title"},"Flowline Temperature (F)",-1)),t("span",af,I(((N=s.sampleData)==null?void 0:N.flowlineTemperature)||""),1)]),t("div",df,[e[11]||(e[11]=t("span",{class:"field-title"},"Measured Depth (ft) ",-1)),t("span",rf,I(((T=s.sampleData)==null?void 0:T.measuredDepth)||""),1)]),t("div",uf,[e[12]||(e[12]=t("span",{class:"field-title"},"MW (ppg or lbs/gal)",-1)),t("span",cf,I(((w=s.sampleData)==null?void 0:w.weightedMud)||""),1)]),t("div",ff,[e[13]||(e[13]=t("span",{class:"field-title"},"Funnel Viscosity (sec/qt) ",-1)),t("span",mf,I(((h=s.sampleData)==null?void 0:h.funnelViscosity)||""),1)]),t("div",pf,[e[14]||(e[14]=t("span",{class:"field-title"},"Temperature for Plastic Viscosity (f) ",-1)),t("span",gf,I(((_=s.sampleData)==null?void 0:_.temperatureForPlasticViscosity)||""),1)]),t("div",bf,[e[15]||(e[15]=t("span",{class:"field-title"},"PV (Plastic Viscosity) (cP) ",-1)),t("span",vf,I(((H=s.sampleData)==null?void 0:H.plasticViscosity)||""),1)]),t("div",hf,[e[16]||(e[16]=t("span",{class:"field-title"},"YP (Yield Point) (lbf/100ft2)",-1)),t("span",yf,I(((C=s.sampleData)==null?void 0:C.yieldPoint)||""),1)]),t("div",wf,[e[17]||(e[17]=t("span",{class:"field-title"},"Gel Str. 10s (lbf/100ft2) ",-1)),t("span",$f,I(((F=s.sampleData)==null?void 0:F.gelStrength10s)||""),1)]),t("div",Sf,[e[18]||(e[18]=t("span",{class:"field-title"},"Gel Str. 10m (lbf/100ft2) ",-1)),t("span",_f,I(((j=s.sampleData)==null?void 0:j.gelStrength10m)||""),1)]),t("div",kf,[e[19]||(e[19]=t("span",{class:"field-title"},"Gel Str. 30m (lbf/100ft2) ",-1)),t("span",Df,I(((x=s.sampleData)==null?void 0:x.gelStrength30m)||""),1)]),t("div",If,[e[20]||(e[20]=t("span",{class:"field-title"},"API filtrate (ml/30min) ",-1)),t("span",Vf,I(((A=s.sampleData)==null?void 0:A.apiFiltrate)||""),1)]),t("div",Pf,[e[21]||(e[21]=t("span",{class:"field-title"},"API cake thickness (1/32in)",-1)),t("span",Cf,I(((M=s.sampleData)==null?void 0:M.apiCakeThickness)||""),1)])])]),t("div",Tf,[t("div",Ff,[t("div",xf,[e[22]||(e[22]=t("span",{class:"field-title"},"Temperature for HTHP (F) ",-1)),t("span",Nf,I(((z=s.sampleData)==null?void 0:z.temperatureForHTHP)||""),1)]),t("div",Af,[e[23]||(e[23]=t("span",{class:"field-title"},"HTHP filtrate (ml/30min)",-1)),t("span",Hf,I(((q=s.sampleData)==null?void 0:q.hthpFiltrate)||""),1)]),t("div",qf,[e[24]||(e[24]=t("span",{class:"field-title"},"HTHP cake thickness (1/32in)",-1)),t("span",Mf,I(((R=s.sampleData)==null?void 0:R.hthpCakeThickness)||""),1)]),t("div",jf,[e[25]||(e[25]=t("span",{class:"field-title"},"Solids (%)",-1)),t("span",zf,I(((Y=s.sampleData)==null?void 0:Y.solids)||""),1)]),t("div",Ef,[e[26]||(e[26]=t("span",{class:"field-title"},"Oil (%)",-1)),t("span",Lf,I(((se=s.sampleData)==null?void 0:se.oil)||""),1)]),t("div",Rf,[e[27]||(e[27]=t("span",{class:"field-title"},"Water (%)",-1)),t("span",Bf,I(((oe=s.sampleData)==null?void 0:oe.water)||""),1)]),t("div",Of,[e[28]||(e[28]=t("span",{class:"field-title"},"Sand Content (%)",-1)),t("span",Uf,I(((le=s.sampleData)==null?void 0:le.sandContent)||""),1)]),t("div",Wf,[e[29]||(e[29]=t("span",{class:"field-title"},"MBT capacity (lb/bbl)",-1)),t("span",Gf,I(((O=s.sampleData)==null?void 0:O.mbtCapacity)||""),1)]),t("div",Yf,[e[30]||(e[30]=t("span",{class:"field-title"},"pH ",-1)),t("span",Jf,I(((K=s.sampleData)==null?void 0:K.pH)||""),1)]),t("div",Qf,[e[31]||(e[31]=t("span",{class:"field-title"},"Mud Alkalinity (Pm) (ml)",-1)),t("span",Kf,I(((te=s.sampleData)==null?void 0:te.mudAlkalinity)||""),1)]),t("div",Xf,[e[32]||(e[32]=t("span",{class:"field-title"},"Filtrate Alkalinity (Pf) (ml)",-1)),t("span",Zf,I(((ue=s.sampleData)==null?void 0:ue.filtrateAlkalinity)||""),1)]),t("div",em,[e[33]||(e[33]=t("span",{class:"field-title"},"Filtrate Alkalinity (Pf) (ml)",-1)),t("span",tm,I(((he=s.sampleData)==null?void 0:he.filtrateAlkalinity)||""),1)]),t("div",sm,[e[34]||(e[34]=t("span",{class:"field-title"},"Calcium (mg/L) ",-1)),t("span",lm,I(((ye=s.sampleData)==null?void 0:ye.calcium)||""),1)]),t("div",om,[e[35]||(e[35]=t("span",{class:"field-title"},"Chlorides (mg/L) ",-1)),t("span",nm,I((($e=s.sampleData)==null?void 0:$e.chlorides)||""),1)]),t("div",am,[e[36]||(e[36]=t("span",{class:"field-title"},"Total Hardness (mg/L)",-1)),t("span",im,I(((Se=s.sampleData)==null?void 0:Se.totalHardness)||""),1)]),t("div",dm,[e[37]||(e[37]=t("span",{class:"field-title"},"Excess Lime (lb/bbl)",-1)),t("span",rm,I(((we=s.sampleData)==null?void 0:we.excessLime)||""),1)])])]),t("div",um,[t("div",cm,[t("div",fm,[e[38]||(e[38]=t("span",{class:"field-title"},"K+ (mg/L)",-1)),t("span",mm,I(((Ve=s.sampleData)==null?void 0:Ve.kPlus)||""),1)]),t("div",pm,[e[39]||(e[39]=t("span",{class:"field-title"},"Make up water: Chlorides (mg/L)",-1)),t("span",gm,I(((Pe=s.sampleData)==null?void 0:Pe.makeUpWater)||""),1)]),t("div",bm,[e[40]||(e[40]=t("span",{class:"field-title"},"Solids adjusted for salt (%)",-1)),t("span",vm,I(((Ce=s.sampleData)==null?void 0:Ce.solidsAdjustedForSalt)||""),1)]),t("div",hm,[e[41]||(e[41]=t("span",{class:"field-title"},"Fine LCM (lb/bbl)",-1)),t("span",ym,I(((Te=s.sampleData)==null?void 0:Te.fineLCM)||""),1)]),t("div",wm,[e[42]||(e[42]=t("span",{class:"field-title"},"Coarse LCM (lb/bbl)",-1)),t("span",$m,I(((Fe=s.sampleData)==null?void 0:Fe.coarseLCM)||""),1)]),t("div",Sm,[t("span",_m,[e[45]||(e[45]=L("Linear Gel Strength (LGS) (%) ")),i(a,{placement:"top",width:300,trigger:"hover"},{reference:c(()=>e[43]||(e[43]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7"},null,-1)])),default:c(()=>[e[44]||(e[44]=t("span",null," LGS (%) = (Gel Strength (lbs/100 ft²) / Mud Weight (lbs/gal)) x 100 ",-1))]),_:1})]),t("span",km,I(((xe=s.sampleData)==null?void 0:xe.linearGelStrengthPercent)||""),1)]),t("div",Dm,[t("span",Im,[e[48]||(e[48]=L("Linear Gel Strength (LGS) (lb/bbl)")),i(a,{placement:"top",width:300,trigger:"hover"},{reference:c(()=>e[46]||(e[46]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7"},null,-1)])),default:c(()=>[e[47]||(e[47]=t("span",null," LGS (lb/bbl) = (Gel Strength (lbs/100 ft²) / (Mud Weight (lbs/gal) * 42)) x 100 ",-1))]),_:1})]),t("span",Vm,I(((Ne=s.sampleData)==null?void 0:Ne.linearGelStrengthLbBbl)||""),1)]),t("div",Pm,[t("span",Cm,[e[51]||(e[51]=L("High Gel Strength (HGS) (%)")),i(a,{placement:"top",width:300,trigger:"hover"},{reference:c(()=>e[49]||(e[49]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7"},null,-1)])),default:c(()=>[e[50]||(e[50]=t("span",null," HGS (%) = (Gel Str. 30m (lbf/100ft²) / MW (lb/gal)) x 100 ",-1))]),_:1})]),t("span",Tm,I(((Ae=s.sampleData)==null?void 0:Ae.highGelStrengthPercent)||""),1)]),t("div",Fm,[t("span",xm,[e[54]||(e[54]=L("High Gel Strength (HGS) (lb/bbl)")),i(a,{placement:"top",width:300,trigger:"hover"},{reference:c(()=>e[52]||(e[52]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7"},null,-1)])),default:c(()=>[e[53]||(e[53]=t("span",null," HGS (lb/bbl) = Gel Str. 30m (lbf/100ft²) / 100 ",-1))]),_:1})]),t("span",Nm,I(((He=s.sampleData)==null?void 0:He.highGelStrengthLbBbl)||""),1)]),t("div",Am,[t("span",Hm,[e[57]||(e[57]=L("Bentonite Concentration (%)")),i(a,{placement:"top",width:300,trigger:"hover"},{reference:c(()=>e[55]||(e[55]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7"},null,-1)])),default:c(()=>[e[56]||(e[56]=t("span",null," Bentonite (%) = (MBT capacity (lb/bbl) / MW (lb/gal)) x 100 ",-1))]),_:1})]),t("span",qm,I(((qe=s.sampleData)==null?void 0:qe.bentoniteConcentrationPercent)||""),1)]),t("div",Mm,[t("span",jm,[e[60]||(e[60]=L("Bentonite Concentration (lb/bbl)")),i(a,{placement:"top",width:300,trigger:"hover"},{reference:c(()=>e[58]||(e[58]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7"},null,-1)])),default:c(()=>[e[59]||(e[59]=t("span",null," Bentonite (lb/bbl) = MBT capacity (lb/bbl) ",-1))]),_:1})]),t("span",zm,I(((Me=s.sampleData)==null?void 0:Me.bentoniteConcentrationLbBbl)||""),1)]),t("div",Em,[t("span",Lm,[e[63]||(e[63]=L("Drill Solids Concentration (%)")),i(a,{placement:"top",width:300,trigger:"hover"},{reference:c(()=>e[61]||(e[61]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7"},null,-1)])),default:c(()=>[e[62]||(e[62]=t("span",null," Drill Solids (%) = Solids (%) - Bentonite (%) ",-1))]),_:1})]),t("span",Rm,I(((je=s.sampleData)==null?void 0:je.drillSolidsConcentrationPercent)||""),1)]),t("div",Bm,[t("span",Om,[e[66]||(e[66]=L("Drill Solids Concentration (lb/bbl)")),i(a,{placement:"top",width:300,trigger:"hover"},{reference:c(()=>e[64]||(e[64]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7"},null,-1)])),default:c(()=>[e[65]||(e[65]=t("span",null," Drill Solids (lb/bbl) = (Solids (%) - Bentonite (%)) x MW (lb/gal) / 100 ",-1))]),_:1})]),t("span",Um,I(((ze=s.sampleData)==null?void 0:ze.drillSolidsConcentrationLbBbl)||""),1)]),t("div",Wm,[t("span",Gm,[e[69]||(e[69]=L("DS/Bent Ratio (Drill Solids to Bentonite Ratio)")),i(a,{placement:"top",width:300,trigger:"hover"},{reference:c(()=>e[67]||(e[67]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7"},null,-1)])),default:c(()=>[e[68]||(e[68]=t("span",null," DS/Bent Ratio = (Drill Solids (lb/bbl) / Bentonite (lb/bbl)) ",-1))]),_:1})]),t("span",Ym,I(((Ee=s.sampleData)==null?void 0:Ee.drillSolidsToBentoniteRatio)||""),1)]),t("div",Jm,[t("span",Qm,[e[72]||(e[72]=L("Average Specific Gravity of Solids")),i(a,{placement:"top",width:300,trigger:"hover"},{reference:c(()=>e[70]||(e[70]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7"},null,-1)])),default:c(()=>[e[71]||(e[71]=t("span",null," Average SG of Solids = Σ(Specific Gravity of Each Solid x Volume Fraction of Each Solid) ",-1))]),_:1})]),t("span",Km,I(((Le=s.sampleData)==null?void 0:Le.averageSpecificGravityOfSolids)||""),1)])])])]),t("div",Xm,[t("div",Zm,[e[90]||(e[90]=t("h3",{class:"text-black mb-7"},"Rheological Properties",-1)),t("div",ep,[t("div",tp,[e[73]||(e[73]=t("span",{class:"field-title"},"Shear Rate: 600 (sec^-1) (rpm) ",-1)),t("span",sp,I(((Re=s.sampleData)==null?void 0:Re.shearRate600)||""),1)]),t("div",lp,[e[74]||(e[74]=t("span",{class:"field-title"},"Shear Rate: 300 (sec^-1) (rpm)",-1)),t("span",op,I(((Be=s.sampleData)==null?void 0:Be.shearRate300)||""),1)]),t("div",np,[e[75]||(e[75]=t("span",{class:"field-title"},"Shear Rate: 200 (sec^-1) (rpm)",-1)),t("span",ap,I(((Oe=s.sampleData)==null?void 0:Oe.shearRate200)||""),1)]),t("div",ip,[e[76]||(e[76]=t("span",{class:"field-title"},"Shear Rate: 100 (sec^-1) (rpm)",-1)),t("span",dp,I(((Ue=s.sampleData)==null?void 0:Ue.shearRate100)||""),1)]),t("div",rp,[e[77]||(e[77]=t("span",{class:"field-title"},"Shear Rate: 6 (sec^-1) (rpm)",-1)),t("span",up,I(((We=s.sampleData)==null?void 0:We.shearRate6)||""),1)]),t("div",cp,[e[78]||(e[78]=t("span",{class:"field-title"},"Shear Rate: 3 (sec^-1) (rpm)",-1)),t("span",fp,I(((Ge=s.sampleData)==null?void 0:Ge.shearRate3)||""),1)]),t("div",mp,[e[79]||(e[79]=t("span",{class:"field-title"},"Plastic Viscosity (PV) (cP)",-1)),t("span",pp,I(((Ye=s.sampleData)==null?void 0:Ye.plasticViscosity)||""),1)]),t("div",gp,[e[80]||(e[80]=t("span",{class:"field-title"},"Yield Point (YP) (lbf/100ft2)",-1)),t("span",bp,I(((Je=s.sampleData)==null?void 0:Je.yieldPoint)||""),1)]),t("div",vp,[t("span",hp,[e[83]||(e[83]=L("Apparent Viscosity (AV) (cP) ")),i(a,{placement:"top",width:300,trigger:"hover"},{reference:c(()=>e[81]||(e[81]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7"},null,-1)])),default:c(()=>[e[82]||(e[82]=t("span",null,[L(" Apparent Viscosity (AV) (cP) is a measure of the overall resistance to flow of the drilling mud. It's the sum of the plastic viscosity and half of the yield point."),t("br"),t("br"),L(" AV (cP) = PV + YP / 2 ")],-1))]),_:1})]),t("span",yp,I(((Qe=s.sampleData)==null?void 0:Qe.apparentViscosity)||""),1)]),t("div",wp,[t("span",$p,[e[86]||(e[86]=L("Shear Rate")),i(a,{placement:"top",width:300,trigger:"hover"},{reference:c(()=>e[84]||(e[84]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7"},null,-1)])),default:c(()=>[e[85]||(e[85]=t("span",null,[L(" Shear rate is a measure of how fast the mud is flowing. "),t("br"),t("br"),L(" γ = (2 x Pump Speed (rpm)) / (Dial Reading (sec^-1)) ")],-1))]),_:1})]),t("span",Sp,I(((Ke=s.sampleData)==null?void 0:Ke.shearRate)||""),1)]),t("div",_p,[t("span",kp,[e[89]||(e[89]=L("Shear Stress τ (lbf/100ft²)")),i(a,{placement:"top",width:300,trigger:"hover"},{reference:c(()=>e[87]||(e[87]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7"},null,-1)])),default:c(()=>[e[88]||(e[88]=t("span",null,[L(" Shear stress is the force applied per unit area on the mud. "),t("br"),t("br"),L(" τ (lbf/100ft²) = PV + (YP x Shear Rate) ")],-1))]),_:1})]),t("span",Dp,I(((Xe=s.sampleData)==null?void 0:Xe.shearStress)||""),1)])])]),t("div",Ip,[i(d,{type:"area",options:s.chartOptions,series:s.series},null,8,["options","series"])])])]))])]}),_:1},8,["modelValue"])}const Cp=Q(Oc,[["render",Pp]]),Tp=G({name:"sample",components:{SvgIcon:X,BottomTool:pe,SampleModal:Bc,EditTargetedPropertiesModal:zr,ViewSampleModal:Cp,NoEntries:be},setup(){var h,_;const s=re(),e=dt(),v=zt(),b=y(!1),f=y(),r=y(null),l=y(null),o=y(null),a=y([]),d=(_=(h=s.params)==null?void 0:h.id)==null?void 0:_.toString(),n=ee("dailyReport");ie(()=>{n!=null&&n.getDailyReportId()&&m(),u()});const u=async()=>{b.value=!0,v.getTargetPropertyDetails({wellId:d,callback:{onSuccess:H=>{f.value=H},onFinish:H=>{b.value=!1}}})},m=async(H={dailyReportId:n==null?void 0:n.getDailyReportId(),page:1,limit:200})=>{b.value=!0,e.getSamples({params:H,callback:{onSuccess:C=>{a.value=C==null?void 0:C.items},onFinish:C=>{b.value=!1}}})},p=H=>{var C,F;(C=l==null?void 0:l.value)==null||C.setId(H),(F=l==null?void 0:l.value)==null||F.show()},D=()=>{var H;(H=r==null?void 0:r.value)==null||H.show()},k=H=>{var C,F;(C=r==null?void 0:r.value)==null||C.setId(H),(F=r==null?void 0:r.value)==null||F.show()},N=()=>{var H;(H=o==null?void 0:o.value)==null||H.show()},T=H=>{ne.deletionAlert({onConfirmed:()=>{w(H)}})},w=async H=>{b.value=!0,e.deleteSample({id:H,callback:{onSuccess:C=>{m()},onFinish:C=>{b.value=!1}}})};return{wellId:d,sampleList:a,loading:b,sampleModal:r,targetPropertyData:f,fluidTypeOptions:lt,viewSampleModal:l,sampleFromOptions:st,editTargetedPropertiesModal:o,getOption:tt,getSamples:m,formatTime:_t,deleteSample:T,numberWithCommas:ge,getTargetProperty:u,toggleNewSampleModal:D,toggleEditSampleModal:k,toggleViewSampleModal:p,toggleEditTargetedPropertiesModal:N}}}),Fp={class:"d-flex flex-wrap flex-lg-nowrap w-100 sample-tab"},xp={class:"py-4 min-w-300px w-100 w-lg-auto"},Np={class:"bg-light-primary rounded border-primary border border-dashed px-5 pb-7 px-xl-7 pb-xl-9 pt-16 d-flex flex-column gap-3 font-roboto position-relative"},Ap={class:"svg-icon svg-icon-3"},Hp={class:"d-flex flex-column gap-3 text-gray-800"},qp={class:"d-flex flex-wrap align-items-center justify-content-between"},Mp={class:"fw-300 ms-auto"},jp={class:"d-flex flex-wrap align-items-center justify-content-between"},zp={class:"fw-300 ms-auto"},Ep={class:"d-flex flex-wrap align-items-center justify-content-between"},Lp={class:"fw-300 ms-auto"},Rp={class:"d-flex flex-wrap align-items-center justify-content-between"},Bp={class:"fw-300 ms-auto"},Op={class:"d-flex flex-wrap align-items-center justify-content-between"},Up={class:"fw-300 ms-auto"},Wp={class:"d-flex flex-wrap align-items-center justify-content-between"},Gp={class:"fw-300 ms-auto"},Yp={class:"d-flex flex-wrap align-items-center justify-content-between"},Jp={class:"fw-300 ms-auto"},Qp={class:"d-flex flex-wrap align-items-center justify-content-between"},Kp={class:"fw-300 ms-auto"},Xp={class:"d-flex flex-wrap align-items-center justify-content-between"},Zp={class:"fw-300 ms-auto"},eg={class:"d-flex flex-wrap align-items-center justify-content-between"},tg={class:"fw-300 ms-auto"},sg={class:"d-flex flex-wrap align-items-center justify-content-between"},lg={class:"fw-300 ms-auto"},og={class:"d-flex flex-wrap align-items-center justify-content-between"},ng={class:"fw-300 ms-auto"},ag={class:"d-flex flex-wrap align-items-center justify-content-between"},ig={class:"fw-300 ms-auto"},dg={class:"d-flex flex-wrap align-items-center justify-content-between"},rg={class:"fw-300 ms-auto"},ug={class:"d-flex flex-wrap align-items-center justify-content-between"},cg={class:"fw-300 ms-auto"},fg={class:"py-4 px-10 w-100"},mg={class:"row gap-10"},pg={key:0,class:"text-center"},gg=["onClick"],bg={class:"mb-0 text-primary"},vg={class:"d-flex flex-column gap-3"},hg={class:"d-flex flex-wrap align-items-center justify-content-between border-bottom-dashed border-bottom-1 border-blue-light pb-3"},yg={class:"fw-700 fs-7 text-success"},wg={class:"d-flex flex-wrap align-items-center justify-content-between border-bottom-dashed border-bottom-1 border-blue-light pb-3"},$g={class:"fw-700 fs-7 text-success"},Sg={class:"d-flex flex-wrap align-items-center justify-content-between"},_g={class:"fw-700 fs-7 text-dark"},kg={class:"d-flex justify-content-center"},Dg={class:"d-flex flex-wrap gap-3 align-items-center"},Ig={class:"border border-gray-300 border-dashed rounded py-3 px-4"},Vg={class:"fw-700 fs-6 text-gray-700"},Pg={class:"border border-gray-300 border-dashed rounded py-3 px-4"},Cg={class:"fw-700 fs-6 text-gray-700"},Tg={class:"d-flex align-items-center gap-2 position-absolute top-0 end-0 transform-top-50"},Fg=["onClick"],xg={class:"svg-icon svg-icon-3"},Ng=["onClick"],Ag={class:"svg-icon svg-icon-3 text-danger"};function Hg(s,e,v,b,f,r){var p,D,k,N,T,w,h,_,H,C,F,j,x,A,M,z;const l=S("SvgIcon"),o=S("el-tooltip"),a=S("NoEntries"),d=S("BottomTool"),n=S("SampleModal"),u=S("EditTargetedPropertiesModal"),m=S("ViewSampleModal");return $(),V(B,null,[t("div",Fp,[t("div",xp,[t("div",Np,[i(o,{content:"Edit",placement:"top",effect:"customize"},{default:c(()=>[t("div",{class:"btn rounded-circle btn-icon btn-action btn-sm bg-light btn-edit position-absolute top-0 end-0 m-3",onClick:e[0]||(e[0]=(...q)=>s.toggleEditTargetedPropertiesModal&&s.toggleEditTargetedPropertiesModal(...q))},[t("span",Ap,[i(l,{icon:"pencilIcon"})])])]),_:1}),t("div",Hp,[e[16]||(e[16]=t("h5",{class:"mb-0 pb-3"},"Targeted Properties",-1)),t("div",qp,[e[1]||(e[1]=t("span",{class:"fw-bold me-5"},"Fluid Type: ",-1)),t("span",Mp,I(((D=s.getOption((p=s.targetPropertyData)==null?void 0:p.fluidType,s.fluidTypeOptions))==null?void 0:D.label)||""),1)]),t("div",jp,[e[2]||(e[2]=t("span",{class:"fw-bold me-5"},"MW (ppg or lbs/gal):",-1)),t("span",zp,I((k=s.targetPropertyData)==null?void 0:k.mudWeight),1)]),t("div",Ep,[e[3]||(e[3]=t("span",{class:"fw-bold me-5"},"Funnel Viscosity (sec/qt):",-1)),t("span",Lp,I((N=s.targetPropertyData)==null?void 0:N.funnelViscosity),1)]),t("div",Rp,[e[4]||(e[4]=t("span",{class:"fw-bold me-5"},"PV (Plastic Viscosity) (cP):",-1)),t("span",Bp,I((T=s.targetPropertyData)==null?void 0:T.plasticViscosity),1)]),t("div",Op,[e[5]||(e[5]=t("span",{class:"fw-bold me-5"},"YP (Yield Point) (lbf/100ft2): ",-1)),t("span",Up,I((w=s.targetPropertyData)==null?void 0:w.yieldPoint),1)]),t("div",Wp,[e[6]||(e[6]=t("span",{class:"fw-bold me-5"},"6 rpm:",-1)),t("span",Gp,I((h=s.targetPropertyData)==null?void 0:h.rpm),1)]),t("div",Yp,[e[7]||(e[7]=t("span",{class:"fw-bold me-5"},"API filtrate (ml/30min):",-1)),t("span",Jp,I((_=s.targetPropertyData)==null?void 0:_.apiFiltrate),1)]),t("div",Qp,[e[8]||(e[8]=t("span",{class:"fw-bold me-5"},"API Cake: ",-1)),t("span",Kp,I((H=s.targetPropertyData)==null?void 0:H.apiCakeThickness),1)]),t("div",Xp,[e[9]||(e[9]=t("span",{class:"fw-bold me-5"},"pH:",-1)),t("span",Zp,I((C=s.targetPropertyData)==null?void 0:C.pH),1)]),t("div",eg,[e[10]||(e[10]=t("span",{class:"fw-bold me-5"},"Mud Alkalinity (Pm) (ml): ",-1)),t("span",tg,I((F=s.targetPropertyData)==null?void 0:F.mudAlkalinity),1)]),t("div",sg,[e[11]||(e[11]=t("span",{class:"fw-bold me-5"},"Filtrate Alkalinity (Pf) (ml):",-1)),t("span",lg,I((j=s.targetPropertyData)==null?void 0:j.filtrateAlkalinity),1)]),t("div",og,[e[12]||(e[12]=t("span",{class:"fw-bold me-5"},"Filtrate Alkalinity (Mf) (ml):",-1)),t("span",ng,I((x=s.targetPropertyData)==null?void 0:x.filtrateAlkalinity),1)]),t("div",ag,[e[13]||(e[13]=t("span",{class:"fw-bold me-5"},"Chlorides (mg/L):",-1)),t("span",ig,I((A=s.targetPropertyData)==null?void 0:A.chlorides),1)]),t("div",dg,[e[14]||(e[14]=t("span",{class:"fw-bold me-5"},"Total Hardness (mg/L):",-1)),t("span",rg,I((M=s.targetPropertyData)==null?void 0:M.totalHardness),1)]),t("div",ug,[e[15]||(e[15]=t("span",{class:"fw-bold me-5"},"Linear Gel Strength (LGS) (%):",-1)),t("span",cg,I((z=s.targetPropertyData)==null?void 0:z.linearGelStrengthPercent),1)])])])]),t("div",fg,[t("div",mg,[s.loading?($(),V("div",pg,e[17]||(e[17]=[t("div",{class:"spinner-border text-primary",role:"status"},[t("span",{class:"sr-only"},"Loading...")],-1)]))):($(),V(B,{key:1},[s.sampleList.length===0?($(),W(a,{key:0,addNew:s.toggleNewSampleModal},null,8,["addNew"])):($(!0),V(B,{key:1},Z(s.sampleList,q=>{var R,Y;return $(),V("div",{key:q==null?void 0:q.id,class:"col-4 card rounded-3 shadow-sm border-top border-top-2 border-primary position-relative card-item"},[t("div",{class:"card-body gap-5 d-flex flex-column",onClick:se=>s.toggleViewSampleModal(q==null?void 0:q.id.toString())},[t("h5",bg,I(q==null?void 0:q.name),1),t("div",vg,[t("div",hg,[e[18]||(e[18]=t("span",{class:"fw-semibold fs-7 text-gray-700"},"Sample From",-1)),t("span",yg,I(((R=s.getOption(q==null?void 0:q.sampleFrom,s.sampleFromOptions))==null?void 0:R.label)||""),1)]),t("div",wg,[e[19]||(e[19]=t("span",{class:"fw-semibold fs-7 text-gray-700"},"Type",-1)),t("span",$g,I((Y=s.getOption(q==null?void 0:q.fluidType,s.fluidTypeOptions))==null?void 0:Y.label),1)]),t("div",Sg,[e[20]||(e[20]=t("span",{class:"fw-semibold fs-7 text-gray-700"},"Time Sample Taken",-1)),t("span",_g,I(s.formatTime(q==null?void 0:q.timeSampleTaken)),1)])]),t("div",kg,[t("div",Dg,[t("div",Ig,[t("div",Vg,I(`${s.numberWithCommas(q==null?void 0:q.mudWeight)} (ppg)`),1),e[21]||(e[21]=t("div",{class:"fw-semibold fs-6 text-danger"},"MW",-1))]),t("div",Pg,[t("div",Cg,I(`${s.numberWithCommas(q==null?void 0:q.measuredDepth)} (in)`),1),e[22]||(e[22]=t("div",{class:"fw-semibold fs-6 text-primary"},"Depth",-1))])])])],8,gg),t("div",Tg,[t("button",{class:"btn rounded-circle btn-icon btn-sm btn-action bg-light btn-edit",onClick:se=>s.toggleEditSampleModal(q==null?void 0:q.id.toString())},[t("span",xg,[i(l,{icon:"pencilIcon"})])],8,Fg),t("button",{class:"btn rounded-circle btn-icon btn-sm btn-action bg-light btn-delete",onClick:se=>s.deleteSample(q==null?void 0:q.id.toString())},[t("span",Ag,[i(l,{icon:"trashIcon"})])],8,Ng)])])}),128))],64))])])]),i(d,{addNew:s.toggleNewSampleModal,showHelpInfo:!1},null,8,["addNew"]),i(n,{ref:"sampleModal",loadPage:s.getSamples},null,8,["loadPage"]),i(u,{ref:"editTargetedPropertiesModal",wellId:s.wellId,loadPage:s.getTargetProperty},null,8,["wellId","loadPage"]),i(m,{ref:"viewSampleModal",onEditModal:s.toggleEditSampleModal},null,8,["onEditModal"])],64)}const Et=Q(Tp,[["render",Hg]]),qg=ae("solid",()=>({getSolidDetails:async({dailyReportId:f,callback:r})=>{var a;const l=g.get(r,"onSuccess",g.noop),o=g.get(r,"onFinish",g.noop);try{P.setHeader();const d=await P.get(`solids/${f}`);l(((a=d.data)==null?void 0:a.data)||d.data)}catch(d){E(d,r)}finally{o()}},updateSolid:async({id:f,params:r,callback:l})=>{var d;const o=g.get(l,"onSuccess",g.noop),a=g.get(l,"onFinish",g.noop);try{P.setHeader();const n=await P.put(`solids/${f}`,r);o(((d=n.data)==null?void 0:d.data)||n.data)}catch(n){E(n,l)}finally{a()}},deleteSolid:async({id:f,callback:r})=>{var a;const l=g.get(r,"onSuccess",g.noop),o=g.get(r,"onFinish",g.noop);try{P.setHeader();const d=await P.delete(`solids/${f}`);l(((a=d.data)==null?void 0:a.data)||d.data)}catch(d){E(d,r)}finally{o()}},createSolid:async({params:f,callback:r})=>{var a;const l=g.get(r,"onSuccess",g.noop),o=g.get(r,"onFinish",g.noop);try{P.setHeader();const d=await P.post("solids",f);l(((a=d.data)==null?void 0:a.data)||d.data)}catch(d){E(d,r)}finally{o()}}})),Mg=G({name:"solid",components:{SvgIcon:X,BottomTool:pe},setup(){const s=re(),e=ee("dailyReport"),v=qg(),b=y(null),f=y({shaleCEC:null,bentCEC:null,highGelStrength:null,linearGelStrength:null}),r=y(!1),l=y(!1),o=y({...f.value}),a=y({});yt((N,T,w)=>{Dt(N,T,w,l.value||m())}),ie(()=>{d()});const d=async()=>{e!=null&&e.getDailyReportId()&&(r.value=!0,v.getSolidDetails({dailyReportId:e==null?void 0:e.getDailyReportId(),callback:{onSuccess:N=>{N&&(o.value={...N},f.value={...N})},onFinish:N=>{r.value=!1}}}))},n=async N=>{var T;l.value=!0,v.updateSolid({id:((T=o==null?void 0:o.value)==null?void 0:T.id)||"",params:N,callback:{onSuccess:w=>{d()},onFinish:()=>{l.value=!1}}})},u=async N=>{v.createSolid({params:N,callback:{onSuccess:T=>{d()},onFinish:()=>{l.value=!1}}})},m=()=>!wt.isEqual(o.value,f.value);return{cancel:()=>{ne.incompleteFormAlert({onConfirmed:()=>{o.value=JSON.parse(JSON.stringify(f.value))}},"Cancel changes on this section?","Yes")},submit:()=>{b.value&&b.value.validate(N=>{var T,w,h,_,H,C;if(N){if(l.value||!m())return;const F={shaleCEC:Number((T=o==null?void 0:o.value)==null?void 0:T.shaleCEC),bentCEC:Number((w=o==null?void 0:o.value)==null?void 0:w.bentCEC),highGelStrength:Number((h=o==null?void 0:o.value)==null?void 0:h.highGelStrength),linearGelStrength:Number((_=o==null?void 0:o.value)==null?void 0:_.linearGelStrength)};(H=o==null?void 0:o.value)!=null&&H.id?n({...F,dailyReportId:e==null?void 0:e.getDailyReportId()}):(l.value=!0,e==null||e.createDailyReport({wellId:(C=s==null?void 0:s.params)==null?void 0:C.id,callback:{onSuccess:j=>{u({...F,dailyReportId:e==null?void 0:e.getDailyReportId()})},onFailure:()=>{l.value=!1}}}))}})},isFormDirty:m,isValidForm:async()=>{var T;return await((T=b==null?void 0:b.value)==null?void 0:T.validate(w=>w))},rules:a,loading:r,submitting:l,formRef:b,targetData:o}}}),jg={key:0,class:"text-center"},zg={key:1,class:"row g-9"},Eg={class:"col-12 d-flex align-items-center justify-content-end gap-3"},Lg=["disabled"],Rg=["disabled"],Bg={key:0,class:"indicator-label"},Og={key:1,class:"indicator-progress"},Ug={class:"col-md-6 fv-row d-flex flex-column justify-content-stretch"},Wg={class:"d-flex align-items-center fs-6 fw-semibold mb-2 text-break"},Gg={class:"col-md-6 fv-row d-flex flex-column justify-content-stretch"},Yg={class:"d-flex align-items-center fs-6 fw-semibold mb-2 text-break"},Jg={class:"col-md-6 fv-row d-flex flex-column justify-content-stretch"},Qg={class:"d-flex align-items-center fs-6 fw-semibold mb-2 text-break"},Kg={class:"col-md-6 fv-row d-flex flex-column justify-content-stretch"},Xg={class:"d-flex align-items-center fs-6 fw-semibold mb-2 text-break"};function Zg(s,e,v,b,f,r){const l=S("el-popover"),o=S("el-input"),a=S("el-form-item"),d=S("el-form"),n=S("BottomTool");return $(),V(B,null,[i(d,{onSubmit:de(s.submit,["prevent"]),model:s.targetData,rules:s.rules,ref:"formRef",class:"form new-report-form"},{default:c(()=>[s.loading?($(),V("div",jg,e[5]||(e[5]=[t("div",{class:"spinner-border text-primary",role:"status"},[t("span",{class:"sr-only"},"Loading...")],-1)]))):($(),V("div",zg,[t("div",Eg,[t("button",{type:"button",class:"btn text-gray-700 btn-sm btn-blue",onClick:e[0]||(e[0]=(...u)=>s.cancel&&s.cancel(...u)),disabled:s.isFormDirty()!==!0||s.submitting}," Cancel ",8,Lg),t("button",{class:"btn btn-success btn-sm fw-semibold",type:"submit",disabled:s.isFormDirty()!==!0||s.submitting},[s.submitting?U("",!0):($(),V("span",Bg," Save ")),s.submitting?($(),V("span",Og,e[6]||(e[6]=[L(" Please wait... "),t("span",{class:"spinner-border spinner-border-sm align-middle ms-2"},null,-1)]))):U("",!0)],8,Rg)]),t("div",Ug,[t("label",Wg,[e[9]||(e[9]=t("span",null,"Shale CEC (Cation Exchange Capacity) (mg/g)",-1)),i(l,{placement:"top",width:400,trigger:"hover"},{reference:c(()=>e[7]||(e[7]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7"},null,-1)])),default:c(()=>[e[8]||(e[8]=t("span",null," Shale CEC is a measure of the cation exchange capacity of shale particles present in the drilling mud. It quantifies the shale's ability to adsorb and exchange ions with the mud. High CEC can lead to mud instability and unwanted interactions. ",-1))]),_:1})]),i(a,{prop:"shaleCEC",class:"mt-auto"},{default:c(()=>[i(o,{type:"number",class:"w-100",controls:!1,step:"any",modelValue:s.targetData.shaleCEC,"onUpdate:modelValue":e[1]||(e[1]=u=>s.targetData.shaleCEC=u),placeholder:"",name:"shaleCEC"},null,8,["modelValue"])]),_:1})]),t("div",Gg,[t("label",Yg,[e[12]||(e[12]=t("span",null,"Bent CEC (Cation Exchange Capacity) (mg/g)",-1)),i(l,{placement:"top",width:400,trigger:"hover"},{reference:c(()=>e[10]||(e[10]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7"},null,-1)])),default:c(()=>[e[11]||(e[11]=t("span",null," Bentonite CEC is a measure of the cation exchange capacity of bentonite clay used as an additive in drilling mud. It assesses the clay's capacity to exchange ions with the mud. Bentonite is often added to enhance mud properties. ",-1))]),_:1})]),i(a,{prop:"bentCEC",class:"mt-auto"},{default:c(()=>[i(o,{type:"number",class:"w-100",controls:!1,step:"any",modelValue:s.targetData.bentCEC,"onUpdate:modelValue":e[2]||(e[2]=u=>s.targetData.bentCEC=u),placeholder:"",name:"bentCEC"},null,8,["modelValue"])]),_:1})]),t("div",Jg,[t("label",Qg,[e[15]||(e[15]=t("span",null,"HGS (High Gel Strength) Specific Gravity",-1)),i(l,{placement:"top",width:400,trigger:"hover"},{reference:c(()=>e[13]||(e[13]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7"},null,-1)])),default:c(()=>[e[14]||(e[14]=t("span",null," HGS Specific Gravity refers to the specific gravity (density) of the high gel strength component of the drilling mud. It assesses the density of the solid materials responsible for high gel strength. ",-1))]),_:1})]),i(a,{prop:"highGelStrength",class:"mt-auto"},{default:c(()=>[i(o,{type:"number",class:"w-100",controls:!1,step:"any",modelValue:s.targetData.highGelStrength,"onUpdate:modelValue":e[3]||(e[3]=u=>s.targetData.highGelStrength=u),placeholder:"",name:"highGelStrength"},null,8,["modelValue"])]),_:1})]),t("div",Kg,[t("label",Xg,[e[18]||(e[18]=t("span",null,"LGS (Linear Gel Strength) Specific Gravity",-1)),i(l,{placement:"top",width:400,trigger:"hover"},{reference:c(()=>e[16]||(e[16]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7"},null,-1)])),default:c(()=>[e[17]||(e[17]=t("span",null," LGS Specific Gravity refers to the specific gravity (density) of the linear gel strength component of the drilling mud. It assesses the density of the solid materials responsible for linear gel strength. ",-1))]),_:1})]),i(a,{prop:"linearGelStrength",class:"mt-auto"},{default:c(()=>[i(o,{type:"number",class:"w-100",controls:!1,step:"any",modelValue:s.targetData.linearGelStrength,"onUpdate:modelValue":e[4]||(e[4]=u=>s.targetData.linearGelStrength=u),placeholder:"",name:"linearGelStrength"},null,8,["modelValue"])]),_:1})])]))]),_:1},8,["onSubmit","model","rules"]),i(n,{showAddNew:!1})],64)}const Lt=Q(Mg,[["render",Zg]]),eb={[Ie.Samples]:Et,[Ie.Solids]:Lt},tb=G({name:"daily-report-mud-properties",components:{SvgIcon:X,Sample:Et,Solid:Lt},props:{setChildActiveTab:{type:Function,required:!1,default:()=>{}}},setup(s){const e=y(!1),v=y(Ie.Samples),b=y(null),f=ot(()=>eb[v.value]),r=a=>{const d=a.target;v.value=Number(d.getAttribute("data-tab-index")),s!=null&&s.setChildActiveTab&&s.setChildActiveTab(v.value)},l=async a=>{v.value!==Ie.Solids?r(a):o()?ne.incompleteFormAlert({onConfirmed:()=>{r(a)}},"You have unsaved changes. Are you sure you want to leave?"):r(a)},o=()=>{var a,d;return b!=null&&b.value&&((a=b==null?void 0:b.value)!=null&&a.isFormDirty)?(d=b==null?void 0:b.value)==null?void 0:d.isFormDirty():!1};return{mudPropertiesTabs:gs,EMudPropertiesTab:Ie,currentComponent:f,currentChildTab:b,tabIndex:v,loading:e,handleActiveTab:l,isFormOfChildTabDirty:o}}}),sb={class:"card h-100 my-8"},lb={class:"card-header"},ob={class:"d-flex align-items-stretch gap-2 gap-lg-3 xxl:mt-0"},nb={class:"nav nav-stretch nav-line-tabs border-0 daily-nav",role:"tablist",id:"kt_layout_builder_tabs",ref:"kt_layout_builder_tabs"},ab=["data-tab-index"],ib={class:"card-body"};function db(s,e,v,b,f,r){return $(),V("div",sb,[t("div",lb,[t("div",ob,[t("ul",nb,[($(!0),V(B,null,Z(s.mudPropertiesTabs,l=>($(),V("li",{class:"nav-item",key:l.value},[t("div",{class:_e(["nav-link py-4 cursor-pointer text-active-primary fw-semibold text-hover-primary fs-6",{active:s.tabIndex===l.value}]),onClick:e[0]||(e[0]=o=>s.handleActiveTab(o)),"data-tab-index":l.value,role:"tab"},I(l.label),11,ab)]))),128))],512)])]),t("div",ib,[($(),W(rt(s.currentComponent),{ref:"currentChildTab"},null,512))])])}const Rt=Q(tb,[["render",db],["__scopeId","data-v-f8be1a6e"]]),rb=G({name:"file-component",props:{file:{type:Object,required:!0,default:{}},deleteFile:{type:Function,required:!1,default:()=>{}},edit:{type:Boolean,required:!1,default:!1}},components:{SvgIcon:X},setup(s){return{deleteFile:b=>{s!=null&&s.deleteFile&&(s==null||s.deleteFile(b))},getImage:b=>b!=null&&b.includes("css")?"media/svg/files/css.svg":b!=null&&b.includes("pdf")?"media/svg/files/pdf.svg":b!=null&&b.includes("docx")?"media/svg/files/docx.svg":b!=null&&b.includes("doc")?"media/svg/files/doc.svg":b!=null&&b.includes("image")?"media/svg/files/image.svg":"media/svg/files/file.svg"}}}),ub={class:"card position-relative"},cb={class:"card-body d-flex align-items-center gap-2 cursor-pointer p-2"},fb={class:"symbol symbol-30px"},mb=["src"],pb={class:"text-start"},gb={class:"fs-5 fw-bold mb-2 text-gray-800 text-hover-primary mw-165 text-truncate"},bb={class:"fs-6 fw-semibold text-gray-400"},vb={class:"svg-icon svg-icon-9"};function hb(s,e,v,b,f,r){var o,a,d;const l=S("SvgIcon");return $(),V("div",ub,[t("div",cb,[t("div",fb,[t("img",{src:(o=s.file)==null?void 0:o.type,alt:""},null,8,mb)]),t("div",pb,[t("p",gb,I((a=s.file)==null?void 0:a.title),1),t("span",bb,I((d=s.file)==null?void 0:d.size),1)])]),s.edit?($(),V("button",{key:0,class:"btn rounded-circle btn-icon btn-cancel btn-action bg-light btn-view position-absolute top-0 start-0",type:"button",onClick:e[0]||(e[0]=n=>s.deleteFile(s.file))},[t("span",vb,[i(l,{icon:"cancelIcon"})])])):U("",!0)])}const yb=Q(rb,[["render",hb]]),Bt=ae("note",()=>({getNotes:async({params:r,callback:l})=>{var d;const o=g.get(l,"onSuccess",g.noop),a=g.get(l,"onFinish",g.noop);try{P.setHeader();const n=await P.getWithParams("notes",r);o(((d=n.data)==null?void 0:d.data)||n.data)}catch(n){E(n,l)}finally{a()}},getNoteDetails:async({id:r,callback:l})=>{var d;const o=g.get(l,"onSuccess",g.noop),a=g.get(l,"onFinish",g.noop);try{P.setHeader();const n=await P.get(`notes/${r}`);o(((d=n.data)==null?void 0:d.data)||n.data)}catch(n){E(n,l)}finally{a()}},updateNote:async({id:r,params:l,callback:o})=>{var n;const a=g.get(o,"onSuccess",g.noop),d=g.get(o,"onFinish",g.noop);try{P.setHeader();const u=await P.put(`notes/${r}`,l);a(((n=u.data)==null?void 0:n.data)||u.data)}catch(u){E(u,o)}finally{d()}},deleteNote:async({id:r,callback:l})=>{var d;const o=g.get(l,"onSuccess",g.noop),a=g.get(l,"onFinish",g.noop);try{P.setHeader();const n=await P.delete(`notes/${r}`);o(((d=n.data)==null?void 0:d.data)||n.data)}catch(n){E(n,l)}finally{a()}},createNote:async({params:r,callback:l})=>{var d;const o=g.get(l,"onSuccess",g.noop),a=g.get(l,"onFinish",g.noop);try{P.setHeader();const n=await P.post("notes",r);o(((d=n.data)==null?void 0:d.data)||n.data)}catch(n){E(n,l)}finally{a()}}})),wb=G({name:"notes-modal",components:{SvgIcon:X},props:{loadPage:{type:Function,default:()=>{},required:!0}},setup(s){const e=re(),v=Bt(),b=y(!1),f=y({title:"",notes:""}),r=y(null),l=y(null),o=y(!1),a=y(""),d=ee("dailyReport");J(a,C=>{C!==""&&n()}),J(b,C=>{var F;C===!1&&(a.value="",w(),(F=r==null?void 0:r.value)==null||F.resetFields())});const n=async()=>{v.getNoteDetails({id:a.value,callback:{onSuccess:C=>{var F;f.value={...C,costSettingId:(F=C==null?void 0:C.costSetting)==null?void 0:F.id}}}})},u=async C=>{o.value=!0,v.updateNote({id:a.value,params:C,callback:{onSuccess:F=>{s!=null&&s.loadPage&&(s==null||s.loadPage()),D()},onFinish:F=>{o.value=!1}}})},m=async C=>{o.value=!0,v.createNote({params:C,callback:{onSuccess:F=>{s!=null&&s.loadPage&&(s==null||s.loadPage()),D()},onFinish:F=>{o.value=!1}}})},p=()=>{b.value=!0},D=()=>{b.value=!1},k=C=>{a.value=C.toString()},N=y({title:[{required:!0,message:"Please select Title",trigger:"change"}],notes:[{required:!0,message:"Please type Notes",trigger:"blur"}]}),T=()=>{r.value&&r.value.validate(C=>{var F;C&&(a!=null&&a.value?u({...f==null?void 0:f.value,dailyReportId:d==null?void 0:d.getDailyReportId()}):d==null||d.createDailyReport({wellId:(F=e==null?void 0:e.params)==null?void 0:F.id,callback:{onSuccess:j=>{m({...f==null?void 0:f.value,dailyReportId:d==null?void 0:d.getDailyReportId()})}}}))})},w=()=>{f.value={title:"",notes:""}};return{id:a,modal:b,rules:N,loading:o,targetData:f,formRef:r,inputFile:l,show:p,hide:D,submit:T,setId:k,reset:w,deleteFile:C=>{var j,x,A;const F=(x=(j=f==null?void 0:f.value)==null?void 0:j.files)==null?void 0:x.indexOf(C);F!==-1&&((A=f==null?void 0:f.value.files)==null||A.splice(F,1))},addFile:()=>{var C;(C=l==null?void 0:l.value)==null||C.click()},handleFile:C=>{var F,j;((j=(F=C.target)==null?void 0:F.files)==null?void 0:j.length)>0}}}}),$b={class:"d-flex align-items-center w-100"},Sb={class:"modal-title"},_b={class:"row g-7 mb-3"},kb={class:"col-12 fv-row"},Db={class:"row g-7 mb-3"},Ib={class:"col-12 fv-row"},Vb={class:"row g-7 mb-3"},Pb={class:"col-12 fv-row"},Cb={class:"svg-icon svg-icon-3 text-danger"},Tb={class:"modal-footer d-flex justify-content-center align-items-center"},Fb=["disabled"],xb=["data-kt-indicator","disabled"],Nb={key:0,class:"indicator-label"},Ab={key:1,class:"indicator-progress"};function Hb(s,e,v,b,f,r){const l=S("SvgIcon"),o=S("el-input"),a=S("el-form-item"),d=S("el-form"),n=S("el-dialog");return $(),W(n,{modelValue:s.modal,"onUpdate:modelValue":e[6]||(e[6]=u=>s.modal=u),"show-close":!1,width:"500","align-center":""},{header:c(()=>[t("div",$b,[t("h3",Sb,I(`${s.id?"Edit Note":"New Note"}`),1),t("span",{class:"cursor-pointer ms-auto",onClick:e[0]||(e[0]=(...u)=>s.hide&&s.hide(...u))},[i(l,{icon:"closeModalIcon"})])])]),default:c(()=>[t("div",null,[i(d,{id:"cost_form",onSubmit:de(s.submit,["prevent"]),model:s.targetData,rules:s.rules,ref:"formRef",class:"form"},{default:c(()=>[t("div",_b,[t("div",kb,[e[7]||(e[7]=t("label",{class:"d-flex align-items-center fs-6 fw-semibold mb-2 text-break required"},"Title ",-1)),i(a,{prop:"title",class:"mt-auto"},{default:c(()=>[i(o,{class:"w-100",modelValue:s.targetData.title,"onUpdate:modelValue":e[1]||(e[1]=u=>s.targetData.title=u),placeholder:"",name:"title"},null,8,["modelValue"])]),_:1})])]),t("div",Db,[t("div",Ib,[e[8]||(e[8]=t("label",{class:"d-flex align-items-center fs-6 fw-semibold mb-2 required"},"Notes",-1)),i(a,{prop:"notes"},{default:c(()=>[i(o,{class:"w-100",modelValue:s.targetData.notes,"onUpdate:modelValue":e[2]||(e[2]=u=>s.targetData.notes=u),placeholder:"",name:"notes",type:"textarea",rows:"5"},null,8,["modelValue"])]),_:1})])]),t("div",Vb,[t("div",Pb,[t("button",{class:"btn btn-sm btn-blue text-primary",type:"button",onClick:e[4]||(e[4]=(...u)=>s.addFile&&s.addFile(...u))},[t("span",Cb,[i(l,{icon:"attachmentIcon"})]),t("input",{type:"file",ref:"inputFile",class:"d-none",onChange:e[3]||(e[3]=(...u)=>s.handleFile&&s.handleFile(...u)),multiple:""},null,544),e[9]||(e[9]=L(" Attachment "))])])]),e[11]||(e[11]=t("div",{class:"row g-7 mb-3"},[t("div",{class:"col-12 fv-row"},[t("div",{class:"d-flex flex-wrap gap-5 align-items-center justify-content-start"})])],-1)),t("div",Tb,[t("button",{type:"button",id:"kt_modal_add_customer_cancel",class:"btn text-gray-700 btn-sm me-3 btn-blue",onClick:e[5]||(e[5]=(...u)=>s.hide&&s.hide(...u)),disabled:s.loading}," Discard ",8,Fb),t("button",{"data-kt-indicator":s.loading?"on":null,class:"btn btn-sm btn-primary",type:"submit",disabled:s.loading},[s.loading?U("",!0):($(),V("span",Nb," Save ")),s.loading?($(),V("span",Ab,e[10]||(e[10]=[L(" Please wait... "),t("span",{class:"spinner-border spinner-border-sm align-middle ms-2"},null,-1)]))):U("",!0)],8,xb)])]),_:1},8,["onSubmit","model","rules"])])]),_:1},8,["modelValue"])}const qb=Q(wb,[["render",Hb]]),Mb=G({name:"notes",components:{SvgIcon:X,BottomTool:pe,NoteModal:qb,File:yb,NoEntries:be},props:{setChildActiveTab:{type:Function,required:!1,default:()=>{}}},setup(s){const e=Bt(),v=y(null),b=y(!1),f=y([]),r=ee("dailyReport");ie(()=>{r!=null&&r.getDailyReportId()&&l()});const l=async(u={dailyReportId:r==null?void 0:r.getDailyReportId(),page:1,limit:200})=>{b.value=!0,e.getNotes({params:u,callback:{onSuccess:m=>{f.value=JSON.parse(JSON.stringify(m==null?void 0:m.items))},onFinish:m=>{b.value=!1}}})},o=()=>{var u;(u=v==null?void 0:v.value)==null||u.show()},a=u=>{var m,p;(m=v==null?void 0:v.value)==null||m.setId(u),(p=v==null?void 0:v.value)==null||p.show()},d=u=>{ne.deletionAlert({onConfirmed:()=>{n(u)}})},n=u=>{b.value=!0,e.deleteNote({id:u,callback:{onSuccess:m=>{l()},onFinish:m=>{b.value=!1}}})};return{loading:b,noteList:f,noteModal:v,numberWithCommas:ge,toggleAddNoteModal:o,toggleEditNoteModal:a,deleteNote:d,formatDate:ke,getNotes:l}}}),jb={class:"card h-100 my-8 notes"},zb={class:"card-body"},Eb={key:0,class:"text-center"},Lb={key:1},Rb={key:1,class:"row gap-10"},Bb={class:"card-body gap-5 d-flex flex-column"},Ob={class:"mb-0 text-primary fs-4"},Ub={class:"overflow-hidden"},Wb={class:"mb-0 text-gray-800 fs-5 truncate-text"},Gb={class:"d-flex flex-wrap gap-5 align-items-center justify-content-start"},Yb={class:"text-end fw-bold text-gray-700 fst-italic fs-5"},Jb={class:"d-flex align-items-center gap-2 position-absolute top-0 end-0 transform-top-50"},Qb=["onClick"],Kb={class:"svg-icon svg-icon-3"},Xb=["onClick"],Zb={class:"svg-icon svg-icon-3 text-danger"};function e1(s,e,v,b,f,r){const l=S("NoEntries"),o=S("KTFile"),a=S("SvgIcon"),d=S("BottomTool"),n=S("NoteModal");return $(),V(B,null,[t("div",jb,[e[1]||(e[1]=t("div",{class:"card-header d-flex flex-wrap align-items-center"},[t("h1",{class:"mb-0 me-4 text-gray-900"},"Notes")],-1)),t("div",zb,[s.loading?($(),V("div",Eb,e[0]||(e[0]=[t("div",{class:"spinner-border text-primary",role:"status"},[t("span",{class:"sr-only"},"Loading...")],-1)]))):($(),V("div",Lb,[s.noteList.length===0?($(),W(l,{key:0,addNew:s.toggleAddNoteModal},null,8,["addNew"])):($(),V("div",Rb,[($(!0),V(B,null,Z(s.noteList,u=>($(),V("div",{key:u==null?void 0:u.id,class:"col-4 card rounded-3 shadow-sm border-top border-top-2 border-primary position-relative card-item"},[t("div",Bb,[t("h5",Ob,I(u==null?void 0:u.title),1),t("div",Ub,[t("p",Wb,I(u==null?void 0:u.notes),1)]),t("div",Gb,[($(!0),V(B,null,Z(u==null?void 0:u.files,(m,p)=>($(),W(o,{key:p,file:m},null,8,["file"]))),128))]),t("div",Yb,I(s.formatDate(u==null?void 0:u.createdAt,"DD MMM YYYY")),1)]),t("div",Jb,[t("button",{class:"btn rounded-circle btn-icon btn-sm btn-action bg-light btn-edit",onClick:m=>s.toggleEditNoteModal(u==null?void 0:u.id)},[t("span",Kb,[i(a,{icon:"pencilIcon"})])],8,Qb),t("button",{class:"btn rounded-circle btn-icon btn-sm btn-action bg-light btn-delete",onClick:m=>s.deleteNote(u==null?void 0:u.id)},[t("span",Zb,[i(a,{icon:"trashIcon"})])],8,Xb)])]))),128))]))]))])]),i(d,{addNew:s.toggleAddNoteModal,showHelpInfo:!1},null,8,["addNew"]),i(n,{ref:"noteModal",loadPage:s.getNotes},null,8,["loadPage"])],64)}const Ot=Q(Mb,[["render",e1]]);var ce=(s=>(s[s.initial=1]="initial",s[s.received=2]="received",s[s.used=3]="used",s[s.adjusted=4]="adjusted",s[s.returned=5]="returned",s))(ce||{});const Ut=[{value:1,label:"Initial"},{value:2,label:"Received"},{value:3,label:"Used"},{value:4,label:"Adjusted"},{value:5,label:"Returned"}],Wt=ae("productAndPackageInventory",()=>({deleteProductPackageInventory:async({id:v,callback:b})=>{var l;const f=g.get(b,"onSuccess",g.noop),r=g.get(b,"onFinish",g.noop);try{P.setHeader();const o=await P.delete(`productAndPackageInventories/${v}`);f(((l=o.data)==null?void 0:l.data)||o.data)}catch(o){E(o,b)}finally{r()}},getProductPackageInventories:async({dailyReportId:v,callback:b})=>{var l;const f=g.get(b,"onSuccess",g.noop),r=g.get(b,"onFinish",g.noop);try{P.setHeader();const o=await P.get(`productAndPackageInventories/${v}`);f(((l=o.data)==null?void 0:l.data)||o.data)}catch(o){E(o,b)}finally{r()}}})),Gt=ae("productPackageItem",()=>({getProductPackageItems:async({params:r,callback:l})=>{var d;const o=g.get(l,"onSuccess",g.noop),a=g.get(l,"onFinish",g.noop);try{P.setHeader();const n=await P.getWithParams("productAndPackageInventoryItems",r);o(((d=n.data)==null?void 0:d.data)||n.data)}catch(n){E(n,l)}finally{a()}},getProductPackageItemDetails:async({id:r,callback:l})=>{var d;const o=g.get(l,"onSuccess",g.noop),a=g.get(l,"onFinish",g.noop);try{P.setHeader();const n=await P.get(`productAndPackageInventoryItems/${r}`);o(((d=n.data)==null?void 0:d.data)||n.data)}catch(n){E(n,l)}finally{a()}},createProductPackageItem:async({params:r,callback:l})=>{var d;const o=g.get(l,"onSuccess",g.noop),a=g.get(l,"onFinish",g.noop);try{P.setHeader();const n=await P.post("productAndPackageInventoryItems",r);o(((d=n.data)==null?void 0:d.data)||n.data)}catch(n){E(n,l)}finally{a()}},updateProductPackageItem:async({id:r,params:l,callback:o})=>{var n;const a=g.get(o,"onSuccess",g.noop),d=g.get(o,"onFinish",g.noop);try{P.setHeader();const u=await P.put(`productAndPackageInventoryItems/${r}`,l);a(((n=u.data)==null?void 0:n.data)||u.data)}catch(u){E(u,o)}finally{d()}},deleteProductPackageItem:async({id:r,callback:l})=>{var d;const o=g.get(l,"onSuccess",g.noop),a=g.get(l,"onFinish",g.noop);try{P.setHeader();const n=await P.delete(`productAndPackageInventoryItems/${r}`);o(((d=n.data)==null?void 0:d.data)||n.data)}catch(n){E(n,l)}finally{a()}}})),ut=ae("productPackageReport",()=>({getProductPackageReports:async({params:f,callback:r})=>{var a;const l=g.get(r,"onSuccess",g.noop),o=g.get(r,"onFinish",g.noop);try{P.setHeader();const d=await P.getWithParams("productAndPackageInventoryReports",f);l(((a=d.data)==null?void 0:a.data)||d.data)}catch(d){E(d,r)}finally{o()}},getProductPackageReportDetails:async({id:f,callback:r})=>{var a;const l=g.get(r,"onSuccess",g.noop),o=g.get(r,"onFinish",g.noop);try{P.setHeader();const d=await P.get(`productAndPackageInventoryReports/${f}`);l(((a=d.data)==null?void 0:a.data)||d.data)}catch(d){E(d,r)}finally{o()}},createProductPackageReport:async({params:f,callback:r})=>{var a;const l=g.get(r,"onSuccess",g.noop),o=g.get(r,"onFinish",g.noop);try{P.setHeader();const d=await P.post("productAndPackageInventoryReports",f);l(((a=d.data)==null?void 0:a.data)||d.data)}catch(d){E(d,r)}finally{o()}},deleteProductPackageReport:async({id:f,callback:r})=>{var a;const l=g.get(r,"onSuccess",g.noop),o=g.get(r,"onFinish",g.noop);try{P.setHeader();const d=await P.delete(`productAndPackageInventoryReports/${f}`);l(((a=d.data)==null?void 0:a.data)||d.data)}catch(d){E(d,r)}finally{o()}}})),nt=ae("volumeTracking",()=>({getVolumeTrackings:async({params:r,callback:l})=>{var d;const o=g.get(l,"onSuccess",g.noop),a=g.get(l,"onFinish",g.noop);try{P.setHeader();const n=await P.getWithParams("volumeTrackings",r);o(((d=n.data)==null?void 0:d.data)||n.data)}catch(n){E(n,l)}finally{a()}},getVolumeTrackingDetails:async({id:r,callback:l})=>{var d;const o=g.get(l,"onSuccess",g.noop),a=g.get(l,"onFinish",g.noop);try{P.setHeader();const n=await P.get(`volumeTrackings/${r}`);o(((d=n.data)==null?void 0:d.data)||n.data)}catch(n){E(n,l)}finally{a()}},updateVolumeTracking:async({id:r,params:l,callback:o})=>{var n;const a=g.get(o,"onSuccess",g.noop),d=g.get(o,"onFinish",g.noop);try{P.setHeader();const u=await P.put(`volumeTrackings/${r}`,l);a(((n=u.data)==null?void 0:n.data)||u.data)}catch(u){E(u,o)}finally{d()}},deleteVolumeTracking:async({id:r,callback:l})=>{var d;const o=g.get(l,"onSuccess",g.noop),a=g.get(l,"onFinish",g.noop);try{P.setHeader();const n=await P.delete(`volumeTrackings/${r}`);o(((d=n.data)==null?void 0:d.data)||n.data)}catch(n){E(n,l)}finally{a()}},createVolumeTracking:async({params:r,callback:l})=>{var d;const o=g.get(l,"onSuccess",g.noop),a=g.get(l,"onFinish",g.noop);try{P.setHeader();const n=await P.post("volumeTrackings",r);o(((d=n.data)==null?void 0:d.data)||n.data)}catch(n){E(n,l)}finally{a()}}}));var me=(s=>(s[s.Addition=1]="Addition",s[s.Loss=2]="Loss",s[s.Transfer=3]="Transfer",s))(me||{});const t1=[{value:1,label:"Addition"},{value:2,label:"Loss"},{value:3,label:"Transfer"}],s1=[{value:1,label:"Active"},{value:2,label:"Inactive"}],l1=G({name:"storage-or-pit-modal",components:{SvgIcon:X},props:{loadPage:{type:Function,default:()=>{},required:!0}},setup(s){const e=re(),v=nt(),b=y(!1),f={name:"",storageType:"",status:null,measuredVolume:null,mudWeight:null,mudType:null},r=y(JSON.parse(JSON.stringify(f))),l=y(null),o=y(!1),a=y(null),d=y(""),n=ee("dailyReport");J(d,h=>{h!==""&&u()}),J(b,h=>{var _;h===!1&&(d.value="",w(),(_=l==null?void 0:l.value)==null||_.resetFields())});const u=async()=>{v.getVolumeTrackingDetails({id:d.value,callback:{onSuccess:h=>{r.value={...h}}}})},m=async h=>{o.value=!0,v.updateVolumeTracking({id:d.value,params:h,callback:{onSuccess:_=>{s!=null&&s.loadPage&&(s==null||s.loadPage()),k()},onFinish:_=>{o.value=!1}}})},p=async h=>{o.value=!0,v.createVolumeTracking({params:h,callback:{onSuccess:_=>{s!=null&&s.loadPage&&(s==null||s.loadPage()),k()},onFinish:_=>{o.value=!1}}})},D=()=>{b.value=!0},k=()=>{b.value=!1},N=h=>{d.value=h},T=()=>{l.value&&l.value.validate(h=>{var _,H,C,F,j;if(h){const x={...r==null?void 0:r.value,status:Number((_=r==null?void 0:r.value)==null?void 0:_.status),measuredVolume:Number((H=r==null?void 0:r.value)==null?void 0:H.measuredVolume),mudWeight:Number((C=r==null?void 0:r.value)==null?void 0:C.mudWeight),mudType:Number((F=r==null?void 0:r.value)==null?void 0:F.mudType)};d!=null&&d.value?m({...x,dailyReportId:n==null?void 0:n.getDailyReportId()}):n==null||n.createDailyReport({wellId:(j=e==null?void 0:e.params)==null?void 0:j.id,callback:{onSuccess:A=>{p({...x,dailyReportId:n==null?void 0:n.getDailyReportId()})}}})}})},w=()=>{r.value=JSON.parse(JSON.stringify(f))};return{id:d,type:a,modal:b,loading:o,targetData:r,volumeTrackingStatusOptions:s1,formRef:l,show:D,hide:k,submit:T,reset:w,setId:N}}}),o1={class:"d-flex align-items-center w-100"},n1={class:"modal-title"},a1={class:"row g-7 mb-3"},i1={class:"col-md-6 fv-row d-flex flex-column justify-content-stretch"},d1={class:"col-md-6 fv-row d-flex flex-column justify-content-stretch"},r1={class:"row g-7 mb-3"},u1={class:"col-md-6 fv-row d-flex flex-column justify-content-stretch"},c1={class:"col-md-6 fv-row d-flex flex-column justify-content-stretch"},f1={class:"row g-7 mb-3"},m1={class:"col-md-6 fv-row d-flex flex-column justify-content-stretch"},p1={class:"col-md-6 fv-row d-flex flex-column justify-content-stretch"},g1={class:"modal-footer d-flex justify-content-center align-items-center"},b1=["disabled"],v1=["data-kt-indicator","disabled"],h1={key:0,class:"indicator-label"},y1={key:1,class:"indicator-progress"};function w1(s,e,v,b,f,r){const l=S("SvgIcon"),o=S("el-input"),a=S("el-form-item"),d=S("el-option"),n=S("el-select"),u=S("el-form"),m=S("el-dialog");return $(),W(m,{modelValue:s.modal,"onUpdate:modelValue":e[8]||(e[8]=p=>s.modal=p),"show-close":!1,width:"800","align-center":""},{header:c(()=>[t("div",o1,[t("h3",n1,I(`${s.id?"Edit Storage or Pit":"Add Storage or Pit"}`),1),t("span",{class:"cursor-pointer ms-auto",onClick:e[0]||(e[0]=(...p)=>s.hide&&s.hide(...p))},[i(l,{icon:"closeModalIcon"})])])]),default:c(()=>[t("div",null,[i(u,{id:"storage_or_pit_form",onSubmit:de(s.submit,["prevent"]),model:s.targetData,ref:"formRef",class:"form"},{default:c(()=>[t("div",a1,[t("div",i1,[e[9]||(e[9]=t("label",{class:"d-flex align-items-center fs-6 fw-semibold mb-2 text-break required"},"Name ",-1)),i(a,{prop:"name",rules:{required:!0,message:"Please type Name",trigger:"blur"},class:"mt-auto"},{default:c(()=>[i(o,{class:"w-100",modelValue:s.targetData.name,"onUpdate:modelValue":e[1]||(e[1]=p=>s.targetData.name=p),placeholder:"",name:"name"},null,8,["modelValue"])]),_:1})]),t("div",d1,[e[10]||(e[10]=t("label",{class:"d-flex align-items-center fs-6 fw-semibold mb-2 text-break"},"Storage Type ",-1)),i(a,{prop:"storageType",class:"mt-auto"},{default:c(()=>[i(o,{modelValue:s.targetData.storageType,"onUpdate:modelValue":e[2]||(e[2]=p=>s.targetData.storageType=p),placeholder:"",name:"storageType"},null,8,["modelValue"])]),_:1})])]),t("div",r1,[t("div",u1,[e[11]||(e[11]=t("label",{class:"d-flex align-items-center fs-6 fw-semibold mb-2 text-break"},"Status ",-1)),i(a,{prop:"status",class:"mt-auto"},{default:c(()=>[i(n,{modelValue:s.targetData.status,"onUpdate:modelValue":e[3]||(e[3]=p=>s.targetData.status=p),placeholder:"Select Status",class:"w-100",clearable:""},{default:c(()=>[($(!0),V(B,null,Z(s.volumeTrackingStatusOptions,p=>($(),W(d,{key:p==null?void 0:p.value,label:p==null?void 0:p.label,value:p==null?void 0:p.value,name:"status"},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),t("div",c1,[e[12]||(e[12]=t("label",{class:"d-flex align-items-center fs-6 fw-semibold mb-2 text-break"},"Measured Volume (bbl) ",-1)),i(a,{prop:"measuredVolume",class:"mt-auto"},{default:c(()=>[i(o,{type:"number",class:"w-100",controls:!1,step:"any",modelValue:s.targetData.measuredVolume,"onUpdate:modelValue":e[4]||(e[4]=p=>s.targetData.measuredVolume=p),placeholder:"",name:"measuredVolume"},null,8,["modelValue"])]),_:1})])]),t("div",f1,[t("div",m1,[e[13]||(e[13]=t("label",{class:"d-flex align-items-center fs-6 fw-semibold mb-2 text-break"},"Mud Weight (ppg) ",-1)),i(a,{prop:"mudWeight",class:"mt-auto"},{default:c(()=>[i(o,{type:"number",class:"w-100",controls:!1,step:"any",modelValue:s.targetData.mudWeight,"onUpdate:modelValue":e[5]||(e[5]=p=>s.targetData.mudWeight=p),placeholder:"",name:"mudWeight"},null,8,["modelValue"])]),_:1})]),t("div",p1,[e[14]||(e[14]=t("label",{class:"d-flex align-items-center fs-6 fw-semibold mb-2 text-break"},"Mud Type ",-1)),i(a,{prop:"mudType",class:"mt-auto"},{default:c(()=>[i(o,{type:"number",class:"w-100",controls:!1,step:"any",modelValue:s.targetData.mudType,"onUpdate:modelValue":e[6]||(e[6]=p=>s.targetData.mudType=p),placeholder:"",name:"mudType"},null,8,["modelValue"])]),_:1})])]),t("div",g1,[t("button",{type:"button",id:"kt_modal_add_customer_cancel",class:"btn text-gray-700 btn-sm me-3 btn-blue",onClick:e[7]||(e[7]=(...p)=>s.hide&&s.hide(...p)),disabled:s.loading}," Discard ",8,b1),t("button",{"data-kt-indicator":s.loading?"on":null,class:"btn btn-sm btn-primary",type:"submit",disabled:s.loading},[s.loading?U("",!0):($(),V("span",h1," Save ")),s.loading?($(),V("span",y1,e[15]||(e[15]=[L(" Please wait... "),t("span",{class:"spinner-border spinner-border-sm align-middle ms-2"},null,-1)]))):U("",!0)],8,v1)])]),_:1},8,["onSubmit","model"])])]),_:1},8,["modelValue"])}const Yt=Q(l1,[["render",w1]]),$1=G({name:"form-modal",components:{SvgIcon:X,StorageOrPitModal:Yt},props:{loadTable:{type:Function,default:()=>{},required:!1},productAndPackageInventoryId:{type:String,required:!0}},setup(s){const e=re(),v=kt(),b=ws(),f=ut(),r=Gt(),l=nt(),o=y(!1),a=y({productAndPackageInventoryReportId:"",productId:"",locationId:"",type:null,quantity:null,cost:null,bolNo:"",notes:""}),d=y([]),n=y([]),u=y(null),m=y(!1),p=y(""),D=y(null),k=y(null),N=y(""),T=ee("dailyReport");ie(()=>{w()}),J(p,O=>{O!==""&&_()}),J(()=>a.value.productId,O=>{var K;a.value={...a.value,cost:(K=le(O))==null?void 0:K.price}}),J(o,O=>{var K;O==!0?(T!=null&&T.getDailyReportId()&&H(),!p.value&&N.value&&C()):(oe(),(K=u==null?void 0:u.value)==null||K.resetFields())});const w=async()=>{v.getWellDetails({wellId:e.params.id,callback:{onSuccess:O=>{var K;N.value=(K=O==null?void 0:O.company)==null?void 0:K.id,C()},onFinish:O=>{m.value=!1}}})},h=()=>{var O;(O=k==null?void 0:k.value)==null||O.show()},_=async()=>{r.getProductPackageItemDetails({id:p.value,callback:{onSuccess:O=>{var K,te,ue,he,ye;a.value={...a.value,...O,locationId:(K=O==null?void 0:O.location)==null?void 0:K.id},d.value=[{value:(ue=(te=O==null?void 0:O.productAndPackageInventoryReport)==null?void 0:te.product)==null?void 0:ue.id,label:(ye=(he=O==null?void 0:O.productAndPackageInventoryReport)==null?void 0:he.product)==null?void 0:ye.name,price:O==null?void 0:O.cost}]}}})},H=async(O={dailyReportId:T==null?void 0:T.getDailyReportId(),page:1,limit:200})=>{l.getVolumeTrackings({params:O,callback:{onSuccess:K=>{var te;n.value=(te=K==null?void 0:K.items)==null?void 0:te.map(ue=>({value:ue==null?void 0:ue.id,key:ue==null?void 0:ue.name}))},onFinish:K=>{}}})},C=async()=>{b.getProducts({params:{page:1,limit:200,companyId:N.value},callback:{onSuccess:O=>{var K;d.value=(K=O==null?void 0:O.items)==null?void 0:K.map(te=>({value:te==null?void 0:te.id,key:te==null?void 0:te.name,price:te==null?void 0:te.price}))}}})},F=async O=>{b.getProductDetails({id:O,callback:{onSuccess:K=>{a.value={...a.value,cost:K==null?void 0:K.price}}}})},j=async O=>{m.value=!0,r.updateProductPackageItem({id:p.value,params:O,callback:{onSuccess:K=>{s!=null&&s.loadTable&&(s==null||s.loadTable()),z()},onFinish:K=>{m.value=!1}}})},x=async O=>{m.value=!0,r.createProductPackageItem({params:O,callback:{onSuccess:K=>{s!=null&&s.loadTable&&(s==null||s.loadTable()),z()},onFinish:K=>{m.value=!1}}})},A=async O=>{m.value=!0,f.createProductPackageReport({params:O,callback:{onSuccess:K=>{s!=null&&s.loadTable&&(s==null||s.loadTable()),z()},onFinish:K=>{m.value=!1}}})},M=()=>{o.value=!0},z=()=>{o.value=!1},q=y({productId:[{required:!0,message:"Please choose Product",trigger:"change"}],locationId:[{required:!0,message:"Please choose location",trigger:"change"}],quantity:[{required:!0,message:"Please type quantity",trigger:"blur"}]}),R=(O,K,te)=>{p.value=K,a.value={...a.value,productId:te,productAndPackageInventoryReportId:O},p.value||F(te)},Y=O=>{D.value=O},se=()=>{u.value&&u.value.validate(O=>{var K,te,ue,he,ye,$e,Se;if(O){let we={productAndPackageInventoryId:s==null?void 0:s.productAndPackageInventoryId,productAndPackageInventoryReportId:(K=a.value)==null?void 0:K.productAndPackageInventoryReportId,locationId:(te=a==null?void 0:a.value)==null?void 0:te.locationId,type:D.value,quantity:Number((ue=a==null?void 0:a.value)==null?void 0:ue.quantity),notes:((he=a==null?void 0:a.value)==null?void 0:he.notes)||null,productId:(ye=a==null?void 0:a.value)==null?void 0:ye.productId,cost:Number(($e=a==null?void 0:a.value)==null?void 0:$e.cost),bolNo:((Se=a==null?void 0:a.value)==null?void 0:Se.bolNo)||null};p!=null&&p.value?j(we):D.value!=ce.initial?x(we):A(we)}})},oe=()=>{p.value="",D.value=null,d.value=[],n.value=[],a.value={productAndPackageInventoryReportId:"",productId:"",locationId:"",type:null,quantity:null,cost:null,bolNo:"",notes:""}},le=O=>{var K;return((K=d==null?void 0:d.value)==null?void 0:K.find(te=>O==te.value))||null};return{id:p,type:D,modal:o,rules:q,loading:m,targetData:a,formRef:u,productOptions:d,locationOptions:n,productAndPackageInventoryOptions:Ut,show:M,hide:z,submit:se,reset:oe,setId:R,setType:Y,getOption:tt,ProductPackageEnum:ce,storageOrPitModal:k,getVolumeTrackings:H,toggleAddStorageOrPitModal:h}}}),S1={class:"d-flex align-items-center w-100"},_1={class:"modal-title"},k1={class:"row"},D1={class:"col-12 fv-row d-flex flex-column justify-content-stretch"},I1={class:"col-12 fv-row d-flex flex-column justify-content-stretch"},V1={class:"d-flex justify-content-between align-items-center mb-2"},P1={class:"col-12 fv-row d-flex flex-column justify-content-stretch"},C1={class:"col-12 fv-row d-flex flex-column justify-content-stretch"},T1={class:"col-12 fv-row d-flex flex-column justify-content-stretch"},F1={class:"col-12 fv-row d-flex flex-column justify-content-stretch"},x1={class:"modal-footer d-flex justify-content-center align-items-center"},N1=["disabled"],A1=["data-kt-indicator","disabled"],H1={key:0,class:"indicator-label"},q1={key:1,class:"indicator-progress"};function M1(s,e,v,b,f,r){const l=S("SvgIcon"),o=S("el-option"),a=S("el-select"),d=S("el-form-item"),n=S("el-input"),u=S("el-form"),m=S("el-dialog"),p=S("StorageOrPitModal");return $(),V(B,null,[i(m,{modelValue:s.modal,"onUpdate:modelValue":e[9]||(e[9]=D=>s.modal=D),"show-close":!1,width:"500","align-center":""},{header:c(()=>{var D;return[t("div",S1,[t("h3",_1,I((D=s.getOption(s.type,s.productAndPackageInventoryOptions))==null?void 0:D.label),1),t("span",{class:"cursor-pointer ms-auto",onClick:e[0]||(e[0]=(...k)=>s.hide&&s.hide(...k))},[i(l,{icon:"closeModalIcon"})])])]}),default:c(()=>[t("div",null,[i(u,{id:"product_package_form",onSubmit:de(s.submit,["prevent"]),model:s.targetData,rules:s.rules,ref:"formRef",class:"form"},{default:c(()=>[t("div",k1,[t("div",D1,[e[10]||(e[10]=t("label",{class:"d-flex align-items-center fs-6 fw-semibold mb-2 text-break required"},"Product ",-1)),i(d,{prop:"productId",class:"mt-auto"},{default:c(()=>[i(a,{modelValue:s.targetData.productId,"onUpdate:modelValue":e[1]||(e[1]=D=>s.targetData.productId=D),placeholder:"Select Product",class:"w-100",clearable:"",disabled:s.type!==s.ProductPackageEnum.initial||s.type==s.ProductPackageEnum.initial&&s.id!=""},{default:c(()=>[($(!0),V(B,null,Z(s.productOptions,D=>($(),W(o,{key:D.value,label:D.key,value:D.value,name:"productId"},null,8,["label","value"]))),128))]),_:1},8,["modelValue","disabled"])]),_:1})]),t("div",I1,[t("div",V1,[e[11]||(e[11]=t("label",{class:"d-flex align-items-center fs-6 fw-semibold text-break required"},"Location ",-1)),t("button",{type:"button",class:"btn btn-primary btn-sm fw-semibold",onClick:e[2]||(e[2]=(...D)=>s.toggleAddStorageOrPitModal&&s.toggleAddStorageOrPitModal(...D))}," New Location ")]),i(d,{prop:"locationId",class:"mt-auto"},{default:c(()=>[i(a,{modelValue:s.targetData.locationId,"onUpdate:modelValue":e[3]||(e[3]=D=>s.targetData.locationId=D),placeholder:"Select Location",class:"w-100",clearable:""},{default:c(()=>[($(!0),V(B,null,Z(s.locationOptions,D=>($(),W(o,{key:D.value,label:D.key,value:D.value,name:"locationId"},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),t("div",P1,[e[12]||(e[12]=t("label",{class:"d-flex align-items-center fs-6 fw-semibold mb-2 text-break required"},"Quantity ",-1)),i(d,{prop:"quantity",class:"mt-auto"},{default:c(()=>[i(n,{type:"number",class:"w-100",controls:!1,step:"any",modelValue:s.targetData.quantity,"onUpdate:modelValue":e[4]||(e[4]=D=>s.targetData.quantity=D),placeholder:"",name:"quantity"},null,8,["modelValue"])]),_:1})]),s.type!==s.ProductPackageEnum.used?($(),V(B,{key:0},[t("div",C1,[e[13]||(e[13]=t("label",{class:"d-flex align-items-center fs-6 fw-semibold mb-2 text-break"},"Cost ",-1)),i(d,{prop:"cost",class:"mt-auto"},{default:c(()=>[i(n,{disabled:"",class:"w-100",modelValue:s.targetData.cost,"onUpdate:modelValue":e[5]||(e[5]=D=>s.targetData.cost=D),placeholder:"",name:"cost"},null,8,["modelValue"])]),_:1})]),t("div",T1,[e[14]||(e[14]=t("label",{class:"d-flex align-items-center fs-6 fw-semibold mb-2 text-break"},"BOL No ",-1)),i(d,{prop:"bolNo",class:"mt-auto"},{default:c(()=>[i(n,{class:"w-100",modelValue:s.targetData.bolNo,"onUpdate:modelValue":e[6]||(e[6]=D=>s.targetData.bolNo=D),placeholder:"",name:"bolNo"},null,8,["modelValue"])]),_:1})])],64)):U("",!0),t("div",F1,[e[15]||(e[15]=t("label",{class:"d-flex align-items-center fs-6 fw-semibold mb-2 text-break"},"Notes ",-1)),i(d,{prop:"notes",class:"mt-auto"},{default:c(()=>[i(n,{class:"w-100",modelValue:s.targetData.notes,"onUpdate:modelValue":e[7]||(e[7]=D=>s.targetData.notes=D),placeholder:"",name:"notes",type:"textarea",rows:"2"},null,8,["modelValue"])]),_:1})])]),t("div",x1,[t("button",{type:"button",id:"kt_modal_add_customer_cancel",class:"btn text-gray-700 btn-sm me-3 btn-blue",onClick:e[8]||(e[8]=(...D)=>s.hide&&s.hide(...D)),disabled:s.loading}," Discard ",8,N1),t("button",{"data-kt-indicator":s.loading?"on":null,class:"btn btn-sm btn-primary",type:"submit",disabled:s.loading},[s.loading?U("",!0):($(),V("span",H1," Save ")),s.loading?($(),V("span",q1,e[16]||(e[16]=[L(" Please wait... "),t("span",{class:"spinner-border spinner-border-sm align-middle ms-2"},null,-1)]))):U("",!0)],8,A1)])]),_:1},8,["onSubmit","model","rules"])])]),_:1},8,["modelValue"]),i(p,{ref:"storageOrPitModal",loadPage:s.getVolumeTrackings},null,8,["loadPage"])],64)}const Jt=Q($1,[["render",M1]]),j1=G({name:"product-package",components:{SvgIcon:X,FormModal:Jt,BottomTool:pe,TablePagination:Ss,NoEntries:be},props:{setChildActiveTab:{type:Function,required:!1,default:()=>{}}},setup(s){const e=re(),v=ut(),b=Wt(),f=Gt(),r=y(!1),l=y(!1),o=y(),a=y([]),d=y(null),n=y(""),u=ee("dailyReport");ie(()=>{u!=null&&u.getDailyReportId()?p():m()});const m=async()=>{var F;u==null||u.createDailyReport({wellId:(F=e==null?void 0:e.params)==null?void 0:F.id,callback:{onSuccess:j=>{p()}}})},p=async()=>{u!=null&&u.getDailyReportId()&&(r.value=!0,b.getProductPackageInventories({dailyReportId:u==null?void 0:u.getDailyReportId(),callback:{onSuccess:F=>{o.value=JSON.parse(JSON.stringify(F)),n.value=F==null?void 0:F.id,F!=null&&F.id&&D(F.id)},onFinish:F=>{r.value=!1}}}))},D=async F=>{l.value=!0,v.getProductPackageReports({params:{productAndPackageInventoryId:F,page:1,limit:200},callback:{onSuccess:j=>{var x,A,M;a.value=JSON.parse(JSON.stringify(j==null?void 0:j.items));for(let z=0;z<((x=a==null?void 0:a.value)==null?void 0:x.length);z++){const q={...a==null?void 0:a.value[z],historyList:[],pageCount:0,totalElements:0,currentPage:1,loading:!1};a.value[z]=q,(A=a.value[z])!=null&&A.id&&((M=a.value[z])!=null&&M.currentPage)&&k(z,a.value[z].id,a.value[z].currentPage)}},onFinish:j=>{l.value=!1}}})},k=async(F,j,x)=>{a.value[F].loading=!0,f.getProductPackageItems({params:{productAndPackageInventoryReportId:j,page:x,limit:10},callback:{onSuccess:A=>{a.value[F].historyList=JSON.parse(JSON.stringify(A==null?void 0:A.items)),a.value[F].pageCount=A==null?void 0:A.totalPage,a.value[F].totalElements=A==null?void 0:A.total,a.value[F].currentPage=A==null?void 0:A.page},onFinish:A=>{a.value[F].loading=!1}}})},N=(F,j,x)=>{a.value[F].currentPage=x,k(F,j,x)},T=F=>{ne.deletionAlert({onConfirmed:()=>{w(F)}})},w=async F=>{v.deleteProductPackageReport({id:F,callback:{onSuccess:j=>{p()}}})},h=F=>{ne.deletionAlert({onConfirmed:()=>{_(F)}})},_=async F=>{f.deleteProductPackageItem({id:F,callback:{onSuccess:j=>{p()}}})};return{packageInfo:o,packageList:a,formModal:d,loadingPackageInfo:r,loadingPackageList:l,productAndPackageInventoryOptions:Ut,productAndPackageInventoryId:n,ProductAndPackageInventoryType:ce,formatDate:ke,getOption:tt,getPackageInfo:p,pageChange:N,deleteProductPackage:T,deleteProductPackageItem:h,numberWithCommas:ge,toggleItem:(F,j,x,A)=>{var M,z,q,R,Y,se,oe;x==ce.received?(M=d==null?void 0:d.value)==null||M.setType(ce.received):x==ce.used?(z=d==null?void 0:d.value)==null||z.setType(ce.used):x==ce.adjusted?(q=d==null?void 0:d.value)==null||q.setType(ce.adjusted):x==ce.returned?(R=d==null?void 0:d.value)==null||R.setType(ce.returned):x==ce.initial&&((Y=d==null?void 0:d.value)==null||Y.setType(ce.initial)),(se=d==null?void 0:d.value)==null||se.setId(F,j,A),(oe=d==null?void 0:d.value)==null||oe.show()},toggleAddInitial:()=>{var F,j;(F=d==null?void 0:d.value)==null||F.setType(ce.initial),(j=d==null?void 0:d.value)==null||j.show()}}}}),z1={class:"card h-100 my-8"},E1={class:"card-body"},L1={class:"d-flex flex-wrap flex-lg-nowrap w-100 sample-tab overflow-y-hidden"},R1={class:"py-4 min-w-300px w-100 w-lg-auto"},B1={class:"bg-light-primary rounded border-primary border border-dashed px-5 py-7 px-xl-7 pb-xl-9 d-flex flex-column gap-3 font-roboto"},O1={key:0,class:"text-center"},U1={key:1,class:"d-flex flex-column gap-3 text-gray-800"},W1={class:"d-flex flex-wrap align-items-center justify-content-between"},G1={class:"fw-300 ms-auto"},Y1={class:"py-4 w-100 overflow-y-hidden"},J1={class:"accordion accordion-flush",id:"accordionPanelsStayOpenExample"},Q1={key:0,class:"text-center"},K1={key:1},X1=["id"],Z1=["data-bs-target","aria-controls"],ev={class:"d-flex flex-wrap align-items-center btn-accordion p-4 rounded-3 w-100 gap-3 justify-content-between accordion-toggle"},tv={class:"d-flex align-items-center"},sv={class:"flex-grow-1"},lv={class:"text-gray-800"},ov={class:"d-flex flex-wrap align-items-center"},nv={class:"text-green-light fw-semibold d-block me-5 fs-5"},av={class:"text-danger fw-semibold d-block me-5 fs-5"},iv={class:"d-flex align-items-center gap-2"},dv=["onClick"],rv={class:"svg-icon svg-icon-3"},uv=["onClick"],cv={class:"svg-icon svg-icon-3"},fv=["onClick"],mv={class:"svg-icon svg-icon-3"},pv=["onClick"],gv={class:"svg-icon svg-icon-3"},bv=["onClick"],vv={class:"svg-icon svg-icon-3 text-danger"},hv=["id","aria-labelledby"],yv={class:"accordion-body pt-0"},wv={class:"d-flex flex-column w-100"},$v={class:"table-responsive"},Sv={class:"table table-row-bordered align-middle gs-0 gy-3"},_v={key:0,colspan:"6"},kv={class:"text-gray-600 fw-bold"},Dv={class:"text-gray-600 fw-bold"},Iv={class:"text-gray-600 fw-bold text-break"},Vv={class:"text-gray-600 fw-bold"},Pv={class:"text-gray-600 fw-300"},Cv={class:"d-flex align-items-center justify-content-end"},Tv=["onClick"],Fv={class:"svg-icon svg-icon-3"},xv=["onClick","disabled"],Nv={class:"svg-icon svg-icon-3 text-danger"},Av={class:"d-flex flex-wrap align-items-center mt-5 gap-4"},Hv={key:0,class:"text-gray-700 fw-semibold fs-6 me-auto"};function qv(s,e,v,b,f,r){var m;const l=S("NoEntries"),o=S("SvgIcon"),a=S("el-tooltip"),d=S("TablePagination"),n=S("BottomTool"),u=S("FormModal");return $(),V(B,null,[t("div",z1,[e[8]||(e[8]=t("div",{class:"card-header d-flex flex-wrap align-items-center"},[t("h1",{class:"mb-0 me-4 text-gray-900"},"Product & Package Inventory")],-1)),t("div",E1,[t("div",L1,[t("div",R1,[t("div",B1,[s.loadingPackageInfo?($(),V("div",O1,e[0]||(e[0]=[t("div",{class:"spinner-border text-primary",role:"status"},[t("span",{class:"sr-only"},"Loading...")],-1)]))):($(),V("div",U1,[e[2]||(e[2]=t("h5",{class:"mb-0 pb-3"},"Details",-1)),t("div",W1,[e[1]||(e[1]=t("span",{class:"fw-bold me-5"},"Total cost ",-1)),t("span",G1,I(`$${s.numberWithCommas((m=s.packageInfo)==null?void 0:m.totalCost)}`),1)])]))])]),t("div",Y1,[t("div",J1,[s.loadingPackageList?($(),V("div",Q1,e[3]||(e[3]=[t("div",{class:"spinner-border text-primary",role:"status"},[t("span",{class:"sr-only"},"Loading...")],-1)]))):($(),V("div",K1,[s.packageList.length===0?($(),W(l,{key:0,addNew:s.toggleAddInitial},null,8,["addNew"])):($(!0),V(B,{key:1},Z(s.packageList,(p,D)=>{var k,N,T;return $(),V("div",{key:p==null?void 0:p.id,class:"accordion-item mb-5"},[t("div",{class:"accordion-header",id:`panelsStayOpen-heading${p==null?void 0:p.id}`},[t("div",{class:"accordion-button bg-transparent shadow-none py-0",type:"button","data-bs-toggle":"collapse","data-bs-target":`#panelsStayOpen-collapse${p==null?void 0:p.id}`,"aria-expanded":"true","aria-controls":`panelsStayOpen-heading${p==null?void 0:p.id}`},[t("div",ev,[t("div",tv,[e[4]||(e[4]=t("span",{class:"bullet bullet-green h-40px me-3"},null,-1)),t("div",sv,[t("h4",lv,I((k=p==null?void 0:p.product)==null?void 0:k.name),1),t("div",ov,[t("span",nv,I(`Quantity: ${p==null?void 0:p.quantity}`),1),t("span",av,I(`Total Cost: $${s.numberWithCommas(p==null?void 0:p.totalCost)}`),1)])])]),t("div",iv,[i(a,{content:"Received",placement:"top",effect:"customize"},{default:c(()=>[t("button",{class:"btn rounded-circle btn-icon btn-sm btn-action bg-light non-collapsing btn-export","data-bs-toggle":"collapse","data-bs-target":"",onClick:w=>{var h;return s.toggleItem(p==null?void 0:p.id,"",s.ProductAndPackageInventoryType.received,(h=p==null?void 0:p.product)==null?void 0:h.id)}},[t("span",rv,[i(o,{icon:"addIcon",color:"#43a047"})])],8,dv)]),_:2},1024),i(a,{content:"Used",placement:"top",effect:"customize"},{default:c(()=>[t("button",{class:"btn rounded-circle btn-icon btn-sm btn-action bg-light non-collapsing btn-view","data-bs-toggle":"collapse","data-bs-target":"",onClick:w=>{var h;return s.toggleItem(p==null?void 0:p.id,"",s.ProductAndPackageInventoryType.used,(h=p==null?void 0:p.product)==null?void 0:h.id)}},[t("span",cv,[i(o,{icon:"minusIcon"})])],8,uv)]),_:2},1024),i(a,{content:"Returned",placement:"top",effect:"customize"},{default:c(()=>[t("button",{class:"btn rounded-circle btn-icon btn-sm btn-action bg-light non-collapsing btn-archive","data-bs-toggle":"collapse","data-bs-target":"",onClick:w=>{var h;return s.toggleItem(p==null?void 0:p.id,"",s.ProductAndPackageInventoryType.returned,(h=p==null?void 0:p.product)==null?void 0:h.id)}},[t("span",mv,[i(o,{icon:"refreshIcon"})])],8,fv)]),_:2},1024),i(a,{content:"Adjusted",placement:"top",effect:"customize"},{default:c(()=>[t("button",{class:"btn rounded-circle btn-icon btn-sm btn-action bg-light non-collapsing btn-edit","data-bs-toggle":"collapse","data-bs-target":"",onClick:w=>{var h;return s.toggleItem(p==null?void 0:p.id,"",s.ProductAndPackageInventoryType.adjusted,(h=p==null?void 0:p.product)==null?void 0:h.id)}},[t("span",gv,[i(o,{icon:"pencilIcon"})])],8,pv)]),_:2},1024),i(a,{content:"Delete",placement:"top",effect:"customize"},{default:c(()=>[t("button",{class:"btn rounded-circle btn-icon btn-sm btn-action bg-light non-collapsing btn-delete",onClick:w=>s.deleteProductPackage(p==null?void 0:p.id),"data-bs-toggle":"collapse","data-bs-target":""},[t("span",vv,[i(o,{icon:"trashIcon"})])],8,bv)]),_:2},1024)])])],8,Z1)],8,X1),t("div",{id:`panelsStayOpen-collapse${p==null?void 0:p.id}`,class:"accordion-collapse collapse bg-green-light px-0 mx-6","aria-labelledby":`panelsStayOpen-heading${p==null?void 0:p.id}`},[t("div",yv,[t("div",wv,[e[7]||(e[7]=t("p",{class:"text-gray-900 fw-bold d-block text-small fs-5"}," History ",-1)),t("div",$v,[t("table",Sv,[e[6]||(e[6]=t("thead",null,[t("tr",{class:"fw-bold text-gray-800"},[t("th",null,"Date"),t("th",null,"Type"),t("th",null,"Location"),t("th",null,"Quantity"),t("th",null,"Cost"),t("th")])],-1)),t("tbody",null,[p!=null&&p.loading?($(),V("td",_v,e[5]||(e[5]=[t("div",{class:"text-center"},[t("div",{class:"spinner-border text-primary",role:"status"},[t("span",{class:"sr-only"},"Loading...")])],-1)]))):($(!0),V(B,{key:1},Z(p==null?void 0:p.historyList,w=>{var h,_;return $(),V("tr",{key:p.id},[t("td",kv,I(s.formatDate(w==null?void 0:w.createdAt)),1),t("td",Dv,I((h=s.getOption(w==null?void 0:w.type,s.productAndPackageInventoryOptions))==null?void 0:h.label),1),t("td",Iv,I((_=w==null?void 0:w.location)==null?void 0:_.name),1),t("td",Vv,I(s.numberWithCommas(w==null?void 0:w.quantity)),1),t("td",Pv,I(w!=null&&w.cost?`$${s.numberWithCommas(w==null?void 0:w.cost)}`:""),1),t("td",null,[t("div",Cv,[t("button",{class:"btn btn-icon btn-sm btn-blue me-3",onClick:H=>{var C;return s.toggleItem(p==null?void 0:p.id,w==null?void 0:w.id,w==null?void 0:w.type,(C=p==null?void 0:p.product)==null?void 0:C.id)}},[t("span",Fv,[i(o,{icon:"newReportIcon"})])],8,Tv),t("button",{class:"btn btn-icon btn-sm btn-blue me-3",onClick:H=>s.deleteProductPackageItem(w==null?void 0:w.id),disabled:(w==null?void 0:w.type)==s.ProductAndPackageInventoryType.initial},[t("span",Nv,[i(o,{icon:"trashIcon"})])],8,xv)])])])}),128))])])]),t("div",Av,[(N=p==null?void 0:p.historyList)!=null&&N.length?($(),V("div",Hv,I(`Showing ${((p==null?void 0:p.currentPage)-1)*10+1} to ${(T=p==null?void 0:p.historyList)==null?void 0:T.length} of ${p==null?void 0:p.totalElements} entries`),1)):U("",!0),(p==null?void 0:p.pageCount)>=1?($(),W(d,{key:1,"total-pages":p==null?void 0:p.pageCount,total:p==null?void 0:p.totalElements,"per-page":10,"current-page":p==null?void 0:p.currentPage,onPageChange:w=>s.pageChange(D,p==null?void 0:p.id,w)},null,8,["total-pages","total","current-page","onPageChange"])):U("",!0)])])])],8,hv)])}),128))]))])])])])]),i(n,{addNew:s.toggleAddInitial,showHelpInfo:!1},null,8,["addNew"]),i(u,{ref:"formModal",productAndPackageInventoryId:s.productAndPackageInventoryId,loadTable:s.getPackageInfo},null,8,["productAndPackageInventoryId","loadTable"])],64)}const Qt=Q(j1,[["render",qv]]),at=ae("pump",()=>({getPumpSummary:async({dailyReportId:l,callback:o})=>{var n;const a=g.get(o,"onSuccess",g.noop),d=g.get(o,"onFinish",g.noop);try{P.setHeader();const u=await P.get(`pumps/summary/${l}`);a(((n=u.data)==null?void 0:n.data)||u.data)}catch(u){E(u,o)}finally{d()}},getPumps:async({params:l,callback:o})=>{var n;const a=g.get(o,"onSuccess",g.noop),d=g.get(o,"onFinish",g.noop);try{P.setHeader();const u=await P.getWithParams("pumps",l);a(((n=u.data)==null?void 0:n.data)||u.data)}catch(u){E(u,o)}finally{d()}},getPumpDetails:async({id:l,callback:o})=>{var n;const a=g.get(o,"onSuccess",g.noop),d=g.get(o,"onFinish",g.noop);try{P.setHeader();const u=await P.get(`pumps/${l}`);a(((n=u.data)==null?void 0:n.data)||u.data)}catch(u){E(u,o)}finally{d()}},updatePump:async({id:l,params:o,callback:a})=>{var u;const d=g.get(a,"onSuccess",g.noop),n=g.get(a,"onFinish",g.noop);try{P.setHeader();const m=await P.put(`pumps/${l}`,o);d(((u=m.data)==null?void 0:u.data)||m.data)}catch(m){E(m,a)}finally{n()}},deletePump:async({id:l,callback:o})=>{var n;const a=g.get(o,"onSuccess",g.noop),d=g.get(o,"onFinish",g.noop);try{P.setHeader();const u=await P.delete(`pumps/${l}`);a(((n=u.data)==null?void 0:n.data)||u.data)}catch(u){E(u,o)}finally{d()}},createPump:async({params:l,callback:o})=>{var n;const a=g.get(o,"onSuccess",g.noop),d=g.get(o,"onFinish",g.noop);try{P.setHeader();const u=await P.post("pumps",l);a(((n=u.data)==null?void 0:n.data)||u.data)}catch(u){E(u,o)}finally{d()}}})),Kt=ae("pumpDuration",()=>({getPumpDurationDetails:async({id:f,callback:r})=>{var a;const l=g.get(r,"onSuccess",g.noop),o=g.get(r,"onFinish",g.noop);try{P.setHeader();const d=await P.get(`pumpDurations/${f}`);l(((a=d.data)==null?void 0:a.data)||d.data)}catch(d){E(d,r)}finally{o()}},updatePumpDuration:async({id:f,params:r,callback:l})=>{var d;const o=g.get(l,"onSuccess",g.noop),a=g.get(l,"onFinish",g.noop);try{P.setHeader();const n=await P.put(`pumpDurations/${f}`,r);o(((d=n.data)==null?void 0:d.data)||n.data)}catch(n){E(n,l)}finally{a()}},deletePumpDuration:async({id:f,callback:r})=>{var a;const l=g.get(r,"onSuccess",g.noop),o=g.get(r,"onFinish",g.noop);try{P.setHeader();const d=await P.delete(`pumpDurations/${f}`);l(((a=d.data)==null?void 0:a.data)||d.data)}catch(d){E(d,r)}finally{o()}},createPumpDuration:async({params:f,callback:r})=>{var a;const l=g.get(r,"onSuccess",g.noop),o=g.get(r,"onFinish",g.noop);try{P.setHeader();const d=await P.post("pumpDurations",f);l(((a=d.data)==null?void 0:a.data)||d.data)}catch(d){E(d,r)}finally{o()}}})),Mv=G({name:"pump-durations-modal",components:{SvgIcon:X},props:{loadPage:{type:Function,required:!1}},setup(s){const e=Kt(),v=y(!1),b=y({durations:null}),f=y(null),r=y(!1),l=y(""),o=y("");J(o,T=>{T!==""&&a()}),J(v,T=>{var w;T===!1&&(o.value="",l.value="",N(),(w=f==null?void 0:f.value)==null||w.resetFields())});const a=async()=>{e.getPumpDurationDetails({id:o.value,callback:{onSuccess:T=>{b.value={...T}}}})},d=async T=>{r.value=!0,e.updatePumpDuration({id:o.value,params:T,callback:{onSuccess:w=>{s!=null&&s.loadPage&&(s==null||s.loadPage()),m()},onFinish:w=>{r.value=!1}}})},n=async T=>{r.value=!0,e.createPumpDuration({params:T,callback:{onSuccess:w=>{s!=null&&s.loadPage&&(s==null||s.loadPage()),m()},onFinish:w=>{r.value=!1}}})},u=()=>{v.value=!0},m=()=>{v.value=!1},p=(T,w)=>{l.value=T,o.value=w},D=y({durations:[{required:!0,message:"Please type Duration",trigger:"blur"}]}),k=()=>{f.value&&f.value.validate(T=>{var w;if(T){const h={pumpId:l==null?void 0:l.value,durations:Number((w=b==null?void 0:b.value)==null?void 0:w.durations)};o!=null&&o.value?d(h):n(h)}})},N=()=>{b.value={durations:null}};return{id:o,modal:v,rules:D,loading:r,targetData:b,formRef:f,show:u,hide:m,submit:k,setId:p,reset:N}}}),jv={class:"d-flex align-items-center w-100"},zv={class:"modal-title"},Ev={class:"row"},Lv={class:"col-12 fv-row d-flex flex-column justify-content-stretch"},Rv={class:"modal-footer d-flex justify-content-center align-items-center"},Bv=["disabled"],Ov=["data-kt-indicator","disabled"],Uv={key:0,class:"indicator-label"},Wv={key:1,class:"indicator-progress"};function Gv(s,e,v,b,f,r){const l=S("SvgIcon"),o=S("el-input"),a=S("el-form-item"),d=S("el-form"),n=S("el-dialog");return $(),W(n,{modelValue:s.modal,"onUpdate:modelValue":e[3]||(e[3]=u=>s.modal=u),"show-close":!1,width:"500","align-center":""},{header:c(()=>[t("div",jv,[t("h3",zv,I(`${s.id?"Edit Pump Duration":"Add Pump Duration"}`),1),t("span",{class:"cursor-pointer ms-auto",onClick:e[0]||(e[0]=(...u)=>s.hide&&s.hide(...u))},[i(l,{icon:"closeModalIcon"})])])]),default:c(()=>[i(d,{id:"pump_duration_form",onSubmit:de(s.submit,["prevent"]),model:s.targetData,rules:s.rules,ref:"formRef",class:"form"},{default:c(()=>[t("div",Ev,[t("div",Lv,[e[4]||(e[4]=t("label",{class:"d-flex align-items-center fs-6 fw-semibold mb-2 text-break required"},"Duration (hr) ",-1)),i(a,{prop:"durations",class:"mt-auto"},{default:c(()=>[i(o,{type:"number",class:"w-100",controls:!1,step:"any",modelValue:s.targetData.durations,"onUpdate:modelValue":e[1]||(e[1]=u=>s.targetData.durations=u),placeholder:"",name:"durations"},null,8,["modelValue"])]),_:1})])]),t("div",Rv,[t("button",{type:"button",id:"kt_modal_add_customer_cancel",class:"btn text-gray-700 btn-sm me-3 btn-blue",onClick:e[2]||(e[2]=(...u)=>s.hide&&s.hide(...u)),disabled:s.loading}," Discard ",8,Bv),t("button",{"data-kt-indicator":s.loading?"on":null,class:"btn btn-sm btn-primary",type:"submit",disabled:s.loading},[s.loading?U("",!0):($(),V("span",Uv," Save ")),s.loading?($(),V("span",Wv,e[5]||(e[5]=[L(" Please wait... "),t("span",{class:"spinner-border spinner-border-sm align-middle ms-2"},null,-1)]))):U("",!0)],8,Ov)])]),_:1},8,["onSubmit","model","rules"])]),_:1},8,["modelValue"])}const Xt=Q(Mv,[["render",Gv]]),Yv=G({name:"pump-modal",components:{SvgIcon:X},props:{loadPage:{type:Function,default:()=>{},required:!0}},setup(s){const e=re(),v=at(),b=y(!1),f={description:"",inUse:!1,model:"",linearID:null,rodOD:null,strokeLength:null,efficiency:null,stroke:null,displacement:null,rate:null},r=y(JSON.parse(JSON.stringify(f))),l=y(null),o=y(!1),a=y(""),d=ee("dailyReport");J(a,h=>{h!==""&&n()}),J(b,h=>{var _;h===!1&&(a.value="",w(),(_=l==null?void 0:l.value)==null||_.resetFields())});const n=async()=>{v.getPumpDetails({id:a.value,callback:{onSuccess:h=>{r.value=JSON.parse(JSON.stringify(h))}}})},u=async h=>{o.value=!0,v.updatePump({id:a.value,params:h,callback:{onSuccess:_=>{s!=null&&s.loadPage&&(s==null||s.loadPage()),D()},onFinish:_=>{o.value=!1}}})},m=async h=>{o.value=!0,v.createPump({params:h,callback:{onSuccess:_=>{s!=null&&s.loadPage&&(s==null||s.loadPage()),D()},onFinish:_=>{o.value=!1}}})},p=()=>{b.value=!0},D=()=>{b.value=!1},k=h=>{a.value=h.toString()},N=y({description:[{required:!0,message:"Please type Description",trigger:"blur"}]}),T=()=>{l.value&&l.value.validate(h=>{var _,H,C,F,j,x,A,M,z,q,R;if(h){const Y={description:(_=r==null?void 0:r.value)==null?void 0:_.description,inUse:(H=r==null?void 0:r.value)==null?void 0:H.inUse,model:(C=r==null?void 0:r.value)==null?void 0:C.model,linearID:Number((F=r==null?void 0:r.value)==null?void 0:F.linearID),rodOD:Number((j=r==null?void 0:r.value)==null?void 0:j.rodOD),strokeLength:Number((x=r==null?void 0:r.value)==null?void 0:x.strokeLength),efficiency:Number((A=r==null?void 0:r.value)==null?void 0:A.efficiency),stroke:Number((M=r==null?void 0:r.value)==null?void 0:M.stroke),displacement:Number((z=r==null?void 0:r.value)==null?void 0:z.displacement),rate:Number((q=r==null?void 0:r.value)==null?void 0:q.rate)};a!=null&&a.value?u({...Y,dailyReportId:d==null?void 0:d.getDailyReportId()}):d==null||d.createDailyReport({wellId:(R=e==null?void 0:e.params)==null?void 0:R.id,callback:{onSuccess:se=>{m({...Y,dailyReportId:d==null?void 0:d.getDailyReportId()})}}})}})},w=()=>{r.value=JSON.parse(JSON.stringify(f))};return{id:a,modal:b,rules:N,loading:o,targetData:r,formRef:l,show:p,hide:D,submit:T,setId:k,reset:w}}}),Jv={class:"d-flex align-items-center w-100"},Qv={class:"modal-title"},Kv={class:"row g-7 mb-3"},Xv={class:"col-sm-6 fv-row d-flex flex-column justify-content-stretch"},Zv={class:"col-sm-6 fv-row d-flex flex-sm-column align-items-center align-items-sm-start justify-content-stretch"},eh={class:"d-flex align-items-center fs-6 fw-semibold mb-2 text-break"},th={class:"row g-7 mb-3"},sh={class:"col-sm-6 fv-row d-flex flex-column justify-content-stretch"},lh={class:"d-flex align-items-center fs-6 fw-semibold mb-2"},oh={class:"col-sm-6 fv-row d-flex flex-column justify-content-stretch"},nh={class:"row g-7 mb-3"},ah={class:"col-sm-6 fv-row d-flex flex-column justify-content-stretch"},ih={class:"d-flex align-items-center fs-6 fw-semibold mb-2"},dh={class:"col-sm-6 fv-row d-flex flex-column justify-content-stretch"},rh={class:"row g-7 mb-3"},uh={class:"col-sm-6 fv-row d-flex flex-column justify-content-stretch"},ch={class:"d-flex align-items-center fs-6 fw-semibold mb-2"},fh={class:"col-sm-6 fv-row d-flex flex-column justify-content-stretch"},mh={class:"d-flex align-items-center fs-6 fw-semibold mb-2"},ph={class:"row g-7 mb-3"},gh={class:"col-sm-6 fv-row d-flex flex-column justify-content-stretch"},bh={class:"d-flex align-items-center fs-6 fw-semibold mb-2"},vh={class:"col-sm-6 fv-row d-flex flex-column justify-content-stretch"},hh={class:"d-flex align-items-center fs-6 fw-semibold mb-2"},yh={class:"modal-footer d-flex justify-content-center align-items-center"},wh=["disabled"],$h=["data-kt-indicator","disabled"],Sh={key:0,class:"indicator-label"},_h={key:1,class:"indicator-progress"};function kh(s,e,v,b,f,r){const l=S("SvgIcon"),o=S("el-input"),a=S("el-form-item"),d=S("el-popover"),n=S("el-form"),u=S("el-dialog");return $(),W(u,{modelValue:s.modal,"onUpdate:modelValue":e[12]||(e[12]=m=>s.modal=m),"show-close":!1,width:"500","align-center":""},{header:c(()=>[t("div",Jv,[t("h3",Qv,I(`${s.id?"Edit Pump":"Add Pump"}`),1),t("span",{class:"cursor-pointer ms-auto",onClick:e[0]||(e[0]=(...m)=>s.hide&&s.hide(...m))},[i(l,{icon:"closeModalIcon"})])])]),default:c(()=>[i(n,{id:"pump_form",onSubmit:de(s.submit,["prevent"]),model:s.targetData,rules:s.rules,ref:"formRef",class:"form"},{default:c(()=>[t("div",Kv,[t("div",Xv,[e[13]||(e[13]=t("label",{class:"d-flex align-items-center fs-6 fw-semibold mb-2 text-break required"},"Description ",-1)),i(a,{prop:"description",class:"mt-auto"},{default:c(()=>[i(o,{modelValue:s.targetData.description,"onUpdate:modelValue":e[1]||(e[1]=m=>s.targetData.description=m),placeholder:"",name:"description"},null,8,["modelValue"])]),_:1})]),t("div",Zv,[t("label",eh,[e[16]||(e[16]=L("In Use ")),i(d,{placement:"top",width:200,trigger:"hover"},{reference:c(()=>e[14]||(e[14]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7"},null,-1)])),default:c(()=>[e[15]||(e[15]=t("span",null," Indicates whether the pump is currently in use or not. ",-1))]),_:1})]),i(a,{prop:"inUse",class:"mt-sm-auto mb-0 mb-sm-auto ms-5 ms-sm-0"},{default:c(()=>[$t(t("input",{class:"form-check-input h-20px w-20px",type:"checkbox",placeholder:"",name:"inUse","onUpdate:modelValue":e[2]||(e[2]=m=>s.targetData.inUse=m)},null,512),[[St,s.targetData.inUse]])]),_:1})])]),t("div",th,[t("div",sh,[t("label",lh,[e[19]||(e[19]=t("span",null,"Model",-1)),i(d,{placement:"top",width:250,trigger:"hover"},{reference:c(()=>e[17]||(e[17]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7"},null,-1)])),default:c(()=>[e[18]||(e[18]=t("span",null,"Specifies the model or type of the pump. ",-1))]),_:1})]),i(a,{prop:"model"},{default:c(()=>[i(o,{class:"w-100",modelValue:s.targetData.model,"onUpdate:modelValue":e[3]||(e[3]=m=>s.targetData.model=m),placeholder:"",name:"model"},null,8,["modelValue"])]),_:1})]),t("div",oh,[e[20]||(e[20]=t("label",{class:"d-flex align-items-center fs-6 fw-semibold mb-2"}," Linear ID (in) ",-1)),i(a,{prop:"linearID"},{default:c(()=>[i(o,{type:"number",class:"w-100",controls:!1,step:"any",modelValue:s.targetData.linearID,"onUpdate:modelValue":e[4]||(e[4]=m=>s.targetData.linearID=m),placeholder:"",name:"linearID"},null,8,["modelValue"])]),_:1})])]),t("div",nh,[t("div",ah,[t("label",ih,[e[23]||(e[23]=t("span",null,"Rod OD (in)",-1)),i(d,{placement:"top",width:250,trigger:"hover"},{reference:c(()=>e[21]||(e[21]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7"},null,-1)])),default:c(()=>[e[22]||(e[22]=t("span",null,"The outside diameter of the pump's rod or plunger. ",-1))]),_:1})]),i(a,{prop:"rodOD"},{default:c(()=>[i(o,{type:"number",class:"w-100",controls:!1,step:"any",modelValue:s.targetData.rodOD,"onUpdate:modelValue":e[5]||(e[5]=m=>s.targetData.rodOD=m),placeholder:"",name:"rodOD"},null,8,["modelValue"])]),_:1})]),t("div",dh,[e[24]||(e[24]=t("label",{class:"d-flex align-items-center fs-6 fw-semibold mb-2"}," Stroke Length (in) ",-1)),i(a,{prop:"strokeLength"},{default:c(()=>[i(o,{type:"number",class:"w-100",controls:!1,step:"any",modelValue:s.targetData.strokeLength,"onUpdate:modelValue":e[6]||(e[6]=m=>s.targetData.strokeLength=m),placeholder:"",name:"strokeLength"},null,8,["modelValue"])]),_:1})])]),t("div",rh,[t("div",uh,[t("label",ch,[e[27]||(e[27]=t("span",null,"Efficiency (%)",-1)),i(d,{placement:"top",width:250,trigger:"hover"},{reference:c(()=>e[25]||(e[25]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7"},null,-1)])),default:c(()=>[e[26]||(e[26]=t("span",null,"The efficiency of the pump, indicating how effectively it converts mechanical power to hydraulic power. ",-1))]),_:1})]),i(a,{prop:"efficiency"},{default:c(()=>[i(o,{type:"number",class:"w-100",controls:!1,step:"any",modelValue:s.targetData.efficiency,"onUpdate:modelValue":e[7]||(e[7]=m=>s.targetData.efficiency=m),placeholder:"",name:"efficiency",min:0,max:100},null,8,["modelValue"])]),_:1})]),t("div",fh,[t("label",mh,[e[30]||(e[30]=t("span",null,"Stroke (stroke/min)",-1)),i(d,{placement:"top",width:250,trigger:"hover"},{reference:c(()=>e[28]||(e[28]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7"},null,-1)])),default:c(()=>[e[29]||(e[29]=t("span",null,"The number of strokes (up-and-down movements) the pump makes per minute. ",-1))]),_:1})]),i(a,{prop:"stroke"},{default:c(()=>[i(o,{type:"number",class:"w-100",controls:!1,step:"any",modelValue:s.targetData.stroke,"onUpdate:modelValue":e[8]||(e[8]=m=>s.targetData.stroke=m),placeholder:"",name:"stroke"},null,8,["modelValue"])]),_:1})])]),t("div",ph,[t("div",gh,[t("label",bh,[e[33]||(e[33]=t("span",null,"Displacement (bbl/stroke)",-1)),i(d,{placement:"top",width:250,trigger:"hover"},{reference:c(()=>e[31]||(e[31]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7"},null,-1)])),default:c(()=>[e[32]||(e[32]=t("span",null,"The volume of fluid displaced by the pump with each stroke. ",-1))]),_:1})]),i(a,{prop:"displacement"},{default:c(()=>[i(o,{type:"number",class:"w-100",controls:!1,step:"any",modelValue:s.targetData.displacement,"onUpdate:modelValue":e[9]||(e[9]=m=>s.targetData.displacement=m),placeholder:"",name:"displacement"},null,8,["modelValue"])]),_:1})]),t("div",vh,[t("label",hh,[e[36]||(e[36]=t("span",null,"Rate (gpm)",-1)),i(d,{placement:"top",width:250,trigger:"hover"},{reference:c(()=>e[34]||(e[34]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7"},null,-1)])),default:c(()=>[e[35]||(e[35]=t("span",null,"The flow rate of fluid delivered by the pump. ",-1))]),_:1})]),i(a,{prop:"rate"},{default:c(()=>[i(o,{type:"number",class:"w-100",controls:!1,step:"any",modelValue:s.targetData.rate,"onUpdate:modelValue":e[10]||(e[10]=m=>s.targetData.rate=m),placeholder:"",name:"rate"},null,8,["modelValue"])]),_:1})])]),t("div",yh,[t("button",{type:"button",id:"kt_modal_add_customer_cancel",class:"btn text-gray-700 btn-sm me-3 btn-blue",onClick:e[11]||(e[11]=(...m)=>s.hide&&s.hide(...m)),disabled:s.loading}," Discard ",8,wh),t("button",{"data-kt-indicator":s.loading?"on":null,class:"btn btn-sm btn-primary",type:"submit",disabled:s.loading},[s.loading?U("",!0):($(),V("span",Sh," Save ")),s.loading?($(),V("span",_h,e[37]||(e[37]=[L(" Please wait... "),t("span",{class:"spinner-border spinner-border-sm align-middle ms-2"},null,-1)]))):U("",!0)],8,$h)])]),_:1},8,["onSubmit","model","rules"])]),_:1},8,["modelValue"])}const Zt=Q(Yv,[["render",kh]]),Dh=G({name:"pump-details-modal",components:{SvgIcon:X,PumpModal:Zt,PumpDurationModal:Xt},props:{loadPage:{type:Function,required:!1}},setup(s){const e=Kt(),v=at(),b=y(!1),f=y(null),r=y(!1),l=y(""),o=y(null),a=y(null);J(l,h=>{h!==""&&d()}),J(b,h=>{h==!0&&d()});const d=async()=>{r.value=!0,v.getPumpDetails({id:l.value,callback:{onSuccess:h=>{f.value={...h}},onFinish:h=>{r.value=!1}}})},n=()=>{b.value=!0},u=()=>{b.value=!1,l.value="",f.value=null},m=h=>{l.value=h.toString()},p=()=>{d(),s!=null&&s.loadPage&&s.loadPage()},D=()=>{var h,_;(h=a==null?void 0:a.value)==null||h.setId(l==null?void 0:l.value,""),(_=a==null?void 0:a.value)==null||_.show()},k=h=>{var _,H;(_=a==null?void 0:a.value)==null||_.setId(l==null?void 0:l.value,h),(H=a==null?void 0:a.value)==null||H.show()},N=h=>{var _,H;(_=o==null?void 0:o.value)==null||_.setId(h),(H=o==null?void 0:o.value)==null||H.show()},T=h=>{ne.deletionAlert({onConfirmed:()=>{w(h)}})},w=h=>{e.deletePumpDuration({id:h,callback:{onSuccess:_=>{d(),s!=null&&s.loadPage&&s.loadPage()}}})};return{id:l,modal:b,loading:r,data:f,pumpModal:o,pumpDurationModal:a,show:n,hide:u,setId:m,formatDate:ke,reloadPage:p,numberWithCommas:ge,toggleAddPumpDurationModal:D,toggleEditPumpDurationModal:k,toggleEditPumpModal:N,deleteDuration:T}}}),Ih={class:"d-flex align-items-center w-100"},Vh={key:0,class:"text-center"},Ph={key:1,class:"d-flex flex-column gap-6 h-100"},Ch={class:"gap-5 d-flex flex-column"},Th={class:"mb-0 text-primary"},Fh={class:"d-flex flex-column gap-3"},xh={class:"d-flex flex-wrap align-items-center justify-content-between border-bottom-dashed border-bottom-1 border-blue-light pb-3"},Nh={class:"d-flex flex-wrap align-items-center justify-content-between border-bottom-dashed border-bottom-1 border-blue-light pb-3"},Ah={class:"fw-700 fs-6 text-gray-700"},Hh={class:"d-flex flex-wrap align-items-center justify-content-between"},qh={class:"fw-700 fs-6 text-dark"},Mh={class:"d-flex"},jh={class:"d-flex flex-wrap gap-3 align-items-center justify-content-center"},zh={class:"border border-gray-300 border-dashed rounded py-3 px-4"},Eh={class:"fw-700 fs-6 text-gray-700"},Lh={class:"border border-gray-300 border-dashed rounded py-3 px-4"},Rh={class:"fw-700 fs-6 text-gray-700"},Bh={class:"border border-gray-300 border-dashed rounded py-3 px-4"},Oh={class:"fw-700 fs-6 text-gray-700"},Uh={class:"border border-gray-300 border-dashed rounded py-3 px-4"},Wh={class:"fw-700 fs-6 text-gray-700"},Gh={class:"border border-gray-300 border-dashed rounded py-3 px-4"},Yh={class:"fw-700 fs-6 text-gray-700"},Jh={class:"border border-gray-300 border-dashed rounded py-3 px-4"},Qh={class:"fw-700 fs-6 text-gray-700"},Kh={class:"border border-gray-300 border-dashed rounded py-3 px-4"},Xh={class:"fw-700 fs-6 text-gray-700"},Zh={class:"flex-fill overflow-hidden-y d-flex flex-column"},ey={class:"d-flex flex-column gap-3 overflow-auto"},ty={class:"d-flex align-items-center justify-content-between flex-fill"},sy={class:"text-gray-600 fs-6 fw-semibold"},ly={class:"text-gray-800 fs-6 fw-300 me-10"},oy={class:"d-flex align-items-center ms-auto"},ny=["onClick"],ay={class:"svg-icon svg-icon-3"},iy=["onClick"],dy={class:"svg-icon svg-icon-3 text-danger"},ry={class:"modal-footer d-flex justify-content-center align-items-center gap-7 mt-6"},uy=["disabled"],cy=["data-kt-indicator","disabled"],fy=["data-kt-indicator","disabled"];function my(s,e,v,b,f,r){const l=S("SvgIcon"),o=S("el-empty"),a=S("el-dialog"),d=S("PumpModal"),n=S("PumpDurationModal");return $(),V(B,null,[i(a,{modelValue:s.modal,"onUpdate:modelValue":e[4]||(e[4]=u=>s.modal=u),"show-close":!1,width:"500","align-center":""},{header:c(()=>[t("div",Ih,[e[5]||(e[5]=t("h3",{class:"modal-title"},"Pump Details",-1)),t("span",{class:"cursor-pointer ms-auto",onClick:e[0]||(e[0]=(...u)=>s.hide&&s.hide(...u))},[i(l,{icon:"closeModalIcon"})])])]),default:c(()=>{var u,m,p,D,k,N,T,w,h,_,H,C,F,j,x,A,M,z,q,R,Y,se,oe;return[t("div",null,[s.loading?($(),V("div",Vh,e[6]||(e[6]=[t("div",{class:"spinner-border text-primary",role:"status"},[t("span",{class:"sr-only"},"Loading...")],-1)]))):($(),V("div",Ph,[t("div",Ch,[t("h3",Th,I(((u=s.data)==null?void 0:u.description)||""),1),t("div",Fh,[t("div",xh,[e[7]||(e[7]=t("span",{class:"fw-semibold fs-6 text-gray-700"},"In Use",-1)),t("span",{class:_e(["fw-700 fs-6",{"text-success":(m=s.data)==null?void 0:m.inUse,"text-danger":!((p=s.data)!=null&&p.inUse)}])},I((D=s.data)!=null&&D.inUse?"Active":"Inactive"),3)]),t("div",Nh,[e[8]||(e[8]=t("span",{class:"fw-semibold fs-6 text-gray-700"},"Model",-1)),t("span",Ah,I(((k=s.data)==null?void 0:k.model)||""),1)]),t("div",Hh,[e[9]||(e[9]=t("span",{class:"fw-semibold fs-6 text-black"},"Total Duration",-1)),t("span",qh,I(`${((N=s.data)==null?void 0:N.totalDurations)||"0"} hrs`),1)])]),t("div",Mh,[t("div",jh,[t("div",zh,[t("div",Eh,I(`${(T=s.data)!=null&&T.linearID?s.numberWithCommas((w=s.data)==null?void 0:w.linearID):""} (in)`),1),e[10]||(e[10]=t("div",{class:"fw-semibold fs-6 text-danger"},"Linear ID",-1))]),t("div",Lh,[t("div",Rh,I(`${(h=s.data)!=null&&h.rodOD?s.numberWithCommas((_=s.data)==null?void 0:_.rodOD):""} (in)`),1),e[11]||(e[11]=t("div",{class:"fw-semibold fs-6 text-primary"},"Rod OD",-1))]),t("div",Bh,[t("div",Oh,I(`${(H=s.data)!=null&&H.strokeLength?s.numberWithCommas((C=s.data)==null?void 0:C.strokeLength):""} (in)`),1),e[12]||(e[12]=t("div",{class:"fw-semibold fs-6 text-primary"},"Stroke Length",-1))]),t("div",Uh,[t("div",Wh,I(`${(F=s.data)!=null&&F.efficiency?s.numberWithCommas((j=s.data)==null?void 0:j.efficiency):""} (%)`),1),e[13]||(e[13]=t("div",{class:"fw-semibold fs-6 text-primary"},"Efficiency",-1))]),t("div",Gh,[t("div",Yh,I(`${(x=s.data)!=null&&x.stroke?s.numberWithCommas((A=s.data)==null?void 0:A.stroke):""} (stroke/min)`),1),e[14]||(e[14]=t("div",{class:"fw-semibold fs-6 text-primary"},"Stroke",-1))]),t("div",Jh,[t("div",Qh,I(`${(M=s.data)!=null&&M.displacement?s.numberWithCommas((z=s.data)==null?void 0:z.displacement):""} (bbl/stroke)`),1),e[15]||(e[15]=t("div",{class:"fw-semibold fs-6 text-primary"},"Displacement",-1))]),t("div",Kh,[t("div",Xh,I(`${(q=s.data)!=null&&q.rate?s.numberWithCommas((R=s.data)==null?void 0:R.rate):""} (gpm)`),1),e[16]||(e[16]=t("div",{class:"fw-semibold fs-6 text-primary"},"Rate",-1))])])])]),e[17]||(e[17]=t("div",null,[t("p",{class:"fw-semibold fs-6 text-dark mb-0"},"Time Distribution")],-1)),t("div",Zh,[t("div",ey,[((se=(Y=s.data)==null?void 0:Y.durations)==null?void 0:se.length)===0?($(),W(o,{key:0,"image-size":100})):($(!0),V(B,{key:1},Z((oe=s.data)==null?void 0:oe.durations,le=>($(),V("div",{key:le.id,class:"d-flex flex-wrap align-items-center"},[t("div",ty,[t("span",sy,I(s.formatDate(le==null?void 0:le.createdAt,"DD MMM YYYY HH:mm")),1),t("span",ly,I(`${le==null?void 0:le.durations} hrs`),1)]),t("div",oy,[t("button",{class:"btn btn-icon btn-sm btn-blue me-3",onClick:O=>s.toggleEditPumpDurationModal(le==null?void 0:le.id)},[t("span",ay,[i(l,{icon:"newReportIcon"})])],8,ny),t("button",{class:"btn btn-icon btn-sm btn-blue",onClick:O=>s.deleteDuration(le==null?void 0:le.id)},[t("span",dy,[i(l,{icon:"trashIcon"})])],8,iy)])]))),128))])])]))]),t("div",ry,[t("button",{type:"button",id:"kt_modal_add_customer_cancel",class:"btn text-gray-700 btn-sm btn-blue",disabled:s.loading,onClick:e[1]||(e[1]=(...le)=>s.hide&&s.hide(...le))}," Close ",8,uy),t("button",{"data-kt-indicator":s.loading?"on":null,class:"btn btn-sm btn-primary",type:"button",disabled:s.loading,onClick:e[2]||(e[2]=()=>{var le,O;return s.toggleEditPumpModal(((O=(le=s.data)==null?void 0:le.id)==null?void 0:O.toString())||"")})}," Edit Pump ",8,cy),t("button",{"data-kt-indicator":s.loading?"on":null,class:"btn btn-sm btn-success",type:"button",disabled:s.loading,onClick:e[3]||(e[3]=(...le)=>s.toggleAddPumpDurationModal&&s.toggleAddPumpDurationModal(...le))}," Add Duration ",8,fy)])]}),_:1},8,["modelValue"]),i(d,{ref:"pumpModal",loadPage:s.reloadPage},null,8,["loadPage"]),i(n,{ref:"pumpDurationModal",loadPage:s.reloadPage},null,8,["loadPage"])],64)}const py=Q(Dh,[["render",my],["__scopeId","data-v-1d5a8bb8"]]),gy=G({name:"pump-equipment",components:{SvgIcon:X,PumpModal:Zt,PumpDetailsModal:py,PumpDurationModal:Xt,NoEntries:be},props:{loadPage:{type:Function,required:!0}},setup(s){const e=at(),v=y(null),b=y(null),f=y(null),r=y([]),l=y(!1),o=ee("dailyReport");ie(()=>{o!=null&&o.getDailyReportId()&&d()});const a=()=>{d(),s!=null&&s.loadPage&&(s==null||s.loadPage())},d=async(N={dailyReportId:o==null?void 0:o.getDailyReportId(),page:1,limit:200})=>{l.value=!0,e.getPumps({params:N,callback:{onSuccess:T=>{r.value=T==null?void 0:T.items},onFinish:T=>{l.value=!1}}})},n=()=>{var N;(N=v==null?void 0:v.value)==null||N.show()},u=(N,T,w="")=>{var h,_;N.stopPropagation(),(h=f==null?void 0:f.value)==null||h.setId(T,w),(_=f==null?void 0:f.value)==null||_.show()},m=(N,T)=>{var w,h;N.stopPropagation(),(w=v==null?void 0:v.value)==null||w.setId(T),(h=v==null?void 0:v.value)==null||h.show()},p=(N,T)=>{N.stopPropagation(),ne.deletionAlert({onConfirmed:()=>{D(T)}})},D=async N=>{l.value=!0,e.deletePump({id:N,callback:{onSuccess:T=>{a()},onFinish:T=>{l.value=!1}}})};return{pumpModal:v,loadingPump:l,pumpDetailsModal:b,pumpDurationModal:f,pumpEquipmentList:r,deletePump:p,loadData:a,toggleAddPumpModal:n,toggleEditPumpModal:m,togglePumpDetailsModal:N=>{var T,w;(T=b==null?void 0:b.value)==null||T.setId(N),(w=b==null?void 0:b.value)==null||w.show()},toggleAddPumpDurationModal:u}}}),by={class:"row gap-10"},vy={key:0,class:"text-center"},hy=["onClick"],yy={class:"card-body gap-5 d-flex flex-column p-6"},wy={class:"mb-0 text-primary"},$y={class:"d-flex flex-column gap-3"},Sy={class:"d-flex flex-wrap align-items-center justify-content-between border-bottom-dashed border-bottom-1 border-blue-light pb-3"},_y={class:"d-flex flex-wrap align-items-center justify-content-between border-bottom-dashed border-bottom-1 border-blue-light pb-3"},ky={class:"fw-700 fs-7 text-gray-700"},Dy={class:"d-flex flex-wrap align-items-center justify-content-between"},Iy={class:"fw-700 fs-7 text-dark"},Vy={class:"d-flex align-items-center gap-2 position-absolute top-0 end-0 transform-top-50"},Py=["onClick"],Cy={class:"svg-icon svg-icon-3"},Ty=["onClick"],Fy={class:"svg-icon svg-icon-3"},xy=["onClick"],Ny={class:"svg-icon svg-icon-3 text-danger"};function Ay(s,e,v,b,f,r){const l=S("NoEntries"),o=S("SvgIcon"),a=S("el-tooltip"),d=S("PumpModal"),n=S("PumpDetailsModal"),u=S("PumpDurationModal");return $(),V(B,null,[t("div",null,[e[4]||(e[4]=t("h4",{class:"my-7 text-black fw-700"},"Pump Equipment",-1)),t("div",by,[s.loadingPump?($(),V("div",vy,e[0]||(e[0]=[t("div",{class:"spinner-border text-primary",role:"status"},[t("span",{class:"sr-only"},"Loading...")],-1)]))):($(),V(B,{key:1},[s.pumpEquipmentList.length===0?($(),W(l,{key:0,addNew:s.toggleAddPumpModal},null,8,["addNew"])):($(!0),V(B,{key:1},Z(s.pumpEquipmentList,m=>($(),V("div",{key:m==null?void 0:m.id,class:"col-4 card rounded-3 shadow-sm border-top border-top-2 border-primary position-relative card-item cursor-pointer",onClick:p=>s.togglePumpDetailsModal((m==null?void 0:m.id)||"")},[t("div",yy,[t("h5",wy,I(m==null?void 0:m.description),1),t("div",$y,[t("div",Sy,[e[1]||(e[1]=t("span",{class:"fw-semibold fs-7 text-gray-700"},"In Use",-1)),t("span",{class:_e(["fw-700 fs-7",{"text-success":m==null?void 0:m.inUse,"text-danger":!(m!=null&&m.inUse)}])},I(m!=null&&m.inUse?"Active":"Inactive"),3)]),t("div",_y,[e[2]||(e[2]=t("span",{class:"fw-semibold fs-7 text-gray-700"},"Model",-1)),t("span",ky,I(m==null?void 0:m.model),1)]),t("div",Dy,[e[3]||(e[3]=t("span",{class:"fw-semibold fs-7 text-gray-700"},"Total Duration",-1)),t("span",Iy,I(`${(m==null?void 0:m.totalDurations)||"0"} hrs`),1)])])]),t("div",Vy,[i(a,{content:"Add Duration",placement:"top",effect:"customize"},{default:c(()=>[t("button",{class:"btn rounded-circle btn-icon btn-sm btn-action bg-light btn-export",onClick:p=>s.toggleAddPumpDurationModal(p,(m==null?void 0:m.id)||"")},[t("span",Cy,[i(o,{icon:"durationIcon"})])],8,Py)]),_:2},1024),i(a,{content:"Edit Pump",placement:"top",effect:"customize"},{default:c(()=>[t("button",{class:"btn rounded-circle btn-icon btn-sm btn-action bg-light btn-edit",onClick:p=>s.toggleEditPumpModal(p,(m==null?void 0:m.id)||"")},[t("span",Fy,[i(o,{icon:"pencilIcon"})])],8,Ty)]),_:2},1024),i(a,{content:"Delete Pump",placement:"top",effect:"customize"},{default:c(()=>[t("button",{class:"btn rounded-circle btn-icon btn-sm btn-action bg-light btn-delete",onClick:p=>s.deletePump(p,(m==null?void 0:m.id)||"")},[t("span",Ny,[i(o,{icon:"trashIcon"})])],8,xy)]),_:2},1024)])],8,hy))),128))],64))])]),i(d,{ref:"pumpModal",loadPage:s.loadData},null,8,["loadPage"]),i(n,{ref:"pumpDetailsModal",loadPage:s.loadData},null,8,["loadPage"]),i(u,{ref:"pumpDurationModal",loadPage:s.loadData},null,8,["loadPage"])],64)}const Hy=Q(gy,[["render",Ay]]),ct=ae("solidControlEquipment",()=>({getSolidControlEquipments:async({params:r,callback:l})=>{var d;const o=g.get(l,"onSuccess",g.noop),a=g.get(l,"onFinish",g.noop);try{P.setHeader();const n=await P.getWithParams("solidControlEquipments",r);o(((d=n.data)==null?void 0:d.data)||n.data)}catch(n){E(n,l)}finally{a()}},getSolidControlEquipmentDetails:async({id:r,callback:l})=>{var d;const o=g.get(l,"onSuccess",g.noop),a=g.get(l,"onFinish",g.noop);try{P.setHeader();const n=await P.get(`solidControlEquipments/${r}`);o(((d=n.data)==null?void 0:d.data)||n.data)}catch(n){E(n,l)}finally{a()}},updateSolidControlEquipment:async({id:r,params:l,callback:o})=>{var n;const a=g.get(o,"onSuccess",g.noop),d=g.get(o,"onFinish",g.noop);try{P.setHeader();const u=await P.put(`solidControlEquipments/${r}`,l);a(((n=u.data)==null?void 0:n.data)||u.data)}catch(u){E(u,o)}finally{d()}},deleteSolidControlEquipment:async({id:r,callback:l})=>{var d;const o=g.get(l,"onSuccess",g.noop),a=g.get(l,"onFinish",g.noop);try{P.setHeader();const n=await P.delete(`solidControlEquipments/${r}`);o(((d=n.data)==null?void 0:d.data)||n.data)}catch(n){E(n,l)}finally{a()}},createSolidControlEquipment:async({params:r,callback:l})=>{var d;const o=g.get(l,"onSuccess",g.noop),a=g.get(l,"onFinish",g.noop);try{P.setHeader();const n=await P.post("solidControlEquipments",r);o(((d=n.data)==null?void 0:d.data)||n.data)}catch(n){E(n,l)}finally{a()}}})),es=ae("solidControlEquipmentInput",()=>({getSolidControlEquipmentInputDetails:async({id:f,callback:r})=>{var a;const l=g.get(r,"onSuccess",g.noop),o=g.get(r,"onFinish",g.noop);try{P.setHeader();const d=await P.get(`solidControlEquipmentInputs/${f}`);l(((a=d.data)==null?void 0:a.data)||d.data)}catch(d){E(d,r)}finally{o()}},updateSolidControlEquipmentInput:async({id:f,params:r,callback:l})=>{var d;const o=g.get(l,"onSuccess",g.noop),a=g.get(l,"onFinish",g.noop);try{P.setHeader();const n=await P.put(`solidControlEquipmentInputs/${f}`,r);o(((d=n.data)==null?void 0:d.data)||n.data)}catch(n){E(n,l)}finally{a()}},deleteSolidControlEquipmentInput:async({id:f,callback:r})=>{var a;const l=g.get(r,"onSuccess",g.noop),o=g.get(r,"onFinish",g.noop);try{P.setHeader();const d=await P.delete(`solidControlEquipmentInputs/${f}`);l(((a=d.data)==null?void 0:a.data)||d.data)}catch(d){E(d,r)}finally{o()}},createSolidControlEquipmentInput:async({params:f,callback:r})=>{var a;const l=g.get(r,"onSuccess",g.noop),o=g.get(r,"onFinish",g.noop);try{P.setHeader();const d=await P.post("solidControlEquipmentInputs",f);l(((a=d.data)==null?void 0:a.data)||d.data)}catch(d){E(d,r)}finally{o()}}})),ts=ae("solidControlEquipmentTime",()=>({getSolidControlEquipmentTimeDetails:async({id:f,callback:r})=>{var a;const l=g.get(r,"onSuccess",g.noop),o=g.get(r,"onFinish",g.noop);try{P.setHeader();const d=await P.get(`solidControlEquipmentTimes/${f}`);l(((a=d.data)==null?void 0:a.data)||d.data)}catch(d){E(d,r)}finally{o()}},updateSolidControlEquipmentTime:async({id:f,params:r,callback:l})=>{var d;const o=g.get(l,"onSuccess",g.noop),a=g.get(l,"onFinish",g.noop);try{P.setHeader();const n=await P.put(`solidControlEquipmentTimes/${f}`,r);o(((d=n.data)==null?void 0:d.data)||n.data)}catch(n){E(n,l)}finally{a()}},deleteSolidControlEquipmentTime:async({id:f,callback:r})=>{var a;const l=g.get(r,"onSuccess",g.noop),o=g.get(r,"onFinish",g.noop);try{P.setHeader();const d=await P.delete(`solidControlEquipmentTimes/${f}`);l(((a=d.data)==null?void 0:a.data)||d.data)}catch(d){E(d,r)}finally{o()}},createSolidControlEquipmentTime:async({params:f,callback:r})=>{var a;const l=g.get(r,"onSuccess",g.noop),o=g.get(r,"onFinish",g.noop);try{P.setHeader();const d=await P.post("solidControlEquipmentTimes",f);l(((a=d.data)==null?void 0:a.data)||d.data)}catch(d){E(d,r)}finally{o()}}})),qy=G({name:"solid-duration-modal",components:{SvgIcon:X},props:{loadPage:{type:Function,required:!1}},setup(s){const e=ts(),v=y(!1),b=y({duration:null}),f=y(null),r=y(!1),l=y(""),o=y("");J(o,T=>{T!==""&&a()}),J(v,T=>{var w;T===!1&&(o.value="",l.value="",N(),(w=f==null?void 0:f.value)==null||w.resetFields())});const a=async()=>{e.getSolidControlEquipmentTimeDetails({id:o.value,callback:{onSuccess:T=>{b.value={...T}}}})},d=async T=>{r.value=!0,e.updateSolidControlEquipmentTime({id:o.value,params:T,callback:{onSuccess:w=>{s!=null&&s.loadPage&&(s==null||s.loadPage()),m()},onFinish:w=>{r.value=!1}}})},n=async T=>{r.value=!0,e.createSolidControlEquipmentTime({params:T,callback:{onSuccess:w=>{s!=null&&s.loadPage&&(s==null||s.loadPage()),m()},onFinish:w=>{r.value=!1}}})},u=()=>{v.value=!0},m=()=>{v.value=!1},p=(T,w)=>{l.value=T,o.value=w},D=y({duration:[{required:!0,message:"Please type Duration",trigger:"blur"}]}),k=()=>{f.value&&f.value.validate(T=>{var w;if(T){const h={solidControlEquipmentId:l==null?void 0:l.value,duration:Number((w=b==null?void 0:b.value)==null?void 0:w.duration)};o!=null&&o.value?d(h):n(h)}})},N=()=>{b.value={duration:null}};return{id:o,modal:v,rules:D,loading:r,targetData:b,formRef:f,show:u,hide:m,submit:k,setId:p,reset:N}}}),My={class:"d-flex align-items-center w-100"},jy={class:"modal-title"},zy={class:"row"},Ey={class:"col-12 fv-row d-flex flex-column justify-content-stretch"},Ly={class:"modal-footer d-flex justify-content-center align-items-center"},Ry=["disabled"],By=["data-kt-indicator","disabled"],Oy={key:0,class:"indicator-label"},Uy={key:1,class:"indicator-progress"};function Wy(s,e,v,b,f,r){const l=S("SvgIcon"),o=S("el-input"),a=S("el-form-item"),d=S("el-form"),n=S("el-dialog");return $(),W(n,{modelValue:s.modal,"onUpdate:modelValue":e[3]||(e[3]=u=>s.modal=u),"show-close":!1,width:"500","align-center":""},{header:c(()=>[t("div",My,[t("h3",jy,I(`${s.id?"Edit Solid duration":"Add Solid duration"}`),1),t("span",{class:"cursor-pointer ms-auto",onClick:e[0]||(e[0]=(...u)=>s.hide&&s.hide(...u))},[i(l,{icon:"closeModalIcon"})])])]),default:c(()=>[t("div",null,[i(d,{id:"solid_duration_form",onSubmit:de(s.submit,["prevent"]),model:s.targetData,rules:s.rules,ref:"formRef",class:"form"},{default:c(()=>[t("div",zy,[t("div",Ey,[e[4]||(e[4]=t("label",{class:"d-flex align-items-center fs-6 fw-semibold mb-2 text-break required"},"Duration (hr) ",-1)),i(a,{prop:"duration",class:"mt-auto"},{default:c(()=>[i(o,{type:"number",class:"w-100",controls:!1,step:"any",modelValue:s.targetData.duration,"onUpdate:modelValue":e[1]||(e[1]=u=>s.targetData.duration=u),placeholder:"",name:"duration"},null,8,["modelValue"])]),_:1})])]),t("div",Ly,[t("button",{type:"button",id:"kt_modal_add_customer_cancel",class:"btn text-gray-700 btn-sm me-3 btn-blue",onClick:e[2]||(e[2]=(...u)=>s.hide&&s.hide(...u)),disabled:s.loading}," Discard ",8,Ry),t("button",{"data-kt-indicator":s.loading?"on":null,class:"btn btn-sm btn-primary",type:"submit",disabled:s.loading},[s.loading?U("",!0):($(),V("span",Oy," Save ")),s.loading?($(),V("span",Uy,e[5]||(e[5]=[L(" Please wait... "),t("span",{class:"spinner-border spinner-border-sm align-middle ms-2"},null,-1)]))):U("",!0)],8,By)])]),_:1},8,["onSubmit","model","rules"])])]),_:1},8,["modelValue"])}const ss=Q(qy,[["render",Wy]]),Gy=G({name:"solid-input-modal",components:{SvgIcon:X},props:{loadPage:{type:Function,required:!1}},setup(s){const e=es(),v=y(!1),b=y({description:"",value:null,units:""}),f=y(null),r=y(!1),l=y(""),o=y("");J(o,T=>{T!==""&&a()}),J(v,T=>{var w;T===!1&&(o.value="",l.value="",N(),(w=f==null?void 0:f.value)==null||w.resetFields())});const a=async()=>{e.getSolidControlEquipmentInputDetails({id:o.value,callback:{onSuccess:T=>{b.value={...T}}}})},d=async T=>{r.value=!0,e.updateSolidControlEquipmentInput({id:o.value,params:T,callback:{onSuccess:w=>{s!=null&&s.loadPage&&(s==null||s.loadPage()),m()},onFinish:w=>{r.value=!1}}})},n=async T=>{r.value=!0,e.createSolidControlEquipmentInput({params:T,callback:{onSuccess:w=>{s!=null&&s.loadPage&&(s==null||s.loadPage()),m()},onFinish:w=>{r.value=!1}}})},u=()=>{v.value=!0},m=()=>{v.value=!1},p=(T,w)=>{l.value=T,o.value=w},D=y({description:[{required:!0,message:"Please type Description",trigger:"blur"}],value:[{required:!0,message:"Please type Value",trigger:"blur"}],units:[{required:!0,message:"Please type Units",trigger:"blur"}]}),k=()=>{f.value&&f.value.validate(T=>{var w;if(T){const h={...b==null?void 0:b.value,solidControlEquipmentId:l==null?void 0:l.value,value:Number((w=b==null?void 0:b.value)==null?void 0:w.value)};o!=null&&o.value?d(h):n(h)}})},N=()=>{b.value={description:"",value:null,units:""}};return{id:o,modal:v,rules:D,loading:r,targetData:b,formRef:f,show:u,hide:m,submit:k,setId:p,reset:N}}}),Yy={class:"d-flex align-items-center w-100"},Jy={class:"modal-title"},Qy={class:"row"},Ky={class:"col-12 fv-row d-flex flex-column justify-content-stretch"},Xy={class:"col-12 fv-row d-flex flex-column justify-content-stretch"},Zy={class:"col-12 fv-row d-flex flex-column justify-content-stretch"},e0={class:"modal-footer d-flex justify-content-center align-items-center"},t0=["disabled"],s0=["data-kt-indicator","disabled"],l0={key:0,class:"indicator-label"},o0={key:1,class:"indicator-progress"};function n0(s,e,v,b,f,r){const l=S("SvgIcon"),o=S("el-input"),a=S("el-form-item"),d=S("el-form"),n=S("el-dialog");return $(),W(n,{modelValue:s.modal,"onUpdate:modelValue":e[5]||(e[5]=u=>s.modal=u),"show-close":!1,width:"500","align-center":""},{header:c(()=>[t("div",Yy,[t("h3",Jy,I(`${s.id?"Edit Input":"Add Input"}`),1),t("span",{class:"cursor-pointer ms-auto",onClick:e[0]||(e[0]=(...u)=>s.hide&&s.hide(...u))},[i(l,{icon:"closeModalIcon"})])])]),default:c(()=>[t("div",null,[i(d,{id:"solid_duration_form",onSubmit:de(s.submit,["prevent"]),model:s.targetData,rules:s.rules,ref:"formRef",class:"form"},{default:c(()=>[t("div",Qy,[t("div",Ky,[e[6]||(e[6]=t("label",{class:"d-flex align-items-center fs-6 fw-semibold mb-2 text-break required"},"Description ",-1)),i(a,{prop:"description",class:"mt-auto"},{default:c(()=>[i(o,{class:"w-100",modelValue:s.targetData.description,"onUpdate:modelValue":e[1]||(e[1]=u=>s.targetData.description=u),placeholder:"",name:"description"},null,8,["modelValue"])]),_:1})]),t("div",Xy,[e[7]||(e[7]=t("label",{class:"d-flex align-items-center fs-6 fw-semibold mb-2 text-break required"},"Value ",-1)),i(a,{prop:"value",class:"mt-auto"},{default:c(()=>[i(o,{class:"w-100",modelValue:s.targetData.value,"onUpdate:modelValue":e[2]||(e[2]=u=>s.targetData.value=u),placeholder:"",name:"value",type:"number",controls:!1,step:"any"},null,8,["modelValue"])]),_:1})]),t("div",Zy,[e[8]||(e[8]=t("label",{class:"d-flex align-items-center fs-6 fw-semibold mb-2 text-break required"},"Unit ",-1)),i(a,{prop:"units",class:"mt-auto"},{default:c(()=>[i(o,{class:"w-100",modelValue:s.targetData.units,"onUpdate:modelValue":e[3]||(e[3]=u=>s.targetData.units=u),placeholder:"",name:"units"},null,8,["modelValue"])]),_:1})])]),t("div",e0,[t("button",{type:"button",id:"kt_modal_add_customer_cancel",class:"btn text-gray-700 btn-sm me-3 btn-blue",onClick:e[4]||(e[4]=(...u)=>s.hide&&s.hide(...u)),disabled:s.loading}," Discard ",8,t0),t("button",{"data-kt-indicator":s.loading?"on":null,class:"btn btn-sm btn-primary",type:"submit",disabled:s.loading},[s.loading?U("",!0):($(),V("span",l0," Save ")),s.loading?($(),V("span",o0,e[9]||(e[9]=[L(" Please wait... "),t("span",{class:"spinner-border spinner-border-sm align-middle ms-2"},null,-1)]))):U("",!0)],8,s0)])]),_:1},8,["onSubmit","model","rules"])])]),_:1},8,["modelValue"])}const ls=Q(Gy,[["render",n0]]),a0=ae("solidControlEquipmentType",()=>({getSolidControlEquipmentTypes:async({callback:e})=>{var f;const v=g.get(e,"onSuccess",g.noop),b=g.get(e,"onFinish",g.noop);try{P.setHeader();const r=await P.get("solidControlEquipmentTypes");v(((f=r.data)==null?void 0:f.data)||r.data)}catch(r){E(r,e)}finally{b()}}})),i0=G({name:"solid-modal",components:{SvgIcon:X},props:{loadPage:{type:Function,default:()=>{},required:!0}},setup(s){const e=re(),v=ct(),b=a0(),f=y(!1),r={dailyReportId:"",typeId:null,screen:""},l=y(JSON.parse(JSON.stringify(r))),o=y([]),a=y(null),d=y(!1),n=y(""),u=ee("dailyReport");J(n,C=>{C!==""&&(p(),m())}),J(f,C=>{var F;C===!1&&(n.value="",H(),(F=a==null?void 0:a.value)==null||F.resetFields())}),ie(()=>{m()});const m=async()=>{b.getSolidControlEquipmentTypes({callback:{onSuccess:C=>{o.value=C==null?void 0:C.map(F=>({value:F==null?void 0:F.id,key:F==null?void 0:F.name}))}}})},p=async()=>{v.getSolidControlEquipmentDetails({id:n.value,callback:{onSuccess:C=>{var F;l.value={dailyReportId:u==null?void 0:u.getDailyReportId(),typeId:(F=C==null?void 0:C.type)==null?void 0:F.id,screen:C==null?void 0:C.screen}}}})},D=async C=>{d.value=!0,v.updateSolidControlEquipment({id:n.value,params:C,callback:{onSuccess:F=>{s!=null&&s.loadPage&&(s==null||s.loadPage()),T()},onFinish:F=>{d.value=!1}}})},k=async C=>{d.value=!0,v.createSolidControlEquipment({params:C,callback:{onSuccess:F=>{s!=null&&s.loadPage&&(s==null||s.loadPage()),T()},onFinish:F=>{d.value=!1}}})},N=()=>{f.value=!0},T=()=>{f.value=!1},w=C=>{n.value=C.toString()},h=y({typeId:[{required:!0,message:"Please select Type",trigger:"change"}],screen:[{required:!0,message:"Please type Screen",trigger:["change","blur"]}]}),_=()=>{a.value&&a.value.validate(C=>{var F;C&&(n!=null&&n.value?D({...l==null?void 0:l.value,dailyReportId:u==null?void 0:u.getDailyReportId()}):u==null||u.createDailyReport({wellId:(F=e==null?void 0:e.params)==null?void 0:F.id,callback:{onSuccess:j=>{k({...l==null?void 0:l.value,dailyReportId:u==null?void 0:u.getDailyReportId()})}}}))})},H=()=>{l.value=JSON.parse(JSON.stringify(r))};return{id:n,modal:f,rules:h,loading:d,targetData:l,formRef:a,solidTypes:o,show:N,hide:T,submit:_,setId:w,reset:H}}}),d0={class:"d-flex align-items-center w-100"},r0={class:"modal-title"},u0={class:"row g-7 mb-3"},c0={class:"col-12 fv-row d-flex flex-column justify-content-stretch"},f0={class:"row g-7 mb-3"},m0={class:"col-12 fv-row d-flex flex-column justify-content-stretch"},p0={class:"d-flex align-items-center fs-6 fw-semibold mb-2"},g0={class:"modal-footer d-flex justify-content-center align-items-center"},b0=["disabled"],v0=["data-kt-indicator","disabled"],h0={key:0,class:"indicator-label"},y0={key:1,class:"indicator-progress"};function w0(s,e,v,b,f,r){const l=S("SvgIcon"),o=S("el-option"),a=S("el-select"),d=S("el-form-item"),n=S("el-popover"),u=S("el-input"),m=S("el-form"),p=S("el-dialog");return $(),W(p,{modelValue:s.modal,"onUpdate:modelValue":e[4]||(e[4]=D=>s.modal=D),"show-close":!1,width:"500","align-center":""},{header:c(()=>[t("div",d0,[t("h3",r0,I(`${s.id?"Edit Solids Control Equipment":"Add Solids Control Equipment"}`),1),t("span",{class:"cursor-pointer ms-auto",onClick:e[0]||(e[0]=(...D)=>s.hide&&s.hide(...D))},[i(l,{icon:"closeModalIcon"})])])]),default:c(()=>[t("div",null,[i(m,{id:"solid_form",onSubmit:de(s.submit,["prevent"]),model:s.targetData,rules:s.rules,ref:"formRef",class:"form"},{default:c(()=>[t("div",u0,[t("div",c0,[e[5]||(e[5]=t("label",{class:"d-flex align-items-center fs-6 fw-semibold mb-2 text-break required"},"Type ",-1)),i(d,{prop:"typeId"},{default:c(()=>[i(a,{modelValue:s.targetData.typeId,"onUpdate:modelValue":e[1]||(e[1]=D=>s.targetData.typeId=D),placeholder:"",class:"w-100",clearable:""},{default:c(()=>[($(!0),V(B,null,Z(s.solidTypes,D=>($(),W(o,{key:D.value,label:D.key,value:D.value,name:"typeId"},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})])]),t("div",f0,[t("div",m0,[t("label",p0,[e[8]||(e[8]=t("span",{class:"required"},"Screen",-1)),i(n,{placement:"top",width:350,trigger:"hover"},{reference:c(()=>e[6]||(e[6]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7"},null,-1)])),default:c(()=>[e[7]||(e[7]=t("span",null,`The "Screen" typically refers to the mesh size and configuration of the screens used in the equipment. The screen mesh size defines the size of particles that can pass through the screen, while the number of layers may indicate the screen's efficiency in capturing solids. (i.e. 140 x 4)`,-1))]),_:1})]),i(d,{prop:"screen"},{default:c(()=>[i(u,{class:"w-100",modelValue:s.targetData.screen,"onUpdate:modelValue":e[2]||(e[2]=D=>s.targetData.screen=D),placeholder:"",name:"screen"},null,8,["modelValue"])]),_:1})])]),t("div",g0,[t("button",{type:"button",id:"kt_modal_add_customer_cancel",class:"btn text-gray-700 btn-sm me-3 btn-blue",onClick:e[3]||(e[3]=(...D)=>s.hide&&s.hide(...D)),disabled:s.loading}," Discard ",8,b0),t("button",{"data-kt-indicator":s.loading?"on":null,class:"btn btn-sm btn-primary",type:"submit",disabled:s.loading},[s.loading?U("",!0):($(),V("span",h0," Save ")),s.loading?($(),V("span",y0,e[9]||(e[9]=[L(" Please wait... "),t("span",{class:"spinner-border spinner-border-sm align-middle ms-2"},null,-1)]))):U("",!0)],8,v0)])]),_:1},8,["onSubmit","model","rules"])])]),_:1},8,["modelValue"])}const os=Q(i0,[["render",w0]]),$0=G({name:"solid-details-modal",components:{SvgIcon:X,SolidModal:os,SolidDurationModal:ss,SolidInputModal:ls},props:{loadPage:{type:Function,required:!1}},setup(s){const e=ts(),v=es(),b=ct(),f=y(!1),r=y(null),l=y(!1),o=y(""),a=y(null),d=y(null),n=y(null);J(o,j=>{j!==""&&u()}),J(f,j=>{j===!0&&u()});const u=async()=>{l.value=!0,b.getSolidControlEquipmentDetails({id:o.value,callback:{onSuccess:j=>{r.value={...j}},onFinish:j=>{l.value=!1}}})},m=()=>{f.value=!0},p=()=>{f.value=!1,o.value="",r.value=null},D=()=>{u(),s!=null&&s.loadPage&&s.loadPage()},k=j=>{o.value=j.toString()},N=j=>{var x,A;(x=d==null?void 0:d.value)==null||x.setId(o==null?void 0:o.value,""),(A=d==null?void 0:d.value)==null||A.show()},T=j=>{var x,A;(x=d==null?void 0:d.value)==null||x.setId(o==null?void 0:o.value,j),(A=d==null?void 0:d.value)==null||A.show()},w=j=>{var x,A;(x=n==null?void 0:n.value)==null||x.setId(o==null?void 0:o.value,j),(A=n==null?void 0:n.value)==null||A.show()},h=(j,x)=>{var A,M;(A=a==null?void 0:a.value)==null||A.setId(x),(M=a==null?void 0:a.value)==null||M.show()},_=j=>{ne.deletionAlert({onConfirmed:()=>{H(j)}})},H=j=>{v.deleteSolidControlEquipmentInput({id:j,callback:{onSuccess:x=>{u(),s!=null&&s.loadPage&&s.loadPage()}}})},C=(j,x)=>{j.stopPropagation(),ne.deletionAlert({onConfirmed:()=>{F(x)}})},F=j=>{e.deleteSolidControlEquipmentTime({id:j,callback:{onSuccess:x=>{u(),s!=null&&s.loadPage&&s.loadPage()}}})};return{id:o,modal:f,loading:l,data:r,solidModal:a,solidInputModal:n,solidDurationModal:d,show:m,hide:p,setId:k,reloadPage:D,deleteInput:_,deleteDuration:C,formatDate:ke,numberWithCommas:ge,toggleAddSolidDurationModal:N,toggleEditSolidDurationModal:T,toggleEditSolidInputModal:w,toggleEditSolidModal:h}}}),S0={class:"d-flex align-items-center w-100"},_0={key:0,class:"text-center"},k0={key:1,class:"d-flex flex-column gap-6 h-100"},D0={class:"mb-0 text-primary"},I0={key:0,class:"flex-fill overflow-hidden-y d-flex flex-column"},V0={class:"d-flex flex-column gap-3 overflow-auto"},P0={class:"me-auto"},C0={class:"fw-semibold fs-6 text-gray-600"},T0={class:"me-3"},F0={class:"fw-semibold fs-6 text-gray-600"},x0={class:"d-flex align-items-center ms-auto"},N0=["onClick"],A0={class:"svg-icon svg-icon-3"},H0=["onClick"],q0={class:"svg-icon svg-icon-3 text-danger"},M0={class:"d-flex flex-wrap align-items-center justify-content-between gap-4"},j0={class:"fw-semibold fs-6 text-dark"},z0={key:2,class:"flex-fill overflow-hidden-y d-flex flex-column"},E0={class:"d-flex flex-column gap-3 overflow-auto"},L0={class:"d-flex align-items-center justify-content-between flex-fill"},R0={class:"text-gray-600 fs-6 fw-semibold"},B0={class:"text-gray-600 fs-6 me-10"},O0={class:"d-flex align-items-center ms-auto"},U0=["onClick"],W0={class:"svg-icon svg-icon-3"},G0=["onClick"],Y0={class:"svg-icon svg-icon-3 text-danger"},J0={class:"modal-footer d-flex justify-content-center align-items-center gap-7 mt-6"},Q0=["disabled"],K0=["data-kt-indicator","disabled"],X0=["data-kt-indicator","disabled"];function Z0(s,e,v,b,f,r){const l=S("SvgIcon"),o=S("el-empty"),a=S("el-dialog"),d=S("SolidModal"),n=S("SolidDurationModal"),u=S("SolidInputModal");return $(),V(B,null,[i(a,{modelValue:s.modal,"onUpdate:modelValue":e[4]||(e[4]=m=>s.modal=m),"show-close":!1,width:"500","align-center":""},{header:c(()=>[t("div",S0,[e[5]||(e[5]=t("h3",{class:"modal-title"},"Solid Control Details",-1)),t("span",{class:"cursor-pointer ms-auto",onClick:e[0]||(e[0]=(...m)=>s.hide&&s.hide(...m))},[i(l,{icon:"closeModalIcon"})])])]),default:c(()=>{var m,p,D,k,N,T,w,h,_,H;return[t("div",null,[s.loading?($(),V("div",_0,e[6]||(e[6]=[t("div",{class:"spinner-border text-primary",role:"status"},[t("span",{class:"sr-only"},"Loading...")],-1)]))):($(),V("div",k0,[t("h3",D0,I(`${((p=(m=s.data)==null?void 0:m.type)==null?void 0:p.name)||""} ${((D=s.data)==null?void 0:D.screen)||""}`),1),((N=(k=s.data)==null?void 0:k.inputs)==null?void 0:N.length)>0?($(),V("div",I0,[t("div",V0,[($(!0),V(B,null,Z((T=s.data)==null?void 0:T.inputs,C=>($(),V("div",{key:C==null?void 0:C.id,class:"d-flex flex-wrap align-items-center"},[t("div",P0,[t("span",C0,I(`${C==null?void 0:C.description} : `),1)]),t("div",T0,[t("span",F0,I(`${C==null?void 0:C.value} ${C==null?void 0:C.units}`),1)]),t("div",x0,[t("button",{class:"btn btn-icon btn-sm btn-blue me-3",onClick:F=>s.toggleEditSolidInputModal((C==null?void 0:C.id)||"")},[t("span",A0,[i(l,{icon:"newReportIcon"})])],8,N0),t("button",{class:"btn btn-icon btn-sm btn-blue",onClick:F=>s.deleteInput((C==null?void 0:C.id)||"")},[t("span",q0,[i(l,{icon:"trashIcon"})])],8,H0)])]))),128))])])):U("",!0),t("div",M0,[e[7]||(e[7]=t("span",{class:"fw-semibold fs-6 text-black"},"Total Duration",-1)),t("span",j0,I(`${((w=s.data)==null?void 0:w.totalDurations)||"0"} hrs`),1)]),e[8]||(e[8]=t("div",null,[t("p",{class:"fw-semibold fs-6 text-dark mb-0"},"Time Distribution")],-1)),((_=(h=s.data)==null?void 0:h.durations)==null?void 0:_.length)===0?($(),W(o,{key:1,"image-size":100})):($(),V("div",z0,[t("div",E0,[($(!0),V(B,null,Z((H=s.data)==null?void 0:H.durations,C=>($(),V("div",{key:C.id,class:"d-flex flex-wrap align-items-center"},[t("div",L0,[t("span",R0,I(s.formatDate(C==null?void 0:C.createdAt,"DD MMM YYYY HH:mm")),1),t("span",B0,I(`${C==null?void 0:C.duration} hrs`),1)]),t("div",O0,[t("button",{class:"btn btn-icon btn-sm btn-blue me-3",onClick:F=>s.toggleEditSolidDurationModal((C==null?void 0:C.id)||"")},[t("span",W0,[i(l,{icon:"newReportIcon"})])],8,U0),t("button",{class:"btn btn-icon btn-sm btn-blue",onClick:F=>s.deleteDuration(F,(C==null?void 0:C.id)||"")},[t("span",Y0,[i(l,{icon:"trashIcon"})])],8,G0)])]))),128))])]))]))]),t("div",J0,[t("button",{type:"button",id:"kt_modal_add_customer_cancel",class:"btn text-gray-700 btn-sm btn-blue",disabled:s.loading,onClick:e[1]||(e[1]=(...C)=>s.hide&&s.hide(...C))}," Close ",8,Q0),t("button",{"data-kt-indicator":s.loading?"on":null,class:"btn btn-sm btn-primary",type:"button",disabled:s.loading,onClick:e[2]||(e[2]=C=>{var F;return s.toggleEditSolidModal(C,((F=s.data)==null?void 0:F.id)||"")})}," Edit Solid Equipment ",8,K0),t("button",{"data-kt-indicator":s.loading?"on":null,class:"btn btn-sm btn-success",type:"button",disabled:s.loading,onClick:e[3]||(e[3]=C=>s.toggleAddSolidDurationModal(C))}," Add Duration ",8,X0)])]}),_:1},8,["modelValue"]),i(d,{ref:"solidModal",loadPage:s.reloadPage},null,8,["loadPage"]),i(n,{ref:"solidDurationModal",loadPage:s.reloadPage},null,8,["loadPage"]),i(u,{ref:"solidInputModal",loadPage:s.reloadPage},null,8,["loadPage"])],64)}const ew=Q($0,[["render",Z0],["__scopeId","data-v-5148c4a5"]]),tw=G({name:"site-equipment",components:{SvgIcon:X,SolidModal:os,SolidDetailsModal:ew,SolidDurationModal:ss,SolidInputModal:ls,NoEntries:be},props:{loadPage:{type:Function,required:!0}},setup(s){const e=ct(),v=y(null),b=y(null),f=y(null),r=y(null),l=y(!1),o=y([]),a=ee("dailyReport");ie(()=>{a!=null&&a.getDailyReportId()&&n()});const d=()=>{n(),s!=null&&s.loadPage&&(s==null||s.loadPage())},n=async(w={dailyReportId:a==null?void 0:a.getDailyReportId(),page:1,limit:200})=>{l.value=!0,e.getSolidControlEquipments({params:w,callback:{onSuccess:h=>{o.value=h==null?void 0:h.items},onFinish:h=>{l.value=!1}}})},u=()=>{var w;(w=v==null?void 0:v.value)==null||w.show()},m=(w,h)=>{var _,H;w.stopPropagation(),(_=r==null?void 0:r.value)==null||_.setId(h,""),(H=r==null?void 0:r.value)==null||H.show()},p=(w,h,_="")=>{var H,C;w.stopPropagation(),(H=f==null?void 0:f.value)==null||H.setId(h,_),(C=f==null?void 0:f.value)==null||C.show()},D=(w,h)=>{var _,H;w.stopPropagation(),(_=v==null?void 0:v.value)==null||_.setId(h),(H=v==null?void 0:v.value)==null||H.show()},k=(w,h)=>{w.stopPropagation(),ne.deletionAlert({onConfirmed:()=>{N(h)}})},N=async w=>{l.value=!0,e.deleteSolidControlEquipment({id:w,callback:{onSuccess:h=>{d()},onFinish:h=>{l.value=!1}}})};return{loading:l,solidModal:v,solidInputModal:r,solidDetailsModal:b,solidDurationModal:f,solidControlEquipmentList:o,loadData:d,deleteSolid:k,toggleAddSolidModal:u,toggleEditSolidModal:D,toggleAddSolidInputModal:m,toggleSolidDetailsModal:w=>{var h,_;(h=b==null?void 0:b.value)==null||h.setId(w),(_=b==null?void 0:b.value)==null||_.show()},toggleAddSolidDurationModal:p}}}),sw={class:"row gap-10"},lw={key:0,class:"text-center"},ow=["onClick"],nw={class:"card-body gap-5 d-flex flex-column p-6 pt-8"},aw={class:"mb-0 text-primary"},iw={class:"d-flex flex-column gap-3"},dw={class:"d-flex flex-column gap-3 border-bottom-dashed border-bottom-1 border-blue-light pb-3"},rw={key:0,class:"d-flex flex-wrap align-items-center justify-content-between fw-semibold fs-7 text-gray-700"},uw={class:"mw-130px text-truncate"},cw={class:"ms-auto"},fw={key:1,class:"d-flex flex-wrap align-items-center justify-content-between fw-semibold fs-7 text-gray-700"},mw={class:"mw-130px text-truncate"},pw={class:"ms-auto"},gw={key:2,class:"text-primary fst-italic fs-7 cursor-pointer w-fit"},bw={class:"d-flex flex-wrap align-items-center justify-content-between"},vw={class:"fw-700 fs-7 text-dark"},hw={class:"d-flex align-items-center gap-2 position-absolute top-0 end-0 transform-top-50"},yw=["onClick"],ww={class:"svg-icon svg-icon-3"},$w=["onClick"],Sw={class:"svg-icon svg-icon-3"},_w=["onClick"],kw={class:"svg-icon svg-icon-3"},Dw=["onClick"],Iw={class:"svg-icon svg-icon-3 text-danger"};function Vw(s,e,v,b,f,r){const l=S("NoEntries"),o=S("SvgIcon"),a=S("el-tooltip"),d=S("SolidModal"),n=S("SolidDurationModal"),u=S("SolidDetailsModal"),m=S("SolidInputModal");return $(),V(B,null,[t("div",null,[e[2]||(e[2]=t("h4",{class:"my-7 text-black fw-700"},"Solid Control Equipment",-1)),t("div",sw,[s.loading?($(),V("div",lw,e[0]||(e[0]=[t("div",{class:"spinner-border text-primary",role:"status"},[t("span",{class:"sr-only"},"Loading...")],-1)]))):($(),V(B,{key:1},[s.solidControlEquipmentList.length===0?($(),W(l,{key:0,addNew:s.toggleAddSolidModal},null,8,["addNew"])):($(!0),V(B,{key:1},Z(s.solidControlEquipmentList,p=>{var D;return $(),V("div",{key:p==null?void 0:p.id,class:"col-4 card rounded-3 shadow-sm border-top border-top-2 border-primary position-relative card-item cursor-pointer",onClick:k=>s.toggleSolidDetailsModal((p==null?void 0:p.id.toString())||"")},[t("div",nw,[t("h5",aw,I(`${(D=p==null?void 0:p.type)==null?void 0:D.name} ${p==null?void 0:p.screen}`),1),t("div",iw,[t("div",dw,[p!=null&&p.inputs[0]?($(),V("div",rw,[t("span",uw,I(p==null?void 0:p.inputs[0].description),1),t("span",cw,I(`${p==null?void 0:p.inputs[0].value} ${p==null?void 0:p.inputs[0].units}`),1)])):U("",!0),p!=null&&p.inputs[1]?($(),V("div",fw,[t("span",mw,I(p==null?void 0:p.inputs[1].description),1),t("span",pw,I(`${p==null?void 0:p.inputs[1].value} ${p==null?void 0:p.inputs[1].units}`),1)])):U("",!0),(p==null?void 0:p.inputs.length)>2?($(),V("div",gw," More... ")):U("",!0)]),t("div",bw,[e[1]||(e[1]=t("span",{class:"fw-semibold fs-7 text-gray-700"},"Total Duration",-1)),t("span",vw,I(`${(p==null?void 0:p.totalDurations)||"0"} hrs`),1)])])]),t("div",hw,[i(a,{content:"Add Input",placement:"top",effect:"customize"},{default:c(()=>[t("button",{class:"btn rounded-circle btn-icon btn-sm btn-primary",onClick:k=>s.toggleAddSolidInputModal(k,(p==null?void 0:p.id)||"")},[t("span",ww,[i(o,{icon:"addIcon"})])],8,yw)]),_:2},1024),i(a,{content:"Add Duration",placement:"top",effect:"customize"},{default:c(()=>[t("button",{class:"btn rounded-circle btn-icon btn-sm btn-action bg-light btn-export",onClick:k=>s.toggleAddSolidDurationModal(k,(p==null?void 0:p.id)||"")},[t("span",Sw,[i(o,{icon:"durationIcon"})])],8,$w)]),_:2},1024),i(a,{content:"Edit",placement:"top",effect:"customize"},{default:c(()=>[t("button",{class:"btn rounded-circle btn-icon btn-sm btn-action bg-light btn-edit",onClick:k=>s.toggleEditSolidModal(k,(p==null?void 0:p.id.toString())||"")},[t("span",kw,[i(o,{icon:"pencilIcon"})])],8,_w)]),_:2},1024),i(a,{content:"Delete",placement:"top",effect:"customize"},{default:c(()=>[t("button",{class:"btn rounded-circle btn-icon btn-sm btn-action bg-light btn-delete",onClick:k=>s.deleteSolid(k,(p==null?void 0:p.id.toString())||"")},[t("span",Iw,[i(o,{icon:"trashIcon"})])],8,Dw)]),_:2},1024)])],8,ow)}),128))],64))])]),i(d,{ref:"solidModal",loadPage:s.loadData},null,8,["loadPage"]),i(n,{ref:"solidDurationModal",loadPage:s.loadData},null,8,["loadPage"]),i(u,{ref:"solidDetailsModal",loadPage:s.loadData},null,8,["loadPage"]),i(m,{ref:"solidInputModal",loadPage:s.loadData},null,8,["loadPage"])],64)}const Pw=Q(tw,[["render",Vw],["__scopeId","data-v-6640f5c6"]]),Cw=G({name:"site-equipment",components:{SvgIcon:X,Pump:Hy,Solid:Pw,BottomTool:pe},props:{setChildActiveTab:{type:Function,required:!1,default:()=>{}}},setup(s){const e=at(),v=y(null),b=y(null),f=y(!1),r=y([]),l=ee("dailyReport");ie(()=>{o()});const o=async()=>{l!=null&&l.getDailyReportId()&&(f.value=!0,e.getPumpSummary({dailyReportId:l==null?void 0:l.getDailyReportId(),callback:{onSuccess:n=>{r.value=n},onFinish:n=>{f.value=!1}}}))};return{loading:f,summaryData:r,pumpRef:v,solidRef:b,toggleAddPumpModal:()=>{var n;(n=v==null?void 0:v.value)==null||n.toggleAddPumpModal()},toggleAddSolidModal:()=>{var n;(n=b==null?void 0:b.value)==null||n.toggleAddSolidModal()},getPumpSummary:o}}}),Tw={class:"card h-100 my-8 site-equipment"},Fw={class:"card-body d-flex flex-column gap-5"},xw={class:"bg-light-primary rounded border-primary border border-dashed p-7 gap-3"},Nw={key:0,class:"text-center"},Aw={key:1,class:"d-flex flex-wrap flex-md-nowrap gap-5"},Hw={class:"flex-fill bg-white rounded border-blue-light border border-dashed p-7 d-flex flex-column gap-3"},qw={class:"mb-0 text-gray-800 fw-700"},Mw={class:"mb-0 text-gray-800 fw-700"},jw={class:"flex-fill bg-white rounded border-blue-light border border-dashed p-7 d-flex flex-column gap-3"},zw={class:"mb-0 text-gray-800 fw-700"},Ew={class:"mb-0 text-gray-800 fw-700"},Lw={class:"flex flex-wrap items-center"},Rw={class:"btn rounded-circle btn-icon btn-primary btn-tool"},Bw={class:"svg-icon svg-icon-1"};function Ow(s,e,v,b,f,r){var D,k;const l=S("el-popover"),o=S("Pump"),a=S("Solid"),d=S("SvgIcon"),n=S("el-dropdown-item"),u=S("el-dropdown-menu"),m=S("el-dropdown"),p=S("BottomTool");return $(),V(B,null,[t("div",Tw,[e[10]||(e[10]=t("div",{class:"card-header d-flex flex-wrap align-items-center"},[t("h1",{class:"mb-0 me-4 text-gray-900"},"Site Equipment")],-1)),t("div",Fw,[t("div",xw,[e[9]||(e[9]=t("h4",{class:"text-gray-800 fw-700"},"Detail",-1)),s.loading?($(),V("div",Nw,e[0]||(e[0]=[t("div",{class:"spinner-border text-primary",role:"status"},[t("span",{class:"sr-only"},"Loading...")],-1)]))):($(),V("div",Aw,[t("div",Hw,[t("h4",qw,[e[3]||(e[3]=L(" Total Rate")),i(l,{placement:"top",width:300,trigger:"hover"},{reference:c(()=>e[1]||(e[1]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7 me-4"},null,-1)])),default:c(()=>[e[2]||(e[2]=t("span",null," The total flow rate of fluid delivered by all active pumps combined. ",-1))]),_:1})]),e[4]||(e[4]=t("div",{class:"text-gray-400 fw-semibold fs-7"}," Total Rate (gpm) = Sum(Rate (gpm) of all active pumps) ",-1)),t("h3",Mw,I(`${((D=s.summaryData)==null?void 0:D.totalRate)||"0"} (gmp)`),1)]),t("div",jw,[t("h4",zw,[e[7]||(e[7]=L(" Pump pressure")),i(l,{placement:"top",width:200,trigger:"hover"},{reference:c(()=>e[5]||(e[5]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7 me-4"},null,-1)])),default:c(()=>[e[6]||(e[6]=t("span",null," The pressure generated by the pumps. ",-1))]),_:1})]),e[8]||(e[8]=t("div",{class:"text-gray-400 fw-semibold fs-7"}," Pump Pressure (psi) = (Displacement (bbl/stroke) * Rate (gpm) * 8.33) / (Efficiency (%) * 2 * Stroke (stroke/min)) ",-1)),t("h3",Ew,I(`${((k=s.summaryData)==null?void 0:k.pumpPressure)||"0"} (psi)`),1)])]))]),i(o,{ref:"pumpRef",loadPage:s.getPumpSummary},null,8,["loadPage"]),i(a,{ref:"solidRef",loadPage:s.getPumpSummary},null,8,["loadPage"])])]),i(p,{showHelpInfo:!1},{header:c(()=>[t("div",Lw,[i(m,{placement:"top"},{dropdown:c(()=>[i(u,{class:"p-4"},{default:c(()=>[i(n,{class:"customize-dropdown-item mb-3",onClick:s.toggleAddPumpModal},{default:c(()=>e[11]||(e[11]=[L("Add Pump")])),_:1},8,["onClick"]),i(n,{class:"customize-dropdown-item",onClick:s.toggleAddSolidModal},{default:c(()=>e[12]||(e[12]=[L("Add Solids Control")])),_:1},8,["onClick"])]),_:1})]),default:c(()=>[t("button",Rw,[t("span",Bw,[i(d,{icon:"addIcon"})])])]),_:1})])]),_:1})],64)}const ns=Q(Cw,[["render",Ow]]),as=ae("task",()=>({getTasks:async({params:r,callback:l})=>{var d;const o=g.get(l,"onSuccess",g.noop),a=g.get(l,"onFinish",g.noop);try{P.setHeader();const n=await P.getWithParams("tasks",r);o(((d=n.data)==null?void 0:d.data)||n.data)}catch(n){E(n,l)}finally{a()}},getTaskDetails:async({id:r,callback:l})=>{var d;const o=g.get(l,"onSuccess",g.noop),a=g.get(l,"onFinish",g.noop);try{P.setHeader();const n=await P.get(`tasks/${r}`);o(((d=n.data)==null?void 0:d.data)||n.data)}catch(n){E(n,l)}finally{a()}},updateTask:async({id:r,params:l,callback:o})=>{var n;const a=g.get(o,"onSuccess",g.noop),d=g.get(o,"onFinish",g.noop);try{P.setHeader();const u=await P.put(`tasks/${r}`,l);a(((n=u.data)==null?void 0:n.data)||u.data)}catch(u){E(u,o)}finally{d()}},deleteTask:async({id:r,callback:l})=>{var d;const o=g.get(l,"onSuccess",g.noop),a=g.get(l,"onFinish",g.noop);try{P.setHeader();const n=await P.delete(`tasks/${r}`);o(((d=n.data)==null?void 0:d.data)||n.data)}catch(n){E(n,l)}finally{a()}},createTask:async({params:r,callback:l})=>{var d;const o=g.get(l,"onSuccess",g.noop),a=g.get(l,"onFinish",g.noop);try{P.setHeader();const n=await P.post("tasks",r);o(((d=n.data)==null?void 0:d.data)||n.data)}catch(n){E(n,l)}finally{a()}}})),Uw=G({name:"task-modal",components:{SvgIcon:X},props:{loadPage:{type:Function,default:()=>{},required:!0}},setup(s){const e=re(),v=as(),b=y(!1),f={description:"",durations:null},r=y(JSON.parse(JSON.stringify(f))),l=y(null),o=y(!1),a=y(""),d=ee("dailyReport");J(a,h=>{h!==""&&n()}),J(b,h=>{var _;h===!1&&(a.value="",T(),(_=l==null?void 0:l.value)==null||_.resetFields())});const n=async()=>{v.getTaskDetails({id:a.value,callback:{onSuccess:h=>{r.value={...h}}}})},u=async h=>{o.value=!0,v.updateTask({id:a.value,params:h,callback:{onSuccess:_=>{s!=null&&s.loadPage&&(s==null||s.loadPage()),p()},onFinish:_=>{o.value=!1}}})},m=()=>{b.value=!0},p=()=>{b.value=!1},D=y({description:[{required:!0,message:"Please type Description",trigger:"blur"}],durations:[{required:!0,message:"Please type Duration",trigger:"blur"}]}),k=()=>{l.value&&l.value.validate(h=>{var _,H,C;if(h){const F={description:(_=r==null?void 0:r.value)==null?void 0:_.description,durations:Number((H=r==null?void 0:r.value)==null?void 0:H.durations)};a!=null&&a.value?u({...F,dailyReportId:d==null?void 0:d.getDailyReportId()}):d==null||d.createDailyReport({wellId:(C=e==null?void 0:e.params)==null?void 0:C.id,callback:{onSuccess:j=>{N({...F,dailyReportId:d==null?void 0:d.getDailyReportId()})}}})}})},N=async h=>{o.value=!0,v.createTask({params:h,callback:{onSuccess:_=>{s!=null&&s.loadPage&&(s==null||s.loadPage()),p()},onFinish:_=>{o.value=!1}}})},T=()=>{r.value=JSON.parse(JSON.stringify(f))};return{id:a,modal:b,rules:D,loading:o,targetData:r,formRef:l,show:m,hide:p,submit:k,reset:T,setId:h=>{a.value=h.toString()}}}}),Ww={class:"d-flex align-items-center w-100"},Gw={class:"modal-title"},Yw={class:"row g-7 mb-3"},Jw={class:"col-12 fv-row d-flex flex-column justify-content-stretch"},Qw={class:"row g-7 mb-3"},Kw={class:"col-12 fv-row d-flex flex-column justify-content-stretch"},Xw={class:"modal-footer d-flex justify-content-center align-items-center"},Zw=["disabled"],e$=["data-kt-indicator","disabled"],t$={key:0,class:"indicator-label"},s$={key:1,class:"indicator-progress"};function l$(s,e,v,b,f,r){const l=S("SvgIcon"),o=S("el-input"),a=S("el-form-item"),d=S("el-form"),n=S("el-dialog");return $(),W(n,{modelValue:s.modal,"onUpdate:modelValue":e[4]||(e[4]=u=>s.modal=u),"show-close":!1,width:"500","align-center":""},{header:c(()=>[t("div",Ww,[t("h3",Gw,I(`${s.id?"Edit Task":"Add Task"}`),1),t("span",{class:"cursor-pointer ms-auto",onClick:e[0]||(e[0]=(...u)=>s.hide&&s.hide(...u))},[i(l,{icon:"closeModalIcon"})])])]),default:c(()=>[t("div",null,[i(d,{id:"add_task_form",onSubmit:de(s.submit,["prevent"]),model:s.targetData,rules:s.rules,ref:"formRef",class:"form"},{default:c(()=>[t("div",Yw,[t("div",Jw,[e[5]||(e[5]=t("label",{class:"d-flex align-items-center fs-6 fw-semibold mb-2 text-break required"},"Description ",-1)),i(a,{prop:"description",class:"mt-auto"},{default:c(()=>[i(o,{modelValue:s.targetData.description,"onUpdate:modelValue":e[1]||(e[1]=u=>s.targetData.description=u),placeholder:"",name:"description"},null,8,["modelValue"])]),_:1})])]),t("div",Qw,[t("div",Kw,[e[6]||(e[6]=t("label",{class:"d-flex align-items-center fs-6 fw-semibold mb-2 text-break required"},"Duration (hr) ",-1)),i(a,{prop:"durations",class:"mt-auto"},{default:c(()=>[i(o,{type:"number",class:"w-100",controls:!1,step:"any",modelValue:s.targetData.durations,"onUpdate:modelValue":e[2]||(e[2]=u=>s.targetData.durations=u),placeholder:"",name:"durations"},null,8,["modelValue"])]),_:1})])]),t("div",Xw,[t("button",{type:"button",id:"kt_modal_add_customer_cancel",class:"btn text-gray-700 btn-sm me-3 btn-blue",onClick:e[3]||(e[3]=(...u)=>s.hide&&s.hide(...u)),disabled:s.loading}," Discard ",8,Zw),t("button",{"data-kt-indicator":s.loading?"on":null,class:"btn btn-sm btn-primary",type:"submit",disabled:s.loading},[s.loading?U("",!0):($(),V("span",t$," Save ")),s.loading?($(),V("span",s$,e[7]||(e[7]=[L(" Please wait... "),t("span",{class:"spinner-border spinner-border-sm align-middle ms-2"},null,-1)]))):U("",!0)],8,e$)])]),_:1},8,["onSubmit","model","rules"])])]),_:1},8,["modelValue"])}const o$=Q(Uw,[["render",l$]]),n$=G({name:"tasks",components:{SvgIcon:X,BottomTool:pe,TaskModal:o$,NoEntries:be},props:{setChildActiveTab:{type:Function,required:!1,default:()=>{}}},setup(s){const e=y(!1),v=as(),b=y([]),f=y(null),r=ee("dailyReport");ie(()=>{r!=null&&r.getDailyReportId()&&l()});const l=async(u={dailyReportId:r==null?void 0:r.getDailyReportId(),page:1,limit:200})=>{e.value=!0,v.getTasks({params:u,callback:{onSuccess:m=>{b.value=m==null?void 0:m.items},onFinish:m=>{e.value=!1}}})},o=()=>{var u;(u=f==null?void 0:f.value)==null||u.show()},a=u=>{var m,p;(m=f==null?void 0:f.value)==null||m.setId(u),(p=f==null?void 0:f.value)==null||p.show()},d=(u,m)=>{u.stopPropagation(),ne.deletionAlert({onConfirmed:()=>{n(m)}})},n=async u=>{e.value=!0,v.deleteTask({id:u,callback:{onSuccess:m=>{l()},onFinish:m=>{e.value=!1}}})};return{loading:e,taskList:b,taskModal:f,getTasks:l,deleteTask:d,toggleAddTaskModal:o,toggleEditDurationModal:a}}}),a$={class:"card h-100 my-8 tasks"},i$={class:"card-body"},d$={key:0,class:"text-center"},r$={key:1,class:"row row-cols-1 row-cols-md-2 row-cols-lg-3"},u$={class:"d-flex align-items-center bg-green-light p-4 rounded-3"},c$={class:"flex-grow-1"},f$={class:"text-gray-800"},m$={class:"text-green-light fw-semibold d-block text-small"},p$={class:"d-flex align-items-center gap-2"},g$=["onClick"],b$={class:"svg-icon svg-icon-3"},v$=["onClick"],h$={class:"svg-icon svg-icon-3 text-danger"};function y$(s,e,v,b,f,r){const l=S("NoEntries"),o=S("SvgIcon"),a=S("el-tooltip"),d=S("TaskModal"),n=S("BottomTool");return $(),V(B,null,[t("div",a$,[e[2]||(e[2]=t("div",{class:"card-header d-flex flex-wrap align-items-center"},[t("h1",{class:"mb-0 me-4 text-gray-900"},"Tasks")],-1)),t("div",i$,[s.loading?($(),V("div",d$,e[0]||(e[0]=[t("div",{class:"spinner-border text-primary",role:"status"},[t("span",{class:"sr-only"},"Loading...")],-1)]))):($(),V(B,{key:1},[s.taskList.length===0?($(),W(l,{key:0,addNew:s.toggleAddTaskModal},null,8,["addNew"])):($(),V("div",r$)),($(!0),V(B,null,Z(s.taskList,u=>($(),V("div",{class:"col pb-3",key:u==null?void 0:u.id},[t("div",u$,[e[1]||(e[1]=t("span",{class:"bullet bullet-green h-40px me-3"},null,-1)),t("div",c$,[t("h4",f$,I(u==null?void 0:u.description),1),t("span",m$,I(`${u==null?void 0:u.durations} hours`),1)]),t("div",p$,[i(a,{content:"Edit Task",placement:"top",effect:"customize"},{default:c(()=>[t("button",{class:"btn rounded-circle btn-icon btn-sm btn-action bg-light btn-edit",onClick:m=>s.toggleEditDurationModal(u==null?void 0:u.id)},[t("span",b$,[i(o,{icon:"pencilIcon"})])],8,g$)]),_:2},1024),i(a,{content:"Delete",placement:"top",effect:"customize"},{default:c(()=>[t("button",{class:"btn rounded-circle btn-icon btn-sm btn-action bg-light btn-delete",onClick:m=>s.deleteTask(m,u==null?void 0:u.id)},[t("span",h$,[i(o,{icon:"trashIcon"})])],8,v$)]),_:2},1024)])])]))),128))],64))])]),i(d,{ref:"taskModal",loadPage:s.getTasks},null,8,["loadPage"]),i(n,{addNew:s.toggleAddTaskModal,showHelpInfo:!1},null,8,["addNew"])],64)}const is=Q(n$,[["render",y$]]),ds=ae("volumeTrackingItem",()=>({getVolumeTrackingItemDetails:async({id:f,callback:r})=>{var a;const l=g.get(r,"onSuccess",g.noop),o=g.get(r,"onFinish",g.noop);try{P.setHeader();const d=await P.get(`volumeTrackingItems/${f}`);l(((a=d.data)==null?void 0:a.data)||d.data)}catch(d){E(d,r)}finally{o()}},updateVolumeTrackingItem:async({id:f,params:r,callback:l})=>{var d;const o=g.get(l,"onSuccess",g.noop),a=g.get(l,"onFinish",g.noop);try{P.setHeader();const n=await P.put(`volumeTrackingItems/${f}`,r);o(((d=n.data)==null?void 0:d.data)||n.data)}catch(n){E(n,l)}finally{a()}},deleteVolumeTrackingItem:async({id:f,callback:r})=>{var a;const l=g.get(r,"onSuccess",g.noop),o=g.get(r,"onFinish",g.noop);try{P.setHeader();const d=await P.delete(`volumeTrackingItems/${f}`);l(((a=d.data)==null?void 0:a.data)||d.data)}catch(d){E(d,r)}finally{o()}},createVolumeTrackingItem:async({params:f,callback:r})=>{var a;const l=g.get(r,"onSuccess",g.noop),o=g.get(r,"onFinish",g.noop);try{P.setHeader();const d=await P.post("volumeTrackingItems",f);l(((a=d.data)==null?void 0:a.data)||d.data)}catch(d){E(d,r)}finally{o()}}})),w$=G({name:"add-volume-modal",components:{SvgIcon:X,FormModal:Jt},props:{loadPage:{type:Function,required:!1},productAndPackageInventoryId:{type:String,default:""}},setup(s){const e=ds(),v=ut(),b=y(!1),f={description:"",volume:null,type:null,productId:null},r=y(JSON.parse(JSON.stringify(f))),l=y(null),o=y(!1),a=y(null),d=y(""),n=y(""),u=y(null),m=y([]),p=y(!1);J(n,M=>{M!==""&&N()}),J(()=>s==null?void 0:s.productAndPackageInventoryId,M=>{M!==""&&k(M)}),J(b,M=>{var z;M===!1&&(d.value="",n.value="",a.value=null,x(),(z=l==null?void 0:l.value)==null||z.resetFields())});const D=()=>{var M,z;(M=u==null?void 0:u.value)==null||M.setType(ce.initial),(z=u==null?void 0:u.value)==null||z.show()},k=async M=>{p.value=!0,v.getProductPackageReports({params:{productAndPackageInventoryId:M,page:1,limit:200},callback:{onSuccess:z=>{m.value=z==null?void 0:z.items.map(q=>{var R,Y;return{value:(R=q==null?void 0:q.product)==null?void 0:R.id,key:(Y=q==null?void 0:q.product)==null?void 0:Y.name}})},onFinish:z=>{p.value=!1}}})},N=async()=>{e.getVolumeTrackingItemDetails({id:n.value,callback:{onSuccess:M=>{r.value={...M}}}})},T=async M=>{o.value=!0,e.updateVolumeTrackingItem({id:n.value,params:M,callback:{onSuccess:z=>{s!=null&&s.loadPage&&(s==null||s.loadPage()),_()},onFinish:z=>{o.value=!1}}})},w=async M=>{o.value=!0,e.createVolumeTrackingItem({params:M,callback:{onSuccess:z=>{s!=null&&s.loadPage&&(s==null||s.loadPage()),_()},onFinish:z=>{o.value=!1}}})},h=()=>{b.value=!0},_=()=>{b.value=!1},H=y({description:[{required:!0,message:"Please type Description",trigger:"blur"}],productId:[{required:!0,message:"Please select Product",trigger:"blur"}],volume:[{required:!0,message:"Please type Volume",trigger:"blur"}]}),C=(M,z)=>{d.value=M,n.value=z},F=M=>{a.value=M},j=()=>{l.value&&l.value.validate(M=>{var z,q;if(M){const R={volumeTrackingId:d==null?void 0:d.value,description:(z=r==null?void 0:r.value)==null?void 0:z.description,volume:Number((q=r==null?void 0:r.value)==null?void 0:q.volume),type:Number(a==null?void 0:a.value)};n!=null&&n.value?T(R):w(R)}})},x=()=>{r.value=JSON.parse(JSON.stringify(f))};return{id:n,type:a,modal:b,rules:H,loading:o,targetData:r,formRef:l,productList:m,newProductModal:u,toggleAddNewProductModal:D,setId:C,show:h,hide:_,submit:j,reset:x,setType:F,getTitle:()=>{var M;return((M=tt(a.value,t1))==null?void 0:M.label)||""},getPackageReportInfo:k}}}),$$={class:"d-flex align-items-center w-100"},S$={class:"modal-title"},_$={class:"row g-7 mb-3"},k$={class:"col-12 fv-row d-flex flex-column justify-content-stretch"},D$={class:"row g-7 mb-3"},I$={class:"col-12 fv-row d-flex flex-column justify-content-stretch"},V$={class:"d-flex justify-content-between align-items-center mb-2"},P$={class:"row g-7 mb-3"},C$={class:"col-12 fv-row d-flex flex-column justify-content-stretch"},T$={class:"modal-footer d-flex justify-content-center align-items-center"},F$=["disabled"],x$=["data-kt-indicator","disabled"],N$={key:0,class:"indicator-label"},A$={key:1,class:"indicator-progress"};function H$(s,e,v,b,f,r){const l=S("SvgIcon"),o=S("el-input"),a=S("el-form-item"),d=S("el-option"),n=S("el-select"),u=S("el-form"),m=S("el-dialog"),p=S("FormModal");return $(),V(B,null,[i(m,{modelValue:s.modal,"onUpdate:modelValue":e[6]||(e[6]=D=>s.modal=D),"show-close":!1,width:"500","align-center":""},{header:c(()=>[t("div",$$,[t("h3",S$,I(s.id?`Edit ${s.getTitle()}`:`Add ${s.getTitle()}`),1),t("span",{class:"cursor-pointer ms-auto",onClick:e[0]||(e[0]=(...D)=>s.hide&&s.hide(...D))},[i(l,{icon:"closeModalIcon"})])])]),default:c(()=>[t("div",null,[i(u,{id:"add_volume_form",onSubmit:de(s.submit,["prevent"]),model:s.targetData,rules:s.rules,ref:"formRef",class:"form"},{default:c(()=>[t("div",_$,[t("div",k$,[e[7]||(e[7]=t("label",{class:"d-flex align-items-center fs-6 fw-semibold mb-2 text-break required"},"Volume (bbl) ",-1)),i(a,{prop:"volume",class:"mt-auto"},{default:c(()=>[i(o,{type:"number",class:"w-100",controls:!1,step:"any",modelValue:s.targetData.volume,"onUpdate:modelValue":e[1]||(e[1]=D=>s.targetData.volume=D),placeholder:"",name:"volume"},null,8,["modelValue"])]),_:1})])]),t("div",D$,[t("div",I$,[t("div",V$,[e[8]||(e[8]=t("label",{class:"d-flex align-items-center fs-6 fw-semibold text-break required"},"Product ",-1)),t("button",{type:"button",class:"btn btn-primary btn-sm fw-semibold",onClick:e[2]||(e[2]=(...D)=>s.toggleAddNewProductModal&&s.toggleAddNewProductModal(...D))}," New Product ")]),i(a,{prop:"productId",class:"mt-auto"},{default:c(()=>[i(n,{modelValue:s.targetData.productId,"onUpdate:modelValue":e[3]||(e[3]=D=>s.targetData.productId=D),placeholder:"Select Product",class:"w-100",clearable:""},{default:c(()=>[($(!0),V(B,null,Z(s.productList,D=>($(),W(d,{key:D.value,label:D.key,value:D.value,name:"productId"},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})])]),t("div",P$,[t("div",C$,[e[9]||(e[9]=t("label",{class:"d-flex align-items-center fs-6 fw-semibold mb-2 text-break required"},"Description ",-1)),i(a,{prop:"description",class:"mt-auto"},{default:c(()=>[i(o,{type:"textarea",rows:"3",modelValue:s.targetData.description,"onUpdate:modelValue":e[4]||(e[4]=D=>s.targetData.description=D),placeholder:"",name:"description"},null,8,["modelValue"])]),_:1})])]),t("div",T$,[t("button",{type:"button",id:"kt_modal_add_customer_cancel",class:"btn text-gray-700 btn-sm me-3 btn-blue",onClick:e[5]||(e[5]=(...D)=>s.hide&&s.hide(...D)),disabled:s.loading}," Discard ",8,F$),t("button",{"data-kt-indicator":s.loading?"on":null,class:"btn btn-sm btn-primary",type:"submit",disabled:s.loading},[s.loading?U("",!0):($(),V("span",N$," Save ")),s.loading?($(),V("span",A$,e[10]||(e[10]=[L(" Please wait... "),t("span",{class:"spinner-border spinner-border-sm align-middle ms-2"},null,-1)]))):U("",!0)],8,x$)])]),_:1},8,["onSubmit","model","rules"])])]),_:1},8,["modelValue"]),i(p,{ref:"newProductModal",productAndPackageInventoryId:s.productAndPackageInventoryId,loadTable:()=>s.getPackageReportInfo(s.productAndPackageInventoryId)},null,8,["productAndPackageInventoryId","loadTable"])],64)}const q$=Q(w$,[["render",H$]]),it=[{value:me.Addition,key:"Additions"},{value:me.Loss,key:"Losses"},{value:me.Transfer,key:"Transfers"}],M$=G({name:"volume-details-modal",components:{SvgIcon:X},props:{loadPage:{type:Function,default:()=>{},required:!0},editAddition:{type:Function,required:!1},editLoss:{type:Function,required:!1},editTransfer:{type:Function,required:!1}},setup(s){const e=nt(),v=ds(),b=y(!1),f=y(!1),r=y(),l=y(it[0].value.toString()),o=y("");J(o,h=>{h!==""&&a()});const a=async()=>{f.value=!0,e.getVolumeTrackingDetails({id:o.value,callback:{onSuccess:h=>{h.additions=[],h.losses=[],h.transfers=[];for(const _ of h==null?void 0:h.volumeItems)(_==null?void 0:_.type)==me.Addition?h.additions.push(_):(_==null?void 0:_.type)==me.Loss?h.losses.push(_):(_==null?void 0:_.type)==me.Transfer&&h.transfers.push(_);r.value=JSON.parse(JSON.stringify(h))},onFinish:h=>{f.value=!1}}})},d=()=>{b.value=!0},n=h=>{o.value=h},u=()=>{b.value=!1,l.value=it[0].value.toString(),o.value="",p()},m=h=>{const _=h.target;l.value=_.getAttribute("data-tab-index")},p=()=>{r.value=null},D=h=>{ne.deletionAlert({onConfirmed:()=>{k(h)}})},k=h=>{v.deleteVolumeTrackingItem({id:h,callback:{onSuccess:_=>{a(),s!=null&&s.loadPage&&s.loadPage()}}})};return{loading:f,tabs:it,tabIndex:l,modal:b,targetData:r,VolumeTracking:me,show:d,hide:u,setId:n,deleteItem:D,numberWithCommas:ge,setActiveTab:m,formatDate:ke,toggleEditAdditionModal:h=>{s!=null&&s.editAddition&&(s==null||s.editAddition(o==null?void 0:o.value,h))},toggleEditLossModal:h=>{s!=null&&s.editLoss&&(s==null||s.editLoss(o==null?void 0:o.value,h))},toggleEditTransferModal:h=>{s!=null&&s.editTransfer&&(s==null||s.editTransfer(o==null?void 0:o.value,h))}}}}),j$={key:0,class:"rounded-3 shadow-sm border-top border-top-2 border-primary d-flex flex-column gap-6 pt-10 card-body overflow-hidden-y"},z$={key:1,class:"rounded-3 shadow-sm border-top border-top-2 border-primary d-flex flex-column gap-6 pt-10 card-body overflow-hidden-y"},E$={class:"d-flex flex-wrap justify-content-between align-items-center gap-3"},L$={class:"mb-0 text-primary"},R$={class:"d-flex align-items-center gap-3"},B$={class:"d-flex flex-column gap-3"},O$={class:"d-flex flex-wrap align-items-center justify-content-between pb-3 border-bottom-dashed border-bottom-1 border-blue-light"},U$={class:"fw-700 fs-7 text-gray-700"},W$={class:"d-flex flex-wrap align-items-center justify-content-between pb-3 border-bottom-dashed border-bottom-1 border-blue-light"},G$={class:"fw-700 fs-7 text-gray-700"},Y$={class:"d-flex flex-wrap align-items-center justify-content-between"},J$={class:"fw-700 fs-7 text-gray-700"},Q$={class:"d-flex flex-wrap gap-3 align-items-center justify-content-center"},K$={class:"border border-gray-300 border-dashed rounded p-3"},X$={class:"fw-700 fs-6 text-gray-700"},Z$={class:"border border-gray-300 border-dashed rounded p-3"},eS={class:"fw-700 fs-6 text-gray-700"},tS={class:"d-flex align-items-stretch gap-2 gap-lg-3 xxl:mt-0"},sS={class:"nav nav-stretch nav-line-tabs border-0 daily-nav",role:"tablist",id:"kt_layout_builder_tabs",ref:"kt_layout_builder_tabs"},lS=["data-tab-index"],oS={class:"flex-fill d-flex flex-column overflow-hidden-y"},nS={key:0,class:"overflow-scroll-y"},aS={class:"table"},iS={class:"text-gray-600 fs-6 fw-semibold"},dS={class:"text-gray-600 fs-6 text-truncate mw-200"},rS={class:"text-gray-800 fs-6 fw-300"},uS={class:"d-flex align-items-center ms-auto justify-content-end"},cS=["onClick"],fS={class:"svg-icon svg-icon-3"},mS=["onClick"],pS={class:"svg-icon svg-icon-3 text-danger"},gS={key:1,class:"overflow-scroll-y"},bS={class:"table"},vS={class:"text-gray-600 fs-6 fw-semibold"},hS={class:"text-gray-600 fs-6 text-truncate mw-200"},yS={class:"text-gray-800 fs-6 fw-300"},wS={class:"d-flex align-items-center ms-auto justify-content-end"},$S=["onClick"],SS={class:"svg-icon svg-icon-3"},_S=["onClick"],kS={class:"svg-icon svg-icon-3 text-danger"},DS={key:2,class:"overflow-scroll-y"},IS={class:"table"},VS={class:"text-gray-600 fs-6 fw-semibold"},PS={class:"text-gray-600 fs-6 text-truncate mw-200"},CS={class:"text-gray-800 fs-6 fw-300"},TS={class:"d-flex align-items-center ms-auto justify-content-end"},FS=["onClick"],xS={class:"svg-icon svg-icon-3"},NS=["onClick"],AS={class:"svg-icon svg-icon-3 text-danger"};function HS(s,e,v,b,f,r){const l=S("SvgIcon"),o=S("el-dialog");return $(),W(o,{modelValue:s.modal,"onUpdate:modelValue":e[2]||(e[2]=a=>s.modal=a),"show-close":!1,width:"500","align-center":"",class:"volume-details-modal"},{default:c(()=>{var a,d,n,u,m,p,D,k,N,T,w,h;return[s.loading?($(),V("div",j$,e[3]||(e[3]=[t("div",{class:"text-center"},[t("div",{class:"spinner-border text-primary",role:"status"},[t("span",{class:"sr-only"},"Loading...")])],-1)]))):($(),V("div",z$,[t("div",E$,[t("h5",L$,I((a=s.targetData)==null?void 0:a.name),1),t("div",R$,[t("span",{class:_e(["badge px-3 py-2",{"badge-light-success":(d=s.targetData)==null?void 0:d.status,"badge-light-danger":!((n=s.targetData)!=null&&n.status)}])},I((u=s.targetData)!=null&&u.status?"Active":"Inactive"),3),t("span",{class:"cursor-pointer ms-auto",onClick:e[0]||(e[0]=(..._)=>s.hide&&s.hide(..._))},[i(l,{icon:"closeModalIcon"})])])]),t("div",B$,[e[7]||(e[7]=t("div",{class:"d-flex flex-wrap align-items-center justify-content-between pb-3 border-bottom-dashed border-bottom-1 border-blue-light"},[t("span",{class:"fw-semibold fs-7 text-gray-700"},"Type"),t("span",{class:"fw-700 fs-7 text-gray-700"}," Storage Type ")],-1)),t("div",O$,[e[4]||(e[4]=t("span",{class:"fw-semibold fs-7 text-gray-700"},"Total Additions",-1)),t("span",U$,I(s.numberWithCommas(((m=s.targetData)==null?void 0:m.totalAdditions)||0)),1)]),t("div",W$,[e[5]||(e[5]=t("span",{class:"fw-semibold fs-7 text-gray-700"},"Total Losses",-1)),t("span",G$,I(s.numberWithCommas(((p=s.targetData)==null?void 0:p.totalLosses)||0)),1)]),t("div",Y$,[e[6]||(e[6]=t("span",{class:"fw-semibold fs-7 text-gray-700"},"Total Transfers",-1)),t("span",J$,I(s.numberWithCommas(((D=s.targetData)==null?void 0:D.totalTransfers)||0)),1)])]),t("div",Q$,[t("div",K$,[t("div",X$,I(`${s.numberWithCommas(((k=s.targetData)==null?void 0:k.calculatedVolume)||0)} (bbl)`),1),e[8]||(e[8]=t("div",{class:"fw-semibold fs-7 text-success"},"Calculated Volume",-1))]),t("div",Z$,[t("div",eS,I(`${s.numberWithCommas((N=s.targetData)==null?void 0:N.measuredVolume)} (bbl)`),1),e[9]||(e[9]=t("div",{class:"fw-semibold fs-7 text-primary"},"Measured Volume",-1))])]),t("div",tS,[t("ul",sS,[($(!0),V(B,null,Z(s.tabs,_=>($(),V("li",{class:"nav-item",key:_==null?void 0:_.value},[t("div",{class:_e(["nav-link py-4 cursor-pointer text-active-primary fw-semibold text-hover-primary",{active:s.tabIndex===(_==null?void 0:_.value.toString())}]),"data-bs-toggle":"tab",onClick:e[1]||(e[1]=H=>s.setActiveTab(H)),"data-tab-index":_==null?void 0:_.value,role:"tab"},I(_==null?void 0:_.key),11,lS)]))),128))],512)]),t("div",oS,[s.tabIndex==s.VolumeTracking.Addition.toString()?($(),V("div",nS,[t("table",aS,[t("tbody",null,[($(!0),V(B,null,Z((T=s.targetData)==null?void 0:T.additions,_=>($(),V("tr",{key:_.id},[t("td",iS,I(s.formatDate(_==null?void 0:_.createdAt,"DD MMM YYYY")),1),t("td",dS,I(_==null?void 0:_.description),1),t("td",rS,I(`${_==null?void 0:_.volume} (bbl)`),1),t("td",null,[t("div",uS,[t("button",{class:"btn btn-icon btn-sm btn-blue me-3 btn-view",onClick:H=>s.toggleEditAdditionModal(_==null?void 0:_.id)},[t("span",fS,[i(l,{icon:"newReportIcon"})])],8,cS),t("button",{class:"btn btn-icon btn-sm btn-blue",onClick:H=>s.deleteItem(_==null?void 0:_.id)},[t("span",pS,[i(l,{icon:"trashIcon"})])],8,mS)])])]))),128))])])])):U("",!0),s.tabIndex==s.VolumeTracking.Loss.toString()?($(),V("div",gS,[t("table",bS,[t("tbody",null,[($(!0),V(B,null,Z((w=s.targetData)==null?void 0:w.losses,_=>($(),V("tr",{key:_.id},[t("td",vS,I(s.formatDate(_==null?void 0:_.createdAt,"DD MMM YYYY")),1),t("td",hS,I(_==null?void 0:_.description),1),t("td",yS,I(`${_==null?void 0:_.volume} (bbl)`),1),t("td",null,[t("div",wS,[t("button",{class:"btn btn-icon btn-sm btn-blue me-3 btn-view",onClick:H=>s.toggleEditLossModal(_==null?void 0:_.id)},[t("span",SS,[i(l,{icon:"newReportIcon"})])],8,$S),t("button",{class:"btn btn-icon btn-sm btn-blue",onClick:H=>s.deleteItem(_==null?void 0:_.id)},[t("span",kS,[i(l,{icon:"trashIcon"})])],8,_S)])])]))),128))])])])):U("",!0),s.tabIndex==s.VolumeTracking.Transfer.toString()?($(),V("div",DS,[t("table",IS,[t("tbody",null,[($(!0),V(B,null,Z((h=s.targetData)==null?void 0:h.transfers,_=>($(),V("tr",{key:_.id},[t("td",VS,I(s.formatDate(_==null?void 0:_.createdAt,"DD MMM YYYY")),1),t("td",PS,I(_==null?void 0:_.description),1),t("td",CS,I(`${_==null?void 0:_.volume} (bbl)`),1),t("td",null,[t("div",TS,[t("button",{class:"btn btn-icon btn-sm btn-blue me-3 btn-view",onClick:H=>s.toggleEditTransferModal(_==null?void 0:_.id)},[t("span",xS,[i(l,{icon:"newReportIcon"})])],8,FS),t("button",{class:"btn btn-icon btn-sm btn-blue",onClick:H=>s.deleteItem(_==null?void 0:_.id)},[t("span",AS,[i(l,{icon:"trashIcon"})])],8,NS)])])]))),128))])])])):U("",!0)])]))]}),_:1},8,["modelValue"])}const qS=Q(M$,[["render",HS]]),MS=G({name:"volume-tracking",components:{SvgIcon:X,BottomTool:pe,AddVolumeModal:q$,StorageOrPitModal:Yt,VolumeDetailsModal:qS,NoEntries:be},props:{setChildActiveTab:{type:Function,required:!1,default:()=>{}}},setup(s){const e=re(),v=Wt(),b=nt(),f=y(null),r=y(null),l=y(null),o=y(!1),a=y([]),d=y(""),n=ee("dailyReport");ie(()=>{n!=null&&n.getDailyReportId()?(m(),p()):u()});const u=async()=>{var x;n==null||n.createDailyReport({wellId:(x=e==null?void 0:e.params)==null?void 0:x.id,callback:{onSuccess:A=>{m(),p()}}})},m=async(x={dailyReportId:n==null?void 0:n.getDailyReportId(),page:1,limit:200})=>{o.value=!0,b.getVolumeTrackings({params:x,callback:{onSuccess:A=>{a.value=JSON.parse(JSON.stringify(A==null?void 0:A.items))},onFinish:A=>{o.value=!1}}})},p=async()=>{n!=null&&n.getDailyReportId()&&(o.value=!0,v.getProductPackageInventories({dailyReportId:n==null?void 0:n.getDailyReportId(),callback:{onSuccess:x=>{d.value=x==null?void 0:x.id},onFinish:x=>{o.value=!1}}}))},D=x=>{var A,M,z;(A=f==null?void 0:f.value)==null||A.setId(x,""),(M=f==null?void 0:f.value)==null||M.setType(me.Addition),(z=f==null?void 0:f.value)==null||z.show()},k=(x,A)=>{var M,z,q,R;(M=l==null?void 0:l.value)==null||M.hide(),(z=f==null?void 0:f.value)==null||z.setType(me.Addition),(q=f==null?void 0:f.value)==null||q.setId(x,A),(R=f==null?void 0:f.value)==null||R.show()},N=x=>{var A,M,z;(A=f==null?void 0:f.value)==null||A.setId(x,""),(M=f==null?void 0:f.value)==null||M.setType(me.Loss),(z=f==null?void 0:f.value)==null||z.show()},T=(x,A)=>{var M,z,q,R;(M=l==null?void 0:l.value)==null||M.hide(),(z=f==null?void 0:f.value)==null||z.setType(me.Loss),(q=f==null?void 0:f.value)==null||q.setId(x,A),(R=f==null?void 0:f.value)==null||R.show()},w=x=>{var A,M,z;(A=f==null?void 0:f.value)==null||A.setId(x,""),(M=f==null?void 0:f.value)==null||M.setType(me.Transfer),(z=f==null?void 0:f.value)==null||z.show()},h=(x,A)=>{var M,z,q,R;(M=l==null?void 0:l.value)==null||M.hide(),(z=f==null?void 0:f.value)==null||z.setType(me.Transfer),(q=f==null?void 0:f.value)==null||q.setId(x,A),(R=f==null?void 0:f.value)==null||R.show()},_=()=>{var x;(x=r==null?void 0:r.value)==null||x.show()},H=x=>{var A,M;(A=r==null?void 0:r.value)==null||A.setId(x),(M=r==null?void 0:r.value)==null||M.show()},C=x=>{var A,M;(A=l==null?void 0:l.value)==null||A.setId(x),(M=l==null?void 0:l.value)==null||M.show()},F=x=>{ne.deletionAlert({onConfirmed:()=>{j(x)}})},j=x=>{o.value=!0,b.deleteVolumeTracking({id:x,callback:{onSuccess:A=>{m()},onFinish:A=>{o.value=!1}}})};return{getVolumeTrackings:m,deleteVolumeTracking:F,numberWithCommas:ge,toggleAddAdditionModal:D,toggleAddLossModal:N,toggleAddTransferModal:w,toggleEditAdditionModal:k,toggleEditLossModal:T,toggleEditTransferModal:h,toggleAddStorageOrPitModal:_,toggleEditStorageOrPitModal:H,toggleVolumeDetailsModal:C,loading:o,volumeTrackingList:a,addVolumeModal:f,storageOrPitModal:r,volumeDetailsModal:l,productAndPackageInventoryId:d}}}),jS={class:"card h-100 my-8 volume-tracking"},zS={class:"card-body"},ES={key:0,class:"text-center"},LS={key:1},RS={key:1,class:"row gap-10"},BS=["onClick"],OS={class:"d-flex flex-wrap justify-content-between align-items-center gap-3"},US={class:"mb-0 text-primary"},WS={class:"d-flex flex-column gap-3"},GS={class:"d-flex flex-wrap align-items-center justify-content-between pb-3 border-bottom-dashed border-bottom-1 border-blue-light"},YS={class:"fw-700 fs-7 text-gray-700"},JS={class:"d-flex flex-wrap align-items-center justify-content-between pb-3 border-bottom-dashed border-bottom-1 border-blue-light"},QS={class:"fw-700 fs-7 text-gray-700"},KS={class:"d-flex flex-wrap align-items-center justify-content-between pb-3 border-bottom-dashed border-bottom-1 border-blue-light"},XS={class:"fw-700 fs-7 text-gray-700"},ZS={class:"d-flex flex-wrap align-items-center justify-content-between"},e2={class:"fw-700 fs-7 text-gray-700"},t2={class:"d-flex flex-wrap gap-3 align-items-center justify-content-center"},s2={class:"border border-gray-300 border-dashed rounded p-3"},l2={class:"fw-700 fs-6 text-gray-700"},o2={class:"border border-gray-300 border-dashed rounded p-3"},n2={class:"fw-700 fs-6 text-gray-700"},a2={class:"d-flex align-items-center gap-2 position-absolute top-0 end-0 transform-top-50"},i2=["onClick"],d2={class:"svg-icon svg-icon-3"},r2=["onClick"],u2={class:"svg-icon svg-icon-3"},c2=["onClick"],f2={class:"svg-icon svg-icon-3"},m2=["onClick"],p2={class:"svg-icon svg-icon-3"},g2=["onClick"],b2={class:"svg-icon svg-icon-3 text-danger"};function v2(s,e,v,b,f,r){const l=S("NoEntries"),o=S("SvgIcon"),a=S("el-tooltip"),d=S("BottomTool"),n=S("AddVolumeModal"),u=S("StorageOrPitModal"),m=S("VolumeDetailsModal");return $(),V(B,null,[t("div",jS,[e[7]||(e[7]=t("div",{class:"card-header d-flex flex-wrap align-items-center"},[t("h1",{class:"mb-0 me-4 text-gray-900"},"Volume Tracking")],-1)),t("div",zS,[s.loading?($(),V("div",ES,e[0]||(e[0]=[t("div",{class:"spinner-border text-primary",role:"status"},[t("span",{class:"sr-only"},"Loading...")],-1)]))):($(),V("div",LS,[s.volumeTrackingList.length===0?($(),W(l,{key:0,addNew:s.toggleAddStorageOrPitModal},null,8,["addNew"])):($(),V("div",RS,[($(!0),V(B,null,Z(s.volumeTrackingList,p=>($(),V("div",{key:p==null?void 0:p.id,class:"col-4 card rounded-3 shadow-sm border-top border-top-2 border-primary position-relative card-item"},[t("div",{class:"card-body gap-5 pt-10 d-flex flex-column",onClick:D=>s.toggleVolumeDetailsModal(p==null?void 0:p.id)},[t("div",OS,[t("h5",US,I(p==null?void 0:p.name),1),t("span",{class:_e(["badge px-3 py-2",{"badge-light-success":p==null?void 0:p.status,"badge-light-danger":!(p!=null&&p.status)}])},I(p!=null&&p.status?"Active":"Inactive"),3)]),t("div",WS,[t("div",GS,[e[1]||(e[1]=t("span",{class:"fw-semibold fs-7 text-gray-700"},"Type",-1)),t("span",YS,I(p==null?void 0:p.storageType),1)]),t("div",JS,[e[2]||(e[2]=t("span",{class:"fw-semibold fs-7 text-gray-700"},"Total Additions",-1)),t("span",QS,I(s.numberWithCommas((p==null?void 0:p.totalAdditions)||0)),1)]),t("div",KS,[e[3]||(e[3]=t("span",{class:"fw-semibold fs-7 text-gray-700"},"Total Losses",-1)),t("span",XS,I(s.numberWithCommas((p==null?void 0:p.totalLosses)||0)),1)]),t("div",ZS,[e[4]||(e[4]=t("span",{class:"fw-semibold fs-7 text-gray-700"},"Total Transfers",-1)),t("span",e2,I(s.numberWithCommas((p==null?void 0:p.totalTransfers)||0)),1)])]),t("div",t2,[t("div",s2,[t("div",l2,I(`${s.numberWithCommas((p==null?void 0:p.calculatedVolume)||0)} (bbl)`),1),e[5]||(e[5]=t("div",{class:"fw-semibold fs-7 text-success"}," Calculated Volume ",-1))]),t("div",o2,[t("div",n2,I(`${s.numberWithCommas(p==null?void 0:p.measuredVolume)} (bbl)`),1),e[6]||(e[6]=t("div",{class:"fw-semibold fs-7 text-primary"}," Measured Volume ",-1))])])],8,BS),t("div",a2,[i(a,{content:"Addition",placement:"top",effect:"customize"},{default:c(()=>[t("button",{class:"btn rounded-circle btn-icon btn-sm btn-view btn-action bg-light",onClick:D=>s.toggleAddAdditionModal(p==null?void 0:p.id)},[t("span",d2,[i(o,{icon:"addIcon"})])],8,i2)]),_:2},1024),i(a,{content:"Loss",placement:"top",effect:"customize"},{default:c(()=>[t("button",{class:"btn rounded-circle btn-icon btn-sm btn-action bg-light btn-view",onClick:D=>s.toggleAddLossModal(p==null?void 0:p.id)},[t("span",u2,[i(o,{icon:"minusIcon"})])],8,r2)]),_:2},1024),i(a,{content:"Transfer",placement:"top",effect:"customize"},{default:c(()=>[t("button",{class:"btn rounded-circle btn-icon btn-action btn-sm bg-light btn-archive",onClick:D=>s.toggleAddTransferModal(p==null?void 0:p.id)},[t("span",f2,[i(o,{icon:"arrowFromLeft"})])],8,c2)]),_:2},1024),i(a,{content:"Edit",placement:"top",effect:"customize"},{default:c(()=>[t("button",{class:"btn rounded-circle btn-icon btn-action btn-sm bg-light btn-edit",onClick:D=>s.toggleEditStorageOrPitModal(p==null?void 0:p.id)},[t("span",p2,[i(o,{icon:"pencilIcon"})])],8,m2)]),_:2},1024),i(a,{content:"Delete",placement:"top",effect:"customize"},{default:c(()=>[t("button",{class:"btn rounded-circle btn-icon btn-sm btn-action bg-light btn-delete",onClick:D=>s.deleteVolumeTracking(p==null?void 0:p.id)},[t("span",b2,[i(o,{icon:"trashIcon"})])],8,g2)]),_:2},1024)])]))),128))]))]))])]),i(d,{addNew:s.toggleAddStorageOrPitModal,showHelpInfo:!1},null,8,["addNew"]),i(n,{ref:"addVolumeModal",loadPage:s.getVolumeTrackings,productAndPackageInventoryId:s.productAndPackageInventoryId},null,8,["loadPage","productAndPackageInventoryId"]),i(u,{ref:"storageOrPitModal",loadPage:s.getVolumeTrackings},null,8,["loadPage"]),i(m,{ref:"volumeDetailsModal",loadPage:s.getVolumeTrackings,editAddition:s.toggleEditAdditionModal,editLoss:s.toggleEditLossModal,editTransfer:s.toggleEditTransferModal},null,8,["loadPage","editAddition","editLoss","editTransfer"])],64)}const rs=Q(MS,[["render",v2]]),h2=["Home","Daily Report"],y2={[ve.General]:jt,[ve.MudProperties]:Rt,[ve.SiteEquipment]:ns,[ve.Tasks]:is,[ve.Product]:Qt,[ve.VolumeTracking]:rs,[ve.Costs]:It,[ve.Notes]:Ot},w2=G({name:"daily-report",components:{SvgIcon:X,General:jt,MudProperties:Rt,Customize:Hs,SiteEquipment:ns,Tasks:is,ProductPackage:Qt,VolumeTracking:rs,Costs:It,Notes:Ot,PageHeader:ms},setup(){const s=re(),e=y(ve.General),v=y(-1),b=y(),f=ot(()=>y2[e.value]),r=y(null),l=y(!1),o=ee("dailyReport");ie(()=>{var u,m;(u=s==null?void 0:s.params)!=null&&u.dailyReportId?o==null||o.setDailyReportId((m=s==null?void 0:s.params)==null?void 0:m.dailyReportId):o==null||o.resetDailyReportId()}),fs(()=>{o==null||o.resetDailyReportId()});const a=async u=>{const m=u.target;e.value!==ve.General&&e.value!==ve.MudProperties?e.value=Number(m.getAttribute("data-tab-index")):d()?ne.incompleteFormAlert({onConfirmed:()=>{e.value=Number(m.getAttribute("data-tab-index"))}},"You have unsaved changes. Are you sure you want to leave?"):e.value=Number(m.getAttribute("data-tab-index"))},d=()=>{var u,m;if(r!=null&&r.value&&((u=r==null?void 0:r.value)!=null&&u.isFormOfChildTabDirty))return(m=r==null?void 0:r.value)==null?void 0:m.isFormOfChildTabDirty()},n=u=>{v.value=u};return{currentTab:r,loading:l,EDailyTab:ve,dailyTabs:bs,breadcrumbs:h2,wellData:b,tabIndex:e,currentComponent:f,currentChildTabIndex:v,isSystemAdmin:De.checkRole(et.SystemAdmin),formatDate:ke,isValidDate:hs,setActiveTab:a,setChildActiveTab:n}}}),$2={class:"d-flex flex-wrap align-items-center justify-content-between w-100"},S2={class:"my-3"},_2={class:"d-flex align-items-between flex-column"},k2={class:"d-flex align-items-center gap-2 gap-lg-3"},D2={class:"nav nav-stretch nav-line-tabs border-0 daily-nav",role:"tablist",id:"kt_layout_builder_tabs",ref:"kt_layout_builder_tabs"},I2=["data-tab-index"],V2={key:0,class:"text-center mt-7"},P2={key:1};function C2(s,e,v,b,f,r){const l=S("PageHeader"),o=S("Customize");return $(),V(B,null,[t("div",$2,[t("div",S2,[i(l,{title:"Daily Report General",breadcrumbs:s.breadcrumbs},null,8,["breadcrumbs"])]),t("div",_2,[t("div",k2,[t("ul",D2,[($(!0),V(B,null,Z(s.dailyTabs,a=>($(),V("li",{class:"nav-item",key:a==null?void 0:a.value},[t("div",{class:_e(["nav-link cursor-pointer text-active-primary fw-semibold text-hover-primary fs-5",{active:s.tabIndex===(a==null?void 0:a.value)}]),onClick:e[0]||(e[0]=d=>s.setActiveTab(d)),"data-tab-index":a==null?void 0:a.value,role:"tab"},I(a==null?void 0:a.label),11,I2)]))),128))],512)])])]),s.loading?($(),V("div",V2,e[1]||(e[1]=[t("div",{class:"spinner-border text-primary",role:"status"},[t("span",{class:"sr-only"},"Loading...")],-1)]))):($(),V("div",P2,[($(),W(rt(s.currentComponent),{setChildActiveTab:s.setChildActiveTab,ref:"currentTab"},null,8,["setChildActiveTab"]))])),i(o,{currentTab:s.currentChildTabIndex},null,8,["currentTab"])],64)}const T2=Q(w2,[["render",C2],["__scopeId","data-v-48e90018"]]),F2=G({name:"provide-component",components:{DailyReportProvide:_s,DailyReportContent:T2},setup(){return{}}});function x2(s,e,v,b,f,r){const l=S("DailyReportContent"),o=S("DailyReportProvide");return $(),W(o,null,{default:c(()=>[i(l)]),_:1})}const Y2=Q(F2,[["render",x2]]);export{Y2 as default};
