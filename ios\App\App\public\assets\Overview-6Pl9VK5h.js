import{P as te}from"./PageHeader-l8HvxxsN.js";import{O as ce,P as g,Q as A,d as j,q as p,j as q,A as U,Z as me,_ as z,c as v,l as S,o as h,a as o,b as d,t as N,r as w,w as _,B as oe,p as se,$ as H,x as le,E as G,e as ae,m as J,H as K,M as X,F as ne,k as pe}from"./index-DalLS0_6.js";import{u as fe}from"./customer-CD9RajQq.js";import{C as ge}from"./CustomerModal-BJI2MaCc.js";import{T as he}from"./TablePagination-BmkwndgK.js";import{S as re}from"./SvgIcon-CfrWCA-H.js";import{T as be}from"./TableHeader-DGMH-x_O.js";import{S as ee,a as ve}from"./table-bhK9qpe4.js";import{f as we}from"./date-CCTVzEJd.js";import{h as R}from"./handleFailure-DrOe_u9W.js";import{y as L}from"./validator-BJ5Qi8qK.js";import{c as ye}from"./index.esm-C3uaQ3c9.js";const ie=ce("customerContact",()=>({getCustomerContacts:async({params:m,callback:a})=>{var c;const l=g.get(a,"onSuccess",g.noop),r=g.get(a,"onFinish",g.noop);try{A.setHeader();const i=await A.getWithParams("customersContact",m);l(((c=i.data)==null?void 0:c.data)||i.data)}catch(i){R(i,a)}finally{r()}},getContactDetails:async({id:m,callback:a})=>{var c;const l=g.get(a,"onSuccess",g.noop),r=g.get(a,"onFinish",g.noop);try{A.setHeader();const i=await A.get(`customersContact/${m}`);l(((c=i.data)==null?void 0:c.data)||i.data)}catch(i){R(i,a)}finally{r()}},updateCustomerContact:async({id:m,params:a,callback:l})=>{var i;const r=g.get(l,"onSuccess",g.noop),c=g.get(l,"onFinish",g.noop);try{A.setHeader();const n=await A.put(`customersContact/${m}`,a);r(((i=n.data)==null?void 0:i.data)||n.data)}catch(n){R(n,l)}finally{c()}},removeContact:async({ids:m,callback:a})=>{var c;const l=g.get(a,"onSuccess",g.noop),r=g.get(a,"onFinish",g.noop);try{A.setHeader();const i=await A.post("customersContact/delete",{ids:m});l(((c=i.data)==null?void 0:c.data)||i.data)}catch(i){R(i,a)}finally{r()}},createContact:async({params:m,callback:a})=>{var c;const l=g.get(a,"onSuccess",g.noop),r=g.get(a,"onFinish",g.noop);try{A.setHeader();const i=await A.post("customersContact",m);l(((c=i.data)==null?void 0:c.data)||i.data)}catch(i){R(i,a)}finally{r()}}})),Ce=j({name:"customer-modal",components:{SvgIcon:re},props:{isVisible:{type:Boolean,required:!0},close:{type:Function,required:!0},loadPage:{type:Function,required:!1}},setup(e){var O,T;const t=q(),V=p(!1),y=ie(),$={customerId:"",name:"",emailAddress:"",mobilePhone:"",officePhone:"",address:"",notes:"",primaryContact:!1,notifyOnNewReport:!1},m=p({...$}),a=p(null),l=p(!1),r=p(""),c=((T=(O=t.params)==null?void 0:O.customerId)==null?void 0:T.toString())||"";U(V,b=>{b===!1?P():i()});const i=async()=>{r.value&&(l.value=!0,y.getContactDetails({id:r.value,callback:{onSuccess:b=>{m.value={...b}},onFinish:()=>{l.value=!1}}}))},n=()=>{e.close()},x=b=>{r.value=b.toString()},C=ye({emailAddress:L.emailAddress,mobilePhone:L.mobilePhone,officePhone:L.officePhone}),E=p({name:[{required:!0,message:"Please type Contact Name",trigger:["blur","change"]}],emailAddress:[{required:!0,validator:(b,k,f)=>{C.fields.emailAddress.validate(k).then(()=>{f()}).catch(D=>{f(new Error(D.errors[0]))})},trigger:["blur","change"]}],mobilePhone:[{required:!0,validator:(b,k,f)=>{C.fields.mobilePhone.validate(k).then(()=>{f()}).catch(D=>{f(new Error(D.errors[0]))})},trigger:["blur","change"]}],officePhone:[{validator:(b,k,f)=>{k?C.fields.officePhone.validate(k).then(()=>{f()}).catch(D=>{f(new Error(D.errors[0]))}):f()},trigger:["blur","change"]}]}),F=()=>{a.value&&a.value.validate(b=>{var k,f,D;if(b){const B={...m.value,officePhone:((k=m.value)==null?void 0:k.officePhone)||null,address:((f=m.value)==null?void 0:f.address)||null,note:((D=m.value)==null?void 0:D.notes)||null,customerId:c};r.value?s(B):M(B)}})},M=async b=>{l.value=!0,y.createContact({params:b,callback:{onSuccess:k=>{var f;e.close(),(f=e==null?void 0:e.loadPage)==null||f.call(e)},onFinish:()=>{l.value=!1}}})},s=async b=>{r.value&&(l.value=!0,y.updateCustomerContact({id:r.value,params:b,callback:{onSuccess:k=>{var f;e.close(),(f=e==null?void 0:e.loadPage)==null||f.call(e)},onFinish:()=>{l.value=!1}}}))},P=()=>{var b;r.value="",m.value={...$},(b=a==null?void 0:a.value)==null||b.resetFields()};return{id:r,modal:V,rules:E,loading:l,targetData:m,formRef:a,rolesOptions:me,submit:F,setId:x,reset:P,closeModal:n}}}),xe={key:0,class:"fixed top-0 right-0 bottom-0 left-0 z-40 flex items-center"},_e={class:"relative bg-card-background text-card-text max-h-11/12 w-11/12 overflow-x-scroll flex flex-col items-center gap-3 mx-auto p-5 rounded-xl md:overflow-hidden md:w-4/5 md:text-lg lg:w-2/5 lg:h-auto"},ke={class:"h-auto w-full flex flex-row items-center justify-between"},$e={class:"text-lg font-bold"},Pe={class:"h-auto w-full flex flex-col gap-3 md:flex-row md:gap-4"},Se={class:"h-auto w-full flex flex-col gap-3"},Ve={class:"h-auto w-full flex flex-col gap-2"},De={class:"h-auto w-full flex flex-col gap-2"},Ae={class:"h-auto w-full flex flex-col gap-2"},Ne={class:"h-auto w-full flex flex-col gap-3"},Ee={class:"h-auto w-full flex flex-col gap-2"},Fe={class:"h-auto w-full flex flex-col gap-2"},Ie={class:"h-auto w-full flex flex-col gap-2"},Me={class:"h-auto w-full flex flex-row items-center justify-between"},Re={class:"h-auto w-full flex flex-row items-center justify-between"},Oe={class:"h-auto w-full flex flex-row items-start mt-4 gap-3"},Te=["disabled"],Be=["disabled"],He={key:0,class:"indicator-label"},Le={key:1,class:"indicator-progress"};function Ue(e,t,V,y,$,m){const a=w("SvgIcon"),l=w("el-input"),r=w("el-form-item"),c=w("el-checkbox"),i=w("el-form");return e.isVisible?(h(),v("div",xe,[t[19]||(t[19]=o("div",{class:"absolute inset-0 bg-sidebar-background opacity-25"},null,-1)),o("div",_e,[o("div",ke,[o("h3",$e,N(e.id?"Edit Contact":"New Contact"),1),o("span",{class:"cursor-pointer",onClick:t[0]||(t[0]=(...n)=>e.closeModal&&e.closeModal(...n))},[d(a,{icon:"closeModalIcon"})])]),d(i,{id:"product_form",onSubmit:se(e.submit,["prevent"]),model:e.targetData,rules:e.rules,ref:"formRef",class:"h-auto w-full flex flex-col gap-3"},{default:_(()=>[o("div",Pe,[o("div",Se,[o("div",Ve,[t[10]||(t[10]=o("label",{class:"font-semibold"},"Name ",-1)),d(r,{prop:"name",class:"mt-auto"},{default:_(()=>[d(l,{modelValue:e.targetData.name,"onUpdate:modelValue":t[1]||(t[1]=n=>e.targetData.name=n),placeholder:"",name:"name"},null,8,["modelValue"])]),_:1})]),o("div",De,[t[11]||(t[11]=o("label",{class:"font-semibold"},"Email Address ",-1)),d(r,{prop:"emailAddress",class:"mt-auto"},{default:_(()=>[d(l,{modelValue:e.targetData.emailAddress,"onUpdate:modelValue":t[2]||(t[2]=n=>e.targetData.emailAddress=n),placeholder:"",name:"emailAddress"},null,8,["modelValue"])]),_:1})]),o("div",Ae,[t[12]||(t[12]=o("label",{class:"font-semibold"},"Address ",-1)),d(r,{prop:"address",class:"mt-auto"},{default:_(()=>[d(l,{class:"w-100",modelValue:e.targetData.address,"onUpdate:modelValue":t[3]||(t[3]=n=>e.targetData.address=n),placeholder:"",name:"address"},null,8,["modelValue"])]),_:1})])]),o("div",Ne,[o("div",Ee,[t[13]||(t[13]=o("label",{class:"font-semibold"},"Mobile Phone ",-1)),d(r,{prop:"mobilePhone",class:"mt-auto"},{default:_(()=>[d(l,{modelValue:e.targetData.mobilePhone,"onUpdate:modelValue":t[4]||(t[4]=n=>e.targetData.mobilePhone=n),placeholder:"",name:"mobilePhone"},null,8,["modelValue"])]),_:1})]),o("div",Fe,[t[14]||(t[14]=o("label",{class:"font-semibold"},"Office Phone ",-1)),d(r,{prop:"officePhone",class:"mt-auto"},{default:_(()=>[d(l,{class:"w-100",modelValue:e.targetData.officePhone,"onUpdate:modelValue":t[5]||(t[5]=n=>e.targetData.officePhone=n),placeholder:"",name:"officePhone"},null,8,["modelValue"])]),_:1})])])]),o("div",Ie,[t[15]||(t[15]=o("label",{class:"font-semibold"},"Note ",-1)),d(r,{prop:"notes",class:"mt-auto"},{default:_(()=>[d(l,{class:"h-auto w-full",modelValue:e.targetData.notes,"onUpdate:modelValue":t[6]||(t[6]=n=>e.targetData.notes=n),placeholder:"",name:"notes",type:"textarea",rows:3},null,8,["modelValue"])]),_:1})]),o("div",Me,[t[16]||(t[16]=o("label",{class:"font-semibold"},"Primary Contact? ",-1)),d(r,{prop:"primaryContact",style:{"margin-bottom":"0"}},{default:_(()=>[d(c,{modelValue:e.targetData.primaryContact,"onUpdate:modelValue":t[7]||(t[7]=n=>e.targetData.primaryContact=n),name:"primaryContact"},null,8,["modelValue"])]),_:1})]),o("div",Re,[t[17]||(t[17]=o("label",{class:"font-semibold"},"Notify on new report ",-1)),d(r,{prop:"notifyOnNewReport",style:{"margin-bottom":"0"}},{default:_(()=>[d(c,{modelValue:e.targetData.notifyOnNewReport,"onUpdate:modelValue":t[8]||(t[8]=n=>e.targetData.notifyOnNewReport=n),name:"notifyOnNewReport"},null,8,["modelValue"])]),_:1})]),o("div",Oe,[o("button",{type:"button",class:"bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover rounded-md px-4 py-2 font-semibold",onClick:t[9]||(t[9]=(...n)=>e.closeModal&&e.closeModal(...n)),disabled:e.loading}," Discard ",8,Te),o("button",{class:"bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover rounded-md px-4 py-2 font-semibold",type:"submit",disabled:e.loading},[e.loading?S("",!0):(h(),v("span",He," Save ")),e.loading?(h(),v("span",Le,t[18]||(t[18]=[oe(" Please wait... "),o("span",{class:"spinner-border spinner-border-sm align-middle ms-2"},null,-1)]))):S("",!0)],8,Be)])]),_:1},8,["onSubmit","model","rules"])])])):S("",!0)}const je=z(Ce,[["render",Ue]]),qe=j({name:"customer-list",components:{PageHeader:te,SvgIcon:re,TablePagination:he,ContactModal:je,TableHeader:be},setup(){var W,Y;const e=ie(),t=q(),V=ae(),y=p([]),$=p(!1),m=p(0),a=p(0),l=p(1),r=p(!1),c=p(""),i=p(!1),n=p([]),x=p(null),C=((Y=(W=t.params)==null?void 0:W.customerId)==null?void 0:Y.toString())||"",E=p(!1),F=p({sortDirection:ve.ASC,sortBy:ee.Name}),M=[{label:"",class:"w-25px",display:H()},{label:"FULL NAME",sortBy:ee.Name,class:"min-w-150px"},{label:"ADDRESS",class:"min-w-150px"},{label:"MOBILE NUMBER",class:"min-w-120px"},{label:"OFFICE NUMBER",class:"min-w-120px"},{label:"EMAIL",class:"min-w-150px"},{label:"PRIMARY CONTACT",class:"min-w-150px"},{label:"REPORT NOTIFICATION",class:"min-w-120px"},{label:"ACTIONS",class:"min-w-60px",display:H()}];le(()=>{s()}),U(l,()=>{s()}),U(y,u=>{$.value=n.value.length!==0&&u.length===n.value.length});const s=async()=>{r.value=!0,e.getCustomerContacts({params:{customerId:C,name:c.value.trim()||null,page:l.value,limit:10,...F.value},callback:{onSuccess:u=>{n.value=[...u==null?void 0:u.items],m.value=u==null?void 0:u.totalPage,a.value=u==null?void 0:u.total,l.value=u==null?void 0:u.page},onFinish:()=>{r.value=!1}}})},P=u=>{F.value={...u},s()},O=()=>{l.value!==1?l.value=1:s()},T=()=>{E.value=!E.value},b=u=>{var I;(I=x==null?void 0:x.value)==null||I.setId(u),E.value=!E.value},k=()=>{G.deletionAlert({onConfirmed:()=>{Q(y.value,!0)}})},f=u=>{G.deletionAlert({onConfirmed:()=>{Q([u])}})},D=u=>{l.value=u},B=()=>{i.value=!i.value},de=u=>{var I;(I=u==null?void 0:u.target)!=null&&I.checked?y.value=n.value.map(Z=>Z.id):y.value=[]},ue=u=>{V.push({path:`/users/${u}`})},Q=async(u,I=!1)=>{r.value=!0,e.removeContact({ids:u,callback:{onSuccess:Z=>{I&&(y.value=[]),s()},onFinish:()=>{r.value=!1}}})};return{sortParams:F,tableHeader:M,search:c,loading:r,contactModal:x,checkedRows:y,checkAll:$,contactList:n,currentPage:l,totalElements:a,pageCount:m,showContactModal:i,isModalVisible:E,searchCustomerContacts:O,pageChange:D,toggleFilter:B,deleteEngineer:f,formatDate:we,view:ue,onRemove:k,toggleNewCustomer:T,toggleEditCustomer:b,isAdmin:H,onToggleCheckAll:de,getCustomerContacts:s,onSort:P}}}),ze={class:"h-auto w-11/12 mx-auto bg-card-background text-card-text rounded-lg p-4 lg:w-4/5 lg:min-w-[1560px]"},Qe={class:"h-auto w-full flex flex-col gap-4 items-start"},We={class:"h-auto w-full flex items-center"},Ye={class:"h-auto w-full flex flex-row justify-end gap-4"},Ze={key:0,class:"text-center p-5"},Ge={key:2,class:"h-auto w-full overflow-x-scroll px-4 lg:overflow-hidden lg:min-w-fit"},Je={class:"lg:mx-auto"},Ke={class:"font-bold whitespace-nowrap"},Xe={class:"font-bold"},et={key:0},tt={class:"form-check form-check-sm form-check-custom form-check-solid"},ot={key:1,class:"p-4"},st={key:0},lt={class:"form-check form-check-sm form-check-custom form-check-solid"},at=["value"],nt={class:"w-36 p-4"},rt={class:"font-semibold whitespace-nowrap"},it={class:"w-36 p-4"},dt={class:"font-semibold whitespace-nowrap"},ut={class:"w-36 p-4"},ct={class:"font-semibold whitespace-nowrap"},mt={class:"w-36 p-4"},pt={class:"font-semibold whitespace-nowrap"},ft={class:"w-36 p-4"},gt={class:"font-semibold whitespace-nowrap"},ht={class:"w-36 p-4"},bt={class:"flex justify-center"},vt=["checked"],wt={class:"w-36 p-4"},yt={class:"flex justify-center"},Ct=["checked"],xt={key:1},_t={class:"h-auto w-full flex flex-row items-center justify-evenly gap-2"},kt=["onClick"],$t=["onClick"],Pt={class:"text-danger"},St={class:"h-auto w-full flex flex-col items-center my-5"},Vt={key:0,class:"font-semibold"};function Dt(e,t,V,y,$,m){var F,M;const a=w("SvgIcon"),l=w("el-icon"),r=w("el-input"),c=w("el-form-item"),i=w("el-form"),n=w("el-empty"),x=w("TableHeader"),C=w("TablePagination"),E=w("ContactModal");return h(),v("div",ze,[o("div",Qe,[t[8]||(t[8]=o("h1",{class:"text-lg font-bold"},"Customer Contacts",-1)),o("div",We,[d(i,{onSubmit:se(e.searchCustomerContacts,["prevent"]),class:"w-full"},{default:_(()=>[d(c,{class:"w-full"},{default:_(()=>[d(r,{placeholder:"Search",modelValue:e.search,"onUpdate:modelValue":t[0]||(t[0]=s=>e.search=s),name:"search",size:"large"},{prefix:_(()=>[d(l,{class:"el-input__icon"},{default:_(()=>[d(a,{icon:"searchIcon"})]),_:1})]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["onSubmit"])]),o("div",Ye,[e.checkedRows.length!==0&&e.isAdmin()?(h(),v("button",{key:0,class:"bg-danger rounded-md px-4 py-2 font-semibold",onClick:t[1]||(t[1]=(...s)=>e.onRemove&&e.onRemove(...s))}," Remove ")):S("",!0),e.isAdmin()?(h(),v("button",{key:1,class:"bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover rounded-md px-4 py-2 font-semibold flex flex-row items-center gap-2 justify-between",onClick:t[2]||(t[2]=(...s)=>e.toggleNewCustomer&&e.toggleNewCustomer(...s))},[d(a,{icon:"addIcon"}),t[6]||(t[6]=oe(" New "))])):S("",!0)]),e.loading?(h(),v("div",Ze,t[7]||(t[7]=[o("div",{class:"spinner-border text-primary",role:"status"},[o("span",{class:"sr-only"},"Loading...")],-1)]))):e.contactList.length===0?(h(),J(n,{key:1,description:"No Data"})):(h(),v("div",Ge,[o("table",Je,[o("thead",Ke,[o("tr",Xe,[d(x,{headers:e.tableHeader,sortBy:e.sortParams.sortBy,sortDirection:e.sortParams.sortDirection,onSort:e.onSort},{customHeader:_(({header:s})=>[s.label===""?(h(),v("div",et,[o("div",tt,[K(o("input",{class:"h-4 w-4",type:"checkbox","onUpdate:modelValue":t[3]||(t[3]=P=>e.checkAll=P),onChange:t[4]||(t[4]=(...P)=>e.onToggleCheckAll&&e.onToggleCheckAll(...P))},null,544),[[X,e.checkAll]])])])):(h(),v("div",ot,N(s.label),1))]),_:1},8,["headers","sortBy","sortDirection","onSort"])])]),o("tbody",null,[(h(!0),v(ne,null,pe(e.contactList,s=>(h(),v("tr",{key:s.id,class:"font-bold my-2 text-center border-b-[1px] border-dashed border-light-border"},[e.isAdmin()?(h(),v("td",st,[o("div",lt,[K(o("input",{class:"h-4 w-4",type:"checkbox",value:s.id,"onUpdate:modelValue":t[5]||(t[5]=P=>e.checkedRows=P)},null,8,at),[[X,e.checkedRows]])])])):S("",!0),o("td",nt,[o("span",rt,N(s==null?void 0:s.name),1)]),o("td",it,[o("span",dt,N(s==null?void 0:s.address),1)]),o("td",ut,[o("span",ct,N(s==null?void 0:s.mobilePhone),1)]),o("td",mt,[o("span",pt,N(s==null?void 0:s.officePhone),1)]),o("td",ft,[o("span",gt,N(s==null?void 0:s.emailAddress),1)]),o("td",ht,[o("div",bt,[o("input",{class:"h-4 w-4",type:"checkbox",checked:s==null?void 0:s.primaryContact,disabled:""},null,8,vt)])]),o("td",wt,[o("div",yt,[o("input",{class:"h-4 w-4",type:"checkbox",checked:s==null?void 0:s.notifyOnNewReport,disabled:""},null,8,Ct)])]),e.isAdmin()?(h(),v("td",xt,[o("div",_t,[o("button",{class:"bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover font-semibold h-8 w-8 flex items-center justify-center rounded-lg pl-[7.5px] pt-[1px] md:h-10 md:w-10 md:pl-[3px] md:pt-[5px] md:pb-[3.75px]",onClick:P=>e.toggleEditCustomer((s==null?void 0:s.id)??"")},[d(a,{icon:"newReportIcon",classname:"md:h-6 md:w-6"})],8,kt),o("button",{class:"bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover font-semibold h-8 w-8 flex items-center justify-center rounded-lg pl-[7.5px] pt-[1px] md:h-10 md:w-10 md:pl-[3px] md:pb-[6px]",onClick:P=>e.deleteEngineer(s==null?void 0:s.id)},[o("span",Pt,[d(a,{icon:"trashIcon",classname:"md:h-7 md:w-7"})])],8,$t)])])):S("",!0)]))),128))])])])),o("div",St,[(F=e.contactList)!=null&&F.length?(h(),v("div",Vt,N(`Showing ${(e.currentPage-1)*10+1} to ${(M=e.contactList)==null?void 0:M.length} of ${e.totalElements} entries`),1)):S("",!0),e.pageCount>=1?(h(),J(C,{key:1,"total-pages":e.pageCount,total:e.totalElements,"per-page":10,"current-page":e.currentPage,onPageChange:e.pageChange},null,8,["total-pages","total","current-page","onPageChange"])):S("",!0)])]),d(E,{isVisible:e.isModalVisible,close:e.toggleNewCustomer,ref:"contactModal",loadPage:e.getCustomerContacts},null,8,["isVisible","close","loadPage"])])}const At=z(qe,[["render",Dt]]),Nt=j({name:"customer-overview",components:{PageHeader:te,CustomerModal:ge,CustomerList:At},props:{isVisible:{type:Boolean,required:!0},close:{type:Function,required:!0}},setup(){var n,x;const e=fe(),t=p(!1),V=q(),y=ae(),$=p(),m=p(null),a=((x=(n=V.params)==null?void 0:n.customerId)==null?void 0:x.toString())||"",l=p(!1);le(()=>{a&&c()});const r=()=>{var C;(C=m==null?void 0:m.value)==null||C.setId(a),l.value=!l.value},c=async()=>{t.value=!0,e.getCustomerDetails({id:a,callback:{onSuccess:C=>{$.value=C},onFinish:()=>{t.value=!1}}})};return{loading:t,customerModal:m,customerDetails:$,isModalVisible:l,back:()=>{y.go(-1)},toggleEditCompany:r,getCustomerDetails:c,isAdmin:H}}}),Et={key:0,class:"text-center my-auto"},Ft={class:"h-auto w-11/12 mx-auto my-4 bg-card-background text-card-text rounded-xl flex flex-col items-center lg:w-4/5 lg:min-w-[1560px]"},It={class:"h-auto w-full p-4 flex flex-col gap-3 items-center border-b-[1px] border-dashed border-light-border"},Mt={class:"h-auto w-full flex flex-row items-center justify-end gap-3 md:justify-start"},Rt={class:"h-auto w-full p-4 flex flex-col gap-3 items-start md:flex-row"},Ot={class:"h-auto w-full flex flex-col gap-2 group"},Tt={class:"group-hover:text-primary"},Bt={class:"h-auto w-full flex flex-col gap-2 group"},Ht={class:"group-hover:text-primary"};function Lt(e,t,V,y,$,m){var c,i,n,x;const a=w("PageHeader"),l=w("CustomerList"),r=w("CustomerModal");return h(),v(ne,null,[e.loading?(h(),v("div",Et,t[2]||(t[2]=[o("div",{class:"spinner-border text-primary",role:"status"},[o("span",{class:"sr-only"},"Loading...")],-1)]))):S("",!0),d(a,{title:"Customer details",breadcrumbs:[((i=(c=e.customerDetails)==null?void 0:c.company)==null?void 0:i.name)||"","Customer","Customer details"]},null,8,["breadcrumbs"]),o("div",Ft,[o("div",It,[t[3]||(t[3]=o("h1",{class:"text-lg font-bold self-start"}," Customer Overview ",-1)),o("div",Mt,[e.isAdmin()?(h(),v("button",{key:0,class:"bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover rounded-md px-4 py-2 font-semibold",onClick:t[0]||(t[0]=(...C)=>e.toggleEditCompany&&e.toggleEditCompany(...C))}," Edit ")):S("",!0),o("button",{type:"button",class:"bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover rounded-md px-4 py-2 font-semibold",onClick:t[1]||(t[1]=(...C)=>e.back&&e.back(...C))}," Back ")])]),o("div",Rt,[o("div",Ot,[t[4]||(t[4]=o("h4",{class:"font-semibold"},"Customer/Company Name",-1)),o("p",Tt,N((n=e.customerDetails)==null?void 0:n.customerName),1)]),o("div",Bt,[o("div",null,[t[5]||(t[5]=o("h4",{class:"font-semibold"},"Notes",-1)),o("p",Ht,N((x=e.customerDetails)==null?void 0:x.notes),1)])])])]),o("div",null,[d(l)]),d(r,{isVisible:e.isModalVisible,close:e.toggleEditCompany,ref:"customerModal",loadPage:e.getCustomerDetails},null,8,["isVisible","close","loadPage"])],64)}const eo=z(Nt,[["render",Lt]]);export{eo as default};
