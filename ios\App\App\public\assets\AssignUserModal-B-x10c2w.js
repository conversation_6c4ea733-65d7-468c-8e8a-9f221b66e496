import{S as $}from"./SvgIcon-CMhyaXWN.js";import{d as L,q as c,A as C,Z as q,E as I,_ as B,c as u,l as b,o as r,a as s,b as w,t as f,r as g,B as U,a5 as j,w as k,m as N,F as S,k as V}from"./index-CGNRhvz7.js";import{U as M}from"./UserModal-Ck8RxOB2.js";const P=L({name:"assign-user-modal",components:{SvgIcon:$,UserModal:M},props:{title:{type:String,required:!0},initialAddUserValue:{type:Object,required:!1},userList:{type:Array,required:!0},onAdd:{type:Function,required:!0},onSearch:{type:Function,required:!0},loadingSearch:{type:Boolean,required:!1},isVisible:{type:Boolean,required:!0},close:{type:Function,required:!0}},setup(l){const t=c(!1),h=c(!1),i=c(null),p=c(!1),d=c([]),n=c([]);C(d,o=>{t.value=l.userList.every(a=>o.some(m=>m===a.id))}),C(h,o=>{o===!1&&v()});const A=o=>{n.value.some(a=>a.id===o.id)?n.value=n.value.filter(a=>a.id!==o.id):n.value.push(o)},_=()=>{h.value=!0},x=()=>{l.close(),p.value=!1,v()},v=()=>{h.value=!1,n.value=[],d.value=[]},e=()=>{var o,a;l!=null&&l.initialAddUserValue&&((o=i==null?void 0:i.value)==null||o.setInitialValue(l==null?void 0:l.initialAddUserValue)),(a=i==null?void 0:i.value)==null||a.show()},y=()=>{p.value=!0,l.onAdd(n.value.map(o=>o.id))};return{selectedUserIds:d,checkAll:t,selectedUsers:n,modal:h,loading:p,rolesOptions:q,userModal:i,show:_,hide:v,closeModal:x,handleDelete:o=>{n.value=n.value.filter(a=>a.id!==o),d.value=d.value.filter(a=>a!==o)},onClickAddNewUser:e,onClickAdd:y,handleCheckAll:o=>{o?(n.value=Array.from(new Set(n.value.concat(l.userList))),d.value=Array.from(new Set(d.value.concat(l.userList.map(a=>a.id))))):(n.value=n.value.filter(a=>!l.userList.some(m=>m.id===a.id)),d.value=d.value.filter(a=>!l.userList.some(m=>m.id===a)))},handleSelectOption:A,afterAddNewUser:o=>{n.value.push({id:o}),y(),I.toast("Added new user Successfully!","success","top-right")}}}}),D={key:0,class:"fixed top-0 right-0 bottom-0 left-0 flex flex-col items-center justify-center bg-dark z-40"},F={class:"bg-white h-4/5 overflow-y-scroll w-11/12 rounded-xl p-4 flex flex-col gap-4"},O={class:"h-auto w-full pb-3 flex flex-row items-center justify-between border-b-2 border-grey-300"},E={class:"text-lg font-bold text-dark"},z={class:"h-auto w-fullflex flex-row items-center justify-start"},T={class:"font-bold text-primary"},Y={class:"flex flex-col py-4 cursor-pointer"},Z={class:"flex flex-row items-center gap-2"},G=["src"],H={class:"flex flex-col items-start"},J={class:"font-bold text-gray-900 hover:text-primary"},K={class:"font-semibold text-gray-400"},Q={class:"h-4/5 w-full overflow-y-scroll mt-4"},R=["src"],W={class:"flex flex-col items-center"},X={class:"flex flex-col"},ee={href:"#",class:"font-bold text-gray-900 hover:text-primary"},le={class:"font-semibold text-gray-400"},se=["onClick"],te={class:"text-danger"},oe={class:"h-auto w-full flex flex-row items-center justify-between"},ae=["disabled"],ne=["disabled"],de={key:0,class:"indicator-label"},ie={key:1,class:"indicator-progress"};function re(l,t,h,i,p,d){const n=g("SvgIcon"),A=g("el-checkbox"),_=g("el-option"),x=g("el-select"),v=g("UserModal");return l.isVisible?(r(),u("div",D,[s("div",F,[s("div",O,[s("h3",E,f(l.title),1),s("span",{class:"cursor-pointer",onClick:t[0]||(t[0]=(...e)=>l.closeModal&&l.closeModal(...e))},[w(n,{icon:"closeModalIcon"})])]),t[7]||(t[7]=s("p",{class:"text-gray-600 text-xs text-center"},[U(" You can add Users from existing list or create a new user by clicking on "),s("span",{class:"text-primary"},"“Add New User”"),U(" if you can not find out any from the existing ones. ")],-1)),w(x,{height:"300px",multiple:"",filterable:"",remote:"","reserve-keyword":"",placeholder:"Please enter a keyword","remote-method":l.onSearch,loading:l.loadingSearch,modelValue:l.selectedUserIds,"onUpdate:modelValue":t[2]||(t[2]=e=>l.selectedUserIds=e)},j({default:k(()=>[(r(!0),u(S,null,V(l.userList,e=>(r(),N(_,{class:"assign-user-select select-option px-5",key:e.id,label:`${e.firstName} ${e.lastName}`,value:e.id,onClick:y=>l.handleSelectOption(e)},{default:k(()=>[s("div",Y,[s("div",Z,[s("img",{class:"h-11 w-11 rounded-full",alt:"Pic",src:(e==null?void 0:e.avatar)||"/media/avatars/blank.png"},null,8,G),s("div",H,[s("div",J,f(`${e==null?void 0:e.firstName} ${e==null?void 0:e.lastName}`),1),s("div",K,f(e==null?void 0:e.email),1)])])])]),_:2},1032,["label","value","onClick"]))),128))]),_:2},[l.loadingSearch?void 0:{name:"header",fn:k(()=>[s("div",z,[l.userList.length!==0?(r(),N(A,{key:0,modelValue:l.checkAll,"onUpdate:modelValue":t[1]||(t[1]=e=>l.checkAll=e),onChange:l.handleCheckAll,class:"h-auto w-full assign-user-select check-box-add-all py-3 px-5"},{default:k(()=>[s("div",T," Add all "+f(`${l.userList.length} members`),1)]),_:1},8,["modelValue","onChange"])):b("",!0)])]),key:"0"}]),1032,["remote-method","loading","modelValue"]),s("div",Q,[(r(!0),u(S,null,V(l.selectedUsers,e=>(r(),u("div",{key:e==null?void 0:e.id,class:"h-auto w-full flex flex-row items-center justify-evenly gap-3 py-2 first:pt-0"},[s("img",{class:"h-11 w-11 rounded-full",alt:"Pic",src:(e==null?void 0:e.avatar)||"/media/avatars/blank.png"},null,8,R),s("div",W,[s("div",X,[s("a",ee,f(`${e==null?void 0:e.firstName} ${e==null?void 0:e.lastName}`),1),s("div",le,f(e==null?void 0:e.email),1)])]),s("button",{class:"bg-grey-300 h-8 w-8 rounded-lg px-1.5 py-0.5",onClick:y=>l.handleDelete((e==null?void 0:e.id)??"")},[s("span",te,[w(n,{icon:"trashIcon"})])],8,se)]))),128))]),s("div",oe,[s("button",{class:"bg-primary text-white rounded-md px-4 py-2 font-semibold",type:"button",onClick:t[3]||(t[3]=(...e)=>l.onClickAddNewUser&&l.onClickAddNewUser(...e)),disabled:l.loading},t[5]||(t[5]=[s("span",{class:"indicator-label"}," Add New User ",-1)]),8,ae),s("button",{class:"bg-primary text-white rounded-md px-4 py-2 font-semibold",type:"button",disabled:l.loading,onClick:t[4]||(t[4]=(...e)=>l.onClickAdd&&l.onClickAdd(...e))},[l.loading?b("",!0):(r(),u("span",de," Add ")),l.loading?(r(),u("span",ie,t[6]||(t[6]=[U(" Please wait... "),s("span",{class:"spinner-border spinner-border-sm align-middle ms-2"},null,-1)]))):b("",!0)],8,ne)]),w(v,{ref:"userModal",loadPage:l.afterAddNewUser},null,8,["loadPage"])])])):b("",!0)}const ge=B(P,[["render",re]]);export{ge as A};
