<template>
  <div
    class="h-1/12 w-full px-6 bg-sidebar-background border-b-4 border-light-border flex flex-row justify-between items-center"
  >
    <div class="h-auto w-full md:flex md:items-center md:justify-center">
      <router-link to="/">
        <img
          alt="Logo"
          src="/media/logos/opslink-light.png"
          class="h-15 w-auto md:h-40"
        />
      </router-link>
    </div>
    <div class="h-auto w-full flex flex-row items-center justify-end md:w-2/12">
      <span class="cursor-pointer app-sidebar-logo-default" @click="signOut"
        ><SvgIcon icon="logoutIcon" classname="text-icon-light"
      /></span>

      <SvgIcon icon="doubleLeftArrow" classname="text-icon-light" />
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent } from "vue";
import SvgIcon from "../../../constants/SvgIcon.vue";
import { useAuthStore } from "../../../stores/auth";
import { useRouter } from "vue-router";

export default defineComponent({
  name: "sidebar-logo",
  components: { SvgIcon },
  setup() {
    const store = useAuthStore();
    const router = useRouter();

    const signOut = () => {
      store.logout();
      router.push({ name: "sign-in" });
    };

    return {
      signOut,
    };
  },
});
</script>
