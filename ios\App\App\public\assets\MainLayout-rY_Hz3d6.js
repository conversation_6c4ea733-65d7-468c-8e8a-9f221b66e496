import{S as U}from"./SvgIcon-CMhyaXWN.js";import{_ as v,c as d,o as i,a as s,b as u,w as k,r as c,d as h,u as P,e as V,f as j,i as E,g as A,J as m,U as _,h as q,j as z,t as w,F as C,k as T,l as O,n as M,m as L,p as D,q as N,s as F,v as J,x as X,C as B,y as H,z as G}from"./index-CGNRhvz7.js";import{_ as K}from"./d-left-arrow-079-B3YbfCzd.js";const Q="/media/icons/menu-icon.svg",W={},Y={class:"flex flex-row items-center bg-white pt-15"},Z={class:"d-flex align-items-center flex-grow-1 flex-lg-grow-0 me-lg-15"};function ee(e,o){const n=c("router-link");return i(),d("div",Y,[s("div",{class:"p-4 cursor-pointer",onClick:o[0]||(o[0]=a=>e.$emit("toggle-sidebar"))},o[1]||(o[1]=[s("img",{src:Q},null,-1)])),s("div",Z,[u(n,{to:"/"},{default:k(()=>o[2]||(o[2]=[s("img",{alt:"Logo",src:"/media/logos/sidebar-logo-default.svg",class:"h-20px h-lg-30px app-sidebar-logo-default theme-light-show"},null,-1)])),_:1})])])}const oe=v(W,[["render",ee]]),se=h({name:"sidebar-logo",components:{SvgIcon:U},setup(){const e=P(),o=V();return{signOut:()=>{e.logout(),o.push({name:"sign-in"})}}}}),te="/media/logos/sidebar-logo-default.svg",ne={class:"h-1/12 w-full px-6 bg-dark border-b-[1px] border-grey-600 flex flex-row justify-between items-center"},re={class:"flex flex-row items-center"};function ie(e,o,n,a,t,S){const p=c("router-link"),g=c("SvgIcon");return i(),d("div",ne,[u(p,{to:"/"},{default:k(()=>o[1]||(o[1]=[s("img",{alt:"Logo",src:te,class:"h-30px app-sidebar-logo-default"},null,-1)])),_:1}),s("div",re,[s("span",{class:"cursor-pointer app-sidebar-logo-default",onClick:o[0]||(o[0]=(...f)=>e.signOut&&e.signOut(...f))},[u(g,{icon:"logoutIcon"})]),o[2]||(o[2]=s("img",{src:K},null,-1))])])}const ae=v(se,[["render",ie]]),le=h({name:"sidebar-menu",components:{SvgIcon:U},props:{close:{type:Function,required:!0}},setup(e){const{t:o,te:n}=j(),a=z(),t=E("userInfo");return{userProfile:t==null?void 0:t.userInfo,userRoleOptions:A,getUserRoles:()=>{var l,y,r,x,R;return(y=(l=t==null?void 0:t.userInfo)==null?void 0:l.value)!=null&&y.roles?((R=(x=(r=t==null?void 0:t.userInfo)==null?void 0:r.value)==null?void 0:x.roles)==null?void 0:R.map(b=>{var I;return{value:b==null?void 0:b.value,label:(I=q(b==null?void 0:b.value,A))==null?void 0:I.label}}))||[]:[]},translate:l=>n(l)?o(l):l,hasActiveChildren:l=>a.path.indexOf(l)!==-1,closeSidebar:()=>{e.close()}}},data(){const e=[{sectionTitle:"Home",route:"/wells/overview",icon:"homeMenu"},{sectionTitle:"Companies",route:"/companies",icon:"customerMenu"},{sectionTitle:"My Company",route:"/my-company",icon:"customerMenu"},{sectionTitle:"User Management",route:"/wells/overview",icon:"useMenu"},{sectionTitle:"Settings",route:"/wells/overview",icon:"settingMenu"}];return{menuConfig:(()=>{const n=[e[0]];return m.checkRole(_.SystemAdmin)&&n.push(e[1]),(m.checkRole(_.CompanyAdmin)||m.checkRole(_.Supervisor)||m.checkRole(_.Engineer))&&n.push(e[2]),n.push(e[3]),(m.checkRole(_.SystemAdmin)||m.checkRole(_.CompanyAdmin)||m.checkRole(_.Supervisor))&&n.push(e[4]),n})()}}}),ce={class:"h-full w-full flex flex-col"},de={class:"h-2/12 w-full border-b-[1px] border-grey-600 flex flex-row items-center gap-2 px-6 py-3"},ue=["src"],pe={class:"flex flex-col"},fe={class:"font-semibold text-white"},me={class:"text-xs font-semibold text-gray-400"},ge={class:"cursor-pointer self-start pt-3"},_e={class:"h-full w-full"},be={class:"pt-6 px-5"},ve={class:"menu-title"};function he(e,o,n,a,t,S){var f,$,l,y;const p=c("SvgIcon"),g=c("router-link");return i(),d("div",ce,[s("div",de,[s("img",{class:"h-4/5 w-auto rounded-2xl",src:((f=e.userProfile)==null?void 0:f.avatar)||"/media/avatars/blank.png"},null,8,ue),s("div",pe,[s("div",fe,w(`${($=e.userProfile)==null?void 0:$.firstName} ${(l=e.userProfile)==null?void 0:l.lastName}`),1),s("div",me,w((y=e.userProfile)==null?void 0:y.email),1),(i(!0),d(C,null,T(e.getUserRoles(),r=>(i(),d("div",{key:r.value,class:"font-semibold text-green-400 text-sm"},w(r.key),1))),128))]),s("div",ge,[u(p,{icon:"settingUser"})])]),s("div",_e,[s("div",be,[(i(!0),d(C,null,T(e.menuConfig,(r,x)=>(i(),d(C,{key:x},[r.sectionTitle&&r.route?(i(),d("div",{key:0,class:M({show:e.hasActiveChildren(r.route)})},[s("div",null,[r.route?(i(),L(g,{key:0,class:"mx-2 px-2 py-3 flex flex-row items-center gap-2 hover:bg-primary hover:rounded-2xl text-sm text-grey-400","active-class":"active",to:r.route,onClick:e.closeSidebar},{default:k(()=>[u(p,{icon:r.icon},null,8,["icon"]),s("span",ve,w(e.translate(r.sectionTitle)),1)]),_:2},1032,["to","onClick"])):O("",!0)])],2)):O("",!0)],64))),128))])])])}const Se=v(le,[["render",he]]),$e=h({name:"sidebar",props:{displaySidebar:{type:Boolean,required:!0}},methods:{closeSidebar(){this.$emit("update:displaySidebar",!1)}},components:{SidebarLogo:ae,SidebarMenu:Se}});function ye(e,o,n,a,t,S){const p=c("SidebarLogo"),g=c("SidebarMenu");return i(),d(C,null,[s("div",{class:M(["fixed left-0 top-0 bottom-0 right-0 bg-dark opacity-10 transform ease-in-out",{"translate-x-0":e.displaySidebar,"-translate-x-full":!e.displaySidebar}]),onClick:o[0]||(o[0]=(...f)=>e.closeSidebar&&e.closeSidebar(...f))},null,2),s("div",{class:M(["flex flex-col fixed top-0 left-0 h-full w-3/4 bg-dark transform transition-transform duration-300 ease-in-out z-10 pt-15",{"translate-x-0":e.displaySidebar,"-translate-x-full":!e.displaySidebar}]),onClick:o[1]||(o[1]=D(()=>{},["stop"]))},[u(p),u(g,{close:e.closeSidebar},null,8,["close"])],2)],64)}const xe=v($e,[["render",ye]]),we=h({__name:"UserInfoProvide",setup(e){const o=N(m.getUserInfo());return J("userInfo",{userInfo:o,updateUserInfo:a=>{const t={...o.value,...a};o.value={...t},m.saveUserInfo(JSON.stringify(t))}}),(a,t)=>F(a.$slots,"default")}}),Ce=h({name:"chat-box",setup(){return X(()=>{{const e=document.createElement("script");e.type="text/javascript",e.textContent=`
      window.embeddedChatbotConfig = {
        chatbotId: "${B}",
        domain: "${H}"
      };
    `,document.head.appendChild(e);const o=document.createElement("script");o.src=G,o.setAttribute("chatbotId",B),o.setAttribute("domain",H),o.defer=!0,document.head.appendChild(o)}}),{}}});function ke(e,o,n,a,t,S){return i(),d("div")}const Me=v(Ce,[["render",ke]]),Ue=h({name:"default-layout",components:{Sidebar:xe,SvgIcon:U,UserInfoProvide:we,Header:oe,ChatBox:Me},setup(){const e=N(!1);return{displaySidebar:e,toggleSidebar:()=>{e.value=!e.value}}}}),Re={class:"flex flex-col h-screen w-full bg-grey-300 overflow-y-scroll"};function Ie(e,o,n,a,t,S){const p=c("Header"),g=c("Sidebar"),f=c("RouterView"),$=c("UserInfoProvide");return i(),L($,null,{default:k(()=>[s("div",Re,[u(p,{onToggleSidebar:e.toggleSidebar},null,8,["onToggleSidebar"]),u(g,{displaySidebar:e.displaySidebar,"onUpdate:displaySidebar":e.toggleSidebar},null,8,["displaySidebar","onUpdate:displaySidebar"]),s("div",null,[u(f)])])]),_:1})}const Be=v(Ue,[["render",Ie]]);export{Be as default};
