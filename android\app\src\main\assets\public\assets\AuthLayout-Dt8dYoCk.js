import{d as o,_ as s,c as r,a as t,b as c,r as l,o as a}from"./index-BmHWvWFS.js";const d=o({name:"auth-layout",components:{},setup(){}}),u={class:"z-10 min-h-screen min-w-full flex flex-col items-center justify-center bg-screen-background md:bg-card-background"},i={class:"h-full w-full z-30 md:min-h-screen md:flex md:flex-col md:items-center md:justify-center"};function m(f,e,p,_,b,g){const n=l("router-view");return a(),r("div",u,[e[0]||(e[0]=t("div",{class:"z-20 absolute bg-[url('/media/well-background.png')] bg-no-repeat bg-[center_40%] bg-cover inset-0 w-full h-full"},null,-1)),t("div",i,[c(n)])])}const v=s(d,[["render",m]]);export{v as default};
