<template>
  <div v-if="loading" class="text-center my-auto">
    <div class="spinner-border text-primary" role="status">
      <span class="sr-only">Loading...</span>
    </div>
  </div>
  <PageHeader title="My Profile" :breadcrumbs="breadcrumbs" />
  <UserDetail
    v-if="!loading && userDetail"
    :userDetail="userDetail"
    :reloadUserData="getMyProfile"
  />
</template>

<script lang="ts">
import PageHeader from "@/components/PageHeader.vue";
import { useUserStore } from "@/stores/user";
import { defineComponent, inject, onMounted, ref } from "vue";
import UserDetail from "./UserDetail.vue";
import type { Provide } from "@/types/injection-types";

export default defineComponent({
  name: "my-profile",
  components: {
    UserDetail,
    PageHeader,
  },
  setup() {
    const userStore = useUserStore();
    const userDetail = ref<User.Info>();
    const loading = ref<boolean>(false);
    const breadcrumbs = ["My Profile", "Settings"];
    const userInfoProvide = inject<Provide.UserInfo>("userInfo");

    onMounted(() => {
      getMyProfile();
    });

    const getMyProfile = async (): Promise<void> => {
      loading.value = true;
      userStore.getMyProfile({
        callback: {
          onSuccess: (res: User.Info) => {
            userInfoProvide?.updateUserInfo({ ...res });
            userDetail.value = res;
          },
          onFinish: () => {
            loading.value = false;
          },
        },
      });
    };

    return {
      loading,
      userDetail,
      breadcrumbs,
      getMyProfile,
    };
  },
});
</script>
