import{S as F}from"./SuccessTemplate-BuLX5i7K.js";import{S as N}from"./SvgIcon-CMhyaXWN.js";import{R as H,a as A,b as L,c as M}from"./regex-BLjctcPP.js";import{v as b}from"./validator-6laVLK0J.js";import{d as B,u as T,q as c,a8 as $,_ as q,c as u,m as I,l as k,w as E,r as m,o as i,a as e,b as r,F as U,k as G,n as j,t as z,B as R}from"./index-CGNRhvz7.js";import{E as D,F as O,a as J}from"./vee-validate-CesDBK0n.js";import{c as K,a as f,b as Q}from"./index.esm-DXW765zG.js";import{_ as W}from"./well-logo-diufKTHF.js";var V=(s=>(s[s.REGISTER=1]="REGISTER",s[s.SUCCESS=2]="SUCCESS",s))(V||{});const X=B({name:"sign-up",components:{Field:J,VForm:O,ErrorMessage:D,SvgIcon:N,SuccessTemplate:F},setup(){const s=T(),t=c(1),g=c(!1),_={length:{isValid:!1,text:"At least 8 characters"},lowercase:{isValid:!1,text:"At least 1 lowercase character"},uppercase:{isValid:!1,text:"At least 1 uppercase character"},specialCharacter:{isValid:!1,text:"At least 1 number and 1 special character"}},y=(a="")=>{const o=b.validate(a,{pattern:H,errorsMessage:{pattern:"Incorrect password format."}}),w=b.validate(a,{pattern:A,errorsMessage:{pattern:"Incorrect password format."}}),p=b.validate(a,{pattern:L,errorsMessage:{pattern:"Incorrect password format."}}),C=b.validate(a,{pattern:M,errorsMessage:{pattern:"Incorrect password format."}});return h.value={length:{isValid:!o,text:"At least 8 characters"},lowercase:{isValid:!w,text:"At least 1 lowercase character"},uppercase:{isValid:!p,text:"At least 1 uppercase character"},specialCharacter:{isValid:!C,text:"At least 1 number and 1 special character"}},o||w||p||C||""},h=c({..._}),n=c(!0),l=c(),d=c(!0),x=K().shape({first_name:f().required().label("First Name"),last_name:f().required().label("Last Name"),email:f().min(4).required().email().label("Email"),password:f().required().test("password",function(a){if(a==="")return this.createError({path:"password",message:"Please make sure at least one submit inspection type checkbox is checked and try again"});{const o=y(a);return o!==""?this.createError({path:"password",message:o}):!0}}).label("Password"),password_confirmation:f().required().oneOf([Q("password")],"Passwords must match").label("Password Confirmation")});return{currentStep:t,STEP:V,registration:x,isHideConfirmPassword:d,isHidePassword:n,checkPassword:h,loading:g,userRegistered:l,onSubmitRegister:async a=>{const o={email:a.email,password:a.password,firstName:a.first_name,lastName:a.last_name};s.purgeAuth(),g.value=!0,await s.register({credentials:o,callback:{onSuccess:()=>{l.value={fullName:o.firstName+" "+o.lastName},t.value=2},onFinish:()=>{g.value=!1},onFailure:w=>{var p;$.fire({text:(p=w.response.data)==null?void 0:p.message,icon:"error",buttonsStyling:!1,confirmButtonText:"Try again!",heightAuto:!1,customClass:{confirmButton:"btn fw-semibold btn-light-danger"}})}}})},setIsHidePassword:a=>{n.value=a},setIsHideConfirmPassword:a=>{d.value=a}}}}),Y={class:"h-auto w-4/5 rounded-3xl mx-auto py-6 bg-white rounded-3 overflow-x-scroll align-self-center flex flex-col justify-center items-center"},Z={class:"flex flex-col mb-2 mx-3 h-auto w-full"},ee={class:"text-danger mt-[0.3rem]"},se={class:"flex flex-col mb-2 mx-3 h-auto w-full"},te={class:"text-danger mt-[0.3rem]"},ae={class:"flex flex-col mb-2 mx-3 h-auto w-full"},re={class:"text-danger mt-[0.3rem]"},oe={class:"flex flex-col mb-2 mx-3 h-auto w-full"},ne={class:"relative"},le={class:"text-danger mt-[0.3rem]"},ie={class:"flex flex-col mb-2 mx-3 h-auto w-full"},de={class:"relative"},ce={class:"fv-plugins-message-container"},me={className:"text-xs font-medium mb-4"},pe={class:"w-full mb-4 flex flex-row justify-between items-center"},ue={class:"font-semibold"},fe=["disabled"],ge={key:0,class:"text-white text-sm font-semibold"},we={key:1,class:"indicator-progress"};function be(s,t,g,_,y,h){var v;const n=m("Field"),l=m("ErrorMessage"),d=m("SvgIcon"),x=m("router-link"),S=m("VForm"),P=m("SuccessTemplate");return i(),u("div",Y,[s.currentStep===s.STEP.REGISTER?(i(),I(S,{key:0,class:"flex flex-col items-center w-4/5",novalidate:"",onSubmit:s.onSubmitRegister,id:"kt_login_signup_form","validation-schema":s.registration},{default:E(()=>[t[10]||(t[10]=e("img",{src:W,width:100,height:100},null,-1)),t[11]||(t[11]=e("div",{class:"text-dark mb-3 font-bold"},[e("h1",{class:"text-dark mb-0"},"Register")],-1)),e("div",Z,[t[2]||(t[2]=e("label",{class:"form-label tracking-wide font-bold text-dark"},"Email",-1)),r(n,{class:"rounded-lg bg-grey-300 pl-3",type:"email",placeholder:"",name:"email",autocomplete:"off"}),e("div",ee,[r(l,{name:"email"})])]),e("div",se,[t[3]||(t[3]=e("label",{class:"form-label tracking-wide font-bold text-dark"},"First Name",-1)),r(n,{class:"w-full rounded-lg bg-grey-300 pl-3",type:"text",placeholder:"",name:"first_name",autocomplete:"off"}),e("div",te,[r(l,{name:"first_name"})])]),e("div",ae,[t[4]||(t[4]=e("label",{class:"form-label tracking-wide font-bold text-dark"},"Last Name",-1)),r(n,{class:"w-full rounded-lg bg-grey-300 pl-3",type:"text",placeholder:"",name:"last_name",autocomplete:"off"}),e("div",re,[r(l,{name:"last_name"})])]),e("div",oe,[t[5]||(t[5]=e("label",{class:"form-label tracking-wide font-bold text-dark"},"Password",-1)),e("div",ne,[r(n,{tabindex:"2",class:"w-full rounded-lg bg-grey-300 pl-3",type:s.isHidePassword?"password":"input",name:"password",autocomplete:"off"},null,8,["type"]),e("span",{class:"svg-icon svg-icon-1 eye-icon absolute top-[0.3rem] right-1",onClick:t[0]||(t[0]=()=>s.setIsHidePassword(!s.isHidePassword))},[r(d,{icon:s.isHidePassword?"hidePasswordIcon":"showPasswordIcon"},null,8,["icon"])])]),e("div",le,[r(l,{name:"password"})])]),e("div",ie,[t[6]||(t[6]=e("label",{class:"form-label tracking-wide font-bold text-dark"},"Confirm Password",-1)),e("div",de,[r(n,{tabindex:"2",class:"w-full rounded-lg bg-grey-300 pl-3",type:s.isHideConfirmPassword?"password":"input",name:"password_confirmation",autocomplete:"off"},null,8,["type"]),e("span",{class:"svg-icon svg-icon-1 eye-icon absolute top-[0.3rem] right-1",onClick:t[1]||(t[1]=()=>s.setIsHideConfirmPassword(!s.isHideConfirmPassword))},[r(d,{icon:s.isHideConfirmPassword?"hidePasswordIcon":"showPasswordIcon"},null,8,["icon"])])]),e("div",ce,[r(l,{name:"password_confirmation"})])]),e("div",me,[t[7]||(t[7]=e("p",null,"Password must contain:",-1)),e("div",null,[(i(!0),u(U,null,G(s.checkPassword,(a,o)=>(i(),u("div",{key:o,class:j(["flex items-center mt-1 gap-2",a.isValid?"text-success":"text-danger"])},[r(d,{icon:a.isValid?"checkMarkIcon":"exclamationMarkIcon"},null,8,["icon"]),e("span",null,z(a.text),1)],2))),128))])]),e("div",pe,[e("div",ue,[r(x,{to:"/sign-in",class:"text-primary font-medium text-sm"},{default:E(()=>t[8]||(t[8]=[R(" Back to Login ")])),_:1})]),e("button",{type:"submit",class:"bg-primary rounded-md px-2 py-1",disabled:s.loading},[s.loading?k("",!0):(i(),u("span",ge," Continue ")),s.loading?(i(),u("span",we,t[9]||(t[9]=[R(" Please wait... "),e("span",{class:"spinner-border spinner-border-sm align-middle ms-2"},null,-1)]))):k("",!0)],8,fe)])]),_:1},8,["onSubmit","validation-schema"])):s.currentStep===s.STEP.SUCCESS?(i(),I(P,{key:1,message:`Hey ${(v=s.userRegistered)==null?void 0:v.fullName}, thanks for signing up!`,description:"Go to Login Page and start your journey now"},null,8,["message"])):k("",!0)])}const Ie=q(X,[["render",be]]);export{Ie as default};
