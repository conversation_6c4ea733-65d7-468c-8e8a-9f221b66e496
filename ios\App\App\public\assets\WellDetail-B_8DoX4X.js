import{P as X}from"./PageHeader-3hadTn26.js";import{S as W}from"./SvgIcon-CMhyaXWN.js";import{E as N,w as Y}from"./daily-report-BYo3Yy2S.js";import{O as M,P as g,Q as P,d as L,q as w,A as E,_ as A,m as C,o as $,w as I,a as n,b as y,r as _,c as S,l as q,B as U,p as R,t as V,x as B,E as j,F as H,k as x,R as Z,j as T,J as ee,U as te,S as ne,n as se}from"./index-CGNRhvz7.js";import{u as ae}from"./well-Caso2ZGG.js";import{W as G}from"./WellGeneralInfo-BKW7Ii__.js";import{T as O}from"./TablePagination-BmVxunEG.js";import{h as F}from"./handleFailure-DtTpu7r3.js";import{n as oe}from"./numberFormatter-C7uP7NWj.js";import"./date-CvSHk5ED.js";import"./navigation-guard-BSVpYbbP.js";import"./company-oDyd0dWV.js";import"./customer-C9SausZF.js";import"./user-KFDu8xJF.js";import"./d-left-arrow-079-B3YbfCzd.js";const z=M("interval",()=>({getIntervals:async({params:s,callback:a})=>{var i;const r=g.get(a,"onSuccess",g.noop),c=g.get(a,"onFinish",g.noop);try{P.setHeader();const l=await P.getWithParams("intervals",s);r(((i=l.data)==null?void 0:i.data)||l.data)}catch(l){F(l,a)}finally{c()}},getIntervalDetails:async({id:s,callback:a})=>{var i;const r=g.get(a,"onSuccess",g.noop),c=g.get(a,"onFinish",g.noop);try{P.setHeader();const l=await P.get(`intervals/${s}`);r(((i=l.data)==null?void 0:i.data)||l.data)}catch(l){F(l,a)}finally{c()}},updateInterval:async({id:s,params:a,callback:r})=>{var l;const c=g.get(r,"onSuccess",g.noop),i=g.get(r,"onFinish",g.noop);try{P.setHeader();const o=await P.put(`intervals/${s}`,a);c(((l=o.data)==null?void 0:l.data)||o.data)}catch(o){F(o,r)}finally{i()}},deleteInterval:async({id:s,callback:a})=>{var i;const r=g.get(a,"onSuccess",g.noop),c=g.get(a,"onFinish",g.noop);try{P.setHeader();const l=await P.delete(`intervals/${s}`);r(((i=l.data)==null?void 0:i.data)||l.data)}catch(l){F(l,a)}finally{c()}},createInterval:async({params:s,callback:a})=>{var i;const r=g.get(a,"onSuccess",g.noop),c=g.get(a,"onFinish",g.noop);try{P.setHeader();const l=await P.post("intervals",s);r(((i=l.data)==null?void 0:i.data)||l.data)}catch(l){F(l,a)}finally{c()}}})),le=L({name:"interval-modal",components:{SvgIcon:W},props:{loadPage:{type:Function,default:()=>{},required:!0},wellId:{type:String,required:!0}},setup(e){const t=z(),f=w(!1),v=w(""),p=w({interval:null,notes:""}),s=w(null),a=w(!1);E(v,d=>{d!==""&&r()}),E(f,d=>{var u;d===!1&&(v.value="",h(),(u=s==null?void 0:s.value)==null||u.resetFields())});const r=async()=>{t.getIntervalDetails({id:v.value,callback:{onSuccess:d=>{p.value=d}}})},c=async d=>{a.value=!0,t.updateInterval({id:v.value,params:d,callback:{onSuccess:u=>{e!=null&&e.loadPage&&(e==null||e.loadPage()),o()},onFinish:u=>{a.value=!1}}})},i=async d=>{a.value=!0,t.createInterval({params:d,callback:{onSuccess:u=>{e!=null&&e.loadPage&&(e==null||e.loadPage()),o()},onFinish:u=>{a.value=!1}}})},l=()=>{f.value=!0},o=()=>{f.value=!1},m=d=>{v.value=d.toString()},b=w({interval:[{required:!0,message:"Please input interval number",trigger:"blur"}],notes:[{required:!0,message:"Please input notes",trigger:"blur"}]}),k=()=>{s.value&&s.value.validate(d=>{d&&(a.value=!0,v!=null&&v.value?c(p.value):i({...p.value,wellId:e==null?void 0:e.wellId}))})},h=()=>{p.value={interval:null,notes:""}};return{id:v,modal:f,rules:b,loading:a,targetData:p,formRef:s,show:l,hide:o,submit:k,setId:m,reset:h}}}),ie={class:"d-flex align-items-center w-100"},re={class:"modal-title"},de={class:"d-flex flex-column"},ce={class:"d-flex flex-column"},ue={class:"modal-footer d-flex justify-content-center align-items-center"},me=["disabled"],ge=["data-kt-indicator","disabled"],ve={key:0,class:"indicator-label"},pe={key:1,class:"indicator-progress"};function fe(e,t,f,v,p,s){const a=_("SvgIcon"),r=_("el-input-number"),c=_("el-form-item"),i=_("el-input"),l=_("el-form"),o=_("el-dialog");return $(),C(o,{modelValue:e.modal,"onUpdate:modelValue":t[4]||(t[4]=m=>e.modal=m),"show-close":!1,width:"800",id:"interval-modal","align-center":""},{header:I(()=>[n("div",ie,[n("h3",re,V(`${e.id?"Edit Interval":"New Interval"}`),1),n("span",{class:"cursor-pointer ms-auto",onClick:t[0]||(t[0]=(...m)=>e.hide&&e.hide(...m))},[y(a,{icon:"closeModalIcon"})])])]),default:I(()=>[n("div",null,[y(l,{id:"interval_form",onSubmit:R(e.submit,["prevent"]),model:e.targetData,rules:e.rules,ref:"formRef",class:"form"},{default:I(()=>[n("div",de,[y(c,{prop:"interval"},{default:I(()=>[t[5]||(t[5]=n("label",{class:"d-flex align-items-center fs-6 fw-semibold mb-2 text-break"},[n("span",{class:"required"},"Interval number")],-1)),y(r,{type:"number",class:"w-100",controls:!1,min:0,step:1,modelValue:e.targetData.interval,"onUpdate:modelValue":t[1]||(t[1]=m=>e.targetData.interval=m),placeholder:"",name:"interval"},null,8,["modelValue"])]),_:1})]),n("div",ce,[t[6]||(t[6]=n("label",{class:"d-flex align-items-center fs-6 fw-semibold mb-2 text-break"},[n("span",{class:"required"},"Notes")],-1)),y(c,{prop:"notes"},{default:I(()=>[y(i,{modelValue:e.targetData.notes,"onUpdate:modelValue":t[2]||(t[2]=m=>e.targetData.notes=m),type:"textarea",rows:"10",name:"notes",placeholder:"Type Notes"},null,8,["modelValue"])]),_:1})]),n("div",ue,[n("button",{type:"button",class:"btn text-gray-700 btn-sm me-3 btn-blue",onClick:t[3]||(t[3]=(...m)=>e.hide&&e.hide(...m)),disabled:e.loading}," Discard ",8,me),n("button",{"data-kt-indicator":e.loading?"on":null,class:"btn btn-sm btn-primary",type:"submit",disabled:e.loading},[e.loading?q("",!0):($(),S("span",ve," Save ")),e.loading?($(),S("span",pe,t[7]||(t[7]=[U(" Please wait... "),n("span",{class:"spinner-border spinner-border-sm align-middle ms-2"},null,-1)]))):q("",!0)],8,ge)])]),_:1},8,["onSubmit","model","rules"])])]),_:1},8,["modelValue"])}const be=A(le,[["render",fe]]),ye=L({name:"well-interval",props:{id:{type:String,required:!0}},components:{SvgIcon:W,TablePagination:O,IntervalModal:be},setup(e){const t=z(),f=w([]),v=w(0),p=w(0),s=w(1),a=w(!1),r=w(null);B(()=>{c()}),E(s,()=>{c()});const c=async(h={wellId:e==null?void 0:e.id,page:s.value,limit:10})=>{a.value=!0,t.getIntervals({params:h,callback:{onSuccess:d=>{v.value=d==null?void 0:d.totalPage,p.value=d==null?void 0:d.total,s.value=d==null?void 0:d.page,f.value=d==null?void 0:d.items},onFinish:d=>{a.value=!1}}})},i=(h,d)=>{const u=f.value[d];f.value[d]=f.value[h],f.value[h]=u},l=h=>{s.value=h},o=()=>{var h;(h=r==null?void 0:r.value)==null||h.show()},m=h=>{var d,u;(d=r==null?void 0:r.value)==null||d.setId(h),(u=r==null?void 0:r.value)==null||u.show()},b=h=>{j.deletionAlert({onConfirmed:()=>{k(h)}})},k=async h=>{a.value=!0,t.deleteInterval({id:h,callback:{onSuccess:d=>{c()},onFinish:d=>{a.value=!1}}})};return{changePosition:i,toggleNewInterval:o,toggleEditInterval:m,deleteInterval:b,getIntervals:c,pageChange:l,intervalList:f,pageCount:v,currentPage:s,totalElements:p,intervalModal:r,loading:a}}}),_e={class:"card h-100 my-8"},he={class:"card-header py-4 d-flex flex-wrap align-items-center"},we={class:"card-toolbar ms-auto"},$e={class:"svg-icon svg-icon-2"},Ie={class:"card-body"},Pe={key:0,class:"text-center"},Se={key:2,class:"table-responsive"},De={class:"table table-row-bordered align-middle gs-0 gy-3"},ke={class:"text-gray-600 fw-semibold fs-5"},Fe={class:"d-flex align-items-center justify-content-between"},Ve={class:"text-gray-700 fs-5 me-3"},Ce={class:"d-flex align-items-center"},Ne=["onClick"],qe={class:"svg-icon svg-icon-3"},Ee=["onClick"],He={class:"svg-icon svg-icon-3 text-danger"},We=["disabled","onClick"],Le={class:"svg-icon svg-icon-3 text-primary"},Ae=["disabled","onClick"],Ue={class:"svg-icon svg-icon-3 text-primary"},Be={class:"d-flex flex-wrap align-items-center mt-5"},je={key:0,class:"text-gray-700 fw-semibold fs-6 me-auto"};function xe(e,t,f,v,p,s){const a=_("inline-svg"),r=_("el-empty"),c=_("SvgIcon"),i=_("TablePagination"),l=_("IntervalModal");return $(),S(H,null,[n("div",_e,[n("div",he,[t[2]||(t[2]=n("h1",{class:"mb-0 me-4 text-gray-900"},"Interval",-1)),n("div",we,[n("div",{class:"btn btn-sm btn-flex btn-primary",onClick:t[0]||(t[0]=(...o)=>e.toggleNewInterval&&e.toggleNewInterval(...o))},[n("span",$e,[y(a,{src:"/media/icons/arrows/cross-arrow-075.svg"})]),t[1]||(t[1]=U(" New Interval "))])])]),n("div",Ie,[e.loading?($(),S("div",Pe,t[3]||(t[3]=[n("div",{class:"spinner-border text-primary",role:"status"},[n("span",{class:"sr-only"},"Loading...")],-1)]))):e.intervalList.length===0?($(),C(r,{key:1,description:"No Data"})):($(),S("div",Se,[n("table",De,[t[4]||(t[4]=n("thead",null,[n("tr",{class:"fw-semibold text-gray-800"},[n("th",{class:"min-w-60px fs-4"},"Interval"),n("th",{class:"min-w-150px fs-4"},"Note")])],-1)),n("tbody",null,[($(!0),S(H,null,x(e.intervalList,(o,m)=>($(),S("tr",{key:o.id},[n("td",ke,V(o==null?void 0:o.interval),1),n("td",Fe,[n("div",Ve,V(o==null?void 0:o.notes),1),n("div",Ce,[n("button",{type:"button",class:"btn btn-icon btn-sm btn-blue me-3",onClick:b=>{var k;return e.toggleEditInterval(((k=o==null?void 0:o.id)==null?void 0:k.toString())||"")}},[n("span",qe,[y(c,{icon:"newReportIcon"})])],8,Ne),n("button",{type:"button",class:"btn btn-icon btn-sm btn-blue me-3",onClick:b=>{var k;return e.deleteInterval(((k=o==null?void 0:o.id)==null?void 0:k.toString())||"")}},[n("span",He,[y(c,{icon:"trashIcon"})])],8,Ee),n("button",{type:"button",class:"btn btn-icon btn-sm btn-blue arrow-down me-3",disabled:m==e.intervalList.length-1,onClick:b=>e.changePosition(m,m+1)},[n("span",Le,[y(c,{icon:"arrowDown"})])],8,We),n("button",{type:"button",class:"btn btn-icon btn-sm btn-blue arrow-up",disabled:m==0,onClick:b=>e.changePosition(m,m-1)},[n("span",Ue,[y(c,{icon:"arrowUp"})])],8,Ae)])])]))),128))])])])),n("div",Be,[e.intervalList.length?($(),S("div",je,V(`Showing ${(e.currentPage-1)*10+1} to ${e.intervalList.length} of ${e.totalElements} entries`),1)):q("",!0),e.pageCount>=1?($(),C(i,{key:1,"total-pages":e.pageCount,total:e.totalElements,"per-page":10,"current-page":e.currentPage,onPageChange:e.pageChange},null,8,["total-pages","total","current-page","onPageChange"])):q("",!0)])])]),y(l,{ref:"intervalModal",loadPage:e.getIntervals,wellId:e.id},null,8,["loadPage","wellId"])],64)}const J=A(ye,[["render",xe],["__scopeId","data-v-a11794da"]]),K=M("plan",()=>({getPlans:async({params:s,callback:a})=>{var i;const r=g.get(a,"onSuccess",g.noop),c=g.get(a,"onFinish",g.noop);try{P.setHeader();const l=await P.getWithParams("plans",s);r(((i=l.data)==null?void 0:i.data)||l.data)}catch(l){F(l,a)}finally{c()}},getPlanDetails:async({id:s,callback:a})=>{var i;const r=g.get(a,"onSuccess",g.noop),c=g.get(a,"onFinish",g.noop);try{P.setHeader();const l=await P.get(`plans/${s}`);r(((i=l.data)==null?void 0:i.data)||l.data)}catch(l){F(l,a)}finally{c()}},updatePlan:async({id:s,params:a,callback:r})=>{var l;const c=g.get(r,"onSuccess",g.noop),i=g.get(r,"onFinish",g.noop);try{P.setHeader();const o=await P.put(`plans/${s}`,a);c(((l=o.data)==null?void 0:l.data)||o.data)}catch(o){F(o,r)}finally{i()}},deletePlan:async({id:s,callback:a})=>{var i;const r=g.get(a,"onSuccess",g.noop),c=g.get(a,"onFinish",g.noop);try{P.setHeader();const l=await P.delete(`plans/${s}`);r(((i=l.data)==null?void 0:i.data)||l.data)}catch(l){F(l,a)}finally{c()}},createPlan:async({params:s,callback:a})=>{var i;const r=g.get(a,"onSuccess",g.noop),c=g.get(a,"onFinish",g.noop);try{P.setHeader();const l=await P.post("plans",s);r(((i=l.data)==null?void 0:i.data)||l.data)}catch(l){F(l,a)}finally{c()}}})),Me=L({name:"plan-modal",components:{SvgIcon:W},props:{loadPage:{type:Function,default:()=>{},required:!0},wellId:{type:String,required:!0}},setup(e){const t=K(),f=w(!1),v=w({mudDepth:null,day:null,cost:null}),p=w(null),s=w(!1),a=w("");E(a,d=>{d!==""&&r()}),E(f,d=>{var u;d===!1&&(a.value="",h(),(u=p==null?void 0:p.value)==null||u.resetFields())});const r=async()=>{t.getPlanDetails({id:a.value,callback:{onSuccess:d=>{v.value=d}}})},c=async d=>{s.value=!0,t.updatePlan({id:a.value,params:d,callback:{onSuccess:u=>{e!=null&&e.loadPage&&(e==null||e.loadPage()),o()},onFinish:u=>{s.value=!1}}})},i=async d=>{s.value=!0,t.createPlan({params:d,callback:{onSuccess:u=>{e!=null&&e.loadPage&&(e==null||e.loadPage()),o()},onFinish:u=>{s.value=!1}}})},l=()=>{f.value=!0},o=()=>{f.value=!1},m=d=>{a.value=d.toString()},b=w({mudDepth:[{required:!0,message:"Please type mud depth",trigger:"blur"}],day:[{required:!0,message:"Please type day",trigger:"blur"}],cost:[{required:!0,message:"Please type cost",trigger:"blur"}]}),k=()=>{p.value&&p.value.validate(d=>{if(d){s.value=!0;const u={mudDepth:Number(v.value.mudDepth),day:Number(v.value.day),cost:Number(v.value.cost)};a!=null&&a.value?c(u):i({...u,wellId:e==null?void 0:e.wellId})}})},h=()=>{v.value={mudDepth:null,day:null,cost:null}};return{id:a,modal:f,rules:b,loading:s,targetData:v,formRef:p,show:l,hide:o,submit:k,setId:m,reset:h}}}),Re={class:"d-flex align-items-center w-100"},Ge={class:"modal-title"},Oe={class:"d-flex flex-column"},ze={class:"d-flex align-items-center fs-6 fw-semibold mb-2 text-break"},Je={class:"d-flex flex-column"},Ke={class:"d-flex align-items-center fs-6 fw-semibold mb-2 text-break"},Qe={class:"d-flex flex-column"},Xe={class:"d-flex align-items-center fs-6 fw-semibold mb-2 text-break"},Ye={class:"modal-footer d-flex justify-content-center align-items-center"},Ze=["disabled"],Te=["data-kt-indicator","disabled"],et={key:0,class:"indicator-label"},tt={key:1,class:"indicator-progress"};function nt(e,t,f,v,p,s){const a=_("SvgIcon"),r=_("el-popover"),c=_("el-input"),i=_("el-form-item"),l=_("el-input-number"),o=_("el-form"),m=_("el-dialog");return $(),C(m,{modelValue:e.modal,"onUpdate:modelValue":t[5]||(t[5]=b=>e.modal=b),"show-close":!1,width:"500","align-center":""},{header:I(()=>[n("div",Re,[n("h3",Ge,V(`${e.id?"Edit Plan":"New Plan"}`),1),n("span",{class:"cursor-pointer ms-auto",onClick:t[0]||(t[0]=(...b)=>e.hide&&e.hide(...b))},[y(a,{icon:"closeModalIcon"})])])]),default:I(()=>[n("div",null,[y(o,{id:"plan_form",onSubmit:R(e.submit,["prevent"]),model:e.targetData,rules:e.rules,ref:"formRef",class:"form"},{default:I(()=>[n("div",Oe,[n("label",ze,[t[8]||(t[8]=n("span",{class:"required"},"Mud Depth (ft)",-1)),y(r,{placement:"right",width:200,trigger:"hover"},{reference:I(()=>t[6]||(t[6]=[n("i",{class:"fas fa-exclamation-circle ms-2 fs-7"},null,-1)])),default:I(()=>[t[7]||(t[7]=n("span",null," ... ",-1))]),_:1})]),y(i,{prop:"mudDepth",class:"mt-auto"},{default:I(()=>[y(c,{type:"number",class:"w-100",controls:!1,step:"any",modelValue:e.targetData.mudDepth,"onUpdate:modelValue":t[1]||(t[1]=b=>e.targetData.mudDepth=b),placeholder:"",name:"mudDepth"},null,8,["modelValue"])]),_:1})]),n("div",Je,[n("label",Ke,[t[11]||(t[11]=n("span",{class:"required"},"Day",-1)),y(r,{placement:"right",width:200,trigger:"hover"},{reference:I(()=>t[9]||(t[9]=[n("i",{class:"fas fa-exclamation-circle ms-2 fs-7"},null,-1)])),default:I(()=>[t[10]||(t[10]=n("span",null," ... ",-1))]),_:1})]),y(i,{prop:"day",class:"mt-auto"},{default:I(()=>[y(l,{type:"number",class:"w-100",controls:!1,min:0,step:1,"step-strictly":"",modelValue:e.targetData.day,"onUpdate:modelValue":t[2]||(t[2]=b=>e.targetData.day=b),placeholder:"",name:"day"},null,8,["modelValue"])]),_:1})]),n("div",Qe,[n("label",Xe,[t[14]||(t[14]=n("span",{class:"required"},"Cost",-1)),y(r,{placement:"right",width:200,trigger:"hover"},{reference:I(()=>t[12]||(t[12]=[n("i",{class:"fas fa-exclamation-circle ms-2 fs-7"},null,-1)])),default:I(()=>[t[13]||(t[13]=n("span",null," ... ",-1))]),_:1})]),y(i,{prop:"cost",class:"mt-auto"},{default:I(()=>[y(c,{type:"number",class:"w-100",controls:!1,step:"any",modelValue:e.targetData.cost,"onUpdate:modelValue":t[3]||(t[3]=b=>e.targetData.cost=b),placeholder:"",name:"cost"},null,8,["modelValue"])]),_:1})]),n("div",Ye,[n("button",{type:"button",id:"kt_modal_add_customer_cancel",class:"btn text-gray-700 btn-sm me-3 btn-blue",onClick:t[4]||(t[4]=(...b)=>e.hide&&e.hide(...b)),disabled:e.loading}," Discard ",8,Ze),n("button",{"data-kt-indicator":e.loading?"on":null,class:"btn btn-sm btn-primary",type:"submit",disabled:e.loading},[e.loading?q("",!0):($(),S("span",et," Save ")),e.loading?($(),S("span",tt,t[15]||(t[15]=[U(" Please wait... "),n("span",{class:"spinner-border spinner-border-sm align-middle ms-2"},null,-1)]))):q("",!0)],8,Te)])]),_:1},8,["onSubmit","model","rules"])])]),_:1},8,["modelValue"])}const st=A(Me,[["render",nt]]),at=L({name:"well-plan",props:{id:{type:String,required:!0}},components:{SvgIcon:W,TablePagination:O,PlanModal:st},setup(e){const t=K(),f=w([]),v=w(!1),p=w(null);B(()=>{s()});const s=async(o={wellId:e==null?void 0:e.id,page:1,limit:200})=>{v.value=!0,t.getPlans({params:o,callback:{onSuccess:m=>{f.value=m==null?void 0:m.items},onFinish:m=>{v.value=!1}}})},a=(o,m)=>{const b=f.value[m];f.value[m]=f.value[o],f.value[o]=b},r=()=>{var o;(o=p==null?void 0:p.value)==null||o.show()},c=o=>{var m,b;(m=p==null?void 0:p.value)==null||m.setId(o),(b=p==null?void 0:p.value)==null||b.show()},i=o=>{j.deletionAlert({onConfirmed:()=>{l(o)}})},l=async o=>{v.value=!0,t.deletePlan({id:o,callback:{onSuccess:m=>{s()},onFinish:m=>{v.value=!1}}})};return{numberWithCommas:oe,changePosition:a,toggleNewPlan:r,toggleEditPlan:c,deletePlan:i,getPlans:s,planList:f,planModal:p,loading:v}}}),ot={class:"card h-100 my-8 well-plan"},lt={class:"card-header py-4 d-flex flex-wrap align-items-center"},it={class:"card-toolbar ms-auto"},rt={class:"svg-icon svg-icon-2"},dt={class:"card-body"},ct={class:"row gap-10"},ut={key:0,class:"text-center"},mt={class:"card-body gap-5 d-flex flex-column"},gt={class:"mb-0 text-primary fs-4"},vt={class:"d-flex flex-wrap align-items-center justify-content-between border-bottom-dashed border-bottom-1 border-blue-light pb-3"},pt={class:"fw-700 fs-5 text-success"},ft={class:"d-flex flex-wrap align-items-center justify-content-between"},bt={class:"fw-700 fs-5 text-dark"},yt={class:"d-flex align-items-center gap-2 position-absolute top-0 end-0 transform-top-50"},_t=["onClick"],ht={class:"svg-icon svg-icon-3"},wt=["onClick"],$t={class:"svg-icon svg-icon-3 text-danger"};function It(e,t,f,v,p,s){const a=_("inline-svg"),r=_("el-empty"),c=_("SvgIcon"),i=_("el-tooltip"),l=_("PlanModal");return $(),S(H,null,[n("div",ot,[n("div",lt,[t[2]||(t[2]=n("h1",{class:"mb-0 me-4 text-gray-900"},"Plans",-1)),n("div",it,[n("div",{class:"btn btn-sm btn-flex btn-primary",onClick:t[0]||(t[0]=(...o)=>e.toggleNewPlan&&e.toggleNewPlan(...o))},[n("span",rt,[y(a,{src:"/media/icons/arrows/cross-arrow-075.svg"})]),t[1]||(t[1]=U(" New Plan "))])])]),n("div",dt,[n("div",ct,[e.loading?($(),S("div",ut,t[3]||(t[3]=[n("div",{class:"spinner-border text-primary",role:"status"},[n("span",{class:"sr-only"},"Loading...")],-1)]))):e.planList.length===0?($(),C(r,{key:1,description:"No Data"})):($(!0),S(H,{key:2},x(e.planList,o=>($(),S("div",{key:o.id,class:"col-4 card rounded-3 shadow-sm border-top border-top-2 border-primary position-relative card-item"},[n("div",mt,[n("h5",gt,V(`${o.day} Days`),1),n("div",vt,[t[4]||(t[4]=n("span",{class:"fw-semibold fs-5 text-gray-700"},"Measured Depth",-1)),n("span",pt,V(`${o==null?void 0:o.mudDepth} (ft)`),1)]),n("div",ft,[t[5]||(t[5]=n("span",{class:"fw-semibold fs-5 text-gray-700"},"Cost",-1)),n("span",bt,V(`$${e.numberWithCommas((o==null?void 0:o.cost)||0)}`),1)])]),n("div",yt,[y(i,{content:"Edit Plan",placement:"top",effect:"customize"},{default:I(()=>[n("div",{class:"btn rounded-circle btn-icon btn-action btn-sm bg-light btn-edit",onClick:m=>{var b;return e.toggleEditPlan(((b=o==null?void 0:o.id)==null?void 0:b.toString())||"")}},[n("span",ht,[y(c,{icon:"pencilIcon"})])],8,_t)]),_:2},1024),y(i,{content:"Delete Plan",placement:"top",effect:"customize"},{default:I(()=>[n("button",{class:"btn rounded-circle btn-icon btn-action btn-sm bg-light btn-delete",onClick:m=>e.deletePlan((o==null?void 0:o.id)||"")},[n("span",$t,[y(c,{icon:"trashIcon"})])],8,wt)]),_:2},1024)])]))),128))])])]),y(l,{ref:"planModal",loadPage:e.getPlans,wellId:e.id},null,8,["loadPage","wellId"])],64)}const Q=A(at,[["render",It],["__scopeId","data-v-2a133139"]]),Pt={[N.General]:G,[N.Interval]:J,[N.Plan]:Q},St=L({name:"well-detail",components:{SvgIcon:W,WellGeneralInfo:G,WellInterval:J,WellPlan:Q,PageHeader:X},setup(){var h,d;const e=T(),t=ae(),f=w(),v=w(N.General),p=["Home","Well","Edit"],s=w(null),a=Z(()=>Pt[v.value]),r=(d=(h=e.params)==null?void 0:h.id)==null?void 0:d.toString();B(()=>{r&&c(r)});const c=async u=>{t.getWellDetails({wellId:u,callback:{onSuccess:D=>{f.value=D}}})},i=u=>{const D=u.target;v.value=Number(D.getAttribute("data-tab-index"))},l=()=>{var u,D;if(s!=null&&s.value&&((u=s==null?void 0:s.value)!=null&&u.loading))return(D=s==null?void 0:s.value)==null?void 0:D.loading},o=()=>{var u,D;if(s!=null&&s.value&&((u=s==null?void 0:s.value)!=null&&u.isFormDirty))return(D=s==null?void 0:s.value)==null?void 0:D.isFormDirty()},m=async()=>{var u,D;return s!=null&&s.value&&((u=s==null?void 0:s.value)!=null&&u.isValidForm)?await((D=s==null?void 0:s.value)==null?void 0:D.isValidForm()):null},b=async()=>{var u;s!=null&&s.value&&((u=s==null?void 0:s.value)!=null&&u.submit)&&s.value.submit()},k=async u=>{if(v.value!==N.General||l())i(u);else{if(!o()){i(u);return}const D=await m();if(D===null){i(u);return}D?(b(),i(u)):j.incompleteFormAlert({onConfirmed:()=>{i(u)}})}};return{wellId:r,EWellDetailsTab:N,tabIndex:v,wellInfo:f,breadcrumbs:p,wellDetailsTabs:Y,currentChildTab:s,currentComponent:a,isSystemAdmin:ee.checkRole(te.SystemAdmin),handleActiveTab:k}}}),Dt={class:"d-flex align-items-between flex-column"},kt={class:"d-flex align-items-center gap-2 gap-lg-3"},Ft={class:"nav nav-stretch nav-line-tabs border-0",role:"tablist",id:"kt_layout_builder_tabs",ref:"kt_layout_builder_tabs"},Vt=["data-tab-index"];function Ct(e,t,f,v,p,s){const a=_("PageHeader"),r=_("KTContent");return $(),C(r,null,{header:I(()=>{var c;return[y(a,{title:(c=e.wellInfo)==null?void 0:c.nameOrNo,breadcrumbs:e.breadcrumbs},null,8,["title","breadcrumbs"]),n("div",Dt,[n("div",kt,[n("ul",Ft,[($(!0),S(H,null,x(e.wellDetailsTabs,i=>($(),S("li",{class:"nav-item",key:i.value},[n("a",{class:se(["nav-link cursor-pointer text-active-primary fw-semibold fs-6",{active:e.tabIndex===i.value}]),onClick:t[0]||(t[0]=l=>e.handleActiveTab(l)),"data-tab-index":i.value,role:"tab"},V(i.label),11,Vt)]))),128))],512)])])]}),body:I(()=>[($(),C(ne(e.currentComponent),{id:e.wellId,ref:"currentChildTab"},null,8,["id"]))]),_:1})}const Jt=A(St,[["render",Ct]]);export{Jt as default};
