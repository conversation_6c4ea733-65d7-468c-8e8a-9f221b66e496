<template>
  <form
    @submit.prevent="submit"
    :model="targetData"
    :rules="rules"
    ref="formRef"
    class="h-auto w-11/12 text-card-text-light"
  >
    <div>
      <div v-if="loading" class="text-center py-7">
        <div class="spinner-border text-primary" role="status">
          <span class="sr-only">Loading...</span>
        </div>
      </div>
      <div v-else>
        <div class="h-auto w-full mx-auto py-4 bg-card-background text-card-text-light">
          <div>
            <h1 class="font-bold text-2xl">General Information</h1>
            <div class="font-semibold">
              Note some information here for short description
            </div>
          </div>
          <div class="h-auto w-full flex flex-row items-center justify-end">
            <!--begin::Button-->
            <button
              class="bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover rounded-md px-4 py-2 font-semibold"
              type="submit"
              :disabled="loading"
            >
              <span v-if="!loading"> Save </span>
              <span v-if="loading" class="indicator-progress">
                Please wait...
                <span
                  class="spinner-border spinner-border-sm align-middle ms-2"
                ></span>
              </span>
            </button>
            <!--end::Button-->
          </div>
        </div>
      </div>
      <!--end::Card header-->

      <!--begin::Card body-->

      <div class="h-auto w-full flex flex-col gap-3">
        <div v-if="isSystemAdmin()">
          <label class="font-semibold">
            Assign Company <span class="text-danger-active font-light">*</span>
          </label>

          <el-form-item prop="companyId">
            <el-select-v2
              v-model="targetData.companyId"
              :options="companyList"
              placeholder="Search Company"
              filterable
              name="companyId"
              :loading="loadingCompany"
              :disabled="isJustEngineer()"
            />
          </el-form-item>
        </div>

        <div>
          <label class="font-semibold"> Assign Engineers </label>
          <el-form-item prop="engineerIds">
            <el-select-v2
              v-model="targetData.engineerIds"
              :options="engineerList"
              placeholder="Search Engineers"
              filterable
              multiple
              clearable
              name="engineerIds"
              :loading="loadingUser"
              :disabled="isJustEngineer()"
            >
              <template #empty>
                <p
                  v-if="isSystemAdmin() && !targetData.companyId"
                  type="danger"
                  class="text-semibold text-danger-active text-lg"
                >
                  Please select a company first!
                </p>
                <p v-else-if="!loadingUser">No data</p>
              </template></el-select-v2
            >
          </el-form-item>
        </div>

        <div>
          <label class="font-semibold"> Assign Supervisors </label>
          <el-form-item prop="supervisorIds">
            <el-select-v2
              v-model="targetData.supervisorIds"
              :options="supervisorList"
              placeholder="Search Supervisor"
              filterable
              multiple
              clearable
              name="supervisorIds"
              :loading="loadingUser"
              :disabled="isJustEngineer()"
            >
              <template #empty>
                <p
                  v-if="isSystemAdmin() && !targetData.companyId"
                  type="danger"
                  class="text-semibold text-danger-active text-lg"
                >
                  Please select a company first!
                </p>
                <p v-else-if="!loadingUser">No data</p>
              </template></el-select-v2
            >
          </el-form-item>
        </div>

        <div>
          <label class="font-semibold"> Assign Customer </label>
          <el-form-item prop="customerIds">
            <el-select-v2
              v-model="targetData.customerIds"
              :options="customerList"
              placeholder="Search Customer"
              filterable
              multiple
              clearable
              name="customerIds"
              :loading="loadingCustomer"
              :disabled="isJustEngineer()"
            >
              <template #empty>
                <p
                  v-if="isSystemAdmin() && !targetData.companyId"
                  type="danger"
                  class="text-semibold text-danger-active text-lg"
                >
                  Please select a company first!
                </p>
                <p v-else-if="!loadingCustomer">No data</p>
              </template></el-select-v2
            >
          </el-form-item>
        </div>

        <div>
          <label class="font-semibold">
            Well Name/ No.
            <span class="text-danger-active font-light">*</span></label
          >
          <el-form-item prop="nameOrNo">
            <el-input
              v-model="targetData.nameOrNo"
              placeholder=""
              name="nameOrNo"
            ></el-input>
          </el-form-item>
        </div>

        <div>
          <label class="font-semibold"> API Well No. </label>
          <el-form-item prop="apiWellNo">
            <el-input
              v-model="targetData.apiWellNo"
              placeholder=""
              name="apiWellNo"
            ></el-input>
          </el-form-item>
        </div>

        <div>
          <label class="font-semibold"> Field / Block </label>

          <el-form-item prop="fieldOrBlock">
            <el-input
              v-model="targetData.fieldOrBlock"
              placeholder=""
              name="fieldOrBlock"
            ></el-input>
          </el-form-item>
        </div>

        <div>
          <label class="font-semibold">Latitude (deg)</label>
          <el-form-item prop="latitude">
            <el-input
              type="number"
              :controls="false"
              step="any"
              v-model="targetData.latitude"
              placeholder=""
              name="latitude"
            ></el-input>
          </el-form-item>
        </div>

        <div>
          <label class="font-semibold"> Longitude (deg) </label>
          <el-form-item prop="longitude">
            <el-input
              type="number"
              :controls="false"
              step="any"
              v-model="targetData.longitude"
              placeholder=""
              name="longitude"
            ></el-input>
          </el-form-item>
        </div>

        <div>
          <label class="font-semibold">Section/ Township/ Range</label>
          <el-form-item prop="sectionOrTownshipOrRange">
            <el-input
              v-model="targetData.sectionOrTownshipOrRange"
              placeholder=""
              name="sectionOrTownshipOrRange"
            ></el-input>
          </el-form-item>
        </div>

        <div>
          <label class="font-semibold"> County/ Parish/ Offshore Area </label>
          <el-form-item prop="countyOrParishOrOffshoreArea">
            <el-input
              v-model="targetData.countyOrParishOrOffshoreArea"
              placeholder=""
              name="countyOrParishOrOffshoreArea"
            ></el-input>
          </el-form-item>
        </div>

        <div>
          <label class="font-semibold"> Country </label>
          <el-form-item prop="countryId">
            <el-select-v2
              v-model="targetData.countryId"
              :options="countryList"
              placeholder="Search Country"
              filterable
              clearable
              remote
              :remote-method="getCountries"
              :loading="loadingCountry"
              name="countryId"
              :props="{
                label: 'name',
                value: 'id',
              }"
            />
          </el-form-item>
        </div>

        <div>
          <label class="font-semibold">State/Province</label>
          <el-form-item prop="stateOrProvinceId">
            <el-select-v2
              v-model="targetData.stateOrProvinceId"
              :options="stateList"
              placeholder="Search"
              filterable
              clearable
              remote
              :remote-method="getStates"
              :loading="loadingState"
              name="stateOrProvinceId"
              :props="{
                label: 'name',
                value: 'id',
              }"
            />
          </el-form-item>
        </div>

        <div>
          <label class="font-semibold"
            ><span>Stock Point</span>
            <el-popover placement="top" :width="300" trigger="hover">
              <template #reference>
                <i class="fas fa-exclamation-circle ml-2"></i>
              </template>
              <span>
                The "Stock Point" is a designated location or storage area where
                drilling equipment, tools, spare parts, and other materials
                required for drilling operations are stored, managed, and
                maintained. It serves as a central hub for the storage and
                distribution of drilling-related inventory. The stock point is
                strategically located to provide easy access to the drilling rig
                or wellsite, allowing for efficient equipment replenishment and
                maintenance.<br /><br />

                The stock point is critical for ensuring that the drilling rig
                has a constant supply of essential equipment and materials,
                reducing downtime, and supporting continuous drilling
                activities. It is typically managed by logistics and supply
                chain personnel who coordinate the movement of items to and from
                the drilling site.
              </span>
            </el-popover>
          </label>
          <el-form-item prop="stockPoint">
            <el-input
              v-model="targetData.stockPoint"
              placeholder=""
              name="stockPoint"
            ></el-input>
          </el-form-item>
        </div>

        <div>
          <label class="font-semibold">
            <span>Stock Point Contact</span>
            <el-tooltip
              content="Contact information for the stock point"
              placement="top"
              effect="light"
            >
              <i class="fas fa-exclamation-circle ml-2"></i>
            </el-tooltip>
          </label>
          <el-form-item prop="stockPointContact">
            <el-input
              v-model="targetData.stockPointContact"
              placeholder=""
              name="stockPointContact"
            ></el-input>
          </el-form-item>
        </div>

        <div>
          <label class="font-semibold"
            ><span>Operator</span>
            <el-popover placement="top" :width="300" trigger="hover">
              <template #reference>
                <i class="fas fa-exclamation-circle ml-2"></i>
              </template>
              <span>
                The "Operator" is typically the company that owns or holds the
                rights to operate the oil or gas well or drilling project. They
                are the entity responsible for managing and overseeing all
                aspects of the well or field operations.
              </span>
            </el-popover></label
          >
          <el-form-item prop="operator">
            <el-input
              v-model="targetData.operator"
              placeholder=""
              name="operator"
            ></el-input>
          </el-form-item>
        </div>

        <div>
          <label class="font-semibold">
            <span>Contractor</span>
            <el-popover placement="top" :width="300" trigger="hover">
              <template #reference>
                <i class="fas fa-exclamation-circle ml-2"></i>
              </template>
              <span>
                The "Contractor" refers to companies or entities hired by the
                Operator to provide specific services, equipment, or expertise
                during drilling and well operations. Contractors are often
                specialized service providers.
              </span>
            </el-popover>
          </label>
          <el-form-item prop="contractor">
            <el-input
              v-model="targetData.contractor"
              placeholder=""
              name="contractor"
            ></el-input>
          </el-form-item>
        </div>

        <div>
          <label class="font-semibold">Rig Name</label>
          <el-form-item prop="rigName">
            <el-input
              v-model="targetData.rigName"
              placeholder=""
              name="rigName"
            ></el-input>
          </el-form-item>
        </div>

        <div>
          <label class="font-semibold"> Spud Date </label>
          <el-form-item prop="spudDate">
            <el-date-picker
              type="date"
              v-model="targetData.spudDate"
              placeholder="MM/DD/YYYY"
              format="MM/DD/YYYY"
              value-format="YYYY-MM-DD"
              name="spudDate"
            />
          </el-form-item>
        </div>

        <div>
          <label class="font-semibold"
            >KOP - Kick-Off Point (ft)
            <el-popover placement="top" :width="300" trigger="hover">
              <template #reference>
                <i class="fas fa-exclamation-circle ml-2"></i>
              </template>
              <span>
                The "Kick-Off Point" is the depth in the well where drilling
                operations transition from vertical drilling to directional or
                horizontal drilling. At the KOP, the wellbore is intentionally
                deviated from the vertical position and begins to follow a
                specific directional path. This is typically done to access
                specific geological formations or reservoirs. The KOP depth is
                measured in feet and represents the point where the drilling
                trajectory starts to deviate from vertical.
              </span>
            </el-popover></label
          >
          <el-form-item prop="kickOffPoint">
            <el-input
              type="number"
              :controls="false"
              step="any"
              v-model="targetData.kickOffPoint"
              placeholder=""
              name="kickOffPoint"
            ></el-input>
          </el-form-item>
        </div>

        <div>
          <label class="font-semibold"
            >LP - Landing Point (ft)
            <el-popover placement="top" :width="300" trigger="hover">
              <template #reference>
                <i class="fas fa-exclamation-circle ml-2"></i>
              </template>
              <span>
                LP stands for "Landing Point." The LP depth is the depth in the
                well where the directional or horizontal drilling section ends,
                and the wellbore returns to a vertical or near-vertical
                orientation. The LP is often located at the depth where the
                target reservoir or production zone is reached. It is the point
                at which the drilling assembly or bottom-hole assembly (BHA)
                "lands" in the desired location. Like the KOP, the LP depth is
                also measured in feet.
              </span>
            </el-popover></label
          >
          <el-form-item prop="landingPoint">
            <el-input
              type="number"
              :controls="false"
              step="any"
              v-model="targetData.landingPoint"
              placeholder=""
              name="landingPoint"
            ></el-input>
          </el-form-item>
        </div>

        <div>
          <label class="font-semibold"
            >Sea Level - S/L
            <el-popover placement="top" :width="300" trigger="hover">
              <template #reference>
                <i class="fas fa-exclamation-circle ml-2"></i>
              </template>
              <span>
                For Offshore only - "S/L" stands for "Sea Level." It represents
                the reference point at or above the surface of the sea or ocean.
                Sea level is an important reference for measuring the depth of
                water, positioning offshore platforms, and assessing potential
                hazards related to tides and storm surges.
              </span>
            </el-popover></label
          >
          <el-form-item prop="seaLevel">
            <el-input
              type="number"
              :controls="false"
              step="any"
              v-model="targetData.seaLevel"
              placeholder=""
              name="seaLevel"
            ></el-input>
          </el-form-item>
        </div>

        <div>
          <label class="font-semibold"
            >Air Gap (ft)
            <el-popover placement="top" :width="300" trigger="hover">
              <template #reference>
                <i class="fas fa-exclamation-circle ml-2"></i>
              </template>
              <span>
                For Offshore only - The "Air Gap" is the vertical distance
                between the still water level (SWL) and the lowest point on an
                offshore platform's structure, such as its deck or equipment. It
                is expressed in feet and is a critical safety parameter that
                helps determine the platform's vulnerability to wave action and
                storm surges. A larger air gap provides greater protection
                against waves and tidal fluctuations.
              </span>
            </el-popover></label
          >
          <el-form-item prop="airGap">
            <el-input
              type="number"
              :controls="false"
              step="any"
              v-model="targetData.airGap"
              placeholder=""
              name="airGap"
            ></el-input>
          </el-form-item>
        </div>

        <div>
          <label class="font-semibold"
            >Water Depth (ft)
            <el-popover placement="top" :width="300" trigger="hover">
              <template #reference>
                <i class="fas fa-exclamation-circle ml-2"></i>
              </template>
              <span>
                For Offshore only - "Water Depth" refers to the depth of the
                water at a specific location, typically measured in feet. It is
                a fundamental parameter used to describe the location of
                offshore drilling rigs, platforms, and subsea wellheads. Water
                depth is a key factor in well planning, equipment selection, and
                riser design.
              </span>
            </el-popover></label
          >
          <el-form-item prop="waterDepth">
            <el-input
              type="number"
              :controls="false"
              step="any"
              v-model="targetData.waterDepth"
              placeholder=""
              name="waterDepth"
            ></el-input>
          </el-form-item>
        </div>

        <div>
          <label class="font-semibold"
            >Riser ID (in)
            <el-popover placement="top" :width="300" trigger="hover">
              <template #reference>
                <i class="fas fa-exclamation-circle ml-2"></i>
              </template>
              <span>
                For Offshore only - "Riser ID" stands for "Riser Inner Diameter"
                and is measured in inches. It refers to the inside diameter of
                the riser, which is a vertical conduit used to transport
                drilling mud, production fluids, and other materials between the
                seafloor and the drilling platform or vessel. The riser ID is a
                critical parameter for riser design and fluid circulation.
              </span>
            </el-popover></label
          >
          <el-form-item prop="riserId">
            <el-input
              type="number"
              :controls="false"
              step="any"
              v-model="targetData.riserId"
              placeholder=""
              name="riserId"
            ></el-input>
          </el-form-item>
        </div>

        <div>
          <label class="font-semibold"
            >Riser OD (in)
            <el-popover placement="top" :width="300" trigger="hover">
              <template #reference>
                <i class="fas fa-exclamation-circle ml-2"></i>
              </template>
              <span>
                For Offshore only - "Riser OD" stands for "Riser Outer Diameter"
                and is measured in inches. It represents the outside diameter of
                the riser. The riser OD is important for structural integrity
                and compatibility with other riser and wellhead components.
              </span>
            </el-popover></label
          >
          <el-form-item prop="riserOD">
            <el-input
              type="number"
              :controls="false"
              step="any"
              v-model="targetData.riserOD"
              placeholder=""
              name="riserOD"
            ></el-input>
          </el-form-item>
        </div>
        <div>
          <label class="font-semibold"
            >Choke line ID (in)
            <el-popover placement="top" :width="300" trigger="hover">
              <template #reference>
                <i class="fas fa-exclamation-circle ml-2"></i>
              </template>
              <span>
                For Offshore only - The "Choke Line Inner Diameter" is the
                inside diameter of the choke line, which is a conduit used for
                well control operations, specifically controlling the flow of
                fluids from the well during drilling or in emergency situations.
                It is measured in inches.
              </span>
            </el-popover></label
          >
          <el-form-item prop="chokeLineId">
            <el-input
              type="number"
              :controls="false"
              step="any"
              v-model="targetData.chokeLineId"
              placeholder=""
              name="chokeLineId"
            ></el-input>
          </el-form-item>
        </div>

        <div>
          <label class="font-semibold"
            >Kill line ID (in)
            <el-popover placement="top" :width="300" trigger="hover">
              <template #reference>
                <i class="fas fa-exclamation-circle ml-2"></i>
              </template>
              <span>
                For Offshore only - The "Kill Line Inner Diameter" is the inside
                diameter of the kill line, which is another conduit used for
                well control purposes, especially to introduce heavy fluids or
                drilling mud into the well to control pressure. It is also
                measured in inches.
              </span>
            </el-popover></label
          >
          <el-form-item prop="killLineId">
            <el-input
              type="number"
              :controls="false"
              step="any"
              v-model="targetData.killLineId"
              placeholder=""
              name="killLineId"
            ></el-input>
          </el-form-item>
        </div>

        <div>
          <label class="font-semibold"
            >Boost line ID (in)
            <el-popover placement="top" :width="300" trigger="hover">
              <template #reference>
                <i class="fas fa-exclamation-circle ml-2"></i>
              </template>
              <span>
                For Offshore only - The "Boost Line Inner Diameter" is the
                inside diameter of the boost line, which is used to pump fluids
                or chemicals into the well for various purposes, including well
                control and stimulation. It is measured in inches.
              </span>
            </el-popover></label
          >
          <el-form-item prop="boostLineId">
            <el-input
              type="number"
              :controls="false"
              step="any"
              v-model="targetData.boostLineId"
              placeholder=""
              name="boostLineId"
            ></el-input>
          </el-form-item>
        </div>

        <div class="h-auto w-full flex flex-row items-center justify-between">
          <div>
            <label class="font-semibold"
              >Rate of Penetration - ROP (ft/hr)</label
            >
            <el-popover placement="top" :width="300" trigger="hover">
              <template #reference>
                <i class="fas fa-exclamation-circle ml-2"></i>
              </template>
              <span>
                For use in hydraulics calculation - ROP, which stands for "Rate
                of Penetration," represents the speed at which a drilling bit
                advances or penetrates the rock formation during the drilling
                process. It is typically expressed in units of depth per unit of
                time, such as feet per hour (ft/hr) or meters per hour (m/hr).
              </span>
            </el-popover>
          </div>
          <el-form-item prop="rateOfPenetration">
            <input
              class="h-4 w-4"
              type="checkbox"
              placeholder=""
              name="rateOfPenetration"
              v-model="targetData.rateOfPenetration"
          /></el-form-item>
        </div>

        <div class="h-auto w-full flex flex-row items-center justify-between">
          <div>
            <span class="font-semibold">Revolutions per Minute (RPM)</span>
            <el-popover placement="top" :width="300" trigger="hover">
              <template #reference>
                <i class="fas fa-exclamation-circle ml-2"></i>
              </template>
              <span>
                For use in hydraulics calculation - RPM, which stands for
                "Revolutions Per Minute," refers to the speed at which the
                drilling bit or drill string rotates while drilling. It is
                measured in rotations or revolutions completed in one minute.
              </span>
            </el-popover>
          </div>
          <el-form-item prop="revolutionsPerMinute">
            <input
              class="h-4 w-4"
              type="checkbox"
              placeholder=""
              name="revolutionsPerMinute"
              v-model="targetData.revolutionsPerMinute"
          /></el-form-item>
        </div>

        <div class="h-0- w-full flex flex-row items-center justify-between">
          <div>
            <span class="font-semibold">Eccentricity</span>
            <el-popover placement="top" :width="300" trigger="hover">
              <template #reference>
                <i class="fas fa-exclamation-circle ml-2"></i>
              </template>
              <span>
                For use in hydraulics calculation - Eccentricity, in the context
                of drilling, refers to the deviation of the wellbore or drilling
                assembly from the vertical axis. It is the measurement of how
                off-center or non-concentric the drilling assembly is with
                respect to the desired drilling path.
              </span>
            </el-popover>
          </div>

          <el-form-item prop="eccentricity">
            <input
              class="h-4 w-4"
              type="checkbox"
              placeholder=""
              name="eccentricity"
              v-model="targetData.eccentricity"
          /></el-form-item>
        </div>
      </div>
    </div>
  </form>
</template>

<script lang="ts">
import SvgIcon from "@/constants/SvgIcon.vue";
import { UserType } from "@/constants/user";
import { formatDate } from "@/utils/date";
import { setupNavigationGuard } from "@/utils/navigation-guard";
import AlertService from "@/services/AlertService";
import JwtService, {
  isJustEngineer,
  isSystemAdmin,
} from "@/services/JwtService";
import { useCompanyStore } from "@/stores/company";
import { useCountryStore } from "@/stores/country";
import { useCustomerStore } from "@/stores/customer";
import { useUserStore } from "@/stores/user";
import { useWellStore } from "@/stores/well";
import _ from "lodash";
import { defineComponent, onMounted, ref, watch } from "vue";
import { onBeforeRouteLeave, useRouter } from "vue-router";
import type { Option } from "@/types/common";

interface FormData extends Well.GeneralInfo {
  companyId: string | null;
  engineerIds: string[];
  supervisorIds: string[];
}

export default defineComponent({
  name: "well-general",
  components: { SvgIcon },
  props: {
    id: {
      type: String,
      required: false,
    },
  },
  setup(props) {
    const router = useRouter();
    const companyStore = useCompanyStore();
    const wellStore = useWellStore();
    const countryStore = useCountryStore();
    const userStore = useUserStore();
    const customerStore = useCustomerStore();
    const initialForm = ref<FormData>({
      companyId: isSystemAdmin() ? null : JwtService.getUserInfo()?.companyId,
      engineerIds: [],
      customerIds: [],
      supervisorIds:
        JwtService.checkRole(UserType.Supervisor) &&
        JwtService.getUserInfo()?.id
          ? [JwtService.getUserInfo()?.id]
          : [],
      stateOrProvinceId: "",
      countryId: "",
      nameOrNo: "",
      apiWellNo: "",
      latitude: null,
      longitude: null,
      fieldOrBlock: "",
      sectionOrTownshipOrRange: "",
      countyOrParishOrOffshoreArea: "",
      rigName: "",
      spudDate: "",
      stockPoint: "",
      stockPointContact: "",
      operator: "",
      contractor: "",
      kickOffPoint: null,
      landingPoint: null,
      seaLevel: null,
      airGap: null,
      waterDepth: null,
      riserId: null,
      riserOD: null,
      chokeLineId: null,
      killLineId: null,
      boostLineId: null,
      rateOfPenetration: false,
      revolutionsPerMinute: false,
      eccentricity: false,
    });
    const formRef = ref<null | HTMLFormElement>(null);
    const loading = ref<boolean>(false);
    const loadingCountry = ref<boolean>(false);
    const loadingState = ref<boolean>(false);
    const loadingCustomer = ref<boolean>(false);
    const loadingUser = ref<boolean>(false);
    const loadingCompany = ref<boolean>(false);

    const targetData = ref<FormData>(
      JSON.parse(JSON.stringify(initialForm.value))
    );
    const countryList = ref([]);
    const stateList = ref([]);
    const engineerList = ref<Option[]>([]);
    const supervisorList = ref<Option[]>([]);
    const customerList = ref<Option[]>([]);
    const companyList = ref<Option[]>([]);
    const firstRenderAfterFetchData = ref(false);

    onMounted(() => {
      getCountries();

      if (isSystemAdmin()) {
        getCompanies();
      }

      if (props?.id) {
        getWellDetails(props.id || "");
      } else {
        if (!isSystemAdmin()) {
          getUsers(JwtService.getUserInfo()?.companyId);
          getCustomers(JwtService.getUserInfo()?.companyId);
        }
      }
    });

    const isFormDirty = () => {
      return !_.isEqual(targetData.value, initialForm.value);
    };

    onBeforeRouteLeave((to, from, next) => {
      setupNavigationGuard(to, from, next, isFormDirty());
    });

    watch(
      () => props.id,
      (newValue) => {
        if (newValue !== "") {
          getWellDetails(newValue || "");
        } else {
          if (!isSystemAdmin()) {
            getUsers(JwtService.getUserInfo()?.companyId);
            getCustomers(JwtService.getUserInfo()?.companyId);
          }
        }
      }
    );

    watch(
      () => targetData.value.countryId,
      (newValue) => {
        stateList.value = [];
        if (!firstRenderAfterFetchData.value) {
          targetData.value = { ...targetData.value, stateOrProvinceId: "" };
        }

        firstRenderAfterFetchData.value = false;
        if (newValue) {
          getStates();
        }
      }
    );

    watch(
      () => targetData.value.companyId,
      (newValue, oldValue) => {
        if (isSystemAdmin()) {
          if (oldValue) {
            targetData.value = {
              ...targetData.value,
              engineerIds: [],
              customerIds: [],
              supervisorIds: [],
            };
          }

          if (newValue) {
            getUsers(newValue);
            getCustomers(newValue);
          }
        }
      }
    );

    const rules = isSystemAdmin()
      ? {
          companyId: [
            {
              required: true,
              message: "Please select Company",
              trigger: ["change", "blur"],
            },
          ],
          nameOrNo: [
            {
              required: true,
              message: "Please type Well Name/ No.",
              trigger: ["change", "blur"],
            },
          ],
        }
      : {
          nameOrNo: [
            {
              required: true,
              message: "Please type Well Name/ No.",
              trigger: ["change", "blur"],
            },
          ],
        };

    const submit = () => {
      if (!formRef.value) {
        return;
      }

      formRef.value.validate((valid: boolean) => {
        if (valid) {
          if (loading.value) return;
          if (!isFormDirty()) return;

          const userIds: string[] = [
            ...new Set([
              ...targetData?.value?.supervisorIds,
              ...targetData?.value?.engineerIds,
            ]),
          ];

          const param = {
            ...targetData.value,
            userIds,
            apiWellNo: targetData.value.apiWellNo || null,
            fieldOrBlock: targetData.value.fieldOrBlock || null,
            latitude: Number(targetData.value.latitude) || null,
            longitude: Number(targetData.value.longitude) || null,
            sectionOrTownshipOrRange:
              targetData.value.sectionOrTownshipOrRange || null,
            countyOrParishOrOffshoreArea:
              targetData.value.countyOrParishOrOffshoreArea || null,
            countryId: targetData.value.countryId || null,
            stateOrProvinceId: targetData.value.stateOrProvinceId || null,
            stockPoint: targetData.value.stockPoint || null,
            stockPointContact: targetData.value.stockPointContact || null,
            operator: targetData.value.operator || null,
            contractor: targetData.value.contractor || null,
            rigName: targetData.value.rigName || null,
            spudDate: formatDate(targetData.value.spudDate) || null,
            kickOffPoint: Number(targetData.value.kickOffPoint) || null,
            landingPoint: Number(targetData.value.landingPoint) || null,
            seaLevel: Number(targetData.value.seaLevel) || null,
            airGap: Number(targetData.value.airGap) || null,
            waterDepth: Number(targetData.value.waterDepth) || null,
            riserId: Number(targetData.value.riserId) || null,
            riserOD: Number(targetData.value.riserOD) || null,
            chokeLineId: Number(targetData.value.chokeLineId) || null,
            killLineId: Number(targetData.value.killLineId) || null,
            boostLineId: Number(targetData.value.boostLineId) || null,
          };

          if (props?.id) {
            updateWell(param);
          } else {
            createWell(param);
          }
        }
      });
    };

    const reset = () => {
      targetData.value = JSON.parse(JSON.stringify(initialForm.value));
    };

    const getWellDetails = async (id: string): Promise<void> => {
      loading.value = true;
      wellStore.getWellDetails({
        wellId: id,
        callback: {
          onSuccess: (res: any) => {
            getUsers(res?.company?.id);
            getCustomers(res?.company?.id);
            firstRenderAfterFetchData.value = true;

            const engineerIds: string[] = [];
            const supervisorIds: string[] = [];
            const customerIds: string[] = res?.customers?.map(
              (item: Customer.Info) => item?.id
            );
            res?.users?.forEach((item: User.Info) => {
              if (
                item?.roles?.some((role) => role.value === UserType.Supervisor)
              ) {
                supervisorIds.push(item.id ?? "");
              }
              if (
                item?.roles?.some((role) => role.value === UserType.Engineer)
              ) {
                engineerIds.push(item.id ?? "");
              }
            });

            const data = {
              engineerIds,
              supervisorIds,
              customerIds,
              companyId: res?.company?.id,
              countryId: res?.countryId,
              stateOrProvinceId: res?.stateOrProvinceId,
              nameOrNo: res?.nameOrNo,
              apiWellNo: res?.apiWellNo,
              latitude: res?.latitude,
              longitude: res?.longitude,
              fieldOrBlock: res?.fieldOrBlock,
              sectionOrTownshipOrRange: res?.sectionOrTownshipOrRange,
              countyOrParishOrOffshoreArea: res?.countyOrParishOrOffshoreArea,
              rigName: res?.rigName,
              spudDate: res?.spudDate,
              stockPoint: res?.stockPoint,
              stockPointContact: res?.stockPointContact,
              operator: res?.operator,
              contractor: res?.contractor,
              kickOffPoint: res?.kickOffPoint,
              landingPoint: res?.landingPoint,
              seaLevel: res?.seaLevel,
              airGap: res?.airGap,
              waterDepth: res?.waterDepth,
              riserId: res?.riserId,
              riserOD: res?.riserOD,
              chokeLineId: res?.chokeLineId,
              killLineId: res?.killLineId,
              boostLineId: res?.boostLineId,
              rateOfPenetration: res?.rateOfPenetration,
              revolutionsPerMinute: res?.revolutionsPerMinute,
              eccentricity: res?.eccentricity,
            };
            initialForm.value = JSON.parse(JSON.stringify(data));
            targetData.value = JSON.parse(JSON.stringify(data));
          },
          onFinish: () => {
            loading.value = false;
          },
        },
      });
    };

    const getCountries = async (keyword = ""): Promise<void> => {
      loadingCountry.value = true;
      countryStore.getCountries({
        params: {
          keyword: keyword,
          page: 1,
          limit: 500,
        },
        callback: {
          onSuccess: (res: any) => {
            countryList.value = res?.items;
          },
          onFinish: (_err: any) => {
            loadingCountry.value = false;
          },
        },
      });
    };

    const getStates = async (keyword = ""): Promise<void> => {
      if (!targetData?.value?.countryId) return;
      loadingState.value = true;
      countryStore.getStates({
        params: {
          keyword: keyword,
          countryId: targetData.value.countryId,
          page: 1,
          limit: 500,
        },
        callback: {
          onSuccess: (res: any) => {
            stateList.value = res?.items;
          },
          onFinish: (_err: any) => {
            loadingState.value = false;
          },
        },
      });
    };

    const getUsers = async (companyId: string | null): Promise<void> => {
      loadingUser.value = true;
      userStore.getUsers({
        params: {
          companyId,
          page: 1,
          limit: 500,
        },
        callback: {
          onSuccess: (res: any) => {
            const engineers: Option[] = [];
            const supervisors: Option[] = [];

            res?.items?.forEach((item: User.Info) => {
              if (
                item?.roles?.some((role) => role.value === UserType.Supervisor)
              ) {
                supervisors.push({
                  label: `${item?.firstName} ${item?.lastName}`,
                  value: item.id,
                });
              }
              if (
                item?.roles?.some((role) => role.value === UserType.Engineer)
              ) {
                engineers.push({
                  label: `${item?.firstName} ${item?.lastName}`,
                  value: item.id,
                });
              }
            });

            engineerList.value = [...engineers];
            supervisorList.value = [...supervisors];
          },
          onFinish: () => {
            loadingUser.value = false;
          },
        },
      });
    };

    const getCustomers = async (companyId: string | null): Promise<void> => {
      loadingCustomer.value = true;
      customerStore.getCustomers({
        params: {
          companyId,
          page: 1,
          limit: 500,
        },
        callback: {
          onSuccess: (res: any) => {
            customerList.value = res?.items.map((item: any) => {
              return {
                label: item?.customerName,
                value: item?.id,
              };
            });
          },
          onFinish: () => {
            loadingCustomer.value = false;
          },
        },
      });
    };

    const getCompanies = async (): Promise<void> => {
      loadingCompany.value = true;
      companyStore.getCompanies({
        params: {
          page: 1,
          limit: 500,
        },
        callback: {
          onSuccess: (res: any) => {
            const list = res?.items.map((item: any) => {
              return {
                label: item?.name,
                value: item?.id,
              };
            });
            companyList.value = [...list];
          },
          onFinish: () => {
            loadingCompany.value = false;
          },
        },
      });
    };

    const updateWell = async (param: Well.GeneralInfo): Promise<void> => {
      loading.value = true;
      wellStore.updateWell({
        id: props?.id || "",
        params: param,
        callback: {
          onSuccess: (_res: any) => {
            AlertService.toast("You have updated this well!", "success", "top");
            getWellDetails(props.id || "");
          },
          onFinish: (_err: any) => {
            loading.value = false;
          },
        },
      });
    };

    const createWell = async (param: Well.GeneralInfo): Promise<void> => {
      loading.value = true;
      wellStore.createWell({
        params: param,
        callback: {
          onSuccess: (res: any) => {
            initialForm.value = JSON.parse(JSON.stringify(targetData.value));
            router.push({ path: `/wells/${res?.id}` });
          },
          onFinish: (_err: any) => {
            loading.value = false;
          },
        },
      });
    };

    const isValidForm = async (): Promise<boolean> => {
      const result = await formRef?.value?.validate((valid: boolean) => {
        return valid;
      });

      return result;
    };

    return {
      targetData,
      loading,
      formRef,
      rules,
      loadingCountry,
      loadingState,
      loadingCustomer,
      loadingUser,
      loadingCompany,
      countryList,
      stateList,
      customerList,
      engineerList,
      supervisorList,
      companyList,
      isSystemAdmin,
      submit,
      reset,
      getCountries,
      getStates,
      isFormDirty,
      isValidForm,
      isJustEngineer,
    };
  },
});
</script>

<style scoped>
.el-form-item {
  margin-bottom: 0px;
}
</style>
