export enum EDailyTab {
  General = 0,
  MudProperties,
  SiteEquipment,
  Tasks,
  Product,
  VolumeTracking,
  Costs,
  Notes,
}

export const dailyTabs = [
  { value: EDailyTab.General, label: "General" },
  { value: EDailyTab.MudProperties, label: "Mud Properties" },
  { value: EDailyTab.SiteEquipment, label: "Site Equipment" },
  { value: EDailyTab.Tasks, label: "Tasks" },
  { value: EDailyTab.Product, label: "Product & Package Inventory" },
  { value: EDailyTab.VolumeTracking, label: "Volume Tracking" },
  { value: EDailyTab.Costs, label: "Costs" },
  { value: EDailyTab.Notes, label: "Notes" },
];

export enum EGeneralTab {
  WellInformation = 8,
  CasedHole,
  OpenHold,
  DrillString,
  Bits,
  Nozzles,
}

export const generalTabs = [
  { value: EGeneralTab.WellInformation, label: "Well Information" },
  { value: EGeneralTab.CasedHole, label: "Cased Hole" },
  { value: EGeneralTab.OpenHold, label: "Open Hole" },
  { value: EGeneralTab.DrillString, label: "Drill String" },
  { value: EGeneralTab.Bits, label: "Bits" },
  { value: EGeneralTab.Nozzles, label: "Nozzles" },
];

export enum EMudPropertiesTab {
  Samples = 14,
  Solids,
}

export const mudPropertiesTabs = [
  { value: EMudPropertiesTab.Samples, label: "Samples" },
  { value: EMudPropertiesTab.Solids, label: "Solids" },
];

export enum EWellDetailsTab {
  General = 16,
  Interval,
  Plan,
}

export const wellDetailsTabs = [
  { value: EWellDetailsTab.General, label: "General" },
  { value: EWellDetailsTab.Interval, label: "Interval" },
  { value: EWellDetailsTab.Plan, label: "Plan" },
];
