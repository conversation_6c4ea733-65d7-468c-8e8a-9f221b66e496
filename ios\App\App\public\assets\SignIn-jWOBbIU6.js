import{S as _}from"./SvgIcon-CfrWCA-H.js";import{d as y,u as S,q as u,a7 as P,G as F,e as E,_ as I,c as p,b as t,w as c,r as a,o as b,a as o,B as l,H as C,M as H,l as w}from"./index-DalLS0_6.js";import{E as V,F as N,a as B}from"./vee-validate-CHcWpYsl.js";import{c as M,a as h}from"./index.esm-C3uaQ3c9.js";import{_ as R}from"./opslink-light-DYL7OcHB.js";const $=y({name:"sign-in",components:{Field:B,VForm:N,ErrorMessage:V,SvgIcon:_},setup(){const s=S(),e=E(),f=u(!1),i=u(!0),r=u(!1),g=M().shape({email:h().email().required().label("Email"),password:h().min(4).required().label("Password")});return{setIsHidePassword:n=>{i.value=n},onSubmitLogin:async n=>{s.purgeAuth(),r.value=!0,s.login({params:n,callback:{onSuccess:()=>{e.push({name:"wells-overview"})},onFinish:()=>{r.value=!1},onFailure:x=>{P.fire({text:F.USER_NOT_FOUND,icon:"error",buttonsStyling:!1,confirmButtonText:"Confirm",heightAuto:!1,customClass:{confirmButton:"h-auto w-auto bg-button-primary hover:bg-button-alert-danger-hover text-button-text-light hover:text-button-text-light-hover font-semibold h-8 w-8 rounded-lg px-3 py-3"}})}}})},login:g,loading:r,isRemember:f,isHidePassword:i}}}),L={class:"h-auto w-4/5 rounded-3xl mx-auto py-6 bg-card-background text-card-text-light rounded-3 align-self-center flex flex-col justify-center items-center md:h-[50vh] md:text-lg lg:max-w-2/5"},A={class:"text-center mb-10"},q={class:"font-bold fs-4"},T={class:"flex flex-col mb-8 mx-3 h-auto w-full"},U={class:"text-danger mt-[0.3rem]"},D={class:"flex flex-col mb-8 mx-3 h-auto w-full"},O={class:"relative"},j={class:"text-danger mt-[0.3rem]"},G={class:"h-auto w-full flex flex-row mb-8 gap-2 items-center"},z={class:"place-self-end"},J=["disabled"],K={key:0,class:"font-semibold"},Q={key:1,class:"indicator-progress"};function W(s,e,f,i,r,g){const d=a("router-link"),m=a("Field"),n=a("ErrorMessage"),x=a("SvgIcon"),v=a("VForm");return b(),p("div",L,[t(v,{class:"flex flex-col items-center w-4/5",onSubmit:s.onSubmitLogin,"validation-schema":s.login,"initial-values":{email:"",password:""}},{default:c(()=>[e[10]||(e[10]=o("img",{class:"h-30 w-auto",src:R},null,-1)),o("div",A,[e[4]||(e[4]=o("h1",{class:"mb-3 font-bold"},"Log In",-1)),o("div",q,[e[3]||(e[3]=l(" New here? ")),t(d,{to:"/sign-up",class:"text-link hover:text-link-hover font-bold"},{default:c(()=>e[2]||(e[2]=[l(" Create an Account ")])),_:1})])]),o("div",T,[e[5]||(e[5]=o("label",{class:"form-label tracking-wide font-bold"},"Email",-1)),t(m,{tabindex:"1",class:"rounded-lg bg-input-background text-input-text-dark pl-3",type:"text",name:"email",autocomplete:"off",placeholder:"Enter email"}),o("div",U,[t(n,{name:"email"})])]),o("div",D,[e[6]||(e[6]=o("label",{class:"form-label tracking-wide font-bold"},"Password",-1)),o("div",O,[t(m,{tabindex:"2",class:"w-full rounded-lg bg-input-background text-input-text-dark pl-3",type:s.isHidePassword?"password":"input",name:"password",autocomplete:"off",placeholder:"Enter password"},null,8,["type"]),o("span",{class:"text-icon-dark absolute top-[0.3rem] right-1",onClick:e[0]||(e[0]=()=>s.setIsHidePassword(!s.isHidePassword))},[t(x,{icon:s.isHidePassword?"hidePasswordIcon":"showPasswordIcon"},null,8,["icon"])])]),o("div",j,[t(n,{name:"password"})])]),o("div",G,[C(o("input",{class:"h-4 w-4",type:"checkbox","onUpdate:modelValue":e[1]||(e[1]=k=>s.isRemember=k)},null,512),[[H,s.isRemember]]),e[8]||(e[8]=o("label",{class:"font-semibold cursor-pointer text-xs whitespace-nowrap md:text-lg"}," Remember me ",-1)),t(d,{to:"/forgot-password",class:"ms-3 text-link hover:text-link-hover font-bold text-xs whitespace-nowrap md:text-lg"},{default:c(()=>e[7]||(e[7]=[l(" Forgot Password ? ")])),_:1})]),o("div",z,[o("button",{class:"bg-button-primary hover:bg-button-primary-hover rounded-md px-2 py-1 md:px-4 md:py-3 md:text-xl",type:"submit",disabled:s.loading},[s.loading?w("",!0):(b(),p("span",K," Login ")),s.loading?(b(),p("span",Q,e[9]||(e[9]=[l(" Please wait... "),o("span",{class:"spinner-border spinner-border-sm align-middle ms-2"},null,-1)]))):w("",!0)],8,J)])]),_:1},8,["onSubmit","validation-schema"])])}const se=I($,[["render",W]]);export{se as default};
