import{O as d,P as t,Q as r}from"./index-BmHWvWFS.js";import{h as i}from"./handleFailure-WBgBpurp.js";const F=d("customer",()=>({getCustomers:async({params:a,callback:o})=>{var e;const n=t.get(o,"onSuccess",t.noop),c=t.get(o,"onFinish",t.noop);try{r.setHeader();const s=await r.getWithParams("customers",a);n(((e=s.data)==null?void 0:e.data)||s.data)}catch(s){i(s,o)}finally{c()}},getCustomerDetails:async({id:a,callback:o})=>{var e;const n=t.get(o,"onSuccess",t.noop),c=t.get(o,"onFinish",t.noop);try{r.setHeader();const s=await r.get(`customers/${a}`);n(((e=s.data)==null?void 0:e.data)||s.data)}catch(s){i(s,o)}finally{c()}},updateCustomer:async({id:a,params:o,callback:n})=>{var s;const c=t.get(n,"onSuccess",t.noop),e=t.get(n,"onFinish",t.noop);try{r.setHeader();const u=await r.put(`customers/${a}`,o);c(((s=u.data)==null?void 0:s.data)||u.data)}catch(u){i(u,n)}finally{e()}},removeCustomers:async({customerIds:a,callback:o})=>{var e;const n=t.get(o,"onSuccess",t.noop),c=t.get(o,"onFinish",t.noop);try{r.setHeader();const s=await r.post("customers/delete",{ids:a});n(((e=s.data)==null?void 0:e.data)||s.data)}catch(s){i(s,o)}finally{c()}},createCustomer:async({params:a,callback:o})=>{var e;const n=t.get(o,"onSuccess",t.noop),c=t.get(o,"onFinish",t.noop);try{r.setHeader();const s=await r.post("customers",a);n(((e=s.data)==null?void 0:e.data)||s.data)}catch(s){i(s,o)}finally{c()}}}));export{F as u};
