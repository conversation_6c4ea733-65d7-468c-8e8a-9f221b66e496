import{d as i,_ as p,c as e,o as t,a as s,t as o,F as n,k as d}from"./index-CGNRhvz7.js";const m=i({name:"page-header",components:{},props:{title:String,breadcrumbs:{type:Array,default:()=>[]}},setup(){return{}}}),u={class:"flex flex-col px-4 bg-white"},f={class:"text-gray-800 font-bold"},_={class:"flex flex-row text-sm font-semibold items-center py-2"},g={key:0,class:"text-grey-800"};function x(r,a,y,b,h,k){return t(),e("div",u,[s("h1",f,o(r.title),1),s("ul",_,[(t(!0),e(n,null,d(r.breadcrumbs,(l,c)=>(t(),e(n,{key:c},[c===r.breadcrumbs.length-1?(t(),e("li",g,o(l),1)):(t(),e(n,{key:1},[s("li",null,o(l),1),a[0]||(a[0]=s("div",{class:"bg-grey-500 h-[2px] w-[5px] mx-2"},null,-1))],64))],64))),128))])])}const w=p(m,[["render",x]]);export{w as P};
