<template>
  <div class="card position-relative">
    <div class="card-body d-flex align-items-center gap-2 cursor-pointer p-2">
      <div class="symbol symbol-30px">
        <img :src="file?.type" alt="" />
      </div>
      <div class="text-start">
        <p
          class="fs-5 fw-bold mb-2 text-gray-800 text-hover-primary mw-165 text-truncate"
        >
          {{ file?.title }}
        </p>
        <span class="fs-6 fw-semibold text-gray-400">{{ file?.size }}</span>
      </div>
    </div>

    <button
      v-if="edit"
      class="btn rounded-circle btn-icon btn-cancel btn-action bg-light btn-view position-absolute top-0 start-0"
      type="button"
      @click="deleteFile(file)"
    >
      <span class="svg-icon svg-icon-9">
        <SvgIcon icon="cancelIcon" />
      </span>
    </button>
  </div>
</template>

<script lang="ts">
import { defineComponent } from "vue";
import SvgIcon from "@/constants/SvgIcon.vue";

export default defineComponent({
  name: "file-component",
  props: {
    file: {
      type: Object,
      required: true,
      default: {},
    },

    deleteFile: {
      type: Function,
      required: false,
      default: () => {},
    },
    edit: {
      type: Boolean,
      required: false,
      default: false,
    },
  },
  components: { SvgIcon },
  setup(props) {
    const deleteFile = (item: any) => {
      if (props?.deleteFile) {
        props?.deleteFile(item);
      }
    };

    const getImage = (type: string) => {
      if (type?.includes("css")) {
        return "media/svg/files/css.svg";
      } else if (type?.includes("pdf")) {
        return "media/svg/files/pdf.svg";
      } else if (type?.includes("docx")) {
        return "media/svg/files/docx.svg";
      } else if (type?.includes("doc")) {
        return "media/svg/files/doc.svg";
      } else if (type?.includes("image")) {
        return "media/svg/files/image.svg";
      } else {
        return "media/svg/files/file.svg";
      }
    };

    return {
      deleteFile,
      getImage,
    };
  },
});
</script>

<style scope>
.mw-165 {
  max-width: 165px;
}

.btn-cancel {
  width: 1.5rem !important;
  height: 1.5rem !important;
}
</style>
