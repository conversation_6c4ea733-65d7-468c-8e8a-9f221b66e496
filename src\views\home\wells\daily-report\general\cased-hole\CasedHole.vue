<template>
  <div class="cased-hole">
    <div v-if="loading" class="text-center">
      <div class="spinner-border" role="status">
        <span class="sr-only">Loading...</span>
      </div>
    </div>
    <template v-else>
      <NoEntries
        v-if="casedHoleList.length === 0"
        :addNew="toggleNewCasedHoleModal"
      />
      <div
        v-else
        class="grid grid-cols-1 gap-4 md:grid-cols-2 md:gap-7 lg:grid-cols-3"
      >
        <div
          v-for="item in casedHoleList"
          :key="item.id"
          class="relative h-auto w-full bg-minicard-background text-minicard-text-light flex flex-col items-center p-4 rounded-xl border-t-2 border-active my-2 first:mt-3 last:mb-5 font-semibold md:first:mt-0 md:last:mb-0 md:m-0"
        >
        <div class="h-auto w-full flex flex-col gap-3">
          <h5 class="text-xl font-bold">{{ item.description }}</h5>
          <div
            class="flex items-center justify-between border-dashed border-b-[1px] py-3"
          >
            <span>Top Depth</span>
            <span>
              {{ `${item.topDepth} (ft)` }}
            </span>
          </div>
          <div
            class="flex items-center justify-between border-dashed border-b-[1px] py-3"
          >
            <span>Casing Shoe Depth</span>
            <span>
              {{ numberWithCommas(item.casingShoeDepth) }}
              <span>(in)</span>
            </span>
          </div>
          <div
            class="flex items-center justify-between border-dashed border-b-[1px] py-3 mb-3"
          >
            <span>Casing Length</span>
            <span>
              {{ numberWithCommas(item.casingLength) }}
              <span>(lb/ft)</span>
            </span>
          </div>
        </div>

        <div class="flex flex-wrap items-center gap-2">
          <div
            class="flex items-center gap-2 border border-gray-300 border-dashed rounded py-3 px-4"
          >
            <div>
              {{ `${numberWithCommas(item.outsideDiameter)} (in)` }}
            </div>
            <div class="text-danger">OD</div>
          </div>
          <div
            class="flex items-center gap-2 border border-gray-300 border-dashed rounded py-3 px-4"
          >
            <div>
              {{ `${numberWithCommas(item.insideDiameter)} (in)` }}
            </div>
            <div>ID</div>
          </div>
          <div
            class="flex items-center gap-2 border border-gray-300 border-dashed rounded py-3 px-4"
          >
            <div>
              {{ `${numberWithCommas(item.weight)} (lb/ft)` }}
            </div>
            <div class="text-success">Wt</div>
          </div>
        </div>

        <div
          class="flex flex-row items-center gap-2 absolute top-0 end-0 -translate-y-1/2"
        >
          <button
            class="p-2 rounded-full bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover"
            @click="toggleEditCasedHoleModal(item.id.toString())"
          >
            <SvgIcon icon="pencilIcon" />
          </button>
          <button
            class="p-2 rounded-full bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover"
            @click="deleteCasedHole(item?.id?.toString())"
          >
            <span class="text-danger">
              <SvgIcon icon="trashIcon" />
            </span>
          </button>
        </div>
      </div>
      </div>
    </template>
  </div>
  <BottomTool
    :addNew="toggleNewCasedHoleModal"
    :showHelpWindow="showCustomize"
  />
  <CasedHoleModal
    :isVisible="isModalVisible"
    :close="toggleNewCasedHoleModal"
    ref="casedHoleModal"
    :loadPage="getCasedHoles"
  />
</template>

<script lang="ts">
import NoEntries from "@/components/NoEntries.vue";
import SvgIcon from "@/constants/SvgIcon.vue";
import { numberWithCommas } from "@/utils/numberFormatter";
import AlertService from "@/services/AlertService";
import { useCasedHoleStore } from "@/stores/cased-hole";
import { defineComponent, inject, onMounted, ref, type Ref } from "vue";
import BottomTool from "@/components/common/BottomTool.vue";
import CasedHoleModal from "./CasedHoleModal.vue";
import type { Provide } from "@/types/injection-types";

export default defineComponent({
  name: "cased-hole",
  components: {
    SvgIcon,
    BottomTool,
    CasedHoleModal,
    NoEntries,
  },
  props: {
    showCustomize: {
      type: Function,
      required: false,
    },
  },
  setup() {
    const casedHoleModal: Ref<any> = ref<typeof CasedHoleModal | null>(null);
    const loading = ref(false);
    const casedHoleStore = useCasedHoleStore();
    const casedHoleList = ref<any>([]);
    const dailyReportProvide = inject<Provide.DailyReport>("dailyReport");
    const isModalVisible = ref(false);

    onMounted(() => {
      if (dailyReportProvide?.getDailyReportId()) {
        getCasedHoles();
      }
    });

    const getCasedHoles = async (
      params = {
        dailyReportId: dailyReportProvide?.getDailyReportId(),
        page: 1,
        limit: 200,
      }
    ): Promise<void> => {
      loading.value = true;

      casedHoleStore.getCasedHoles({
        params: params,
        callback: {
          onSuccess: (res: any) => {
            casedHoleList.value = res?.items;
          },
          onFinish: (_err: any) => {
            loading.value = false;
          },
        },
      });
    };

    const toggleNewCasedHoleModal = () => {
      isModalVisible.value = !isModalVisible.value;
    };

    const toggleEditCasedHoleModal = (id: string): void => {
      casedHoleModal?.value?.setId(id);
      isModalVisible.value = !isModalVisible.value;
    };

    const deleteCasedHole = (id: string) => {
      AlertService.deletionAlert({
        onConfirmed: () => {
          deleteCasedHoleById(id);
        },
      });
    };

    const deleteCasedHoleById = async (id: string): Promise<void> => {
      loading.value = true;
      casedHoleStore.deleteCasedHole({
        id: id,
        callback: {
          onSuccess: (_res: any) => {
            getCasedHoles();
          },
          onFinish: (_err: any) => {
            loading.value = false;
          },
        },
      });
    };

    return {
      loading,
      casedHoleList,
      casedHoleModal,
      isModalVisible,
      numberWithCommas,
      getCasedHoles,
      deleteCasedHole,
      toggleEditCasedHoleModal,
      toggleNewCasedHoleModal,
    };
  },
});
</script>
