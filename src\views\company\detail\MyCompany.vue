<template>
  <div v-if="loading" class="text-center my-auto">
    <div class="spinner-border text-primary" role="status">
      <span class="sr-only">Loading...</span>
    </div>
  </div>
  <PageHeader
    title="My Company"
    :breadcrumbs="['My Company', companyDetail?.name || '']"
  />
  <CompanyDetail
    v-if="!loading && companyDetail"
    :companyDetail="companyDetail"
    :reloadCompanyData="getCompanyDetails"
  />
</template>

<script lang="ts">
import PageHeader from "@/components/PageHeader.vue";
import JwtService from "@/services/JwtService";
import { useCompanyStore } from "@/stores/company";
import { defineComponent, onMounted, ref } from "vue";
import CompanyDetail from "./CompanyDetail.vue";

export default defineComponent({
  name: "my-company",
  components: {
    CompanyDetail,
    PageHeader,
  },
  setup() {
    const loading = ref<boolean>(false);
    const companyStore = useCompanyStore();
    const companyDetail = ref<Company.Info>();

    onMounted(() => {
      getCompanyDetails();
    });

    const getCompanyDetails = async (): Promise<void> => {
      const id = JwtService?.getUserInfo()?.companyId;
      if (!id) return;
      loading.value = true;
      companyStore.getCompanyById({
        id,
        callback: {
          onSuccess: (res: any) => {
            companyDetail.value = res;
          },
          onFinish: () => {
            loading.value = false;
          },
        },
      });
    };

    return {
      loading,
      companyDetail,
      getCompanyDetails,
    };
  },
});
</script>
