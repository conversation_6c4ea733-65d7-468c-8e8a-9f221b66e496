<template>
  <div
    v-if="isVisible"
    class="fixed top-0 right-0 bottom-0 left-0 z-40 flex items-center"
  >
    <!-- Dark overlay background -->
    <div class="absolute inset-0 bg-sidebar-background opacity-25"></div>
    <div
      class="relative bg-card-background text-card-text-light max-h-11/12 w-11/12 flex flex-col items-center gap-3 mx-auto my-6 p-5 rounded-xl overflow-y-scroll md:overflow-hidden md:w-4/5 md:text-lg lg:w-2/5 lg:h-auto"
    >
      <div class="h-auto w-full flex flex-row items-center justify-between">
        <h3 class="text-lg font-bold">
          {{ `${id ? "Edit Bit" : "New Bit"}` }}
        </h3>
        <span class="cursor-pointer" @click="closeModal">
          <SvgIcon icon="closeModalIcon" />
        </span>
      </div>

      <el-form
        id="bit_form"
        @submit.prevent="submit"
        :model="targetData"
        :rules="rules"
        ref="formRef"
        class="h-auto w-full"
      >
        <div class="h-auto w-full flex flex-col gap-3 md:flex-row md:gap-4">
          <div class="h-auto w-full flex flex-col gap-3">
            <div class="h-auto w-full flex flex-col gap-1">
              <label class="font-bold">
                Bit No.
                <span class="text-danger-active font-light">*</span>
                <el-popover placement="top" :width="300" trigger="hover">
                  <template #reference>
                    <i class="fas fa-exclamation-circle ms-2 fs-7"></i>
                  </template>
                  <span
                    >The "Bit No." is a unique identifier or serial number
                    assigned to the drill bit. It helps track the usage and
                    performance history of individual bits.
                  </span>
                </el-popover></label
              >
              <el-form-item prop="bitNo" class="mt-auto">
                <el-input
                  v-model="targetData.bitNo"
                  placeholder=""
                  name="bitNo"
                ></el-input>
              </el-form-item>
            </div>
            <div class="h-auto w-full flex flex-col gap-1">
              <label class="font-bold"
                >Type<el-popover placement="top" :width="300" trigger="hover">
                  <template #reference>
                    <i class="fas fa-exclamation-circle ms-2 fs-7"></i>
                  </template>
                  <span
                    >The "Type" of drill bit refers to the specific design and
                    characteristics of the bit. It can include information about
                    the bit's cutting structure, materials, and intended use.
                    Examples of types include PDC (Polycrystalline Diamond
                    Compact), roller cone, diamond-impregnated, and others.
                  </span>
                </el-popover></label
              >
              <el-form-item prop="type">
                <el-input
                  v-model="targetData.type"
                  placeholder=""
                  name="bitNo"
                ></el-input>
              </el-form-item>
            </div>

            <div class="h-auto w-full flex flex-col gap-1">
              <label class="font-bold"
                >IADC Type<el-popover
                  placement="top"
                  :width="300"
                  trigger="hover"
                >
                  <template #reference>
                    <i class="fas fa-exclamation-circle ms-2 fs-7"></i>
                  </template>
                  <span
                    >Bit Type (IADC Classification): The IADC (International
                    Association of Drilling Contractors) classification code, if
                    applicable, which provides standardized codes for various
                    drill bit types.
                  </span>
                </el-popover></label
              >
              <el-form-item prop="iadcType" class="mt-auto">
                <el-input
                  v-model="targetData.iadcType"
                  placeholder=""
                  name="iadcType"
                ></el-input>
              </el-form-item>
            </div>
          </div>
          <div class="h-auto w-full flex flex-col gap-3">
            <div class="h-auto w-full flex flex-col gap-1">
              <label class="font-bold"
                >Bit Size (inches)<el-popover
                  placement="top"
                  :width="300"
                  trigger="hover"
                >
                  <template #reference>
                    <i class="fas fa-exclamation-circle ms-2 fs-7"></i>
                  </template>
                  <span
                    >The size of the drill bit, often specified by its diameter
                    in inches or millimeters. Bit size affects the hole diameter
                    and drilling efficiency.
                  </span>
                </el-popover></label
              >
              <el-form-item prop="bitSize">
                <el-input
                  type="number"
                  class="w-100"
                  :controls="false"
                  step="any"
                  v-model="targetData.bitSize"
                  placeholder=""
                  name="bitSize"
                ></el-input>
              </el-form-item>
            </div>

            <div class="h-auto w-full flex flex-col gap-1">
              <label class="font-bold"
                >Depth<el-popover placement="top" :width="300" trigger="hover">
                  <template #reference>
                    <i class="fas fa-exclamation-circle ms-2 fs-7"></i>
                  </template>
                  <span
                    >The "Depth" represents the depth at which the drill bit is
                    used or the depth at which it was pulled out of the
                    wellbore. It is typically measured in feet or meters.
                  </span>
                </el-popover></label
              >
              <el-form-item prop="depth" class="mt-auto">
                <el-input
                  type="number"
                  class="w-100"
                  :controls="false"
                  step="any"
                  v-model="targetData.depth"
                  placeholder=""
                  name="depth"
                ></el-input>
              </el-form-item>
            </div>
            <div class="h-auto w-full flex flex-col gap-1">
              <label class="font-bold"
                >Bit Run Duration<el-popover
                  placement="top"
                  :width="300"
                  trigger="hover"
                >
                  <template #reference>
                    <i class="fas fa-exclamation-circle ms-2 fs-7"></i>
                  </template>
                  <span
                    >The amount of time or footage the drill bit was used before
                    it was pulled out of the hole. It helps assess the bit's
                    performance and longevity.
                  </span>
                </el-popover></label
              >
              <el-form-item prop="bitRunDuration">
                <el-input
                  type="number"
                  class="w-100"
                  :controls="false"
                  step="any"
                  v-model="targetData.bitRunDuration"
                  placeholder=""
                  name="bitRunDuration"
                ></el-input>
              </el-form-item>
            </div>
          </div>
        </div>
        <div class="h-auto w-full flex flex-row items-center gap-2">
          <button
            type="button"
            class="bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover rounded-md px-4 py-2 font-semibold md:px-4 md:py-3 md:text-xl"
            @click="closeModal"
            :disabled="loading"
          >
            Discard
          </button>
          <button
            class="bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover rounded-md px-4 py-2 font-semibold md:px-4 md:py-3 md:text-xl"
            type="submit"
            :disabled="loading"
          >
            <span v-if="!loading" class="indicator-label"> Save </span>
            <span v-if="loading" class="indicator-progress">
              Please wait...
              <span
                class="spinner-border spinner-border-sm align-middle ms-2"
              ></span>
            </span>
          </button>
        </div>
      </el-form>
    </div>
  </div>
</template>

<script lang="ts">
import SvgIcon from "@/constants/SvgIcon.vue";
import { useDrillBitStore } from "@/stores/drill-bit";
import { defineComponent, inject, ref, watch } from "vue";
import { useRoute } from "vue-router";
import type { Provide } from "@/types/injection-types";

interface NewBitData {
  bitNo: string;
  type: string;
  iadcType: string;
  bitSize: number | null;
  depth: number | null;
  bitRunDuration: number | null;
}

export default defineComponent({
  name: "bit-modal",
  components: { SvgIcon },
  props: {
    isVisible: {
      type: Boolean,
      required: true,
    },
    close: {
      type: Function,
      required: true,
    },
    loadPage: {
      type: Function,
      default: () => {},
      required: true,
    },
  },
  setup(props) {
    const route = useRoute();
    const drillBitStore = useDrillBitStore();
    const modal = ref(false);
    const targetData = ref<NewBitData>({
      bitNo: "",
      type: "",
      iadcType: "",
      bitSize: null,
      depth: null,
      bitRunDuration: null,
    });
    const formRef = ref<null | HTMLFormElement>(null);
    const loading = ref<boolean>(false);
    const id = ref("");
    const dailyReportProvide = inject<Provide.DailyReport>("dailyReport");

    watch(id, (newValue) => {
      console.log("id", newValue);
      if (newValue !== "") {
        getDrillBitDetails();
      }
    });

    watch(
      () => props.isVisible,
      (newValue) => {
        if (newValue === false) {
          id.value = "";
          reset();
          formRef?.value?.resetFields();
        }
      }
    );

    const getDrillBitDetails = async (): Promise<void> => {
      drillBitStore.getDrillBitDetails({
        id: id.value,
        callback: {
          onSuccess: (res: any) => {
            targetData.value = res;
            console.log("targetData", targetData.value);
          },
        },
      });
    };

    const updateDrillBit = async (param: any): Promise<void> => {
      loading.value = true;
      drillBitStore.updateDrillBit({
        id: id.value,
        params: param,
        callback: {
          onSuccess: (_res: any) => {
            if (props?.loadPage) {
              props?.loadPage();
            }
            props.close();
          },
          onFinish: (_err: any) => {
            loading.value = false;
          },
        },
      });
    };

    const createDrillBit = async (param: any): Promise<void> => {
      loading.value = true;
      drillBitStore.createDrillBit({
        params: param,
        callback: {
          onSuccess: (_res: any) => {
            if (props?.loadPage) {
              props?.loadPage();
            }
            props.close();
          },
          onFinish: (_err: any) => {
            loading.value = false;
          },
        },
      });
    };

    const closeModal = () => {
      props.close();
    };

    const setId = (bitId: string) => {
      id.value = bitId.toString();
    };

    const rules = ref({
      bitNo: [
        {
          required: true,
          message: "Please type Bit No.",
          trigger: "blur",
        },
      ],
    });

    const submit = () => {
      if (!formRef.value) {
        return;
      }

      formRef.value.validate((valid: boolean) => {
        if (valid) {
          const param = {
            bitNo: targetData?.value?.bitNo,
            type: targetData?.value?.type,
            iadcType: targetData?.value?.iadcType,
            bitSize: Number(targetData?.value?.bitSize),
            depth: targetData?.value?.depth
              ? Number(targetData?.value?.depth)
              : null,
            bitRunDuration: targetData?.value?.bitRunDuration
              ? Number(targetData?.value?.bitRunDuration)
              : null,
          };

          if (id?.value) {
            updateDrillBit({
              ...param,
              dailyReportId: dailyReportProvide?.getDailyReportId(),
            });
          } else {
            dailyReportProvide?.createDailyReport({
              wellId: route?.params?.id as string,
              callback: {
                onSuccess: (res: string) => {
                  createDrillBit({ ...param, dailyReportId: res });
                },
              },
            });
          }
        }
      });
    };

    const reset = () => {
      targetData.value = {
        bitNo: "",
        type: "",
        iadcType: "",
        bitSize: null,
        depth: null,
        bitRunDuration: null,
      };
    };

    return {
      id,
      modal,
      rules,
      loading,
      targetData,
      formRef,
      closeModal,
      submit,
      setId,
      reset,
    };
  },
});
</script>
