<template>
  <div
    class="bg-screen-background text-card-text-dark min-h-screen max-w-screen flex flex-col items-center justify-center text-center md:text-lg"
  >
    <div v-if="loading" class="text-center p-5">
      <div class="spinner-border text-primary" role="status">
        <span class="sr-only">Loading...</span>
      </div>
    </div>
    <!--begin::Title-->
    <div v-else>
      <h1 class="font-extrabold mb-4">Accept Invitation</h1>
      <!--end::Title-->
      <!--begin::Text-->
      <!-- <div class="fw-semibold fs-6 text-gray-500 mb-7">
              Sorry, looks like there are some errors detected, please try again
              or contact your admin.
            </div> -->
      <!--end::Text-->
      <!--begin::Illustration-->
      <div class="mb-3">
        <img src="/media/auth/agency.png" class="w-4/5 h-auto mx-auto" alt="" />
        <!-- <img
                src="/media/auth/agency-dark.png"
                class="mw-100 mh-300px theme-dark-show"
                alt=""
              /> -->
      </div>
    </div>
    <!--end::Illustration-->
  </div>
</template>

<script lang="ts">
import { useAuthStore } from "@/stores/auth";
import { useUserStore } from "@/stores/user";
import { defineComponent, onMounted, ref } from "vue";
import { useRoute, useRouter } from "vue-router";

export default defineComponent({
  name: "accept-invite",
  components: {},
  setup() {
    const userStore = useUserStore();
    const authStore = useAuthStore();
    const router = useRouter();
    const route = useRoute();
    const token = route.params?.token?.toString() || "";
    const loading = ref<boolean>(false);
    // const themeMode = computed(() => {
    //   return storeTheme.mode;
    // });
    // const bgImage =
    //   themeMode.value !== "dark"
    //     ? "/media/auth/bg1.jpg"
    //     : "/media/auth/bg1-dark.jpg";

    onMounted(() => {
      acceptInvite();
    });

    const acceptInvite = async () => {
      loading.value = true;
      userStore.acceptInviteToken({
        token,
        callback: {
          onSuccess: (res: any) => {
            authStore.setAuth(res);
            router.push({ path: "/register-invited-user" });
          },
          onFinish: () => {
            loading.value = false;
          },
        },
      });
    };

    return {
      loading,
      // bgImage,
      acceptInvite,
    };
  },
});
</script>
