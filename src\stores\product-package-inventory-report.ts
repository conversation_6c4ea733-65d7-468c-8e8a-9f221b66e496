import ApiService from "../services/ApiService";
import { get, noop } from "lodash";
import { defineStore } from "pinia";
import { handleFailure } from "../utils/handleFailure";
import type { Callback } from "../types/common";

export const useProductPackageReportStore = defineStore(
  "productPackageReport",
  () => {
    const getProductPackageReports = async ({
      params,
      callback,
    }: {
      params: any;
      callback: Callback;
    }): Promise<void> => {
      const onSuccess = get(callback, "onSuccess", noop);
      const onFinish = get(callback, "onFinish", noop);

      try {
        ApiService.setHeader();
        const response = await ApiService.getWithParams(
          `productAndPackageInventoryReports`,
          params
        );
        onSuccess(response.data?.data || response.data);
      } catch (error) {
        handleFailure(error, callback);
      } finally {
        onFinish();
      }
    };

    const getProductPackageReportDetails = async ({
      id,
      callback,
    }: {
      id: string;
      callback: Callback;
    }): Promise<void> => {
      const onSuccess = get(callback, "onSuccess", noop);
      const onFinish = get(callback, "onFinish", noop);

      try {
        ApiService.setHeader();
        const response = await ApiService.get(
          `productAndPackageInventoryReports/${id}`
        );
        onSuccess(response.data?.data || response.data);
      } catch (error) {
        handleFailure(error, callback);
      } finally {
        onFinish();
      }
    };

    const createProductPackageReport = async ({
      params,
      callback,
    }: {
      params: any;
      callback: Callback;
    }): Promise<void> => {
      const onSuccess = get(callback, "onSuccess", noop);
      const onFinish = get(callback, "onFinish", noop);

      try {
        ApiService.setHeader();
        const response = await ApiService.post(
          `productAndPackageInventoryReports`,
          params
        );
        onSuccess(response.data?.data || response.data);
      } catch (error) {
        handleFailure(error, callback);
      } finally {
        onFinish();
      }
    };

    const deleteProductPackageReport = async ({
      id,
      callback,
    }: {
      id: string;
      callback: Callback;
    }): Promise<void> => {
      const onSuccess = get(callback, "onSuccess", noop);
      const onFinish = get(callback, "onFinish", noop);

      try {
        ApiService.setHeader();
        const response = await ApiService.delete(
          `productAndPackageInventoryReports/${id}`
        );
        onSuccess(response.data?.data || response.data);
      } catch (error) {
        handleFailure(error, callback);
      } finally {
        onFinish();
      }
    };

    return {
      getProductPackageReports,
      getProductPackageReportDetails,
      createProductPackageReport,
      deleteProductPackageReport,
    };
  }
);
