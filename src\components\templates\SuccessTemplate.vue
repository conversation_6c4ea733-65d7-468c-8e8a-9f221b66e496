<template>
  <div class="w-80 mx-auto flex flex-col justify-center items-center">
    <img src="/media/success.svg" :width="170" />
    <h1 class="text-dark text-center mb-3">{{ message }}</h1>
    <div class="text-gray-400 text-center font-semibold">
      {{ description }}
    </div>
    <button
      type="button"
      class="mt-12 px-3 py-2 rounded-md bg-primary font-bold text-white"
      @click="goToSignIn"
    >
      <span class="indicator-label"> {{ btnTitle }} </span>
    </button>
  </div>
</template>

<script lang="ts">
import { defineComponent } from "vue";
import { useRouter } from "vue-router";

export default defineComponent({
  name: "success-template",
  components: {},
  props: {
    description: {
      type: String,
      required: false,
      default: "",
    },
    message: {
      type: String,
      required: true,
    },
    btnTitle: {
      type: String,
      required: false,
      default: "Go To Login",
    },
    btnPath: {
      type: String,
      required: false,
      default: "/sign-in",
    },
  },
  setup(props) {
    const router = useRouter();
    const goToSignIn = () => {
      router.push({ path: props.btnPath });
    };
    return { goToSignIn };
  },
});
</script>
