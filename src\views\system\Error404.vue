<template>
  <div
    class="bg-screen-background text-card-text-dark min-h-screen max-w-screen flex flex-col items-center justify-center md:text-lg"
  >
    <!--begin::Title-->
    <h1 class="font-extrabold mb-4">Oops!</h1>
    <!--end::Title-->
    <!--begin::Text-->
    <div class="font-semibold mb-7">We can't find that page.</div>
    <!--end::Text-->
    <!--begin::Illustration-->
    <div class="mb-3">
      <img
        src="/media/auth/404-error.png"
        class="mw-100 mh-300px theme-light-show"
        alt=""
      />
      <!-- <img
              src="/media/auth/404-error-dark.png"
              class="mw-100 mh-300px theme-dark-show"
              alt=""
            /> -->
    </div>
    <!--end::Illustration-->
    <!--begin::Link-->
    <div class="mb-0">
      <router-link
        to="/"
        class="bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover rounded-md px-4 py-2 font-semibold md:px-4 md:py-3 md:text-xl"
        >Return Home</router-link
      >
    </div>
    <!--end::Link-->
  </div>
</template>

<script lang="ts">
import { defineComponent } from "vue";

export default defineComponent({
  name: "error-404",
  components: {},
  setup() {
    // const themeMode = computed(() => {
    //   return storeTheme.mode;
    // });
    // const bgImage =
    //   themeMode.value !== "dark"
    //     ? getAssetPath("media/auth/bg1.jpg")
    //     : getAssetPath("media/auth/bg1-dark.jpg");

    // onMounted(() => {
    //   LayoutService.emptyElementClassesAndAttributes(document.body);

    //   storeBody.addBodyClassname("bg-body");
    //   storeBody.addBodyAttribute({
    //     qualifiedName: "style",
    //     value: `background-image: url("${bgImage}")`,
    //   });
    // });

    return {};
  },
});
</script>
