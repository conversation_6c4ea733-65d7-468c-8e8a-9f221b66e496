<template>
  <div
    class="bg-screen-background h-auto w-11/12 mx-auto my-4 rounded-xl flex flex-col items-center"
  >
    <div
      class="bg-card-background text-card-text rounded-lg h-auto w-full flex flex-col items-start gap-3 lg:w-4/5 lg:mx-auto lg:min-w-[1560px]"
    >
      <div class="h-auto w-full flex flex-col items-start my-4 px-8 gap-3">
        <h1 class="font-bold text-lg">{{ label }}</h1>
        <div class="h-auto w-full">
          <el-form @submit.prevent="searchCostSettings">
            <el-form-item class="mb-0">
              <el-input
                placeholder="Search"
                v-model="search"
                name="search"
                size="large"
                ><template #prefix>
                  <el-icon class="el-input__icon">
                    <SvgIcon icon="searchIcon"
                  /></el-icon> </template
              ></el-input> </el-form-item
          ></el-form>
          <div
            class="h-auto w-full flex flex-row gap-3 overflow-x-scroll mt-4 items-center justify-between md:overflow-hidden md:justify-start md:gap-4"
          >
            <button
              class="flex flex-row gap-2 font-semibold font- rounded-md px-4 py-2 text-button-text-light hover:text-button-text-light-hover"
              :class="
                isShowFilter
                  ? 'bg-button-primary-active'
                  : 'bg-button-primary hover:bg-button-primary-hover'
              "
              @click="() => toggleFilter(!isShowFilter)"
            >
              <SvgIcon icon="filterIcon" />
              Filter
            </button>
            <button
              class="bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover rounded-md px-4 py-2 font-semibold flex flex-row items-center gap-2 justify-between"
              @click="toggleNewServiceCosts"
            >
              <SvgIcon icon="addIcon" />
              New
            </button>
            <button
              class="bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover rounded-md px-4 py-2 font-semibold flex flex-row items-center gap-2 justify-between"
            >
              <SvgIcon icon="exportIcon" classname="rotate-90" />
              Export
            </button>
          </div>
        </div>
        <Filter
          v-show="isShowFilter"
          :hideFilter="() => toggleFilter(false)"
          :onFilter="onFilter"
        />
      </div>
      <div v-if="loading" class="text-center">
        <div class="spinner-border text-primary" role="status">
          <span class="sr-only">Loading...</span>
        </div>
      </div>
      <el-empty v-else-if="costList.length === 0" description="No Data" />
      <div v-else class="h-auto w-full overflow-x-scroll p-4 md:overflow-hidden">
        <table class="md:w-11/12 md:mx-auto md:text-center">
          <thead>
            <tr class="font-bold whitespace-nowrap">
              <TableHeader
                :headers="tableHeader"
                :sortBy="sortParams.sortBy!"
                :sortDirection="sortParams.sortDirection!"
                :onSort="onSort"
              >
              </TableHeader>
            </tr>
          </thead>
          <tbody class="text-sm">
            <template v-for="item in costList" :key="item?.id">
              <tr>
                <td class="font-semibold text-nowrap p-4">
                  {{ item?.name }}
                </td>
                <td
                  v-if="isSystemAdmin()"
                  class="font-semibold text-nowrap p-4"
                >
                  {{ item?.company?.name }}
                </td>
                <td class="font-semibold text-nowrap p-4">
                  {{ `$${numberWithCommas(item?.cost)}` }}
                </td>
                <td class="font-semibold text-nowrap p-4">
                  {{ formatDate(item?.createdAt, "MMM DD, YYYY") }}
                </td>
                <td>
                  <div
                    class="h-auto w-full flex flex-row items-center justify-evenly gap-2"
                  >
                    <button
                      class="bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover font-semibold h-8 w-8 flex items-center justify-center rounded-lg pl-[7.5px] pt-[1px] md:h-10 md:w-10 md:pl-[3px] md:pt-[2px]"
                      @click="toggleEditServiceCosts(item?.id)"
                    >
                      <SvgIcon icon="newReportIcon" classname="md:h-6 md:w-6" />
                    </button>
                    <button
                      class="bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover font-semibold h-8 w-8 flex items-center justify-center rounded-lg pl-[7.5px] pt-[1px] md:h-10 md:w-10 md:pl-[3px] md:pb-[3.75px]"
                      @click="deleteServiceCost(item?.id)"
                    >
                      <span class="text-danger">
                        <SvgIcon icon="trashIcon" classname="md:h-7 md:w-7"/>
                      </span>
                    </button>
                  </div>
                </td>
              </tr>
            </template>
          </tbody>
        </table>
      </div>

      <div class="h-auto w-full flex flex-col items-center my-5">
        <div class="font-semibold">
          {{
            `Showing ${(currentPage - 1) * 10 + 1} to ${
              costList?.length
            } of ${totalElements} entries`
          }}
        </div>
        <TablePagination
          v-if="pageCount >= 1"
          :total-pages="pageCount"
          :total="totalElements"
          :per-page="10"
          :current-page="currentPage"
          @page-change="pageChange"
        />
      </div>
    </div>
  </div>
  <CostsModal
    :isVisible="isModalVisible"
    :close="toggleNewServiceCosts"
    ref="costsModal"
    :loadPage="getCostSettings"
    :label="label"
    :type="type"
  />
</template>

<script lang="ts">
import TablePagination from "@/components/common/TablePagination.vue";
import SvgIcon from "@/constants/SvgIcon.vue";
import TableHeader from "@/components/common/TableHeader.vue";
import { SortByEnum, SortDirectionEnum } from "@/constants/table";
import { formatDate } from "@/utils/date";
import { numberWithCommas } from "@/utils/numberFormatter";
import AlertService from "@/services/AlertService";
import JwtService, { isSystemAdmin } from "@/services/JwtService";
import { useCostSettingStore } from "@/stores/cost-setting";
import { defineComponent, onMounted, ref, watch, type Ref } from "vue";
import Filter from "@/components/filters/Filter.vue";
import CostsModal from "./CostsModal.vue";

export default defineComponent({
  name: "costs-page",
  components: {
    Filter,
    SvgIcon,
    TablePagination,
    CostsModal,
    TableHeader,
  },
  props: {
    type: {
      type: Number,
      required: true,
    },
    label: {
      type: String,
      required: true,
    },
  },
  setup(props) {
    const costSettingStore = useCostSettingStore();
    const costList = ref<any[]>([]);
    const pageCount = ref(0);
    const totalElements = ref(0);
    const currentPage = ref(1);
    const loading = ref(false);
    const search = ref<string>("");
    const isShowFilter = ref(false);
    const costsModal: Ref<any> = ref<typeof CostsModal | null>(null);
    const filterParams = ref<Filter.FilterForm>({});
    const isModalVisible = ref(false);
    const sortParams = ref<Filter.FilterForm>({
      sortDirection: SortDirectionEnum.ASC,
      sortBy: SortByEnum.Name,
    });

    const tableHeader: Table.ColumnHeader[] = [
      { label: "NAME", sortBy: SortByEnum.Name, class: "min-w-150px" },
      { label: "COMPANY", class: "min-w-150px", display: isSystemAdmin() },
      { label: "PRICE", sortBy: SortByEnum.Cost },
      {
        label: "CREATED DATE",
        class: "min-w-150px",
        sortBy: SortByEnum.CreatedAt,
      },
      { label: "ACTIONS" },
    ];

    onMounted(() => {
      getCostSettings();
    });

    watch(currentPage, () => {
      getCostSettings();
    });

    const getCostSettings = async (): Promise<void> => {
      loading.value = true;

      costSettingStore.getCostSettings({
        params: {
          companyId: isSystemAdmin()
            ? null
            : JwtService?.getUserInfo()?.companyId,
          keyword: search.value.trim(),
          type: props.type,
          page: currentPage.value,
          limit: 10,
          ...sortParams.value,
          ...filterParams.value,
        },
        callback: {
          onSuccess: (res: any) => {
            costList.value = res?.items;
            pageCount.value = res?.totalPage;
            totalElements.value = res?.total;
            currentPage.value = res?.page;
          },
          onFinish: (_err: any) => {
            loading.value = false;
          },
        },
      });
    };

    const onSort = (newSortValue: Filter.FilterForm) => {
      sortParams.value = { ...newSortValue };
      getCostSettings();
    };

    const pageChange = (newPage: number) => {
      currentPage.value = newPage;
    };

    const toggleNewServiceCosts = (): void => {
      isModalVisible.value = !isModalVisible.value;
    };

    const toggleEditServiceCosts = (id: string): void => {
      costsModal?.value?.setId(id);
      isModalVisible.value = !isModalVisible.value;
    };

    const deleteServiceCost = (id: string) => {
      AlertService.deletionAlert({
        onConfirmed: () => {
          deleteServiceCostById(id);
        },
      });
    };

    const deleteServiceCostById = async (id: string): Promise<void> => {
      loading.value = true;

      costSettingStore.deleteCostSetting({
        id: id,
        callback: {
          onSuccess: (_res: any) => {
            getCostSettings();
          },
          onFinish: (_err: any) => {
            loading.value = false;
          },
        },
      });
    };

    const searchCostSettings = () => {
      if (currentPage.value !== 1) {
        currentPage.value = 1;
      } else {
        getCostSettings();
      }
    };

    const toggleFilter = (value: boolean) => {
      isShowFilter.value = value;
    };

    const onFilter = (filterValues: Filter.FilterForm) => {
      filterParams.value = { ...filterValues };
      if (currentPage.value !== 1) {
        currentPage.value = 1;
      } else {
        getCostSettings();
      }
    };

    return {
      onSort,
      onFilter,
      toggleFilter,
      searchCostSettings,
      getCostSettings,
      deleteServiceCost,
      pageChange,
      numberWithCommas,
      formatDate,
      toggleNewServiceCosts,
      toggleEditServiceCosts,
      isSystemAdmin,
      search,
      loading,
      costList,
      pageCount,
      currentPage,
      totalElements,
      isShowFilter,
      costsModal,
      sortParams,
      tableHeader,
      isModalVisible,
    };
  },
});
</script>
