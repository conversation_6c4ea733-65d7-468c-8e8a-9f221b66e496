import{a as s}from"./table-bhK9qpe4.js";import{d as k,_ as B,c as r,o as i,k as g,F as a,l as D,n as b,s as l,a as u,B as y,t as m}from"./index-CGNRhvz7.js";const _=k({name:"table-header",components:{},props:{headers:{type:Object,require:!0},sortBy:{type:String,require:!0},sortDirection:{type:String,require:!0},onSort:{type:Function,require:!0}},setup(t){return{getArrowPath:o=>t.sortBy===o?t.sortDirection===s.ASC?"/media/icons/arrows/order-by-asc.svg":"/media/icons/arrows/order-by-des.svg":"/media/icons/arrows/order-by-base.svg",onClickSort:o=>{var c;let n={};(t==null?void 0:t.sortBy)!==o?n={sortBy:o,sortDirection:s.ASC}:n={sortBy:t==null?void 0:t.sortBy,sortDirection:(t==null?void 0:t.sortDirection)===s.DESC?s.ASC:s.DESC},(c=t==null?void 0:t.onSort)==null||c.call(t,n)}}}}),d=["onClick"],w={class:"sort-icon"},A=["src","alt"];function $(t,S,f,o,n,c){return i(!0),r(a,null,g(t.headers,(e,C)=>(i(),r(a,{key:C},[(e==null?void 0:e.display)!==!1?(i(),r("th",{key:0,class:b(e==null?void 0:e.class)},[e!=null&&e.sortBy?(i(),r("div",{key:0,class:"flex flex-row justify-center items-center",onClick:()=>t.onClickSort(e.sortBy)},[l(t.$slots,"customHeader",{header:e},()=>[y(m(e.label),1)]),u("span",w,[u("img",{src:t.getArrowPath(e.sortBy),alt:t.sortDirection},null,8,A)])],8,d)):l(t.$slots,"customHeader",{key:1,header:e},()=>[y(m(e.label),1)])],2)):D("",!0)],64))),128)}const E=B(_,[["render",$]]);export{E as T};
