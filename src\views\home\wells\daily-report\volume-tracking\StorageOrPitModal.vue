<template>
  <div
    v-if="isVisible"
    class="fixed top-0 right-0 bottom-0 left-0 z-40 flex items-center"
  >
    <!-- Dark overlay background -->
    <div class="absolute inset-0 bg-sidebar-background opacity-25"></div>
    <div
      class="relative bg-card-background text-card-text-light max-h-11/12 w-11/12 flex flex-col items-center gap-3 mx-auto my-6 p-5 rounded-xl overflow-y-scroll md:overflow-hidden lg:w-2/5 lg:h-auto"
    >
      <div class="flex flex-row h-auto w-full items-center justify-between">
        <h3 class="text-lg font-bold">
          {{ `${id ? "Edit Storage or Pit" : "Add Storage or Pit"}` }}
        </h3>
        <span class="cursor-pointer ms-auto" @click="closeModal">
          <SvgIcon icon="closeModalIcon" />
        </span>
      </div>

      <el-form
        id="storage_or_pit_form"
        @submit.prevent="submit"
        :model="targetData"
        ref="formRef"
        class="h-auto w-full"
      >
        <div class="flex flex-col gap-3 md:flex-row">
          <div class="h-auto w-full flex flex-col gap-3">
            <div class="flex flex-col gap-1">
              <label class="font-bold">Name </label>
              <el-form-item
                prop="name"
                :rules="{
                  required: true,
                  message: 'Please type Name',
                  trigger: 'blur',
                }"
              >
                <el-input
                  v-model="targetData.name"
                  placeholder=""
                  name="name"
                ></el-input>
              </el-form-item>
            </div>
            <div class="flex flex-col gap-1">
              <label class="font-bold">Storage Type </label>
              <el-form-item prop="storageType">
                <el-input
                  v-model="targetData.storageType"
                  placeholder=""
                  name="storageType"
                ></el-input>
              </el-form-item>
            </div>
            <div class="flex flex-col gap-1">
              <label class="font-bold">Status </label>
              <el-form-item prop="status">
                <el-select
                  v-model="targetData.status"
                  placeholder="Select Status"
                  clearable
                >
                  <el-option
                    v-for="item in volumeTrackingStatusOptions"
                    :key="item?.value"
                    :label="item?.label"
                    :value="item?.value"
                    name="status"
                /></el-select>
              </el-form-item>
            </div>
          </div>
          <div class="h-auto w-full flex flex-col gap-3">
            <div class="flex flex-col gap-1">
              <label class="font-bold">Measured Volume (bbl) </label>
              <el-form-item prop="measuredVolume">
                <el-input
                  type="number"
                  :controls="false"
                  step="any"
                  v-model="targetData.measuredVolume"
                  placeholder=""
                  name="measuredVolume"
                ></el-input>
              </el-form-item>
            </div>
            <div class="flex flex-col gap-1">
              <label class="font-bold">Mud Weight (ppg) </label>
              <el-form-item prop="mudWeight">
                <el-input
                  type="number"
                  :controls="false"
                  step="any"
                  v-model="targetData.mudWeight"
                  placeholder=""
                  name="mudWeight"
                ></el-input>
              </el-form-item>
            </div>
            <div class="flex flex-col gap-1">
              <label class="font-bold">Mud Type </label>
              <el-form-item prop="mudType">
                <el-input
                  type="number"
                  :controls="false"
                  step="any"
                  v-model="targetData.mudType"
                  placeholder=""
                  name="mudType"
                ></el-input>
              </el-form-item>
            </div>
          </div>
        </div>

        <div class="h-auto w-full flex flex-row items-center gap-2">
          <button
            type="button"
            class="bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover rounded-md px-4 py-2 font-semibold"
            @click="closeModal"
            :disabled="loading"
          >
            Discard
          </button>
          <button
            class="bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover rounded-md px-4 py-2 font-semibold"
            type="submit"
            :disabled="loading"
          >
            <span v-if="!loading" class="indicator-label"> Save </span>
            <span v-if="loading" class="indicator-progress">
              Please wait...
              <span
                class="spinner-border spinner-border-sm align-middle ms-2"
              ></span>
            </span>
          </button>
        </div>
      </el-form>
    </div>
  </div>
</template>

<script lang="ts">
import SvgIcon from "@/constants/SvgIcon.vue";
import { volumeTrackingStatusOptions } from "@/constants/volume-tracking";
import { useVolumeTrackingStore } from "@/stores/volume-tracking";
import { defineComponent, inject, ref, watch } from "vue";
import { useRoute } from "vue-router";
import type { Provide } from "@/types/injection-types";

export default defineComponent({
  name: "storage-or-pit-modal",
  components: { SvgIcon },
  props: {
    isVisible: {
      type: Boolean,
      required: true,
    },
    close: {
      type: Function,
      required: true,
    },
    loadPage: {
      type: Function,
      default: () => {},
      required: true,
    },
  },
  setup(props) {
    const route = useRoute();
    const volumeTrackingStore = useVolumeTrackingStore();
    const modal = ref(false);
    const initialForm = {
      name: "",
      storageType: "",
      status: null,
      measuredVolume: null,
      mudWeight: null,
      mudType: null,
    };
    const targetData = ref(JSON.parse(JSON.stringify(initialForm)));
    const formRef = ref<null | HTMLFormElement>(null);
    const loading = ref<boolean>(false);
    const type = ref<any>(null);
    const id = ref("");
    const dailyReportProvide = inject<Provide.DailyReport>("dailyReport");

    watch(id, (newValue) => {
      if (newValue !== "") {
        getVolumeTrackingDetails();
      }
    });

    watch(
      () => props.isVisible,
      (newValue) => {
        if (newValue === false) {
          id.value = "";
          reset();
          formRef?.value?.resetFields();
        }
      }
    );

    const getVolumeTrackingDetails = async (): Promise<void> => {
      volumeTrackingStore.getVolumeTrackingDetails({
        id: id.value,
        callback: {
          onSuccess: (res: any) => {
            targetData.value = { ...res };
          },
        },
      });
    };

    const updateVolumeTracking = async (param: any): Promise<void> => {
      loading.value = true;
      volumeTrackingStore.updateVolumeTracking({
        id: id.value,
        params: param,
        callback: {
          onSuccess: (_res: any) => {
            if (props?.loadPage) {
              props?.loadPage();
            }
            closeModal();
          },
          onFinish: (_err: any) => {
            loading.value = false;
          },
        },
      });
    };

    const createVolumeTracking = async (param: any): Promise<void> => {
      loading.value = true;
      volumeTrackingStore.createVolumeTracking({
        params: param,
        callback: {
          onSuccess: (_res: any) => {
            if (props?.loadPage) {
              props?.loadPage();
            }
            closeModal();
          },
          onFinish: (_err: any) => {
            loading.value = false;
          },
        },
      });
    };

    const closeModal = () => {
      props.close();
    };

    const setId = (storageId: string) => {
      id.value = storageId;
    };

    const submit = () => {
      if (!formRef.value) {
        return;
      }

      formRef.value.validate((valid: boolean) => {
        if (valid) {
          const param = {
            ...targetData?.value,
            status: Number(targetData?.value?.status),
            measuredVolume: Number(targetData?.value?.measuredVolume),
            mudWeight: Number(targetData?.value?.mudWeight),
            mudType: Number(targetData?.value?.mudType),
          };

          if (id?.value) {
            updateVolumeTracking({
              ...param,
              dailyReportId: dailyReportProvide?.getDailyReportId(),
            });
          } else {
            dailyReportProvide?.createDailyReport({
              wellId: route?.params?.id as string,
              callback: {
                onSuccess: (_res: string) => {
                  createVolumeTracking({
                    ...param,
                    dailyReportId: dailyReportProvide?.getDailyReportId(),
                  });
                },
              },
            });
          }
        }
      });
    };

    const reset = () => {
      targetData.value = JSON.parse(JSON.stringify(initialForm));
    };

    return {
      id,
      type,
      modal,
      loading,
      targetData,
      volumeTrackingStatusOptions,
      formRef,
      closeModal,
      submit,
      reset,
      setId,
    };
  },
});
</script>
