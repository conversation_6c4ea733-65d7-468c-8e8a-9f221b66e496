import Swal, {
  type SweetAlertIcon,
  type SweetAlertPosition,
} from "sweetalert2";

type ConfirmCallback = {
  onConfirmed?: () => void;
  onCanceled?: () => void;
};

export default class AlertService {
  static resultAlert(
    message: string,
    icon?: SweetAlertIcon,
    imageUrl?: string | null,
    imageHeight?: number | string
  ): void {
    Swal.fire({
      text: message,
      icon,
      buttonsStyling: false,
      focusConfirm: false,
      confirmButtonColor: "#3085d6",
      confirmButtonText: "Ok, got it!",
      customClass: {
        actions: "flex gap-4",
        confirmButton:
          "h-auto w-auto bg-button-primary hover:bg-button-alert-success-hover text-button-text-light hover:text-button-text-light-hover font-semibold h-8 w-8 rounded-lg px-3 py-3",
        cancelButton:
          "h-auto w-auto bg-button-primary hover:bg-button-alert-danger-hover text-button-text-light hover:text-button-text-light-hover font-semibold h-8 w-8 rounded-lg px-3 py-3",
      },
      imageUrl: imageUrl || undefined,
      imageHeight,
    });
  }

  static deletionAlert(callback: ConfirmCallback): void {
    Swal.fire({
      text: "Are you sure you want to delete?",
      icon: "warning",
      showCancelButton: true,
      buttonsStyling: false,
      confirmButtonText: "Yes, delete.",
      cancelButtonText: "No, cancel.",
      heightAuto: false,
      customClass: {
        actions: "flex gap-4",
        confirmButton:
          "h-auto w-auto bg-button-primary hover:bg-button-alert-success-hover text-button-text-light hover:text-button-text-light-hover font-semibold h-8 w-8 rounded-lg px-3 py-3",
        cancelButton:
          "h-auto w-auto bg-button-primary hover:bg-button-alert-danger-hover text-button-text-light hover:text-button-text-light-hover font-semibold h-8 w-8 rounded-lg px-3 py-3",
      },
    }).then(({ isConfirmed, dismiss }) => {
      if (isConfirmed) callback.onConfirmed?.();
      else if (dismiss === Swal.DismissReason.cancel) callback.onCanceled?.();
    });
  }

  static incompleteFormAlert(
    callback: ConfirmCallback | null = null,
    message = "You have unsaved changes. Are you sure you want to leave?",
    confirmButtonText = "Yes, leave."
  ): void {
    Swal.fire({
      text: message,
      icon: "warning",
      showCancelButton: true,
      buttonsStyling: false,
      confirmButtonText,
      cancelButtonText: "No, return.",
      customClass: {
        actions: "flex gap-4",
        confirmButton:
          "h-auto w-auto bg-button-primary hover:bg-button-alert-success-hover text-button-text-light hover:text-button-text-light-hover font-semibold h-8 w-8 rounded-lg px-3 py-3",
        cancelButton:
          "h-auto w-auto bg-button-primary hover:bg-button-alert-danger-hover text-button-text-light hover:text-button-text-light-hover font-semibold h-8 w-8 rounded-lg px-3 py-3",
      },
    }).then(({ isConfirmed, dismiss }) => {
      if (isConfirmed) callback?.onConfirmed?.();
      else if (dismiss === Swal.DismissReason.cancel) callback?.onCanceled?.();
    });
  }

  static alert(
    message: string,
    options: {
      confirmButtonText?: string;
      cancelButtonText?: string;
      confirmButtonClass?: string;
      cancelButtonClass?: string;
      callback?: ConfirmCallback;
    } = {},
    icon?: SweetAlertIcon
  ): void {
    Swal.fire({
      text: message,
      icon,
      buttonsStyling: false,
      showCancelButton: true,
      focusConfirm: true,
      confirmButtonText: options.confirmButtonText || "Confirm",
      cancelButtonText: options.cancelButtonText || "Cancel",
      customClass: {
        actions: "flex gap-4",
        confirmButton:
          "h-auto w-auto bg-button-primary hover:bg-button-alert-success-hover text-button-text-light hover:text-button-text-light-hover font-semibold h-8 w-8 rounded-lg px-3 py-3",
        cancelButton:
          "h-auto w-auto bg-button-primary hover:bg-button-alert-danger-hover text-button-text-light hover:text-button-text-light-hover font-semibold h-8 w-8 rounded-lg px-3 py-3",
      },
    }).then(({ isConfirmed, dismiss }) => {
      if (isConfirmed) options.callback?.onConfirmed?.();
      else if (dismiss === Swal.DismissReason.cancel)
        options.callback?.onCanceled?.();
    });
  }

  static toast(
    title: string,
    icon: SweetAlertIcon = "success",
    position: SweetAlertPosition = "top-end"
  ): void {
    const Toast = Swal.mixin({
      toast: true,
      position,
      showConfirmButton: false,
      timer: 3000,
      timerProgressBar: true,
      didOpen: (toast) => {
        toast.onmouseenter = Swal.stopTimer;
        toast.onmouseleave = Swal.resumeTimer;
        toast.addEventListener("click", () => Swal.close());
      },
    });

    Toast.fire({ icon, title });
  }
}
