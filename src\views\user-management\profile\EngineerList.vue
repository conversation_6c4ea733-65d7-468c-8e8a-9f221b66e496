<template>
  <div class="bg-card-background text-card-text h-auto w-11/12 mx-auto my-4 rounded-xl py-4 px-4">
    <h1 class="font-bold text-lg">Assigned Engineers</h1>
    <el-form @submit.prevent="searchTable">
      <div class="h-auto w-full flex flex-col align-items-center">
        <el-form-item class="mb-0">
          <el-input
            class="w-250px"
            placeholder="Search"
            v-model="search"
            name="search"
            size="large"
            ><template #prefix>
              <el-icon class="el-input__icon">
                <SvgIcon icon="searchIcon" />
              </el-icon> </template
          ></el-input>
        </el-form-item></div
    ></el-form>
    <div class="h-auto w-full flex flex-row justify-end gap-2">
      <button
        class="bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover rounded-md px-4 py-2 font-semibold flex flex-row items-center gap-2 justify-between"
        v-if="checkedRows.length !== 0"
        type="button"
        @click="onRemove"
      >
        Remove
      </button>
      <button
        v-if="showAddBtn()"
        class="bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover rounded-md px-4 py-2 font-semibold flex flex-row items-center gap-2 justify-between"
        type="button"
        @click="onClickAdd"
      >
        Add
      </button>
      <button
        class="bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover rounded-md px-4 py-2 font-semibold flex flex-row items-center gap-2 justify-between"
      >
        <SvgIcon :icon="'logoutIcon'"/>
        Export
      </button>
    </div>
    <!--end::Card header-->
    <div v-if="loading" class="text-center p-5">
      <div class="spinner-border text-primary" role="status">
        <span class="sr-only">Loading...</span>
      </div>
    </div>
    <el-empty
      v-else-if="assignedEngineerList.length === 0"
      description="No Data"
    />
    <div v-else>
      <div class="table-responsive mt-3 mx-8">
        <!--begin::Table-->
        <table
          class="table table-row-dashed table-row-gray-300 align-middle gs-0 gy-2"
        >
          <!--begin::Table head-->
          <thead>
            <tr class="fw-bold text-gray-400">
              <TableHeader
                :headers="tableHeader"
                :sortBy="sortParams.sortBy!"
                :sortDirection="sortParams.sortDirection!"
                :onSort="onSort"
                ><template v-slot:customHeader="{ header }">
                  <div v-if="header.label === ''">
                    <div
                      class="form-check form-check-sm form-check-custom form-check-solid"
                    >
                      <input
                        class="form-check-input"
                        type="checkbox"
                        v-model="checkAll"
                        @change="onToggleCheckAll"
                      />
                    </div>
                  </div>
                  <div v-else>
                    {{ header.label }}
                  </div>
                </template></TableHeader
              >
            </tr>
          </thead>
          <!--end::Table head-->

          <!--begin::Table body-->
          <tbody>
            <template v-for="item in assignedEngineerList" :key="item.id">
              <tr>
                <td v-if="isAdmin()">
                  <div
                    class="form-check form-check-sm form-check-custom form-check-solid"
                  >
                    <input
                      class="form-check-input widget-9-check"
                      type="checkbox"
                      :value="item.id"
                      v-model="checkedRows"
                    />
                  </div>
                </td>

                <td>
                  <div class="d-flex align-items-center">
                    <div class="symbol symbol-45px symbol-circle me-5">
                      <img
                        :src="item?.avatar || '/media/avatars/blank.png'"
                        alt=""
                      />
                    </div>
                    <div class="d-flex justify-content-start flex-column">
                      <router-link
                        :to="`/users/${item?.id}`"
                        class="text-dark fw-bold text-hover-primary fs-6"
                        >{{
                          `${item?.firstName} ${item?.lastName}`
                        }}</router-link
                      >
                      <span class="text-gray-400 fw-semibold d-block fs-5">{{
                        item?.email
                      }}</span>
                    </div>
                  </div>
                </td>

                <td>
                  <span class="text-gray-600 fw-semibold d-block fs-5">{{
                    item?.mobilePhone
                  }}</span>
                </td>
                <td>
                  <span class="text-gray-600 fw-semibold d-block fs-5">{{
                    item?.officePhone
                  }}</span>
                </td>
                <td>
                  <span class="text-gray-600 fw-semibold d-block fs-5">{{
                    formatDate(item?.assignedDate, "MMM DD, YYYY")
                  }}</span>
                </td>

                <td v-if="isAdmin()">
                  <div class="d-flex align-items-center">
                    <button
                      class="btn btn-icon btn-sm btn-blue me-3"
                      @click="() => view(item?.id ?? '')"
                    >
                      <span class="svg-icon svg-icon-3">
                        <SvgIcon icon="eyeIcon" />
                      </span>
                    </button>
                    <button
                      class="btn btn-icon btn-sm btn-blue me-3"
                      @click="deleteEngineer(item?.id!)"
                    >
                      <span class="svg-icon svg-icon-3 text-danger">
                        <SvgIcon icon="trashIcon" />
                      </span>
                    </button>
                  </div>
                </td>
              </tr>
            </template>
          </tbody>
          <!--end::Table body-->
        </table>
        <!--end::Table-->
      </div>
      <div class="d-flex flex-wrap align-items-center mb-5 mx-8">
        <div
          v-if="assignedEngineerList?.length"
          class="text-gray-700 fw-semibold fs-6 me-auto"
        >
          {{
            `Showing ${(currentPage - 1) * 10 + 1} to ${
              assignedEngineerList?.length
            } of ${totalElements} entries`
          }}
        </div>
        <TablePagination
          v-if="pageCount >= 1"
          :total-pages="pageCount"
          :total="totalElements"
          :per-page="10"
          :current-page="currentPage"
          @page-change="pageChange"
        />
      </div>
    </div>
  </div>
  <AssignUserModal
    ref="assignEngineersModal"
    title="Assign Engineers"
    :userList="userListForAssignUserModal"
    :onAdd="onAdd"
    :onSearch="onSearch"
    :loadingSearch="loadingUserListForAssignUserModal"
    :initialAddUserValue="initialAddUserValue"
    :isVisible="isModalVisible"
    :close="toggleModal"
  />
</template>

<script lang="ts">
import AssignUserModal from "@/components/AssignUserModal.vue";
import TablePagination from "@/components/common/TablePagination.vue";
import SvgIcon from "@/constants/SvgIcon.vue";
import TableHeader from "@/components/common/TableHeader.vue";
import { SortByEnum, SortDirectionEnum } from "@/constants/table";
import { UserStatus, UserType } from "@/constants/user";
import { formatDate } from "@/utils/date";
import AlertService from "@/services/AlertService";
import JwtService, { isAdmin } from "@/services/JwtService";
import { useUserStore } from "@/stores/user";
import {
  defineComponent,
  onMounted,
  type PropType,
  ref,
  type Ref,
  watch,
} from "vue";
import { useRoute, useRouter } from "vue-router";

export default defineComponent({
  name: "user-info-engineer",
  components: {
    SvgIcon,
    TablePagination,
    AssignUserModal,
    TableHeader,
  },
  props: {
    userDetail: {
      type: Object as PropType<User.Info>,
      required: true,
    },
  },
  setup(props) {
    const userStore = useUserStore();
    const route = useRoute();
    const router = useRouter();
    const checkedRows = ref<Array<string>>([]);
    const checkAll = ref<boolean>(false);
    const pageCount = ref(0);
    const totalElements = ref(0);
    const currentPage = ref(1);
    const loading = ref(false);
    const loadingUserListForAssignUserModal = ref(false);
    const search = ref<string>("");
    const assignedEngineerList = ref<User.Info[]>([]);
    const userListForAssignUserModal = ref<User.Info[]>([]);
    const assignEngineersModal: Ref<any> = ref<typeof AssignUserModal | null>(
      null
    );
    const isModalVisible = ref(false);

    const sortParams = ref<Company.GetFilter>({
      sortDirection: SortDirectionEnum.ASC,
      sortBy: SortByEnum.Name,
    });

    const tableHeader: Table.ColumnHeader[] = [
      { label: "", class: "w-25px", display: isAdmin() },
      {
        label: "FULL NAME",
        sortBy: SortByEnum.Name,
        class: "min-w-150px",
      },
      { label: "MOBILE NUMBER", class: "min-w-120px" },
      { label: "OFFICE NUMBER", class: "min-w-120px" },
      { label: "ADDED DATE", class: "min-w-120px" },
      { label: "ACTIONS", class: "min-w-60px", display: isAdmin() },
    ];

    onMounted(() => {
      getAssignedEngineerList();
      getUserListForAssignUserModal();
    });

    watch(currentPage, () => {
      getAssignedEngineerList();
    });

    const toggleModal = (): void => {
      isModalVisible.value = !isModalVisible.value;
    };

    const getAssignedEngineerList = async (): Promise<void> => {
      loading.value = true;

      userStore.getAssignedEngineers({
        params: {
          supervisorId: props?.userDetail?.id!,
          name: search.value.trim() || null,
          page: currentPage.value,
          limit: 10,
          ...sortParams.value,
        },
        callback: {
          onSuccess: (res: any) => {
            assignedEngineerList.value = [...res?.items];
            pageCount.value = res?.totalPage;
            totalElements.value = res?.total;
            currentPage.value = res?.page;
          },
          onFinish: () => {
            loading.value = false;
          },
        },
      });
    };

    const onSort = (newSortValue: Filter.FilterForm) => {
      sortParams.value = { ...newSortValue };

      getAssignedEngineerList();
    };

    const getUserListForAssignUserModal = async (
      name: string = ""
    ): Promise<void> => {
      loadingUserListForAssignUserModal.value = true;
      userStore.getUsers({
        params: {
          role: UserType.Engineer,
          status: UserStatus.Active,
          companyId: props.userDetail?.companyId,
          sortBy: "email",
          name: name.trim() || null,
          page: currentPage.value,
          limit: 500,
        },
        callback: {
          onSuccess: (res: any) => {
            userListForAssignUserModal.value = [...res?.items];
          },
          onFinish: () => {
            loadingUserListForAssignUserModal.value = false;
          },
        },
      });
    };

    const deleteEngineer = (id: string) => {
      AlertService.deletionAlert({
        onConfirmed: () => {
          removeEngineers([id]);
        },
      });
    };

    const pageChange = (newPage: number) => {
      currentPage.value = newPage;
    };

    watch(checkedRows, (newValue) => {
      checkAll.value = newValue.length === assignedEngineerList.value.length;
    });

    const onToggleCheckAll = (e: any) => {
      if (e?.target?.checked) {
        checkedRows.value = assignedEngineerList.value.map((user) => user.id!);
      } else {
        checkedRows.value = [];
      }
    };

    const view = (id: string) => {
      router.push({ path: `/users/${id}` });
    };

    const onAdd = (list: string[]) => {
      assignEngineers(list);
      assignEngineersModal?.value?.closeModal();
    };

    const onSearch = (query: string) => {
      getUserListForAssignUserModal(query);
    };

    const searchTable = () => {
      if (currentPage.value !== 1) {
        currentPage.value = 1;
      } else {
        getAssignedEngineerList();
      }
    };

    const onClickAdd = (): void => {
      assignEngineersModal?.value?.show();
    };

    const showAddBtn = () => {
      return (
        (JwtService.checkRole(UserType.SystemAdmin) ||
          JwtService.checkRole(UserType.CompanyAdmin)) &&
        props?.userDetail?.roles?.find(
          (item) => item?.value === UserType.Supervisor
        ) &&
        route.name !== "my-profile"
      );
    };

    const onRemove = () => {
      AlertService.deletionAlert({
        onConfirmed: () => {
          removeEngineers(checkedRows.value, true);
        },
      });
    };

    const removeEngineers = async (
      engineerIds: string[],
      clearRemoveList = false
    ): Promise<void> => {
      loading.value = true;

      userStore.removeEngineers({
        engineerIds,
        callback: {
          onSuccess: (_res: any) => {
            if (clearRemoveList) {
              checkedRows.value = [];
            }
            getAssignedEngineerList();
          },
          onFinish: () => {
            loading.value = false;
          },
        },
      });
    };

    const assignEngineers = async (engineerIds: string[]): Promise<void> => {
      loading.value = true;

      userStore.assignEngineers({
        params: {
          engineerIds,
          supervisorId: props.userDetail?.id!,
        },
        callback: {
          onSuccess: (_res: any) => {
            getAssignedEngineerList();
            AlertService.toast("Added successfully", "success", "top-right");
          },
          onFinish: () => {
            loading.value = false;
          },
        },
      });
    };

    return {
      sortParams,
      tableHeader,

      userListForAssignUserModal,
      search,
      loading,
      assignedEngineerList,
      checkedRows,
      checkAll,
      currentPage,
      totalElements,
      pageCount,
      assignEngineersModal,
      loadingUserListForAssignUserModal,
      initialAddUserValue: {
        userRoles: [UserType.Engineer],
        companyId: props?.userDetail?.company?.id,
      },
      isModalVisible,
      showAddBtn,
      pageChange,
      deleteEngineer,
      onSearch,
      onAdd,
      view,
      onClickAdd,
      isAdmin,
      onToggleCheckAll,
      searchTable,
      onRemove,
      formatDate,
      onSort,
      toggleModal,
    };
  },
});
</script>
