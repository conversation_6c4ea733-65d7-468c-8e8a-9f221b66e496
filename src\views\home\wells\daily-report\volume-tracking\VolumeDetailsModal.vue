<template>
  <div
    v-if="isVisible"
    class="fixed top-0 right-0 bottom-0 left-0 z-40 flex items-center"
  >
    <!-- Dark overlay background -->
    <div class="absolute inset-0 bg-sidebar-background opacity-25"></div>
    <div
      v-if="loading"
      class="rounded-3 shadow-sm border-top border-top-2 border-primary d-flex flex-column gap-6 pt-10 card-body overflow-x-scroll md:overflow-hidden"
    >
      <div class="text-center">
        <div class="spinner-border text-primary" role="status">
          <span class="sr-only">Loading...</span>
        </div>
      </div>
    </div>
    <div
      v-else
      class="relative bg-card-background text-card-text-light max-h-11/12 w-11/12 flex flex-col items-center gap-3 mx-auto my-6 p-5 rounded-xl overflow-y-scroll font-semibold md:w-4/5 md:overflow-hidden lg:w-2/5 lg:h-auto"
    >
      <h5 class="font-bold">{{ targetData?.name }}</h5>
      <div class="h-auto w-full flex items-center justify-between">
        <span
          class="rounded-lg px-2 py-1"
          :class="targetData?.status === 1 ? 'bg-success' : 'bg-danger'"
          >{{ targetData?.status === 1 ? "Active" : "Inactive" }}</span
        >
        <span class="cursor-pointer" @click="closeModal">
          <SvgIcon icon="closeModalIcon" />
        </span>
      </div>
      <div class="h-auto w-full flex flex-col gap-3 md:flex-row md:gap-4">
        <div class="h-auto w-full flex flex-col gap-3">
          <div class="h-auto w-full flex justify-between">
            <span>Type</span>
            <span> Storage Type </span>
          </div>
          <div class="h-auto w-full flex justify-between">
            <span>Total Additions</span>
            <span>
              {{ numberWithCommas(targetData?.totalAdditions || 0) }}
            </span>
          </div>
          <div class="h-auto w-full flex justify-between">
            <span>Total Losses</span>
            <span>
              {{ numberWithCommas(targetData?.totalLosses || 0) }}
            </span>
          </div>
          <div class="h-auto w-full flex justify-between">
            <span>Total Transfers</span>
            <span>
              {{ numberWithCommas(targetData?.totalTransfers || 0) }}
            </span>
          </div>

          <div class="h-auto w-full flex flex-wrap gap-3 text-sm">
            <div class="border border-gray-300 border-dashed rounded p-3">
              <div>
                {{
                  `${numberWithCommas(targetData?.calculatedVolume || 0)} (bbl)`
                }}
              </div>
              <div class="fw-semibold fs-7 text-success">Calculated Volume</div>
            </div>
            <div class="border border-gray-300 border-dashed rounded p-3">
              <div>
                {{ `${numberWithCommas(targetData?.measuredVolume)} (bbl)` }}
              </div>
              <div class="fw-semibold fs-7 text-primary">Measured Volume</div>
            </div>
          </div>
        </div>
        <div class="h-auto w-full">
          <ul
            class="h-auto w-full flex flex-row flex-wrap items-center gap-2"
            role="tablist"
          >
            <li class="nav-item" v-for="item in tabs" :key="item?.value">
              <div
                class="whitespace-nowrap cursor-pointer font-semibold hover:text-active-hover hover:border-b-2 hover:border-active-border-hover"
                :class="{
                  active: tabIndex === item?.value.toString(),
                  'text-active border-b-2 border-active-border':
                    tabIndex === item?.value.toString(),
                  'text-inactive border-b-2 border-inactive-border':
                    tabIndex !== item?.value.toString(),
                }"
                data-bs-toggle="tab"
                @click="setActiveTab($event)"
                :data-tab-index="item?.value"
                role="tab"
              >
                {{ item?.key }}
              </div>
            </li>
          </ul>

          <div
            v-if="tabIndex == VolumeTracking.Addition.toString()"
            class="h-auto w-full overflow-x-scroll p-4 md:overflow-hidden"
          >
            <table>
              <tbody>
                <tr v-for="item in targetData?.additions" :key="item.id">
                  <td class="p-4 whitespace-nowrap">
                    {{ formatDate(item?.createdAt, "DD MMM YYYY") }}
                  </td>
                  <td class="p-4 whitespace-nowrap">
                    {{ item?.description }}
                  </td>
                  <td class="p-4 whitespace-nowrap">
                    {{ `${item?.volume} (bbl)` }}
                  </td>
                  <td>
                    <div class="flex items-center gap-3">
                      <button
                        class="bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover font-semibold h-8 w-8 rounded-lg px-1.5 py-0.5"
                        @click="
                          editVolumeModal(item?.id, VolumeTracking.Addition)
                        "
                      >
                        <span class="svg-icon svg-icon-3">
                          <SvgIcon icon="newReportIcon" />
                        </span>
                      </button>
                      <button
                        class="bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover font-semibold h-8 w-8 rounded-lg px-1.5 pb-1"
                        @click="deleteItem(item?.id)"
                      >
                        <span class="svg-icon svg-icon-3 text-danger">
                          <SvgIcon icon="trashIcon" />
                        </span>
                      </button>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
          <div
            v-if="tabIndex == VolumeTracking.Loss.toString()"
            class="h-auto w-full overflow-x-scroll p-4 md:overflow-hidden"
          >
            <table>
              <tbody>
                <tr v-for="item in targetData?.losses" :key="item.id">
                  <td class="p-4 whitespace-nowrap">
                    {{ formatDate(item?.createdAt, "DD MMM YYYY") }}
                  </td>
                  <td class="p-4 whitespace-nowrap">
                    {{ item?.description }}
                  </td>
                  <td class="p-4 whitespace-nowrap">
                    {{ `${item?.volume} (bbl)` }}
                  </td>
                  <td>
                    <div class="flex items-center gap-3">
                      <button
                        class="bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover font-semibold h-8 w-8 rounded-lg px-1.5 py-0.5"
                        @click="editVolumeModal(item?.id, VolumeTracking.Loss)"
                      >
                        <span class="svg-icon svg-icon-3">
                          <SvgIcon icon="newReportIcon" />
                        </span>
                      </button>
                      <button
                        class="bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover font-semibold h-8 w-8 rounded-lg px-1.5 py-0.5"
                        @click="deleteItem(item?.id)"
                      >
                        <span class="text-danger">
                          <SvgIcon icon="trashIcon" />
                        </span>
                      </button>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
          <div
            v-if="tabIndex == VolumeTracking.Transfer.toString()"
            class="h-auto w-full overflow-x-scroll p-4 md:overflow-hidden"
          >
            <table class="table">
              <tbody>
                <tr v-for="item in targetData?.transfers" :key="item.id">
                  <td class="p-4 whitespace-nowrap">
                    {{ formatDate(item?.createdAt, "DD MMM YYYY") }}
                  </td>
                  <td class="p-4 whitespace-nowrap">
                    {{ item?.description }}
                  </td>
                  <td class="p-4 whitespace-nowrap">
                    {{ `${item?.volume} (bbl)` }}
                  </td>
                  <td>
                    <div class="flex items-center gap-3">
                      <button
                        class="bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover font-semibold h-8 w-8 rounded-lg px-1.5 py-0.5"
                        @click="
                          editVolumeModal(item?.id, VolumeTracking.Transfer)
                        "
                      >
                        <SvgIcon icon="newReportIcon" />
                      </button>
                      <button
                        class="bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover font-semibold h-8 w-8 rounded-lg px-1.5 py-0.5"
                        @click="deleteItem(item?.id)"
                      >
                        <span class="text-danger">
                          <SvgIcon icon="trashIcon" />
                        </span>
                      </button>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import SvgIcon from "@/constants/SvgIcon.vue";
import { VolumeTracking } from "@/constants/volume-tracking";
import { formatDate } from "@/utils/date";
import { numberWithCommas } from "@/utils/numberFormatter";
import AlertService from "@/services/AlertService";
import { useVolumeTrackingStore } from "@/stores/volume-tracking";
import { useVolumeTrackingItemStore } from "@/stores/volume-tracking-item";
import { defineComponent, ref, watch } from "vue";

const tabs = [
  { value: VolumeTracking.Addition, key: "Additions" },
  { value: VolumeTracking.Loss, key: "Losses" },
  { value: VolumeTracking.Transfer, key: "Transfers" },
];

export default defineComponent({
  name: "volume-details-modal",
  components: { SvgIcon },
  props: {
    isVisible: {
      type: Boolean,
      required: true,
    },
    close: {
      type: Function,
      required: true,
    },
    loadPage: { type: Function, default: () => {}, required: true },
    editVolume: { type: Function, required: false },
  },
  setup(props) {
    const volumeTrackingStore = useVolumeTrackingStore();
    const volumeItemStore = useVolumeTrackingItemStore();
    const loading = ref(false);
    const targetData = ref<any>();
    const tabIndex = ref<string>(tabs[0].value.toString());
    const id = ref("");

    watch(id, (newValue) => {
      if (newValue !== "") {
        getVolumeTrackingDetails();
      }
    });

    const getVolumeTrackingDetails = async (): Promise<void> => {
      loading.value = true;
      volumeTrackingStore.getVolumeTrackingDetails({
        id: id.value,
        callback: {
          onSuccess: (res: any) => {
            res.additions = [];
            res.losses = [];
            res.transfers = [];
            for (const volumeItem of res?.volumeItems) {
              if (volumeItem?.type == VolumeTracking.Addition) {
                res.additions.push(volumeItem);
              } else if (volumeItem?.type == VolumeTracking.Loss) {
                res.losses.push(volumeItem);
              } else if (volumeItem?.type == VolumeTracking.Transfer) {
                res.transfers.push(volumeItem);
              }
            }
            targetData.value = JSON.parse(JSON.stringify(res));
          },
          onFinish: (_err: any) => {
            loading.value = false;
          },
        },
      });
    };

    const setId = (volumeId: string) => {
      id.value = volumeId;
    };

    const closeModal = () => {
      props.close();
    };

    const setActiveTab = (e: Event) => {
      const target = e.target as HTMLInputElement;

      tabIndex.value = target.getAttribute("data-tab-index") as string;
    };

    const deleteItem = (id: string) => {
      AlertService.deletionAlert({
        onConfirmed: () => {
          deleteItemById(id);
        },
      });
    };

    const deleteItemById = (id: string) => {
      volumeItemStore.deleteVolumeTrackingItem({
        id: id,
        callback: {
          onSuccess: (_res: any) => {
            getVolumeTrackingDetails();
            if (props?.loadPage) {
              props.loadPage();
            }
          },
        },
      });
    };

    const editVolumeModal = (itemId: string, type: VolumeTracking) => {
      if (props.editVolume) {
        props.editVolume(id?.value, type, itemId);
      }
    };

    return {
      loading,
      tabs,
      tabIndex,
      targetData,
      VolumeTracking,
      closeModal,
      setId,
      deleteItem,
      numberWithCommas,
      setActiveTab,
      formatDate,
      editVolumeModal,
    };
  },
});
</script>
