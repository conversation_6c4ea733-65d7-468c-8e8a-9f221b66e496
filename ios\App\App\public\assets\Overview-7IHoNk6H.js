import{P as te}from"./PageHeader-3hadTn26.js";import{O as ue,P as g,Q as A,d as q,q as p,j as z,A as U,Z as me,_ as Q,c as b,l as V,o as h,a as t,b as d,t as N,r as y,w as k,B as se,p as ae,a0 as H,x as ne,E as J,e as le,m as K,H as X,M as ee,F as j,k as pe}from"./index-CGNRhvz7.js";import{u as fe}from"./customer-C9SausZF.js";import{C as ge}from"./CustomerModal-B1iI3o6f.js";import{T as he}from"./TablePagination-BmVxunEG.js";import{S as re}from"./SvgIcon-CMhyaXWN.js";import{T as ve}from"./TableHeader-C1CWTWQa.js";import{S as oe,a as be}from"./table-bhK9qpe4.js";import{f as ye}from"./date-CvSHk5ED.js";import{h as R}from"./handleFailure-DtTpu7r3.js";import{y as L}from"./validator-6laVLK0J.js";import{c as Ce}from"./index.esm-DXW765zG.js";import"./d-left-arrow-079-B3YbfCzd.js";const ie=ue("customerContact",()=>({getCustomerContacts:async({params:m,callback:n})=>{var u;const a=g.get(n,"onSuccess",g.noop),r=g.get(n,"onFinish",g.noop);try{A.setHeader();const i=await A.getWithParams("customersContact",m);a(((u=i.data)==null?void 0:u.data)||i.data)}catch(i){R(i,n)}finally{r()}},getContactDetails:async({id:m,callback:n})=>{var u;const a=g.get(n,"onSuccess",g.noop),r=g.get(n,"onFinish",g.noop);try{A.setHeader();const i=await A.get(`customersContact/${m}`);a(((u=i.data)==null?void 0:u.data)||i.data)}catch(i){R(i,n)}finally{r()}},updateCustomerContact:async({id:m,params:n,callback:a})=>{var i;const r=g.get(a,"onSuccess",g.noop),u=g.get(a,"onFinish",g.noop);try{A.setHeader();const l=await A.put(`customersContact/${m}`,n);r(((i=l.data)==null?void 0:i.data)||l.data)}catch(l){R(l,a)}finally{u()}},removeContact:async({ids:m,callback:n})=>{var u;const a=g.get(n,"onSuccess",g.noop),r=g.get(n,"onFinish",g.noop);try{A.setHeader();const i=await A.post("customersContact/delete",{ids:m});a(((u=i.data)==null?void 0:u.data)||i.data)}catch(i){R(i,n)}finally{r()}},createContact:async({params:m,callback:n})=>{var u;const a=g.get(n,"onSuccess",g.noop),r=g.get(n,"onFinish",g.noop);try{A.setHeader();const i=await A.post("customersContact",m);a(((u=i.data)==null?void 0:u.data)||i.data)}catch(i){R(i,n)}finally{r()}}})),we=q({name:"customer-modal",components:{SvgIcon:re},props:{isVisible:{type:Boolean,required:!0},close:{type:Function,required:!0},loadPage:{type:Function,required:!1}},setup(e){var O,T;const o=z(),x=p(!1),C=ie(),S={customerId:"",name:"",emailAddress:"",mobilePhone:"",officePhone:"",address:"",notes:"",primaryContact:!1,notifyOnNewReport:!1},m=p({...S}),n=p(null),a=p(!1),r=p(""),u=((T=(O=o.params)==null?void 0:O.customerId)==null?void 0:T.toString())||"";U(x,v=>{v===!1?$():i()});const i=async()=>{r.value&&(a.value=!0,C.getContactDetails({id:r.value,callback:{onSuccess:v=>{m.value={...v}},onFinish:()=>{a.value=!1}}}))},l=()=>{e.close()},_=v=>{r.value=v.toString()},w=Ce({emailAddress:L.emailAddress,mobilePhone:L.mobilePhone,officePhone:L.officePhone}),E=p({name:[{required:!0,message:"Please type Contact Name",trigger:["blur","change"]}],emailAddress:[{required:!0,validator:(v,P,f)=>{w.fields.emailAddress.validate(P).then(()=>{f()}).catch(D=>{f(new Error(D.errors[0]))})},trigger:["blur","change"]}],mobilePhone:[{required:!0,validator:(v,P,f)=>{w.fields.mobilePhone.validate(P).then(()=>{f()}).catch(D=>{f(new Error(D.errors[0]))})},trigger:["blur","change"]}],officePhone:[{validator:(v,P,f)=>{P?w.fields.officePhone.validate(P).then(()=>{f()}).catch(D=>{f(new Error(D.errors[0]))}):f()},trigger:["blur","change"]}]}),F=()=>{n.value&&n.value.validate(v=>{var P,f,D;if(v){const B={...m.value,officePhone:((P=m.value)==null?void 0:P.officePhone)||null,address:((f=m.value)==null?void 0:f.address)||null,note:((D=m.value)==null?void 0:D.notes)||null,customerId:u};r.value?s(B):M(B)}})},M=async v=>{a.value=!0,C.createContact({params:v,callback:{onSuccess:P=>{var f;e.close(),(f=e==null?void 0:e.loadPage)==null||f.call(e)},onFinish:()=>{a.value=!1}}})},s=async v=>{r.value&&(a.value=!0,C.updateCustomerContact({id:r.value,params:v,callback:{onSuccess:P=>{var f;e.close(),(f=e==null?void 0:e.loadPage)==null||f.call(e)},onFinish:()=>{a.value=!1}}}))},$=()=>{var v;r.value="",m.value={...S},(v=n==null?void 0:n.value)==null||v.resetFields()};return{id:r,modal:x,rules:E,loading:a,targetData:m,formRef:n,rolesOptions:me,submit:F,setId:_,reset:$,closeModal:l}}}),_e={key:0,class:"fixed top-0 right-0 bottom-0 left-0 flex flex-row items-center bg-dark z-40"},ke={class:"bg-white h-auto w-11/12 flex flex-col items-center gap-3 mx-auto p-5 rounded-xl"},Pe={class:"h-auto w-full flex flex-row items-center justify-between"},Se={class:"modal-title"},$e={class:"flex flex-col gap-2"},Ve={class:"flex flex-col gap-2"},xe={class:"flex flex-col gap-2"},De={class:"flex flex-col gap-2"},Ae={class:"flex flex-col gap-2"},Ne={class:"flex flex-col gap-2"},Ee={class:"flex flex-row items-center justify-between"},Fe={class:"flex flex-row items-center justify-between"},Ie={class:"flex flex-row items-start mt-4 gap-3"},Me=["disabled"],Re=["disabled"],Oe={key:0,class:"indicator-label"},Te={key:1,class:"indicator-progress"};function Be(e,o,x,C,S,m){const n=y("SvgIcon"),a=y("el-input"),r=y("el-form-item"),u=y("el-checkbox"),i=y("el-form");return e.isVisible?(h(),b("div",_e,[t("div",ke,[t("div",Pe,[t("h3",Se,N(e.id?"Edit Contact":"New Contact"),1),t("span",{class:"cursor-pointer",onClick:o[0]||(o[0]=(...l)=>e.closeModal&&e.closeModal(...l))},[d(n,{icon:"closeModalIcon"})])]),d(i,{id:"product_form",onSubmit:ae(e.submit,["prevent"]),model:e.targetData,rules:e.rules,ref:"formRef",class:"h-auto w-full"},{default:k(()=>[t("div",$e,[o[10]||(o[10]=t("label",{class:"font-semibold"},"Name ",-1)),d(r,{prop:"name",class:"mt-auto"},{default:k(()=>[d(a,{modelValue:e.targetData.name,"onUpdate:modelValue":o[1]||(o[1]=l=>e.targetData.name=l),placeholder:"",name:"name"},null,8,["modelValue"])]),_:1})]),t("div",Ve,[o[11]||(o[11]=t("label",{class:"font-semibold"},"Email Address ",-1)),d(r,{prop:"emailAddress",class:"mt-auto"},{default:k(()=>[d(a,{modelValue:e.targetData.emailAddress,"onUpdate:modelValue":o[2]||(o[2]=l=>e.targetData.emailAddress=l),placeholder:"",name:"emailAddress"},null,8,["modelValue"])]),_:1})]),t("div",xe,[o[12]||(o[12]=t("label",{class:"font-semibold"},"Mobile Phone ",-1)),d(r,{prop:"mobilePhone",class:"mt-auto"},{default:k(()=>[d(a,{modelValue:e.targetData.mobilePhone,"onUpdate:modelValue":o[3]||(o[3]=l=>e.targetData.mobilePhone=l),placeholder:"",name:"mobilePhone"},null,8,["modelValue"])]),_:1})]),t("div",De,[o[13]||(o[13]=t("label",{class:"font-semibold"},"Office Phone ",-1)),d(r,{prop:"officePhone",class:"mt-auto"},{default:k(()=>[d(a,{class:"w-100",modelValue:e.targetData.officePhone,"onUpdate:modelValue":o[4]||(o[4]=l=>e.targetData.officePhone=l),placeholder:"",name:"officePhone"},null,8,["modelValue"])]),_:1})]),t("div",Ae,[o[14]||(o[14]=t("label",{class:"font-semibold"},"Address ",-1)),d(r,{prop:"address",class:"mt-auto"},{default:k(()=>[d(a,{class:"w-100",modelValue:e.targetData.address,"onUpdate:modelValue":o[5]||(o[5]=l=>e.targetData.address=l),placeholder:"",name:"address"},null,8,["modelValue"])]),_:1})]),t("div",Ne,[o[15]||(o[15]=t("label",{class:"font-semibold"},"Note ",-1)),d(r,{prop:"notes",class:"mt-auto"},{default:k(()=>[d(a,{class:"h-auto w-full",modelValue:e.targetData.notes,"onUpdate:modelValue":o[6]||(o[6]=l=>e.targetData.notes=l),placeholder:"",name:"notes",type:"textarea",rows:3},null,8,["modelValue"])]),_:1})]),t("div",Ee,[o[16]||(o[16]=t("label",{class:"font-semibold"},"Primary Contact? ",-1)),d(r,{prop:"primaryContact",style:{"margin-bottom":"0"}},{default:k(()=>[d(u,{modelValue:e.targetData.primaryContact,"onUpdate:modelValue":o[7]||(o[7]=l=>e.targetData.primaryContact=l),name:"primaryContact"},null,8,["modelValue"])]),_:1})]),t("div",Fe,[o[17]||(o[17]=t("label",{class:"font-semibold"},"Notify on new report ",-1)),d(r,{prop:"notifyOnNewReport",style:{"margin-bottom":"0"}},{default:k(()=>[d(u,{modelValue:e.targetData.notifyOnNewReport,"onUpdate:modelValue":o[8]||(o[8]=l=>e.targetData.notifyOnNewReport=l),name:"notifyOnNewReport"},null,8,["modelValue"])]),_:1})]),t("div",Ie,[t("button",{type:"button",class:"bg-grey-400 hover:bg-grey-500 px-4 py-2 text-gray-700 rounded-md",onClick:o[9]||(o[9]=(...l)=>e.closeModal&&e.closeModal(...l)),disabled:e.loading}," Discard ",8,Me),t("button",{class:"bg-primary px-4 py-2 text-white rounded-md",type:"submit",disabled:e.loading},[e.loading?V("",!0):(h(),b("span",Oe," Save ")),e.loading?(h(),b("span",Te,o[18]||(o[18]=[se(" Please wait... "),t("span",{class:"spinner-border spinner-border-sm align-middle ms-2"},null,-1)]))):V("",!0)],8,Re)])]),_:1},8,["onSubmit","model","rules"])])])):V("",!0)}const He=Q(we,[["render",Be]]),Le=q({name:"customer-list",components:{PageHeader:te,SvgIcon:re,TablePagination:he,ContactModal:He,TableHeader:ve},setup(){var Y,Z;const e=ie(),o=z(),x=le(),C=p([]),S=p(!1),m=p(0),n=p(0),a=p(1),r=p(!1),u=p(""),i=p(!1),l=p([]),_=p(null),w=((Z=(Y=o.params)==null?void 0:Y.customerId)==null?void 0:Z.toString())||"",E=p(!1),F=p({sortDirection:be.ASC,sortBy:oe.Name}),M=[{label:"",class:"w-25px",display:H()},{label:"FULL NAME",sortBy:oe.Name,class:"min-w-150px"},{label:"ADDRESS",class:"min-w-150px"},{label:"MOBILE NUMBER",class:"min-w-120px"},{label:"OFFICE NUMBER",class:"min-w-120px"},{label:"EMAIL",class:"min-w-150px"},{label:"PRIMARY CONTACT",class:"min-w-150px"},{label:"REPORT NOTIFICATION",class:"min-w-120px"},{label:"ACTIONS",class:"min-w-60px",display:H()}];ne(()=>{s()}),U(a,()=>{s()}),U(C,c=>{S.value=l.value.length!==0&&c.length===l.value.length});const s=async()=>{r.value=!0,e.getCustomerContacts({params:{customerId:w,name:u.value.trim()||null,page:a.value,limit:10,...F.value},callback:{onSuccess:c=>{l.value=[...c==null?void 0:c.items],m.value=c==null?void 0:c.totalPage,n.value=c==null?void 0:c.total,a.value=c==null?void 0:c.page},onFinish:()=>{r.value=!1}}})},$=c=>{F.value={...c},s()},O=()=>{a.value!==1?a.value=1:s()},T=()=>{E.value=!E.value},v=c=>{var I;(I=_==null?void 0:_.value)==null||I.setId(c),E.value=!E.value},P=()=>{J.deletionAlert({onConfirmed:()=>{W(C.value,!0)}})},f=c=>{J.deletionAlert({onConfirmed:()=>{W([c])}})},D=c=>{a.value=c},B=()=>{i.value=!i.value},de=c=>{var I;(I=c==null?void 0:c.target)!=null&&I.checked?C.value=l.value.map(G=>G.id):C.value=[]},ce=c=>{x.push({path:`/users/${c}`})},W=async(c,I=!1)=>{r.value=!0,e.removeContact({ids:c,callback:{onSuccess:G=>{I&&(C.value=[]),s()},onFinish:()=>{r.value=!1}}})};return{sortParams:F,tableHeader:M,search:u,loading:r,contactModal:_,checkedRows:C,checkAll:S,contactList:l,currentPage:a,totalElements:n,pageCount:m,showContactModal:i,isModalVisible:E,searchCustomerContacts:O,pageChange:D,toggleFilter:B,deleteEngineer:f,formatDate:ye,view:ce,onRemove:P,toggleNewCustomer:T,toggleEditCustomer:v,isAdmin:H,onToggleCheckAll:de,getCustomerContacts:s,onSort:$}}}),Ue={class:"bg-white rounded-lg p-4"},je={class:"h-auto w-full flex flex-col gap-2 items-start"},qe={class:"h-auto w-full"},ze={class:"d-flex align-items-center"},Qe={class:"svg-icon svg-icon-2"},We={class:"h-auto w-full flex flex-row justify-end gap-4"},Ye={key:0,class:"text-center p-5"},Ze={key:2,class:"h-auto w-full overflow-x-scroll p-4"},Ge={class:"font-bold text-gray-400 whitespace-nowrap"},Je={class:"font-bold text-gray-400"},Ke={key:0},Xe={class:"form-check form-check-sm form-check-custom form-check-solid"},eo={key:1,class:"p-4"},oo={key:0},to={class:"form-check form-check-sm form-check-custom form-check-solid"},so=["value"],ao={class:"w-36 p-4"},no={class:"text-dark font-semibold whitespace-nowrap"},lo={class:"w-36 p-4"},ro={class:"text-grey-600 font-semibold whitespace-nowrap"},io={class:"w-36 p-4"},co={class:"text-grey-600 font-semibold whitespace-nowrap"},uo={class:"w-36 p-4"},mo={class:"text-grey-600 font-semibold whitespace-nowrap"},po={class:"w-36 p-4"},fo={class:"text-grey-600 font-semibold whitespace-nowrap"},go={class:"w-36 p-4"},ho={class:"flex justify-center"},vo=["checked"],bo={class:"w-36 p-4"},yo={class:"flex justify-center"},Co=["checked"],wo={key:1},_o={class:"h-auto w-full flex flex-row items-center justify-evenly gap-2"},ko=["onClick"],Po=["onClick"],So={class:"text-danger"},$o={class:"d-flex flex-wrap align-items-center mb-5 mx-8"},Vo={key:0,class:"text-gray-700 fw-semibold fs-6 me-auto"};function xo(e,o,x,C,S,m){var F,M;const n=y("SvgIcon"),a=y("el-icon"),r=y("el-input"),u=y("el-form-item"),i=y("el-form"),l=y("el-empty"),_=y("TableHeader"),w=y("TablePagination"),E=y("ContactModal");return h(),b(j,null,[t("div",Ue,[t("div",je,[o[7]||(o[7]=t("h1",{class:"text-gray-900 text-lg font-bold"},"Customer Contacts",-1)),t("div",qe,[d(i,{onSubmit:ae(e.searchCustomerContacts,["prevent"])},{default:k(()=>[t("div",ze,[d(u,{class:"mb-0"},{default:k(()=>[d(r,{class:"w-250px",placeholder:"Search",modelValue:e.search,"onUpdate:modelValue":o[0]||(o[0]=s=>e.search=s),name:"search",size:"large"},{prefix:k(()=>[d(a,{class:"el-input__icon"},{default:k(()=>[t("span",Qe,[d(n,{icon:"searchIcon"})])]),_:1})]),_:1},8,["modelValue"])]),_:1})])]),_:1},8,["onSubmit"]),t("div",We,[e.checkedRows.length!==0&&e.isAdmin()?(h(),b("button",{key:0,class:"bg-danger text-white rounded-md px-4 py-2 font-semibold",onClick:o[1]||(o[1]=(...s)=>e.onRemove&&e.onRemove(...s))}," Remove ")):V("",!0),e.isAdmin()?(h(),b("button",{key:1,class:"bg-primary text-white rounded-md px-4 py-2 font-semibold flex flex-row items-center gap-2 justify-between",onClick:o[2]||(o[2]=(...s)=>e.toggleNewCustomer&&e.toggleNewCustomer(...s))},[d(n,{icon:"addIcon"}),o[6]||(o[6]=se(" New "))])):V("",!0)])])]),e.loading?(h(),b("div",Ye,o[8]||(o[8]=[t("div",{class:"spinner-border text-primary",role:"status"},[t("span",{class:"sr-only"},"Loading...")],-1)]))):e.contactList.length===0?(h(),K(l,{key:1,description:"No Data"})):(h(),b("div",Ze,[t("table",null,[t("thead",Ge,[t("tr",Je,[d(_,{headers:e.tableHeader,sortBy:e.sortParams.sortBy,sortDirection:e.sortParams.sortDirection,onSort:e.onSort},{customHeader:k(({header:s})=>[s.label===""?(h(),b("div",Ke,[t("div",Xe,[X(t("input",{class:"h-4 w-4",type:"checkbox","onUpdate:modelValue":o[3]||(o[3]=$=>e.checkAll=$),onChange:o[4]||(o[4]=(...$)=>e.onToggleCheckAll&&e.onToggleCheckAll(...$))},null,544),[[ee,e.checkAll]])])])):(h(),b("div",eo,N(s.label),1))]),_:1},8,["headers","sortBy","sortDirection","onSort"])])]),t("tbody",null,[(h(!0),b(j,null,pe(e.contactList,s=>(h(),b("tr",{key:s.id},[e.isAdmin()?(h(),b("td",oo,[t("div",to,[X(t("input",{class:"h-4 w-4",type:"checkbox",value:s.id,"onUpdate:modelValue":o[5]||(o[5]=$=>e.checkedRows=$)},null,8,so),[[ee,e.checkedRows]])])])):V("",!0),t("td",ao,[t("span",no,N(s==null?void 0:s.name),1)]),t("td",lo,[t("span",ro,N(s==null?void 0:s.address),1)]),t("td",io,[t("span",co,N(s==null?void 0:s.mobilePhone),1)]),t("td",uo,[t("span",mo,N(s==null?void 0:s.officePhone),1)]),t("td",po,[t("span",fo,N(s==null?void 0:s.emailAddress),1)]),t("td",go,[t("div",ho,[t("input",{class:"h-4 w-4",type:"checkbox",checked:s==null?void 0:s.primaryContact,disabled:""},null,8,vo)])]),t("td",bo,[t("div",yo,[t("input",{class:"h-4 w-4",type:"checkbox",checked:s==null?void 0:s.notifyOnNewReport,disabled:""},null,8,Co)])]),e.isAdmin()?(h(),b("td",wo,[t("div",_o,[t("button",{class:"bg-grey-300 h-8 w-8 rounded-lg px-1.5 py-0.5",onClick:$=>e.toggleEditCustomer((s==null?void 0:s.id)??"")},[d(n,{icon:"newReportIcon"})],8,ko),t("button",{class:"bg-grey-300 h-8 w-8 rounded-lg px-2 py-0.5",onClick:$=>e.deleteEngineer(s==null?void 0:s.id)},[t("span",So,[d(n,{icon:"trashIcon"})])],8,Po)])])):V("",!0)]))),128))])]),t("div",$o,[(F=e.contactList)!=null&&F.length?(h(),b("div",Vo,N(`Showing ${(e.currentPage-1)*10+1} to ${(M=e.contactList)==null?void 0:M.length} of ${e.totalElements} entries`),1)):V("",!0),e.pageCount>=1?(h(),K(w,{key:1,"total-pages":e.pageCount,total:e.totalElements,"per-page":10,"current-page":e.currentPage,onPageChange:e.pageChange},null,8,["total-pages","total","current-page","onPageChange"])):V("",!0)])]))]),d(E,{isVisible:e.isModalVisible,close:e.toggleNewCustomer,ref:"contactModal",loadPage:e.getCustomerContacts},null,8,["isVisible","close","loadPage"])],64)}const Do=Q(Le,[["render",xo]]),Ao=q({name:"customer-overview",components:{PageHeader:te,CustomerModal:ge,CustomerList:Do},props:{isVisible:{type:Boolean,required:!0},close:{type:Function,required:!0}},setup(){var l,_;const e=fe(),o=p(!1),x=z(),C=le(),S=p(),m=p(null),n=((_=(l=x.params)==null?void 0:l.customerId)==null?void 0:_.toString())||"",a=p(!1);ne(()=>{n&&u()});const r=()=>{var w;(w=m==null?void 0:m.value)==null||w.setId(n),a.value=!a.value},u=async()=>{o.value=!0,e.getCustomerDetails({id:n,callback:{onSuccess:w=>{S.value=w},onFinish:()=>{o.value=!1}}})};return{loading:o,customerModal:m,customerDetails:S,isModalVisible:a,back:()=>{C.go(-1)},toggleEditCompany:r,getCustomerDetails:u,isAdmin:H}}}),No={key:0,class:"text-center my-auto"},Eo={class:"h-auto w-11/12 mx-auto my-4 bg-white rounded-xl flex flex-col items-center"},Fo={class:"h-auto w-full p-4 flex flex-col gap-3 items-center border-b-[1px] border-dashed border-grey-300"},Io={class:"h-auto w-full flex flex-row items-center justify-end gap-3"},Mo={class:"h-auto w-full p-4 flex flex-col gap-3 items-start"},Ro={class:"h-auto w-full flex flex-col gap-2 text-gray-400 group"},Oo={class:"group-hover:text-primary"},To={class:"h-auto w-full flex flex-col gap-2 text-gray-400 group"},Bo={class:"group-hover:text-primary"};function Ho(e,o,x,C,S,m){var u,i,l,_;const n=y("PageHeader"),a=y("CustomerList"),r=y("CustomerModal");return h(),b(j,null,[e.loading?(h(),b("div",No,o[2]||(o[2]=[t("div",{class:"spinner-border text-primary",role:"status"},[t("span",{class:"sr-only"},"Loading...")],-1)]))):V("",!0),d(n,{title:"Customer details",breadcrumbs:[((i=(u=e.customerDetails)==null?void 0:u.company)==null?void 0:i.name)||"","Customer","Customer details"]},null,8,["breadcrumbs"]),t("div",Eo,[t("div",Fo,[o[3]||(o[3]=t("h1",{class:"text-lg font-bold text-gray-900 self-start"}," Customer Overview ",-1)),t("div",Io,[e.isAdmin()?(h(),b("button",{key:0,class:"bg-primary text-white rounded-md px-4 py-2 font-semibold",onClick:o[0]||(o[0]=(...w)=>e.toggleEditCompany&&e.toggleEditCompany(...w))}," Edit ")):V("",!0),t("button",{type:"button",class:"bg-grey-300 text-gray-700 rounded-md px-4 py-2 font-semibold",onClick:o[1]||(o[1]=(...w)=>e.back&&e.back(...w))}," Back ")])]),t("div",Mo,[t("div",Ro,[o[4]||(o[4]=t("h4",{class:"font-semibold text-gray-800"},"Customer/Company Name",-1)),t("p",Oo,N((l=e.customerDetails)==null?void 0:l.customerName),1)]),t("div",To,[t("div",null,[o[5]||(o[5]=t("h4",{class:"font-semibold text-gray-800"},"Notes",-1)),t("p",Bo,N((_=e.customerDetails)==null?void 0:_.notes),1)])])])]),t("div",null,[d(a)]),d(r,{isVisible:e.isModalVisible,close:e.toggleEditCompany,ref:"customerModal",loadPage:e.getCustomerDetails},null,8,["isVisible","close","loadPage"])],64)}const et=Q(Ao,[["render",Ho]]);export{et as default};
