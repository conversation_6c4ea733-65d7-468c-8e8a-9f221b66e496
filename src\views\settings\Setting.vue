<template>
  <div class="bg-screen-background w-full mx-auto h-auto">
    <PageHeader title="Settings" :breadcrumbs="breadcrumbs" />
    <div
      class="h-auto w-full flex flex-col items-start p-2"
    >
      <ul class="flex flex-row gap-3 items-center px-4" role="tablist">
        <li class="nav-item" v-for="item in tabs" :key="item.value">
          <div
            class="cursor-pointer font-semibold pt-0 mb-3 hover:text-active-hover hover:border-b-2 hover:border-active-border-hover"
            :class="{
              active: tabIndex === item.value,
              'text-link border-b-2 hover:border-link-hover':
                tabIndex === item.value,
              'text-inactive border-b-2 border-b-inactive-border':
                item.value !== item.value,
            }"
            data-bs-toggle="tab"
            @click="setActiveTab($event)"
            :data-tab-index="item.value"
            role="tab"
          >
            {{ item.label }}
          </div>
        </li>
      </ul>
    </div>
  </div>
  <div v-if="tabIndex == 'products'">
    <Products />
  </div>
  <div v-else-if="tabIndex == 'serviceCosts'">
    <CostsPage label="Service Costs" :type="CostSettingType.Service" />
  </div>
  <div v-else-if="tabIndex == 'engineeringCosts'">
    <CostsPage label="Engineering Costs" :type="CostSettingType.Engineer" />
  </div>
</template>

<script lang="ts">
import SvgIcon from "@/constants/SvgIcon.vue";
import { CostSettingType } from "@/constants/cost-setting";
import { getOption } from "@/utils/option";
import { defineComponent, ref } from "vue";
import CostsPage from "./costs/CostsPage.vue";
import Products from "./products/Products.vue";
import PageHeader from "@/components/PageHeader.vue";

const tabs = [
  { value: "products", label: "Products" },
  { value: "serviceCosts", label: "Service Costs" },
  { value: "engineeringCosts", label: "Engineering Costs" },
];

export default defineComponent({
  name: "setting",
  components: {
    SvgIcon,
    Products,
    CostsPage,
    PageHeader,
  },
  setup() {
    const tabIndex = ref<string>(tabs[0].value);
    const breadcrumbs = ref<string[]>(["Settings", "Products"]);

    const setActiveTab = (e: Event) => {
      const target = e.target as HTMLInputElement;

      tabIndex.value = target.getAttribute("data-tab-index") as string;

      //adjust the breadcrumbs list to update for each tab
      const selectedTab = tabs.find((t) => t.value === tabIndex.value);
      if (selectedTab) {
        breadcrumbs.value.pop();
        breadcrumbs.value.push(selectedTab.label);
      }
    };

    return {
      tabs,
      tabIndex,
      CostSettingType,
      breadcrumbs,
      getOption,
      setActiveTab,
    };
  },
});
</script>
