<template>
  <div class="form-container w-xxl-500px p-10 bg-white rounded-3">
    <div v-if="currentStep === STEP.REGISTER">
      <div class="text-center mb-10">
        <img src="/media/well-logo.png" :width="100" :height="100" />
      </div>
      <div class="d-flex flex-column gap-10">
        <h1 class="text-dark text-center">Register</h1>
        <el-form
          id="change_pass_form"
          @submit.prevent="submit"
          :model="targetData"
          :rules="rules"
          ref="formRef"
          class="form"
        >
          <div class="row">
            <div
              class="col-12 fv-row d-flex flex-column justify-content-stretch mb-7"
            >
              <label
                class="d-flex align-items-center fs-6 fw-semibold mb-2 text-break required"
                >Email
              </label>
              <el-form-item class="mt-auto">
                <el-input
                  class="w-100"
                  placeholder="Enter email"
                  v-model="targetData.email"
                  disabled
                ></el-input>
              </el-form-item>
            </div>
            <div
              class="col-12 fv-row d-flex flex-column justify-content-stretch mb-7"
            >
              <label
                class="d-flex align-items-center fs-6 fw-semibold mb-2 text-break required"
                >First Name
              </label>
              <el-form-item prop="firstName" class="mt-auto">
                <el-input
                  class="w-100"
                  v-model="targetData.firstName"
                  placeholder="Enter first name"
                  name="firstName"
                ></el-input>
              </el-form-item>
            </div>
            <div
              class="col-12 fv-row d-flex flex-column justify-content-stretch mb-7"
            >
              <label
                class="d-flex align-items-center fs-6 fw-semibold mb-2 text-break required"
                >Last Name
              </label>
              <el-form-item prop="lastName" class="mt-auto">
                <el-input
                  class="w-100"
                  v-model="targetData.lastName"
                  placeholder="Enter last name"
                  type="input"
                  name="lastName"
                ></el-input>
              </el-form-item>
            </div>
            <div
              class="col-12 fv-row d-flex flex-column justify-content-stretch mb-7"
            >
              <label
                class="d-flex align-items-center fs-6 fw-semibold mb-2 text-break required"
                >Password
              </label>
              <div class="position-relative input-password">
                <el-form-item prop="newPassword" class="mt-auto mb-0">
                  <el-input
                    size="large"
                    class="w-100"
                    v-model="targetData.newPassword"
                    name="newPassword"
                    :type="targetData?.hideNewPassword ? 'password' : 'input'"
                  ></el-input>
                </el-form-item>
                <span
                  class="svg-icon svg-icon-1 position-absolute top-50 eye-icon"
                  @click="() => toggleEye('newPassword')"
                >
                  <SvgIcon
                    :icon="
                      targetData?.hideNewPassword
                        ? 'hidePasswordIcon'
                        : 'showPasswordIcon'
                    "
                  />
                </span>
              </div>
            </div>
            <div
              class="col-12 fv-row d-flex flex-column justify-content-stretch"
            >
              <label
                class="d-flex align-items-center fs-6 fw-semibold mb-2 required"
              >
                Confirm Password
              </label>
              <div class="position-relative input-password">
                <el-form-item prop="confirmPassword" class="mt-auto mb-0">
                  <el-input
                    size="large"
                    class="w-100"
                    v-model="targetData.confirmPassword"
                    name="confirmPassword"
                    :type="
                      targetData?.hideConfirmPassword ? 'password' : 'input'
                    "
                  ></el-input>
                </el-form-item>
                <span
                  class="svg-icon svg-icon-1 position-absolute top-50 eye-icon"
                  @click="() => toggleEye('confirmPassword')"
                >
                  <SvgIcon
                    :icon="
                      targetData?.hideConfirmPassword
                        ? 'hidePasswordIcon'
                        : 'showPasswordIcon'
                    "
                  />
                </span>
              </div>
            </div>
          </div>
          <div className="validate-password my-5">
            <p>New password must contain:</p>
            <div>
              <div
                v-for="[key, value] in Object.entries(checkPassword)"
                :key="key"
                :className="`d-flex mt-1 ${
                  value.isValid ? 'valid' : 'invalid'
                }`"
              >
                <SvgIcon
                  :icon="
                    value.isValid ? 'checkMarkIcon' : 'exclamationMarkIcon'
                  "
                />
                <span class="ms-2">{{ value.text }}</span>
              </div>
            </div>
          </div>
          <div class="d-flex justify-content-end mt-10">
            <button
              :data-kt-indicator="loading ? 'on' : null"
              class="btn btn-sm btn-primary ms-auto"
              type="submit"
              :disabled="loading"
            >
              <span v-if="!loading" class="indicator-label"> Register </span>
              <span v-if="loading" class="indicator-progress">
                Please wait...
                <span
                  class="spinner-border spinner-border-sm align-middle ms-2"
                ></span>
              </span>
            </button>
          </div>
        </el-form>
      </div>
    </div>
    <SuccessTemplate
      v-else-if="currentStep === STEP.SUCCESS"
      message="Your new password is set"
      btnTitle="Go to Home page"
      btnPath="/"
    />
  </div>
</template>

<script lang="ts">
import SuccessTemplate from "@/components/templates/SuccessTemplate.vue";
import SvgIcon from "@/constants/SvgIcon.vue";
import { ExceptionCode, ExceptionMessages } from "@/constants/exceptions";
import {
  RegexLowercase,
  RegexPasswordLength,
  RegexSpecialChar,
  RegexUppercase,
} from "@/constants/regex";
import { DEFAULT_PASS } from "@/constants/user";
import { validator } from "@/utils/validator";
import JwtService from "@/services/JwtService";
import { useUserStore } from "@/stores/user";
import Swal from "sweetalert2";
import { ErrorMessage, Field, Form as VForm } from "vee-validate";
import { defineComponent, ref, watch } from "vue";

enum STEP {
  REGISTER = 1,
  SUCCESS,
}

interface Form {
  email: string;
  firstName: string;
  lastName: string;
  newPassword: string;
  confirmPassword: string;
  currentPassword: string;
  hideNewPassword: boolean;
  hideConfirmPassword: boolean;
}

export default defineComponent({
  name: "register-invited-user",
  components: {
    Field,
    VForm,
    ErrorMessage,
    SvgIcon,
    SuccessTemplate,
  },
  setup() {
    const userStore = useUserStore();
    const currentStep = ref<number>(STEP.REGISTER);
    const initialValues: Form = {
      email: JwtService.getUserInfo()?.email,
      firstName: "",
      lastName: "",
      newPassword: "",
      confirmPassword: "",
      currentPassword: "",
      hideNewPassword: true,
      hideConfirmPassword: true,
    };
    const initialCheckPassFormat = {
      length: {
        isValid: false,
        text: "At least 8 characters",
      },
      lowercase: {
        isValid: false,
        text: "At least 1 lowercase character",
      },
      uppercase: {
        isValid: false,
        text: "At least 1 uppercase character",
      },
      specialCharacter: {
        isValid: false,
        text: "At least 1 number and 1 special character",
      },
    };
    const targetData = ref<Form>({ ...initialValues });
    const loading = ref<boolean>(false);
    const formRef = ref<null | HTMLFormElement>(null);
    const checkPassword = ref({ ...initialCheckPassFormat });

    watch(
      () => targetData.value.newPassword,
      (newValue) => {
        if (newValue !== "") {
        }
      }
    );

    const validateNewPasswordFormat = (value: string) => {
      const errLength = validator.validate(value, {
        pattern: RegexPasswordLength,
        errorsMessage: { pattern: "Incorrect password format." },
      });
      const errLowercase = validator.validate(value, {
        pattern: RegexLowercase,
        errorsMessage: { pattern: "Incorrect password format." },
      });
      const errUppercase = validator.validate(value, {
        pattern: RegexUppercase,
        errorsMessage: { pattern: "Incorrect password format." },
      });
      const errNumberAndChar = validator.validate(value, {
        pattern: RegexSpecialChar,
        errorsMessage: { pattern: "Incorrect password format." },
      });

      checkPassword.value = {
        length: {
          isValid: errLength ? false : true,
          text: "At least 8 characters",
        },
        lowercase: {
          isValid: errLowercase ? false : true,
          text: "At least 1 lowercase character",
        },
        uppercase: {
          isValid: errUppercase ? false : true,
          text: "At least 1 uppercase character",
        },
        specialCharacter: {
          isValid: errNumberAndChar ? false : true,
          text: "At least 1 number and 1 special character",
        },
      };

      const err =
        errLength || errLowercase || errUppercase || errNumberAndChar || "";

      return err;
    };

    const validateNewPass = (_rule: any, value: any, callback: any) => {
      if (value === "") {
        callback(new Error("Please type New Password"));
      } else {
        const err = validateNewPasswordFormat(value);
        if (err !== "") {
          callback(new Error(err));
        } else {
          callback();
        }
      }
    };

    const validateConfirmPass = (_rule: any, value: any, callback: any) => {
      if (value === "") {
        callback(new Error("Please type Confirm Password"));
      } else if (value !== targetData.value.newPassword) {
        callback(new Error("Confirm Password doesn't match New Password!"));
      } else {
        callback();
      }
    };

    const rules = ref({
      firstName: [
        {
          required: true,
          message: "Please type First Name",
          trigger: ["blur", "change"],
        },
      ],
      lastName: [
        {
          required: true,
          message: "Please type Last Name",
          trigger: ["blur", "change"],
        },
      ],
      email: [
        [
          {
            required: true,
            message: "Please input email address",
            trigger: "blur",
          },
          {
            type: "email",
            message: "Please input correct email address",
            trigger: ["blur", "change"],
          },
        ],
      ],
      newPassword: [
        { validator: validateNewPass, trigger: ["change", "blur"] },
      ],
      confirmPassword: [
        { validator: validateConfirmPass, trigger: ["change", "blur"] },
      ],
    });

    const submit = () => {
      if (!formRef.value) {
        return;
      }

      formRef.value.validate((valid: boolean) => {
        if (valid) {
          const params: User.RegisterInvitedUser = {
            userInfo: {
              firstName: targetData.value?.firstName,
              lastName: targetData.value?.lastName,
            },
            changePass: {
              currentPassword: DEFAULT_PASS,
              newPassword: targetData?.value?.newPassword,
            },
          };
          registerInvitedUser(params);
        }
      });
    };

    const registerInvitedUser = async (params: User.RegisterInvitedUser) => {
      loading.value = true;
      userStore.registerInvitedUser({
        params,
        callback: {
          onSuccess: (_res: any) => {
            userStore.getMyProfile({
              callback: {
                onSuccess: (res: User.Info) => {
                  JwtService.saveUserInfo(
                    JSON.stringify({
                      ...res,
                      companyId: res?.company?.id || "",
                    })
                  );
                  currentStep.value = STEP.SUCCESS;
                },
                onFinish: () => {
                  loading.value = false;
                },
              },
            });
          },
          onFailure: (error) => {
            loading.value = false;
            Swal.fire({
              text:
                ExceptionMessages[error?.response?.data?.errorCode as ExceptionCode] ||
                error?.response?.data?.message ||
                error?.message ||
                "Sorry, looks like there are some errors detected, please try again.",
              icon: "error",
              buttonsStyling: false,
              confirmButtonText: "Got it!",
              heightAuto: false,
              customClass: {
                confirmButton: "btn fw-semibold btn-light-danger",
              },
            });
          },
        },
      });
    };

    const toggleEye = (field: string) => {
      if (field === "confirmPassword") {
        targetData.value.hideConfirmPassword =
          !targetData.value.hideConfirmPassword;
      } else if (field === "newPassword") {
        targetData.value.hideNewPassword = !targetData.value.hideNewPassword;
      }
    };

    return {
      currentStep,
      STEP,
      formRef,
      rules,
      loading,
      targetData,
      checkPassword,
      submit,
      toggleEye,
    };
  },
});
</script>
