declare namespace Company {
  export type Info = {
    id?: string;
    name?: string;
    registerNumber?: string | null;
    description?: string;
    createdAt?: string;
    users?: User.Info[];
  };

  export interface GetFilter extends Filter.FilterForm {
    companyId?: string | null;
  }

  export interface RemoveUserList {
    role: number;
    userIds: string[];
  }

  export interface AddUserList extends RemoveUserList {
    companyId: string;
  }

  export interface GetCustomer extends Filter.FilterForm {
    companyId: string;
    name?: string;
  }
}
