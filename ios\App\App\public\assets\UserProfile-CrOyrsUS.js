import{P as D}from"./PageHeader-3hadTn26.js";import{d as b,q as m,j as P,x as v,A as y,J as _,i as k,_ as B,c as d,l as f,m as C,a as U,w as H,F as S,r as g,o as p,b as $}from"./index-CGNRhvz7.js";import{u as h}from"./user-KFDu8xJF.js";import{U as w}from"./UserDetail-BrRvNZ4N.js";import"./handleFailure-DtTpu7r3.js";import"./SvgIcon-CMhyaXWN.js";import"./Overview-CymoOfok.js";import"./validator-6laVLK0J.js";import"./index.esm-DXW765zG.js";import"./company-oDyd0dWV.js";import"./regex-BLjctcPP.js";import"./AssignUserModal-B-x10c2w.js";import"./UserModal-Ck8RxOB2.js";import"./TablePagination-BmVxunEG.js";import"./d-left-arrow-079-B3YbfCzd.js";import"./TableHeader-C1CWTWQa.js";import"./table-bhK9qpe4.js";import"./date-CvSHk5ED.js";const F=b({name:"my-profile",components:{UserDetail:w,PageHeader:D},setup(){const e=h(),a=P(),s=m(!1),t=m(a.params.id),n=m(),i=k("userInfo"),l=["User Management","User Profile"];v(()=>{t&&o()}),y(()=>a.params.id,r=>{t.value=r,r&&o()});const o=async()=>{t&&(s.value=!0,e.getUserProfile({id:t.value,callback:{onSuccess:r=>{var u,c;n.value=r,((c=(u=_)==null?void 0:u.getUserInfo())==null?void 0:c.id)===(r==null?void 0:r.id)&&(i==null||i.updateUserInfo({...r}))},onFinish:()=>{s.value=!1}}}))};return{loading:s,userDetail:n,breadcrumbs:l,getUserProfile:o}}}),N={key:0,class:"text-center my-auto"};function V(e,a,s,t,n,i){const l=g("PageHeader"),o=g("UserDetail");return p(),d(S,null,[e.loading?(p(),d("div",N,a[0]||(a[0]=[U("div",{class:"spinner-border text-primary",role:"status"},[U("span",{class:"sr-only"},"Loading...")],-1)]))):f("",!0),!e.loading&&e.userDetail?(p(),C(o,{key:1,userDetail:e.userDetail,reloadUserData:e.getUserProfile},{pageHeader:H(()=>[$(l,{title:"User Profile",breadcrumbs:e.breadcrumbs,reloadUserData:e.getUserProfile},null,8,["breadcrumbs","reloadUserData"])]),_:1},8,["userDetail","reloadUserData"])):f("",!0)],64)}const Y=B(F,[["render",V]]);export{Y as default};
