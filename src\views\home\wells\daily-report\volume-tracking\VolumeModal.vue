<template>
  <div
    v-if="isVisible"
    class="fixed top-0 right-0 bottom-0 left-0 z-40 flex items-center"
  >
    <!-- Dark overlay background -->
    <div class="absolute inset-0 bg-sidebar-background opacity-25"></div>
    <div
      class="relative bg-card-background text-card-text-light max-h-11/12 w-11/12 flex flex-col items-center gap-3 mx-auto my-6 p-5 rounded-xl md:w-3/5 lg:w-2/5 lg:h-auto"
    >
      <div class="flex flex-row h-auto w-full items-center justify-between">
        <h3 class="text-lg font-bold">
          {{ id ? `Edit ${getTitle()}` : `Add ${getTitle()}` }}
        </h3>
        <span class="cursor-pointer ms-auto" @click="closeModal">
          <SvgIcon icon="closeModalIcon" />
        </span>
      </div>

      <el-form
        id="add_volume_form"
        @submit.prevent="submit"
        :model="targetData"
        :rules="rules"
        ref="formRef"
        class="h-auto w-full flex flex-col gap-3"
      >
        <div class="flex flex-col gap-1">
          <label class="font-bold"
            >Volume (bbl)<span class="text-danger-active font-light"
              >*</span
            ></label
          >
          <el-form-item prop="volume" class="mt-auto">
            <el-input
              type="number"
              :controls="false"
              step="any"
              v-model="targetData.volume"
              placeholder=""
              name="volume"
            ></el-input>
          </el-form-item>
        </div>

        <div class="flex flex-col justify-between gap-1">
          <div class="flex flex-row justify-between items-center gap-1">
            <label class="font-bold"
              >Product<span class="text-danger-active font-light"
                >*</span
              ></label
            >
            <button
              type="button"
              class="bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover rounded-md px-4 py-2 font-semibold"
              @click="toggleAddNewProductModal"
            >
              New Product
            </button>
          </div>
          <el-form-item prop="productId" class="mt-auto">
            <el-select
              v-model="targetData.productId"
              placeholder="Select Product"
              clearable
            >
              <el-option
                v-for="item in productList"
                :key="item.value"
                :label="item.key"
                :value="item.value"
                name="productId"
              />
            </el-select>
          </el-form-item>
        </div>

        <div class="flex flex-col gap-1">
          <label class="font-bold"
            >Description<span class="text-danger-active font-light"
              >*</span
            ></label
          >
          <el-form-item prop="description" class="mt-auto">
            <el-input
              type="textarea"
              :rows="3"
              v-model="targetData.description"
              placeholder=""
              name="description"
            ></el-input>
          </el-form-item>
        </div>

        <div class="h-auto w-full flex flex-row items-center gap-2">
          <button
            type="button"
            class="bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover rounded-md px-4 py-2 font-semibold"
            @click="closeModal"
            :disabled="loading"
          >
            Discard
          </button>
          <button
            class="bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover rounded-md px-4 py-2 font-semibold"
            type="submit"
            :disabled="loading"
          >
            <span v-if="!loading" class="indicator-label"> Save </span>
            <span v-if="loading" class="indicator-progress">
              Please wait...
              <span
                class="spinner-border spinner-border-sm align-middle ms-2"
              ></span>
            </span>
          </button>
        </div>
      </el-form>
    </div>
  </div>
  <FormModal
    :isVisible="isModalVisible"
    :close="toggleAddNewProductModal"
    ref="newProductModal"
    :productAndPackageInventoryId="productAndPackageInventoryId"
    :loadTable="() => getPackageReportInfo(productAndPackageInventoryId)"
  />
</template>

<script lang="ts">
import SvgIcon from "@/constants/SvgIcon.vue";
import { getOption } from "@/utils/option";
import { ProductAndPackageInventoryType } from "@/constants/product-package-inventory";
import { volumeTrackingOptions } from "@/constants/volume-tracking";
import { useProductPackageReportStore } from "@/stores/product-package-inventory-report";
import { useVolumeTrackingItemStore } from "@/stores/volume-tracking-item";
import FormModal from "@/views/home/<USER>/daily-report/product-package/FormModal.vue";
import { defineComponent, ref, watch, type Ref } from "vue";
import type { ProductPackageReportItem } from "@/types/product-package";

export default defineComponent({
  name: "add-volume-modal",
  components: { SvgIcon, FormModal },
  props: {
    isVisible: {
      type: Boolean,
      required: true,
    },
    close: {
      type: Function,
      required: true,
    },
    loadPage: {
      type: Function,
      required: false,
    },
    productAndPackageInventoryId: {
      type: String,
      default: "",
    },
  },
  setup(props) {
    const volumeItemStore = useVolumeTrackingItemStore();
    const productReportStore = useProductPackageReportStore();
    const modal = ref(false);
    const initialForm = {
      description: "",
      volume: null,
      type: null,
      productId: null,
    };
    const targetData = ref(JSON.parse(JSON.stringify(initialForm)));
    const formRef = ref<null | HTMLFormElement>(null);
    const loading = ref<boolean>(false);
    const type = ref<any>(null);
    const volumeTrackingId = ref("");
    const id = ref("");
    const newProductModal: Ref<any> = ref<typeof FormModal | null>(null);
    const productList = ref<any>([]);
    const loadingPackageList = ref<boolean>(false);
    const isModalVisible = ref(false);

    watch(id, (newValue) => {
      if (newValue != "") {
        getVolumeTrackingItem();
      }
    });

    watch(
      () => props?.productAndPackageInventoryId,
      (newValue) => {
        if (newValue !== "") {
          getPackageReportInfo(newValue);
        }
      }
    );

    watch(
      () => props.isVisible,
      (newValue) => {
        if (newValue === false) {
          volumeTrackingId.value = "";
          id.value = "";
          type.value = null;
          reset();
          formRef?.value?.resetFields();
        }
      }
    );

    const toggleAddNewProductModal = () => {
      newProductModal?.value?.setType(ProductAndPackageInventoryType.initial);
      isModalVisible.value = !isModalVisible.value;
    };

    const getPackageReportInfo = async (id: string): Promise<void> => {
      loadingPackageList.value = true;

      productReportStore.getProductPackageReports({
        params: {
          productAndPackageInventoryId: id,
          page: 1,
          limit: 200,
        },
        callback: {
          onSuccess: (res: any) => {
            productList.value = res?.items.map(
              (item: ProductPackageReportItem) => {
                return { value: item?.product?.id, key: item?.product?.name };
              }
            );
          },
          onFinish: (_err: any) => {
            loadingPackageList.value = false;
          },
        },
      });
    };

    const getVolumeTrackingItem = async (): Promise<void> => {
      volumeItemStore.getVolumeTrackingItemDetails({
        id: id.value,
        callback: {
          onSuccess: (res: any) => {
            targetData.value = { ...res };
          },
        },
      });
    };

    const updateVolumeTrackingItem = async (param: any): Promise<void> => {
      loading.value = true;
      volumeItemStore.updateVolumeTrackingItem({
        id: id.value,
        params: param,
        callback: {
          onSuccess: (_res: any) => {
            if (props?.loadPage) {
              props?.loadPage();
            }
            closeModal();
          },
          onFinish: (_err: any) => {
            loading.value = false;
          },
        },
      });
    };

    const createVolumeTrackingItem = async (param: any): Promise<void> => {
      loading.value = true;
      volumeItemStore.createVolumeTrackingItem({
        params: param,
        callback: {
          onSuccess: (_res: any) => {
            if (props?.loadPage) {
              props?.loadPage();
            }
            closeModal();
          },
          onFinish: (_err: any) => {
            loading.value = false;
          },
        },
      });
    };

    const closeModal = () => {
      reset();
      props.close();
    };

    const rules = ref({
      description: [
        {
          required: true,
          message: "Please type Description",
          trigger: "blur",
        },
      ],
      productId: [
        {
          required: true,
          message: "Please select Product",
          trigger: "blur",
        },
      ],
      volume: [
        {
          required: true,
          message: "Please type Volume",
          trigger: "blur",
        },
      ],
    });

    const setId = (volumeId: string, itemId: string) => {
      volumeTrackingId.value = volumeId;
      id.value = itemId;
    };

    const setType = (packageType: number) => {
      type.value = packageType;
    };

    const submit = () => {
      if (!formRef.value) {
        return;
      }

      formRef.value.validate((valid: boolean) => {
        if (valid) {
          const param = {
            volumeTrackingId: volumeTrackingId?.value,
            description: targetData?.value?.description,
            volume: Number(targetData?.value?.volume),
            type: Number(type?.value),
          };

          if (id?.value) {
            updateVolumeTrackingItem(param);
          } else {
            createVolumeTrackingItem(param);
          }
        }
      });
    };

    const reset = () => {
      targetData.value = JSON.parse(JSON.stringify(initialForm));
    };

    const getTitle = () => {
      return getOption(type.value, volumeTrackingOptions)?.label || "";
    };

    return {
      id,
      type,
      modal,
      rules,
      loading,
      targetData,
      formRef,
      productList,
      newProductModal,
      isModalVisible,
      toggleAddNewProductModal,
      setId,
      closeModal,
      submit,
      reset,
      setType,
      getTitle,
      getPackageReportInfo,
    };
  },
});
</script>
