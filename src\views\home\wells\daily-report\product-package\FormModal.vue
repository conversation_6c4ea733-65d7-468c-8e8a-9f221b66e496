<template>
  <div
    v-if="isVisible"
    class="fixed top-0 right-0 bottom-0 left-0 z-40 flex items-center"
  >
    <!-- Dark overlay background -->
    <div class="absolute inset-0 bg-sidebar-background opacity-25"></div>
    <div
      class="relative bg-card-background text-card-text-light max-h-11/12 w-11/12 flex flex-col items-center gap-3 mx-auto my-6 p-5 rounded-xl overflow-y-scroll md:overflow-hidden lg:w-2/5 lg:h-auto"
    >
      <div class="flex flex-row h-auto w-full items-center justify-between">
        <h3 class="text-lg font-bold">
          {{ getOption(type, productAndPackageInventoryOptions)?.label }}
        </h3>
        <span class="cursor-pointer" @click="closeModal">
          <SvgIcon icon="closeModalIcon" />
        </span>
      </div>

      <el-form
        id="product_package_form"
        @submit.prevent="submit"
        :model="targetData"
        :rules="rules"
        ref="formRef"
        class="h-auto w-full"
      >
        <div class="flex flex-col gap-3 md:flex-row">
          <div class="h-auto w-full flex flex-col gap-3">
            <div class="flex flex-col gap-1">
              <label class="font-bold"
                >Product
                <span class="text-danger-active font-light">*</span></label
              >
              <el-form-item prop="productId">
                <el-select
                  v-model="targetData.productId"
                  placeholder="Select Product"
                  clearable
                  :disabled="
                    type !== ProductPackageEnum.initial ||
                    (type == ProductPackageEnum.initial && id != '')
                  "
                >
                  <el-option
                    v-for="item in productOptions"
                    :key="item.value"
                    :label="item.key"
                    :value="item.value"
                    name="productId"
                  />
                </el-select>
              </el-form-item>
            </div>
            <div class="flex flex-col gap-2">
              <div class="flex justify-between items-center">
                <label class="font-bold">Location </label>
                <button
                  type="button"
                  class="bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover rounded-md px-4 py-2 font-semibold"
                  @click="() => toggleStorageOrPitModal()"
                >
                  New Location
                </button>
              </div>
              <el-form-item prop="locationId">
                <el-select
                  v-model="targetData.locationId"
                  placeholder="Select Location"
                  clearable
                >
                  <el-option
                    v-for="item in locationOptions"
                    :key="item.value"
                    :label="item.key"
                    :value="item.value"
                    name="locationId"
                  />
                </el-select>
              </el-form-item>
            </div>
            <div class="flex flex-col gap-1">
              <label class="font-bold"
                >Quantity
                <span class="text-danger-active font-light">*</span></label
              >
              <el-form-item prop="quantity">
                <el-input
                  type="number"
                  :controls="false"
                  step="any"
                  v-model="targetData.quantity"
                  placeholder=""
                  name="quantity"
                ></el-input>
              </el-form-item>
            </div>
          </div>
          <div class="h-auto w-full flex flex-col gap-3">
            <template v-if="type !== ProductPackageEnum.used">
              <div class="flex flex-col gap-1">
                <label class="font-bold">Cost </label>
                <el-form-item prop="cost">
                  <el-input
                    disabled
                    v-model="targetData.cost"
                    placeholder=""
                    name="cost"
                  ></el-input>
                </el-form-item>
              </div>
              <div class="flex flex-col gap-1">
                <div class="md:h-11 md:flex md:items-center">
                  <label class="font-bold">BOL No </label>
                </div>
                <el-form-item prop="bolNo">
                  <el-input
                    v-model="targetData.bolNo"
                    placeholder=""
                    name="bolNo"
                  ></el-input>
                </el-form-item>
              </div>
            </template>
          </div>
        </div>
        <div class="flex flex-col gap-1">
          <label class="font-bold">Notes </label>
          <el-form-item prop="notes">
            <el-input
              v-model="targetData.notes"
              placeholder=""
              name="notes"
              type="textarea"
              :rows="2"
            ></el-input>
          </el-form-item>
        </div>
        <div class="h-auto w-full flex flex-row items-center gap-2">
          <button
            type="button"
            class="bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover rounded-md px-4 py-2 font-semibold"
            @click="closeModal"
            :disabled="loading"
          >
            Discard
          </button>
          <button
            class="bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover rounded-md px-4 py-2 font-semibold"
            type="submit"
            :disabled="loading"
          >
            <span v-if="!loading" class="indicator-label"> Save </span>
            <span v-if="loading" class="indicator-progress">
              Please wait...
              <span
                class="spinner-border spinner-border-sm align-middle ms-2"
              ></span>
            </span>
          </button>
        </div>
      </el-form>
    </div>
  </div>
  <StorageOrPitModal
    :isVisible="isSoPModalVisible"
    :close="() => toggleStorageOrPitModal()"
    ref="storageOrPitModal"
    :loadPage="getVolumeTrackings"
  />
</template>

<script lang="ts">
import SvgIcon from "@/constants/SvgIcon.vue";
import { getOption } from "@/utils/option";
import {
  type ProductAndPackageInventoryType,
  productAndPackageInventoryOptions,
  ProductAndPackageInventoryType as ProductPackageEnum,
} from "@/constants/product-package-inventory";
import { useProductStore } from "@/stores/product";
import { useProductPackageItemStore } from "@/stores/product-package-inventory-item";
import { useProductPackageReportStore } from "@/stores/product-package-inventory-report";
import { useVolumeTrackingStore } from "@/stores/volume-tracking";
import { useWellStore } from "@/stores/well";
import StorageOrPitModal from "@/views/home/<USER>/daily-report/volume-tracking/StorageOrPitModal.vue";
import { defineComponent, inject, onMounted, ref, watch, type Ref } from "vue";
import { useRoute } from "vue-router";
import type { Provide } from "@/types/injection-types";

interface Product {
  productAndPackageInventoryReportId: string;
  productId: string;
  locationId: string;
  type: string | null;
  quantity: number | null;
  cost: number | null;
  bolNo: string | undefined;
  notes: string | undefined;
}

export default defineComponent({
  name: "form-modal",
  components: { SvgIcon, StorageOrPitModal },
  props: {
    isVisible: {
      type: Boolean,
      required: true,
    },
    close: {
      type: Function,
      required: true,
    },
    loadTable: {
      type: Function,
      default: () => {},
      required: false,
    },
    productAndPackageInventoryId: {
      type: String,
      required: true,
    },
  },
  setup(props) {
    const route = useRoute();
    const wellStore = useWellStore();
    const productStore = useProductStore();
    const productReportStore = useProductPackageReportStore();
    const productPackageItemStore = useProductPackageItemStore();
    const volumeTrackingStore = useVolumeTrackingStore();
    const targetData = ref<Product>({
      productAndPackageInventoryReportId: "",
      productId: "",
      locationId: "",
      type: null,
      quantity: null,
      cost: null,
      bolNo: "",
      notes: "",
    });
    const productOptions = ref<any[]>([]);
    const locationOptions = ref<any>([]);
    const formRef = ref<null | HTMLFormElement>(null);
    const loading = ref<boolean>(false);
    const id = ref("");
    const type = ref<null | ProductAndPackageInventoryType>(null);
    const storageOrPitModal: Ref<any> = ref<typeof StorageOrPitModal | null>(
      null
    );
    const companyId = ref<string>("");
    const dailyReportProvide = inject<Provide.DailyReport>("dailyReport");
    const isSoPModalVisible = ref(false);

    onMounted(() => {
      getWellInfo();
    });

    watch(id, (newValue) => {
      if (newValue !== "") {
        getProductDetails();
      }
    });

    watch(
      () => targetData.value.productId,
      (newValue) => {
        targetData.value = {
          ...targetData.value,
          cost: getCost(newValue)?.price,
        };
      }
    );

    watch(
      () => props.isVisible,
      (newValue) => {
        if (newValue == true) {
          if (dailyReportProvide?.getDailyReportId()) {
            getVolumeTrackings();
          }
          if (!id.value && companyId.value) {
            getProductOptions();
          }
        } else {
          reset();
          formRef?.value?.resetFields();
        }
      }
    );

    const getWellInfo = async (): Promise<void> => {
      wellStore.getWellDetails({
        wellId: route.params.id as string,
        callback: {
          onSuccess: (res: any) => {
            companyId.value = res?.company?.id;
            getProductOptions();
          },
          onFinish: (_err: any) => {
            loading.value = false;
          },
        },
      });
    };

    const toggleStorageOrPitModal = (id?: string) => {
      if (id) {
        storageOrPitModal?.value?.setId(id);
      }
      isSoPModalVisible.value = !isSoPModalVisible.value;
    };

    const getProductDetails = async (): Promise<void> => {
      productPackageItemStore.getProductPackageItemDetails({
        id: id.value,
        callback: {
          onSuccess: (res: any) => {
            targetData.value = {
              ...targetData.value,
              ...res,
              locationId: res?.location?.id,
            };

            productOptions.value = [
              {
                value: res?.productAndPackageInventoryReport?.product?.id,
                label: res?.productAndPackageInventoryReport?.product?.name,
                price: res?.cost,
              },
            ];
          },
        },
      });
    };

    const getVolumeTrackings = async (
      params = {
        dailyReportId: dailyReportProvide?.getDailyReportId(),
        page: 1,
        limit: 200,
      }
    ): Promise<void> => {
      // loading.value = true;

      volumeTrackingStore.getVolumeTrackings({
        params: params,
        callback: {
          onSuccess: (res: any) => {
            // loading.value = false;
            locationOptions.value = res?.items?.map((item: any) => {
              return {
                value: item?.id,
                key: item?.name,
              };
            });
          },
          onFinish: (_err: any) => {
            // loading.value = false;
          },
        },
      });
    };

    const getProductOptions = async (): Promise<void> => {
      productStore.getProducts({
        params: {
          page: 1,
          limit: 200,
          companyId: companyId.value,
        },
        callback: {
          onSuccess: (res: any) => {
            productOptions.value = res?.items?.map((item: any) => {
              return {
                value: item?.id,
                key: item?.name,
                price: item?.price,
              };
            });
          },
        },
      });
    };

    const getProductCost = async (id: string): Promise<void> => {
      productStore.getProductDetails({
        id: id,
        callback: {
          onSuccess: (res: any) => {
            targetData.value = {
              ...targetData.value,
              cost: res?.price,
            };
          },
        },
      });
    };

    const updateProduct = async (param: any): Promise<void> => {
      loading.value = true;
      productPackageItemStore.updateProductPackageItem({
        id: id.value,
        params: param,
        callback: {
          onSuccess: (_res: any) => {
            if (props?.loadTable) {
              props?.loadTable();
            }
            closeModal();
          },
          onFinish: (_err: any) => {
            loading.value = false;
          },
        },
      });
    };

    const createProduct = async (param: any): Promise<void> => {
      loading.value = true;
      productPackageItemStore.createProductPackageItem({
        params: param,
        callback: {
          onSuccess: (_res: any) => {
            if (props?.loadTable) {
              props?.loadTable();
            }
            closeModal();
          },
          onFinish: (_err: any) => {
            loading.value = false;
          },
        },
      });
    };

    const createProductReport = async (param: any): Promise<void> => {
      loading.value = true;
      productReportStore.createProductPackageReport({
        params: param,
        callback: {
          onSuccess: (_res: any) => {
            if (props?.loadTable) {
              props?.loadTable();
            }
            closeModal();
          },
          onFinish: (_err: any) => {
            loading.value = false;
          },
        },
      });
    };

    const closeModal = () => {
      props.close();
    };

    const rules = ref({
      productId: [
        {
          required: true,
          message: "Please choose Product",
          trigger: "change",
        },
      ],
      locationId: [
        {
          required: true,
          message: "Please choose location",
          trigger: "change",
        },
      ],
      quantity: [
        {
          required: true,
          message: "Please type quantity",
          trigger: "blur",
        },
      ],
    });

    const setId = (reportId: string, itemId: string, idOfProduct: string) => {
      id.value = itemId;
      targetData.value = {
        ...targetData.value,
        productId: idOfProduct,
        productAndPackageInventoryReportId: reportId,
      };

      if (!id.value) {
        getProductCost(idOfProduct);
      }
    };

    const setType = (packageType: number) => {
      type.value = packageType;
    };

    const submit = () => {
      if (!formRef.value) {
        return;
      }

      formRef.value.validate((valid: boolean) => {
        if (valid) {
          let param: any = {
            productAndPackageInventoryId: props?.productAndPackageInventoryId,
            productAndPackageInventoryReportId:
              targetData.value?.productAndPackageInventoryReportId,
            locationId: targetData?.value?.locationId,
            type: type.value,
            quantity: Number(targetData?.value?.quantity),
            notes: targetData?.value?.notes || null,
            productId: targetData?.value?.productId,
            cost: Number(targetData?.value?.cost),
            bolNo: targetData?.value?.bolNo || null,
          };

          if (id?.value) {
            updateProduct(param);
          } else if (type.value != ProductPackageEnum.initial) {
            createProduct(param);
          } else {
            createProductReport(param);
          }
        }
      });
    };

    const reset = () => {
      id.value = "";
      type.value = null;
      productOptions.value = [];
      locationOptions.value = [];
      targetData.value = {
        productAndPackageInventoryReportId: "",
        productId: "",
        locationId: "",
        type: null,
        quantity: null,
        cost: null,
        bolNo: "",
        notes: "",
      };
    };

    const getCost = (value: any) => {
      return (
        productOptions?.value?.find((option) => value == option.value) || null
      );
    };

    return {
      id,
      type,
      rules,
      loading,
      targetData,
      formRef,
      productOptions,
      locationOptions,
      productAndPackageInventoryOptions,
      isSoPModalVisible,
      ProductPackageEnum,
      storageOrPitModal,
      closeModal,
      submit,
      reset,
      setId,
      setType,
      getOption,
      getVolumeTrackings,
      toggleStorageOrPitModal,
    };
  },
});
</script>
