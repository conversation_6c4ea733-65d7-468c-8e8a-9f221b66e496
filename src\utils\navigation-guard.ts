import type { RouteLocationNormalized, NavigationGuardNext } from "vue-router";
import AlertService from "../services/AlertService";

export const setupNavigationGuard = (
  _to: RouteLocationNormalized,
  _from: RouteLocationNormalized,
  next: NavigationGuardNext,
  condition: boolean,
  message = "You have unsaved changes. Are you sure you want to leave?"
): void => {
  if (condition) {
    AlertService.incompleteFormAlert(
      {
        onConfirmed: () => {
          next();
        },
        onCanceled: () => {
          next(false);
        },
      },
      message
    );
  } else {
    next();
  }
};
