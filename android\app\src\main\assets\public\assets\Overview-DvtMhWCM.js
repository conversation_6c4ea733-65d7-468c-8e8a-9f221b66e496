import{P as C}from"./PageHeader-Sj9hJFB8.js";import{u as f}from"./company-KQxnMnUF.js";import{d as D,q as l,j as g,x as v,_ as b,c,l as d,b as _,m as k,a as u,F as B,r as y,o as p}from"./index-BmHWvWFS.js";import{C as P}from"./CompanyDetail-BzJa4nxJ.js";import"./handleFailure-WBgBpurp.js";import"./CompanyModal-C5rLKitg.js";import"./SvgIcon-DYvlNVZf.js";import"./TablePagination-lslz6s2e.js";import"./TableHeader-DGdsllig.js";import"./table-bhK9qpe4.js";import"./date-CKteeARj.js";import"./customer-2JcOh_4Q.js";import"./CustomerModal-DQWo9a5A.js";import"./user-CVSNmFaf.js";import"./index.esm-C4vtr4xS.js";import"./AssignUserModal-kk-OATZ2.js";import"./UserModal-Cgio3UOU.js";import"./validator-D_t2fUhD.js";const S=D({name:"company-overview",components:{CompanyDetail:P,PageHeader:C},setup(){var t,n;const e=l(!1),o=f(),i=g(),s=l(),a=((n=(t=i.params)==null?void 0:t.id)==null?void 0:n.toString())||"";v(()=>{a&&m()});const m=async()=>{e.value=!0,o.getCompanyById({id:a,callback:{onSuccess:r=>{s.value=r},onFinish:()=>{e.value=!1}}})};return{loading:e,companyId:a,companyDetail:s,getCompanyDetails:m}}}),$={key:0,class:"text-center my-auto"};function F(e,o,i,s,a,m){var r;const t=y("PageHeader"),n=y("CompanyDetail");return p(),c(B,null,[e.loading?(p(),c("div",$,o[0]||(o[0]=[u("div",{class:"spinner-border text-primary",role:"status"},[u("span",{class:"sr-only"},"Loading...")],-1)]))):d("",!0),_(t,{title:"Companies",breadcrumbs:["Companies",((r=e.companyDetail)==null?void 0:r.name)||""]},null,8,["breadcrumbs"]),!e.loading&&e.companyDetail?(p(),k(n,{key:1,companyDetail:e.companyDetail,reloadCompanyData:e.getCompanyDetails},null,8,["companyDetail","reloadCompanyData"])):d("",!0)],64)}const K=b(S,[["render",F]]);export{K as default};
