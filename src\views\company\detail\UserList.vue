<template>
  <div class="bg-card-background text-card-text rounded-lg p-4 lg:w-4/5 lg:mx-auto lg:min-w-[1560px]">
    <!--begin::Card header-->
    <div
      class="h-auto w-full flex flex-col gap-2 items-start md:flex-row md:gap-4 md:items-center md:p-4"
    >
      <h1 class="text-lg font-bold whitespace-nowrap">
        {{ `Company ${currentTab?.key}` }}
      </h1>
      <div class="h-auto w-full">
        <el-form @submit.prevent="searchUser">
          <el-form-item class="m-0">
            <el-input
              placeholder="Search"
              v-model="search"
              name="search"
              size="large"
              ><template #prefix>
                <el-icon class="el-input__icon">
                  <SvgIcon icon="searchIcon"
                /></el-icon> </template
            ></el-input> </el-form-item
        ></el-form>
      </div>
      <div class="h-auto w-full flex flex-row justify-end gap-4">
        <button
          class="bg-danger rounded-md px-4 py-2 font-semibold"
          v-if="checkedRows.length !== 0 && isAdmin()"
          @click="onRemove"
        >
          Remove
        </button>
        <button
          class="bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover rounded-md px-4 py-2 font-semibold flex flex-row items-center gap-2 justify-between md:px-4 md:py-3 md:text-xl"
          @click="toggleNewUser"
          v-if="isAdmin()"
        >
          <SvgIcon icon="addIcon" />
          Add
        </button>
      </div>
    </div>
    <!--end::Card header-->
    <div v-if="loading" class="text-center p-5">
      <div class="spinner-border text-primary" role="status">
        <span class="sr-only">Loading...</span>
      </div>
    </div>
    <el-empty v-else-if="userList.length === 0" description="No Data" />
    <div v-else class="h-auto w-full overflow-x-scroll p-4 md:overflow-hidden">
      <!--begin::Table-->
      <table class="md:w-11/12 md:mx-auto md:text-center">
        <!--begin::Table head-->
        <thead>
          <tr class="font-bold whitespace-nowrap">
            <TableHeader
              :headers="tableHeader"
              :sortBy="sortParams.sortBy!"
              :sortDirection="sortParams.sortDirection!"
              :onSort="onSort"
              ><template v-slot:customHeader="{ header }">
                <div v-if="header.label === ''">
                  <input
                    class="h-4 w-4"
                    type="checkbox"
                    v-model="checkAll"
                    @change="onToggleCheckAll"
                  />
                </div>
                <div v-else class="p-4">
                  {{ header.label }}
                </div>
              </template></TableHeader
            >
          </tr>
        </thead>
        <!--end::Table head-->

        <!--begin::Table body-->
        <tbody>
          <template v-for="item in userList" :key="item.id">
            <tr
              class="font-bold my-2 text-center border-b-[1px] border-light-border border-dashed"
            >
              <td v-if="isAdmin()">
                <input
                  class="h-4 w-4"
                  type="checkbox"
                  :value="item.id"
                  v-model="checkedRows"
                />
              </td>

              <td class="w-36 p-4">
                <div class="flex flex-row items-center gap-3">
                  <img
                    class="h-11 w-11 rounded-full"
                    :src="item?.avatar || '/media/avatars/blank.png'"
                    alt=""
                  />
                  <div class="flex flex-col text-start truncate">
                    <router-link
                      :to="`/users/${item?.id}`"
                      class="text-link hover:text-link-hover font-semibold"
                    >
                      {{ `${item?.firstName} ${item?.lastName}` }}</router-link
                    >
                    <span class="font-semibold">{{ item?.email }}</span>
                  </div>
                </div>
              </td>

              <td>
                <span class="font-semibold">{{ item?.mobilePhone }}</span>
              </td>
              <td>
                <span class="font-semibold">{{ item?.officePhone }}</span>
              </td>
              <td>
                <span class="font-semibold">{{
                  formatDate(item?.assignedDate, "MMM DD, YYYY")
                }}</span>
              </td>

              <td v-if="isAdmin()">
                <div
                  class="h-auto w-full flex flex-row items-center justify-evenly gap-2"
                >
                  <button
                    class="bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover font-semibold h-8 w-8 flex items-center justify-center rounded-lg pl-[7.5px] pt-[1px] md:h-10 md:w-10 md:pl-[3px] md:pt-[5px] md:pb-[3.75px]"
                    @click="view(item?.id ?? '')"
                    item.addedDate
                  >
                    <SvgIcon icon="eyeIcon" classname="md:h-6 md:w-6" />
                  </button>
                  <button
                    class="bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover font-semibold h-8 w-8 flex items-center justify-center rounded-lg pl-[7.5px] pt-[1px] md:h-10 md:w-10 md:pl-[3px] md:pb-[6px]"
                    @click="deleteUser(item?.id!)"
                  >
                    <span class="text-danger">
                      <SvgIcon icon="trashIcon" classname="md:h-7 md:w-7" />
                    </span>
                  </button>
                </div>
              </td>
            </tr>
          </template>
        </tbody>
        <!--end::Table body-->
      </table>
      <!--end::Table-->
    </div>
    <div class="flex flex-col items-center my-5">
      <div v-if="userList?.length" class="font-semibold">
        {{
          `Showing ${(currentPage - 1) * 10 + 1} to ${
            userList?.length
          } of ${totalElements} entries`
        }}
      </div>
      <TablePagination
        v-if="pageCount >= 1"
        :total-pages="pageCount"
        :total="totalElements"
        :per-page="10"
        :current-page="currentPage"
        @page-change="pageChange"
      />
    </div>
  </div>
  <AssignUserModal
    ref="addUserModal"
    :title="currentTab?.titleModal || ''"
    :userList="userListForAddUserModal"
    :onAdd="onAdd"
    :onSearch="onSearchForAddUserModal"
    :initialAddUserValue="initialAddUserValue"
    :loadingSearch="loadingUserListForAddUserModal"
    :isVisible="isModalVisible"
    :close="toggleModal"
  />
</template>

<script lang="ts">
import AssignUserModal from "@/components/AssignUserModal.vue";
import PageHeader from "@/components/PageHeader.vue";
import TablePagination from "@/components/common/TablePagination.vue";
import SvgIcon from "@/constants/SvgIcon.vue";
import TableHeader from "@/components/common/TableHeader.vue";
import { SortByEnum, SortDirectionEnum } from "@/constants/table";
import { CompanyUser, UserType } from "@/constants/user";
import { formatDate } from "@/utils/date";
import AlertService from "@/services/AlertService";
import JwtService, { isAdmin } from "@/services/JwtService";
import { useCompanyStore } from "@/stores/company";
import { useUserStore } from "@/stores/user";
import {
  computed,
  defineComponent,
  onMounted,
  ref,
  type Ref,
  watch,
} from "vue";
import { useRoute, useRouter } from "vue-router";

const tabs = [
  {
    value: UserType.CompanyAdmin,
    key: "Admins",
    titleModal: "Add Company Admins",
    initialAddUserValue: { roles: [UserType.CompanyAdmin] },
  },
  {
    value: UserType.Supervisor,
    key: "Supervisors",
    titleModal: "Add Company Supervisors",
    initialAddUserValue: { roles: [UserType.Supervisor] },
  },
  {
    value: UserType.Engineer,
    key: "Engineers",
    titleModal: "Add Company Engineers",
    initialAddUserValue: { roles: [UserType.Engineer] },
  },
  {
    value: 5,
    key: CompanyUser.Customer,
    titleModal: "Add Customer",
  },
];

export default defineComponent({
  name: "user-list",
  components: {
    PageHeader,
    SvgIcon,
    TablePagination,
    AssignUserModal,
    TableHeader,
  },
  props: {
    listType: {
      type: Number,
      required: true,
    },
  },
  setup(props) {
    const route = useRoute();
    const companyStore = useCompanyStore();
    const userStore = useUserStore();
    const router = useRouter();
    const checkedRows = ref<Array<string>>([]);
    const checkAll = ref<boolean>(false);
    const pageCount = ref(0);
    const totalElements = ref(0);
    const currentPage = ref(1);
    const loading = ref(false);
    const isShowModal = ref(false);
    const search = ref<string>("");
    const addUserModal: Ref<any> = ref<typeof AssignUserModal | null>(null);
    const loadingUserListForAddUserModal = ref(false);
    const userListForAddUserModal = ref<User.Info[]>([]);
    const currentTab = ref(tabs.find((tab) => tab.value === props.listType));
    const userList = ref<User.Info[]>([]);
    const isMyCompany = route.name === "my-company";
    const sortParams = ref<Company.GetFilter>({
      sortDirection: SortDirectionEnum.ASC,
      sortBy: SortByEnum.Name,
    });
    const isModalVisible = ref(false);

    const tableHeader: Table.ColumnHeader[] = [
      { label: "", class: "w-25px", display: isAdmin() },
      { label: "FULL NAME", sortBy: SortByEnum.Name, class: "min-w-150px" },
      { label: "MOBILE NUMBER", class: "min-w-120px" },
      { label: "OFFICE NUMBER", class: "min-w-120px" },
      {
        label: "ADDED DATE",
        sortBy: SortByEnum.AssignedDate,
        class: "min-w-120px",
      },
      { label: "ACTIONS", class: "min-w-60px", display: isAdmin() },
    ];

    onMounted(() => {
      getCompanyUserList();
      getUserListForAssignUserModal();
    });

    const initialAddUserValue = computed(() => {
      return {
        companyId: isMyCompany
          ? JwtService.getUserInfo()?.companyId
          : route.params?.id?.toString(),
        userRoles: currentTab?.value?.initialAddUserValue?.roles || [],
      };
    });

    watch(props, (newValue) => {
      currentTab.value = tabs.find((tab) => tab.value === newValue.listType);
      checkedRows.value = [];
      search.value = "";
      currentPage.value = 1;
      getCompanyUserList();
    });

    watch(checkedRows, (newValue) => {
      checkAll.value =
        userList.value.length !== 0 &&
        newValue.length === userList.value.length;
    });

    watch(currentPage, () => {
      getCompanyUserList();
    });

    const getCompanyUserList = async (): Promise<void> => {
      loading.value = true;
      companyStore.getCompanyUsers({
        params: {
          name: search.value.trim() || null,
          companyId: isMyCompany
            ? JwtService.getUserInfo()?.companyId
            : route.params?.id?.toString(),
          role: props.listType,
          page: currentPage.value,
          limit: 10,
          ...sortParams.value,
        },
        callback: {
          onSuccess: (res: any) => {
            userList.value = [...res?.items];
            pageCount.value = res?.totalPage;
            totalElements.value = res?.total;
            currentPage.value = res?.page;
          },
          onFinish: () => {
            loading.value = false;
          },
        },
      });
    };

    const onSort = (newSortValue: Filter.FilterForm) => {
      sortParams.value = { ...newSortValue };

      getCompanyUserList();
    };

    const getUserListForAssignUserModal = async (
      name: string = ""
    ): Promise<void> => {
      loadingUserListForAddUserModal.value = true;
      userStore.getUsers({
        params: {
          name: name.trim() || null,
          companyId: isMyCompany
            ? JwtService.getUserInfo()?.companyId
            : route.params?.id?.toString(),
          page: 1,
          limit: 500,
        },
        callback: {
          onSuccess: (res: any) => {
            userListForAddUserModal.value = [...res?.items];
          },
          onFinish: () => {
            loadingUserListForAddUserModal.value = false;
          },
        },
      });
    };

    const toggleNewUser = (): void => {
      isModalVisible.value = !isModalVisible.value;
    };

    const toggleModal = (): void => {
      isModalVisible.value = !isModalVisible.value;
    };

    const pageChange = (newPage: number) => {
      currentPage.value = newPage;
    };
    const toggleFilter = (): void => {
      isShowModal.value = !isShowModal.value;
    };

    const onToggleCheckAll = (e: any) => {
      if (e?.target?.checked) {
        checkedRows.value = userList.value.map((user) => user.id!);
      } else {
        checkedRows.value = [];
      }
    };

    const view = (id: string) => {
      router.push({ path: `/users/${id}` });
    };

    const onAdd = (list: string[]) => {
      addUsers(list);
      addUserModal?.value?.closeModal();
    };

    // const onAdd = (list: any[]) => {
    //   SwalPopup.toast("Added successfully", "success", "top-right");
    //   addUserModal?.value?.closeModal();
    // };

    const onSearchForAddUserModal = (query: string) => {
      getUserListForAssignUserModal(query);
    };

    const searchUser = () => {
      if (currentPage.value !== 1) {
        currentPage.value = 1;
      } else {
        getCompanyUserList();
      }
    };

    const onRemove = () => {
      AlertService.deletionAlert({
        onConfirmed: () => {
          removeUsers(checkedRows.value, true);
        },
      });
    };

    const deleteUser = (id: string) => {
      AlertService.deletionAlert({
        onConfirmed: () => {
          removeUsers([id]);
        },
      });
    };

    const removeUsers = async (
      userIds: string[],
      clearRemoveList = false
    ): Promise<void> => {
      loading.value = true;

      companyStore.removeUserListOfCompany({
        params: {
          userIds,
          role: props.listType,
        },
        callback: {
          onSuccess: (_res: any) => {
            if (clearRemoveList) {
              checkedRows.value = [];
            }
            getCompanyUserList();
          },
          onFinish: () => {
            loading.value = false;
          },
        },
      });
    };

    const addUsers = async (userIds: string[]): Promise<void> => {
      loading.value = true;

      companyStore.addUserListToCompany({
        params: {
          companyId: isMyCompany
            ? JwtService.getUserInfo()?.companyId
            : route.params?.id?.toString(),
          role: props.listType,
          userIds,
        },
        callback: {
          onSuccess: (_res: any) => {
            getCompanyUserList();
            AlertService.toast("Added successfully", "success", "top-right");
          },
          onFinish: () => {
            loading.value = false;
          },
        },
      });
    };
    return {
      sortParams,
      tableHeader,
      search,
      loading,
      checkedRows,
      checkAll,
      userList,
      currentPage,
      totalElements,
      pageCount,
      isShowModal,
      addUserModal,
      userListForAddUserModal,
      tabs,
      currentTab,
      initialAddUserValue,
      loadingUserListForAddUserModal,
      isModalVisible,
      onSort,
      pageChange,
      toggleFilter,
      deleteUser,
      toggleNewUser,
      formatDate,
      view,
      onAdd,
      onSearchForAddUserModal,
      onRemove,
      isAdmin,
      onToggleCheckAll,
      searchUser,
      toggleModal,
    };
  },
});
</script>

<style>
.el-form-item {
  margin-bottom: 0px;
}
</style>
