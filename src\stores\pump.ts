import ApiService from "../services/ApiService";
import { get, noop } from "lodash";
import { defineStore } from "pinia";
import { handleFailure } from "../utils/handleFailure";
import type { Callback } from "../types/common";

export const usePumpStore = defineStore("pump", () => {
  const getPumps = async ({
    params,
    callback,
  }: {
    params: any;
    callback: Callback;
  }): Promise<void> => {
    const onSuccess = get(callback, "onSuccess", noop);
    const onFinish = get(callback, "onFinish", noop);

    try {
      ApiService.setHeader();
      const response = await ApiService.getWithParams("pumps", params);
      onSuccess(response.data?.data || response.data);
    } catch (error) {
      handleFailure(error, callback);
    } finally {
      onFinish();
    }
  };

  const getPumpDetails = async ({
    id,
    callback,
  }: {
    id: string;
    callback: Callback;
  }): Promise<void> => {
    const onSuccess = get(callback, "onSuccess", noop);
    const onFinish = get(callback, "onFinish", noop);

    try {
      ApiService.setHeader();
      const response = await ApiService.get(`pumps/${id}`);
      onSuccess(response.data?.data || response.data);
    } catch (error) {
      handleFailure(error, callback);
    } finally {
      onFinish();
    }
  };

  const updatePump = async ({
    id,
    params,
    callback,
  }: {
    id: string;
    params: any;
    callback: Callback;
  }): Promise<void> => {
    const onSuccess = get(callback, "onSuccess", noop);
    const onFinish = get(callback, "onFinish", noop);

    try {
      ApiService.setHeader();
      const response = await ApiService.put(`pumps/${id}`, params);
      onSuccess(response.data?.data || response.data);
    } catch (error) {
      handleFailure(error, callback);
    } finally {
      onFinish();
    }
  };

  const deletePump = async ({
    id,
    callback,
  }: {
    id: string;
    callback: Callback;
  }): Promise<void> => {
    const onSuccess = get(callback, "onSuccess", noop);
    const onFinish = get(callback, "onFinish", noop);

    try {
      ApiService.setHeader();
      const response = await ApiService.delete(`pumps/${id}`);
      onSuccess(response.data?.data || response.data);
    } catch (error) {
      handleFailure(error, callback);
    } finally {
      onFinish();
    }
  };

  const createPump = async ({
    params,
    callback,
  }: {
    params: any;
    callback: Callback;
  }): Promise<void> => {
    const onSuccess = get(callback, "onSuccess", noop);
    const onFinish = get(callback, "onFinish", noop);

    try {
      ApiService.setHeader();
      const response = await ApiService.post("pumps", params);
      onSuccess(response.data?.data || response.data);
    } catch (error) {
      handleFailure(error, callback);
    } finally {
      onFinish();
    }
  };

  const getPumpSummary = async ({
    dailyReportId,
    callback,
  }: {
    dailyReportId: string;
    callback: Callback;
  }): Promise<void> => {
    const onSuccess = get(callback, "onSuccess", noop);
    const onFinish = get(callback, "onFinish", noop);

    try {
      ApiService.setHeader();
      const response = await ApiService.get(`pumps/summary/${dailyReportId}`);
      onSuccess(response.data?.data || response.data);
    } catch (error) {
      handleFailure(error, callback);
    } finally {
      onFinish();
    }
  };

  return {
    getPumpSummary,
    getPumps,
    getPumpDetails,
    updatePump,
    deletePump,
    createPump,
  };
});
