import{O as d,P as s,Q as a}from"./index-BmHWvWFS.js";import{h as c}from"./handleFailure-WBgBpurp.js";const H=d("company",()=>({getCompanies:async({params:r,callback:n})=>{var e;const t=s.get(n,"onSuccess",s.noop),i=s.get(n,"onFinish",s.noop);try{a.setHeader();const o=await a.getWithParams("companies",r);t(((e=o.data)==null?void 0:e.data)||o.data)}catch(o){c(o,n)}finally{i()}},getCompanyById:async({id:r,callback:n})=>{var e;const t=s.get(n,"onSuccess",s.noop),i=s.get(n,"onFinish",s.noop);try{a.setHeader();const o=await a.get(`companies/${r}`);t(((e=o.data)==null?void 0:e.data)||o.data)}catch(o){c(o,n)}finally{i()}},deleteCompanyById:async({id:r,callback:n})=>{var e;const t=s.get(n,"onSuccess",s.noop),i=s.get(n,"onFinish",s.noop);try{a.setHeader();const o=await a.delete(`companies/${r}`);t(((e=o.data)==null?void 0:e.data)||o.data)}catch(o){c(o,n)}finally{i()}},createCompany:async({params:r,callback:n})=>{var e;const t=s.get(n,"onSuccess",s.noop),i=s.get(n,"onFinish",s.noop);try{a.setHeader();const o=await a.post("companies",r);t(((e=o.data)==null?void 0:e.data)||o.data)}catch(o){c(o,n)}finally{i()}},editCompany:async({id:r,params:n,callback:t})=>{var o;const i=s.get(t,"onSuccess",s.noop),e=s.get(t,"onFinish",s.noop);try{a.setHeader();const p=await a.put(`companies/${r}`,n);i(((o=p.data)==null?void 0:o.data)||p.data)}catch(p){c(p,t)}finally{e()}},getCompanyUsers:async({params:r,callback:n})=>{var e;const t=s.get(n,"onSuccess",s.noop),i=s.get(n,"onFinish",s.noop);try{a.setHeader();const o=await a.getWithParams("companies/users/list",r);t(((e=o.data)==null?void 0:e.data)||o.data)}catch(o){c(o,n)}finally{i()}},addUserListToCompany:async({params:r,callback:n})=>{var e;const t=s.get(n,"onSuccess",s.noop),i=s.get(n,"onFinish",s.noop);try{a.setHeader();const o=await a.post("companies/users/add",r);t(((e=o.data)==null?void 0:e.data)||o.data)}catch(o){c(o,n)}finally{i()}},removeUserListOfCompany:async({params:r,callback:n})=>{var e;const t=s.get(n,"onSuccess",s.noop),i=s.get(n,"onFinish",s.noop);try{a.setHeader();const o=await a.post("companies/users/remove",r);t(((e=o.data)==null?void 0:e.data)||o.data)}catch(o){c(o,n)}finally{i()}}}));export{H as u};
