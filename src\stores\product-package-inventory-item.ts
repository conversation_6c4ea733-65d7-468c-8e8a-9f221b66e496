import ApiService from "../services/ApiService";
import { get, noop } from "lodash";
import { defineStore } from "pinia";
import { handleFailure } from "../utils/handleFailure";
import type { Callback } from "../types/common";

export const useProductPackageItemStore = defineStore(
  "productPackageItem",
  () => {
    const getProductPackageItems = async ({
      params,
      callback,
    }: {
      params: any;
      callback: Callback;
    }): Promise<void> => {
      const onSuccess = get(callback, "onSuccess", noop);
      const onFinish = get(callback, "onFinish", noop);

      try {
        ApiService.setHeader();
        const response = await ApiService.getWithParams(
          `productAndPackageInventoryItems`,
          params
        );
        onSuccess(response.data?.data || response.data);
      } catch (error) {
        handleFailure(error, callback);
      } finally {
        onFinish();
      }
    };

    const getProductPackageItemDetails = async ({
      id,
      callback,
    }: {
      id: string;
      callback: Callback;
    }): Promise<void> => {
      const onSuccess = get(callback, "onSuccess", noop);
      const onFinish = get(callback, "onFinish", noop);

      try {
        ApiService.setHeader();
        const response = await ApiService.get(
          `productAndPackageInventoryItems/${id}`
        );
        onSuccess(response.data?.data || response.data);
      } catch (error) {
        handleFailure(error, callback);
      } finally {
        onFinish();
      }
    };

    const createProductPackageItem = async ({
      params,
      callback,
    }: {
      params: any;
      callback: Callback;
    }): Promise<void> => {
      const onSuccess = get(callback, "onSuccess", noop);
      const onFinish = get(callback, "onFinish", noop);

      try {
        ApiService.setHeader();
        const response = await ApiService.post(
          `productAndPackageInventoryItems`,
          params
        );
        onSuccess(response.data?.data || response.data);
      } catch (error) {
        handleFailure(error, callback);
      } finally {
        onFinish();
      }
    };

    const updateProductPackageItem = async ({
      id,
      params,
      callback,
    }: {
      id: string;
      params: any;
      callback: Callback;
    }): Promise<void> => {
      const onSuccess = get(callback, "onSuccess", noop);
      const onFinish = get(callback, "onFinish", noop);

      try {
        ApiService.setHeader();
        const response = await ApiService.put(
          `productAndPackageInventoryItems/${id}`,
          params
        );
        onSuccess(response.data?.data || response.data);
      } catch (error) {
        handleFailure(error, callback);
      } finally {
        onFinish();
      }
    };

    const deleteProductPackageItem = async ({
      id,
      callback,
    }: {
      id: string;
      callback: Callback;
    }): Promise<void> => {
      const onSuccess = get(callback, "onSuccess", noop);
      const onFinish = get(callback, "onFinish", noop);

      try {
        ApiService.setHeader();
        const response = await ApiService.delete(
          `productAndPackageInventoryItems/${id}`
        );
        onSuccess(response.data?.data || response.data);
      } catch (error) {
        handleFailure(error, callback);
      } finally {
        onFinish();
      }
    };

    return {
      getProductPackageItems,
      getProductPackageItemDetails,
      createProductPackageItem,
      updateProductPackageItem,
      deleteProductPackageItem,
    };
  }
);
