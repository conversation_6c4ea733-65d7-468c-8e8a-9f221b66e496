import { UserType } from "../constants/user";
import { VITE_APP_NAME } from "../constants/envKey";
import AlertService from "../services/AlertService";
import JwtService, { getUserRoles } from "../services/JwtService";
import { useAuthStore } from "../stores/auth";
import {
  createRouter,
  createWebHistory,
  type RouteRecordRaw,
} from "vue-router";

const routes: Array<RouteRecordRaw> = [
  {
    path: "/",
    redirect: "/wells/overview",
    name: "home",
    component: () => import("../layouts/main-layout/MainLayout.vue"),
    meta: { middleware: "auth" },
    children: [
      {
        path: "/wells/overview",
        name: "wells-overview",
        component: () => import("../views/home/<USER>/overview/Overview.vue"),
        meta: { pageTitle: "Home" },
      },
      {
        path: "/wells/new",
        name: "well-new",
        component: () => import("../views/home/<USER>/NewWell.vue"),
        meta: { pageTitle: "New Well" },
      },
      {
        path: "/wells/:id",
        name: "well-detail",
        component: () => import("../views/home/<USER>/details/WellDetail.vue"),
        meta: { pageTitle: "Well Details" },
      },
      {
        path: "/wells/:id/daily-report",
        name: "new-well-detail-report",
        component: () =>
          import("../views/home/<USER>/daily-report/DailyReport.vue"),
        meta: { pageTitle: "Daily Report" },
      },
      {
        path: "/wells/:id/daily-report/:dailyReportId",
        name: "edit-well-detail-report",
        component: () =>
          import("../views/home/<USER>/daily-report/DailyReport.vue"),
        meta: { pageTitle: "Daily Report" },
      },
      {
        path: "/settings",
        name: "setting",
        component: () => import("../views/settings/Setting.vue"),
        meta: {
          pageTitle: "Settings",
          roles: [
            UserType.SystemAdmin,
            UserType.CompanyAdmin,
            UserType.Supervisor,
          ],
        },
      },
      {
        path: "/companies",
        name: "companies",
        component: () => import("../views/company/overview/Overview.vue"),
        meta: { pageTitle: "Companies", roles: [UserType.SystemAdmin] },
        children: [],
      },
      {
        path: "/companies/:id",
        name: "company-detail",
        component: () => import("../views/company/detail/Overview.vue"),
        meta: { pageTitle: "Company Details", roles: [UserType.SystemAdmin] },
      },
      {
        path: "/companies/:id/customer/:customerId",
        name: "customer-detail",
        component: () => import("../views/company/customer/Overview.vue"),
        meta: { pageTitle: "Customer Details", roles: [UserType.SystemAdmin] },
      },
      {
        path: "/my-company",
        name: "my-company",
        component: () => import("../views/company/detail/MyCompany.vue"),
        meta: {
          pageTitle: "My Company",
          roles: [
            UserType.CompanyAdmin,
            UserType.Supervisor,
            UserType.Engineer,
          ],
        },
      },
      {
        path: "/my-company/customer/:customerId",
        name: "my-company-customer-detail",
        component: () => import("../views/company/customer/Overview.vue"),
        meta: {
          pageTitle: "Customer Details",
          roles: [
            UserType.CompanyAdmin,
            UserType.Supervisor,
            UserType.Engineer,
          ],
        },
      },
      {
        path: "/users",
        name: "users",
        component: () =>
          import("../views/user-management/overview/Overview.vue"),
        meta: { pageTitle: "User Management" },
      },
      {
        path: "/my-profile",
        name: "my-profile",
        component: () => {
          const userRoles: { value: number }[] =
            JwtService?.getUserInfo()?.roles || [];
          if (
            userRoles.length === 1 &&
            userRoles[0].value === UserType.SystemAdmin
          ) {
            return import(
              "../views/user-management/profile/SystemAdminProfile.vue"
            );
          }
          return import("../views/user-management/profile/MyProfile.vue");
        },
        meta: { pageTitle: "My Profile" },
      },
      {
        path: "/users/:id",
        name: "user-profile",
        component: () =>
          import("../views/user-management/profile/UserProfile.vue"),
        meta: { pageTitle: "User Profile" },
      },
    ],
  },
  {
    path: "/",
    component: () => import("../layouts/AuthLayout.vue"),
    meta: { middleware: "guess" },
    children: [
      {
        path: "/sign-in",
        name: "sign-in",
        component: () => import("../views/authentication/SignIn.vue"),
        meta: { pageTitle: "Sign In" },
      },
      {
        path: "/sign-up",
        name: "sign-up",
        component: () => import("../views/authentication/SignUp.vue"),
        meta: { pageTitle: "Sign Up" },
      },
      {
        path: "/forgot-password",
        name: "forgot-password",
        component: () => import("../views/authentication/ForgotPassword.vue"),
        meta: { pageTitle: "Forgot Password" },
      },
      {
        path: "/users/user-reset-password/:token",
        name: "user-reset-password",
        component: () =>
          import("../views/authentication/UserResetPassword.vue"),
        meta: { pageTitle: "User Reset Password", promptLogout: true },
      },
      {
        path: "/reset-password",
        name: "reset-password",
        component: () => import("../views/authentication/ResetPassword.vue"),
        meta: { pageTitle: "Reset Password" },
      },
    ],
  },
  {
    path: "/",
    component: () => import("../layouts/AuthLayout.vue"),
    meta: { middleware: "auth" },
    children: [
      {
        path: "/register-invited-user",
        name: "register-invited-user",
        component: () =>
          import("../views/authentication/RegisterInvitedUser.vue"),
        meta: { pageTitle: "Register" },
      },
    ],
  },
  {
    path: "/",
    component: () => import("../layouts/SystemLayout.vue"),
    children: [
      {
        // the 404 route, when none of the above matches
        path: "/404",
        name: "404",
        component: () => import("../views/system/Error404.vue"),
        meta: { pageTitle: "Error 404" },
      },
      {
        path: "/500",
        name: "500",
        component: () => import("../views/system/Error500.vue"),
        meta: { pageTitle: "Error 500" },
      },
      {
        path: "/no-role",
        name: "no-role",
        component: () => import("../views/system/NoRole.vue"),
        meta: { pageTitle: "No Role" },
      },
      {
        path: "/accept-invite/:token",
        name: "accept-invite",
        component: () => import("../views/system/AcceptInvite.vue"),
        meta: { pageTitle: "Accept Invitation", promptLogout: true },
      },
    ],
  },
  { path: "/:pathMatch(.*)*", redirect: "/404" },
];

const router = createRouter({
  history: createWebHistory(),
  routes,
});

router.beforeEach((to, _from, next) => {
  const authStore = useAuthStore();
  // current page view title
  document.title = `${to?.meta?.pageTitle || ""} - ${VITE_APP_NAME}`;

  // verify auth token before each page change
  // authStore.verifyAuth();

  // before page access check if page requires authentication
  if (to?.meta?.promptLogout === true) {
    if (authStore.isAuthenticated) {
      AlertService.alert(
        "Do you want to log out of the current account to continue??",
        {
          confirmButtonText: "Yes, log out!",
          cancelButtonText: "No, cancel!",
          confirmButtonClass: "btn fw-bold btn-primary btn-sm",
          cancelButtonClass: "btn fw-bold btn-blue btn-sm",
          callback: {
            onConfirmed: () => {
              authStore.purgeAuth();
              next();
            },
            onCanceled: () => {
              next({ name: "home" });
            },
          },
        },
        "warning"
      );
    } else {
      next();
    }
  } else if (to?.meta?.middleware === "auth") {
    if (authStore.isAuthenticated) {
      const userRoles = JwtService?.getUserInfo()?.roles || [];

      if (userRoles.length === 0) {
        next({ name: "no-role" });
      } else if (
        to?.meta?.roles &&
        !checkPermission(to?.meta?.roles as UserType[])
      ) {
        next({ name: "404" });
      } else {
        next();
      }
    } else {
      next({ name: "sign-in" });
    }
  } else if (to?.meta?.middleware === "guess") {
    if (!authStore.isAuthenticated) {
      next();
    } else {
      next({ name: "home" });
    }
  } else {
    next();
  }

  // Scroll page to top on every route change
  window.scrollTo({
    top: 0,
    left: 0,
    behavior: "smooth",
  });
});

const checkPermission = (roleList: UserType[]): boolean => {
  const userRoles = getUserRoles() || [];
  const setRoleList = new Set(roleList);
  for (let item of userRoles) {
    if (setRoleList.has(item?.value)) {
      return true;
    }
  }
  return false;
};

export default router;
