declare namespace Well {
  export type GetParams = {
    keyword?: string | null;
    page?: number | null;
    limit?: number | null;
    archived?: boolean;
  };

  export interface GeneralInfo {
    id?: string | null;
    stateOrProvinceId?: string | null;
    countryId?: string | null;
    nameOrNo?: string | null;
    apiWellNo?: string | null;
    latitude?: number | null;
    longitude?: number | null;
    fieldOrBlock?: string | null;
    sectionOrTownshipOrRange?: string | null;
    countyOrParishOrOffshoreArea?: string | null;
    rigName?: string | null;
    spudDate?: string | null;
    stockPoint?: string | null;
    stockPointContact?: string | null;
    operator?: string | null;
    contractor?: string | null;
    kickOffPoint?: number | null;
    landingPoint?: number | null;
    seaLevel?: number | null;
    airGap?: number | null;
    waterDepth?: number | null;
    riserId?: number | null;
    riserOD?: number | null;
    chokeLineId?: number | null;
    killLineId?: number | null;
    boostLineId?: number | null;
    rateOfPenetration?: boolean;
    revolutionsPerMinute?: boolean;
    eccentricity?: boolean;
    archived?: boolean;
    company?: Company.Info;
    users?: User.Info[];
    customers?: Customer.Info[];
    dailyReport?: ReportInfo;
    userIds?: string[];
    customerIds?: string[];

    [element: string]: any;
  }

  export type ReportInfo = {
    id?: string;
    nameOrNo?: string;
    dateReport?: string;
    assignEngineers?: Array<string>;
    activity?: string;
    measuredDepth?: number | null;
    trueVerticalDepth?: number | null;
    inclination: number | null;
    azimuth: number | null;
    weightOnBit: number | null;
    rotaryWeight: number | null;
    standoffWeight?: number | null;
    pullUpWeight: number | null;
    revolutionsPerMinute: number | null;
    rateOfPenetration: number | null;
    drillingInterval: string;
    formation: string;
    depthDrilled: number | null;
    totalStringLength: number | null;
    totalLength: number | null;
    totalFlowArea?: number | null;
    tfa: number | null;
    [element: string]: any;
  };
}
