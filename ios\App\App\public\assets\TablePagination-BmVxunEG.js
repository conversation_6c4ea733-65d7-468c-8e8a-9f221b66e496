import{d as b,R as i,_ as f,c as P,o as c,a as s,a7 as r,n as o,F as v,k as y,t as F}from"./index-CGNRhvz7.js";import{_ as I}from"./d-left-arrow-079-B3YbfCzd.js";const L=b({name:"table-pagination",props:{maxVisibleButtons:{type:Number,required:!1,default:5},totalPages:{type:Number,required:!0},total:{type:Number,required:!0},perPage:{type:Number,required:!0},currentPage:{type:Number,required:!0}},emits:["page-change"],setup(e,{emit:a}){const l=i(()=>e.totalPages<e.maxVisibleButtons||e.currentPage===1||e.currentPage<=Math.floor(e.maxVisibleButtons/2)||e.currentPage+2>e.totalPages&&e.totalPages===e.maxVisibleButtons?1:e.currentPage+2>e.totalPages?e.totalPages-e.maxVisibleButtons+1:e.currentPage-2),u=i(()=>Math.min(l.value+e.maxVisibleButtons-1,e.totalPages)),m=i(()=>{const n=[];for(let g=l.value;g<=u.value;g+=1)n.push({name:g,isDisabled:g===e.currentPage});return n}),d=i(()=>e.currentPage===1),t=i(()=>e.currentPage===e.totalPages);return{startPage:l,endPage:u,pages:m,isInFirstPage:d,isInLastPage:t,onClickFirstPage:()=>{a("page-change",1)},onClickPreviousPage:()=>{a("page-change",e.currentPage-1)},onClickPage:n=>{a("page-change",n)},onClickNextPage:()=>{a("page-change",e.currentPage+1)},onClickLastPage:()=>{a("page-change",e.totalPages)},isPageActive:n=>e.currentPage===n}}}),N="/media/icons/arrows/s-left-arrow-074.svg",w="/media/icons/arrows/s-right-arrow-071.svg",B="/media/icons/arrows/d-right-arrow-080.svg",V={class:"h-auto w-full flex flex-row gap-2 justify-between"},q=["onClick"];function $(e,a,l,u,m,d){return c(),P("ul",V,[s("li",{class:o({disabled:e.isInFirstPage}),style:r({cursor:e.isInFirstPage?"auto":"pointer"})},[s("a",{onClick:a[0]||(a[0]=(...t)=>e.onClickFirstPage&&e.onClickFirstPage(...t))},a[4]||(a[4]=[s("img",{src:I},null,-1)]))],6),s("li",{class:o({disabled:e.isInFirstPage}),style:r({cursor:e.isInFirstPage?"auto":"pointer"})},[s("a",{onClick:a[1]||(a[1]=(...t)=>e.onClickPreviousPage&&e.onClickPreviousPage(...t))},a[5]||(a[5]=[s("img",{src:N},null,-1)]))],6),(c(!0),P(v,null,y(e.pages,(t,k)=>(c(),P("li",{class:o({active:e.isPageActive(t.name)}),style:r({cursor:t.isDisabled?"auto":"pointer"}),key:k},[s("a",{onClick:C=>e.onClickPage(t.name)},F(t.name),9,q)],6))),128)),s("li",{class:o({disabled:e.isInLastPage}),style:r({cursor:e.isInLastPage?"auto":"pointer"})},[s("a",{onClick:a[2]||(a[2]=(...t)=>e.onClickNextPage&&e.onClickNextPage(...t))},a[6]||(a[6]=[s("img",{src:w},null,-1)]))],6),s("li",{class:o(["paginate_button page-item",{disabled:e.isInLastPage}]),style:r({cursor:e.isInLastPage?"auto":"pointer"})},[s("a",{onClick:a[3]||(a[3]=(...t)=>e.onClickLastPage&&e.onClickLastPage(...t))},a[7]||(a[7]=[s("img",{src:B},null,-1)]))],6)])}const j=f(L,[["render",$]]);export{j as T};
