<template>
  <div
    class="h-auto w-4/5 rounded-3xl mx-auto py-6 bg-card-background text-card-text-light rounded-3 align-self-center flex flex-col justify-center items-center md:h-[50vh] md:text-lg lg:max-w-2/5"
  >
    <VForm
      class="flex flex-col items-center w-4/5"
      @submit="onSubmitLogin"
      :validation-schema="login"
      :initial-values="{ email: '', password: '' }"
    >
      <img class="h-30 w-auto" src="/media/logos/opslink-light.png" />

      <div class="text-center mb-10">
        <h1 class="mb-3 font-bold">Log In</h1>
        <div class="font-bold fs-4">
          New here?
          <router-link
            to="/sign-up"
            class="text-link hover:text-link-hover font-bold"
          >
            Create an Account
          </router-link>
        </div>
      </div>

      <div class="flex flex-col mb-8 mx-3 h-auto w-full">
        <label class="form-label tracking-wide font-bold">Email</label>
        <Field
          tabindex="1"
          class="rounded-lg bg-input-background text-input-text-dark pl-3"
          type="text"
          name="email"
          autocomplete="off"
          placeholder="Enter email"
        />
        <div class="text-danger mt-[0.3rem]">
          <ErrorMessage name="email" />
        </div>
      </div>

      <div class="flex flex-col mb-8 mx-3 h-auto w-full">
        <label class="form-label tracking-wide font-bold">Password</label>
        <div class="relative">
          <Field
            tabindex="2"
            class="w-full rounded-lg bg-input-background text-input-text-dark pl-3"
            :type="isHidePassword ? 'password' : 'input'"
            name="password"
            autocomplete="off"
            placeholder="Enter password"
          />
          <span
            class="text-icon-dark absolute top-[0.3rem] right-1"
            @click="() => setIsHidePassword(!isHidePassword)"
          >
            <SvgIcon
              :icon="isHidePassword ? 'hidePasswordIcon' : 'showPasswordIcon'"
            />
          </span>
        </div>
        <div class="text-danger mt-[0.3rem]">
          <ErrorMessage name="password" />
        </div>
      </div>

      <div class="h-auto w-full flex flex-row mb-8 gap-2 items-center">
        <input class="h-4 w-4" type="checkbox" v-model="isRemember" />
        <label class="font-semibold cursor-pointer text-xs whitespace-nowrap md:text-lg">
          Remember me
        </label>
        <router-link
          to="/forgot-password"
          class="ms-3 text-link hover:text-link-hover font-bold text-xs whitespace-nowrap md:text-lg"
        >
          Forgot Password ?
        </router-link>
      </div>

      <div class="place-self-end">
        <button
          class="bg-button-primary hover:bg-button-primary-hover rounded-md px-2 py-1 md:px-4 md:py-3 md:text-xl"
          type="submit"
          :disabled="loading"
        >
          <span v-if="!loading" class="font-semibold"> Login </span>
          <span v-if="loading" class="indicator-progress">
            Please wait...
            <span
              class="spinner-border spinner-border-sm align-middle ms-2"
            ></span>
          </span>
        </button>
      </div>
    </VForm>
  </div>
</template>

<script lang="ts">
import SvgIcon from "@/constants/SvgIcon.vue";
import { ExceptionMessages } from "@/constants/exceptions";
import { useAuthStore } from "@/stores/auth";
import Swal from "sweetalert2";
import { ErrorMessage, Field, Form as VForm } from "vee-validate";
import { defineComponent, ref } from "vue";
import { useRouter } from "vue-router";
import * as Yup from "yup";

export default defineComponent({
  name: "sign-in",
  components: {
    Field,
    VForm,
    ErrorMessage,
    SvgIcon,
  },
  setup() {
    const store = useAuthStore();
    const router = useRouter();
    const isRemember = ref(false);
    const isHidePassword = ref(true);
    const loading = ref<boolean>(false);

    //login: <EMAIL>
    //pw: B1s@2o2i

    //Create form validation object
    const login = Yup.object().shape({
      email: Yup.string().email().required().label("Email"),
      password: Yup.string().min(4).required().label("Password"),
    });

    //Form submit function
    const onSubmitLogin = async (values: any) => {
      // Clear existing errors
      store.purgeAuth();
      loading.value = true;

      store.login({
        params: values,
        callback: {
          onSuccess: () => {
            router.push({ name: "wells-overview" });
          },
          onFinish: () => {
            loading.value = false;
          },
          onFailure: (error) => {
            Swal.fire({
              text:
                ExceptionMessages.USER_NOT_FOUND ||
                error?.response?.data?.message ||
                error?.message ||
                "Sorry, looks like there are some errors detected, please try again.",
              icon: "error",
              buttonsStyling: false,
              confirmButtonText: "Confirm",
              heightAuto: false,
              customClass: {
                confirmButton:
                  "h-auto w-auto bg-button-primary hover:bg-button-alert-danger-hover text-button-text-light hover:text-button-text-light-hover font-semibold h-8 w-8 rounded-lg px-3 py-3",
              },
            });
          },
        },
      });
    };

    const setIsHidePassword = (value: boolean) => {
      isHidePassword.value = value;
    };

    return {
      setIsHidePassword,
      onSubmitLogin,
      login,
      loading,
      isRemember,
      isHidePassword,
    };
  },
});
</script>

<style>
.custom-checkbox {
  height: 1rem; /* h-4 */
  width: 1rem; /* w-4 */
  color: #9ca3af; /* text-grey-400 (assuming Tailwind default) */
  border: 1px solid #9ca3af; /* border-grey-400 */
  border-radius: 0.125rem; /* rounded-sm */
  outline: none;
}

.custom-checkbox:focus {
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.5); /* focus:ring-blue-300 */
}
</style>
