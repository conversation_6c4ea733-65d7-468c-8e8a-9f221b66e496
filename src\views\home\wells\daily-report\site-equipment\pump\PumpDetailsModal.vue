<template>
  <div
    v-if="isVisible"
    class="fixed top-0 right-0 bottom-0 left-0 z-40 flex items-center"
  >
    <!-- Dark overlay background -->
    <div class="absolute inset-0 bg-sidebar-background opacity-25"></div>
    <div
      class="relative bg-card-background text-card-text-light max-h-11/12 w-11/12 flex flex-col items-center gap-3 mx-auto my-6 p-4 rounded-xl overflow-y-scroll md:overflow-hidden md:w-3/5 lg:w-2/5 lg:h-auto lg:max-h-3/5
"
    >
      <div class="flex flex-row h-auto w-full items-center justify-between">
        <h3 class="text-lg font-bold">Pump Details</h3>
        <span class="cursor-pointer ms-auto" @click="closeModal">
          <SvgIcon icon="closeModalIcon" />
        </span>
      </div>

      <div v-if="loading" class="text-center">
        <div class="spinner-border text-primary" role="status">
          <span class="sr-only">Loading...</span>
        </div>
      </div>
      <div v-else class="h-auto w-full flex flex-col gap-3 font-semibold">
        <h3 class="font-bold">{{ data?.description || "" }}</h3>
        <div class="h-auto w-full flex flex-col gap-3">
          <div
            class="h-auto w-full flex items-center justify-between border-b-[1px] border-dashed pb-3"
          >
            <span>In Use</span>
            <span
              :class="{
                ['text-success']: data?.inUse,
                ['text-danger']: !data?.inUse,
              }"
            >
              {{ data?.inUse ? "Active" : "Inactive" }}
            </span>
          </div>
          <div
            class="h-auto w-full flex items-center justify-between border-b-[1px] border-dashed pb-3"
          >
            <span>Model</span>
            <span>
              {{ data?.model || "" }}
            </span>
          </div>
          <div
            class="h-auto w-full flex items-center justify-between border-b-[1px] border-dashed pb-3"
          >
            <span>Total Duration</span>
            <span>{{ `${data?.totalDurations || "0"} hrs` }}</span>
          </div>
        </div>

        <div class="flex flex-wrap gap-3 items-center justify-center">
          <div class="border border-active border-dashed rounded p-4">
            <div>
              {{
                `${data?.linearID ? numberWithCommas(data?.linearID) : ""} (in)`
              }}
            </div>
            <div class="text-danger">Linear ID</div>
          </div>
          <div class="border border-active border-dashed rounded p-4">
            <div>
              {{ `${data?.rodOD ? numberWithCommas(data?.rodOD) : ""} (in)` }}
            </div>
            <div class="text-primary">Rod OD</div>
          </div>
          <div class="border border-active border-dashed rounded p-4">
            <div>
              {{
                `${
                  data?.strokeLength ? numberWithCommas(data?.strokeLength) : ""
                } (in)`
              }}
            </div>
            <div class="text-primary">Stroke Length</div>
          </div>

          <div class="border border-active border-dashed rounded p-4">
            <div>
              {{
                `${
                  data?.efficiency ? numberWithCommas(data?.efficiency) : ""
                } (%)`
              }}
            </div>
            <div class="text-primary">Efficiency</div>
          </div>
          <div class="border border-active border-dashed rounded p-4">
            <div>
              {{
                `${
                  data?.stroke ? numberWithCommas(data?.stroke) : ""
                } (stroke/min)`
              }}
            </div>
            <div class="text-primary">Stroke</div>
          </div>
          <div class="border border-active border-dashed rounded p-4">
            <div>
              {{
                `${
                  data?.displacement ? numberWithCommas(data?.displacement) : ""
                } (bbl/stroke)`
              }}
            </div>
            <div class="text-primary">Displacement</div>
          </div>
          <div class="border border-active border-dashed rounded p-4">
            <div>
              {{ `${data?.rate ? numberWithCommas(data?.rate) : ""} (gpm)` }}
            </div>
            <div class="text-primary">Rate</div>
          </div>
        </div>

        <p class="font-bold">Time Distribution</p>

        <el-empty v-if="data?.durations?.length === 0" :image-size="100" />
        <div
          v-else
          v-for="item in data?.durations"
          :key="item.id"
          class="h-auto w-full flex flex-row items-center justify-between gap-2"
        >
          <span>
            {{ formatDate(item?.createdAt, "DD MMM YYYY HH:mm") }}
          </span>
          <span>{{ `${item?.durations} hrs` }}</span>
          <div class="flex items-center gap-3">
            <button
              class="p-2 rounded-full bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover"
              @click="() => toggleDurationModal(item?.id)"
            >
              <SvgIcon icon="newReportIcon" />
            </button>
            <button
              class="p-2 rounded-full bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover"
              @click="deleteDuration(item?.id)"
            >
              <span class="text-danger">
                <SvgIcon icon="trashIcon" />
              </span>
            </button>
          </div>
        </div>
      </div>

      <div class="h-auto w-full flex items-center justify-between">
        <button
          type="button"
          class="bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover rounded-md px-4 py-2 font-semibold"
          :disabled="loading"
          @click="closeModal"
        >
          Close
        </button>
        <button
          class="bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover rounded-md px-4 py-2 font-semibold"
          type="button"
          :disabled="loading"
          @click="() => toggleEditPumpModal(data?.id?.toString() || '')"
        >
          Edit Pump
        </button>
        <button
          class="bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover rounded-md px-4 py-2 font-semibold"
          type="button"
          :disabled="loading"
          @click="() => toggleDurationModal()"
        >
          Add Duration
        </button>
      </div>
    </div>
  </div>
  <PumpModal
    :isVisible="isEditPumpModalVisible"
    :close="toggleEditPumpModal"
    ref="pumpModal"
    :loadPage="reloadPage"
  />
  <PumpDurationModal
    :isVisible="isPumpDurationModalVisible"
    :close="toggleDurationModal"
    ref="pumpDurationModal"
    :loadPage="reloadPage"
  />
</template>

<script lang="ts">
import SvgIcon from "@/constants/SvgIcon.vue";
import { formatDate } from "@/utils/date";
import { numberWithCommas } from "@/utils/numberFormatter";
import AlertService from "@/services/AlertService";
import { usePumpStore } from "@/stores/pump";
import { usePumpDurationStore } from "@/stores/pump-duration";
import { defineComponent, ref, watch, type Ref } from "vue";
import PumpDurationModal from "./PumpDurationModal.vue";
import PumpModal from "./PumpModal.vue";

export default defineComponent({
  name: "pump-details-modal",
  components: { SvgIcon, PumpModal, PumpDurationModal },
  props: {
    isVisible: {
      type: Boolean,
      required: true,
    },
    close: {
      type: Function,
      required: true,
    },
    loadPage: {
      type: Function,
      required: false,
    },
  },
  setup(props) {
    const pumpDurationStore = usePumpDurationStore();
    const pumpStore = usePumpStore();
    const modal = ref(false);
    const data = ref<any>(null);
    const loading = ref<boolean>(false);
    const id = ref("");
    const pumpModal: Ref<any> = ref<typeof PumpModal | null>(null);
    const pumpDurationModal: Ref<any> = ref<typeof PumpDurationModal | null>(
      null
    );
    const isEditPumpModalVisible = ref(false);
    const isPumpDurationModalVisible = ref(false);

    watch(id, (newValue) => {
      if (newValue !== "") {
        getPumpDetails();
      }
    });

    watch(
      () => props.isVisible,
      (newValue) => {
        if (newValue == true) {
          getPumpDetails();
        }
      }
    );

    const getPumpDetails = async (): Promise<void> => {
      loading.value = true;
      pumpStore.getPumpDetails({
        id: id.value,
        callback: {
          onSuccess: (res: any) => {
            data.value = { ...res };
          },
          onFinish: (_err: any) => {
            loading.value = false;
          },
        },
      });
    };

    const closeModal = () => {
      props.close();
    };

    const setId = (pumpId: string) => {
      id.value = pumpId.toString();
    };

    const reloadPage = () => {
      getPumpDetails();
      if (props?.loadPage) {
        props.loadPage();
      }
    };

    const toggleDurationModal = (durationId?: string) => {
      pumpDurationModal?.value?.setId(id.value, durationId || "");
      isPumpDurationModalVisible.value = !isPumpDurationModalVisible.value;
    };

    const toggleEditPumpModal = (pumpId: string) => {
      if (!isEditPumpModalVisible.value) {
        pumpModal?.value?.setId(pumpId);
      }
      isEditPumpModalVisible.value = !isEditPumpModalVisible.value;
    };

    const deleteDuration = (id: string) => {
      AlertService.deletionAlert({
        onConfirmed: () => {
          deleteDurationById(id);
        },
      });
    };

    const deleteDurationById = (id: string) => {
      pumpDurationStore.deletePumpDuration({
        id: id,
        callback: {
          onSuccess: (_res: any) => {
            getPumpDetails();
            if (props?.loadPage) {
              props.loadPage();
            }
          },
        },
      });
    };

    return {
      id,
      modal,
      loading,
      data,
      pumpModal,
      pumpDurationModal,
      isEditPumpModalVisible,
      isPumpDurationModalVisible,
      setId,
      formatDate,
      reloadPage,
      numberWithCommas,
      toggleDurationModal,
      toggleEditPumpModal,
      deleteDuration,
      closeModal,
    };
  },
});
</script>

<style scoped>
.border-blue-light {
  border-color: #e4e6ef;
}

.el-dialog__header {
  margin-right: 0px;
}

@media screen and (min-height: 800px) {
  .overflow-hidden-y {
    overflow-y: hidden;
  }
}
</style>
