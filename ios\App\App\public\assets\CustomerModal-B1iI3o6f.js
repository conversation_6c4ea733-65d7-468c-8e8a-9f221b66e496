import{S as M}from"./SvgIcon-CMhyaXWN.js";import{d as q,q as u,A as S,J as B,j as F,_ as P,c as p,l as y,o as C,a as s,b as d,t as $,r as v,w as _,B as V,p as U}from"./index-CGNRhvz7.js";import{u as j}from"./customer-C9SausZF.js";const A=q({name:"customer-modal",components:{SvgIcon:M},props:{isVisible:{type:Boolean,required:!0},close:{type:Function,required:!0},loadPage:{type:Function,required:!1}},setup(e){const o=F(),r=u({customerName:"",notes:""}),n=u(null),i=u(!1),g=j(),a=u(""),m=u("");S(()=>e.isVisible,t=>{t===!1&&w()}),S(a,t=>{t!==""&&b()});const b=async()=>{a.value&&g.getCustomerDetails({id:a.value,callback:{onSuccess:t=>{m.value=t,r.value={customerName:t==null?void 0:t.customerName,notes:t==null?void 0:t.notes}}}})},c=()=>{e.close()},l=t=>{a.value=t},k=u({customerName:[{required:!0,message:"Please type Customer Name",trigger:["blur","change"]}]}),x=()=>{n.value&&n.value.validate(t=>{t&&(a.value?I():D())})},w=()=>{var t;a.value="",r.value={customerName:"",notes:""},(t=n==null?void 0:n.value)==null||t.resetFields()},D=async()=>{var t,f,N;i.value=!0,g.createCustomer({params:{...r.value,companyId:o.name==="my-company"?(t=B.getUserInfo())==null?void 0:t.companyId:(N=(f=o.params)==null?void 0:f.id)==null?void 0:N.toString()},callback:{onSuccess:X=>{var h;c(),(h=e==null?void 0:e.loadPage)==null||h.call(e)},onFinish:()=>{i.value=!1}}})},I=async()=>{a.value&&(i.value=!0,g.updateCustomer({id:a.value,params:r.value,callback:{onSuccess:t=>{var f;c(),(f=e==null?void 0:e.loadPage)==null||f.call(e)},onFinish:()=>{i.value=!1}}}))};return{id:a,rules:k,loading:i,targetData:r,formRef:n,companyDetail:m,submit:x,setId:l,reset:w,closeModal:c}}}),E={key:0,class:"fixed top-0 right-0 bottom-0 left-0 flex flex-row items-center bg-dark z-40"},J={class:"bg-white h-auto w-11/12 flex flex-col items-center gap-3 mx-auto p-5 rounded-xl"},z={class:"h-auto w-full flex flex-row items-center justify-between"},R={class:"text-lg font-bold text-dark"},T={class:"flex flex-col gap-2"},G={class:"flex flex-col gap-2"},H={class:"flex flex-row items-start mt-4 gap-3"},K=["disabled"],L=["disabled"],O={key:0,class:"indicator-label"},Q={key:1,class:"indicator-progress"};function W(e,o,r,n,i,g){const a=v("SvgIcon"),m=v("el-input"),b=v("el-form-item"),c=v("el-form");return e.isVisible?(C(),p("div",E,[s("div",J,[s("div",z,[s("h3",R,$(e.id?"Edit Customer":"Add Customer"),1),s("span",{class:"cursor-pointer",onClick:o[0]||(o[0]=(...l)=>e.closeModal&&e.closeModal(...l))},[d(a,{icon:"closeModalIcon"})])]),d(c,{id:"product_form",onSubmit:U(e.submit,["prevent"]),model:e.targetData,rules:e.rules,ref:"formRef",class:"h-auto w-full"},{default:_(()=>[s("div",T,[o[4]||(o[4]=s("label",{class:"font-semibold"},[V("Customer/Company Name "),s("span",{class:"text-danger-active font-light"},"*")],-1)),d(b,{prop:"customerName"},{default:_(()=>[d(m,{modelValue:e.targetData.customerName,"onUpdate:modelValue":o[1]||(o[1]=l=>e.targetData.customerName=l),placeholder:"",name:"customerName"},null,8,["modelValue"])]),_:1})]),s("div",G,[o[5]||(o[5]=s("label",{class:"font-semibold"},"Note ",-1)),d(b,{prop:"notes"},{default:_(()=>[d(m,{modelValue:e.targetData.notes,"onUpdate:modelValue":o[2]||(o[2]=l=>e.targetData.notes=l),placeholder:"",name:"notes",type:"textarea",rows:3},null,8,["modelValue"])]),_:1})]),s("div",H,[s("button",{type:"button",class:"bg-grey-400 hover:bg-grey-500 px-4 py-2 text-gray-700 rounded-md",onClick:o[3]||(o[3]=(...l)=>e.closeModal&&e.closeModal(...l)),disabled:e.loading}," Discard ",8,K),s("button",{class:"bg-primary px-4 py-2 text-white rounded-md",type:"submit",disabled:e.loading},[e.loading?y("",!0):(C(),p("span",O," Save ")),e.loading?(C(),p("span",Q,o[6]||(o[6]=[V(" Please wait... "),s("span",{class:"spinner-border spinner-border-sm align-middle ms-2"},null,-1)]))):y("",!0)],8,L)])]),_:1},8,["onSubmit","model","rules"])])])):y("",!0)}const te=P(A,[["render",W]]);export{te as C};
