<template>
  <PageHeader :title="wellInfo?.nameOrNo" :breadcrumbs="breadcrumbs" />
  <!--begin::Actions-->
  <div
    class="h-auto w-11/12 mx-auto mb-4 bg-card-background text-card-text-light rounded-xl md:text-lg"
  >
    <div class="h-auto w-11/12 flex flex-col gap-6 p-4">
      <h2 v-if="isSystemAdmin" class="font-bold">
        <span>Company: </span>
        {{ wellInfo?.company?.name }}
      </h2>
      <ul class="flex items-center gap-3" role="tablist">
        <li v-for="tab in wellDetailsTabs" :key="tab.value">
          <a
            class="whitespace-nowrap cursor-pointer font-semibold hover:text-active-hover hover:border-b-2 hover:border-active-border-hover"
            :class="{
              active: tabIndex === tab?.value,
              'text-active border-b-2 border-active-border':
                tabIndex === tab?.value,
              'text-inactive border-b-2 border-inactive-border':
                tabIndex !== tab?.value,
            }"
            @click="handleActiveTab($event)"
            :data-tab-index="tab.value"
            role="tab"
          >
            {{ tab.label }}
          </a>
        </li>
      </ul>
    </div>
    <component :is="currentComponent" :id="wellId" ref="currentChildTab" class="mx-auto pb-4"/>
  </div>
</template>

<script lang="ts">
import PageHeader from "@/components/PageHeader.vue";
import SvgIcon from "@/constants/SvgIcon.vue";
import {
  EWellDetailsTab,
  wellDetailsTabs,
} from "@/constants/daily-report";
import { UserType } from "@/constants/user";
import AlertService from "@/services/AlertService";
import JwtService from "@/services/JwtService";
import { useWellStore } from "@/stores/well";
import { computed, defineComponent, onMounted, ref, type Ref } from "vue";
import { useRoute } from "vue-router";
import WellGeneralInfo from "@/views/home/<USER>/WellGeneralInfo.vue";
import WellInterval from "./WellInterval.vue";
import WellPlan from "./WellPlan.vue";

const tabComponentMap = {
  [EWellDetailsTab.General]: WellGeneralInfo,
  [EWellDetailsTab.Interval]: WellInterval,
  [EWellDetailsTab.Plan]: WellPlan,
};

interface FormComponent {
  submit?: () => void;
  isFormDirty?: () => boolean;
  isValidForm?: () => Promise<boolean>;
  loading?: boolean;
  formRef?: Ref<HTMLFormElement | null>;
}

export default defineComponent({
  name: "well-detail",
  components: {
    SvgIcon,
    WellGeneralInfo,
    WellInterval,
    WellPlan,
    PageHeader,
  },
  setup() {
    const route = useRoute();
    const wellStore = useWellStore();
    const wellInfo = ref<any>();
    const tabIndex = ref<EWellDetailsTab>(EWellDetailsTab.General);
    const breadcrumbs = ["Home", "Well", "Edit"];
    const currentChildTab = ref<FormComponent | null>(null);
    const currentComponent = computed(() => tabComponentMap[tabIndex.value]);
    const wellId = route.params?.id?.toString();

    onMounted(() => {
      if (wellId) {
        getWellDetails(wellId);
      }
    });

    const getWellDetails = async (id: string): Promise<void> => {
      wellStore.getWellDetails({
        wellId: id,
        callback: {
          onSuccess: (res: any) => {
            wellInfo.value = res;
          },
        },
      });
    };

    const setActiveTab = (e: Event) => {
      const target = e.target as HTMLInputElement;
      tabIndex.value = Number(target.getAttribute("data-tab-index"));
    };

    const isTabLoading = () => {
      if (currentChildTab?.value && currentChildTab?.value?.loading) {
        return currentChildTab?.value?.loading;
      }
    };

    const isFormOfChildTabDirty = () => {
      if (currentChildTab?.value && currentChildTab?.value?.isFormDirty) {
        return currentChildTab?.value?.isFormDirty();
      }
    };

    const isFormOfChildValid = async () => {
      if (currentChildTab?.value && currentChildTab?.value?.isValidForm) {
        const result = await currentChildTab?.value?.isValidForm();
        return result;
      } else {
        return null;
      }
    };

    const saveReport = async () => {
      if (currentChildTab?.value && currentChildTab?.value?.submit) {
        currentChildTab.value.submit();
      }
    };

    const handleActiveTab = async (e: Event) => {
      if (tabIndex.value !== EWellDetailsTab.General || isTabLoading()) {
        setActiveTab(e);
      } else {
        //auto-save
        if (!isFormOfChildTabDirty()) {
          // no change
          setActiveTab(e);
          return;
        }
        const result = await isFormOfChildValid();
        if (result === null) {
          // tab has no form
          setActiveTab(e);
          return;
        }
        if (!result) {
          // form invalid
          AlertService.incompleteFormAlert({
            onConfirmed: () => {
              setActiveTab(e);
            },
          });
        } else {
          // form valid
          saveReport();
          setActiveTab(e);
        }
      }
    };

    return {
      wellId,
      EWellDetailsTab,
      tabIndex,
      wellInfo,
      breadcrumbs,
      wellDetailsTabs,
      currentChildTab,
      currentComponent,
      isSystemAdmin: JwtService.checkRole(UserType.SystemAdmin),
      handleActiveTab,
    };
  },
});
</script>
