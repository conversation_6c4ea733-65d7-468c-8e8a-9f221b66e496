import{S as v}from"./SvgIcon-CfrWCA-H.js";import{d as y,R as r,_ as I,c as d,o as m,a as n,b as c,r as L,a6 as o,n as l,F,k as N,t as B}from"./index-DalLS0_6.js";const w=y({name:"table-pagination",components:{SvgIcon:v},props:{maxVisibleButtons:{type:Number,required:!1,default:5},totalPages:{type:Number,required:!0},total:{type:Number,required:!0},perPage:{type:Number,required:!0},currentPage:{type:Number,required:!0}},emits:["page-change"],setup(e,{emit:a}){const g=r(()=>e.totalPages<e.maxVisibleButtons||e.currentPage===1||e.currentPage<=Math.floor(e.maxVisibleButtons/2)||e.currentPage+2>e.totalPages&&e.totalPages===e.maxVisibleButtons?1:e.currentPage+2>e.totalPages?e.totalPages-e.maxVisibleButtons+1:e.currentPage-2),P=r(()=>Math.min(g.value+e.maxVisibleButtons-1,e.totalPages)),k=r(()=>{const i=[];for(let u=g.value;u<=P.value;u+=1)i.push({name:u,isDisabled:u===e.currentPage});return i}),C=r(()=>e.currentPage===1),s=r(()=>e.currentPage===e.totalPages);return{startPage:g,endPage:P,pages:k,isInFirstPage:C,isInLastPage:s,onClickFirstPage:()=>{a("page-change",1)},onClickPreviousPage:()=>{a("page-change",e.currentPage-1)},onClickPage:i=>{a("page-change",i)},onClickNextPage:()=>{a("page-change",e.currentPage+1)},onClickLastPage:()=>{a("page-change",e.totalPages)},isPageActive:i=>e.currentPage===i}}}),V={class:"h-auto w-full flex flex-row gap-2 justify-between text-card-text md:w-4/5 md:text-xl md:font-semibold md:pt-4 md:justify-center md:gap-6 md:items-center"},h=["onClick"];function A(e,a,g,P,k,C){const s=L("SvgIcon");return m(),d("ul",V,[n("li",{class:l({disabled:e.isInFirstPage}),style:o({cursor:e.isInFirstPage?"auto":"pointer"})},[n("a",{onClick:a[0]||(a[0]=(...t)=>e.onClickFirstPage&&e.onClickFirstPage(...t))},[c(s,{icon:"doubleLeftArrow"})])],6),n("li",{class:l({disabled:e.isInFirstPage}),style:o({cursor:e.isInFirstPage?"auto":"pointer"})},[n("a",{onClick:a[1]||(a[1]=(...t)=>e.onClickPreviousPage&&e.onClickPreviousPage(...t))},[c(s,{icon:"singleLeftArrow"})])],6),(m(!0),d(F,null,N(e.pages,(t,b)=>(m(),d("li",{class:l({active:e.isPageActive(t.name)}),style:o({cursor:t.isDisabled?"auto":"pointer"}),key:b},[n("a",{onClick:f=>e.onClickPage(t.name)},B(t.name),9,h)],6))),128)),n("li",{class:l({disabled:e.isInLastPage}),style:o({cursor:e.isInLastPage?"auto":"pointer"})},[n("a",{onClick:a[2]||(a[2]=(...t)=>e.onClickNextPage&&e.onClickNextPage(...t))},[c(s,{icon:"singleRightArrow"})])],6),n("li",{class:l(["paginate_button page-item",{disabled:e.isInLastPage}]),style:o({cursor:e.isInLastPage?"auto":"pointer"})},[n("a",{onClick:a[3]||(a[3]=(...t)=>e.onClickLastPage&&e.onClickLastPage(...t))},[c(s,{icon:"doubleRightArrow"})])],6)])}const j=I(w,[["render",A]]);export{j as T};
