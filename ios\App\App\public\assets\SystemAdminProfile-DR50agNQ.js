import{P as h}from"./PageHeader-3hadTn26.js";import{S as w}from"./SvgIcon-CMhyaXWN.js";import{U as D,O as k}from"./Overview-CymoOfok.js";import{d as P,q as p,x as S,i as U,a4 as x,_ as C,c as o,l as v,a as s,b as l,w as f,F as b,r as d,o as a,k as M,t as I}from"./index-CGNRhvz7.js";import{u as O}from"./user-KFDu8xJF.js";import"./validator-6laVLK0J.js";import"./index.esm-DXW765zG.js";import"./company-oDyd0dWV.js";import"./handleFailure-DtTpu7r3.js";import"./regex-BLjctcPP.js";const $=[{value:"overview",key:"Overview"}],B=P({name:"system-admin-profile",components:{PageHeader:h,SvgIcon:w,Overview:k,UserInfo:D},setup(){const e=O(),t=p(),r=p(!1),u=["My Profile","Settings"],n=U("userInfo");S(()=>{c()});const c=async()=>{r.value=!0,e.getMyProfile({callback:{onSuccess:i=>{n==null||n.updateUserInfo({...i}),t.value={...i}},onFinish:()=>{r.value=!1}}})};return{loading:r,userDetail:t,breadcrumbs:u,UserStatus:x,tabs:$,getMyProfile:c}}}),F={key:0,class:"text-center my-auto"},H={key:1},N={class:"card h-100 my-8 user-overview"},V={class:"card"},K={class:"card-body pt-9 pb-0"},L={class:"d-flex overflow-auto h-55px border-top"},T={class:"nav nav-stretch nav-line-tabs nav-line-tabs-2x border-transparent fs-5 fw-bold flex-nowrap"},j={class:"nav-item"},q=["data-tab-index"];function A(e,t,r,u,n,c){const i=d("PageHeader"),_=d("UserInfo"),y=d("Overview"),g=d("KTContent");return a(),o(b,null,[e.loading?(a(),o("div",F,t[0]||(t[0]=[s("div",{class:"spinner-border text-primary",role:"status"},[s("span",{class:"sr-only"},"Loading...")],-1)]))):v("",!0),!e.loading&&e.userDetail?(a(),o("div",H,[l(g,null,{header:f(()=>[l(i,{title:"My Profile",breadcrumbs:e.breadcrumbs},null,8,["breadcrumbs"])]),body:f(()=>[s("div",N,[s("div",V,[s("div",K,[l(_,{userDetail:e.userDetail},null,8,["userDetail"]),s("div",L,[s("ul",T,[(a(!0),o(b,null,M(e.tabs,m=>(a(),o("li",j,[s("div",{class:"active nav-link cursor-pointer text-active-primary fw-semibold text-hover-primary fs-5","data-bs-toggle":"tab","data-tab-index":m.value,role:"tab"},I(m.key),9,q)]))),256))])])])])]),l(y,{hideRole:"",userDetail:e.userDetail,reloadUserData:e.getMyProfile},null,8,["userDetail","reloadUserData"])]),_:1})])):v("",!0)],64)}const ee=C(B,[["render",A]]);export{ee as default};
