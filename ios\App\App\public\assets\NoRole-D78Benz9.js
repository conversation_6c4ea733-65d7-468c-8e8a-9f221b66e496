import{d as a,u as c,e as r,_ as l,c as i,a as t,ak as d,o as m}from"./index-CGNRhvz7.js";import{_ as u,a as p}from"./agency-dark-DzOWuJJK.js";const f=a({name:"no-role",components:{},setup(){const e=c(),s=r();return{signOut:()=>{e.logout(),s.push({name:"sign-in"})}}}}),_={class:"d-flex flex-column flex-center flex-column-fluid"},g={class:"d-flex flex-column flex-center text-center p-10"},h={class:"card card-flush w-lg-650px py-5"},x={class:"card-body py-15 py-lg-20"},b={class:"mb-0"};function v(e,s,o,y,w,k){return m(),i("div",_,[t("div",g,[t("div",h,[t("div",x,[s[1]||(s[1]=d('<h1 class="fw-bolder fs-2hx text-gray-900 mb-4">Oops!</h1><div class="fw-semibold fs-6 text-gray-500 mb-7"> You don’t have any roles assigned. Please contact your admin to get access. </div><div class="mb-3"><img src="'+u+'" class="mw-100 mh-300px theme-light-show" alt=""><img src="'+p+'" class="mw-100 mh-300px theme-dark-show" alt=""></div>',3)),t("div",b,[t("button",{type:"button",class:"btn btn-sm btn-primary",onClick:s[0]||(s[0]=(...n)=>e.signOut&&e.signOut(...n))}," Log out ")])])])])])}const B=l(f,[["render",v]]);export{B as default};
