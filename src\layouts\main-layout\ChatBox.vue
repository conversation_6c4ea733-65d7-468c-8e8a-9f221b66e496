<template>
  <!-- The chatbot icon will be injected into the DOM -->
  <div></div>
</template>
<script lang="ts">
import { defineComponent, onMounted } from "vue";
import { CHAT_BOX_ID, CHAT_BOX_DOMAIN, CHAT_BOX_SRC } from "../../constants/envKey";

export default defineComponent({
  name: "chat-box",
  setup() {
    onMounted(() => {
      {
        // Inject the chatbot configuration script
        const configScript = document.createElement("script");
        configScript.type = "text/javascript";
        configScript.textContent = `
      window.embeddedChatbotConfig = {
        chatbotId: "${CHAT_BOX_ID}",
        domain: "${CHAT_BOX_DOMAIN}"
      };
    `;
        document.head.appendChild(configScript);

        // Inject the chatbot script
        const chatbotScript = document.createElement("script");
        chatbotScript.src = CHAT_BOX_SRC;
        chatbotScript.setAttribute("chatbotId", CHAT_BOX_ID);
        chatbotScript.setAttribute("domain", CHAT_BOX_DOMAIN);
        chatbotScript.defer = true;
        document.head.appendChild(chatbotScript);
      }
    });
    return {};
  },
});
</script>
