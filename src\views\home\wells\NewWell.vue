<template>
  <PageHeader title="Home" :breadcrumbs="breadcrumbs" />
  <!--begin::Actions-->
  <ul
    class="h-auto w-full bg-header-background pb-3 px-4 mx-auto flex flex-row gap-3 items-center justify-start"
    role="tablist"
  >
    <li>
      <a
        class="cursor-pointer font-semibold hover:text-active-hover hover:border-b-2 hover:border-active-border-hover"
        :class="{
          active: tabIndex === 'general',
          'text-inactive border-b-2 border-inactive-borderactive': tabIndex === 'general',
          'text-active border-b-2 border-active-border': tabIndex !== 'general',
        }"
        @click="setActiveTab($event)"
        data-tab-index="general"
      >
        General
      </a>
    </li>
  </ul>
  <div class="bg-card-background text-card-text-dark h-auto w-11/12 rounded-xl mx-auto mt-7 px-3 py-4 flex flex-col items-center mb-4">
    <WellGeneralInfo />
  </div>
</template>

<script lang="ts">
import PageHeader from "@/components/PageHeader.vue";
import SvgIcon from "@/constants/SvgIcon.vue";
import { defineComponent, ref } from "vue";
import WellGeneralInfo from "./WellGeneralInfo.vue";

export default defineComponent({
  name: "wells-new",
  components: { SvgIcon, WellGeneralInfo, PageHeader },
  setup() {
    const breadcrumbs = ["Home", "Wells", "New"];
    const tabIndex = ref<string>("general");

    const setActiveTab = (e: Event) => {
      const target = e.target as HTMLInputElement;

      tabIndex.value = target.getAttribute("data-tab-index") as string;
    };

    return {
      breadcrumbs,
      tabIndex,
      setActiveTab,
    };
  },
});
</script>
