import{d as c,_ as i,c as e,o as t,a as s,t as o,F as a,k as p}from"./index-DalLS0_6.js";const u=c({name:"page-header",components:{},props:{title:String,breadcrumbs:{type:Array,default:()=>[]}},setup(){return{}}}),m={class:"flex flex-col px-4 bg-header-background text-header-text-dark"},f={class:"font-bold"},_={class:"flex flex-row text-sm font-semibold items-center py-2"},x={key:0,class:""};function b(r,n,g,h,k,y){return t(),e("div",m,[s("h1",f,o(r.title),1),s("ul",_,[(t(!0),e(a,null,p(r.breadcrumbs,(l,d)=>(t(),e(a,{key:d},[d===r.breadcrumbs.length-1?(t(),e("li",x,o(l),1)):(t(),e(a,{key:1},[s("li",null,o(l),1),n[0]||(n[0]=s("div",{class:"bg-icon-dark h-[2px] w-[5px] mx-2"},null,-1))],64))],64))),128))])])}const B=i(u,[["render",b]]);export{B as P};
