import{S as M}from"./SvgIcon-DYvlNVZf.js";import{d as q,q as u,A as w,J as B,j as F,_ as P,c as p,l as y,o as h,a as s,b as d,t as $,r as v,w as C,B as S,p as U}from"./index-BmHWvWFS.js";import{u as j}from"./customer-2JcOh_4Q.js";const A=q({name:"customer-modal",components:{SvgIcon:M},props:{isVisible:{type:Boolean,required:!0},close:{type:Function,required:!0},loadPage:{type:Function,required:!1}},setup(e){const o=F(),r=u({customerName:"",notes:""}),n=u(null),i=u(!1),b=j(),a=u(""),m=u("");w(()=>e.isVisible,t=>{t===!1&&x()}),w(a,t=>{t!==""&&g()});const g=async()=>{a.value&&b.getCustomerDetails({id:a.value,callback:{onSuccess:t=>{m.value=t,r.value={customerName:t==null?void 0:t.customerName,notes:t==null?void 0:t.notes}}}})},c=()=>{e.close()},l=t=>{a.value=t},V=u({customerName:[{required:!0,message:"Please type Customer Name",trigger:["blur","change"]}]}),k=()=>{n.value&&n.value.validate(t=>{t&&(a.value?I():D())})},x=()=>{var t;a.value="",r.value={customerName:"",notes:""},(t=n==null?void 0:n.value)==null||t.resetFields()},D=async()=>{var t,f,N;i.value=!0,b.createCustomer({params:{...r.value,companyId:o.name==="my-company"?(t=B.getUserInfo())==null?void 0:t.companyId:(N=(f=o.params)==null?void 0:f.id)==null?void 0:N.toString()},callback:{onSuccess:X=>{var _;c(),(_=e==null?void 0:e.loadPage)==null||_.call(e)},onFinish:()=>{i.value=!1}}})},I=async()=>{a.value&&(i.value=!0,b.updateCustomer({id:a.value,params:r.value,callback:{onSuccess:t=>{var f;c(),(f=e==null?void 0:e.loadPage)==null||f.call(e)},onFinish:()=>{i.value=!1}}}))};return{id:a,rules:V,loading:i,targetData:r,formRef:n,companyDetail:m,submit:k,setId:l,reset:x,closeModal:c}}}),E={key:0,class:"fixed top-0 right-0 bottom-0 left-0 z-40 flex items-center"},J={class:"relative bg-card-background text-card-text h-auto w-11/12 flex flex-col items-center gap-3 mx-auto p-5 rounded-xl md:w-4/5 md:text-lg lg:w-2/5"},z={class:"h-auto w-full flex flex-row items-center justify-between"},R={class:"text-lg font-bold"},T={class:"flex flex-col gap-2"},G={class:"flex flex-col gap-2"},H={class:"flex flex-row items-start mt-4 gap-3"},K=["disabled"],L=["disabled"],O={key:0,class:"indicator-label"},Q={key:1,class:"indicator-progress"};function W(e,o,r,n,i,b){const a=v("SvgIcon"),m=v("el-input"),g=v("el-form-item"),c=v("el-form");return e.isVisible?(h(),p("div",E,[o[7]||(o[7]=s("div",{class:"absolute inset-0 bg-sidebar-background opacity-25"},null,-1)),s("div",J,[s("div",z,[s("h3",R,$(e.id?"Edit Customer":"Add Customer"),1),s("span",{class:"cursor-pointer",onClick:o[0]||(o[0]=(...l)=>e.closeModal&&e.closeModal(...l))},[d(a,{icon:"closeModalIcon"})])]),d(c,{id:"product_form",onSubmit:U(e.submit,["prevent"]),model:e.targetData,rules:e.rules,ref:"formRef",class:"h-auto w-full"},{default:C(()=>[s("div",T,[o[4]||(o[4]=s("label",{class:"font-semibold"},[S("Customer/Company Name "),s("span",{class:"text-danger-active font-light"},"*")],-1)),d(g,{prop:"customerName"},{default:C(()=>[d(m,{modelValue:e.targetData.customerName,"onUpdate:modelValue":o[1]||(o[1]=l=>e.targetData.customerName=l),placeholder:"",name:"customerName"},null,8,["modelValue"])]),_:1})]),s("div",G,[o[5]||(o[5]=s("label",{class:"font-semibold"},"Note ",-1)),d(g,{prop:"notes"},{default:C(()=>[d(m,{modelValue:e.targetData.notes,"onUpdate:modelValue":o[2]||(o[2]=l=>e.targetData.notes=l),placeholder:"",name:"notes",type:"textarea",rows:3},null,8,["modelValue"])]),_:1})]),s("div",H,[s("button",{type:"button",class:"bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover rounded-md px-4 py-2 font-semibold",onClick:o[3]||(o[3]=(...l)=>e.closeModal&&e.closeModal(...l)),disabled:e.loading}," Discard ",8,K),s("button",{class:"bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover rounded-md px-4 py-2 font-semibold",type:"submit",disabled:e.loading},[e.loading?y("",!0):(h(),p("span",O," Save ")),e.loading?(h(),p("span",Q,o[6]||(o[6]=[S(" Please wait... "),s("span",{class:"spinner-border spinner-border-sm align-middle ms-2"},null,-1)]))):y("",!0)],8,L)])]),_:1},8,["onSubmit","model","rules"])])])):y("",!0)}const te=P(A,[["render",W]]);export{te as C};
