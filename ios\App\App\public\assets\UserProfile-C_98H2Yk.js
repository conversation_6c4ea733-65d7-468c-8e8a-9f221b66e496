import{P as D}from"./PageHeader-l8HvxxsN.js";import{d as b,q as m,j as P,x as v,A as y,J as k,i as _,_ as B,c as d,l as f,b as S,m as $,a as U,F as C,r as g,o as u}from"./index-DalLS0_6.js";import{u as F}from"./user-UjS69U41.js";import{U as H}from"./UserDetail-ClZeg5BK.js";import"./handleFailure-DrOe_u9W.js";import"./SvgIcon-CfrWCA-H.js";import"./Overview-XX3mNy1g.js";import"./validator-BJ5Qi8qK.js";import"./index.esm-C3uaQ3c9.js";import"./company-DGE9srvS.js";import"./regex-BLjctcPP.js";import"./AssignUserModal-BA3iiGKX.js";import"./UserModal-DfH6xbe7.js";import"./TablePagination-BmkwndgK.js";import"./TableHeader-DGMH-x_O.js";import"./table-bhK9qpe4.js";import"./date-CCTVzEJd.js";const N=b({name:"my-profile",components:{UserDetail:H,PageHeader:D},setup(){const e=F(),a=P(),s=m(!1),o=m(a.params.id),i=m(),n=_("userInfo"),l=["User Management","User Profile"];v(()=>{o&&t()}),y(()=>a.params.id,r=>{o.value=r,r&&t()});const t=async()=>{o&&(s.value=!0,e.getUserProfile({id:o.value,callback:{onSuccess:r=>{var c,p;i.value=r,((p=(c=k)==null?void 0:c.getUserInfo())==null?void 0:p.id)===(r==null?void 0:r.id)&&(n==null||n.updateUserInfo({...r}))},onFinish:()=>{s.value=!1}}}))};return{loading:s,userDetail:i,breadcrumbs:l,getUserProfile:t}}}),V={key:0,class:"text-center my-auto"};function h(e,a,s,o,i,n){const l=g("PageHeader"),t=g("UserDetail");return u(),d(C,null,[e.loading?(u(),d("div",V,a[0]||(a[0]=[U("div",{class:"spinner-border text-primary",role:"status"},[U("span",{class:"sr-only"},"Loading...")],-1)]))):f("",!0),S(l,{title:"User Profile",breadcrumbs:e.breadcrumbs,reloadUserData:e.getUserProfile},null,8,["breadcrumbs","reloadUserData"]),!e.loading&&e.userDetail?(u(),$(t,{key:1,userDetail:e.userDetail,reloadUserData:e.getUserProfile},null,8,["userDetail","reloadUserData"])):f("",!0)],64)}const X=B(N,[["render",h]]);export{X as default};
