import{d as $,q as h,A as I,c as b,o as v,l as F,F as L,k as U,m as P,R as J,x as Q,a as d,n as W,u as R,a7 as D,G as j,e as X,_ as N,t as B,b as T,r as _,w as S,B as O}from"./index-DalLS0_6.js";import{E as Y,F as Z,a as ee}from"./vee-validate-CHcWpYsl.js";import{c as te,a as ne}from"./index.esm-C3uaQ3c9.js";import{_ as oe}from"./opslink-light-DYL7OcHB.js";const se={style:{display:"flex","align-items":"center"}},ae=["type","inputmode","placeholder","disabled","maxlength","value"],le={key:0},ue=["innerHTML"],ie=$({__name:"single-otp-input",props:{inputType:{default:"tel"},inputmode:{default:"numeric"},value:{default:""},separator:{default:""},focus:{type:Boolean,default:!1},inputClasses:{default:""},conditionalClass:{default:""},shouldAutoFocus:{type:Boolean,default:!1},isLastChild:{type:Boolean,default:!1},placeholder:{default:""},isDisabled:{type:Boolean,default:!1}},emits:["on-change","on-keydown","on-paste","on-focus","on-blur"],setup(e,{emit:n}){const p=e,u=n,i=h(p.value||""),l=h(null),s=t=>{const r=t.target.value;return r&&r.trim().length>1?(t.clipboardData={getData:()=>r.trim()},u("on-paste",t)):u("on-change",r)},m=t=>t>=65&&t<=90,c=t=>t>=48&&t<=57||t>=96&&t<=105,f=t=>{p.isDisabled&&t.preventDefault();const r=t||window.event,y=r.which?r.which:r.keyCode;c(y)||p.inputType==="letter-numeric"&&m(y)||[8,9,13,37,39,46,86].includes(y)?u("on-keydown",t):r.preventDefault()},w=t=>u("on-paste",t),C=()=>(l.value.select(),u("on-focus")),a=()=>u("on-blur"),k=J(()=>["letter-numeric","number"].includes(p.inputType)?"text":p.inputType);return I(()=>p.value,(t,r)=>{t!==r&&(i.value=t)}),I(()=>p.focus,(t,r)=>{r!==t&&l.value&&p.focus&&(l.value.focus(),l.value.select())}),Q(()=>{l.value&&p.focus&&p.shouldAutoFocus&&(l.value.focus(),l.value.select())}),(t,r)=>(v(),b("div",se,[d("input",{"data-test":"single-input",type:k.value,inputmode:t.inputmode,placeholder:t.placeholder,disabled:t.isDisabled,ref_key:"input",ref:l,min:"0",max:"9",maxlength:t.isLastChild?1:void 0,pattern:"[0-9]",value:i.value,class:W([t.inputClasses,t.conditionalClass,{"is-complete":i.value}]),onInput:s,onKeydown:f,onPaste:w,onFocus:C,onBlur:a},null,42,ae),!t.isLastChild&&t.separator?(v(),b("span",le,[d("span",{innerHTML:t.separator},null,8,ue)])):F("",!0)]))}}),de={style:{display:"flex"},class:"otp-input-container"},re={key:0,autocomplete:"off",name:"hidden",type:"text",style:{display:"none"}},ce=8,me=37,pe=39,fe=46,ve=$({__name:"vue3-otp-input",props:{value:{default:""},numInputs:{default:4},separator:{default:""},inputClasses:{default:""},conditionalClass:{default:()=>[]},inputType:{},inputmode:{default:"text"},shouldAutoFocus:{type:Boolean,default:!1},placeholder:{default:()=>[]},isDisabled:{type:Boolean,default:!1},shouldFocusOrder:{type:Boolean}},emits:["update:value","on-change","on-complete"],setup(e,{expose:n,emit:p}){const u=e,i=p,l=h(0),s=h([]),m=h([]);I(()=>u.value,o=>{if(o.length===u.numInputs||s.value.length===0){const g=o.split("");s.value=g}},{immediate:!0});const c=o=>{l.value=o},f=()=>{l.value=-1},w=()=>s.value.join("").length===u.numInputs?(i("update:value",s.value.join("")),i("on-complete",s.value.join(""))):"Wait until the user enters the required number of characters",C=o=>{l.value=Math.max(Math.min(u.numInputs-1,o),0)},a=()=>{C(l.value+1)},k=()=>{C(l.value-1)},t=o=>{m.value=Object.assign([],s.value),s.value[l.value]=o.toString(),m.value.join("")!==s.value.join("")&&(i("update:value",s.value.join("")),i("on-change",s.value.join("")),w())},r=o=>{o.preventDefault();const g=o.clipboardData.getData("text/plain").slice(0,u.numInputs-l.value).split("");if(u.inputType==="number"&&!g.join("").match(/^\d+$/)||u.inputType==="letter-numeric"&&!g.join("").match(/^\w+$/))return"Invalid pasted data";const V=s.value.slice(0,l.value).concat(g);return V.slice(0,u.numInputs).forEach(function(x,E){s.value[E]=x}),C(V.slice(0,u.numInputs).length),w()},y=o=>{t(o),a()},G=()=>{s.value.length>0&&(i("update:value",""),i("on-change","")),s.value=[],l.value=0},H=o=>{const g=o.split("");g.length===u.numInputs&&(s.value=g,i("update:value",s.value.join("")),i("on-complete",s.value.join("")))},K=(o,g)=>{switch(o.keyCode){case ce:o.preventDefault(),t(""),k();break;case fe:o.preventDefault(),t("");break;case me:o.preventDefault(),k();break;case pe:o.preventDefault(),a();break;default:z(g);break}},z=o=>{u.shouldFocusOrder&&setTimeout(()=>{const g=s.value.join("").length;o-g>=0&&(l.value=g,s.value[o]="")},100)};return n({clearInput:G,fillInput:H}),(o,g)=>(v(),b("div",de,[o.inputType==="password"?(v(),b("input",re)):F("",!0),(v(!0),b(L,null,U(o.numInputs,(V,x)=>{var E,A;return v(),P(ie,{key:x,focus:l.value===x,value:s.value[x],separator:o.separator,"input-type":o.inputType,inputmode:o.inputmode,"input-classes":o.inputClasses,conditionalClass:(E=o.conditionalClass)==null?void 0:E[x],"is-last-child":x===o.numInputs-1,"should-auto-focus":o.shouldAutoFocus,placeholder:(A=o.placeholder)==null?void 0:A[x],"is-disabled":o.isDisabled,onOnChange:y,onOnKeydown:M=>K(M,x),onOnPaste:r,onOnFocus:M=>c(x),onOnBlur:f},null,8,["focus","value","separator","input-type","inputmode","input-classes","conditionalClass","is-last-child","should-auto-focus","placeholder","is-disabled","onOnKeydown","onOnFocus"])}),128))]))}}),ge=$({name:"otp-verification",components:{Vue3OtpInput:ve},props:{email:String,userForgot:{type:Object,required:!1}},setup(e){const n=R(),p=X(),u=null,i=h(""),l=h(!0),s=h(""),m=h(!1);return{startCountdown:()=>{n.forgotPassword({email:(e==null?void 0:e.email)||"",callback:{onSuccess:()=>{l.value=!0},onFinish:()=>{m.value=!1},onFailure:a=>{var k,t,r,y;D.fire({text:j[(t=(k=a==null?void 0:a.response)==null?void 0:k.data)==null?void 0:t.errorCode]||((y=(r=a==null?void 0:a.response)==null?void 0:r.data)==null?void 0:y.message)||(a==null?void 0:a.message)||"Sorry, looks like there are some errors detected, please try again.",icon:"error",buttonsStyling:!1,confirmButtonText:"Got it!",heightAuto:!1,customClass:{confirmButton:"btn fw-semibold btn-light-danger"}})}}})},onCountdownEnd:()=>{l.value=!1},handleOnComplete:a=>{},onVerify:()=>{i.value.length!==6?s.value="Please type the code":(s.value="",m.value=!0,n.validateOTP({params:{email:(e==null?void 0:e.email)||"",otp:i.value},callback:{onSuccess:()=>{var a;p.push({path:"/reset-password",query:{email:e==null?void 0:e.email,otp:i.value,user_name:(a=e==null?void 0:e.userForgot)==null?void 0:a.name}})},onFinish:()=>{m.value=!1},onFailure:a=>{var k,t,r,y;D.fire({text:j[(t=(k=a==null?void 0:a.response)==null?void 0:k.data)==null?void 0:t.errorCode]||((y=(r=a==null?void 0:a.response)==null?void 0:r.data)==null?void 0:y.message)||(a==null?void 0:a.message)||"Sorry, looks like there are some errors detected, please try again.",icon:"error",buttonsStyling:!1,confirmButtonText:"Got it!",heightAuto:!1,customClass:{confirmButton:"btn fw-semibold btn-light-danger"}})}}}))},error:s,bindValue:i,otpInput:u,counting:l,loading:m}}}),be="/media/OTP-verification.svg",he={class:"d-flex flex-column gap-10"},ye={class:"text-gray-400 text-center fw-semibold fs-3"},we={class:"fv-row"},ke={key:0,class:"error-message mt-3"},xe={class:"text-gray-400 text-center fw-semibold fs-3"},Ce={class:"d-flex flex-wrap justify-content-end pb-lg-0"},Fe=["disabled"],_e={key:0,class:"indicator-label"},Te={key:1,class:"indicator-progress"};function Oe(e,n,p,u,i,l){const s=_("vue3-otp-input"),m=_("vue-countdown"),c=_("router-link");return v(),b(L,null,[n[8]||(n[8]=d("div",{class:"text-center mb-10"},[d("img",{src:be,width:100})],-1)),d("div",he,[n[7]||(n[7]=d("h1",{class:"text-dark text-center"},"OTP Verification",-1)),d("div",ye,B(`Please enter the 6 digit code sent to ${e.email} within 3
      minutes. Remember to check your email!`),1),d("div",we,[n[4]||(n[4]=d("div",{class:"text-dark fw-bold mb-3"},"Type your 6 digit security code",-1)),T(s,{ref:"otpInput","input-classes":"otp-input",inputType:"number","num-inputs":6,value:e.bindValue,"onUpdate:value":[n[0]||(n[0]=f=>e.bindValue=f),n[1]||(n[1]=f=>e.bindValue=f)],"should-auto-focus":!0,"should-focus-order":!0,onOnComplete:e.handleOnComplete},null,8,["value","onOnComplete"]),e.error?(v(),b("div",ke,B(e.error),1)):F("",!0)]),d("div",xe,[e.counting?(v(),P(m,{key:0,"auto-start":"",time:180*1e3,onEnd:e.onCountdownEnd},{default:S(({totalSeconds:f})=>[O(" Resend code in "+B(f)+" s ",1)]),_:1},8,["onEnd"])):e.counting===!1?(v(),b("span",{key:1,class:"link-primary fs-3 fw-bold resend-code",onClick:n[2]||(n[2]=(...f)=>e.startCountdown&&e.startCountdown(...f))}," Resend code ")):F("",!0)]),d("div",Ce,[T(c,{to:"/sign-in",class:"btn btn-lg btn-light-primary fw-bold"},{default:S(()=>n[5]||(n[5]=[O("Cancel")])),_:1}),d("button",{class:"btn btn-sm btn-primary ms-5",type:"submit",disabled:e.loading,onClick:n[3]||(n[3]=(...f)=>e.onVerify&&e.onVerify(...f))},[e.loading?F("",!0):(v(),b("span",_e," Verify Code ")),e.loading?(v(),b("span",Te,n[6]||(n[6]=[O(" Please wait... "),d("span",{class:"spinner-border spinner-border-sm align-middle ms-2"},null,-1)]))):F("",!0)],8,Fe)])])],64)}const Ee=N(ge,[["render",Oe]]);var q=(e=>(e[e.ENTER_EMAIL=1]="ENTER_EMAIL",e[e.OTP=2]="OTP",e))(q||{});const Pe=$({name:"forgot-password",components:{Field:ee,VForm:Z,ErrorMessage:Y,OTPVerification:Ee},setup(){const e=R(),n=h(1),p=h(""),u=h(),i=h(!1),l=te().shape({email:ne().email().required().label("Email")});return{onSubmitForgotPassword:async m=>{i.value=!0,p.value=m==null?void 0:m.email,e.forgotPassword({email:(m==null?void 0:m.email)||"",callback:{onSuccess:c=>{c&&(u.value=c),n.value=2},onFinish:()=>{i.value=!1},onFailure:c=>{var f,w,C,a;D.fire({text:j[(w=(f=c==null?void 0:c.response)==null?void 0:f.data)==null?void 0:w.errorCode]||((a=(C=c==null?void 0:c.response)==null?void 0:C.data)==null?void 0:a.message)||(c==null?void 0:c.message)||"Sorry, looks like there are some errors detected, please try again.",icon:"error",buttonsStyling:!1,confirmButtonText:"Got it!",heightAuto:!1,customClass:{confirmButton:"px-2 py-1 bg-light-danger font-semibold "}})}}})},forgotPassword:l,loading:i,currentStep:n,STEP:q,email:p,userForgot:u}}}),Se={class:"h-auto w-4/5 rounded-3xl mx-auto py-6 bg-card-background text-card-text-light rounded-3 align-self-center flex flex-col justify-center items-center md:text-lg lg:max-w-2/5"},$e={class:"flex flex-col mb-8 mx-3 h-auto w-full"},Ve={class:"text-danger mt-[0.3rem]"},Be={class:"h-auto w-full flex flex-row mb-8 gap-2 items-center"},Ie=["disabled"],De={key:0,class:"font-semibold"},je={key:1,class:"indicator-progress"};function Ae(e,n,p,u,i,l){const s=_("Field"),m=_("ErrorMessage"),c=_("router-link"),f=_("VForm"),w=_("OTPVerification");return v(),b("div",Se,[e.currentStep===e.STEP.ENTER_EMAIL?(v(),P(f,{key:0,class:"flex flex-col items-center w-4/5",onSubmit:e.onSubmitForgotPassword,"validation-schema":e.forgotPassword},{default:S(()=>[n[3]||(n[3]=d("img",{class:"h-30 w-auto",src:oe},null,-1)),n[4]||(n[4]=d("div",{class:"text-center mb-10"},[d("h1",{class:"mb-3 font-bold"},"Forgot Password ?"),d("div",{class:"font-bold fs-4"}," Enter your email to reset your password. ")],-1)),d("div",$e,[n[0]||(n[0]=d("label",{class:"form-label tracking-wide font-bold"},"Email",-1)),T(s,{class:"rounded-lg bg-input-background text-input-text-dark pl-3",type:"email",placeholder:"Enter email",name:"email",autocomplete:"off"}),d("div",Ve,[T(m,{name:"email"})])]),d("div",Be,[T(c,{to:"/sign-in",class:"bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover rounded-md px-4 py-2 font-semibold md:px-4 md:py-3 md:text-xl"},{default:S(()=>n[1]||(n[1]=[O("Cancel")])),_:1}),d("button",{class:"bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover rounded-md px-4 py-2 font-semibold md:px-4 md:py-3 md:text-xl",type:"submit",disabled:e.loading},[e.loading?F("",!0):(v(),b("span",De," Submit ")),e.loading?(v(),b("span",je,n[2]||(n[2]=[O(" Please wait... "),d("span",{class:"spinner-border spinner-border-sm align-middle ms-2"},null,-1)]))):F("",!0)],8,Ie)])]),_:1},8,["onSubmit","validation-schema"])):e.currentStep===e.STEP.OTP?(v(),P(w,{key:1,email:e.email,userForgot:e.userForgot},null,8,["email","userForgot"])):F("",!0)])}const qe=N(Pe,[["render",Ae]]);export{qe as default};
