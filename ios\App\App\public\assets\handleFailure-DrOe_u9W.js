import{P as o,E as p,G as d}from"./index-DalLS0_6.js";const m=async(s,e)=>{var t,a,n,i;e!=null&&e.onFailure?o.get(e,"onFailure",o.noop)(s):p.resultAlert(d[(a=(t=s==null?void 0:s.response)==null?void 0:t.data)==null?void 0:a.errorCode]||((i=(n=s==null?void 0:s.response)==null?void 0:n.data)==null?void 0:i.message)||(s==null?void 0:s.message)||"Sorry, looks like there are some errors detected, please try again.","error")};export{m as h};
