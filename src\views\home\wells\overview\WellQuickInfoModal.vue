<template>
  <div
    v-if="isVisible"
    class="fixed top-0 right-0 bottom-0 left-0 z-40 flex items-center"
  >
    <!-- Dark overlay background -->
    <div class="absolute inset-0 bg-sidebar-background opacity-25"></div>
    <div
      class="relative bg-card-background text-card-text-light max-h-11/12 w-11/12 flex flex-col items-center gap-3 mx-auto my-6 p-5 rounded-xl overflow-y-scroll md:text-lg md:p-10 lg:w-2/5 lg:h-3/5"
    >
      <div class="flex flex-row h-auto w-full items-center justify-between">
        <h3 class="text-lg font-bold md:text-xl">
          {{ wellData?.nameOrNo || "" }}
        </h3>
        <span class="cursor-pointer" @click="closeModal">
          <SvgIcon icon="closeModalIcon" />
        </span>
      </div>
      <div
        class="h-auto w-full flex flex-col gap-3 rounded-lg border border-light-border border-dashed px-3 pb-6 font-roboto"
      >
        <div v-if="loading" class="text-center">
          <div class="spinner-border text-primary" role="status">
            <span class="sr-only">Loading...</span>
          </div>
        </div>

        <div v-else class="flex flex-col gap-4">
          <!-- <el-tooltip content="Edit" placement="top" effect="customize">
              <button
                class="btn rounded-circle btn-icon btn-active btn-sm bg-light btn-edit position-absolute top-0 end-0 m-3"
                type="button"
                @click="editWell(wellData?.id)"
              >
                <span class="svg-icon svg-icon-3">
                  <SvgIcon icon="pencilIcon" />
                </span>
              </button>
            </el-tooltip> -->
          <div class="md:flex md:flex-row md:gap-10">
            <div class="flex flex-col gap-3">
              <div class="flex flex-col gap-2">
                <h3 class="text-lg font-bold m-0 pt-3 pb-2 md:text-xl">
                  General Information
                </h3>
                <div
                  v-if="isSystemAdmin"
                  class="flex flex-row flex-wrap items-center justify-between"
                >
                  <span class="font-bold">Company</span>
                  <span class="font-light max-w-1/2 truncate">{{
                    wellData?.company?.name
                  }}</span>
                </div>
                <div
                  class="flex flex-row flex-wrap items-center justify-between"
                >
                  <span class="font-bold">Customers</span>
                  <span class="font-light max-w-1/2 truncate">{{
                    wellData?.customers
                      ?.map((customer: Customer.Info) => customer?.customerName)
                      .join(", ")
                  }}</span>
                </div>
                <div
                  class="flex flex-row flex-wrap items-center justify-between"
                >
                  <span class="font-bold">Rig Name</span>
                  <span class="font-light max-w-1/2 truncate">{{
                    wellData?.rigName
                  }}</span>
                </div>
                <div
                  class="flex flex-row flex-wrap items-center justify-between"
                >
                  <span class="font-bold">Supervisors</span>
                  <span class="font-light max-w-1/2 truncate">{{
                    wellData?.supervisors
                      ?.map(
                        (supervisor: User.Info) =>
                          `${supervisor?.firstName} ${supervisor?.lastName}`
                      )
                      .join(", ")
                  }}</span>
                </div>
                <div
                  class="flex flex-row flex-wrap items-center justify-between"
                >
                  <span class="font-bold">Engineers</span>
                  <span class="font-light max-w-1/2 truncate">{{
                    wellData?.engineers
                      ?.map(
                        (engineer: User.Info) =>
                          `${engineer?.firstName} ${engineer?.lastName}`
                      )
                      .join(", ")
                  }}</span>
                </div>
                <div
                  class="flex flex-row flex-wrap items-center justify-between"
                >
                  <span class="font-bold">Planned Depth</span>
                  <span class="font-light max-w-1/2 truncate">{{
                    numberWithCommas(wellData?.landingPoint)
                  }}</span>
                </div>
              </div>
            </div>
            <div class="flex flex-col gap-3">
              <h3 class="font-bold my-3">Last Report</h3>
              <div class="flex flex-row flex-wrap items-center justify-between">
                <span class="font-bold">MD</span>
                <span class="font-light max-w-1/2 truncate">{{
                  getLastDailyReport()?.wellInformation?.measuredDepth
                }}</span>
              </div>
              <div class="flex flex-row flex-wrap items-center justify-between">
                <span class="font-bold">TVG</span>
                <span class="font-light max-w-1/2 truncate">{{
                  getLastDailyReport()?.wellInformation?.trueVerticalDepth
                }}</span>
              </div>
              <div class="flex flex-row flex-wrap items-center justify-between">
                <span class="font-bold">Inclination</span>
                <span class="font-light max-w-1/2 truncate">{{
                  getLastDailyReport()?.wellInformation?.inclination
                }}</span>
              </div>
              <div class="flex flex-row flex-wrap items-center justify-between">
                <span class="font-bold">Azimuth</span>
                <span class="font-light max-w-1/2 truncate">{{
                  getLastDailyReport()?.wellInformation?.azimuth
                }}</span>
              </div>
            </div>
          </div>
          <div class="flex flex-row flex-wrap items-center justify-between">
            <span class="font-bold">Treatment</span>
            <span class="font-light">{{
              getLastDailyReport()?.wellInformation?.activity
            }}</span>
          </div>
          <div class="flex flex-row flex-wrap items-center justify-between">
            <span class="font-bold">Notes</span>
            <span class="font-light">{{
              getLastDailyReport()?.notes?.slice(-1)[0]?.notes
            }}</span>
          </div>
        </div>
      </div>
      <div class="h-auto w-full pt-3 flex flex-col md:text-xl">
        <ul
          class="h-auto w-auto flex flex-row justify-start gap-3"
          role="tablist"
        >
          <li class="nav-item">
            <a
              class="cursor-pointer font-semibold pt-0 mb-3 hover:text-active-hover hover:border-b-2 hover:border-active-border-hover"
              :class="{
                active: tabIndex === 'properties',
                'text-active border-b-2 border-active-border':
                  tabIndex === 'properties',
                'text-inactive border-b-2 border-inactive-border':
                  tabIndex !== 'properties',
              }"
              data-bs-toggle="tab"
              @click="setActiveTab($event)"
              data-tab-index="properties"
              role="tab"
            >
              Properties
            </a>
          </li>
          <li class="nav-item">
            <a
              class="cursor-pointer font-semibold pt-0 mb-3 hover:text-active-hover hover:border-b-2 hover:border-active-border-hover"
              :class="{
                active: tabIndex === 'history',
                'text-active border-b-2 border-active-border':
                  tabIndex === 'history',
                'text-inactive border-b-2 border-inactive-border':
                  tabIndex !== 'history',
              }"
              data-bs-toggle="tab"
              @click="setActiveTab($event)"
              data-tab-index="history"
              role="tab"
            >
              History
            </a>
          </li>
        </ul>
      </div>
      <div v-if="id" class="h-auto w-full">
        <div v-if="tabIndex == 'properties'">
          <PropertiesTab ref="propertiesTabRef" :wellId="id" />
        </div>
        <div v-else-if="tabIndex == 'history'">
          <HistoryTab :closeModal="closeModal" :wellId="id" :date="date" />
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import SvgIcon from "@/constants/SvgIcon.vue";
import { UserType } from "@/constants/user";
import { numberWithCommas } from "@/utils/numberFormatter";
import JwtService from "@/services/JwtService";
import { useWellStore } from "@/stores/well";
import { defineComponent, ref, type Ref, toRef, watch } from "vue";
import { useRouter } from "vue-router";
import HistoryTab from "./HistoryTab.vue";
import PropertiesTab from "./PropertiesTab.vue";

export default defineComponent({
  name: "well-quick-info-modal",
  components: { SvgIcon, HistoryTab, PropertiesTab },
  props: {
    isVisible: {
      type: Boolean,
      required: true,
    },
    close: {
      type: Function,
      required: true,
    },
    wellId: {
      type: String,
      required: true,
    },
  },
  setup(props) {
    const router = useRouter();
    const wellStore = useWellStore();
    const date = ref([]);
    const tabIndex = ref<string>("properties");
    const id = toRef(props, "wellId");
    const wellData = ref<any>();
    const loading = ref(false);
    const propertiesTabRef: Ref<any> = ref<typeof PropertiesTab | null>(null);
    const historyTabRef: Ref<any> = ref<typeof HistoryTab | null>(null);

    const closeModal = () => {
      props.close();
      resetData();
    };

    watch(
      () => props.isVisible,
      (visible) => {
        if (visible && props.wellId) {
          getWellDetails(props.wellId);
        }
      }
    );

    const setActiveTab = (e: Event) => {
      const target = e.target as HTMLInputElement;

      tabIndex.value = target.getAttribute("data-tab-index") as string;
    };

    const getWellDetails = async (id: string): Promise<void> => {
      loading.value = true;

      wellStore.getWellDetails({
        wellId: id,
        callback: {
          onSuccess: (res: any) => {
            const engineers = res?.users?.filter((user: User.Info) =>
              user.roles?.some((role) => role.value === UserType.Engineer)
            );
            const supervisors = res?.users?.filter((user: User.Info) =>
              user.roles?.some((role) => role.value === UserType.Supervisor)
            );
            wellData.value = { ...res, engineers, supervisors };
          },
          onFinish: (_err: any) => {
            loading.value = false;
          },
        },
      });
    };

    const getLastDailyReport = () => {
      if (Array.isArray(wellData?.value?.dailyReport)) {
        return wellData?.value?.dailyReport?.at(-1);
      }

      return null;
    };

    const editWell = (id: string) => {
      closeModal();
      router.push({ path: `/wells/${id}` });
    };

    const resetData = () => {
      wellData.value = null;
      propertiesTabRef?.value?.reset();
      historyTabRef?.value?.reset();
      date.value = [];
      tabIndex.value = "properties";
    };

    return {
      id,
      date,
      wellData,
      tabIndex,
      loading,
      propertiesTabRef,
      isSystemAdmin: JwtService.checkRole(UserType.SystemAdmin),
      setActiveTab,
      numberWithCommas,
      editWell,
      getLastDailyReport,
      closeModal,
    };
  },
});
</script>
