import{O as d,P as t,Q as a}from"./index-BmHWvWFS.js";import{h as i}from"./handleFailure-WBgBpurp.js";const w=d("well",()=>({getWells:async({params:r,callback:s})=>{var o;const n=t.get(s,"onSuccess",t.noop),c=t.get(s,"onFinish",t.noop);try{a.setHeader();const e=await a.getWithParams("wells",r);n(((o=e.data)==null?void 0:o.data)||e.data)}catch(e){i(e,s)}finally{c()}},getWellDetails:async({wellId:r,callback:s})=>{var o;const n=t.get(s,"onSuccess",t.noop),c=t.get(s,"onFinish",t.noop);try{a.setHeader();const e=await a.get(`wells/${r}`);n(((o=e.data)==null?void 0:o.data)||e.data)}catch(e){i(e,s)}finally{c()}},deleteWell:async({wellId:r,callback:s})=>{var o;const n=t.get(s,"onSuccess",t.noop),c=t.get(s,"onFinish",t.noop);try{a.setHeader();const e=await a.delete(`wells/${r}`);n(((o=e.data)==null?void 0:o.data)||e.data)}catch(e){i(e,s)}finally{c()}},updateWell:async({id:r,params:s,callback:n})=>{var e;const c=t.get(n,"onSuccess",t.noop),o=t.get(n,"onFinish",t.noop);try{a.setHeader();const l=await a.put(`wells/${r}`,s);c(((e=l.data)==null?void 0:e.data)||l.data)}catch(l){i(l,n)}finally{o()}},createWell:async({params:r,callback:s})=>{var o;const n=t.get(s,"onSuccess",t.noop),c=t.get(s,"onFinish",t.noop);try{a.setHeader();const e=await a.post("wells",r);n(((o=e.data)==null?void 0:o.data)||e.data)}catch(e){i(e,s)}finally{c()}}}));export{w as u};
