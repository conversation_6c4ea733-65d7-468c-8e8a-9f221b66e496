import { getOption } from "../utils/option";
import { userRoleOptions, UserType } from "../constants/user";

const ID_TOKEN_KEY = "MUDDY_WELL_token" as string;
const USER_INFO_KEY = "MUDDY_WELL_user_info" as string;

/**
 * @description get token form localStorage
 */
export const getToken = (): string | null => {
  return window.localStorage.getItem(ID_TOKEN_KEY);
};

export const getUserInfo = (): any => {
  const userInfo = window.localStorage.getItem(USER_INFO_KEY);
  return userInfo ? JSON.parse(userInfo) : null;
};

export const getUserRoles = (): { value: UserType; key: string }[] | null => {
  const userInfo = getUserInfo();
  if (userInfo) {
    return userInfo?.roles?.map((item: {value: number}) => {
      return {
        value: item?.value as UserType,
        key: getOption(item?.value, userRoleOptions)?.label,
      };
    });
  }

  return null;
};

export const checkRole = (roles: UserType): boolean => {
  const userRoles = getUserRoles();
  if (userRoles && userRoles?.find((item) => item?.value === roles)) {
    return true;
  }

  return false;
};

// check if current logged in user has just 1 role: engineer
export const isJustEngineer = (): boolean => {
  const userRoles = getUserRoles();
  if (userRoles?.length === 1 && userRoles[0].value === UserType.Engineer) {
    return true;
  }

  return false;
};

// check if current logged in user has role system admin or company admin
export const isAdmin = () =>
  checkRole(UserType.SystemAdmin) || checkRole(UserType.CompanyAdmin);

// check if current logged in user has role system admin
export const isSystemAdmin = () => checkRole(UserType.SystemAdmin);

/**
 * @description save token into localStorage
 * @param token: string
 */
export const saveToken = (token: string): void => {
  window.localStorage.setItem(ID_TOKEN_KEY, token);
};

export const saveUserInfo = (info: any): void => {
  window.localStorage.setItem(USER_INFO_KEY, info);
};

/**
 * @description remove token form localStorage
 */
export const destroyToken = (): void => {
  window.localStorage.clear();
};

export default {
  getToken,
  saveToken,
  destroyToken,
  saveUserInfo,
  getUserInfo,
  getUserRoles,
  checkRole,
  isJustEngineer,
};
