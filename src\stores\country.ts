import ApiService from "../services/ApiService";
import { get, noop } from "lodash";
import { defineStore } from "pinia";
import { handleFailure } from "../utils/handleFailure";
import type { Callback } from "../types/common";

export const useCountryStore = defineStore("country", () => {
  const getCountries = async ({
    params,
    callback,
  }: {
    params: Country.GetParams;
    callback: Callback;
  }): Promise<void> => {
    const onSuccess = get(callback, "onSuccess", noop);
    const onFinish = get(callback, "onFinish", noop);

    try {
      ApiService.setHeader();
      const response = await ApiService.getWithParams("countries", params);
      onSuccess(response.data?.data || response.data);
    } catch (error) {
      handleFailure(error, callback);
    } finally {
      onFinish();
    }
  };

  const getStates = async ({
    params,
    callback,
  }: {
    params: State.GetParams;
    callback: Callback;
  }): Promise<void> => {
    const onSuccess = get(callback, "onSuccess", noop);
    const onFinish = get(callback, "onFinish", noop);

    try {
      ApiService.setHeader();
      const response = await ApiService.getWithParams("states", params);
      onSuccess(response.data?.data || response.data);
    } catch (error) {
      handleFailure(error, callback);
    } finally {
      onFinish();
    }
  };

  return { getCountries, getStates };
});
