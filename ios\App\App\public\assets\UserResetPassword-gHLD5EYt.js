import{S as A}from"./SuccessTemplate-BuLX5i7K.js";import{S as D}from"./SvgIcon-CMhyaXWN.js";import{d as M,q as u,u as U,j as B,A as T,a8 as j,G as F,_ as L,c as w,m as q,l as V,a,b as c,w as k,p as $,r as P,o as p,F as G,k as z,t as O,B as Y}from"./index-CGNRhvz7.js";import{R as H,a as J,b as K,c as Q}from"./regex-BLjctcPP.js";import{v as S}from"./validator-6laVLK0J.js";import{_ as W}from"./well-logo-diufKTHF.js";import"./index.esm-DXW765zG.js";var I=(e=>(e[e.RESET=1]="RESET",e[e.SUCCESS=2]="SUCCESS",e))(I||{});const X=M({name:"reset-password",components:{SvgIcon:D,SuccessTemplate:A},setup(){var x,R;const e=u(1),t=U(),y=B(),_=y.query,N=((R=(x=y.params)==null?void 0:x.token)==null?void 0:R.toString())||"",E={newPassword:"",confirmPassword:"",hideNewPassword:!0,hideConfirmPassword:!0},h={length:{isValid:!1,text:"At least 8 characters"},lowercase:{isValid:!1,text:"At least 1 lowercase character"},uppercase:{isValid:!1,text:"At least 1 uppercase character"},specialCharacter:{isValid:!1,text:"At least 1 number and 1 special character"}},n=u({...E}),m=u(!1),f=u(null),v=u({...h});T(()=>n.value.newPassword,s=>{});const b=s=>{const o=S.validate(s,{pattern:H,errorsMessage:{pattern:"Incorrect password format."}}),r=S.validate(s,{pattern:J,errorsMessage:{pattern:"Incorrect password format."}}),d=S.validate(s,{pattern:K,errorsMessage:{pattern:"Incorrect password format."}}),g=S.validate(s,{pattern:Q,errorsMessage:{pattern:"Incorrect password format."}});return v.value={length:{isValid:!o,text:"At least 8 characters"},lowercase:{isValid:!r,text:"At least 1 lowercase character"},uppercase:{isValid:!d,text:"At least 1 uppercase character"},specialCharacter:{isValid:!g,text:"At least 1 number and 1 special character"}},o||r||d||g||""},i=u({newPassword:[{validator:(s,o,r)=>{if(o==="")r(new Error("Please type New Password"));else{const d=b(o);d!==""?r(new Error(d)):r()}},trigger:["change","blur"]}],confirmPassword:[{validator:(s,o,r)=>{o===""?r(new Error("Please type Confirm Password")):o!==n.value.newPassword?r(new Error("Confirm Password doesn't match New Password!")):r()},trigger:["change","blur"]}]});return{currentStep:e,STEP:I,formRef:f,rules:i,loading:m,targetData:n,checkPassword:v,query:_,submit:()=>{f.value&&(m.value=!0,t.userResetPassword({token:N,params:{newPassword:n.value.newPassword},callback:{onSuccess:()=>{var s;(s=f.value)==null||s.validate(o=>{o&&(e.value=2)})},onFinish:()=>{m.value=!1},onFailure:s=>{var o,r,d,g;j.fire({text:F[(r=(o=s==null?void 0:s.response)==null?void 0:o.data)==null?void 0:r.errorCode]||((g=(d=s==null?void 0:s.response)==null?void 0:d.data)==null?void 0:g.message)||(s==null?void 0:s.message)||"Sorry, looks like there are some errors detected, please try again.",icon:"error",buttonsStyling:!1,confirmButtonText:"Got it!",heightAuto:!1,customClass:{confirmButton:"btn fw-semibold btn-light-danger"}})}}}))},toggleEye:s=>{s==="confirmPassword"?n.value.hideConfirmPassword=!n.value.hideConfirmPassword:s==="newPassword"&&(n.value.hideNewPassword=!n.value.hideNewPassword)}}}}),Z={class:"form-container w-xxl-500px p-10 bg-white rounded-3"},ee={key:0},se={class:"d-flex flex-column gap-10"},te={class:"row"},ae={class:"col-12 fv-row d-flex flex-column justify-content-stretch mb-7"},oe={class:"position-relative input-password"},re={class:"col-12 fv-row d-flex flex-column justify-content-stretch"},ne={class:"position-relative input-password"},ie={className:"validate-password my-5"},le=["className"],de={class:"ms-2"},ce={class:"d-flex justify-content-end mt-10"},me=["data-kt-indicator","disabled"],pe={key:0,class:"indicator-label"},ue={key:1,class:"indicator-progress"};function we(e,t,y,_,N,E){const h=P("el-input"),n=P("el-form-item"),m=P("SvgIcon"),f=P("el-form"),v=P("SuccessTemplate");return p(),w("div",Z,[e.currentStep===e.STEP.RESET?(p(),w("div",ee,[t[9]||(t[9]=a("div",{class:"text-center mb-10"},[a("img",{src:W,width:100,height:100})],-1)),a("div",se,[t[8]||(t[8]=a("h1",{class:"text-dark text-center"},"Reset Password",-1)),c(f,{id:"change_pass_form",onSubmit:$(e.submit,["prevent"]),model:e.targetData,rules:e.rules,ref:"formRef",class:"form"},{default:k(()=>{var b,C;return[a("div",te,[a("div",ae,[t[4]||(t[4]=a("label",{class:"d-flex align-items-center fs-6 fw-semibold mb-2 text-break required"},"New Password ",-1)),a("div",oe,[c(n,{prop:"newPassword",class:"mt-auto mb-0"},{default:k(()=>{var l;return[c(h,{size:"large",class:"w-100",modelValue:e.targetData.newPassword,"onUpdate:modelValue":t[0]||(t[0]=i=>e.targetData.newPassword=i),name:"newPassword",type:(l=e.targetData)!=null&&l.hideNewPassword?"password":"input"},null,8,["modelValue","type"])]}),_:1}),a("span",{class:"svg-icon svg-icon-1 position-absolute top-50 eye-icon",onClick:t[1]||(t[1]=()=>e.toggleEye("newPassword"))},[c(m,{icon:(b=e.targetData)!=null&&b.hideNewPassword?"hidePasswordIcon":"showPasswordIcon"},null,8,["icon"])])])]),a("div",re,[t[5]||(t[5]=a("label",{class:"d-flex align-items-center fs-6 fw-semibold mb-2 required"}," Confirm Password ",-1)),a("div",ne,[c(n,{prop:"confirmPassword",class:"mt-auto mb-0"},{default:k(()=>{var l;return[c(h,{size:"large",class:"w-100",modelValue:e.targetData.confirmPassword,"onUpdate:modelValue":t[2]||(t[2]=i=>e.targetData.confirmPassword=i),name:"confirmPassword",type:(l=e.targetData)!=null&&l.hideConfirmPassword?"password":"input"},null,8,["modelValue","type"])]}),_:1}),a("span",{class:"svg-icon svg-icon-1 position-absolute top-50 eye-icon",onClick:t[3]||(t[3]=()=>e.toggleEye("confirmPassword"))},[c(m,{icon:(C=e.targetData)!=null&&C.hideConfirmPassword?"hidePasswordIcon":"showPasswordIcon"},null,8,["icon"])])])])]),a("div",ie,[t[6]||(t[6]=a("p",null,"New password must contain:",-1)),a("div",null,[(p(!0),w(G,null,z(Object.entries(e.checkPassword),([l,i])=>(p(),w("div",{key:l,className:`d-flex mt-1 ${i.isValid?"valid":"invalid"}`},[c(m,{icon:i.isValid?"checkMarkIcon":"exclamationMarkIcon"},null,8,["icon"]),a("span",de,O(i.text),1)],8,le))),128))])]),a("div",ce,[a("button",{"data-kt-indicator":e.loading?"on":null,class:"btn btn-sm btn-primary ms-auto",type:"submit",disabled:e.loading},[e.loading?V("",!0):(p(),w("span",pe," Reset ")),e.loading?(p(),w("span",ue,t[7]||(t[7]=[Y(" Please wait... "),a("span",{class:"spinner-border spinner-border-sm align-middle ms-2"},null,-1)]))):V("",!0)],8,me)])]}),_:1},8,["onSubmit","model","rules"])])])):e.currentStep===e.STEP.SUCCESS?(p(),q(v,{key:1,message:"Your new password is set",description:"Go to Login Page and start your journey now"})):V("",!0)])}const ke=L(X,[["render",we]]);export{ke as default};
