import AlertService from "../services/AlertService";
import { get, noop } from "lodash";
import { ExceptionCode, ExceptionMessages } from "../constants/exceptions";
import type { Callback } from "../types/common";

export const handleFailure = async (
  error: any,
  callback: Callback
): Promise<void> => {
  if (callback?.onFailure) {
    const onFailure = get(callback, "onFailure", noop);
    onFailure(error);
  } else {
    AlertService.resultAlert(
      ExceptionMessages[error?.response?.data?.errorCode as ExceptionCode] ||
        error?.response?.data?.message ||
        error?.message ||
        "Sorry, looks like there are some errors detected, please try again.",
      "error"
    );
  }
};
