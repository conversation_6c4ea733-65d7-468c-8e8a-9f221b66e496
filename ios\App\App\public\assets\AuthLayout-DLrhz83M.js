import{d as s,_ as a,c as n,a as t,b as r,r as l,o as c}from"./index-CGNRhvz7.js";const u=s({name:"auth-layout",components:{},setup(){}}),i={class:"z-10 min-h-screen min-w-full flex flex-col items-center justify-center bg-linear-to-r from-bg-auth-start from-50 via-auth-via via-75% to-auth-to to-95%"},f={class:"h-full w-full z-30"};function d(m,e,p,_,v,b){const o=l("router-view");return c(),n("div",i,[e[0]||(e[0]=t("div",{class:"z-20 absolute bg-[url('/media/well-background.png')] bg-no-repeat bg-[center_40%] bg-cover offset w-full h-full"},null,-1)),t("div",f,[r(o)])])}const g=a(u,[["render",d]]);export{g as default};
