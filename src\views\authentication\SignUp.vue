<template>
  <div
    class="h-auto w-4/5 rounded-3xl mx-auto py-6 bg-card-background text-card-text-light rounded-3 align-self-center flex flex-col justify-center items-center md:text-lg lg:max-w-2/5"
  >
    <VForm
      v-if="currentStep === STEP.REGISTER"
      class="flex flex-col items-center w-4/5"
      novalidate
      @submit="onSubmitRegister"
      id="kt_login_signup_form"
      :validation-schema="registration"
    >
      <img class="h-30 w-auto" src="/media/logos/opslink-light.png" />

      <h1 class="font-bold">Register</h1>

      <div class="flex flex-col mb-2 mx-3 h-auto w-full">
        <label class="form-label tracking-wide font-bold">Email</label>
        <Field
          class="rounded-lg bg-input-background pl-3"
          type="email"
          placeholder=""
          name="email"
          autocomplete="off"
        />
        <div class="text-danger mt-[0.3rem]">
          <ErrorMessage name="email" />
        </div>
      </div>

      <div class="flex flex-col mb-2 mx-3 h-auto w-full">
        <label class="form-label tracking-wide font-bold">First Name</label>
        <Field
          class="w-full rounded-lg bg-input-background pl-3"
          type="text"
          placeholder=""
          name="first_name"
          autocomplete="off"
        />
        <div class="text-danger mt-[0.3rem]">
          <ErrorMessage name="first_name" />
        </div>
      </div>

      <div class="flex flex-col mb-2 mx-3 h-auto w-full">
        <label class="form-label tracking-wide font-bold">Last Name</label>
        <Field
          class="w-full rounded-lg bg-input-background pl-3"
          type="text"
          placeholder=""
          name="last_name"
          autocomplete="off"
        />
        <div class="text-danger mt-[0.3rem]">
          <ErrorMessage name="last_name" />
        </div>
      </div>

      <div class="flex flex-col mb-2 mx-3 h-auto w-full">
        <label class="form-label tracking-wide font-bold">Password</label>

        <div class="relative">
          <Field
            tabindex="2"
            class="w-full rounded-lg bg-input-background pl-3"
            :type="isHidePassword ? 'password' : 'input'"
            name="password"
            autocomplete="off"
          />
          <span
            class="svg-icon svg-icon-1 eye-icon absolute top-[0.3rem] right-1"
            @click="() => setIsHidePassword(!isHidePassword)"
          >
            <SvgIcon
              :icon="isHidePassword ? 'hidePasswordIcon' : 'showPasswordIcon'"
            />
          </span>
        </div>
        <div class="text-danger mt-[0.3rem]">
          <ErrorMessage name="password" />
        </div>
      </div>

      <div class="flex flex-col mb-2 mx-3 h-auto w-full">
        <label class="form-label tracking-wide font-bold"
          >Confirm Password</label
        >
        <div class="relative">
          <Field
            tabindex="2"
            class="w-full rounded-lg bg-input-background pl-3"
            :type="isHideConfirmPassword ? 'password' : 'input'"
            name="password_confirmation"
            autocomplete="off"
          />
          <span
            class="svg-icon svg-icon-1 eye-icon absolute top-[0.3rem] right-1"
            @click="() => setIsHideConfirmPassword(!isHideConfirmPassword)"
          >
            <SvgIcon
              :icon="
                isHideConfirmPassword ? 'hidePasswordIcon' : 'showPasswordIcon'
              "
            />
          </span>
        </div>
        <div class="fv-plugins-message-container">
          <ErrorMessage name="password_confirmation" />
        </div>
      </div>

      <div className="text-xs font-medium mb-4 md:text-md">
        <p>Password must contain:</p>
        <div>
          <div
            v-for="(value, key) in checkPassword"
            :key="key"
            :class="[
              'flex items-center mt-1 gap-2',
              value.isValid ? 'text-success' : 'text-danger',
            ]"
          >
            <SvgIcon
              :icon="value.isValid ? 'checkMarkIcon' : 'exclamationMarkIcon'"
            />
            <span>{{ value.text }}</span>
          </div>
        </div>
      </div>

      <div class="w-full mb-4 flex flex-row justify-between items-center">
        <div class="font-semibold">
          <router-link
            to="/sign-in"
            class="bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover rounded-md px-4 py-2 font-semibold md:px-4 md:py-3 md:text-xl"
          >
            Back to Login
          </router-link>
        </div>
        <button
          type="submit"
          class="bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover rounded-md px-4 py-2 font-semibold md:px-4 md:py-3 md:text-xl"
          :disabled="loading"
        >
          <span class="font-semibold" v-if="!loading"> Continue </span>
          <span class="indicator-progress" v-if="loading">
            Please wait...
            <span
              class="spinner-border spinner-border-sm align-middle ms-2"
            ></span>
          </span>
        </button>
      </div>
    </VForm>
    <SuccessTemplate
      v-else-if="currentStep === STEP.SUCCESS"
      :message="`Hey ${userRegistered?.fullName}, thanks for signing up!`"
      description="Go to Login Page and start your journey now"
    />
  </div>
</template>

<script lang="ts">
import SuccessTemplate from "@/components/templates/SuccessTemplate.vue";
import SvgIcon from "@/constants/SvgIcon.vue";
import {
  RegexLowercase,
  RegexPasswordLength,
  RegexSpecialChar,
  RegexUppercase,
} from "@/constants/regex";
import { validator } from "@/utils/validator";
import { useAuthStore } from "@/stores/auth";
import Swal from "sweetalert2";
import { ErrorMessage, Field, Form as VForm } from "vee-validate";
import { defineComponent, ref } from "vue";
import * as Yup from "yup";

enum STEP {
  REGISTER = 1,
  SUCCESS,
}

export default defineComponent({
  name: "sign-up",
  components: {
    Field,
    VForm,
    ErrorMessage,
    SvgIcon,
    SuccessTemplate,
  },
  setup() {
    const store = useAuthStore();
    const currentStep = ref<number>(STEP.REGISTER);
    const loading = ref<boolean>(false);

    const initialCheckPassFormat = {
      length: {
        isValid: false,
        text: "At least 8 characters",
      },
      lowercase: {
        isValid: false,
        text: "At least 1 lowercase character",
      },
      uppercase: {
        isValid: false,
        text: "At least 1 uppercase character",
      },
      specialCharacter: {
        isValid: false,
        text: "At least 1 number and 1 special character",
      },
    };
    const validatePasswordFormat = (value: string = "") => {
      const errLength = validator.validate(value, {
        pattern: RegexPasswordLength,
        errorsMessage: { pattern: "Incorrect password format." },
      });
      const errLowercase = validator.validate(value, {
        pattern: RegexLowercase,
        errorsMessage: { pattern: "Incorrect password format." },
      });
      const errUppercase = validator.validate(value, {
        pattern: RegexUppercase,
        errorsMessage: { pattern: "Incorrect password format." },
      });
      const errNumberAndChar = validator.validate(value, {
        pattern: RegexSpecialChar,
        errorsMessage: { pattern: "Incorrect password format." },
      });

      checkPassword.value = {
        length: {
          isValid: errLength ? false : true,
          text: "At least 8 characters",
        },
        lowercase: {
          isValid: errLowercase ? false : true,
          text: "At least 1 lowercase character",
        },
        uppercase: {
          isValid: errUppercase ? false : true,
          text: "At least 1 uppercase character",
        },
        specialCharacter: {
          isValid: errNumberAndChar ? false : true,
          text: "At least 1 number and 1 special character",
        },
      };

      const err =
        errLength || errLowercase || errUppercase || errNumberAndChar || "";

      return err;
    };

    const checkPassword = ref({ ...initialCheckPassFormat });
    const isHidePassword = ref(true);
    const userRegistered = ref();
    const isHideConfirmPassword = ref(true);

    const registration = Yup.object().shape({
      first_name: Yup.string().required().label("First Name"),
      last_name: Yup.string().required().label("Last Name"),
      email: Yup.string().min(4).required().email().label("Email"),
      password: Yup.string()
        .required()
        .test("password", function (value) {
          if (value === "") {
            return this.createError({
              path: "password",
              message:
                "Please make sure at least one submit inspection type checkbox is checked and try again",
            });
          } else {
            const err = validatePasswordFormat(value);
            if (err !== "") {
              return this.createError({
                path: "password",
                message: err,
              });
            } else {
              return true;
            }
          }
        })
        .label("Password"),
      password_confirmation: Yup.string()
        .required()
        .oneOf([Yup.ref("password")], "Passwords must match")
        .label("Password Confirmation"),
    });

    const setIsHidePassword = (value: boolean) => {
      isHidePassword.value = value;
    };

    const setIsHideConfirmPassword = (value: boolean) => {
      isHideConfirmPassword.value = value;
    };

    const onSubmitRegister = async (values: any) => {
      const data = {
        email: values.email,
        password: values.password,
        firstName: values.first_name,
        lastName: values.last_name,
      };

      store.purgeAuth();
      loading.value = true;

      await store.register({
        credentials: data,
        callback: {
          onSuccess: () => {
            userRegistered.value = {
              fullName: data.firstName + " " + data.lastName,
            };
            currentStep.value = STEP.SUCCESS;
          },
          onFinish: () => {
            loading.value = false;
          },
          onFailure: (error) => {
            Swal.fire({
              text: error.response.data?.message,
              icon: "error",
              buttonsStyling: false,
              confirmButtonText: "Try again!",
              heightAuto: false,
              customClass: {
                confirmButton: "btn fw-semibold btn-light-danger",
              },
            });
          },
        },
      });
    };

    return {
      currentStep,
      STEP,
      registration,
      isHideConfirmPassword,
      isHidePassword,
      checkPassword,
      loading,
      userRegistered,
      onSubmitRegister,
      setIsHidePassword,
      setIsHideConfirmPassword,
    };
  },
});
</script>
