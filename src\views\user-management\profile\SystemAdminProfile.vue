<template>
  <div v-if="loading" class="text-center my-auto">
    <div class="spinner-border text-primary" role="status">
      <span class="sr-only">Loading...</span>
    </div>
  </div>
  <div v-if="!loading && userDetail">
    <PageHeader title="My Profile" :breadcrumbs="breadcrumbs" />
    <div class="bg-screen-background h-auto w-11/12 mx-auto mt-4 rounded-xl">
      <UserInfo :userDetail="userDetail" />

      <!--begin::Navs-->
      <ul
        class="bg-card-background text-card-text h-auto w-full mt-4 pt-4 px-4 rounded-t-lg flex flex-row items-center justify-start gap-3 border-transparent font-bold"
      >
        <!--begin::Nav item-->
        <li
          class="cursor-pointer font-semibold hover:text-primary hover:border-b-2 hover:border-primary"
          v-for="item in tabs"
          :key="item.value"
        >
          <div
            class="nav-link cursor-pointer hover:text-active-hover hover:border-b-2 hover:border-active-border-hover"
            :class="{
              'text-active border-b-2 border-active-border': true,
            }"
            :data-tab-index="item.value"
            role="tab"
          >
            {{ item.key }}
          </div>
        </li>
        <!--end::Nav item-->
      </ul>
      <!--end::Navs-->
    </div>
    <Overview
      hideRole
      :userDetail="userDetail"
      :reloadUserData="getMyProfile"
    />
  </div>
</template>

<script lang="ts">
import PageHeader from "@/components/PageHeader.vue";
import SvgIcon from "@/constants/SvgIcon.vue";
import UserInfo from "./UserInfo.vue";
import { UserStatus } from "@/constants/user";
import { useUserStore } from "@/stores/user";
import { defineComponent, inject, onMounted, ref } from "vue";
import Overview from "./Overview.vue";
import type { Provide } from "@/types/injection-types";

const tabs = [{ value: "overview", key: "Overview" }];

export default defineComponent({
  name: "system-admin-profile",
  components: {
    PageHeader,
    SvgIcon,
    Overview,
    UserInfo,
  },
  setup() {
    const userStore = useUserStore();
    const userDetail = ref<User.Info>();
    const loading = ref<boolean>(false);
    const breadcrumbs = ["My Profile", "Settings"];
    const userInfoProvide = inject<Provide.UserInfo>("userInfo");

    onMounted(() => {
      getMyProfile();
    });

    const getMyProfile = async (): Promise<void> => {
      loading.value = true;
      userStore.getMyProfile({
        callback: {
          onSuccess: (res: User.Info) => {
            userInfoProvide?.updateUserInfo({ ...res });
            userDetail.value = { ...res };
          },
          onFinish: () => {
            loading.value = false;
          },
        },
      });
    };

    return {
      loading,
      userDetail,
      breadcrumbs,
      UserStatus,
      tabs,
      getMyProfile,
    };
  },
});
</script>
<style>
.user-overview {
  .user-info {
    font-weight: 500;
  }
  .user-status {
    font-size: 14px;
    font-weight: 700;
  }
  .btn-edit-image {
    position: absolute;
    bottom: 10px;
    right: 10px;
    .btn.btn-light {
      padding: 0px !important;
      height: 35px;
      width: 35px;
      justify-content: center;
      align-items: center;
      .svg-icon {
        margin: 0px 0px 0px 2px;
      }
    }
  }
}
</style>
