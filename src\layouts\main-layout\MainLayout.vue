<template>
  <!--begin::App-->
  <UserInfoProvide>
    <div
      class="flex flex-col h-screen w-full bg-screen-background overflow-y-scroll"
    >
      <!--begin::Page-->
      <Header
        @toggle-sidebar="toggleSidebar"
        classname="h-8 w-8 md:h-10 md:w-10"
      ></Header>
      <Sidebar
        :displaySidebar="displaySidebar"
        @update:displaySidebar="toggleSidebar"
      />
      <!--begin::Main-->
      <div class="flex-grow flex flex-col items-center justify-center">
        <RouterView></RouterView>
        <!--end:::Main-->
      </div>
      <!--end::Page-->
    </div>
    <!-- <ChatBox /> -->
  </UserInfoProvide>
  <!--end::App-->
</template>

<script lang="ts">
import SvgIcon from "../../constants/SvgIcon.vue";
import Header from "./Header.vue";
import Sidebar from "./sidebar/Sidebar.vue";
import UserInfoProvide from "../../provide/UserInfoProvide.vue";
import ChatBox from "./ChatBox.vue";
import { defineComponent, ref } from "vue";

export default defineComponent({
  name: "default-layout",
  components: {
    Sidebar,
    SvgIcon,
    UserInfoProvide,
    Header,
    ChatBox,
  },
  setup() {
    const displaySidebar = ref(false);
    const title = ref("");

    const toggleSidebar = () => {
      displaySidebar.value = !displaySidebar.value;
    };

    const setTitle = (newTitle: string) => {
      title.value = newTitle;
    };

    return {
      displaySidebar,
      toggleSidebar,
      setTitle,
    };
  },
});
</script>
