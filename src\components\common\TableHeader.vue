<template>
  <template v-for="(header, index) in headers" :key="index">
    <th v-if="header?.display !== false" :class="header?.class">
      <div
        v-if="header?.sortBy"
        class="flex flex-row justify-center items-center"
        @click="() => onClickSort(header.sortBy)"
      >
        <slot name="customHeader" :header="header">
          {{ header.label }}
        </slot>
        <span class="sort-icon">
          <SvgIcon :icon="getArrowPath(header.sortBy)"/>
          <!-- <img :src="getArrowPath(header.sortBy)" :alt="sortDirection" /> -->
        </span>
      </div>
      <slot v-else name="customHeader" :header="header">
        {{ header.label }}
      </slot>
    </th>
  </template>
</template>

<script lang="ts">
import SvgIcon from "../../constants/SvgIcon.vue";
import { SortByEnum, SortDirectionEnum } from "../../constants/table";
import { defineComponent, type PropType } from "vue";

export default defineComponent({
  name: "table-header",
  components: {SvgIcon},
  props: {
    headers: {
      type: Object as PropType<Table.ColumnHeader[]>,
      require: true,
    },
    sortBy: {
      type: String,
      require: true,
    },
    sortDirection: {
      type: String,
      require: true,
    },
    onSort: {
      type: Function,
      require: true,
    },
  },
  setup(props) {
    const getArrowPath = (sortByValue: SortByEnum) => {
      if (props.sortBy === sortByValue) {
        return props.sortDirection === SortDirectionEnum.ASC
          ? "/media/icons/arrows/order-by-asc.svg"
          : "/media/icons/arrows/order-by-des.svg";
      }
      return "/media/icons/arrows/order-by-base.svg";
    };

    const onClickSort = (sortByValue: SortByEnum) => {
      let newSortValue: Filter.FilterForm = {};

      if (props?.sortBy !== sortByValue) {
        newSortValue = {
          sortBy: sortByValue,
          sortDirection: SortDirectionEnum.ASC,
        };
      } else {
        newSortValue = {
          sortBy: props?.sortBy,
          sortDirection:
            props?.sortDirection === SortDirectionEnum.DESC
              ? SortDirectionEnum.ASC
              : SortDirectionEnum.DESC,
        };
      }
      props?.onSort?.(newSortValue);
    };

    return { getArrowPath, onClickSort };
  },
});
</script>
