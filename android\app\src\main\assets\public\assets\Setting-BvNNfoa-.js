import{S as Y}from"./SvgIcon-DYvlNVZf.js";import{d as L,q as g,L as P,J as R,x as O,A as U,_ as H,c as y,l as N,o as v,a as o,t as k,b as s,r as b,w as S,p as z,B as M,E as Z,m as W,H as ee,n as G,I as te,F as j,k as K,U as ue,h as ce}from"./index-BmHWvWFS.js";import{T as oe}from"./TablePagination-lslz6s2e.js";import{T as le}from"./TableHeader-DGdsllig.js";import{S as B,a as ne}from"./table-bhK9qpe4.js";import{f as se}from"./date-CKteeARj.js";import{n as ae}from"./numberFormatter-C7uP7NWj.js";import{u as re,a as ie}from"./product-pmYNrpK1.js";import{F as me}from"./Filter-BOtbyd0U.js";import{u as Q}from"./company-KQxnMnUF.js";import{P as pe}from"./PageHeader-Sj9hJFB8.js";import"./handleFailure-WBgBpurp.js";var de=(e=>(e[e.Service=1]="Service",e[e.Engineer=2]="Engineer",e))(de||{});const ge=L({name:"costs-modal",components:{SvgIcon:Y},props:{isVisible:{type:Boolean,required:!0},close:{type:Function,required:!0},loadPage:{type:Function,required:!1},type:{type:Number,required:!0},label:{type:String,required:!0}},setup(e){var q;const t=Q(),x=re(),w=g(!1),h={companyId:P()?null:(q=R.getUserInfo())==null?void 0:q.companyId,name:"",description:"",cost:null},i=g({...h}),a=g(null),f=g(!1),m=g(!1),p=g([]),n=g("");O(()=>{P()&&d()}),U(n,c=>{c!==""&&F()}),U(w,c=>{var r;c===!1&&(n.value="",T(),(r=a==null?void 0:a.value)==null||r.resetFields())});const d=async()=>{m.value=!0,t.getCompanies({params:{page:1,limit:500},callback:{onSuccess:c=>{const r=c==null?void 0:c.items.map(u=>({label:u==null?void 0:u.name,value:u==null?void 0:u.id}));p.value=[...r]},onFinish:()=>{m.value=!1}}})},F=async()=>{x.getCostSettingDetails({id:n.value,callback:{onSuccess:c=>{var r;i.value={...c,companyId:(r=c==null?void 0:c.company)==null?void 0:r.id}}}})},$=async c=>{f.value=!0,x.updateCostSetting({id:n.value,params:{...c,type:e.type},callback:{onSuccess:r=>{e!=null&&e.loadPage&&(e==null||e.loadPage()),e.close()},onFinish:r=>{f.value=!1}}})},_=async c=>{f.value=!0,x.createCostSetting({params:{...c,type:e.type},callback:{onSuccess:r=>{e!=null&&e.loadPage&&(e==null||e.loadPage()),e.close()},onFinish:r=>{f.value=!1}}})},A=()=>{e.close()},I=c=>{n.value=c.toString()},l={name:[{required:!0,message:"Please type Description",trigger:"blur"}],description:[{required:!0,message:"Please type Description",trigger:"blur"}],cost:[{required:!0,message:"Please type Price",trigger:"blur"}]},V=P()?{...l,companyId:[{required:!0,message:"Please select Company",trigger:["change","blur"]}]}:l,E=()=>{a.value&&a.value.validate(c=>{var r;if(c){const u={...i==null?void 0:i.value,cost:Number((r=i==null?void 0:i.value)==null?void 0:r.cost)};n!=null&&n.value?$(u):_(u)}})},T=()=>{i.value={...h}};return{id:n,modal:w,rules:V,loading:f,targetData:i,formRef:a,isSystemAdmin:P,loadingCompany:m,companyList:p,close:A,submit:E,setId:I,reset:T}}}),be={key:0,class:"fixed top-0 right-0 bottom-0 left-0 z-40 flex items-center"},fe={class:"relative bg-card-background text-card-text max-h-11/12 w-11/12 flex flex-col items-center gap-3 mx-auto p-5 rounded-xl md:w-4/6 md:text-lg lg:w-2/5 lg:h-2/5"},ve={class:"h-auto w-full flex flex-row items-center justify-between"},he={class:"text-lg font-bold"},ye={class:"h-auto w-full"},we={key:0,class:"h-auto w-full flex flex-col gap-2"},xe={class:"h-auto w-full flex flex-col gap-2"},Se={class:"h-auto w-full flex flex-col gap-2"},Ce={class:"h-auto w-full flex flex-col gap-2"},$e={class:"flex flex-row items-start mt-4 gap-3"},_e=["disabled"],Pe=["disabled"],Fe={key:0,class:"indicator-label"},ke={key:1,class:"indicator-progress"};function Ie(e,t,x,w,h,i){const a=b("SvgIcon"),f=b("el-select-v2"),m=b("el-form-item"),p=b("el-input"),n=b("el-form");return e.isVisible?(v(),y("div",be,[t[11]||(t[11]=o("div",{class:"absolute inset-0 bg-sidebar-background opacity-25"},null,-1)),o("div",fe,[o("div",ve,[o("h3",he,k(e.id?`Edit ${e.label}`:`Add ${e.label}`),1),o("span",{class:"cursor-pointer",onClick:t[0]||(t[0]=(...d)=>e.close&&e.close(...d))},[s(a,{icon:"closeModalIcon"})])]),o("div",ye,[s(n,{id:"service_costs_form",onSubmit:z(e.submit,["prevent"]),model:e.targetData,rules:e.rules,ref:"formRef",class:"form"},{default:S(()=>[e.isSystemAdmin()?(v(),y("div",we,[t[6]||(t[6]=o("label",{class:"font-semibold"},"Company ",-1)),s(m,{prop:"companyId"},{default:S(()=>[s(f,{modelValue:e.targetData.companyId,"onUpdate:modelValue":t[1]||(t[1]=d=>e.targetData.companyId=d),options:e.companyList,placeholder:"Search Company",filterable:"",name:"companyId",loading:e.loadingCompany},null,8,["modelValue","options","loading"])]),_:1})])):N("",!0),o("div",xe,[t[7]||(t[7]=o("label",{class:"font-semibold"},"Name ",-1)),s(m,{prop:"name"},{default:S(()=>[s(p,{modelValue:e.targetData.name,"onUpdate:modelValue":t[2]||(t[2]=d=>e.targetData.name=d),placeholder:"",name:"name"},null,8,["modelValue"])]),_:1})]),o("div",Se,[t[8]||(t[8]=o("label",{class:"font-semibold"},"Description ",-1)),s(m,{prop:"description"},{default:S(()=>[s(p,{type:"textarea",rows:5,modelValue:e.targetData.description,"onUpdate:modelValue":t[3]||(t[3]=d=>e.targetData.description=d),placeholder:"",name:"description"},null,8,["modelValue"])]),_:1})]),o("div",Ce,[t[9]||(t[9]=o("label",{class:"font-semibold"},"Price ",-1)),s(m,{prop:"cost"},{default:S(()=>[s(p,{type:"number",controls:!1,step:"any",modelValue:e.targetData.cost,"onUpdate:modelValue":t[4]||(t[4]=d=>e.targetData.cost=d),placeholder:"",name:"cost"},null,8,["modelValue"])]),_:1})]),o("div",$e,[o("button",{type:"button",class:"bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover rounded-md px-4 py-2 font-semibold",onClick:t[5]||(t[5]=(...d)=>e.close&&e.close(...d)),disabled:e.loading}," Discard ",8,_e),o("button",{class:"bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover rounded-md px-4 py-2 font-semibold",type:"submit",disabled:e.loading},[e.loading?N("",!0):(v(),y("span",Fe," Save ")),e.loading?(v(),y("span",ke,t[10]||(t[10]=[M(" Please wait... "),o("span",{class:"spinner-border spinner-border-sm align-middle ms-2"},null,-1)]))):N("",!0)],8,Pe)])]),_:1},8,["onSubmit","model","rules"])])])])):N("",!0)}const Ve=H(ge,[["render",Ie]]),De=L({name:"costs-page",components:{Filter:me,SvgIcon:Y,TablePagination:oe,CostsModal:Ve,TableHeader:le},props:{type:{type:Number,required:!0},label:{type:String,required:!0}},setup(e){const t=re(),x=g([]),w=g(0),h=g(0),i=g(1),a=g(!1),f=g(""),m=g(!1),p=g(null),n=g({}),d=g(!1),F=g({sortDirection:ne.ASC,sortBy:B.Name}),$=[{label:"NAME",sortBy:B.Name,class:"min-w-150px"},{label:"COMPANY",class:"min-w-150px",display:P()},{label:"PRICE",sortBy:B.Cost},{label:"CREATED DATE",class:"min-w-150px",sortBy:B.CreatedAt},{label:"ACTIONS"}];O(()=>{_()}),U(i,()=>{_()});const _=async()=>{var u,C;a.value=!0,t.getCostSettings({params:{companyId:P()?null:(C=(u=R)==null?void 0:u.getUserInfo())==null?void 0:C.companyId,keyword:f.value.trim(),type:e.type,page:i.value,limit:10,...F.value,...n.value},callback:{onSuccess:D=>{x.value=D==null?void 0:D.items,w.value=D==null?void 0:D.totalPage,h.value=D==null?void 0:D.total,i.value=D==null?void 0:D.page},onFinish:D=>{a.value=!1}}})},A=u=>{F.value={...u},_()},I=u=>{i.value=u},l=()=>{d.value=!d.value},V=u=>{var C;(C=p==null?void 0:p.value)==null||C.setId(u),d.value=!d.value},E=u=>{Z.deletionAlert({onConfirmed:()=>{T(u)}})},T=async u=>{a.value=!0,t.deleteCostSetting({id:u,callback:{onSuccess:C=>{_()},onFinish:C=>{a.value=!1}}})};return{onSort:A,onFilter:u=>{n.value={...u},i.value!==1?i.value=1:_()},toggleFilter:u=>{m.value=u},searchCostSettings:()=>{i.value!==1?i.value=1:_()},getCostSettings:_,deleteServiceCost:E,pageChange:I,numberWithCommas:ae,formatDate:se,toggleNewServiceCosts:l,toggleEditServiceCosts:V,isSystemAdmin:P,search:f,loading:a,costList:x,pageCount:w,currentPage:i,totalElements:h,isShowFilter:m,costsModal:p,sortParams:F,tableHeader:$,isModalVisible:d}}}),Ne={class:"bg-screen-background h-auto w-11/12 mx-auto my-4 rounded-xl flex flex-col items-center"},Ae={class:"bg-card-background text-card-text rounded-lg h-auto w-full flex flex-col items-start gap-3 lg:w-4/5 lg:mx-auto lg:min-w-[1560px]"},Ee={class:"h-auto w-full flex flex-col items-start my-4 px-8 gap-3"},Te={class:"font-bold text-lg"},Be={class:"h-auto w-full"},Me={class:"h-auto w-full flex flex-row gap-3 overflow-x-scroll mt-4 items-center justify-between md:overflow-hidden md:justify-start md:gap-4"},qe={class:"bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover rounded-md px-4 py-2 font-semibold flex flex-row items-center gap-2 justify-between"},Ue={key:0,class:"text-center"},je={key:2,class:"h-auto w-full overflow-x-scroll p-4 md:overflow-hidden"},Le={class:"md:w-11/12 md:mx-auto md:text-center"},He={class:"font-bold whitespace-nowrap"},Ye={class:"text-sm"},Re={class:"font-semibold text-nowrap p-4"},Oe={key:0,class:"font-semibold text-nowrap p-4"},ze={class:"font-semibold text-nowrap p-4"},We={class:"font-semibold text-nowrap p-4"},Je={class:"h-auto w-full flex flex-row items-center justify-evenly gap-2"},Ge=["onClick"],Ke=["onClick"],Qe={class:"text-danger"},Xe={class:"h-auto w-full flex flex-col items-center my-5"},Ze={class:"font-semibold"};function et(e,t,x,w,h,i){var I;const a=b("SvgIcon"),f=b("el-icon"),m=b("el-input"),p=b("el-form-item"),n=b("el-form"),d=b("Filter"),F=b("el-empty"),$=b("TableHeader"),_=b("TablePagination"),A=b("CostsModal");return v(),y(j,null,[o("div",Ne,[o("div",Ae,[o("div",Ee,[o("h1",Te,k(e.label),1),o("div",Be,[s(n,{onSubmit:z(e.searchCostSettings,["prevent"])},{default:S(()=>[s(p,{class:"mb-0"},{default:S(()=>[s(m,{placeholder:"Search",modelValue:e.search,"onUpdate:modelValue":t[0]||(t[0]=l=>e.search=l),name:"search",size:"large"},{prefix:S(()=>[s(f,{class:"el-input__icon"},{default:S(()=>[s(a,{icon:"searchIcon"})]),_:1})]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["onSubmit"]),o("div",Me,[o("button",{class:G(["flex flex-row gap-2 font-semibold font- rounded-md px-4 py-2 text-button-text-light hover:text-button-text-light-hover",e.isShowFilter?"bg-button-primary-active":"bg-button-primary hover:bg-button-primary-hover"]),onClick:t[1]||(t[1]=()=>e.toggleFilter(!e.isShowFilter))},[s(a,{icon:"filterIcon"}),t[3]||(t[3]=M(" Filter "))],2),o("button",{class:"bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover rounded-md px-4 py-2 font-semibold flex flex-row items-center gap-2 justify-between",onClick:t[2]||(t[2]=(...l)=>e.toggleNewServiceCosts&&e.toggleNewServiceCosts(...l))},[s(a,{icon:"addIcon"}),t[4]||(t[4]=M(" New "))]),o("button",qe,[s(a,{icon:"exportIcon",classname:"rotate-90"}),t[5]||(t[5]=M(" Export "))])])]),ee(s(d,{hideFilter:()=>e.toggleFilter(!1),onFilter:e.onFilter},null,8,["hideFilter","onFilter"]),[[te,e.isShowFilter]])]),e.loading?(v(),y("div",Ue,t[6]||(t[6]=[o("div",{class:"spinner-border text-primary",role:"status"},[o("span",{class:"sr-only"},"Loading...")],-1)]))):e.costList.length===0?(v(),W(F,{key:1,description:"No Data"})):(v(),y("div",je,[o("table",Le,[o("thead",null,[o("tr",He,[s($,{headers:e.tableHeader,sortBy:e.sortParams.sortBy,sortDirection:e.sortParams.sortDirection,onSort:e.onSort},null,8,["headers","sortBy","sortDirection","onSort"])])]),o("tbody",Ye,[(v(!0),y(j,null,K(e.costList,l=>{var V;return v(),y("tr",{key:l==null?void 0:l.id},[o("td",Re,k(l==null?void 0:l.name),1),e.isSystemAdmin()?(v(),y("td",Oe,k((V=l==null?void 0:l.company)==null?void 0:V.name),1)):N("",!0),o("td",ze,k(`$${e.numberWithCommas(l==null?void 0:l.cost)}`),1),o("td",We,k(e.formatDate(l==null?void 0:l.createdAt,"MMM DD, YYYY")),1),o("td",null,[o("div",Je,[o("button",{class:"bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover font-semibold h-8 w-8 flex items-center justify-center rounded-lg pl-[7.5px] pt-[1px] md:h-10 md:w-10 md:pl-[3px] md:pt-[2px]",onClick:E=>e.toggleEditServiceCosts(l==null?void 0:l.id)},[s(a,{icon:"newReportIcon",classname:"md:h-6 md:w-6"})],8,Ge),o("button",{class:"bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover font-semibold h-8 w-8 flex items-center justify-center rounded-lg pl-[7.5px] pt-[1px] md:h-10 md:w-10 md:pl-[3px] md:pb-[3.75px]",onClick:E=>e.deleteServiceCost(l==null?void 0:l.id)},[o("span",Qe,[s(a,{icon:"trashIcon",classname:"md:h-7 md:w-7"})])],8,Ke)])])])}),128))])])])),o("div",Xe,[o("div",Ze,k(`Showing ${(e.currentPage-1)*10+1} to ${(I=e.costList)==null?void 0:I.length} of ${e.totalElements} entries`),1),e.pageCount>=1?(v(),W(_,{key:0,"total-pages":e.pageCount,total:e.totalElements,"per-page":10,"current-page":e.currentPage,onPageChange:e.pageChange},null,8,["total-pages","total","current-page","onPageChange"])):N("",!0)])])]),s(A,{isVisible:e.isModalVisible,close:e.toggleNewServiceCosts,ref:"costsModal",loadPage:e.getCostSettings,label:e.label,type:e.type},null,8,["isVisible","close","loadPage","label","type"])],64)}const tt=H(De,[["render",et]]),X={value:"",label:"All"},ot=L({name:"settings-filter",components:{},props:{hideFilter:{type:Function,required:!1,default:()=>{}},onFilter:{type:Function,required:!0}},setup(e){const t=Q(),x=g([X]),w=g({companyId:""});O(()=>{P()&&h()});const h=async()=>{t.getCompanies({params:{page:1,limit:500},callback:{onSuccess:m=>{const p=m==null?void 0:m.items.map(n=>({label:n==null?void 0:n.name,value:n==null?void 0:n.id}));x.value=[X,...p]}}})},i=()=>{e!=null&&e.hideFilter&&(e==null||e.hideFilter())},a=()=>{w.value={companyId:""},f()},f=()=>{var m,p,n,d,F;e.onFilter({priceFrom:Number((m=w.value)==null?void 0:m.priceFrom)||null,priceTo:Number((p=w.value)==null?void 0:p.priceTo)||null,companyId:P()?((n=w.value)==null?void 0:n.companyId)||null:(F=(d=R)==null?void 0:d.getUserInfo())==null?void 0:F.companyId})};return{UserType:ue,filterForm:w,companyOptions:x,isSystemAdmin:P,apply:f,hideFilter:i,resetFilter:a}}}),lt={class:"bg-minicard-background text-minicard-text-light max-h-11/12 w-11/12 flex flex-col items-center gap-3 mx-auto p-5 rounded-xl shadow-sm lg:w-2/5 lg:h-3/5"},nt={class:"h-auto w-full flex flex-col"},st={class:"h-auto w-full flex flex-row gap-2 justify-center"},at={class:"h-auto w-full flex flex-col gap-3"},rt={class:"flex flex-col gap-2"},it={class:"flex flex-col gap-2"},dt={key:0,class:"flex flex-col gap-2"};function ut(e,t,x,w,h,i){const a=b("el-input"),f=b("el-form-item"),m=b("el-select-v2"),p=b("el-form");return v(),y("div",lt,[o("div",nt,[t[6]||(t[6]=o("h5",{class:"h-auto w-full font-semibold self-start"},"Filter",-1)),o("div",st,[o("button",{class:"bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover rounded-md px-4 py-2 font-semibold",onClick:t[0]||(t[0]=(...n)=>e.resetFilter&&e.resetFilter(...n))}," Reset "),o("button",{class:"bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover rounded-md px-4 py-2 font-semibold",onClick:t[1]||(t[1]=(...n)=>e.hideFilter&&e.hideFilter(...n))}," Close "),o("button",{class:"bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover rounded-md px-4 py-2 font-semibold",type:"button",onClick:t[2]||(t[2]=(...n)=>e.apply&&e.apply(...n))}," Apply ")])]),o("div",at,[s(p,{model:e.filterForm,onSubmit:z(e.apply,["prevent"])},{default:S(()=>[o("div",rt,[t[7]||(t[7]=o("label",{class:"font-semibold"},"Price from",-1)),s(f,{prop:"priceFrom"},{default:S(()=>[s(a,{type:"number",controls:!1,step:"any",modelValue:e.filterForm.priceFrom,"onUpdate:modelValue":t[3]||(t[3]=n=>e.filterForm.priceFrom=n),placeholder:"Price from",name:"priceFrom"},null,8,["modelValue"])]),_:1})]),o("div",it,[t[8]||(t[8]=o("label",{class:"font-semibold"},"Price to",-1)),s(f,{prop:"priceTo"},{default:S(()=>[s(a,{type:"number",controls:!1,step:"any",modelValue:e.filterForm.priceTo,"onUpdate:modelValue":t[4]||(t[4]=n=>e.filterForm.priceTo=n),placeholder:"Price to",name:"priceTo"},null,8,["modelValue"])]),_:1})]),e.isSystemAdmin()?(v(),y("div",dt,[t[9]||(t[9]=o("label",{class:"font-semibold"}," Company ",-1)),s(f,{prop:"companyId"},{default:S(()=>[s(m,{options:e.companyOptions,placeholder:"Select Company",name:"companyId",modelValue:e.filterForm.companyId,"onUpdate:modelValue":t[5]||(t[5]=n=>e.filterForm.companyId=n)},null,8,["options","modelValue"])]),_:1})])):N("",!0)]),_:1},8,["model","onSubmit"])])])}const ct=H(ot,[["render",ut]]),mt=L({name:"product-modal",components:{SvgIcon:Y},props:{isVisible:{type:Boolean,required:!0},close:{type:Function,required:!0},loadPage:{type:Function,required:!1}},setup(e){var q;const t=Q(),x=ie(),w=g(!1),h={companyId:P()?null:(q=R.getUserInfo())==null?void 0:q.companyId,name:"",description:"",price:null},i=g({...h}),a=g(null),f=g(!1),m=g(!1),p=g([]),n=g("");O(()=>{P()&&d()}),U(n,c=>{c!==""&&F()}),U(w,c=>{var r;c===!1&&(n.value="",T(),(r=a==null?void 0:a.value)==null||r.resetFields())});const d=async()=>{m.value=!0,t.getCompanies({params:{page:1,limit:500},callback:{onSuccess:c=>{const r=c==null?void 0:c.items.map(u=>({label:u==null?void 0:u.name,value:u==null?void 0:u.id}));p.value=[...r]},onFinish:()=>{m.value=!1}}})},F=async()=>{x.getProductDetails({id:n.value,callback:{onSuccess:c=>{var r;i.value={...c,companyId:(r=c==null?void 0:c.company)==null?void 0:r.id}}}})},$=async c=>{f.value=!0,x.updateProduct({id:n.value,params:c,callback:{onSuccess:r=>{e!=null&&e.loadPage&&(e==null||e.loadPage()),e.close()},onFinish:r=>{f.value=!1}}})},_=async c=>{f.value=!0,x.createProduct({params:c,callback:{onSuccess:r=>{e!=null&&e.loadPage&&(e==null||e.loadPage()),e.close()},onFinish:r=>{f.value=!1}}})},A=()=>{e.close()},I=c=>{n.value=c.toString()},l={name:[{required:!0,message:"Please type Name",trigger:["change","blur"]}],description:[{required:!0,message:"Please type Description",trigger:["change","blur"]}],price:[{required:!0,message:"Please type Price",trigger:["change","blur"]}]},V=P()?{...l,companyId:[{required:!0,message:"Please select Company",trigger:["change","blur"]}]}:l,E=()=>{a.value&&a.value.validate(c=>{var r;if(c){const u={...i==null?void 0:i.value,price:Number((r=i==null?void 0:i.value)==null?void 0:r.price)};n!=null&&n.value?$(u):_(u)}})},T=()=>{i.value={...h}};return{id:n,modal:w,rules:V,loading:f,targetData:i,formRef:a,isSystemAdmin:P,loadingCompany:m,companyList:p,close:A,submit:E,setId:I,reset:T}}}),pt={key:0,class:"fixed top-0 right-0 bottom-0 left-0 z-40 flex items-center"},gt={class:"relative bg-card-background text-card-text max-h-11/12 w-11/12 flex flex-col items-center gap-3 mx-auto p-5 rounded-xl md:w-4/6 md:text-lg lg:w-2/5 lg:h-2/5"},bt={class:"h-auto w-full flex flex-row items-center justify-between"},ft={class:"text-lg font-bold"},vt={class:"h-auto w-full"},ht={class:"h-auto w-full flex flex-col gap-2"},yt={class:"h-auto w-full flex flex-col gap-2"},wt={class:"h-auto w-full flex flex-col gap-2"},xt={class:"h-auto w-full flex flex-col gap-2"},St={class:"flex flex-row items-start mt-4 gap-3"},Ct=["disabled"],$t=["disabled"],_t={key:0,class:"indicator-label"},Pt={key:1,class:"indicator-progress"};function Ft(e,t,x,w,h,i){const a=b("SvgIcon"),f=b("el-select-v2"),m=b("el-form-item"),p=b("el-input"),n=b("el-form");return e.isVisible?(v(),y("div",pt,[t[11]||(t[11]=o("div",{class:"absolute inset-0 bg-sidebar-background opacity-25"},null,-1)),o("div",gt,[o("div",bt,[o("h3",ft,k(e.id?"Edit Product":"Add Product"),1),o("span",{class:"cursor-pointer",onClick:t[0]||(t[0]=(...d)=>e.close&&e.close(...d))},[s(a,{icon:"closeModalIcon"})])]),o("div",vt,[s(n,{id:"product_form",onSubmit:z(e.submit,["prevent"]),model:e.targetData,rules:e.rules,ref:"formRef"},{default:S(()=>[o("div",ht,[t[6]||(t[6]=o("label",{class:"font-semibold"},"Company",-1)),s(m,{prop:"companyId"},{default:S(()=>[s(f,{modelValue:e.targetData.companyId,"onUpdate:modelValue":t[1]||(t[1]=d=>e.targetData.companyId=d),options:e.companyList,placeholder:"Search Company",filterable:"",name:"companyId",loading:e.loadingCompany},null,8,["modelValue","options","loading"])]),_:1})]),o("div",yt,[t[7]||(t[7]=o("label",{class:"font-semibold"},"Name",-1)),s(m,{prop:"name"},{default:S(()=>[s(p,{modelValue:e.targetData.name,"onUpdate:modelValue":t[2]||(t[2]=d=>e.targetData.name=d),placeholder:"",name:"name"},null,8,["modelValue"])]),_:1})]),o("div",wt,[t[8]||(t[8]=o("label",{class:"font-semibold"},"Description",-1)),s(m,{prop:"description"},{default:S(()=>[s(p,{type:"textarea",rows:5,modelValue:e.targetData.description,"onUpdate:modelValue":t[3]||(t[3]=d=>e.targetData.description=d),placeholder:"",name:"description"},null,8,["modelValue"])]),_:1})]),o("div",xt,[t[9]||(t[9]=o("label",{class:"font-semibold"},"Price",-1)),s(m,{prop:"price"},{default:S(()=>[s(p,{type:"number",controls:!1,step:"any",modelValue:e.targetData.price,"onUpdate:modelValue":t[4]||(t[4]=d=>e.targetData.price=d),placeholder:"",name:"price"},null,8,["modelValue"])]),_:1})]),o("div",St,[o("button",{type:"button",class:"bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover rounded-md px-4 py-2 font-semibold",onClick:t[5]||(t[5]=(...d)=>e.close&&e.close(...d)),disabled:e.loading}," Discard ",8,Ct),o("button",{class:"bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover rounded-md px-4 py-2 font-semibold",type:"submit",disabled:e.loading},[e.loading?N("",!0):(v(),y("span",_t," Save ")),e.loading?(v(),y("span",Pt,t[10]||(t[10]=[M(" Please wait... "),o("span",{class:"spinner-border spinner-border-sm align-middle ms-2"},null,-1)]))):N("",!0)],8,$t)])]),_:1},8,["onSubmit","model","rules"])])])])):N("",!0)}const kt=H(mt,[["render",Ft]]),It=L({name:"products",components:{Filter:ct,SvgIcon:Y,TablePagination:oe,ProductModal:kt,TableHeader:le},setup(){const e=ie(),t=g([]),x=g(0),w=g(0),h=g(1),i=g(!1),a=g(""),f=g(!1),m=g(null),p=g({}),n=g(!1),d=g({sortDirection:ne.ASC,sortBy:B.Name}),F=[{label:"NAME",sortBy:B.Name,class:"min-w-150px"},{label:"COMPANY",class:"min-w-150px",display:P()},{label:"PRICE",sortBy:B.Price},{label:"CREATED DATE",class:"min-w-150px",sortBy:B.CreatedAt},{label:"ACTIONS"}];O(()=>{$()}),U(h,()=>{$()});const $=async()=>{var r,u;i.value=!0,e.getProducts({params:{companyId:P()?null:(u=(r=R)==null?void 0:r.getUserInfo())==null?void 0:u.companyId,keyword:a.value.trim()||null,page:h.value,limit:10,...d.value,...p.value},callback:{onSuccess:C=>{t.value=C==null?void 0:C.items,x.value=C==null?void 0:C.totalPage,w.value=C==null?void 0:C.total,h.value=C==null?void 0:C.page},onFinish:C=>{i.value=!1}}})},_=r=>{d.value={...r},$()},A=r=>{h.value=r},I=()=>{n.value=!n.value},l=r=>{var u;(u=m==null?void 0:m.value)==null||u.setId(r),n.value=!n.value},V=r=>{Z.deletionAlert({onConfirmed:()=>{E(r)}})},E=async r=>{i.value=!0,e.deleteProduct({id:r,callback:{onSuccess:u=>{$()},onFinish:u=>{i.value=!1}}})};return{onSort:_,onFilter:r=>{p.value={...r},h.value!==1?h.value=1:$()},toggleFilter:r=>{f.value=r},searchProduct:()=>{h.value!==1?h.value=1:$()},getProducts:$,deleteProduct:V,pageChange:A,numberWithCommas:ae,formatDate:se,toggleNewProduct:I,toggleEditProduct:l,loading:i,productList:t,pageCount:x,currentPage:h,totalElements:w,productModal:m,search:a,isShowFilter:f,isSystemAdmin:P,sortParams:d,tableHeader:F,isModalVisible:n}}}),Vt={class:"bg-card-background text-card-text h-auto w-11/12 mx-auto my-4 rounded-xl flex flex-col items-center lg:w-4/5 lg:mx-auto lg:min-w-[1560px]"},Dt={class:"h-auto w-full flex flex-col items-start my-4 px-8 gap-3"},Nt={class:"h-auto w-full"},At={class:"h-auto w-full overflow-x-scroll mt-4 flex flex-row items-center justify-between md:overflow-hidden md:justify-start md:gap-4"},Et={class:"bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover rounded-md px-4 py-2 font-semibold flex flex-row items-center gap-2 justify-between"},Tt={key:0,class:"text-center"},Bt={key:2,class:"h-auto w-full overflow-x-scroll p-4 md:overflow-hidden"},Mt={class:"md:w-11/12 md:mx-auto md:text-center"},qt={class:"font-bold whitespace-nowrap"},Ut={class:"text-sm"},jt={class:"font-semibold text-nowrap p-4"},Lt={key:0,class:"font-semibold text-nowrap p-4"},Ht={class:"text-nowrap p-4"},Yt={class:"text-nowrap p-4"},Rt={class:"p-7"},Ot={class:"h-auto w-full flex flex-row items-center justify-evenly gap-2"},zt=["onClick"],Wt=["onClick"],Jt={class:"text-danger"},Gt={class:"flex flex-col items-center my-5"},Kt={class:"font-semibold"};function Qt(e,t,x,w,h,i){var I;const a=b("SvgIcon"),f=b("el-icon"),m=b("el-input"),p=b("el-form-item"),n=b("el-form"),d=b("Filter"),F=b("el-empty"),$=b("TableHeader"),_=b("TablePagination"),A=b("ProductModal");return v(),y(j,null,[o("div",Vt,[o("div",Dt,[t[6]||(t[6]=o("h1",{class:"font-bold text-lg"},"Products",-1)),o("div",Nt,[s(n,{onSubmit:z(e.searchProduct,["prevent"])},{default:S(()=>[s(p,{class:"mb-0"},{default:S(()=>[s(m,{placeholder:"Search",modelValue:e.search,"onUpdate:modelValue":t[0]||(t[0]=l=>e.search=l),name:"search",size:"large"},{prefix:S(()=>[s(f,{class:"el-input__icon"},{default:S(()=>[s(a,{icon:"searchIcon"})]),_:1})]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["onSubmit"]),o("div",At,[o("button",{class:G(["flex flex-row gap-2 font-semibold rounded-md px-4 py-2 text-button-text-light hover:text-button-text-light-hover",e.isShowFilter?"bg-button-primary-active":"bg-button-primary hover:bg-button-primary-hover"]),onClick:t[1]||(t[1]=()=>e.toggleFilter(!e.isShowFilter))},[s(a,{icon:"filterIcon"}),t[3]||(t[3]=M(" Filter "))],2),o("button",{class:"bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover rounded-md px-4 py-2 font-semibold flex flex-row items-center gap-2 justify-between",onClick:t[2]||(t[2]=(...l)=>e.toggleNewProduct&&e.toggleNewProduct(...l))},[s(a,{icon:"addIcon"}),t[4]||(t[4]=M(" New "))]),o("button",Et,[s(a,{icon:"logoutIcon"}),t[5]||(t[5]=M(" Export "))])])]),ee(s(d,{hideFilter:()=>e.toggleFilter(!1),onFilter:e.onFilter},null,8,["hideFilter","onFilter"]),[[te,e.isShowFilter]])]),e.loading?(v(),y("div",Tt,t[7]||(t[7]=[o("div",{class:"spinner-border text-primary",role:"status"},[o("span",{class:"sr-only"},"Loading...")],-1)]))):e.productList.length===0?(v(),W(F,{key:1,description:"No Data"})):(v(),y("div",Bt,[o("table",Mt,[o("thead",null,[o("tr",qt,[s($,{headers:e.tableHeader,sortBy:e.sortParams.sortBy,sortDirection:e.sortParams.sortDirection,onSort:e.onSort},null,8,["headers","sortBy","sortDirection","onSort"])])]),o("tbody",Ut,[(v(!0),y(j,null,K(e.productList,l=>{var V;return v(),y("tr",{key:l==null?void 0:l.id},[o("td",jt,k(l==null?void 0:l.name),1),e.isSystemAdmin()?(v(),y("td",Lt,k((V=l==null?void 0:l.company)==null?void 0:V.name),1)):N("",!0),o("td",Ht,k(`$${e.numberWithCommas(l==null?void 0:l.price)}`),1),o("td",Yt,k(e.formatDate(l==null?void 0:l.createdAt,"MMM DD, YYYY")),1),o("td",Rt,[o("div",Ot,[o("button",{class:"bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover font-semibold h-8 w-8 flex items-center justify-center rounded-lg pl-[7.5px] pt-[1px] md:h-10 md:w-10 md:pl-[3px] md:pt-[2px]",onClick:E=>e.toggleEditProduct(l==null?void 0:l.id)},[s(a,{icon:"newReportIcon",classname:"md:h-6 md:w-6"})],8,zt),o("button",{class:"bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover font-semibold h-8 w-8 flex items-center justify-center rounded-lg pl-[7.5px] pt-[1px] md:h-10 md:w-10 md:pl-[3px] md:pb-[3.75px]",onClick:E=>e.deleteProduct(l==null?void 0:l.id)},[o("span",Jt,[s(a,{icon:"trashIcon",classname:"md:h-7 md:w-7"})])],8,Wt)])])])}),128))])])])),o("div",Gt,[o("div",Kt,k(`Showing ${(e.currentPage-1)*10+1} to ${(I=e.productList)==null?void 0:I.length} of ${e.totalElements} entries`),1),e.pageCount>=1?(v(),W(_,{key:0,"total-pages":e.pageCount,total:e.totalElements,"per-page":10,"current-page":e.currentPage,onPageChange:e.pageChange},null,8,["total-pages","total","current-page","onPageChange"])):N("",!0)])]),s(A,{isVisible:e.isModalVisible,close:e.toggleNewProduct,ref:"productModal",loadPage:e.getProducts},null,8,["isVisible","close","loadPage"])],64)}const Xt=H(It,[["render",Qt]]),J=[{value:"products",label:"Products"},{value:"serviceCosts",label:"Service Costs"},{value:"engineeringCosts",label:"Engineering Costs"}],Zt=L({name:"setting",components:{SvgIcon:Y,Products:Xt,CostsPage:tt,PageHeader:pe},setup(){const e=g(J[0].value),t=g(["Settings","Products"]);return{tabs:J,tabIndex:e,CostSettingType:de,breadcrumbs:t,getOption:ce,setActiveTab:w=>{const h=w.target;e.value=h.getAttribute("data-tab-index");const i=J.find(a=>a.value===e.value);i&&(t.value.pop(),t.value.push(i.label))}}}}),eo={class:"bg-screen-background w-full mx-auto h-auto"},to={class:"h-auto w-full flex flex-col items-start p-2"},oo={class:"flex flex-row gap-3 items-center px-4",role:"tablist"},lo=["data-tab-index"],no={key:0},so={key:1},ao={key:2};function ro(e,t,x,w,h,i){const a=b("PageHeader"),f=b("Products"),m=b("CostsPage");return v(),y(j,null,[o("div",eo,[s(a,{title:"Settings",breadcrumbs:e.breadcrumbs},null,8,["breadcrumbs"]),o("div",to,[o("ul",oo,[(v(!0),y(j,null,K(e.tabs,p=>(v(),y("li",{class:"nav-item",key:p.value},[o("div",{class:G(["cursor-pointer font-semibold pt-0 mb-3 hover:text-active-hover hover:border-b-2 hover:border-active-border-hover",{active:e.tabIndex===p.value,"text-link border-b-2 hover:border-link-hover":e.tabIndex===p.value,"text-inactive border-b-2 border-b-inactive-border":p.value!==p.value}]),"data-bs-toggle":"tab",onClick:t[0]||(t[0]=n=>e.setActiveTab(n)),"data-tab-index":p.value,role:"tab"},k(p.label),11,lo)]))),128))])])]),e.tabIndex=="products"?(v(),y("div",no,[s(f)])):e.tabIndex=="serviceCosts"?(v(),y("div",so,[s(m,{label:"Service Costs",type:e.CostSettingType.Service},null,8,["type"])])):e.tabIndex=="engineeringCosts"?(v(),y("div",ao,[s(m,{label:"Engineering Costs",type:e.CostSettingType.Engineer},null,8,["type"])])):N("",!0)],64)}const xo=H(Zt,[["render",ro]]);export{xo as default};
