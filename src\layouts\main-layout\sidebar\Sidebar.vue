<template>
  <!--begin::sidebar-->
  <div
    class="fixed left-0 top-0 bottom-0 right-0 bg-sidebar-background opacity-10 transform ease-in-out"
    :class="{
      'translate-x-0': displaySidebar,
      '-translate-x-full': !displaySidebar,
    }"
    @click="closeSidebar"
  ></div>
  <div
    class="flex flex-col fixed top-0 left-0 h-full w-3/4 bg-sidebar-background transform transition-transform duration-300 ease-in-out z-10 pt-15 md:w-2/5"
    :class="{
      'translate-x-0': displaySidebar,
      '-translate-x-full': !displaySidebar,
    }"
    @click.stop
  >
    <SidebarLogo></SidebarLogo>
    <SidebarMenu :close="closeSidebar"></SidebarMenu>
  </div>

  <!--end::sidebar-->
</template>

<script lang="ts">
import SidebarLogo from "./SidebarLogo.vue";
import SidebarMenu from "./SidebarMenu.vue";
import { defineComponent } from "vue";

export default defineComponent({
  name: "sidebar",
  props: {
    displaySidebar: {
      type: Boolean,
      required: true,
    },
  },
  methods: {
    closeSidebar() {
      this.$emit("update:displaySidebar", false);
    },
  },
  components: {
    SidebarLogo,
    SidebarMenu,
  },
});
</script>
