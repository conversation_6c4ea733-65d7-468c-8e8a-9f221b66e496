<template>
  <div class="bits">
    <div class="row gap-10">
      <div v-if="loading" class="text-center">
        <div class="spinner-border text-primary" role="status">
          <span class="sr-only">Loading...</span>
        </div>
      </div>
      <template v-else>
        <NoEntries
          v-if="drillBitList.length === 0"
          :addNew="toggleNewBitModal"
        />
        <div v-else class="grid grid-cols-1 gap-4 md:grid-cols-2 md:gap-7 lg:grid-cols-3">
          <div
            v-for="item in drillBitList"
            :key="item.id"
            class="relative h-auto w-full bg-minicard-background text-minicard-text-light flex flex-col items-center p-4 rounded-xl border-t-2 border-active my-2 first:mt-3 last:mb-5 font-semibold md:first:mt-0 md:last:mb-0 md:m-0"
          >
            <div class="h-auto w-full flex flex-col gap-3">
              <h5 class="text-xl font-bold">{{ item.bitNo }}</h5>
              <div
                class="flex items-center justify-between border-dashed border-b-[1px] py-3"
              >
                <span>Type</span>
                <span>
                  {{ item.type }}
                </span>
              </div>
              <div
                class="flex items-center justify-between border-dashed border-b-[1px] py-3"
              >
                <span>IADC type</span>
                <span>
                  {{ item.iadcType }}
                </span>
              </div>

              <div class="flex flex-wrap items-center gap-2">
                <div
                  class="flex items-center gap-2 border border-gray-300 border-dashed rounded py-3 px-4"
                >
                  <div>
                    {{ `${numberWithCommas(item.bitSize)} (in)` }}
                  </div>
                  <div class="text-danger">Bit Size</div>
                </div>
                <div
                  class="flex items-center gap-2 border border-gray-300 border-dashed rounded py-3 px-4"
                >
                  <div>
                    {{ `${numberWithCommas(item.depth)} (in)` }}
                  </div>
                  <div>Depth</div>
                </div>
                <div
                  class="flex items-center gap-2 border border-gray-300 border-dashed rounded py-3 px-4"
                >
                  <div>
                    {{ `${numberWithCommas(item.bitRunDuration)} (ft)` }}
                  </div>
                  <div class="text-success">Bit Run Duration</div>
                </div>
              </div>
            </div>
            <div
              class="flex flex-row items-center gap-2 absolute top-0 end-0 -translate-y-1/2"
            >
              <button
                class="p-2 rounded-full bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover"
                @click="toggleEditBitModal(item.id.toString())"
              >
                <span class="svg-icon svg-icon-3">
                  <SvgIcon icon="pencilIcon" />
                </span>
              </button>
              <button
                class="p-2 rounded-full bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover"
                @click="deleteDrillBit(item.id.toString())"
              >
                <span class="svg-icon svg-icon-3 text-danger">
                  <SvgIcon icon="trashIcon" />
                </span>
              </button>
            </div>
          </div>
        </div>
      </template>
    </div>
  </div>
  <BottomTool :addNew="toggleNewBitModal" :showHelpWindow="showCustomize" />
  <BitModal
    :isVisible="isModalVisible"
    :close="toggleNewBitModal"
    ref="bitModal"
    :loadPage="getDrillBits"
  />
</template>

<script lang="ts">
import NoEntries from "@/components/NoEntries.vue";
import SvgIcon from "@/constants/SvgIcon.vue";
import { numberWithCommas } from "@/utils/numberFormatter";
import AlertService from "@/services/AlertService";
import { useDrillBitStore } from "@/stores/drill-bit";
import { defineComponent, inject, onMounted, ref, type Ref } from "vue";
import BottomTool from "@/components/common/BottomTool.vue";
import BitModal from "./BitModal.vue";
import type { Provide } from "@/types/injection-types";

export default defineComponent({
  name: "bits",
  components: {
    SvgIcon,
    BottomTool,
    BitModal,
    NoEntries,
  },
  props: {
    showCustomize: {
      type: Function,
      required: false,
    },
  },
  setup(_props) {
    const drillBitStore = useDrillBitStore();
    const bitModal: Ref<any> = ref<typeof BitModal | null>(null);
    const loading = ref(false);
    const drillBitList = ref<any>([]);
    const dailyReportProvide = inject<Provide.DailyReport>("dailyReport");
    const isModalVisible = ref(false);

    onMounted(() => {
      if (dailyReportProvide?.getDailyReportId()) {
        getDrillBits();
      }
    });

    const getDrillBits = async (
      params = {
        dailyReportId: dailyReportProvide?.getDailyReportId(),
        page: 1,
        limit: 200,
      }
    ): Promise<void> => {
      loading.value = true;

      drillBitStore.getDrillBits({
        params: params,
        callback: {
          onSuccess: (res: any) => {
            drillBitList.value = res?.items;
          },
          onFinish: (_err: any) => {
            loading.value = false;
          },
        },
      });
    };

    const toggleNewBitModal = () => {
      isModalVisible.value = !isModalVisible.value;
    };

    const toggleEditBitModal = (id: string): void => {
      bitModal?.value?.setId(id);
      isModalVisible.value = !isModalVisible.value;
    };

    const deleteDrillBit = (id: string) => {
      AlertService.deletionAlert({
        onConfirmed: () => {
          deleteDrillBitById(id);
        },
      });
    };

    const deleteDrillBitById = async (id: string): Promise<void> => {
      loading.value = true;
      drillBitStore.deleteDrillBit({
        id: id,
        callback: {
          onSuccess: (_res: any) => {
            getDrillBits();
          },
          onFinish: (_err: any) => {
            loading.value = false;
          },
        },
      });
    };

    return {
      loading,
      bitModal,
      drillBitList,
      isModalVisible,
      getDrillBits,
      numberWithCommas,
      deleteDrillBit,
      toggleEditBitModal,
      toggleNewBitModal,
    };
  },
});
</script>
