import{S as ye}from"./SvgIcon-CfrWCA-H.js";import{O as he,P as O,Q as E,d as be,q as g,J as D,U as W,L as V,x as Ie,T as Oe,A as z,V as De,W as we,E as ke,e as Pe,_ as Ve,c as b,o as I,a as l,l as U,B as c,b as a,w as i,r as C,p as Se,H,M as Q}from"./index-DalLS0_6.js";import{f as Ne}from"./date-CCTVzEJd.js";import{s as Le}from"./navigation-guard-9jXpDdGb.js";import{u as Ue}from"./company-DGE9srvS.js";import{h as ue}from"./handleFailure-DrOe_u9W.js";import{u as Ce}from"./customer-CD9RajQq.js";import{u as Fe}from"./user-UjS69U41.js";import{u as Te}from"./well-8ACiu0oO.js";const Ae=he("country",()=>({getCountries:async({params:S,callback:y})=>{var v;const w=O.get(y,"onSuccess",O.noop),N=O.get(y,"onFinish",O.noop);try{E.setHeader();const r=await E.getWithParams("countries",S);w(((v=r.data)==null?void 0:v.data)||r.data)}catch(r){ue(r,y)}finally{N()}},getStates:async({params:S,callback:y})=>{var v;const w=O.get(y,"onSuccess",O.noop),N=O.get(y,"onFinish",O.noop);try{E.setHeader();const r=await E.getWithParams("states",S);w(((v=r.data)==null?void 0:v.data)||r.data)}catch(r){ue(r,y)}finally{N()}}})),Re=be({name:"well-general",components:{SvgIcon:ye},props:{id:{type:String,required:!1}},setup(o){var oe,ae,ne;const e=Pe(),S=Ue(),y=Te(),w=Ae(),N=Fe(),v=Ce(),r=g({companyId:V()?null:(oe=D.getUserInfo())==null?void 0:oe.companyId,engineerIds:[],customerIds:[],supervisorIds:D.checkRole(W.Supervisor)&&((ae=D.getUserInfo())!=null&&ae.id)?[(ne=D.getUserInfo())==null?void 0:ne.id]:[],stateOrProvinceId:"",countryId:"",nameOrNo:"",apiWellNo:"",latitude:null,longitude:null,fieldOrBlock:"",sectionOrTownshipOrRange:"",countyOrParishOrOffshoreArea:"",rigName:"",spudDate:"",stockPoint:"",stockPointContact:"",operator:"",contractor:"",kickOffPoint:null,landingPoint:null,seaLevel:null,airGap:null,waterDepth:null,riserId:null,riserOD:null,chokeLineId:null,killLineId:null,boostLineId:null,rateOfPenetration:!1,revolutionsPerMinute:!1,eccentricity:!1}),p=g(null),m=g(!1),F=g(!1),T=g(!1),n=g(!1),J=g(!1),G=g(!1),s=g(JSON.parse(JSON.stringify(r.value))),X=g([]),q=g([]),Z=g([]),x=g([]),_=g([]),ee=g([]),Y=g(!1);Ie(()=>{var d,t;te(),V()&&ge(),o!=null&&o.id?K(o.id||""):V()||(M((d=D.getUserInfo())==null?void 0:d.companyId),B((t=D.getUserInfo())==null?void 0:t.companyId))});const $=()=>!we.isEqual(s.value,r.value);Oe((d,t,u)=>{Le(d,t,u,$())}),z(()=>o.id,d=>{var t,u;d!==""?K(d||""):V()||(M((t=D.getUserInfo())==null?void 0:t.companyId),B((u=D.getUserInfo())==null?void 0:u.companyId))}),z(()=>s.value.countryId,d=>{q.value=[],Y.value||(s.value={...s.value,stateOrProvinceId:""}),Y.value=!1,d&&le()}),z(()=>s.value.companyId,(d,t)=>{V()&&(t&&(s.value={...s.value,engineerIds:[],customerIds:[],supervisorIds:[]}),d&&(M(d),B(d)))});const pe=V()?{companyId:[{required:!0,message:"Please select Company",trigger:["change","blur"]}],nameOrNo:[{required:!0,message:"Please type Well Name/ No.",trigger:["change","blur"]}]}:{nameOrNo:[{required:!0,message:"Please type Well Name/ No.",trigger:["change","blur"]}]},me=()=>{p.value&&p.value.validate(d=>{var t,u;if(d){if(m.value||!$())return;const k=[...new Set([...(t=s==null?void 0:s.value)==null?void 0:t.supervisorIds,...(u=s==null?void 0:s.value)==null?void 0:u.engineerIds])],P={...s.value,userIds:k,apiWellNo:s.value.apiWellNo||null,fieldOrBlock:s.value.fieldOrBlock||null,latitude:Number(s.value.latitude)||null,longitude:Number(s.value.longitude)||null,sectionOrTownshipOrRange:s.value.sectionOrTownshipOrRange||null,countyOrParishOrOffshoreArea:s.value.countyOrParishOrOffshoreArea||null,countryId:s.value.countryId||null,stateOrProvinceId:s.value.stateOrProvinceId||null,stockPoint:s.value.stockPoint||null,stockPointContact:s.value.stockPointContact||null,operator:s.value.operator||null,contractor:s.value.contractor||null,rigName:s.value.rigName||null,spudDate:Ne(s.value.spudDate)||null,kickOffPoint:Number(s.value.kickOffPoint)||null,landingPoint:Number(s.value.landingPoint)||null,seaLevel:Number(s.value.seaLevel)||null,airGap:Number(s.value.airGap)||null,waterDepth:Number(s.value.waterDepth)||null,riserId:Number(s.value.riserId)||null,riserOD:Number(s.value.riserOD)||null,chokeLineId:Number(s.value.chokeLineId)||null,killLineId:Number(s.value.killLineId)||null,boostLineId:Number(s.value.boostLineId)||null};o!=null&&o.id?ce(P):ve(P)}})},fe=()=>{s.value=JSON.parse(JSON.stringify(r.value))},K=async d=>{m.value=!0,y.getWellDetails({wellId:d,callback:{onSuccess:t=>{var A,R,L,ie,re;M((A=t==null?void 0:t.company)==null?void 0:A.id),B((R=t==null?void 0:t.company)==null?void 0:R.id),Y.value=!0;const u=[],k=[],P=(L=t==null?void 0:t.customers)==null?void 0:L.map(h=>h==null?void 0:h.id);(ie=t==null?void 0:t.users)==null||ie.forEach(h=>{var se,de;(se=h==null?void 0:h.roles)!=null&&se.some(j=>j.value===W.Supervisor)&&k.push(h.id??""),(de=h==null?void 0:h.roles)!=null&&de.some(j=>j.value===W.Engineer)&&u.push(h.id??"")});const f={engineerIds:u,supervisorIds:k,customerIds:P,companyId:(re=t==null?void 0:t.company)==null?void 0:re.id,countryId:t==null?void 0:t.countryId,stateOrProvinceId:t==null?void 0:t.stateOrProvinceId,nameOrNo:t==null?void 0:t.nameOrNo,apiWellNo:t==null?void 0:t.apiWellNo,latitude:t==null?void 0:t.latitude,longitude:t==null?void 0:t.longitude,fieldOrBlock:t==null?void 0:t.fieldOrBlock,sectionOrTownshipOrRange:t==null?void 0:t.sectionOrTownshipOrRange,countyOrParishOrOffshoreArea:t==null?void 0:t.countyOrParishOrOffshoreArea,rigName:t==null?void 0:t.rigName,spudDate:t==null?void 0:t.spudDate,stockPoint:t==null?void 0:t.stockPoint,stockPointContact:t==null?void 0:t.stockPointContact,operator:t==null?void 0:t.operator,contractor:t==null?void 0:t.contractor,kickOffPoint:t==null?void 0:t.kickOffPoint,landingPoint:t==null?void 0:t.landingPoint,seaLevel:t==null?void 0:t.seaLevel,airGap:t==null?void 0:t.airGap,waterDepth:t==null?void 0:t.waterDepth,riserId:t==null?void 0:t.riserId,riserOD:t==null?void 0:t.riserOD,chokeLineId:t==null?void 0:t.chokeLineId,killLineId:t==null?void 0:t.killLineId,boostLineId:t==null?void 0:t.boostLineId,rateOfPenetration:t==null?void 0:t.rateOfPenetration,revolutionsPerMinute:t==null?void 0:t.revolutionsPerMinute,eccentricity:t==null?void 0:t.eccentricity};r.value=JSON.parse(JSON.stringify(f)),s.value=JSON.parse(JSON.stringify(f))},onFinish:()=>{m.value=!1}}})},te=async(d="")=>{F.value=!0,w.getCountries({params:{keyword:d,page:1,limit:500},callback:{onSuccess:t=>{X.value=t==null?void 0:t.items},onFinish:t=>{F.value=!1}}})},le=async(d="")=>{var t;(t=s==null?void 0:s.value)!=null&&t.countryId&&(T.value=!0,w.getStates({params:{keyword:d,countryId:s.value.countryId,page:1,limit:500},callback:{onSuccess:u=>{q.value=u==null?void 0:u.items},onFinish:u=>{T.value=!1}}}))},M=async d=>{J.value=!0,N.getUsers({params:{companyId:d,page:1,limit:500},callback:{onSuccess:t=>{var P;const u=[],k=[];(P=t==null?void 0:t.items)==null||P.forEach(f=>{var A,R;(A=f==null?void 0:f.roles)!=null&&A.some(L=>L.value===W.Supervisor)&&k.push({label:`${f==null?void 0:f.firstName} ${f==null?void 0:f.lastName}`,value:f.id}),(R=f==null?void 0:f.roles)!=null&&R.some(L=>L.value===W.Engineer)&&u.push({label:`${f==null?void 0:f.firstName} ${f==null?void 0:f.lastName}`,value:f.id})}),Z.value=[...u],x.value=[...k]},onFinish:()=>{J.value=!1}}})},B=async d=>{n.value=!0,v.getCustomers({params:{companyId:d,page:1,limit:500},callback:{onSuccess:t=>{_.value=t==null?void 0:t.items.map(u=>({label:u==null?void 0:u.customerName,value:u==null?void 0:u.id}))},onFinish:()=>{n.value=!1}}})},ge=async()=>{G.value=!0,S.getCompanies({params:{page:1,limit:500},callback:{onSuccess:d=>{const t=d==null?void 0:d.items.map(u=>({label:u==null?void 0:u.name,value:u==null?void 0:u.id}));ee.value=[...t]},onFinish:()=>{G.value=!1}}})},ce=async d=>{m.value=!0,y.updateWell({id:(o==null?void 0:o.id)||"",params:d,callback:{onSuccess:t=>{ke.toast("You have updated this well!","success","top"),K(o.id||"")},onFinish:t=>{m.value=!1}}})},ve=async d=>{m.value=!0,y.createWell({params:d,callback:{onSuccess:t=>{r.value=JSON.parse(JSON.stringify(s.value)),e.push({path:`/wells/${t==null?void 0:t.id}`})},onFinish:t=>{m.value=!1}}})};return{targetData:s,loading:m,formRef:p,rules:pe,loadingCountry:F,loadingState:T,loadingCustomer:n,loadingUser:J,loadingCompany:G,countryList:X,stateList:q,customerList:_,engineerList:Z,supervisorList:x,companyList:ee,isSystemAdmin:V,submit:me,reset:fe,getCountries:te,getStates:le,isFormDirty:$,isValidForm:async()=>{var t;return await((t=p==null?void 0:p.value)==null?void 0:t.validate(u=>u))},isJustEngineer:De}}}),We=["model","rules"],Me={key:0,class:"text-center py-7"},Be={key:1},Ee={class:"h-auto w-full mx-auto py-4 bg-card-background text-card-text-light"},Je={class:"h-auto w-full flex flex-row items-center justify-end"},Ge=["disabled"],qe={key:0},Ye={key:1,class:"indicator-progress"},$e={class:"h-auto w-full flex flex-col gap-3"},Ke={key:0},je={key:0,type:"danger",class:"text-semibold text-danger-active text-lg"},ze={key:1},He={key:0,type:"danger",class:"text-semibold text-danger-active text-lg"},Qe={key:1},Xe={key:0,type:"danger",class:"text-semibold text-danger-active text-lg"},Ze={key:1},xe={class:"font-semibold"},_e={class:"font-semibold"},et={class:"font-semibold"},tt={class:"font-semibold"},lt={class:"font-semibold"},ot={class:"font-semibold"},at={class:"font-semibold"},nt={class:"font-semibold"},it={class:"font-semibold"},rt={class:"font-semibold"},st={class:"font-semibold"},dt={class:"font-semibold"},ut={class:"font-semibold"},pt={class:"font-semibold"},mt={class:"h-auto w-full flex flex-row items-center justify-between"},ft={class:"h-auto w-full flex flex-row items-center justify-between"},gt={class:"h-0- w-full flex flex-row items-center justify-between"};function ct(o,e,S,y,w,N){const v=C("el-select-v2"),r=C("el-form-item"),p=C("el-input"),m=C("el-popover"),F=C("el-tooltip"),T=C("el-date-picker");return I(),b("form",{onSubmit:e[32]||(e[32]=Se((...n)=>o.submit&&o.submit(...n),["prevent"])),model:o.targetData,rules:o.rules,ref:"formRef",class:"h-auto w-11/12 text-card-text-light"},[l("div",null,[o.loading?(I(),b("div",Me,e[33]||(e[33]=[l("div",{class:"spinner-border text-primary",role:"status"},[l("span",{class:"sr-only"},"Loading...")],-1)]))):(I(),b("div",Be,[l("div",Ee,[e[35]||(e[35]=l("div",null,[l("h1",{class:"font-bold text-2xl"},"General Information"),l("div",{class:"font-semibold"}," Note some information here for short description ")],-1)),l("div",Je,[l("button",{class:"bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover rounded-md px-4 py-2 font-semibold",type:"submit",disabled:o.loading},[o.loading?U("",!0):(I(),b("span",qe," Save ")),o.loading?(I(),b("span",Ye,e[34]||(e[34]=[c(" Please wait... "),l("span",{class:"spinner-border spinner-border-sm align-middle ms-2"},null,-1)]))):U("",!0)],8,Ge)])])])),l("div",$e,[o.isSystemAdmin()?(I(),b("div",Ke,[e[36]||(e[36]=l("label",{class:"font-semibold"},[c(" Assign Company "),l("span",{class:"text-danger-active font-light"},"*")],-1)),a(r,{prop:"companyId"},{default:i(()=>[a(v,{modelValue:o.targetData.companyId,"onUpdate:modelValue":e[0]||(e[0]=n=>o.targetData.companyId=n),options:o.companyList,placeholder:"Search Company",filterable:"",name:"companyId",loading:o.loadingCompany,disabled:o.isJustEngineer()},null,8,["modelValue","options","loading","disabled"])]),_:1})])):U("",!0),l("div",null,[e[37]||(e[37]=l("label",{class:"font-semibold"}," Assign Engineers ",-1)),a(r,{prop:"engineerIds"},{default:i(()=>[a(v,{modelValue:o.targetData.engineerIds,"onUpdate:modelValue":e[1]||(e[1]=n=>o.targetData.engineerIds=n),options:o.engineerList,placeholder:"Search Engineers",filterable:"",multiple:"",clearable:"",name:"engineerIds",loading:o.loadingUser,disabled:o.isJustEngineer()},{empty:i(()=>[o.isSystemAdmin()&&!o.targetData.companyId?(I(),b("p",je," Please select a company first! ")):o.loadingUser?U("",!0):(I(),b("p",ze,"No data"))]),_:1},8,["modelValue","options","loading","disabled"])]),_:1})]),l("div",null,[e[38]||(e[38]=l("label",{class:"font-semibold"}," Assign Supervisors ",-1)),a(r,{prop:"supervisorIds"},{default:i(()=>[a(v,{modelValue:o.targetData.supervisorIds,"onUpdate:modelValue":e[2]||(e[2]=n=>o.targetData.supervisorIds=n),options:o.supervisorList,placeholder:"Search Supervisor",filterable:"",multiple:"",clearable:"",name:"supervisorIds",loading:o.loadingUser,disabled:o.isJustEngineer()},{empty:i(()=>[o.isSystemAdmin()&&!o.targetData.companyId?(I(),b("p",He," Please select a company first! ")):o.loadingUser?U("",!0):(I(),b("p",Qe,"No data"))]),_:1},8,["modelValue","options","loading","disabled"])]),_:1})]),l("div",null,[e[39]||(e[39]=l("label",{class:"font-semibold"}," Assign Customer ",-1)),a(r,{prop:"customerIds"},{default:i(()=>[a(v,{modelValue:o.targetData.customerIds,"onUpdate:modelValue":e[3]||(e[3]=n=>o.targetData.customerIds=n),options:o.customerList,placeholder:"Search Customer",filterable:"",multiple:"",clearable:"",name:"customerIds",loading:o.loadingCustomer,disabled:o.isJustEngineer()},{empty:i(()=>[o.isSystemAdmin()&&!o.targetData.companyId?(I(),b("p",Xe," Please select a company first! ")):o.loadingCustomer?U("",!0):(I(),b("p",Ze,"No data"))]),_:1},8,["modelValue","options","loading","disabled"])]),_:1})]),l("div",null,[e[40]||(e[40]=l("label",{class:"font-semibold"},[c(" Well Name/ No. "),l("span",{class:"text-danger-active font-light"},"*")],-1)),a(r,{prop:"nameOrNo"},{default:i(()=>[a(p,{modelValue:o.targetData.nameOrNo,"onUpdate:modelValue":e[4]||(e[4]=n=>o.targetData.nameOrNo=n),placeholder:"",name:"nameOrNo"},null,8,["modelValue"])]),_:1})]),l("div",null,[e[41]||(e[41]=l("label",{class:"font-semibold"}," API Well No. ",-1)),a(r,{prop:"apiWellNo"},{default:i(()=>[a(p,{modelValue:o.targetData.apiWellNo,"onUpdate:modelValue":e[5]||(e[5]=n=>o.targetData.apiWellNo=n),placeholder:"",name:"apiWellNo"},null,8,["modelValue"])]),_:1})]),l("div",null,[e[42]||(e[42]=l("label",{class:"font-semibold"}," Field / Block ",-1)),a(r,{prop:"fieldOrBlock"},{default:i(()=>[a(p,{modelValue:o.targetData.fieldOrBlock,"onUpdate:modelValue":e[6]||(e[6]=n=>o.targetData.fieldOrBlock=n),placeholder:"",name:"fieldOrBlock"},null,8,["modelValue"])]),_:1})]),l("div",null,[e[43]||(e[43]=l("label",{class:"font-semibold"},"Latitude (deg)",-1)),a(r,{prop:"latitude"},{default:i(()=>[a(p,{type:"number",controls:!1,step:"any",modelValue:o.targetData.latitude,"onUpdate:modelValue":e[7]||(e[7]=n=>o.targetData.latitude=n),placeholder:"",name:"latitude"},null,8,["modelValue"])]),_:1})]),l("div",null,[e[44]||(e[44]=l("label",{class:"font-semibold"}," Longitude (deg) ",-1)),a(r,{prop:"longitude"},{default:i(()=>[a(p,{type:"number",controls:!1,step:"any",modelValue:o.targetData.longitude,"onUpdate:modelValue":e[8]||(e[8]=n=>o.targetData.longitude=n),placeholder:"",name:"longitude"},null,8,["modelValue"])]),_:1})]),l("div",null,[e[45]||(e[45]=l("label",{class:"font-semibold"},"Section/ Township/ Range",-1)),a(r,{prop:"sectionOrTownshipOrRange"},{default:i(()=>[a(p,{modelValue:o.targetData.sectionOrTownshipOrRange,"onUpdate:modelValue":e[9]||(e[9]=n=>o.targetData.sectionOrTownshipOrRange=n),placeholder:"",name:"sectionOrTownshipOrRange"},null,8,["modelValue"])]),_:1})]),l("div",null,[e[46]||(e[46]=l("label",{class:"font-semibold"}," County/ Parish/ Offshore Area ",-1)),a(r,{prop:"countyOrParishOrOffshoreArea"},{default:i(()=>[a(p,{modelValue:o.targetData.countyOrParishOrOffshoreArea,"onUpdate:modelValue":e[10]||(e[10]=n=>o.targetData.countyOrParishOrOffshoreArea=n),placeholder:"",name:"countyOrParishOrOffshoreArea"},null,8,["modelValue"])]),_:1})]),l("div",null,[e[47]||(e[47]=l("label",{class:"font-semibold"}," Country ",-1)),a(r,{prop:"countryId"},{default:i(()=>[a(v,{modelValue:o.targetData.countryId,"onUpdate:modelValue":e[11]||(e[11]=n=>o.targetData.countryId=n),options:o.countryList,placeholder:"Search Country",filterable:"",clearable:"",remote:"","remote-method":o.getCountries,loading:o.loadingCountry,name:"countryId",props:{label:"name",value:"id"}},null,8,["modelValue","options","remote-method","loading"])]),_:1})]),l("div",null,[e[48]||(e[48]=l("label",{class:"font-semibold"},"State/Province",-1)),a(r,{prop:"stateOrProvinceId"},{default:i(()=>[a(v,{modelValue:o.targetData.stateOrProvinceId,"onUpdate:modelValue":e[12]||(e[12]=n=>o.targetData.stateOrProvinceId=n),options:o.stateList,placeholder:"Search",filterable:"",clearable:"",remote:"","remote-method":o.getStates,loading:o.loadingState,name:"stateOrProvinceId",props:{label:"name",value:"id"}},null,8,["modelValue","options","remote-method","loading"])]),_:1})]),l("div",null,[l("label",xe,[e[51]||(e[51]=l("span",null,"Stock Point",-1)),a(m,{placement:"top",width:300,trigger:"hover"},{reference:i(()=>e[49]||(e[49]=[l("i",{class:"fas fa-exclamation-circle ml-2"},null,-1)])),default:i(()=>[e[50]||(e[50]=l("span",null,[c(' The "Stock Point" is a designated location or storage area where drilling equipment, tools, spare parts, and other materials required for drilling operations are stored, managed, and maintained. It serves as a central hub for the storage and distribution of drilling-related inventory. The stock point is strategically located to provide easy access to the drilling rig or wellsite, allowing for efficient equipment replenishment and maintenance.'),l("br"),l("br"),c(" The stock point is critical for ensuring that the drilling rig has a constant supply of essential equipment and materials, reducing downtime, and supporting continuous drilling activities. It is typically managed by logistics and supply chain personnel who coordinate the movement of items to and from the drilling site. ")],-1))]),_:1})]),a(r,{prop:"stockPoint"},{default:i(()=>[a(p,{modelValue:o.targetData.stockPoint,"onUpdate:modelValue":e[13]||(e[13]=n=>o.targetData.stockPoint=n),placeholder:"",name:"stockPoint"},null,8,["modelValue"])]),_:1})]),l("div",null,[l("label",_e,[e[53]||(e[53]=l("span",null,"Stock Point Contact",-1)),a(F,{content:"Contact information for the stock point",placement:"top",effect:"light"},{default:i(()=>e[52]||(e[52]=[l("i",{class:"fas fa-exclamation-circle ml-2"},null,-1)])),_:1})]),a(r,{prop:"stockPointContact"},{default:i(()=>[a(p,{modelValue:o.targetData.stockPointContact,"onUpdate:modelValue":e[14]||(e[14]=n=>o.targetData.stockPointContact=n),placeholder:"",name:"stockPointContact"},null,8,["modelValue"])]),_:1})]),l("div",null,[l("label",et,[e[56]||(e[56]=l("span",null,"Operator",-1)),a(m,{placement:"top",width:300,trigger:"hover"},{reference:i(()=>e[54]||(e[54]=[l("i",{class:"fas fa-exclamation-circle ml-2"},null,-1)])),default:i(()=>[e[55]||(e[55]=l("span",null,' The "Operator" is typically the company that owns or holds the rights to operate the oil or gas well or drilling project. They are the entity responsible for managing and overseeing all aspects of the well or field operations. ',-1))]),_:1})]),a(r,{prop:"operator"},{default:i(()=>[a(p,{modelValue:o.targetData.operator,"onUpdate:modelValue":e[15]||(e[15]=n=>o.targetData.operator=n),placeholder:"",name:"operator"},null,8,["modelValue"])]),_:1})]),l("div",null,[l("label",tt,[e[59]||(e[59]=l("span",null,"Contractor",-1)),a(m,{placement:"top",width:300,trigger:"hover"},{reference:i(()=>e[57]||(e[57]=[l("i",{class:"fas fa-exclamation-circle ml-2"},null,-1)])),default:i(()=>[e[58]||(e[58]=l("span",null,' The "Contractor" refers to companies or entities hired by the Operator to provide specific services, equipment, or expertise during drilling and well operations. Contractors are often specialized service providers. ',-1))]),_:1})]),a(r,{prop:"contractor"},{default:i(()=>[a(p,{modelValue:o.targetData.contractor,"onUpdate:modelValue":e[16]||(e[16]=n=>o.targetData.contractor=n),placeholder:"",name:"contractor"},null,8,["modelValue"])]),_:1})]),l("div",null,[e[60]||(e[60]=l("label",{class:"font-semibold"},"Rig Name",-1)),a(r,{prop:"rigName"},{default:i(()=>[a(p,{modelValue:o.targetData.rigName,"onUpdate:modelValue":e[17]||(e[17]=n=>o.targetData.rigName=n),placeholder:"",name:"rigName"},null,8,["modelValue"])]),_:1})]),l("div",null,[e[61]||(e[61]=l("label",{class:"font-semibold"}," Spud Date ",-1)),a(r,{prop:"spudDate"},{default:i(()=>[a(T,{type:"date",modelValue:o.targetData.spudDate,"onUpdate:modelValue":e[18]||(e[18]=n=>o.targetData.spudDate=n),placeholder:"MM/DD/YYYY",format:"MM/DD/YYYY","value-format":"YYYY-MM-DD",name:"spudDate"},null,8,["modelValue"])]),_:1})]),l("div",null,[l("label",lt,[e[64]||(e[64]=c("KOP - Kick-Off Point (ft) ")),a(m,{placement:"top",width:300,trigger:"hover"},{reference:i(()=>e[62]||(e[62]=[l("i",{class:"fas fa-exclamation-circle ml-2"},null,-1)])),default:i(()=>[e[63]||(e[63]=l("span",null,' The "Kick-Off Point" is the depth in the well where drilling operations transition from vertical drilling to directional or horizontal drilling. At the KOP, the wellbore is intentionally deviated from the vertical position and begins to follow a specific directional path. This is typically done to access specific geological formations or reservoirs. The KOP depth is measured in feet and represents the point where the drilling trajectory starts to deviate from vertical. ',-1))]),_:1})]),a(r,{prop:"kickOffPoint"},{default:i(()=>[a(p,{type:"number",controls:!1,step:"any",modelValue:o.targetData.kickOffPoint,"onUpdate:modelValue":e[19]||(e[19]=n=>o.targetData.kickOffPoint=n),placeholder:"",name:"kickOffPoint"},null,8,["modelValue"])]),_:1})]),l("div",null,[l("label",ot,[e[67]||(e[67]=c("LP - Landing Point (ft) ")),a(m,{placement:"top",width:300,trigger:"hover"},{reference:i(()=>e[65]||(e[65]=[l("i",{class:"fas fa-exclamation-circle ml-2"},null,-1)])),default:i(()=>[e[66]||(e[66]=l("span",null,' LP stands for "Landing Point." The LP depth is the depth in the well where the directional or horizontal drilling section ends, and the wellbore returns to a vertical or near-vertical orientation. The LP is often located at the depth where the target reservoir or production zone is reached. It is the point at which the drilling assembly or bottom-hole assembly (BHA) "lands" in the desired location. Like the KOP, the LP depth is also measured in feet. ',-1))]),_:1})]),a(r,{prop:"landingPoint"},{default:i(()=>[a(p,{type:"number",controls:!1,step:"any",modelValue:o.targetData.landingPoint,"onUpdate:modelValue":e[20]||(e[20]=n=>o.targetData.landingPoint=n),placeholder:"",name:"landingPoint"},null,8,["modelValue"])]),_:1})]),l("div",null,[l("label",at,[e[70]||(e[70]=c("Sea Level - S/L ")),a(m,{placement:"top",width:300,trigger:"hover"},{reference:i(()=>e[68]||(e[68]=[l("i",{class:"fas fa-exclamation-circle ml-2"},null,-1)])),default:i(()=>[e[69]||(e[69]=l("span",null,' For Offshore only - "S/L" stands for "Sea Level." It represents the reference point at or above the surface of the sea or ocean. Sea level is an important reference for measuring the depth of water, positioning offshore platforms, and assessing potential hazards related to tides and storm surges. ',-1))]),_:1})]),a(r,{prop:"seaLevel"},{default:i(()=>[a(p,{type:"number",controls:!1,step:"any",modelValue:o.targetData.seaLevel,"onUpdate:modelValue":e[21]||(e[21]=n=>o.targetData.seaLevel=n),placeholder:"",name:"seaLevel"},null,8,["modelValue"])]),_:1})]),l("div",null,[l("label",nt,[e[73]||(e[73]=c("Air Gap (ft) ")),a(m,{placement:"top",width:300,trigger:"hover"},{reference:i(()=>e[71]||(e[71]=[l("i",{class:"fas fa-exclamation-circle ml-2"},null,-1)])),default:i(()=>[e[72]||(e[72]=l("span",null,` For Offshore only - The "Air Gap" is the vertical distance between the still water level (SWL) and the lowest point on an offshore platform's structure, such as its deck or equipment. It is expressed in feet and is a critical safety parameter that helps determine the platform's vulnerability to wave action and storm surges. A larger air gap provides greater protection against waves and tidal fluctuations. `,-1))]),_:1})]),a(r,{prop:"airGap"},{default:i(()=>[a(p,{type:"number",controls:!1,step:"any",modelValue:o.targetData.airGap,"onUpdate:modelValue":e[22]||(e[22]=n=>o.targetData.airGap=n),placeholder:"",name:"airGap"},null,8,["modelValue"])]),_:1})]),l("div",null,[l("label",it,[e[76]||(e[76]=c("Water Depth (ft) ")),a(m,{placement:"top",width:300,trigger:"hover"},{reference:i(()=>e[74]||(e[74]=[l("i",{class:"fas fa-exclamation-circle ml-2"},null,-1)])),default:i(()=>[e[75]||(e[75]=l("span",null,' For Offshore only - "Water Depth" refers to the depth of the water at a specific location, typically measured in feet. It is a fundamental parameter used to describe the location of offshore drilling rigs, platforms, and subsea wellheads. Water depth is a key factor in well planning, equipment selection, and riser design. ',-1))]),_:1})]),a(r,{prop:"waterDepth"},{default:i(()=>[a(p,{type:"number",controls:!1,step:"any",modelValue:o.targetData.waterDepth,"onUpdate:modelValue":e[23]||(e[23]=n=>o.targetData.waterDepth=n),placeholder:"",name:"waterDepth"},null,8,["modelValue"])]),_:1})]),l("div",null,[l("label",rt,[e[79]||(e[79]=c("Riser ID (in) ")),a(m,{placement:"top",width:300,trigger:"hover"},{reference:i(()=>e[77]||(e[77]=[l("i",{class:"fas fa-exclamation-circle ml-2"},null,-1)])),default:i(()=>[e[78]||(e[78]=l("span",null,' For Offshore only - "Riser ID" stands for "Riser Inner Diameter" and is measured in inches. It refers to the inside diameter of the riser, which is a vertical conduit used to transport drilling mud, production fluids, and other materials between the seafloor and the drilling platform or vessel. The riser ID is a critical parameter for riser design and fluid circulation. ',-1))]),_:1})]),a(r,{prop:"riserId"},{default:i(()=>[a(p,{type:"number",controls:!1,step:"any",modelValue:o.targetData.riserId,"onUpdate:modelValue":e[24]||(e[24]=n=>o.targetData.riserId=n),placeholder:"",name:"riserId"},null,8,["modelValue"])]),_:1})]),l("div",null,[l("label",st,[e[82]||(e[82]=c("Riser OD (in) ")),a(m,{placement:"top",width:300,trigger:"hover"},{reference:i(()=>e[80]||(e[80]=[l("i",{class:"fas fa-exclamation-circle ml-2"},null,-1)])),default:i(()=>[e[81]||(e[81]=l("span",null,' For Offshore only - "Riser OD" stands for "Riser Outer Diameter" and is measured in inches. It represents the outside diameter of the riser. The riser OD is important for structural integrity and compatibility with other riser and wellhead components. ',-1))]),_:1})]),a(r,{prop:"riserOD"},{default:i(()=>[a(p,{type:"number",controls:!1,step:"any",modelValue:o.targetData.riserOD,"onUpdate:modelValue":e[25]||(e[25]=n=>o.targetData.riserOD=n),placeholder:"",name:"riserOD"},null,8,["modelValue"])]),_:1})]),l("div",null,[l("label",dt,[e[85]||(e[85]=c("Choke line ID (in) ")),a(m,{placement:"top",width:300,trigger:"hover"},{reference:i(()=>e[83]||(e[83]=[l("i",{class:"fas fa-exclamation-circle ml-2"},null,-1)])),default:i(()=>[e[84]||(e[84]=l("span",null,' For Offshore only - The "Choke Line Inner Diameter" is the inside diameter of the choke line, which is a conduit used for well control operations, specifically controlling the flow of fluids from the well during drilling or in emergency situations. It is measured in inches. ',-1))]),_:1})]),a(r,{prop:"chokeLineId"},{default:i(()=>[a(p,{type:"number",controls:!1,step:"any",modelValue:o.targetData.chokeLineId,"onUpdate:modelValue":e[26]||(e[26]=n=>o.targetData.chokeLineId=n),placeholder:"",name:"chokeLineId"},null,8,["modelValue"])]),_:1})]),l("div",null,[l("label",ut,[e[88]||(e[88]=c("Kill line ID (in) ")),a(m,{placement:"top",width:300,trigger:"hover"},{reference:i(()=>e[86]||(e[86]=[l("i",{class:"fas fa-exclamation-circle ml-2"},null,-1)])),default:i(()=>[e[87]||(e[87]=l("span",null,' For Offshore only - The "Kill Line Inner Diameter" is the inside diameter of the kill line, which is another conduit used for well control purposes, especially to introduce heavy fluids or drilling mud into the well to control pressure. It is also measured in inches. ',-1))]),_:1})]),a(r,{prop:"killLineId"},{default:i(()=>[a(p,{type:"number",controls:!1,step:"any",modelValue:o.targetData.killLineId,"onUpdate:modelValue":e[27]||(e[27]=n=>o.targetData.killLineId=n),placeholder:"",name:"killLineId"},null,8,["modelValue"])]),_:1})]),l("div",null,[l("label",pt,[e[91]||(e[91]=c("Boost line ID (in) ")),a(m,{placement:"top",width:300,trigger:"hover"},{reference:i(()=>e[89]||(e[89]=[l("i",{class:"fas fa-exclamation-circle ml-2"},null,-1)])),default:i(()=>[e[90]||(e[90]=l("span",null,' For Offshore only - The "Boost Line Inner Diameter" is the inside diameter of the boost line, which is used to pump fluids or chemicals into the well for various purposes, including well control and stimulation. It is measured in inches. ',-1))]),_:1})]),a(r,{prop:"boostLineId"},{default:i(()=>[a(p,{type:"number",controls:!1,step:"any",modelValue:o.targetData.boostLineId,"onUpdate:modelValue":e[28]||(e[28]=n=>o.targetData.boostLineId=n),placeholder:"",name:"boostLineId"},null,8,["modelValue"])]),_:1})]),l("div",mt,[l("div",null,[e[94]||(e[94]=l("label",{class:"font-semibold"},"Rate of Penetration - ROP (ft/hr)",-1)),a(m,{placement:"top",width:300,trigger:"hover"},{reference:i(()=>e[92]||(e[92]=[l("i",{class:"fas fa-exclamation-circle ml-2"},null,-1)])),default:i(()=>[e[93]||(e[93]=l("span",null,' For use in hydraulics calculation - ROP, which stands for "Rate of Penetration," represents the speed at which a drilling bit advances or penetrates the rock formation during the drilling process. It is typically expressed in units of depth per unit of time, such as feet per hour (ft/hr) or meters per hour (m/hr). ',-1))]),_:1})]),a(r,{prop:"rateOfPenetration"},{default:i(()=>[H(l("input",{class:"h-4 w-4",type:"checkbox",placeholder:"",name:"rateOfPenetration","onUpdate:modelValue":e[29]||(e[29]=n=>o.targetData.rateOfPenetration=n)},null,512),[[Q,o.targetData.rateOfPenetration]])]),_:1})]),l("div",ft,[l("div",null,[e[97]||(e[97]=l("span",{class:"font-semibold"},"Revolutions per Minute (RPM)",-1)),a(m,{placement:"top",width:300,trigger:"hover"},{reference:i(()=>e[95]||(e[95]=[l("i",{class:"fas fa-exclamation-circle ml-2"},null,-1)])),default:i(()=>[e[96]||(e[96]=l("span",null,' For use in hydraulics calculation - RPM, which stands for "Revolutions Per Minute," refers to the speed at which the drilling bit or drill string rotates while drilling. It is measured in rotations or revolutions completed in one minute. ',-1))]),_:1})]),a(r,{prop:"revolutionsPerMinute"},{default:i(()=>[H(l("input",{class:"h-4 w-4",type:"checkbox",placeholder:"",name:"revolutionsPerMinute","onUpdate:modelValue":e[30]||(e[30]=n=>o.targetData.revolutionsPerMinute=n)},null,512),[[Q,o.targetData.revolutionsPerMinute]])]),_:1})]),l("div",gt,[l("div",null,[e[100]||(e[100]=l("span",{class:"font-semibold"},"Eccentricity",-1)),a(m,{placement:"top",width:300,trigger:"hover"},{reference:i(()=>e[98]||(e[98]=[l("i",{class:"fas fa-exclamation-circle ml-2"},null,-1)])),default:i(()=>[e[99]||(e[99]=l("span",null," For use in hydraulics calculation - Eccentricity, in the context of drilling, refers to the deviation of the wellbore or drilling assembly from the vertical axis. It is the measurement of how off-center or non-concentric the drilling assembly is with respect to the desired drilling path. ",-1))]),_:1})]),a(r,{prop:"eccentricity"},{default:i(()=>[H(l("input",{class:"h-4 w-4",type:"checkbox",placeholder:"",name:"eccentricity","onUpdate:modelValue":e[31]||(e[31]=n=>o.targetData.eccentricity=n)},null,512),[[Q,o.targetData.eccentricity]])]),_:1})])])])],40,We)}const Vt=Ve(Re,[["render",ct],["__scopeId","data-v-c12dea48"]]);export{Vt as W};
