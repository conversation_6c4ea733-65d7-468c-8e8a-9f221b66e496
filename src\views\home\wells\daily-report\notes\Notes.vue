<template>
  <div
    class="bg-card-background text-card-text-light w-11/12 mx-auto mb-4 h-auto rounded-xl"
  >
    <div class="h-auto w-11/12 flex flex-col gap-6 mx-auto mt-7 px-3 py-4">
      <h1 class="font-bold">Notes</h1>
      <div class="card-body">
        <div v-if="loading" class="text-center">
          <div class="spinner-border text-primary" role="status">
            <span class="sr-only">Loading...</span>
          </div>
        </div>
        <div v-else class="h-auto w-full">
          <NoEntries
            v-if="noteList.length === 0"
            :addNew="() => toggleNoteModal()"
          />
          <div v-else class="grid grid-cols-1 gap-4 md:grid-cols-2 md:gap-7 lg:grid-cols-3">
            <div
              v-for="item in noteList"
              :key="item?.id"
              class="relative h-auto w-full mx-auto bg-minicard-background text-minicard-text-light flex flex-col items-center p-4 pt-7 rounded-xl border-t-2 border-active my-2 first:mt-4 last:mb-0 font-semibold md:first:mt-0 md:last:mb-0 md:m-0"
            >
              <div class="h-auto w-full flex flex-col gap-3">
                <div class="h-auto w-full flex justify-between">
                  <h5>{{ item?.title }}</h5>
                  {{ formatDate(item?.createdAt, "DD MMM YYYY") }}
                </div>
                <div class="overflow-hidden">
                  <p class="line-clamp-4">
                    {{ item?.notes }}
                  </p>
                </div>
              </div>
              <div
                class="flex flex-row items-center gap-2 absolute top-0 end-0 -translate-y-1/2"
              >
                <button
                  class="p-2 rounded-full bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover"
                  @click="toggleNoteModal(item?.id)"
                >
                  <SvgIcon icon="pencilIcon" />
                </button>
                <button
                  class="p-2 rounded-full bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover"
                  @click="deleteNote(item?.id)"
                >
                  <span class="text-danger">
                    <SvgIcon icon="trashIcon" />
                  </span>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <BottomTool :addNew="toggleNoteModal" :showHelpInfo="false" />
  <NoteModal
    :isVisible="isModalVisible"
    :close="toggleNoteModal"
    ref="noteModal"
    :loadPage="getNotes"
  />
</template>

<script lang="ts">
import NoEntries from "@/components/NoEntries.vue";
import SvgIcon from "@/constants/SvgIcon.vue";
import { formatDate } from "@/utils/date";
import { numberWithCommas } from "@/utils/numberFormatter";
import AlertService from "@/services/AlertService";
import { useNoteStore } from "@/stores/note";
import { defineComponent, inject, onMounted, ref, type Ref } from "vue";
import BottomTool from "@/components/common/BottomTool.vue";
import NoteModal from "./NoteModal.vue";
import type { Provide } from "@/types/injection-types";

export default defineComponent({
  name: "notes",
  components: {
    SvgIcon,
    BottomTool,
    NoteModal,
    NoEntries,
  },
  props: {
    setChildActiveTab: {
      type: Function,
      required: false,
      default: () => {},
    },
  },
  setup(_props) {
    const noteStore = useNoteStore();
    const noteModal: Ref<any> = ref<typeof NoteModal | null>(null);
    const loading = ref<boolean>(false);
    const noteList = ref<any>([]);
    const dailyReportProvide = inject<Provide.DailyReport>("dailyReport");
    const isModalVisible = ref(false);

    onMounted(() => {
      if (dailyReportProvide?.getDailyReportId()) {
        getNotes();
      }
    });

    const getNotes = async (
      params = {
        dailyReportId: dailyReportProvide?.getDailyReportId(),
        page: 1,
        limit: 200,
      }
    ): Promise<void> => {
      loading.value = true;

      noteStore.getNotes({
        params: params,
        callback: {
          onSuccess: (res: any) => {
            noteList.value = JSON.parse(JSON.stringify(res?.items));
          },
          onFinish: (_err: any) => {
            loading.value = false;
          },
        },
      });
    };

    const toggleNoteModal = (id?: string) => {
      if (id) {
        noteModal?.value?.setId(id);
      }
      isModalVisible.value = !isModalVisible.value;
    };

    const deleteNote = (id: string) => {
      AlertService.deletionAlert({
        onConfirmed: () => {
          deleteNoteById(id);
        },
      });
    };

    const deleteNoteById = (id: string) => {
      loading.value = true;
      noteStore.deleteNote({
        id: id,
        callback: {
          onSuccess: (_res: any) => {
            getNotes();
          },
          onFinish: (_err: any) => {
            loading.value = false;
          },
        },
      });
    };

    return {
      loading,
      noteList,
      noteModal,
      isModalVisible,
      numberWithCommas,
      toggleNoteModal,
      deleteNote,
      formatDate,
      getNotes,
    };
  },
});
</script>
