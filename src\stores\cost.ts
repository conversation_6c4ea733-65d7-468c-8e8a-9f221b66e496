import ApiService from "../services/ApiService";
import { get, noop } from "lodash";
import { defineStore } from "pinia";
import { handleFailure } from "../utils/handleFailure";
import type { Callback } from "../types/common";

export const useCostStore = defineStore("cost", () => {
  const getCosts = async ({
    params,
    callback,
  }: {
    params: any;
    callback: Callback;
  }): Promise<void> => {
    const onSuccess = get(callback, "onSuccess", noop);
    const onFinish = get(callback, "onFinish", noop);

    try {
      ApiService.setHeader();
      const response = await ApiService.getWithParams("costs", params);
      onSuccess(response.data?.data || response.data);
    } catch (error) {
      handleFailure(error, callback);
    } finally {
      onFinish();
    }
  };

  const getCostDetails = async ({
    id,
    callback,
  }: {
    id: string;
    callback: Callback;
  }): Promise<void> => {
    const onSuccess = get(callback, "onSuccess", noop);
    const onFinish = get(callback, "onFinish", noop);

    try {
      ApiService.setHeader();
      const response = await ApiService.get(`costs/${id}`);
      onSuccess(response.data?.data || response.data);
    } catch (error) {
      handleFailure(error, callback);
    } finally {
      onFinish();
    }
  };

  const updateCost = async ({
    id,
    params,
    callback,
  }: {
    id: string;
    params: any;
    callback: Callback;
  }): Promise<void> => {
    const onSuccess = get(callback, "onSuccess", noop);
    const onFinish = get(callback, "onFinish", noop);

    try {
      ApiService.setHeader();
      const response = await ApiService.put(`costs/${id}`, params);
      onSuccess(response.data?.data || response.data);
    } catch (error) {
      handleFailure(error, callback);
    } finally {
      onFinish();
    }
  };

  const deleteCost = async ({
    id,
    callback,
  }: {
    id: string;
    callback: Callback;
  }): Promise<void> => {
    const onSuccess = get(callback, "onSuccess", noop);
    const onFinish = get(callback, "onFinish", noop);

    try {
      ApiService.setHeader();
      const response = await ApiService.delete(`costs/${id}`);
      onSuccess(response.data?.data || response.data);
    } catch (error) {
      handleFailure(error, callback);
    } finally {
      onFinish();
    }
  };

  const createCost = async ({
    params,
    callback,
  }: {
    params: any;
    callback: Callback;
  }): Promise<void> => {
    const onSuccess = get(callback, "onSuccess", noop);
    const onFinish = get(callback, "onFinish", noop);

    try {
      ApiService.setHeader();
      const response = await ApiService.post("costs", params);
      onSuccess(response.data?.data || response.data);
    } catch (error) {
      handleFailure(error, callback);
    } finally {
      onFinish();
    }
  };

  const getCostSummary = async ({
    params,
    callback,
  }: {
    params: any;
    callback: Callback;
  }): Promise<void> => {
    const onSuccess = get(callback, "onSuccess", noop);
    const onFinish = get(callback, "onFinish", noop);

    try {
      ApiService.setHeader();
      const response = await ApiService.getWithParams(
        "costs/detail/costSummary",
        params
      );
      onSuccess(response.data?.data || response.data);
    } catch (error) {
      handleFailure(error, callback);
    } finally {
      onFinish();
    }
  };

  return {
    getCosts,
    getCostDetails,
    getCostSummary,
    updateCost,
    deleteCost,
    createCost,
  };
});
