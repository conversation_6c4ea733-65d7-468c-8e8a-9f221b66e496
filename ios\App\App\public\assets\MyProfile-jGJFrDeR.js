import{P as f}from"./PageHeader-3hadTn26.js";import{u as g}from"./user-KFDu8xJF.js";import{U as y}from"./UserDetail-BrRvNZ4N.js";import{d as b,q as l,x as D,i as P,_ as U,c as m,l as p,m as _,a as c,w as M,F as k,r as u,o as n,b as v}from"./index-CGNRhvz7.js";import"./handleFailure-DtTpu7r3.js";import"./SvgIcon-CMhyaXWN.js";import"./Overview-CymoOfok.js";import"./validator-6laVLK0J.js";import"./index.esm-DXW765zG.js";import"./company-oDyd0dWV.js";import"./regex-BLjctcPP.js";import"./AssignUserModal-B-x10c2w.js";import"./UserModal-Ck8RxOB2.js";import"./TablePagination-BmVxunEG.js";import"./d-left-arrow-079-B3YbfCzd.js";import"./TableHeader-C1CWTWQa.js";import"./table-bhK9qpe4.js";import"./date-CvSHk5ED.js";const B=b({name:"my-profile",components:{UserDetail:y,PageHeader:f},setup(){const e=g(),r=l(),o=l(!1),i=["My Profile","Settings"],t=P("userInfo");D(()=>{s()});const s=async()=>{o.value=!0,e.getMyProfile({callback:{onSuccess:a=>{t==null||t.updateUserInfo({...a}),r.value=a},onFinish:()=>{o.value=!1}}})};return{loading:o,userDetail:r,breadcrumbs:i,getMyProfile:s}}}),C={key:0,class:"text-center my-auto"};function H(e,r,o,i,t,s){const a=u("PageHeader"),d=u("UserDetail");return n(),m(k,null,[e.loading?(n(),m("div",C,r[0]||(r[0]=[c("div",{class:"spinner-border text-primary",role:"status"},[c("span",{class:"sr-only"},"Loading...")],-1)]))):p("",!0),!e.loading&&e.userDetail?(n(),_(d,{key:1,userDetail:e.userDetail,reloadUserData:e.getMyProfile},{pageHeader:M(()=>[v(a,{title:"My Profile",breadcrumbs:e.breadcrumbs},null,8,["breadcrumbs"])]),_:1},8,["userDetail","reloadUserData"])):p("",!0)],64)}const O=U(B,[["render",H]]);export{O as default};
