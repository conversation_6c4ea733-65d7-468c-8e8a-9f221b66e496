import moment from "moment";
import momentTz from "moment-timezone";

export function formatDate(
  value: string | null | Date | undefined,
  formatStr = "MM/DD/YYYY"
): string {
  if (value) {
    return moment(new Date(value)).format(formatStr);
  }
  return "";
}

export function formatTime(
  value: string | null | Date | undefined,
  formatStr = "HH:mm"
): string {
  if (value) {
    return moment(value, "HH:mm:ss").format(formatStr);
  }
  return "";
}

export function isValidDate(
  value: string | null | Date | undefined,
  formatStr = "MM/DD/YYYY"
): boolean {
  if (value) {
    return moment(value, formatStr, true).isValid();
  }
  return false;
}

export function formatDateTimezone(
  value: string | null | Date | undefined
): any {
  if (value) {
    return momentTz(value).tz("Etc/GMT+0").format();
  }
  return null;
}

