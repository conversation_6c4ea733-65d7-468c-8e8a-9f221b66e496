import{S as y}from"./SvgIcon-CMhyaXWN.js";import{d as k,u as S,q as c,a8 as P,G as F,e as E,_ as I,c as u,b as t,w as p,r as a,o as f,a as s,B as l,H as C,M as H,l as x}from"./index-CGNRhvz7.js";import{E as V,F as N,a as B}from"./vee-validate-CesDBK0n.js";import{c as M,a as v}from"./index.esm-DXW765zG.js";import{_ as R}from"./well-logo-diufKTHF.js";const $=k({name:"sign-in",components:{Field:B,VForm:N,ErrorMessage:V,SvgIcon:y},setup(){const o=S(),e=E(),g=c(!1),i=c(!0),r=c(!1),b=M().shape({email:v().email().required().label("Email"),password:v().min(4).required().label("Password")});return{setIsHidePassword:n=>{i.value=n},onSubmitLogin:async n=>{o.purgeAuth(),r.value=!0,o.login({params:n,callback:{onSuccess:()=>{e.push({name:"wells-overview"})},onFinish:()=>{r.value=!1},onFailure:w=>{P.fire({text:F.USER_NOT_FOUND,icon:"error",buttonsStyling:!1,confirmButtonText:"Got it!",heightAuto:!1,customClass:{confirmButton:"font-semibold bg-danger-light"}})}}})},login:b,loading:r,isRemember:g,isHidePassword:i}}}),L={class:"h-auto w-4/5 rounded-3xl mx-auto py-6 bg-white rounded-3 overflow-x-scroll align-self-center flex flex-col justify-center items-center"},A={class:"text-center mb-10"},q={class:"text-gray-400 font-bold fs-4"},T={class:"flex flex-col mb-8 mx-3 h-auto w-full"},U={class:"text-danger mt-[0.3rem]"},j={class:"flex flex-col mb-8 mx-3 h-auto w-full"},D={class:"relative"},G={class:"text-danger mt-[0.3rem]"},O={class:"h-auto w-full flex flex-row mb-8 justify-between items-center"},z={class:"place-self-end"},J=["disabled"],K={key:0,class:"text-white font-semibold"},Q={key:1,class:"indicator-progress"};function W(o,e,g,i,r,b){const d=a("router-link"),m=a("Field"),n=a("ErrorMessage"),w=a("SvgIcon"),h=a("VForm");return f(),u("div",L,[t(h,{class:"flex flex-col items-center w-4/5",id:"kt_login_form",onSubmit:o.onSubmitLogin,"validation-schema":o.login,"initial-values":{email:"",password:""}},{default:p(()=>[e[10]||(e[10]=s("div",{class:"text-center h-auto"},[s("img",{src:R})],-1)),s("div",A,[e[4]||(e[4]=s("h1",{class:"text-dark mb-3 font-bold"},"Log In",-1)),s("div",q,[e[3]||(e[3]=l(" New here? ")),t(d,{to:"/sign-up",class:"text-primary font-bold"},{default:p(()=>e[2]||(e[2]=[l(" Create an Account ")])),_:1})])]),s("div",T,[e[5]||(e[5]=s("label",{class:"form-label tracking-wide font-bold text-dark"},"Email",-1)),t(m,{tabindex:"1",class:"rounded-lg bg-grey-300 pl-3",type:"text",name:"email",autocomplete:"off",placeholder:"Enter email"}),s("div",U,[t(n,{name:"email"})])]),s("div",j,[e[6]||(e[6]=s("label",{class:"form-label tracking-wide font-bold text-dark"},"Password",-1)),s("div",D,[t(m,{tabindex:"2",class:"w-full rounded-lg bg-grey-300 pl-3",type:o.isHidePassword?"password":"input",name:"password",autocomplete:"off",placeholder:"Enter password"},null,8,["type"]),s("span",{class:"svg-icon svg-icon-1 eye-icon absolute top-[0.3rem] right-1",onClick:e[0]||(e[0]=()=>o.setIsHidePassword(!o.isHidePassword))},[t(w,{icon:o.isHidePassword?"hidePasswordIcon":"showPasswordIcon"},null,8,["icon"])])]),s("div",G,[t(n,{name:"password"})])]),s("div",O,[s("label",null,[C(s("input",{class:"h-4 w-4",type:"checkbox","onUpdate:modelValue":e[1]||(e[1]=_=>o.isRemember=_)},null,512),[[H,o.isRemember]]),e[7]||(e[7]=s("span",{class:"text-grey-700 pl-2 font-semibold cursor-pointer text-xs whitespace-nowrap"}," Remember me ",-1))]),t(d,{to:"/forgot-password",class:"ms-3 text-primary font-bold text-xs whitespace-nowrap"},{default:p(()=>e[8]||(e[8]=[l(" Forgot Password ? ")])),_:1})]),s("div",z,[s("button",{class:"bg-primary rounded-md px-2 py-1",type:"submit",disabled:o.loading},[o.loading?x("",!0):(f(),u("span",K," Login ")),o.loading?(f(),u("span",Q,e[9]||(e[9]=[l(" Please wait... "),s("span",{class:"spinner-border spinner-border-sm align-middle ms-2"},null,-1)]))):x("",!0)],8,J)])]),_:1},8,["onSubmit","validation-schema"])])}const oe=I($,[["render",W]]);export{oe as default};
