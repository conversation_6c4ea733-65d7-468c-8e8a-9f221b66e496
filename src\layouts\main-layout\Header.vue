<template>
  <!--begin::Header-->
  <div
    class="px-4 pb-4 flex flex-row items-center justify-center bg-header-background pt-15"
  >
    <!--begin::sidebar mobile toggle-->
    <div
      class="cursor-pointer"
      @click="$emit('toggle-sidebar')"
    >
      <SvgIcon :icon="'sidebarMenu'" :classname="classname" />
    </div>
    <!--end::sidebar mobile toggle-->
    <!--begin::Mobile logo-->
    <div class="flex flex-col items-center">
      <router-link to="/" class="flex flex-col items-center">
        <img
          alt="Logo"
          :src="'/media/logos/opslink-dark.png'"
          class="h-20 w-auto md:h-40"
        />
        <h1 v-if="titleProvide?.title.value" class="font-bold text-lg md:text-2xl lg:text-3xl">
          {{ titleProvide?.title.value }}
        </h1>
        <!-- <label class="font-bold text-lg text-light-contrast">OpsLink</label> -->
      </router-link>
    </div>
    <!--end::Mobile logo-->
  </div>
  <!--end::Header-->
</template>
<script lang="ts">
import { defineComponent, inject } from "vue";
import SvgIcon from "@/constants/SvgIcon.vue";
import type { Provide } from "@/types/injection-types";

export default defineComponent({
  name: "Header",
  components: { SvgIcon },
  props: {
    classname: {
      type: String,
      required: false,
    },
  },
  setup(_props) {
    const titleProvide = inject<Provide.Title>("title");

    return {
      titleProvide,
    };
  },
});
</script>
