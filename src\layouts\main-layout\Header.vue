<template>
  <!--begin::Header-->
  <div
    class="px-4 flex flex-row items-center bg-header-background pt-15 md:justify-center"
  >
    <!--begin::sidebar mobile toggle-->
    <div class="cursor-pointer" @click="$emit('toggle-sidebar')">
      <SvgIcon :icon="'sidebarMenu'" :classname="classname" />
    </div>
    <!--end::sidebar mobile toggle-->
    <!--begin::Mobile logo-->
    <div class="h-auto w-full ps-20 md:ps-0 md:flex md:justify-center">
      <h1 v-if="title" class="px-9">{{title || ""}}</h1>
      <router-link v-else to="/" class="flex flex-row items-center gap-4">
        <img
          alt="Logo"
          :src="'/media/logos/opslink-dark.png'"
          class="h-20 w-auto md:h-40"
        />
        <!-- <label class="font-bold text-lg text-light-contrast">OpsLink</label> -->
      </router-link>
      
      <!--end::Mobile logo-->
    </div>
  </div>
  <!--end::Header-->
</template>
<script lang="ts">
import { defineComponent } from "vue";
import SvgIcon from "@/constants/SvgIcon.vue";
export default defineComponent({
  name: "Header",
  components: { SvgIcon },
  props: {
    classname: {
      type: String,
      required: false,
    },
    title: {
      type: String,
      required: false
    }
  },
  setup(_props) {
    return {};
  },
});
</script>
