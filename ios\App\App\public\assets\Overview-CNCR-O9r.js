import{P as ve}from"./PageHeader-l8HvxxsN.js";import{S as G}from"./SvgIcon-CfrWCA-H.js";import{d as Q,e as de,q as x,x as ae,A as oe,_ as K,r as F,c as b,o as f,a as e,b as W,F as U,k as le,B as J,t as n,D as we,h as ye,E as re,G as be,n as ne,m as me,H as te,I as pe,K as xe,J as z,U as Y,l as O,L as $e,M as ke,p as Se,N as Ie}from"./index-DalLS0_6.js";import{n as ce}from"./numberFormatter-C7uP7NWj.js";import{u as he}from"./well-8ACiu0oO.js";import{u as ge,s as Ce,a as _e,b as De,B as We}from"./sample-T_GMviqs.js";import{F as je}from"./Filter-CzRU9wVL.js";import{f as se,a as Re}from"./date-CCTVzEJd.js";import"./handleFailure-DrOe_u9W.js";import"./company-DGE9srvS.js";const Ae=Q({name:"history-tab",props:{closeModal:{type:Function,required:!0},wellId:{type:String,required:!0},date:{type:Array,required:!0}},components:{SvgIcon:G},setup(t){const s=de(),$=ge(),I=x([]),m=x(!1);ae(()=>{t!=null&&t.wellId&&w()}),oe(()=>t==null?void 0:t.wellId,()=>{t!=null&&t.wellId&&w()}),oe(()=>t==null?void 0:t.date,()=>{t!=null&&t.wellId&&w()});const w=async()=>{var h,k;const l={wellId:t==null?void 0:t.wellId,fromDate:((h=t==null?void 0:t.date)==null?void 0:h.length)>0?se(t==null?void 0:t.date[0],"YYYY-MM-DD"):null,toDate:((k=t==null?void 0:t.date)==null?void 0:k.length)>0?se(t==null?void 0:t.date[1],"YYYY-MM-DD"):null,page:1,limit:500};m.value=!0,$.getDailyReports({params:l,callback:{onSuccess:o=>{I.value=o==null?void 0:o.items},onFinish:o=>{m.value=!1}}})};return{loading:m,historyList:I,editReport:l=>{s.push({path:`/wells/${t.wellId}/daily-report/${l}`}),t.closeModal()},formatDate:se,reset:()=>{I.value=[]}}}}),Le={key:0,class:"text-center"},Me={key:1,class:"h-20 w-full flex flex-row items-center justify-center text-dark font-bold"},Ne={key:2,class:"h-auto w-full flex flex-col gap-4 md:text-lg"},Fe={class:"flex flex-row items-center gap-2 text-sm md:text-lg md:justify-center"},Ve={class:"flex flex-col"},Te={class:"font-bold"},Pe={class:"font-semibold text-"},Ye={class:"font-semibold"},Oe={class:"text-link hover:text-link-hover mr-2"},He={class:"text-link hover:text-link-hover"},Be={class:"text-uppercase"},Ee=["onClick"];function ze(t,s,$,I,m,w){const v=F("el-date-picker"),g=F("SvgIcon");return t.loading?(f(),b("div",Le,s[1]||(s[1]=[e("div",{class:"spinner-border text-link hover:text-link-hover",role:"status"},[e("span",{class:"sr-only"},"Loading...")],-1)]))):t.historyList.length===0?(f(),b("div",Me," No Data ")):(f(),b("div",Ne,[W(v,{modelValue:t.date,"onUpdate:modelValue":s[0]||(s[0]=l=>t.date=l),type:"daterange","range-separator":"-","start-placeholder":"From","end-placeholder":"To",format:"MM/DD/YYYY"},null,8,["modelValue"]),(f(!0),b(U,null,le(t.historyList,l=>{var h,k,o,D;return f(),b("div",{key:l.id},[e("div",Fe,[s[5]||(s[5]=e("span",{class:"bg-history-tab-list-marker h-[40px] w-3 mr-4"},null,-1)),e("div",Ve,[e("h4",Te,[J(n(`Daily Report ${t.formatDate(l==null?void 0:l.createdAt,"MM/DD/YYYY hh:mm")}`)+" ",1),e("span",Pe,n(t.formatDate(l==null?void 0:l.createdAt,"a")),1)]),e("span",Ye,[s[2]||(s[2]=J(" Created by ")),e("span",Oe,n(`${((h=l==null?void 0:l.createdBy)==null?void 0:h.firstName)||""} ${((k=l==null?void 0:l.createdBy)==null?void 0:k.lastName)||""}`),1),s[3]||(s[3]=J(" Last updated by ")),e("span",He,n(`${((o=l==null?void 0:l.updatedBy)==null?void 0:o.firstName)||""} ${((D=l==null?void 0:l.updatedBy)==null?void 0:D.lastName)||""}`),1),s[4]||(s[4]=J(" at ")),e("span",Be,n(t.formatDate(l==null?void 0:l.updatedAt,"MM/DD/YYYY hh:mm a")),1)])]),e("button",{class:"bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover h-8 w-8 rounded-lg pl-[7.5px] pt-[1px] md:h-10 md:w-10 md:pl-[6.52px] md:pt-[2px]",onClick:()=>t.editReport(l==null?void 0:l.id)},[W(g,{icon:"pencilIcon",classname:"md:h-7 md:w-7"})],8,Ee)])])}),128))]))}const Ue=K(Ae,[["render",ze]]);function ee(t){let s=getComputedStyle(document.documentElement).getPropertyValue(t);return s&&s.length>0&&(s=s.trim()),s}const qe=Q({name:"sample-info",components:{SvgIcon:G},props:{sample:{type:Object,required:!0},costSummary:{type:Object,required:!0}},setup(){const t=x(),s=x(null);let $={};const I=[{name:"Shear Stress",data:[30,40,40,90,90,70,70]}];return we(()=>{Object.assign($,Je())}),ae(()=>{}),{chart:$,series:I,chartRef:s,sampleData:t,formatDate:se,getOption:ye,formatTime:Re,numberWithCommas:ce,sampleFromOptions:Ce}}}),Je=()=>{const t=ee("--color-label-color"),s=ee("--color-border-color"),$=ee("--color-base-color"),I=ee("--color-light-color");return{chart:{fontFamily:"inherit",type:"area",height:350,toolbar:{show:!1}},plotOptions:{},legend:{show:!1},dataLabels:{enabled:!1},fill:{type:"solid",opacity:1},stroke:{curve:"smooth",show:!0,width:3,colors:[$]},xaxis:{categories:["1","2","3","4","5","6","7"],axisBorder:{show:!1},axisTicks:{show:!1},labels:{style:{colors:t,fontSize:"12px"}},crosshairs:{position:"front",stroke:{color:$,width:1,dashArray:3}},tooltip:{enabled:!1},title:{text:"Shear Rate"}},yaxis:{labels:{style:{colors:t,fontSize:"12px"}},title:{text:"Shear Stress"}},states:{hover:{filter:{type:"none"}},active:{allowMultipleDataPointsSelection:!1,filter:{type:"none"}}},tooltip:{style:{fontSize:"12px"},y:{formatter:function(m){return m.toString()}}},colors:[I],grid:{borderColor:s,strokeDashArray:4,yaxis:{lines:{show:!0}}},markers:{strokeColors:$,strokeWidth:3}}},Ge={class:"flex flex-col gap-3 text-card-text-light md:text-lg md:gap-5"},Qe={class:"flex flex-row flex-wrap items-center justify-between"},Ke={class:"font-light"},Xe={class:"flex flex-row flex-wrap items-center justify-between"},Ze={class:"font-light"},et={class:"flex flex-row flex-wrap items-center justify-between"},tt={class:"font-light"},st={class:"flex flex-row flex-wrap items-center justify-between"},ot={class:"font-light"},lt={class:"flex flex-row flex-wrap items-center justify-between"},nt={class:"font-light"},at={class:"flex flex-row flex-wrap items-center justify-between"},it={class:"font-light"},rt={class:"flex flex-row flex-wrap items-center justify-between"},dt={class:"font-light"},ct={class:"flex flex-row flex-wrap items-center justify-between"},ft={class:"font-light"},ut={class:"flex flex-row flex-wrap items-center justify-between"},bt={class:"font-light"},mt={class:"flex flex-row flex-wrap items-center justify-between"},pt={class:"font-light"},ht={class:"flex flex-row flex-wrap items-center justify-between"},gt={class:"font-light"},vt={class:"flex flex-row flex-wrap items-center justify-between"},wt={class:"font-light"},yt={class:"flex flex-row flex-wrap items-center justify-between"},xt={class:"font-light"},$t={class:"flex flex-row flex-wrap items-center justify-between"},kt={class:"font-light"},St={class:"flex flex-row flex-wrap items-center justify-between"},It={class:"font-light"},Ct={class:"flex flex-row flex-wrap items-center justify-between"},_t={class:"font-light"},Dt={class:"flex flex-row flex-wrap items-center justify-between"},Wt={class:"font-light"},jt={class:"flex flex-col gap-3 md:text-lg md:gap-5"},Rt={class:"d-flex flex-wrap align-items-center justify-content-between"},At={class:"font-light"},Lt={class:"d-flex flex-wrap align-items-center justify-content-between"},Mt={class:"font-light"},Nt={class:"d-flex flex-wrap align-items-center justify-content-between"},Ft={class:"font-light"},Vt={class:"d-flex flex-wrap align-items-center justify-content-between"},Tt={class:"font-light"},Pt={class:"d-flex flex-wrap align-items-center justify-content-between"},Yt={class:"font-light"},Ot={class:"d-flex flex-wrap align-items-center justify-content-between"},Ht={class:"font-light"},Bt={class:"d-flex flex-wrap align-items-center justify-content-between"},Et={class:"font-light"},zt={class:"d-flex flex-wrap align-items-center justify-content-between"},Ut={class:"font-light"},qt={class:"d-flex flex-wrap align-items-center justify-content-between"},Jt={class:"font-light"},Gt={class:"d-flex flex-wrap align-items-center justify-content-between"},Qt={class:"font-light"};function Kt(t,s,$,I,m,w){var g,l,h,k,o,D,R,A,c,i,u,S,_,C,V,L,j,M,H,B,E,r,p,a,y,T,d,N;const v=F("apexchart");return f(),b(U,null,[e("div",Ge,[e("div",Qe,[s[0]||(s[0]=e("span",{class:"font-bold text-lg"},"Sample from:",-1)),e("span",Ke,n(((l=t.getOption((g=t.sample)==null?void 0:g.sampleFrom,t.sampleFromOptions))==null?void 0:l.label)||""),1)]),e("div",Xe,[s[1]||(s[1]=e("span",{class:"font-bold"},"Time:",-1)),e("span",Ze,n(t.formatTime((h=t.sample)==null?void 0:h.timeSampleTaken)),1)]),e("div",et,[s[2]||(s[2]=e("span",{class:"font-bold"},"MW (ppg or lbs/gal):",-1)),e("span",tt,n((k=t.sample)!=null&&k.weightedMud?"Active":"Inactive"),1)]),e("div",st,[s[3]||(s[3]=e("span",{class:"font-bold"},"Funnel Viscosity (sec/qt):",-1)),e("span",ot,n(t.numberWithCommas((o=t.sample)==null?void 0:o.funnelViscosity)),1)]),e("div",lt,[s[4]||(s[4]=e("span",{class:"font-bold"},"PV (Plastic Viscosity) (cP):",-1)),e("span",nt,n(t.numberWithCommas((D=t.sample)==null?void 0:D.plasticViscosity)),1)]),e("div",at,[s[5]||(s[5]=e("span",{class:"font-bold"},"YP (Yield Point) (lbf/100ft2): ",-1)),e("span",it,n(t.numberWithCommas((R=t.sample)==null?void 0:R.yieldPoint)),1)]),e("div",rt,[s[6]||(s[6]=e("span",{class:"font-bold"},"6 rpm:",-1)),e("span",dt,n(t.numberWithCommas((A=t.sample)==null?void 0:A.shearRate6)),1)]),e("div",ct,[s[7]||(s[7]=e("span",{class:"font-bold"},"API filtrate (ml/30min):",-1)),e("span",ft,n(t.numberWithCommas((c=t.sample)==null?void 0:c.apiFiltrate)),1)]),e("div",ut,[s[8]||(s[8]=e("span",{class:"font-bold"},"API Cake: ",-1)),e("span",bt,n(t.numberWithCommas((i=t.sample)==null?void 0:i.apiCakeThickness)),1)]),e("div",mt,[s[9]||(s[9]=e("span",{class:"font-bold"},"pH:",-1)),e("span",pt,n(t.numberWithCommas((u=t.sample)==null?void 0:u.pH)),1)]),e("div",ht,[s[10]||(s[10]=e("span",{class:"font-bold"},"Mud Alkalinity (Pm) (ml): ",-1)),e("span",gt,n(t.numberWithCommas((S=t.sample)==null?void 0:S.mudAlkalinity)),1)]),e("div",vt,[s[11]||(s[11]=e("span",{class:"font-bold"},"Filtrate Alkalinity (Pf) (ml):",-1)),e("span",wt,n(t.numberWithCommas((_=t.sample)==null?void 0:_.filtrateAlkalinity)),1)]),e("div",yt,[s[12]||(s[12]=e("span",{class:"font-bold"},"Chlorides (mg/L):",-1)),e("span",xt,n(t.numberWithCommas((C=t.sample)==null?void 0:C.chlorides)),1)]),e("div",$t,[s[13]||(s[13]=e("span",{class:"font-bold"},"Total Hardness (mg/L):",-1)),e("span",kt,n(t.numberWithCommas((V=t.sample)==null?void 0:V.totalHardness)),1)]),e("div",St,[s[14]||(s[14]=e("span",{class:"font-bold"},"Linear Gel Strength (LGS) (%):",-1)),e("span",It,n(t.numberWithCommas((L=t.sample)==null?void 0:L.linearGelStrengthPercent)),1)])]),s[29]||(s[29]=e("h3",{class:"font-bold text-lgpy-3"},"Cost",-1)),e("div",Ct,[s[15]||(s[15]=e("span",{class:"font-bold"},"Daily Cost:",-1)),e("span",_t,n(t.numberWithCommas((j=t.costSummary)==null?void 0:j.dailyCost)),1)]),e("div",Dt,[s[16]||(s[16]=e("span",{class:"font-bold"},"Cumulative Cost:",-1)),e("span",Wt,n(t.numberWithCommas((M=t.costSummary)==null?void 0:M.cumulativeCost)),1)]),e("div",null,[s[17]||(s[17]=e("p",{class:"font-semibold"},"Rheological Properties",-1)),W(v,{ref:"chartRef",type:"area",options:t.chart,series:t.series},null,8,["options","series"])]),e("div",jt,[s[28]||(s[28]=e("h3",{class:"font-bold text-lg py-3"},"Volumes",-1)),e("div",Rt,[s[18]||(s[18]=e("span",{class:"font-bold"},"Mud Lease/Consignment Volume (bbl)",-1)),e("span",At,n(t.numberWithCommas((H=t.sample)==null?void 0:H.mudLeaseOrConsignmentVolume)),1)]),e("div",Lt,[s[19]||(s[19]=e("span",{class:"font-bold"},"Mud Volume on Location (bbl)",-1)),e("span",Mt,n(t.numberWithCommas((B=t.sample)==null?void 0:B.mudVolumeOnLocation)),1)]),e("div",Nt,[s[20]||(s[20]=e("span",{class:"font-bold"},"Mud Volume in Storage (bbl)",-1)),e("span",Ft,n(t.numberWithCommas((E=t.sample)==null?void 0:E.mudVolumeInStorage)),1)]),e("div",Vt,[s[21]||(s[21]=e("span",{class:"font-bold"},"Over/Under Consigned Volume (bbl)",-1)),e("span",Tt,n(t.numberWithCommas((r=t.sample)==null?void 0:r.overOrUnderConsignedVolume)),1)]),e("div",Pt,[s[22]||(s[22]=e("span",{class:"font-bold"},"Total On Location (gal)",-1)),e("span",Yt,n(t.numberWithCommas((p=t.sample)==null?void 0:p.totalOnLocation)),1)]),e("div",Ot,[s[23]||(s[23]=e("span",{class:"font-bold"},"Daily Usage (gal)",-1)),e("span",Ht,n(t.numberWithCommas((a=t.sample)==null?void 0:a.dailyUsage)),1)]),e("div",Bt,[s[24]||(s[24]=e("span",{class:"font-bold"},"Total Usage (gal)",-1)),e("span",Et,n(t.numberWithCommas((y=t.sample)==null?void 0:y.totalUsage)),1)]),e("div",zt,[s[25]||(s[25]=e("span",{class:"font-bold"},"Total Daily Dilution (bbl)",-1)),e("span",Ut,n(t.numberWithCommas((T=t.sample)==null?void 0:T.totalDailyDilution)),1)]),e("div",qt,[s[26]||(s[26]=e("span",{class:"font-bold"},"New Hole Volume (bbl)",-1)),e("span",Jt,n(t.numberWithCommas((d=t.sample)==null?void 0:d.newHoleVolume)),1)]),e("div",Gt,[s[27]||(s[27]=e("span",{class:"font-bold"},"Dilution / New Hole Ratio",-1)),e("span",Qt,n(t.numberWithCommas((N=t.sample)==null?void 0:N.dilutionOrNewHoleRatio)),1)])])],64)}const Xt=K(qe,[["render",Kt]]),Zt=Q({name:"properties-tab",components:{SvgIcon:G,SampleInfo:Xt},props:{wellId:{type:String,required:!0}},setup(t){const s=ge(),$=_e(),I=De(),m=x(""),w=x(!1),v=x([]),g=x([]),l=x(""),h=x({});ae(()=>{t!=null&&t.wellId&&k()}),oe(()=>t==null?void 0:t.wellId,()=>{t!=null&&t.wellId&&k()});const k=async()=>{t!=null&&t.wellId&&(w.value=!0,s.getLatestDailyReport({wellId:t==null?void 0:t.wellId,callback:{onSuccess:c=>{c!=null&&c.id&&(m.value=c==null?void 0:c.id,o(),D())},onFailure:c=>{var i,u,S,_;re.resultAlert(be[(u=(i=c==null?void 0:c.response)==null?void 0:i.data)==null?void 0:u.errorCode]||((_=(S=c==null?void 0:c.response)==null?void 0:S.data)==null?void 0:_.message)||(c==null?void 0:c.message)||"Sorry, looks like there are some errors detected, please try again.","error")},onFinish:()=>{w.value=!1}}}))},o=async()=>{if(!m.value)return;w.value=!0;const c={dailyReportId:m==null?void 0:m.value,page:1,limit:200};I.getSamples({params:c,callback:{onSuccess:i=>{var u,S;v.value=i==null?void 0:i.items,g.value=(u=i==null?void 0:i.items)==null?void 0:u.map((_,C)=>({value:_==null?void 0:_.id,title:"Sample "+(C+1)})),l.value=(S=i==null?void 0:i.items[0])==null?void 0:S.id},onFinish:i=>{w.value=!1}}})},D=async()=>{if(!m.value)return;w.value=!0;const c={dailyReportId:m==null?void 0:m.value,wellId:t==null?void 0:t.wellId};$.getCostSummary({params:c,callback:{onSuccess:i=>{h.value=i},onFinish:i=>{w.value=!1}}})};return{loading:w,tabs:g,tabIndex:l,sampleList:v,costSummary:h,reset:()=>{m.value="",v.value=[],g.value=[],l.value="",h.value={}},setActiveTab:c=>{const i=c.target;l.value=i.getAttribute("data-tab-index")}}}}),es={key:0,class:"text-center"},ts={key:1},ss={class:"h-auto w-full"},os={class:"h-auto w-full flex flex-row flex-wrap items-center gap-2",role:"tablist"},ls=["data-tab-index"],ns={class:"py-4"};function as(t,s,$,I,m,w){const v=F("el-empty"),g=F("SampleInfo");return t.loading?(f(),b("div",es,s[1]||(s[1]=[e("div",{class:"spinner-border text-primary",role:"status"},[e("span",{class:"sr-only"},"Loading...")],-1)]))):(f(),b("div",ts,[e("div",ss,[e("ul",os,[(f(!0),b(U,null,le(t.tabs,l=>(f(),b("li",{key:l==null?void 0:l.value},[e("div",{class:ne(["whitespace-nowrap cursor-pointer font-semibold hover:text-active-hover hover:border-b-2 hover:border-active-border-hover",{active:t.tabIndex===(l==null?void 0:l.value),"text-active border-b-2 border-active-border":t.tabIndex===(l==null?void 0:l.value),"text-inactive border-b-2 border-inactive-border":t.tabIndex!==(l==null?void 0:l.value)}]),onClick:s[0]||(s[0]=h=>t.setActiveTab(h)),"data-tab-index":l==null?void 0:l.value,role:"tab"},n(l==null?void 0:l.title),11,ls)]))),128))])]),e("div",ns,[t.sampleList.length===0?(f(),me(v,{key:0,description:"No Data"})):(f(!0),b(U,{key:1},le(t.sampleList,l=>(f(),b("div",{key:l==null?void 0:l.id},[te(e("div",null,[W(g,{sample:l,costSummary:t.costSummary},null,8,["sample","costSummary"])],512),[[pe,t.tabIndex==(l==null?void 0:l.id)]])]))),128))])]))}const is=K(Zt,[["render",as]]),rs=Q({name:"well-quick-info-modal",components:{SvgIcon:G,HistoryTab:Ue,PropertiesTab:is},props:{isVisible:{type:Boolean,required:!0},close:{type:Function,required:!0},wellId:{type:String,required:!0}},setup(t){const s=de(),$=he(),I=x([]),m=x("properties"),w=xe(t,"wellId"),v=x(),g=x(!1),l=x(null),h=x(null),k=()=>{t.close(),c()};oe(()=>t.isVisible,i=>{i&&t.wellId&&D(t.wellId)});const o=i=>{const u=i.target;m.value=u.getAttribute("data-tab-index")},D=async i=>{g.value=!0,$.getWellDetails({wellId:i,callback:{onSuccess:u=>{var C,V;const S=(C=u==null?void 0:u.users)==null?void 0:C.filter(L=>{var j;return(j=L.roles)==null?void 0:j.some(M=>M.value===Y.Engineer)}),_=(V=u==null?void 0:u.users)==null?void 0:V.filter(L=>{var j;return(j=L.roles)==null?void 0:j.some(M=>M.value===Y.Supervisor)});v.value={...u,engineers:S,supervisors:_}},onFinish:u=>{g.value=!1}}})},R=()=>{var i,u,S;return Array.isArray((i=v==null?void 0:v.value)==null?void 0:i.dailyReport)?(S=(u=v==null?void 0:v.value)==null?void 0:u.dailyReport)==null?void 0:S.at(-1):null},A=i=>{k(),s.push({path:`/wells/${i}`})},c=()=>{var i,u;v.value=null,(i=l==null?void 0:l.value)==null||i.reset(),(u=h==null?void 0:h.value)==null||u.reset(),I.value=[],m.value="properties"};return{id:w,date:I,wellData:v,tabIndex:m,loading:g,propertiesTabRef:l,isSystemAdmin:z.checkRole(Y.SystemAdmin),setActiveTab:o,numberWithCommas:ce,editWell:A,getLastDailyReport:R,closeModal:k}}}),ds={key:0,class:"fixed top-0 right-0 bottom-0 left-0 z-40 flex items-center"},cs={class:"relative bg-card-background text-card-text-light max-h-11/12 w-11/12 flex flex-col items-center gap-3 mx-auto my-6 p-5 rounded-xl overflow-y-scroll md:text-lg md:p-10 lg:w-2/5 lg:h-3/5"},fs={class:"flex flex-row h-auto w-full items-center justify-between"},us={class:"text-lg font-bold md:text-xl"},bs={class:"h-auto w-full flex flex-col gap-3 rounded-lg border border-light-border border-dashed px-3 pb-6 font-roboto"},ms={key:0,class:"text-center"},ps={key:1,class:"flex flex-col gap-4"},hs={class:"md:flex md:flex-row md:gap-10"},gs={class:"flex flex-col gap-3"},vs={class:"flex flex-col gap-2"},ws={key:0,class:"flex flex-row flex-wrap items-center justify-between"},ys={class:"font-light max-w-1/2 truncate"},xs={class:"flex flex-row flex-wrap items-center justify-between"},$s={class:"font-light max-w-1/2 truncate"},ks={class:"flex flex-row flex-wrap items-center justify-between"},Ss={class:"font-light max-w-1/2 truncate"},Is={class:"flex flex-row flex-wrap items-center justify-between"},Cs={class:"font-light max-w-1/2 truncate"},_s={class:"flex flex-row flex-wrap items-center justify-between"},Ds={class:"font-light max-w-1/2 truncate"},Ws={class:"flex flex-row flex-wrap items-center justify-between"},js={class:"font-light max-w-1/2 truncate"},Rs={class:"flex flex-col gap-3"},As={class:"flex flex-row flex-wrap items-center justify-between"},Ls={class:"font-light max-w-1/2 truncate"},Ms={class:"flex flex-row flex-wrap items-center justify-between"},Ns={class:"font-light max-w-1/2 truncate"},Fs={class:"flex flex-row flex-wrap items-center justify-between"},Vs={class:"font-light max-w-1/2 truncate"},Ts={class:"flex flex-row flex-wrap items-center justify-between"},Ps={class:"font-light max-w-1/2 truncate"},Ys={class:"flex flex-row flex-wrap items-center justify-between"},Os={class:"font-light"},Hs={class:"flex flex-row flex-wrap items-center justify-between"},Bs={class:"font-light"},Es={class:"h-auto w-full pt-3 flex flex-col md:text-xl"},zs={class:"h-auto w-auto flex flex-row justify-start gap-3",role:"tablist"},Us={class:"nav-item"},qs={class:"nav-item"},Js={key:0,class:"h-auto w-full"},Gs={key:0},Qs={key:1};function Ks(t,s,$,I,m,w){var h,k,o,D,R,A,c,i,u,S,_,C,V,L,j,M,H,B,E,r,p,a,y,T;const v=F("SvgIcon"),g=F("PropertiesTab"),l=F("HistoryTab");return t.isVisible?(f(),b("div",ds,[s[18]||(s[18]=e("div",{class:"absolute inset-0 bg-sidebar-background opacity-25"},null,-1)),e("div",cs,[e("div",fs,[e("h3",us,n(((h=t.wellData)==null?void 0:h.nameOrNo)||""),1),e("span",{class:"cursor-pointer",onClick:s[0]||(s[0]=(...d)=>t.closeModal&&t.closeModal(...d))},[W(v,{icon:"closeModalIcon"})])]),e("div",bs,[t.loading?(f(),b("div",ms,s[3]||(s[3]=[e("div",{class:"spinner-border text-primary",role:"status"},[e("span",{class:"sr-only"},"Loading...")],-1)]))):(f(),b("div",ps,[e("div",hs,[e("div",gs,[e("div",vs,[s[10]||(s[10]=e("h3",{class:"text-lg font-bold m-0 pt-3 pb-2 md:text-xl"}," General Information ",-1)),t.isSystemAdmin?(f(),b("div",ws,[s[4]||(s[4]=e("span",{class:"font-bold"},"Company",-1)),e("span",ys,n((o=(k=t.wellData)==null?void 0:k.company)==null?void 0:o.name),1)])):O("",!0),e("div",xs,[s[5]||(s[5]=e("span",{class:"font-bold"},"Customers",-1)),e("span",$s,n((R=(D=t.wellData)==null?void 0:D.customers)==null?void 0:R.map(d=>d==null?void 0:d.customerName).join(", ")),1)]),e("div",ks,[s[6]||(s[6]=e("span",{class:"font-bold"},"Rig Name",-1)),e("span",Ss,n((A=t.wellData)==null?void 0:A.rigName),1)]),e("div",Is,[s[7]||(s[7]=e("span",{class:"font-bold"},"Supervisors",-1)),e("span",Cs,n((i=(c=t.wellData)==null?void 0:c.supervisors)==null?void 0:i.map(d=>`${d==null?void 0:d.firstName} ${d==null?void 0:d.lastName}`).join(", ")),1)]),e("div",_s,[s[8]||(s[8]=e("span",{class:"font-bold"},"Engineers",-1)),e("span",Ds,n((S=(u=t.wellData)==null?void 0:u.engineers)==null?void 0:S.map(d=>`${d==null?void 0:d.firstName} ${d==null?void 0:d.lastName}`).join(", ")),1)]),e("div",Ws,[s[9]||(s[9]=e("span",{class:"font-bold"},"Planned Depth",-1)),e("span",js,n(t.numberWithCommas((_=t.wellData)==null?void 0:_.landingPoint)),1)])])]),e("div",Rs,[s[15]||(s[15]=e("h3",{class:"font-bold my-3"},"Last Report",-1)),e("div",As,[s[11]||(s[11]=e("span",{class:"font-bold"},"MD",-1)),e("span",Ls,n((V=(C=t.getLastDailyReport())==null?void 0:C.wellInformation)==null?void 0:V.measuredDepth),1)]),e("div",Ms,[s[12]||(s[12]=e("span",{class:"font-bold"},"TVG",-1)),e("span",Ns,n((j=(L=t.getLastDailyReport())==null?void 0:L.wellInformation)==null?void 0:j.trueVerticalDepth),1)]),e("div",Fs,[s[13]||(s[13]=e("span",{class:"font-bold"},"Inclination",-1)),e("span",Vs,n((H=(M=t.getLastDailyReport())==null?void 0:M.wellInformation)==null?void 0:H.inclination),1)]),e("div",Ts,[s[14]||(s[14]=e("span",{class:"font-bold"},"Azimuth",-1)),e("span",Ps,n((E=(B=t.getLastDailyReport())==null?void 0:B.wellInformation)==null?void 0:E.azimuth),1)])])]),e("div",Ys,[s[16]||(s[16]=e("span",{class:"font-bold"},"Treatment",-1)),e("span",Os,n((p=(r=t.getLastDailyReport())==null?void 0:r.wellInformation)==null?void 0:p.activity),1)]),e("div",Hs,[s[17]||(s[17]=e("span",{class:"font-bold"},"Notes",-1)),e("span",Bs,n((T=(y=(a=t.getLastDailyReport())==null?void 0:a.notes)==null?void 0:y.slice(-1)[0])==null?void 0:T.notes),1)])]))]),e("div",Es,[e("ul",zs,[e("li",Us,[e("a",{class:ne(["cursor-pointer font-semibold pt-0 mb-3 hover:text-active-hover hover:border-b-2 hover:border-active-border-hover",{active:t.tabIndex==="properties","text-active border-b-2 border-active-border":t.tabIndex==="properties","text-inactive border-b-2 border-inactive-border":t.tabIndex!=="properties"}]),"data-bs-toggle":"tab",onClick:s[1]||(s[1]=d=>t.setActiveTab(d)),"data-tab-index":"properties",role:"tab"}," Properties ",2)]),e("li",qs,[e("a",{class:ne(["cursor-pointer font-semibold pt-0 mb-3 hover:text-active-hover hover:border-b-2 hover:border-active-border-hover",{active:t.tabIndex==="history","text-active border-b-2 border-active-border":t.tabIndex==="history","text-inactive border-b-2 border-inactive-border":t.tabIndex!=="history"}]),"data-bs-toggle":"tab",onClick:s[2]||(s[2]=d=>t.setActiveTab(d)),"data-tab-index":"history",role:"tab"}," History ",2)])])]),t.id?(f(),b("div",Js,[t.tabIndex=="properties"?(f(),b("div",Gs,[W(g,{ref:"propertiesTabRef",wellId:t.id},null,8,["wellId"])])):t.tabIndex=="history"?(f(),b("div",Qs,[W(l,{closeModal:t.closeModal,wellId:t.id,date:t.date},null,8,["closeModal","wellId","date"])])):O("",!0)])):O("",!0)])])):O("",!0)}const Xs=K(rs,[["render",Ks]]),Zs=Q({name:"wells-overview",components:{SvgIcon:G,WellQuickInfoModal:Xs,BottomTool:We,PageHeader:ve,Filter:je},setup(){const t=he(),s=de(),$=x(!1),I=x(!1),m=x(!1),w=x([]),v=x(null),g=x(""),l=z.isJustEngineer(),h=z.getUserInfo(),k=["Home","Wells"],o=x(!1),D=x({}),R=x("");ae(()=>{C()});const A=r=>{R.value=r,m.value=!m.value},c=()=>{m.value=!m.value},i=(r,p)=>{r.stopPropagation(),s.push({path:`/wells/${p}/daily-report`})},u=(r,p)=>{r.stopPropagation(),s.push({path:`/wells/${p}`})},S=(r,p)=>{r.stopPropagation(),w.value=w.value.map(a=>(a==null?void 0:a.id)===p?{...a,loadingArchive:!0}:a),_(p)},_=async r=>{t.updateWell({id:r,params:{archived:!0},callback:{onSuccess:p=>{re.toast("Archived Successfully!","success","top-right"),C(!1)},onFailure:p=>{var a,y,T,d;re.resultAlert(be[(y=(a=p==null?void 0:p.response)==null?void 0:a.data)==null?void 0:y.errorCode]||((d=(T=p==null?void 0:p.response)==null?void 0:T.data)==null?void 0:d.message)||(p==null?void 0:p.message)||"Sorry, looks like there are some errors detected, please try again.","error"),w.value=w.value.map(N=>(N==null?void 0:N.id)===r?{...N,loadingArchive:!1}:N)}}})},C=async(r=!0)=>{var a;const p={keyword:g.value.trim(),page:1,limit:200,archived:I.value,companyId:$e()?null:(a=z.getUserInfo())==null?void 0:a.companyId,...D.value};$.value=r,t.getWells({params:p,callback:{onSuccess:y=>{var T;w.value=JSON.parse(JSON.stringify((T=y==null?void 0:y.items)==null?void 0:T.map(d=>{var fe,ue;const N=(fe=d==null?void 0:d.users)==null?void 0:fe.filter(P=>{var q;return(q=P.roles)==null?void 0:q.some(ie=>ie.value===Y.Engineer)}),X=(ue=d==null?void 0:d.users)==null?void 0:ue.filter(P=>{var q;return(q=P.roles)==null?void 0:q.some(ie=>ie.value===Y.Supervisor)});let Z=!1;return z.checkRole(Y.SystemAdmin)||z.checkRole(Y.CompanyAdmin)?Z=!0:(z.checkRole(Y.Engineer)&&(N!=null&&N.find(P=>(P==null?void 0:P.id)===(h==null?void 0:h.id)))&&(Z=!0),z.checkRole(Y.Supervisor)&&(X!=null&&X.find(P=>(P==null?void 0:P.id)===(h==null?void 0:h.id)))&&(Z=!0)),{...d,engineers:N,supervisors:X,canEdit:Z}})))},onFinish:y=>{$.value=!1}}})};return{onFilter:r=>{D.value={...r},C()},toggleFilter:r=>{o.value=r},addNewWell:()=>{s.push({path:"/wells/new"})},toggleQuickInfoModal:A,numberWithCommas:ce,newDailyReport:i,editWell:u,getWells:C,archiveWell:S,getLastDailyReport:r=>{if(r!=null&&r.dailyReport)return r==null?void 0:r.dailyReport[(r==null?void 0:r.dailyReport.length)-1]},searchWell:()=>{C()},toggleArchive:()=>{C()},getReportWithLatestSample:r=>{var p,a,y;return(y=(a=(p=r==null?void 0:r.reportWithLatestSample)==null?void 0:p[0])==null?void 0:a.samples)==null?void 0:y[0]},toggleModal:c,wellQuickInfoModal:v,wellList:w,loading:$,isArchived:I,breadcrumbs:k,search:g,isJustEngineer:l,UserType:Y,isShowFilter:o,isSystemAdmin:z.checkRole(Y.SystemAdmin),isModalVisible:m,selectedWellId:R}}}),eo={class:"bg-card-background text-card-text-light w-11/12 mx-auto h-auto rounded-xl md:text-lg"},to={class:"h-auto w-11/12 flex flex-col gap-6 mx-auto mt-7 mb-4 px-3 py-4"},so={class:"px-5 flex flex-col gap-2 w-full"},oo={class:"inline-flex items-center cursor-pointer"},lo={class:"flex flex-row items-center relative w-full"},no={class:"absolute top-[0.rem] left-2 z-3"},ao={class:"flex flex-row items-center w-full justify-start gap-4 md:mb-4"},io={key:0,class:"text-center"},ro={key:1,class:"grid grid-cols-1 gap-4 md:grid-cols-2 md:gap-7 lg:grid-cols-4"},co={key:0,description:"No Data"},fo=["onClick"],uo={class:"h-auto w-4/5 py-5 mx-auto flex flex-col gap-2 lg:h-full lg:overflow-hidden"},bo={class:"flex flex-row items-center justify-between"},mo={class:"font-semibold"},po={key:0,class:"bg-danger px-3 py-2 rounded-lg font-semibold"},ho={class:"flex flex-col gap-3"},go={key:0,class:"flex flex-row items-center justify-between pb-3 border-b border-light-border border-dashed"},vo={class:"max-w-1/2 truncate"},wo={class:"flex flex-row items-center justify-between pb-3 border-b border-light-border border-dashed"},yo={class:"max-w-1/2 truncate"},xo={class:"flex flex-row items-center justify-between pb-3 border-b border-light-border border-dashed"},$o={class:"max-w-1/2 truncate"},ko={class:"flex flex-row items-center justify-between pb-3 border-b border-light-border border-dashed"},So={class:"max-w-1/2 truncate"},Io={class:"flex flex-row items-center justify-between pb-3 border-b border-light-border border-dashed"},Co={class:"max-w-1/2 truncate"},_o={class:"flex flex-row items-center justify-between pb-3 border-b border-light-border border-dashed"},Do={class:"max-w-1/2 truncate"},Wo={class:"flex flex-wrap align-items-center justify-content-center"},jo={class:"basis-1/2 border border-light-border border-dashed rounded p-3"},Ro={class:"font-semibold"},Ao={class:"basis-1/2 border border-light-border border-dashed rounded p-3"},Lo={class:"font-semibold"},Mo={class:"basis-1/2 border border-light-border border-dashed rounded p-3"},No={class:"font-semibold"},Fo={class:"basis-1/2 border border-light-border border-dashed rounded p-3"},Vo={class:"font-semibold"},To={class:"basis-1/2 border border-light-border border-dashed rounded p-3"},Po={class:"font-semibold"},Yo={class:"flex flex-col"},Oo={class:"truncate"},Ho={class:"flex flex-col"},Bo={class:"truncate-"},Eo={class:"flex flex-row items-center gap-2 absolute top-0 end-0 -translate-y-1/2"},zo={content:"New Daily Report",placement:"top",effect:"customize"},Uo=["onClick"],qo={key:0,content:"Edit",placement:"top",effect:"customize"},Jo=["onClick"],Go={content:"Export",placement:"top",effect:"customize"},Qo={key:1,content:"Archive",placement:"top",effect:"customize"},Ko=["onClick"],Xo={key:0,class:"svg-icon svg-icon-3"},Zo={key:1,class:"spinner-border text-warning",style:{width:"1.35rem",height:"1.35rem"},role:"status"};function el(t,s,$,I,m,w){const v=F("PageHeader"),g=F("SvgIcon"),l=F("Filter"),h=F("BottomTool"),k=F("WellQuickInfoModal");return f(),b(U,null,[e("div",null,[W(v,{title:"Home",breadcrumbs:t.breadcrumbs},null,8,["breadcrumbs"])]),e("div",eo,[e("div",to,[e("div",so,[s[11]||(s[11]=e("h1",{class:"font-bold text-xl"},"Wells",-1)),e("label",oo,[s[7]||(s[7]=e("span",{class:"font-semibold hover:text-active-text"}," Show Archived ",-1)),te(e("input",{class:"hidden peer",type:"checkbox","onUpdate:modelValue":s[0]||(s[0]=o=>t.isArchived=o),onChange:s[1]||(s[1]=(...o)=>t.toggleArchive&&t.toggleArchive(...o))},null,544),[[ke,t.isArchived]]),s[8]||(s[8]=e("div",{class:"ml-2 relative w-11 h-6 bg-icon-active peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-icon-active rounded-full peer dark:bg-icon-active peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-icon-active dark:peer-checked:bg-icon-active"},null,-1))]),e("form",{onSubmit:s[3]||(s[3]=Se((...o)=>t.searchWell&&t.searchWell(...o),["prevent"]))},[e("div",lo,[e("span",no,[W(g,{icon:"searchIcon",classname:"text-icon-dark"})]),e("span",null,[te(e("input",{class:"bg-searhbar-background text-input-text-dark h-8 pl-8 pr-2 w-full",placeholder:"Search Wells By Name","onUpdate:modelValue":s[2]||(s[2]=o=>t.search=o),name:"search"},null,512),[[Ie,t.search]])])])],32),e("div",ao,[e("button",{class:ne(["flex flex-row gap-2 font-semibold font- rounded-md px-4 py-2 text-button-text-light hover:text-button-text-light-hover md:px-4 md:py-3 md:text-xl",t.isShowFilter?"bg-button-primary-active":"bg-button-primary hover:bg-button-primary-hover"]),onClick:s[4]||(s[4]=()=>t.toggleFilter(!t.isShowFilter))},[W(g,{icon:"filterIcon"}),s[9]||(s[9]=J(" Filter "))],2),t.isJustEngineer?O("",!0):(f(),b("button",{key:0,class:"bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover rounded-md px-4 py-2 font-semibold flex flex-row items-center gap-2 justify-between md:px-4 md:py-3 md:text-xl",onClick:s[5]||(s[5]=(...o)=>t.addNewWell&&t.addNewWell(...o))},[W(g,{icon:"addIcon"}),s[10]||(s[10]=e("span",{class:"indicator-label"}," New ",-1))]))])]),te(W(l,{hideFilter:()=>t.toggleFilter(!1),onFilter:t.onFilter},null,8,["hideFilter","onFilter"]),[[pe,t.isShowFilter]]),e("div",null,[t.loading?(f(),b("div",io,s[12]||(s[12]=[e("div",{class:"spinner-border text-primary",role:"status"},[e("span",{class:"sr-only"},"Loading...")],-1)]))):(f(),b("div",ro,[t.wellList.length===0?(f(),b("div",co)):(f(!0),b(U,{key:1},le(t.wellList,o=>{var D,R,A,c,i,u,S,_,C,V,L,j,M,H,B,E,r,p;return f(),b("div",{key:o.id,class:"bg-minicard-background text-minicard-text-light h-auto w-full rounded-xl shadow-lg border-t-2 border-light-border relative text-sm my-7 first:mt-0 last:mb-0 md:text-lg md:flex md:flex-wrap md:mt-0 md:mb-0 lg:max-h-80",onClick:a=>t.toggleQuickInfoModal((o==null?void 0:o.id)||"")},[e("div",uo,[e("div",bo,[e("h5",mo,n(o==null?void 0:o.nameOrNo),1),o!=null&&o.archived?(f(),b("span",po,"Archived")):O("",!0)]),e("div",ho,[t.isSystemAdmin?(f(),b("div",go,[s[13]||(s[13]=e("span",{class:"font-semibold"},"Company Name",-1)),e("span",vo,n((D=o==null?void 0:o.company)==null?void 0:D.name),1)])):O("",!0),e("div",wo,[s[14]||(s[14]=e("span",{class:"font-semibold"},"Customer",-1)),e("span",yo,n((R=o==null?void 0:o.customers)==null?void 0:R.map(a=>a==null?void 0:a.customerName).join(", ")),1)]),e("div",xo,[s[15]||(s[15]=e("span",{class:"font-semibold"},"Rig Name",-1)),e("span",$o,n(o==null?void 0:o.rigName),1)]),e("div",ko,[s[16]||(s[16]=e("span",{class:"font-semibold"},"Supervisors",-1)),e("span",So,n((A=o==null?void 0:o.supervisors)==null?void 0:A.map(a=>`${a==null?void 0:a.firstName} ${a==null?void 0:a.lastName}`).join(", ")),1)]),e("div",Io,[s[17]||(s[17]=e("span",{class:"font-semibold"},"Engineers",-1)),e("span",Co,n((c=o==null?void 0:o.engineers)==null?void 0:c.map(a=>`${a==null?void 0:a.firstName} ${a==null?void 0:a.lastName}`).join(", ")),1)]),e("div",_o,[s[18]||(s[18]=e("span",{class:"font-semibold"},"Planned Depth",-1)),e("span",Do,n(`${t.numberWithCommas((o==null?void 0:o.landingPoint)||0)} (ft)`),1)])]),e("div",Wo,[e("div",jo,[e("div",Ro,n(`${t.numberWithCommas((u=(i=t.getLastDailyReport(o))==null?void 0:i.wellInformation)==null?void 0:u.measuredDepth)} (ft)`),1),s[19]||(s[19]=e("div",{class:"font-semibold text-card-text-light"},"MD",-1))]),e("div",Ao,[e("div",Lo,n(`${t.numberWithCommas((_=(S=t.getLastDailyReport(o))==null?void 0:S.wellInformation)==null?void 0:_.trueVerticalDepth)} (ft)`),1),s[20]||(s[20]=e("div",{class:"font-semibold text-card-text-light"},"TVD",-1))]),e("div",Mo,[e("div",No,n(`${t.numberWithCommas((C=t.getReportWithLatestSample(o))==null?void 0:C.mudWeight)} (ppg)`),1),s[21]||(s[21]=e("div",{class:"font-semibold text-card-text-light"},"MW",-1))]),e("div",Fo,[e("div",Vo,n(`${t.numberWithCommas((L=(V=t.getLastDailyReport(o))==null?void 0:V.wellInformation)==null?void 0:L.inclination)} (deg)`),1),s[22]||(s[22]=e("div",{class:"font-semibold text-card-text-light"}," Inclination ",-1))]),e("div",To,[e("div",Po,n(`${t.numberWithCommas((M=(j=t.getLastDailyReport(o))==null?void 0:j.wellInformation)==null?void 0:M.azimuth)} (deg)`),1),s[23]||(s[23]=e("div",{class:"font-semibold text-card-text-light"},"Azimuth",-1))])]),e("div",Yo,[s[24]||(s[24]=e("span",{class:"font-semibold"},"Treatment:",-1)),e("span",Oo,n((B=(H=t.getLastDailyReport(o))==null?void 0:H.wellInformation)==null?void 0:B.activity),1)]),e("div",Ho,[s[25]||(s[25]=e("span",{class:"font-semibold"},"Notes:",-1)),e("span",Bo,n((p=(r=(E=t.getLastDailyReport(o))==null?void 0:E.notes)==null?void 0:r.slice(-1)[0])==null?void 0:p.notes),1)])]),e("div",Eo,[e("div",zo,[e("div",{class:"p-2 rounded-full bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover",onClick:a=>{var y;return t.newDailyReport(a,((y=o==null?void 0:o.id)==null?void 0:y.toString())||"")}},[W(g,{icon:"addIcon",classname:"md:h-7 md:w-7"})],8,Uo)]),o!=null&&o.canEdit?(f(),b("div",qo,[e("div",{class:"rounded-full p-1.5 bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover",onClick:a=>{var y;return t.editWell(a,((y=o==null?void 0:o.id)==null?void 0:y.toString())||"")}},[W(g,{icon:"pencilIcon",classname:"md:h-7 md:w-7"})],8,Jo)])):O("",!0),e("div",Go,[e("button",{class:"rounded-full p-1.5 bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover",onClick:s[6]||(s[6]=a=>{a.stopPropagation()})},[W(g,{icon:"exportIcon",classname:"md:h-7 md:w-7"})])]),o.archived?O("",!0):(f(),b("div",Qo,[e("button",{class:"rounded-full p-1.5 bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover",onClick:a=>{var y;return t.archiveWell(a,((y=o==null?void 0:o.id)==null?void 0:y.toString())||"")}},[o!=null&&o.loadingArchive?(f(),b("div",Zo,s[26]||(s[26]=[e("span",{class:"sr-only"},"Loading...",-1)]))):(f(),b("span",Xo,[W(g,{icon:"archiveIcon",classname:"md:h-7 md:w-7"})]))],8,Ko)]))])],8,fo)}),128))]))])])]),t.isJustEngineer?O("",!0):(f(),me(h,{key:0,addNew:t.addNewWell,showHelpInfo:!1},null,8,["addNew"])),W(k,{isVisible:t.isModalVisible,close:t.toggleModal,wellId:t.selectedWellId},null,8,["isVisible","close","wellId"])],64)}const fl=K(Zs,[["render",el]]);export{fl as default};
