<template>
  <div
    class="bg-card-background text-card-text-light w-11/12 h-auto rounded-xl md:text-lg"
  >
    <div class="h-auto w-11/12 flex flex-col gap-6 mx-auto mt-7 mb-4 px-3 py-4">
      <!--begin::Card header-->
      <div class="px-5 flex flex-col gap-2 w-full">
        <h1 class="font-bold text-xl">Wells</h1>
        <label class="inline-flex items-center cursor-pointer">
          <span class="font-semibold hover:text-active-text">
            Show Archived
          </span>
          <input
            class="hidden peer"
            type="checkbox"
            v-model="isArchived"
            @change="toggleArchive"
          />
          <div
            class="ml-2 relative w-11 h-6 bg-icon-active peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-icon-active rounded-full peer dark:bg-icon-active peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-icon-active dark:peer-checked:bg-icon-active"
          ></div>
        </label>
        <form @submit.prevent="searchWell">
          <div class="flex flex-row items-center relative w-full">
            <span class="absolute top-[0.rem] left-2 z-3">
              <SvgIcon icon="searchIcon" classname="text-icon-dark" />
            </span>
            <span>
              <input
                class="bg-searhbar-background text-input-text-dark h-8 pl-8 pr-2 w-full"
                placeholder="Search Wells By Name"
                v-model="search"
                name="search"
              />
            </span>
          </div>
        </form>
        <div
          class="flex flex-row items-center w-full justify-start gap-4 md:mb-4"
        >
          <button
            class="flex flex-row gap-2 font-semibold font- rounded-md px-4 py-2 text-button-text-light hover:text-button-text-light-hover md:px-4 md:py-3 md:text-xl"
            :class="
              isShowFilter
                ? 'bg-button-primary-active'
                : 'bg-button-primary hover:bg-button-primary-hover'
            "
            @click="() => toggleFilter(!isShowFilter)"
          >
            <SvgIcon icon="filterIcon" />
            Filter
          </button>
          <button
            v-if="!isJustEngineer"
            class="bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover rounded-md px-4 py-2 font-semibold flex flex-row items-center gap-2 justify-between md:px-4 md:py-3 md:text-xl"
            @click="addNewWell"
          >
            <SvgIcon icon="addIcon" />
            <span class="indicator-label"> New </span>
          </button>
        </div>
      </div>
      <!--end::Card header-->
      <Filter
        v-show="isShowFilter"
        :hideFilter="() => toggleFilter(false)"
        :onFilter="onFilter"
      />

      <!--begin::Card body-->
      <div>
        <!-- This v-if isn't used -->
        <div v-if="loading" class="text-center">
          <div class="spinner-border text-primary" role="status">
            <span class="sr-only">Loading...</span>
          </div>
        </div>
        <div
          v-else
          class="grid grid-cols-1 gap-4 md:grid-cols-2 md:gap-7 lg:grid-cols-4"
        >
          <div v-if="wellList.length === 0" description="No Data" />
          <div
            v-else
            v-for="item in wellList"
            :key="item.id!"
            class="bg-minicard-background text-minicard-text-light h-auto w-full rounded-xl shadow-lg border-t-2 border-light-border relative text-sm my-7 first:mt-0 last:mb-0 md:text-lg md:flex md:flex-wrap md:mt-0 md:mb-0 lg:max-h-80"
            @click="toggleQuickInfoModal(item?.id || '')"
          >
            <div
              class="h-auto w-4/5 py-5 mx-auto flex flex-col gap-2 lg:h-full lg:overflow-hidden"
            >
              <div class="flex flex-row items-center justify-between">
                <h5 class="font-semibold">
                  {{ item?.nameOrNo }}
                </h5>
                <span
                  v-if="item?.archived"
                  class="bg-danger px-3 py-2 rounded-lg font-semibold"
                  >Archived</span
                >
              </div>

              <div class="flex flex-col gap-3">
                <div
                  v-if="isSystemAdmin"
                  class="flex flex-row items-center justify-between pb-3 border-b border-light-border border-dashed"
                >
                  <span class="font-semibold">Company Name</span>
                  <span class="max-w-1/2 truncate">
                    {{ item?.company?.name }}
                  </span>
                </div>
                <div
                  class="flex flex-row items-center justify-between pb-3 border-b border-light-border border-dashed"
                >
                  <span class="font-semibold">Customer</span>
                  <span class="max-w-1/2 truncate">
                    {{
                      item?.customers
                        ?.map((customer) => customer?.customerName)
                        .join(", ")
                    }}
                  </span>
                </div>
                <div
                  class="flex flex-row items-center justify-between pb-3 border-b border-light-border border-dashed"
                >
                  <span class="font-semibold">Rig Name</span>
                  <span class="max-w-1/2 truncate">
                    {{ item?.rigName }}
                  </span>
                </div>
                <div
                  class="flex flex-row items-center justify-between pb-3 border-b border-light-border border-dashed"
                >
                  <span class="font-semibold">Supervisors</span>
                  <span class="max-w-1/2 truncate">
                    {{
                      item?.supervisors
                        ?.map(
                          (supervisor: User.Info) =>
                            `${supervisor?.firstName} ${supervisor?.lastName}`
                        )
                        .join(", ")
                    }}
                  </span>
                </div>
                <div
                  class="flex flex-row items-center justify-between pb-3 border-b border-light-border border-dashed"
                >
                  <span class="font-semibold">Engineers</span>
                  <span class="max-w-1/2 truncate">
                    {{
                      item?.engineers
                        ?.map(
                          (engineer: User.Info) =>
                            `${engineer?.firstName} ${engineer?.lastName}`
                        )
                        .join(", ")
                    }}
                  </span>
                </div>
                <div
                  class="flex flex-row items-center justify-between pb-3 border-b border-light-border border-dashed"
                >
                  <span class="font-semibold">Planned Depth</span>
                  <span class="max-w-1/2 truncate">
                    {{ `${numberWithCommas(item?.landingPoint || 0)} (ft)` }}
                  </span>
                </div>
              </div>
              <div
                class="flex flex-wrap align-items-center justify-content-center"
              >
                <div
                  class="basis-1/2 border border-light-border border-dashed rounded p-3"
                >
                  <div class="font-semibold">
                    {{
                      `${numberWithCommas(
                        getLastDailyReport(item)?.wellInformation?.measuredDepth
                      )} (ft)`
                    }}
                  </div>
                  <div class="font-semibold text-card-text-light">MD</div>
                </div>
                <div
                  class="basis-1/2 border border-light-border border-dashed rounded p-3"
                >
                  <div class="font-semibold">
                    {{
                      `${numberWithCommas(
                        getLastDailyReport(item)?.wellInformation
                          ?.trueVerticalDepth
                      )} (ft)`
                    }}
                  </div>
                  <div class="font-semibold text-card-text-light">TVD</div>
                </div>
                <div
                  class="basis-1/2 border border-light-border border-dashed rounded p-3"
                >
                  <div class="font-semibold">
                    {{
                      `${numberWithCommas(
                        getReportWithLatestSample(item)?.mudWeight
                      )} (ppg)`
                    }}
                  </div>
                  <div class="font-semibold text-card-text-light">MW</div>
                </div>
                <div
                  class="basis-1/2 border border-light-border border-dashed rounded p-3"
                >
                  <div class="font-semibold">
                    {{
                      `${numberWithCommas(
                        getLastDailyReport(item)?.wellInformation?.inclination
                      )} (deg)`
                    }}
                  </div>
                  <div class="font-semibold text-card-text-light">
                    Inclination
                  </div>
                </div>
                <div
                  class="basis-1/2 border border-light-border border-dashed rounded p-3"
                >
                  <div class="font-semibold">
                    {{
                      `${numberWithCommas(
                        getLastDailyReport(item)?.wellInformation?.azimuth
                      )} (deg)`
                    }}
                  </div>
                  <div class="font-semibold text-card-text-light">Azimuth</div>
                </div>
              </div>
              <div class="flex flex-col">
                <span class="font-semibold">Treatment:</span>
                <span class="truncate">
                  {{ getLastDailyReport(item)?.wellInformation?.activity }}
                </span>
              </div>
              <div class="flex flex-col">
                <span class="font-semibold">Notes:</span>
                <span class="truncate-">
                  {{ getLastDailyReport(item)?.notes?.slice(-1)[0]?.notes }}
                </span>
              </div>
            </div>
            <div
              class="flex flex-row items-center gap-2 absolute top-0 end-0 -translate-y-1/2"
            >
              <div
                content="New Daily Report"
                placement="top"
                effect="customize"
              >
                <div
                  class="p-2 rounded-full bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover"
                  @click="(e) => newDailyReport(e, item?.id?.toString() || '')"
                >
                  <SvgIcon icon="addIcon" classname="md:h-7 md:w-7" />
                </div>
              </div>
              <div
                v-if="item?.canEdit"
                content="Edit"
                placement="top"
                effect="customize"
              >
                <div
                  class="rounded-full p-1.5 bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover"
                  @click="(e) => editWell(e, item?.id?.toString() || '')"
                >
                  <SvgIcon icon="pencilIcon" classname="md:h-7 md:w-7" />
                </div>
              </div>
              <div content="Export" placement="top" effect="customize">
                <button
                  class="rounded-full p-1.5 bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover"
                  @click="
                    (e) => {
                      e.stopPropagation();
                    }
                  "
                >
                  <SvgIcon icon="exportIcon" classname="md:h-7 md:w-7" />
                </button>
              </div>
              <div
                content="Archive"
                placement="top"
                effect="customize"
                v-if="!item.archived"
              >
                <button
                  class="rounded-full p-1.5 bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover"
                  @click="(e) => archiveWell(e, item?.id?.toString() || '')"
                >
                  <span
                    v-if="!item?.loadingArchive"
                    class="svg-icon svg-icon-3"
                  >
                    <SvgIcon icon="archiveIcon" classname="md:h-7 md:w-7" />
                  </span>
                  <div
                    v-else
                    class="spinner-border text-warning"
                    style="width: 1.35rem; height: 1.35rem"
                    role="status"
                  >
                    <span class="sr-only">Loading...</span>
                  </div>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <BottomTool
    v-if="!isJustEngineer"
    :addNew="addNewWell"
    :showHelpInfo="false"
  />
  <WellQuickInfoModal
    :isVisible="isModalVisible"
    :close="toggleModal"
    :wellId="selectedWellId"
  />
</template>

<script lang="ts">
import SvgIcon from "@/constants/SvgIcon.vue";
import { ExceptionCode, ExceptionMessages } from "@/constants/exceptions";
import { UserType } from "@/constants/user";
import { numberWithCommas } from "@/utils/numberFormatter";
import AlertService from "@/services/AlertService";
import JwtService, { isSystemAdmin } from "@/services/JwtService";
import { useWellStore } from "@/stores/well";
import { defineComponent, onMounted, ref } from "vue";
import { useRouter } from "vue-router";
import BottomTool from "@/components/common/BottomTool.vue";
import Filter from "@/components/filters/Filter.vue";
import WellQuickInfoModal from "./WellQuickInfoModal.vue";

export default defineComponent({
  name: "wells-overview",
  components: {
    SvgIcon,
    WellQuickInfoModal,
    BottomTool,
    Filter,
  },
  setup() {
    const wellStore = useWellStore();
    const router = useRouter();
    const loading = ref(false);
    const isArchived = ref(false);
    const isModalVisible = ref(false);
    const wellList = ref<Well.GeneralInfo[]>([]);
    const wellQuickInfoModal = ref<typeof WellQuickInfoModal | null>(null);
    const search = ref<string>("");
    const isJustEngineer = JwtService.isJustEngineer(); // user has just 1 role engineer
    const userInfo = JwtService.getUserInfo();
    const breadcrumbs = ["Home", "Wells"];
    const isShowFilter = ref(false);
    const filterParams = ref<Filter.FilterForm>({});
    const selectedWellId = ref("");

    onMounted(() => {
      getWells();
    });

    const toggleQuickInfoModal = (id: string): void => {
      selectedWellId.value = id;
      isModalVisible.value = !isModalVisible.value;
    };

    const toggleModal = () => {
      isModalVisible.value = !isModalVisible.value;
    };

    const newDailyReport = (e: any, id: string) => {
      e.stopPropagation();
      router.push({ path: `/wells/${id}/daily-report` });
    };

    const editWell = (e: any, id: string) => {
      e.stopPropagation();
      router.push({ path: `/wells/${id}` });
    };

    const archiveWell = (e: any, id: string) => {
      e.stopPropagation();
      wellList.value = wellList.value.map((item) => {
        if (item?.id === id) {
          return {
            ...item,
            loadingArchive: true,
          };
        }

        return item;
      });
      updateWell(id);
    };

    const updateWell = async (id: string): Promise<void> => {
      wellStore.updateWell({
        id: id,
        params: {
          archived: true,
        },
        callback: {
          onSuccess: (_res: any) => {
            AlertService.toast(
              "Archived Successfully!",
              "success",
              "top-right"
            );
            getWells(false);
          },
          onFailure: (error: any) => {
            AlertService.resultAlert(
              ExceptionMessages[
                error?.response?.data?.errorCode as ExceptionCode
              ] ||
                error?.response?.data?.message ||
                error?.message ||
                "Sorry, looks like there are some errors detected, please try again.",
              "error"
            );
            wellList.value = wellList.value.map((item) => {
              if (item?.id === id) {
                return {
                  ...item,
                  loadingArchive: false,
                };
              }

              return item;
            });
          },
        },
      });
    };

    const getWells = async (showLoading = true): Promise<void> => {
      const params: Well.GetParams = {
        keyword: search.value.trim(),
        page: 1,
        limit: 200,
        archived: isArchived.value,
        companyId: isSystemAdmin() ? null : JwtService.getUserInfo()?.companyId,
        ...filterParams.value,
      };

      loading.value = showLoading;

      wellStore.getWells({
        params,
        callback: {
          onSuccess: (res: any) => {
            wellList.value = JSON.parse(
              JSON.stringify(
                res?.items?.map((item: Well.GeneralInfo) => {
                  const engineers = item?.users?.filter((user) =>
                    user.roles?.some((role) => role.value === UserType.Engineer)
                  );
                  const supervisors = item?.users?.filter((user) =>
                    user.roles?.some(
                      (role) => role.value === UserType.Supervisor
                    )
                  );
                  let canEdit = false;

                  if (
                    JwtService.checkRole(UserType.SystemAdmin) ||
                    JwtService.checkRole(UserType.CompanyAdmin)
                  ) {
                    canEdit = true;
                  } else {
                    if (
                      JwtService.checkRole(UserType.Engineer) &&
                      engineers?.find(
                        (engineer) => engineer?.id === userInfo?.id
                      )
                    ) {
                      canEdit = true;
                    }

                    if (
                      JwtService.checkRole(UserType.Supervisor) &&
                      supervisors?.find(
                        (supervisor) => supervisor?.id === userInfo?.id
                      )
                    ) {
                      canEdit = true;
                    }
                  }

                  return {
                    ...item,
                    engineers,
                    supervisors,
                    canEdit,
                  };
                })
              )
            );
          },
          onFinish: (_err: any) => {
            loading.value = false;
          },
        },
      });
    };

    const getLastDailyReport = (item: any) => {
      if (item?.dailyReport) {
        return item?.dailyReport[item?.dailyReport.length - 1];
      }
    };

    const getReportWithLatestSample = (item: any) => {
      return item?.reportWithLatestSample?.[0]?.samples?.[0];
    };

    const addNewWell = () => {
      router.push({ path: `/wells/new` });
    };

    const searchWell = () => {
      getWells();
    };

    const toggleArchive = () => {
      getWells();
    };

    const toggleFilter = (value: boolean) => {
      isShowFilter.value = value;
    };

    const onFilter = (filterValues: Filter.FilterForm) => {
      filterParams.value = { ...filterValues };
      getWells();
    };

    return {
      onFilter,
      toggleFilter,
      addNewWell,
      toggleQuickInfoModal,
      numberWithCommas,
      newDailyReport,
      editWell,
      getWells,
      archiveWell,
      getLastDailyReport,
      searchWell,
      toggleArchive,
      getReportWithLatestSample,
      toggleModal,
      wellQuickInfoModal,
      wellList,
      loading,
      isArchived,
      breadcrumbs,
      search,
      isJustEngineer,
      UserType,
      isShowFilter,
      isSystemAdmin: JwtService.checkRole(UserType.SystemAdmin),
      isModalVisible,
      selectedWellId,
    };
  },
});
</script>
