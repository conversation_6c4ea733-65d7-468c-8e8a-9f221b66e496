@import "tailwindcss";

@theme {
  --color-primary: #009ef6;
  --color-primary-active: #0095e8;
  --color-primary-light: #ecf8ff;
  --color-success: #50cd89;
  --color-success-active: #47be7d;
  --color-success-light: #e8fff3;
  --color-danger: #f1416c;
  --color-danger-active: #d9214e;
  --color-danger-light: #fff5f8;
  --color-info: #7239ea;
  --color-info-active: #4013d0;
  --color-info-light: #f8f5ff;
  --color-warning: #ffc700;
  --color-warning-active: #f1bc00;
  --color-warning-light: #fff8dd;
  --color-white-active: #f5f8fa;
  --color-dark: #181c32;

  --color-grey-100: #f5f8fa;
  --color-grey-200: #eff2f5;
  --color-grey-300: #e4e6ef;
  --color-grey-400: #b5b5c3;
  --color-grey-500: #a1a5b7;
  --color-grey-600: #7e8299;
  --color-grey-700: #5e6278;
  --color-grey-800: #3f4254;
  --color-grey-900: #181c32;

  --color-midnightnavy: #000024;
  --color-sttropaz: #2c5c89;
  --color-steelblue: #3f81b1;
  --color-agua: #51a7a0;

  --color-app-baselayer: var(--color-midnightnavy);
  --color-auth-start: var(--color-midnightnavy);
  --color-auth-via: var(--color-sttropaz);
  --color-auth-to: var(--color-steelblue);

  /* Background Colors */
  --color-card-background: var(--color-sttropaz);
  --color-minicard-background: var(--color-agua);
  --color-screen-background: var(--color-white);
  --color-header-background: var(--color-white);
  --color-sidebar-background: var(--color-midnightnavy);
  --color-sidebar-userinfo-background: var(--color-agua);
  --color-popup-background: var(--color-sttropaz);
  --color-searhbar-background: var(--color-white);
  --color-modal-background: var(--color-steelblue);
  --color-history-tab-list-marker: var(--color-midnightnavy);
  --color-input-background: var(--color-white);

  /* Text colors */
  --color-sidebar-text: var(--color-white);
  --color-sidebar-text-hover: var(--color-midnightnavy);
  --color-link: var(--color-agua);
  --color-link-hover: var(--color-sttropaz);
  --color-active: var(--color-white);
  --color-active-dark: var(--color-agua);
  --color-inactive: var(--color-midnightnavy);
  --color-inactive-dark: var(--color-midnightnavy);
  --color-active-hover: var(--color-agua);
  --color-inactive-hover: var(--color-agua);
  --color-card-text: var(--color-white);
  --color-card-text-hover: var(--color-ag);
  --color-card-text-light: var(--color-white);
  --color-card-text-dark: var(--color-midnightnavy);
  --color-minicard-text-light: var(--color-white);
  --color-minicard-text-dark: var(--color-midnightnavy);
  --color-input-text-light: var(--color-white);
  --color-input-text-dark: var(--color-midnightnavy);
  --color-header-text-light: var(--color-white);
  --color-header-text-dark: var(--color-midnightnavy);
  --color-popup-text: var(--color-white);

  /* Button Colors */
  --color-button-primary: var(--color-midnightnavy);
  --color-button-primary-hover: var(--color-white);
  --color-button-primary-active: var(--color-agua);
  --color-button-text-light: var(--color-white);
  --color-button-text-dark: var(--color-midnightnavy);
  --color-button-text-light-hover: var(--color-midnightnavy);
  --color-button-text-dark-hover: var(--color-white);
  --color-button-hover-outline: var(--color-midnightnavy);
  --color-button-alert-success: var(--color-success);
  --color-button-alert-success-hover: var(--color-success-active);
  --color-button-alert-danger: var(--color-danger);
  --color-button-alert-danger-hover: var(--color-danger-active);

  /* Icon Colors */
  --color-icon-light: var(--color-white);
  --color-icon-dark: var(--color-midnightnavy);
  --color-icon-active: var(--color-midnightnavy);
  --color-icon-active: var(--color-agua);
  --color-icon-primary: var(--color-primary);

  /* border/outline colors */
  --color-light-border: var(--color-agua);
  --color-dark-border: var(--color-midnightnavy);
  --color-active-border: var(--color-white);
  --color-active-border-dark: var(--color-agua);
  --color-inactive-border: var(--color-midnightnavy);
  --color-inactive-border-dark: var(--color-midnightnavy);
  --color-active-border-hover: var(--color-agua);
  --color-inactive-border-hover: var(--color-midnightnavy);

  /* apex chart colors */
  --color-label-color: var(--color-midnightnavy);
  --color-border-color: var(--color-midnightnavy);
  --color-base-color: var(--color-agua);
  --color-light-color: var(--color-sttropaz);
}
