<template>
  <div
    v-if="isVisible"
    class="fixed top-0 right-0 bottom-0 left-0 z-40 flex items-center"
  >
    <!-- Dark overlay background -->
    <div class="absolute inset-0 bg-sidebar-background opacity-25"></div>
    <div
      class="relative bg-card-background text-card-text-light max-h-11/12 w-11/12 flex flex-col items-center gap-3 mx-auto my-6 p-5 rounded-xl md:w-3/5 lg:w-2/5 lg:h-auto"
    >
      <div class="flex flex-row h-auto w-full items-center justify-between">
        <h3 class="text-lg font-bold">
          {{ `${id ? "Edit Cost" : "New Cost"}` }}
        </h3>
        <span class="cursor-pointer" @click="closeModal">
          <SvgIcon icon="closeModalIcon" />
        </span>
      </div>

      <el-form
        id="cost_form"
        @submit.prevent="submit"
        :model="targetData"
        :rules="rules"
        ref="formRef"
        class="h-auto w-full flex flex-col gap-3"
      >
        <div class="flex flex-col gap-1">
          <label class="font-bold"
            >Description<span class="text-danger-active font-light">*</span>
          </label>
          <el-form-item prop="costSettingId" class="mt-auto">
            <el-select
              v-model="targetData.costSettingId"
              placeholder="Select Description"
              clearable
              :loading="loadingDescriptionList"
            >
              <el-option
                v-for="item in descriptionOptions"
                :key="item?.value"
                :label="item?.label"
                :value="item?.value"
                name="costSettingId"
            /></el-select>
          </el-form-item>
        </div>

        <div class="flex flex-col gap-1">
          <label class="font-bold"
            >Unit<span class="text-danger-active font-light">*</span></label
          >
          <el-form-item prop="unit">
            <el-input
              type="number"
              :controls="false"
              step="any"
              v-model="targetData.unit"
              placeholder=""
              name="unit"
            ></el-input>
          </el-form-item>
        </div>

        <div class="flex flex-col gap-1">
          <label class="font-bold">
            Quantity<span class="text-danger-active font-light">*</span>
          </label>

          <el-form-item prop="quantity">
            <el-input
              type="number"
              :controls="false"
              step="any"
              v-model="targetData.quantity"
              placeholder=""
              name="quantity"
            ></el-input>
          </el-form-item>
        </div>

        <div class="h-auto w-full flex flex-row items-center gap-2">
          <button
            type="button"
            class="bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover rounded-md px-4 py-2 font-semibold"
            @click="closeModal"
            :disabled="loading"
          >
            Discard
          </button>
          <button
            class="bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover rounded-md px-4 py-2 font-semibold"
            type="submit"
            :disabled="loading"
          >
            <span v-if="!loading" class="indicator-label"> Save </span>
            <span v-if="loading" class="indicator-progress">
              Please wait...
              <span
                class="spinner-border spinner-border-sm align-middle ms-2"
              ></span>
            </span>
          </button>
        </div>
      </el-form>
    </div>
  </div>
</template>

<script lang="ts">
import SvgIcon from "@/constants/SvgIcon.vue";
import { useCostStore } from "@/stores/cost";
import { useCostSettingStore } from "@/stores/cost-setting";
import { useWellStore } from "@/stores/well";
import { defineComponent, inject, onMounted, ref, watch } from "vue";
import { useRoute } from "vue-router";
import type { Provide } from "@/types/injection-types";

export default defineComponent({
  name: "cost-modal",
  components: { SvgIcon },
  props: {
    isVisible: {
      type: Boolean,
      required: true,
    },
    close: {
      type: Function,
      required: true,
    },
    loadPage: {
      type: Function,
      default: () => {},
      required: true,
    },
  },
  setup(props) {
    const route = useRoute();
    const wellStore = useWellStore();
    const costSettingStore = useCostSettingStore();
    const costStore = useCostStore();
    const modal = ref(false);
    const targetData = ref({
      costSettingId: "",
      unit: null,
      quantity: null,
    });
    const formRef = ref<null | HTMLFormElement>(null);
    const loading = ref<boolean>(false);
    const loadingDescriptionList = ref<boolean>(false);
    const id = ref("");
    const descriptionOptions = ref<any>([]);
    const companyId = ref<string>("");
    const dailyReportProvide = inject<Provide.DailyReport>("dailyReport");

    watch(id, (newValue) => {
      if (newValue !== "") {
        getCostDetails();
        if (companyId.value) {
          getCostSettings();
        }
      }
    });

    watch(
      () => props.isVisible,
      (newValue) => {
        if (newValue === false) {
          id.value = "";
          reset();
          formRef?.value?.resetFields();
        }
      }
    );

    onMounted(() => {
      getWellInfo();
    });

    const getWellInfo = async (): Promise<void> => {
      wellStore.getWellDetails({
        wellId: route.params.id as string,
        callback: {
          onSuccess: (res: any) => {
            companyId.value = res?.company?.id;
            getCostSettings();
          },
          onFinish: (_err: any) => {
            loading.value = false;
          },
        },
      });
    };

    const getCostSettings = async (): Promise<void> => {
      if (!companyId.value) {
        return;
      }
      loadingDescriptionList.value = true;
      costSettingStore.getCostSettings({
        params: {
          page: 1,
          limit: 200,
          companyId: companyId.value,
        },
        callback: {
          onSuccess: (res: any) => {
            descriptionOptions.value = res?.items?.map((item: any) => {
              return {
                value: item?.id,
                label: item?.name,
              };
            });
          },
          onFinish: (_err: any) => {
            loadingDescriptionList.value = false;
          },
        },
      });
    };

    const getCostDetails = async (): Promise<void> => {
      costStore.getCostDetails({
        id: id.value,
        callback: {
          onSuccess: (res: any) => {
            targetData.value = {
              ...res,
              costSettingId: res?.costSetting?.id,
            };
          },
        },
      });
    };

    const updateCost = async (param: any): Promise<void> => {
      loading.value = true;
      costStore.updateCost({
        id: id.value,
        params: param,
        callback: {
          onSuccess: (_res: any) => {
            if (props?.loadPage) {
              props?.loadPage();
            }
            props.close();
          },
          onFinish: (_err: any) => {
            loading.value = false;
          },
        },
      });
    };

    const createCost = async (param: any): Promise<void> => {
      loading.value = true;
      costStore.createCost({
        params: param,
        callback: {
          onSuccess: (_res: any) => {
            if (props?.loadPage) {
              props?.loadPage();
            }
            props.close();
          },
          onFinish: (_err: any) => {
            loading.value = false;
          },
        },
      });
    };

    const closeModal = () => {
      props.close();
    };

    const setId = (wellId: string) => {
      id.value = wellId.toString();
    };

    const rules = ref({
      costSettingId: [
        {
          required: true,
          message: "Please select Description",
          trigger: "change",
        },
      ],
      unit: [
        {
          required: true,
          message: "Please type Unit",
          trigger: "blur",
        },
      ],
      quantity: [
        {
          required: true,
          message: "Please type Quantity",
          trigger: "blur",
        },
      ],
    });

    const submit = () => {
      if (!formRef.value) {
        return;
      }

      formRef.value.validate((valid: boolean) => {
        if (valid) {
          const param = {
            ...targetData?.value,
            unit: Number(targetData?.value?.unit),
            quantity: Number(targetData?.value?.quantity),
          };

          if (id?.value) {
            updateCost({
              ...param,
              dailyReportId: dailyReportProvide?.getDailyReportId(),
            });
          } else {
            dailyReportProvide?.createDailyReport({
              wellId: route?.params?.id as string,
              callback: {
                onSuccess: (_res: string) => {
                  createCost({
                    ...param,
                    dailyReportId: dailyReportProvide?.getDailyReportId(),
                  });
                },
              },
            });
          }
        }
      });
    };

    const reset = () => {
      targetData.value = {
        costSettingId: "",
        unit: null,
        quantity: null,
      };
    };

    return {
      id,
      modal,
      rules,
      loading,
      targetData,
      formRef,
      loadingDescriptionList,
      descriptionOptions,
      closeModal,
      submit,
      setId,
      reset,
    };
  },
});
</script>
