import{S as ne}from"./SvgIcon-DYvlNVZf.js";import{U as ve,O as me}from"./Overview-DMdf3K2c.js";import{d as se,q as b,$ as j,x as oe,A as X,a3 as H,U as B,E as T,e as ae,J as Y,j as le,_ as ie,c,o as l,a as s,b as h,m as q,w as L,r as y,p as fe,l as E,B as P,H as Z,M as ee,t as $,F as z,k as re,n as he}from"./index-BmHWvWFS.js";import{u as ce}from"./user-CVSNmFaf.js";import{A as ye}from"./AssignUserModal-kk-OATZ2.js";import{T as ke}from"./TablePagination-lslz6s2e.js";import{T as Ae}from"./TableHeader-DGdsllig.js";import{S as te,a as we}from"./table-bhK9qpe4.js";import{f as pe}from"./date-CKteeARj.js";const De=se({name:"user-info-engineer",components:{SvgIcon:ne,TablePagination:ke,AssignUserModal:ye,TableHeader:Ae},props:{userDetail:{type:Object,required:!0}},setup(e){var Q,W;const n=ce(),R=le(),x=ae(),w=b([]),F=b(!1),k=b(0),U=b(0),f=b(1),g=b(!1),p=b(!1),d=b(""),v=b([]),A=b([]),D=b(null),M=b(!1),_=b({sortDirection:we.ASC,sortBy:te.Name}),V=[{label:"",class:"w-25px",display:j()},{label:"FULL NAME",sortBy:te.Name,class:"min-w-150px"},{label:"MOBILE NUMBER",class:"min-w-120px"},{label:"OFFICE NUMBER",class:"min-w-120px"},{label:"ADDED DATE",class:"min-w-120px"},{label:"ACTIONS",class:"min-w-60px",display:j()}];oe(()=>{m(),O()}),X(f,()=>{m()});const t=()=>{M.value=!M.value},m=async()=>{var o;g.value=!0,n.getAssignedEngineers({params:{supervisorId:(o=e==null?void 0:e.userDetail)==null?void 0:o.id,name:d.value.trim()||null,page:f.value,limit:10,..._.value},callback:{onSuccess:a=>{v.value=[...a==null?void 0:a.items],k.value=a==null?void 0:a.totalPage,U.value=a==null?void 0:a.total,f.value=a==null?void 0:a.page},onFinish:()=>{g.value=!1}}})},J=o=>{_.value={...o},m()},O=async(o="")=>{var a;p.value=!0,n.getUsers({params:{role:B.Engineer,status:H.Active,companyId:(a=e.userDetail)==null?void 0:a.companyId,sortBy:"email",name:o.trim()||null,page:f.value,limit:500},callback:{onSuccess:C=>{A.value=[...C==null?void 0:C.items]},onFinish:()=>{p.value=!1}}})},G=o=>{T.deletionAlert({onConfirmed:()=>{K([o])}})},i=o=>{f.value=o};X(w,o=>{F.value=o.length===v.value.length});const r=o=>{var a;(a=o==null?void 0:o.target)!=null&&a.checked?w.value=v.value.map(C=>C.id):w.value=[]},u=o=>{x.push({path:`/users/${o}`})},I=o=>{var a;ge(o),(a=D==null?void 0:D.value)==null||a.closeModal()},S=o=>{O(o)},N=()=>{f.value!==1?f.value=1:m()},de=()=>{var o;(o=D==null?void 0:D.value)==null||o.show()},ue=()=>{var o,a;return(Y.checkRole(B.SystemAdmin)||Y.checkRole(B.CompanyAdmin))&&((a=(o=e==null?void 0:e.userDetail)==null?void 0:o.roles)==null?void 0:a.find(C=>(C==null?void 0:C.value)===B.Supervisor))&&R.name!=="my-profile"},be=()=>{T.deletionAlert({onConfirmed:()=>{K(w.value,!0)}})},K=async(o,a=!1)=>{g.value=!0,n.removeEngineers({engineerIds:o,callback:{onSuccess:C=>{a&&(w.value=[]),m()},onFinish:()=>{g.value=!1}}})},ge=async o=>{var a;g.value=!0,n.assignEngineers({params:{engineerIds:o,supervisorId:(a=e.userDetail)==null?void 0:a.id},callback:{onSuccess:C=>{m(),T.toast("Added successfully","success","top-right")},onFinish:()=>{g.value=!1}}})};return{sortParams:_,tableHeader:V,userListForAssignUserModal:A,search:d,loading:g,assignedEngineerList:v,checkedRows:w,checkAll:F,currentPage:f,totalElements:U,pageCount:k,assignEngineersModal:D,loadingUserListForAssignUserModal:p,initialAddUserValue:{userRoles:[B.Engineer],companyId:(W=(Q=e==null?void 0:e.userDetail)==null?void 0:Q.company)==null?void 0:W.id},isModalVisible:M,showAddBtn:ue,pageChange:i,deleteEngineer:G,onSearch:S,onAdd:I,view:u,onClickAdd:de,isAdmin:j,onToggleCheckAll:r,searchTable:N,onRemove:be,formatDate:pe,onSort:J,toggleModal:t}}}),Se={class:"bg-card-background text-card-text h-auto w-11/12 mx-auto my-4 rounded-xl py-4 px-4"},Ce={class:"h-auto w-full flex flex-col align-items-center"},Ue={class:"h-auto w-full flex flex-row justify-end gap-2"},Ie={class:"bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover rounded-md px-4 py-2 font-semibold flex flex-row items-center gap-2 justify-between"},Ee={key:0,class:"text-center p-5"},_e={key:2},$e={class:"table-responsive mt-3 mx-8"},Be={class:"table table-row-dashed table-row-gray-300 align-middle gs-0 gy-2"},Te={class:"fw-bold text-gray-400"},xe={key:0},Me={class:"form-check form-check-sm form-check-custom form-check-solid"},Le={key:1},Pe={key:0},Re={class:"form-check form-check-sm form-check-custom form-check-solid"},Fe=["value"],Ve={class:"d-flex align-items-center"},Ne={class:"symbol symbol-45px symbol-circle me-5"},Oe=["src"],je={class:"d-flex justify-content-start flex-column"},He={class:"text-gray-400 fw-semibold d-block fs-5"},Ye={class:"text-gray-600 fw-semibold d-block fs-5"},qe={class:"text-gray-600 fw-semibold d-block fs-5"},Je={class:"text-gray-600 fw-semibold d-block fs-5"},ze={key:1},Ge={class:"d-flex align-items-center"},Ke=["onClick"],Qe={class:"svg-icon svg-icon-3"},We=["onClick"],Xe={class:"svg-icon svg-icon-3 text-danger"},Ze={class:"d-flex flex-wrap align-items-center mb-5 mx-8"},et={key:0,class:"text-gray-700 fw-semibold fs-6 me-auto"};function tt(e,n,R,x,w,F){var _,V;const k=y("SvgIcon"),U=y("el-icon"),f=y("el-input"),g=y("el-form-item"),p=y("el-form"),d=y("el-empty"),v=y("TableHeader"),A=y("router-link"),D=y("TablePagination"),M=y("AssignUserModal");return l(),c(z,null,[s("div",Se,[n[8]||(n[8]=s("h1",{class:"font-bold text-lg"},"Assigned Engineers",-1)),h(p,{onSubmit:fe(e.searchTable,["prevent"])},{default:L(()=>[s("div",Ce,[h(g,{class:"mb-0"},{default:L(()=>[h(f,{class:"w-250px",placeholder:"Search",modelValue:e.search,"onUpdate:modelValue":n[0]||(n[0]=t=>e.search=t),name:"search",size:"large"},{prefix:L(()=>[h(U,{class:"el-input__icon"},{default:L(()=>[h(k,{icon:"searchIcon"})]),_:1})]),_:1},8,["modelValue"])]),_:1})])]),_:1},8,["onSubmit"]),s("div",Ue,[e.checkedRows.length!==0?(l(),c("button",{key:0,class:"bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover rounded-md px-4 py-2 font-semibold flex flex-row items-center gap-2 justify-between",type:"button",onClick:n[1]||(n[1]=(...t)=>e.onRemove&&e.onRemove(...t))}," Remove ")):E("",!0),e.showAddBtn()?(l(),c("button",{key:1,class:"bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover rounded-md px-4 py-2 font-semibold flex flex-row items-center gap-2 justify-between",type:"button",onClick:n[2]||(n[2]=(...t)=>e.onClickAdd&&e.onClickAdd(...t))}," Add ")):E("",!0),s("button",Ie,[h(k,{icon:"logoutIcon"}),n[6]||(n[6]=P(" Export "))])]),e.loading?(l(),c("div",Ee,n[7]||(n[7]=[s("div",{class:"spinner-border text-primary",role:"status"},[s("span",{class:"sr-only"},"Loading...")],-1)]))):e.assignedEngineerList.length===0?(l(),q(d,{key:1,description:"No Data"})):(l(),c("div",_e,[s("div",$e,[s("table",Be,[s("thead",null,[s("tr",Te,[h(v,{headers:e.tableHeader,sortBy:e.sortParams.sortBy,sortDirection:e.sortParams.sortDirection,onSort:e.onSort},{customHeader:L(({header:t})=>[t.label===""?(l(),c("div",xe,[s("div",Me,[Z(s("input",{class:"form-check-input",type:"checkbox","onUpdate:modelValue":n[3]||(n[3]=m=>e.checkAll=m),onChange:n[4]||(n[4]=(...m)=>e.onToggleCheckAll&&e.onToggleCheckAll(...m))},null,544),[[ee,e.checkAll]])])])):(l(),c("div",Le,$(t.label),1))]),_:1},8,["headers","sortBy","sortDirection","onSort"])])]),s("tbody",null,[(l(!0),c(z,null,re(e.assignedEngineerList,t=>(l(),c("tr",{key:t.id},[e.isAdmin()?(l(),c("td",Pe,[s("div",Re,[Z(s("input",{class:"form-check-input widget-9-check",type:"checkbox",value:t.id,"onUpdate:modelValue":n[5]||(n[5]=m=>e.checkedRows=m)},null,8,Fe),[[ee,e.checkedRows]])])])):E("",!0),s("td",null,[s("div",Ve,[s("div",Ne,[s("img",{src:(t==null?void 0:t.avatar)||"/media/avatars/blank.png",alt:""},null,8,Oe)]),s("div",je,[h(A,{to:`/users/${t==null?void 0:t.id}`,class:"text-dark fw-bold text-hover-primary fs-6"},{default:L(()=>[P($(`${t==null?void 0:t.firstName} ${t==null?void 0:t.lastName}`),1)]),_:2},1032,["to"]),s("span",He,$(t==null?void 0:t.email),1)])])]),s("td",null,[s("span",Ye,$(t==null?void 0:t.mobilePhone),1)]),s("td",null,[s("span",qe,$(t==null?void 0:t.officePhone),1)]),s("td",null,[s("span",Je,$(e.formatDate(t==null?void 0:t.assignedDate,"MMM DD, YYYY")),1)]),e.isAdmin()?(l(),c("td",ze,[s("div",Ge,[s("button",{class:"btn btn-icon btn-sm btn-blue me-3",onClick:()=>e.view((t==null?void 0:t.id)??"")},[s("span",Qe,[h(k,{icon:"eyeIcon"})])],8,Ke),s("button",{class:"btn btn-icon btn-sm btn-blue me-3",onClick:m=>e.deleteEngineer(t==null?void 0:t.id)},[s("span",Xe,[h(k,{icon:"trashIcon"})])],8,We)])])):E("",!0)]))),128))])])]),s("div",Ze,[(_=e.assignedEngineerList)!=null&&_.length?(l(),c("div",et,$(`Showing ${(e.currentPage-1)*10+1} to ${(V=e.assignedEngineerList)==null?void 0:V.length} of ${e.totalElements} entries`),1)):E("",!0),e.pageCount>=1?(l(),q(D,{key:1,"total-pages":e.pageCount,total:e.totalElements,"per-page":10,"current-page":e.currentPage,onPageChange:e.pageChange},null,8,["total-pages","total","current-page","onPageChange"])):E("",!0)])]))]),h(M,{ref:"assignEngineersModal",title:"Assign Engineers",userList:e.userListForAssignUserModal,onAdd:e.onAdd,onSearch:e.onSearch,loadingSearch:e.loadingUserListForAssignUserModal,initialAddUserValue:e.initialAddUserValue,isVisible:e.isModalVisible,close:e.toggleModal},null,8,["userList","onAdd","onSearch","loadingSearch","initialAddUserValue","isVisible","close"])],64)}const nt=ie(De,[["render",tt]]),st=se({name:"user-detail",components:{SvgIcon:ne,EngineerList:nt,Overview:me,UserInfo:ve},props:{userDetail:{type:Object,required:!0},reloadUserData:{type:Function,required:!0}},setup(e){const R=(()=>{var i,r;return(r=(i=e==null?void 0:e.userDetail)==null?void 0:i.roles)!=null&&r.find(u=>(u==null?void 0:u.value)===B.Supervisor)&&!Y.isJustEngineer()?[{value:"overview",key:"Overview"},{value:"engineers",key:"Engineers"}]:[{value:"overview",key:"Overview"}]})(),x=ce(),w=b(1),F=b(5),k=b(50),U=le(),f=ae(),g=b(!1),p=b(!1),d=b(R[0].value),v=b(!1);oe(()=>{j()&&!t()&&A()});const A=async()=>{x.getInvitedUser({callback:{onSuccess:i=>{Array.isArray(i)&&(i!=null&&i.some(r=>{var u;return r.id===((u=e==null?void 0:e.userDetail)==null?void 0:u.id)}))&&(p.value=!0)}}})},D=i=>{w.value=i},M=i=>{i===H.Active?T.alert("Are you sure you want to activate this user account?",{confirmButtonText:"Yes, Activate it!",cancelButtonText:"No, cancel!",confirmButtonClass:"btn fw-bold btn-primary btn-sm",cancelButtonClass:"btn fw-bold btn-blue btn-sm",callback:{onConfirmed:()=>{var r;O((r=e==null?void 0:e.userDetail)==null?void 0:r.id,i)}}},"warning"):T.alert("Are you sure you want to deactivate this user account?",{confirmButtonText:"Yes, Deactivate it!",cancelButtonText:"No, cancel!",confirmButtonClass:"btn fw-bold btn-light-danger btn-sm",cancelButtonClass:"btn fw-bold btn-blue btn-sm",callback:{onConfirmed:()=>{var r;O((r=e==null?void 0:e.userDetail)==null?void 0:r.id,i)}}},"warning")},_=()=>{var i,r,u,I;return!!(((r=(i=e==null?void 0:e.userDetail)==null?void 0:i.roles)==null?void 0:r.length)===1&&((I=(u=e==null?void 0:e.userDetail)==null?void 0:u.roles)!=null&&I.find(S=>(S==null?void 0:S.value)===B.Engineer)))},V=async()=>{var i,r,u,I,S;(i=e==null?void 0:e.userDetail)!=null&&i.email&&(v.value=!0,x.inviteUsers({params:{emails:[e.userDetail.email],userRoles:((u=(r=e.userDetail)==null?void 0:r.roles)==null?void 0:u.map(N=>N.value))||[],companyId:((S=(I=e.userDetail)==null?void 0:I.company)==null?void 0:S.id)||""},callback:{onSuccess:N=>{T.toast("Invited successfully","success","top-right")},onFinish:()=>{v.value=!1}}}))},t=()=>U.name==="my-profile",m=()=>{var i,r,u,I,S;return((r=(i=U.params)==null?void 0:i.id)==null?void 0:r.toString())===((u=Y.getUserInfo())==null?void 0:u.id)&&!!((S=(I=e==null?void 0:e.userDetail)==null?void 0:I.roles)!=null&&S.find(N=>N.value===B.SystemAdmin))},J=i=>{const r=i.target;d.value=r.getAttribute("data-tab-index")},O=(i,r)=>{g.value=!0,x.updateUserProfile({id:i,params:{status:r},callback:{onSuccess:()=>{var u;(u=e==null?void 0:e.reloadUserData)==null||u.call(e),r===H.Active?T.resultAlert("Account is activated!","success"):T.resultAlert("Account is deactivated!.","success")},onFinish:()=>{g.value=!1}}})};return{sendingInvitation:v,resendInvitation:p,submitting:g,UserStatus:H,currentPage:w,totalElements:k,pageCount:F,route:U,tabIndex:d,tabs:R,back:()=>{f.go(-1)},isOwner:t,pageChange:D,isAdmin:j,setActiveTab:J,promptChangeAccountStatus:M,userProfileIsEngineer:_,isSystemAdmin:m,sendInvitation:V}}}),ot={class:"bg-screen-background h-auto w-11/12 mx-auto mt-4 rounded-xl"},at={class:"bg-amber-300 d-flex my-4 gap-3"},lt={key:0},it={key:1,class:"indicator-progress"},rt={key:0},ct={class:"svg-icon svg-icon-1 me-1"},dt={key:1,class:"indicator-progress"},ut={key:0},bt={class:"svg-icon svg-icon-1"},gt={key:1,class:"indicator-progress"},vt={class:"bg-card-background text-card-text h-auto w-full mt-4 pt-4 px-4 rounded-t-lg flex flex-row items-center justify-start gap-3 border-transparent font-bold lg:w-4/5 lg:mx-auto lg:min-w-[1560px]"},mt={class:"cursor-pointer font-semibold hover:text-primary hover:border-b-2 hover:border-primary"},ft=["data-tab-index"];function ht(e,n,R,x,w,F){var p;const k=y("inline-svg"),U=y("UserInfo"),f=y("Overview"),g=y("EngineerList");return l(),c("div",ot,[h(U,{userDetail:e.userDetail},{default:L(()=>{var d,v;return[s("div",at,[e.resendInvitation?(l(),c("button",{key:0,class:"btn btn-flex btn-sm btn-success",onClick:n[0]||(n[0]=(...A)=>e.sendInvitation&&e.sendInvitation(...A))},[e.sendingInvitation?(l(),c("span",it,n[5]||(n[5]=[P(" Please wait... "),s("span",{class:"spinner-border spinner-border-sm align-middle ms-2"},null,-1)]))):(l(),c("span",lt," Resend Invitation "))])):E("",!0),((d=e.userDetail)==null?void 0:d.status)===e.UserStatus.Inactive?(l(),c("button",{key:1,class:"btn btn-sm btn-primary",type:"button",onClick:n[1]||(n[1]=A=>e.promptChangeAccountStatus(e.UserStatus.Active))},[e.submitting?(l(),c("span",dt,n[7]||(n[7]=[P(" Please wait... "),s("span",{class:"spinner-border spinner-border-sm align-middle ms-2"},null,-1)]))):(l(),c("span",rt,[s("span",ct,[h(k,{src:"media/icons/duotune/general/unlock.svg"})]),n[6]||(n[6]=P(" Active Account"))]))])):((v=e.userDetail)==null?void 0:v.status)===e.UserStatus.Active?(l(),c("button",{key:2,class:"btn btn-sm btn-danger",type:"button",onClick:n[2]||(n[2]=A=>e.promptChangeAccountStatus(e.UserStatus.Inactive))},[e.submitting?(l(),c("span",gt,n[9]||(n[9]=[P(" Please wait... "),s("span",{class:"spinner-border spinner-border-sm align-middle ms-2"},null,-1)]))):(l(),c("span",ut,[s("span",bt,[h(k,{src:"media/icons/duotune/general/lock.svg"})]),n[8]||(n[8]=P(" Deactive Account "))]))])):E("",!0),e.isOwner()?E("",!0):(l(),c("button",{key:3,type:"button",class:"btn text-gray-700 btn-sm btn-blue",onClick:n[3]||(n[3]=(...A)=>e.back&&e.back(...A))}," Back "))])]}),_:1},8,["userDetail"]),s("ul",vt,[(l(!0),c(z,null,re(e.tabs,d=>(l(),c("li",mt,[s("div",{class:he(["nav-link cursor-pointer hover:text-active-hover hover:border-b-2 hover:border-active-border-hover",{active:e.tabIndex===d.value,"text-active border-b-2 border-active-border":e.tabIndex===(d==null?void 0:d.value),"text-inactive border-b-2 border-inactive-border":e.tabIndex!==(d==null?void 0:d.value)}]),onClick:n[4]||(n[4]=v=>e.setActiveTab(v)),"data-tab-index":d.value,role:"tab"},$(d.key),11,ft)]))),256))]),e.tabIndex===e.tabs[0].value?(l(),q(f,{key:0,hideRole:e.isSystemAdmin(),userDetail:e.userDetail,reloadUserData:e.reloadUserData},null,8,["hideRole","userDetail","reloadUserData"])):e.tabIndex===e.tabs[1].value&&((p=e.userDetail)!=null&&p.id)?(l(),q(g,{key:1,userDetail:e.userDetail},null,8,["userDetail"])):E("",!0)])}const It=ie(st,[["render",ht]]);export{It as U};
