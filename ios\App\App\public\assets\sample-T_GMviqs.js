import{S as R}from"./SvgIcon-CfrWCA-H.js";import{d as x,_ as C,c as S,o as m,s as $,a as h,l as D,b as H,r as N,B as b,O as F,P as t,Q as r}from"./index-DalLS0_6.js";import{h as p}from"./handleFailure-DrOe_u9W.js";const I=x({name:"bottom-tool",components:{SvgIcon:R},props:{addNew:{type:Function,required:!1},showHelpWindow:{type:Function,required:!1},showAddNew:{type:Boolean,required:!1,default:!0},showHelpInfo:{type:Boolean,required:!1,default:!0}},setup(l){return{addNewModal:()=>{l.addNew&&l.addNew()},showHelp:()=>{l.showHelpWindow&&l.showHelpWindow()}}}}),B={className:"h-auto w-auto fixed bottom-4 right-5 cursor-pointer"},W={key:0},P={key:1,class:"flex flex-row gap-4 items-center"},T={class:"relative group inline-block pt-1"},M={key:0,class:"relative group inline-block"};function _(l,u,y,f,w,v){const c=N("SvgIcon");return m(),S("div",B,[l.$slots.header?(m(),S("div",W,[$(l.$slots,"header")])):(m(),S("div",P,[h("div",T,[h("button",{class:"bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover hover:border-[2px] hover:border-button-hover-outline rounded-full p-4",onClick:u[0]||(u[0]=(...n)=>l.addNewModal&&l.addNewModal(...n))},[H(c,{icon:"addIcon"})]),u[2]||(u[2]=h("div",{class:"h-auto w-auto absolute left-1/2 transform -translate-x-1/2 bottom-full mb-2 py-1 px-2 hidden group-hover:block bg-light-border text-popup-text rounded whitespace-nowrap"},[b(" Add New "),h("div",{class:"absolute left-1/2 transform -translate-x-1/2 top-full w-0 h-0 border-l-4 border-r-4 border-t-4 border-t-light-border border-l-transparent border-r-transparent"})],-1))]),l.showHelpInfo?(m(),S("div",M,[h("button",{class:"bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover hover:border-[2px] hover:border-button-hover-outline rounded-full p-4",onClick:u[1]||(u[1]=(...n)=>l.showHelp&&l.showHelp(...n))},[H(c,{icon:"helpIcon"})]),u[3]||(u[3]=h("div",{class:"h-auto w-auto absolute left-1/2 transform -translate-x-1/2 bottom-full mb-2 py-1 px-2 hidden group-hover:block bg-light-border text-popup-text rounded whitespace-nowrap"},[b(" More Info "),h("div",{class:"absolute left-1/2 transform -translate-x-1/2 top-full w-0 h-0 border-l-4 border-r-4 border-t-4 border-t-light-border border-l-transparent border-r-transparent"})],-1))])):D("",!0)]))])}const V=C(I,[["render",_]]),L=F("dailyReport",()=>({getDailyReports:async({params:a,callback:o})=>{var i;const s=t.get(o,"onSuccess",t.noop),e=t.get(o,"onFinish",t.noop);try{r.setHeader();const d=await r.getWithParams("dailyReports",a);s(((i=d.data)==null?void 0:i.data)||d.data)}catch(d){p(d,o)}finally{e()}},getDailyReportById:async({id:a,callback:o})=>{var i;const s=t.get(o,"onSuccess",t.noop),e=t.get(o,"onFinish",t.noop);try{r.setHeader();const d=await r.get(`dailyReports/${a}`);s(((i=d.data)==null?void 0:i.data)||d.data)}catch(d){p(d,o)}finally{e()}},getTodayReport:async({id:a,callback:o})=>{var i;const s=t.get(o,"onSuccess",t.noop),e=t.get(o,"onFinish",t.noop);try{r.setHeader();const d=await r.get(`dailyReports/todayReport/${a}`);s(((i=d.data)==null?void 0:i.data)||d.data)}catch(d){p(d,o)}finally{e()}},updateDailyReport:async({id:a,params:o,callback:s})=>{var d;const e=t.get(s,"onSuccess",t.noop),i=t.get(s,"onFinish",t.noop);try{r.setHeader();const g=await r.put(`dailyReports/${a}`,o);e(((d=g.data)==null?void 0:d.data)||g.data)}catch(g){p(g,s)}finally{i()}},deleteDailyReport:async({id:a,callback:o})=>{var i;const s=t.get(o,"onSuccess",t.noop),e=t.get(o,"onFinish",t.noop);try{r.setHeader();const d=await r.delete(`dailyReports/${a}`);s(((i=d.data)==null?void 0:i.data)||d.data)}catch(d){p(d,o)}finally{e()}},createDailyReport:async({wellId:a,callback:o})=>{var i;const s=t.get(o,"onSuccess",t.noop),e=t.get(o,"onFinish",t.noop);try{r.setHeader();const d=await r.post("dailyReports",{wellId:a});s(((i=d.data)==null?void 0:i.data)||d.data)}catch(d){p(d,o)}finally{e()}},getLatestDailyReport:async({wellId:a,callback:o})=>{var i;const s=t.get(o,"onSuccess",t.noop),e=t.get(o,"onFinish",t.noop);try{r.setHeader();const d=await r.get(`dailyReports/latest/${a}`);s(((i=d.data)==null?void 0:i.data)||d.data)}catch(d){p(d,o)}finally{e()}},getWellInformationTab:async({wellId:a,callback:o})=>{const s=t.get(o,"onSuccess",t.noop),e=t.get(o,"onFinish",t.noop);try{r.setHeader();const i=await Promise.all([r.get(`wells/${a}`),r.get(`dailyReports/latest/${a}`)]);s(i)}catch(i){p(i,o)}finally{e()}}})),E=F("cost",()=>({getCosts:async({params:c,callback:n})=>{var s;const a=t.get(n,"onSuccess",t.noop),o=t.get(n,"onFinish",t.noop);try{r.setHeader();const e=await r.getWithParams("costs",c);a(((s=e.data)==null?void 0:s.data)||e.data)}catch(e){p(e,n)}finally{o()}},getCostDetails:async({id:c,callback:n})=>{var s;const a=t.get(n,"onSuccess",t.noop),o=t.get(n,"onFinish",t.noop);try{r.setHeader();const e=await r.get(`costs/${c}`);a(((s=e.data)==null?void 0:s.data)||e.data)}catch(e){p(e,n)}finally{o()}},getCostSummary:async({params:c,callback:n})=>{var s;const a=t.get(n,"onSuccess",t.noop),o=t.get(n,"onFinish",t.noop);try{r.setHeader();const e=await r.getWithParams("costs/detail/costSummary",c);a(((s=e.data)==null?void 0:s.data)||e.data)}catch(e){p(e,n)}finally{o()}},updateCost:async({id:c,params:n,callback:a})=>{var e;const o=t.get(a,"onSuccess",t.noop),s=t.get(a,"onFinish",t.noop);try{r.setHeader();const i=await r.put(`costs/${c}`,n);o(((e=i.data)==null?void 0:e.data)||i.data)}catch(i){p(i,a)}finally{s()}},deleteCost:async({id:c,callback:n})=>{var s;const a=t.get(n,"onSuccess",t.noop),o=t.get(n,"onFinish",t.noop);try{r.setHeader();const e=await r.delete(`costs/${c}`);a(((s=e.data)==null?void 0:s.data)||e.data)}catch(e){p(e,n)}finally{o()}},createCost:async({params:c,callback:n})=>{var s;const a=t.get(n,"onSuccess",t.noop),o=t.get(n,"onFinish",t.noop);try{r.setHeader();const e=await r.post("costs",c);a(((s=e.data)==null?void 0:s.data)||e.data)}catch(e){p(e,n)}finally{o()}}})),Q=F("sample",()=>({getSamples:async({params:c,callback:n})=>{var s;const a=t.get(n,"onSuccess",t.noop),o=t.get(n,"onFinish",t.noop);try{r.setHeader();const e=await r.getWithParams("samples",c);a(((s=e.data)==null?void 0:s.data)||e.data)}catch(e){p(e,n)}finally{o()}},getSampleDetails:async({id:c,callback:n})=>{var s;const a=t.get(n,"onSuccess",t.noop),o=t.get(n,"onFinish",t.noop);try{r.setHeader();const e=await r.get(`samples/${c}`);a(((s=e.data)==null?void 0:s.data)||e.data)}catch(e){p(e,n)}finally{o()}},updateSample:async({id:c,params:n,callback:a})=>{var e;const o=t.get(a,"onSuccess",t.noop),s=t.get(a,"onFinish",t.noop);try{r.setHeader();const i=await r.put(`samples/${c}`,n);o(((e=i.data)==null?void 0:e.data)||i.data)}catch(i){p(i,a)}finally{s()}},deleteSample:async({id:c,callback:n})=>{var s;const a=t.get(n,"onSuccess",t.noop),o=t.get(n,"onFinish",t.noop);try{r.setHeader();const e=await r.delete(`samples/${c}`);a(((s=e.data)==null?void 0:s.data)||e.data)}catch(e){p(e,n)}finally{o()}},createSample:async({params:c,callback:n})=>{var s;const a=t.get(n,"onSuccess",t.noop),o=t.get(n,"onFinish",t.noop);try{r.setHeader();const e=await r.post("samples",c);a(((s=e.data)==null?void 0:s.data)||e.data)}catch(e){p(e,n)}finally{o()}},getSampleChartInfo:async({id:c,callback:n})=>{var s;const a=t.get(n,"onSuccess",t.noop),o=t.get(n,"onFinish",t.noop);try{r.setHeader();const e=await r.get(`samples/chart/${c}`);a(((s=e.data)==null?void 0:s.data)||e.data)}catch(e){p(e,n)}finally{o()}}})),j=[{value:1,label:"Water"},{value:2,label:"Oil"},{value:3,label:"Synthetic"}],z=[{value:"1",label:"Active"},{value:"2",label:"Suction Pit"},{value:"3",label:"Reserve Pit"},{value:"4",label:"Shakers"},{value:"5",label:"Mud Tanks"},{value:"6",label:"Pump Suction"},{value:"7",label:"Other Locations"}];export{V as B,E as a,Q as b,j as f,z as s,L as u};
