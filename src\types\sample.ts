import type { FluidType, SampleFrom } from "../constants/sample";
export type SampleInformation = {
  id?: string;
  dailyReportId?: string;
  fluidType?: FluidType | null;
  weightedMud?: boolean;
  sampleFrom?: SampleFrom | "";
  timeSampleTaken?: string;
  flowlineTemperature?: number | null;
  measuredDepth?: number | null;
  mudWeight?: number | null;
  funnelViscosity?: number | null;
  temperatureForPlasticViscosity?: number | null;
  plasticViscosity?: number | null;
  yieldPoint?: number | null;
  gelStrength10s?: number | null;
  gelStrength10m?: number | null;
  gelStrength30m?: number | null;
  apiFiltrate?: number | null;
  apiCakeThickness?: number | null;
  temperatureForHTHP?: number | null;
  hthpFiltrate?: number | null;
  hthpCakeThickness?: number | null;
  solids?: number | null;
  oil?: number | null;
  water?: number | null;
  sandContent?: number | null;
  mbtCapacity?: number | null;
  pH?: number | null;
  mudAlkalinity?: number | null;
  filtrateAlkalinity?: number | null;
  calcium?: number | null;
  chlorides?: number | null;
  totalHardness?: number | null;
  excessLime?: number | null;
  kPlus?: number | null;
  makeUpWater?: number | null;
  solidsAdjustedForSalt?: number | null;
  fineLCM?: number | null;
  coarseLCM?: number | null;
  linearGelStrengthPercent?: number | null;
  linearGelStrengthLbBbl?: number | null;
  highGelStrengthPercent?: number | null;
  highGelStrengthLbBbl?: number | null;
  bentoniteConcentrationPercent?: number | null;
  bentoniteConcentrationLbBbl?: number | null;
  drillSolidsConcentrationPercent?: number | null;
  drillSolidsConcentrationLbBbl?: number | null;
  drillSolidsToBentoniteRatio?: number | null;
  averageSpecificGravityOfSolids?: number | null;
  shearRate600?: number | null;
  shearRate300?: number | null;
  shearRate200?: number | null;
  shearRate100?: number | null;
  shearRate6?: number | null;
  shearRate3?: number | null;
  apparentViscosity?: number | null;
  shearRate?: number | null;
  shearStress?: number | null;
  [element: string]: any;
};
