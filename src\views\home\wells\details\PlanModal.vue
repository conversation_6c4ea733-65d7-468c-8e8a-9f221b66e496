<template>
  <div
    v-if="isVisible"
    class="fixed top-0 right-0 bottom-0 left-0 z-40 flex items-center"
  >
    <!-- Dark overlay background -->
    <div class="absolute inset-0 bg-sidebar-background opacity-25"></div>
    <div
      class="relative bg-card-background text-card-text max-h-11/12 w-11/12 flex flex-col items-center gap-3 mx-auto p-5 rounded-xl md:w-4/5 md:text-lg lg:w-2/5 lg:h-auto"
    >
      <div class="h-auto w-full flex flex-row items-center justify-between">
        <h3 class="text-lg font-bold md:text-xl">
          {{ `${id ? "Edit Plan" : "New Plan"}` }}
        </h3>
        <span class="cursor-pointer" @click="closeModal">
          <SvgIcon icon="closeModalIcon" />
        </span>
      </div>
      <el-form
        id="plan_form"
        @submit.prevent="submit"
        :model="targetData"
        :rules="rules"
        ref="formRef"
        class="h-auto w-full"
      >
        <div class="h-auto w-full flex flex-col gap-2">
          <label class="h-auto w-full flex items-center font-semibold"
            >Mud Depth (ft)
            <span class="text-danger-active font-light">*</span>
            <el-popover placement="right" :width="30" trigger="hover">
              <template #reference>
                <i class="fas fa-exclamation-circle ms-2"></i>
              </template>
              <span> ... </span>
            </el-popover></label
          >
          <el-form-item prop="mudDepth">
            <el-input
              type="number"
              :controls="false"
              step="any"
              v-model="targetData.mudDepth"
              placeholder=""
              name="mudDepth"
            ></el-input>
          </el-form-item>
        </div>
        <div class="h-auto w-full flex flex-col gap-2">
          <label class="flex items-center font-semibold"
            >Day <span class="text-danger-active font-light">*</span>
            <el-popover placement="right" :width="30" trigger="hover">
              <template #reference>
                <i class="fas fa-exclamation-circle ms-2"></i>
              </template>
              <span> ... </span>
            </el-popover></label
          >
          <el-form-item prop="day">
            <el-input
              type="number"
              :controls="false"
              :min="0"
              :step="1"
              step-strictly
              v-model="targetData.day"
              placeholder=""
              name="day"
            ></el-input>
          </el-form-item>
        </div>
        <div class="flex flex-col gap-2">
          <label class="flex items-center font-semibold"
            >Cost <span class="text-danger-active font-light">*</span>
            <el-popover placement="right" :width="30" trigger="hover">
              <template #reference>
                <i class="fas fa-exclamation-circle ms-2"></i>
              </template>
              <span> ... </span>
            </el-popover></label
          >
          <el-form-item prop="cost" class="mt-auto">
            <el-input
              type="number"
              :controls="false"
              step="any"
              v-model="targetData.cost"
              placeholder=""
              name="cost"
            ></el-input>
          </el-form-item>
        </div>
        <div class="flex flex-row items-start mt-4 gap-3">
          <button
            type="button"
            class="bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover rounded-md px-4 py-2 font-semibold md:px-4 md:py-3 md:text-xl"
            @click="closeModal"
            :disabled="loading"
          >
            Discard
          </button>
          <button
            class="bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover rounded-md px-4 py-2 font-semibold md:px-4 md:py-3 md:text-xl"
            type="submit"
            :disabled="loading"
          >
            <span v-if="!loading" class="indicator-label"> Save </span>
            <span v-if="loading" class="indicator-progress">
              Please wait...
              <span
                class="spinner-border spinner-border-sm align-middle ms-2"
              ></span>
            </span>
          </button>
        </div>
      </el-form>
    </div>
  </div>
</template>

<script lang="ts">
import SvgIcon from "@/constants/SvgIcon.vue";
import { usePlanStore } from "@/stores/plan";
import { defineComponent, ref, watch } from "vue";

export default defineComponent({
  name: "plan-modal",
  components: { SvgIcon },
  props: {
    isVisible: {
      type: Boolean,
      required: true,
    },
    close: {
      type: Function,
      required: true,
    },
    loadPage: {
      type: Function,
      default: () => {},
      required: true,
    },
    wellId: {
      type: String,
      required: true,
    },
  },
  setup(props) {
    const planStore = usePlanStore();
    const modal = ref(false);
    const targetData = ref<Plan.Info>({
      mudDepth: null,
      day: null,
      cost: null,
    });
    const formRef = ref<null | HTMLFormElement>(null);
    const loading = ref<boolean>(false);
    const id = ref("");

    watch(id, (newValue) => {
      if (newValue !== "") {
        getPlanDetails();
      }
    });

    watch(modal, (newValue) => {
      if (newValue === false) {
        id.value = "";
        reset();
        formRef?.value?.resetFields();
      }
    });

    const getPlanDetails = async (): Promise<void> => {
      planStore.getPlanDetails({
        id: id.value,
        callback: {
          onSuccess: (res: any) => {
            targetData.value = res;
          },
        },
      });
    };

    const updatePlan = async (param: Plan.Info): Promise<void> => {
      loading.value = true;
      planStore.updatePlan({
        id: id.value,
        params: param,
        callback: {
          onSuccess: (_res: any) => {
            if (props?.loadPage) {
              props?.loadPage();
            }
            props.close();
          },
          onFinish: (_err: any) => {
            loading.value = false;
          },
        },
      });
    };

    const createPlan = async (param: Plan.Info): Promise<void> => {
      loading.value = true;
      planStore.createPlan({
        params: param,
        callback: {
          onSuccess: (_res: any) => {
            if (props?.loadPage) {
              props?.loadPage();
            }
            props.close();
          },
          onFinish: (_err: any) => {
            loading.value = false;
          },
        },
      });
    };

    const closeModal = () => {
      props.close();
    };

    const setId = (wellId: string) => {
      id.value = wellId.toString();
    };

    const rules = ref({
      mudDepth: [
        {
          required: true,
          message: "Please type mud depth",
          trigger: "blur",
        },
      ],
      day: [
        {
          required: true,
          message: "Please type day",
          trigger: "blur",
        },
      ],
      cost: [
        {
          required: true,
          message: "Please type cost",
          trigger: "blur",
        },
      ],
    });

    const submit = () => {
      if (!formRef.value) {
        return;
      }

      formRef.value.validate((valid: boolean) => {
        if (valid) {
          loading.value = true;

          const param: Plan.Info = {
            mudDepth: Number(targetData.value.mudDepth),
            day: Number(targetData.value.day),
            cost: Number(targetData.value.cost),
          };

          if (id?.value) {
            updatePlan(param);
          } else {
            createPlan({ ...param, wellId: props?.wellId });
          }
        }
      });
    };

    const reset = () => {
      targetData.value = {
        mudDepth: null,
        day: null,
        cost: null,
      };
    };

    return {
      id,
      modal,
      rules,
      loading,
      targetData,
      formRef,
      submit,
      setId,
      reset,
      closeModal,
    };
  },
});
</script>
