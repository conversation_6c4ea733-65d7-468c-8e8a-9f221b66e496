import{P as C}from"./PageHeader-l8HvxxsN.js";import{d as f,q as l,x as D,J as g,_ as b,c,l as y,b as v,m as _,a as d,F as k,r as u,o as p}from"./index-DalLS0_6.js";import{u as B}from"./company-DGE9srvS.js";import{C as M}from"./CompanyDetail-Cn12BIxD.js";import"./handleFailure-DrOe_u9W.js";import"./CompanyModal-BoBgr7aV.js";import"./SvgIcon-CfrWCA-H.js";import"./TablePagination-BmkwndgK.js";import"./TableHeader-DGMH-x_O.js";import"./table-bhK9qpe4.js";import"./date-CCTVzEJd.js";import"./customer-CD9RajQq.js";import"./CustomerModal-BJI2MaCc.js";import"./user-UjS69U41.js";import"./index.esm-C3uaQ3c9.js";import"./AssignUserModal-BA3iiGKX.js";import"./UserModal-DfH6xbe7.js";import"./validator-BJ5Qi8qK.js";const P=f({name:"my-company",components:{CompanyDetail:M,PageHeader:C},setup(){const e=l(!1),o=B(),t=l();D(()=>{n()});const n=async()=>{var m,a;const r=(a=(m=g)==null?void 0:m.getUserInfo())==null?void 0:a.companyId;r&&(e.value=!0,o.getCompanyById({id:r,callback:{onSuccess:s=>{t.value=s},onFinish:()=>{e.value=!1}}}))};return{loading:e,companyDetail:t,getCompanyDetails:n}}}),S={key:0,class:"text-center my-auto"};function $(e,o,t,n,r,m){var i;const a=u("PageHeader"),s=u("CompanyDetail");return p(),c(k,null,[e.loading?(p(),c("div",S,o[0]||(o[0]=[d("div",{class:"spinner-border text-primary",role:"status"},[d("span",{class:"sr-only"},"Loading...")],-1)]))):y("",!0),v(a,{title:"My Company",breadcrumbs:["My Company",((i=e.companyDetail)==null?void 0:i.name)||""]},null,8,["breadcrumbs"]),!e.loading&&e.companyDetail?(p(),_(s,{key:1,companyDetail:e.companyDetail,reloadCompanyData:e.getCompanyDetails},null,8,["companyDetail","reloadCompanyData"])):y("",!0)],64)}const O=b(P,[["render",$]]);export{O as default};
