<template>
  <div class="form-container w-xxl-500px p-10 bg-white rounded-3">
    <div v-if="currentStep === STEP.RESET">
      <div class="text-center mb-10">
        <img src="/media/well-logo.png" :width="100" :height="100" />
      </div>
      <div class="d-flex flex-column gap-10">
        <h1 class="text-dark text-center">Reset Password</h1>
        <el-form
          id="change_pass_form"
          @submit.prevent="submit"
          :model="targetData"
          :rules="rules"
          ref="formRef"
          class="form"
        >
          <div class="row">
            <div
              class="col-12 fv-row d-flex flex-column justify-content-stretch mb-7"
            >
              <label
                class="d-flex align-items-center fs-6 fw-semibold mb-2 text-break required"
                >New Password
              </label>
              <div class="position-relative input-password">
                <el-form-item prop="newPassword" class="mt-auto mb-0">
                  <el-input
                    size="large"
                    class="w-100"
                    v-model="targetData.newPassword"
                    name="newPassword"
                    :type="targetData?.hideNewPassword ? 'password' : 'input'"
                  ></el-input>
                </el-form-item>
                <span
                  class="svg-icon svg-icon-1 position-absolute top-50 eye-icon"
                  @click="() => toggleEye('newPassword')"
                >
                  <SvgIcon
                    :icon="
                      targetData?.hideNewPassword
                        ? 'hidePasswordIcon'
                        : 'showPasswordIcon'
                    "
                  />
                </span>
              </div>
            </div>
            <div
              class="col-12 fv-row d-flex flex-column justify-content-stretch"
            >
              <label
                class="d-flex align-items-center fs-6 fw-semibold mb-2 required"
              >
                Confirm Password
              </label>
              <div class="position-relative input-password">
                <el-form-item prop="confirmPassword" class="mt-auto mb-0">
                  <el-input
                    size="large"
                    class="w-100"
                    v-model="targetData.confirmPassword"
                    name="confirmPassword"
                    :type="
                      targetData?.hideConfirmPassword ? 'password' : 'input'
                    "
                  ></el-input>
                </el-form-item>
                <span
                  class="svg-icon svg-icon-1 position-absolute top-50 eye-icon"
                  @click="() => toggleEye('confirmPassword')"
                >
                  <SvgIcon
                    :icon="
                      targetData?.hideConfirmPassword
                        ? 'hidePasswordIcon'
                        : 'showPasswordIcon'
                    "
                  />
                </span>
              </div>
            </div>
          </div>
          <div className="validate-password my-5">
            <p>New password must contain:</p>
            <div>
              <div
                v-for="[key, value] in Object.entries(checkPassword)"
                :key="key"
                :className="`d-flex mt-1 ${
                  value.isValid ? 'valid' : 'invalid'
                }`"
              >
                <SvgIcon
                  :icon="
                    value.isValid ? 'checkMarkIcon' : 'exclamationMarkIcon'
                  "
                />
                <span class="ms-2">{{ value.text }}</span>
              </div>
            </div>
          </div>
          <div class="d-flex justify-content-end mt-10">
            <button
              :data-kt-indicator="loading ? 'on' : null"
              class="btn btn-sm btn-primary ms-auto"
              type="submit"
              :disabled="loading"
            >
              <span v-if="!loading" class="indicator-label"> Reset </span>
              <span v-if="loading" class="indicator-progress">
                Please wait...
                <span
                  class="spinner-border spinner-border-sm align-middle ms-2"
                ></span>
              </span>
            </button>
          </div>
        </el-form>
      </div>
    </div>
    <SuccessTemplate
      v-else-if="currentStep === STEP.SUCCESS"
      :message="`Hey ${query?.user_name}, Your new password is set`"
      description="Go to Login Page and start your journey now"
    />
  </div>
</template>

<script lang="ts">
import SuccessTemplate from "@/components/templates/SuccessTemplate.vue";
import SvgIcon from "@/constants/SvgIcon.vue";
import { ExceptionCode, ExceptionMessages } from "@/constants/exceptions";
import {
  RegexLowercase,
  RegexPasswordLength,
  RegexSpecialChar,
  RegexUppercase,
} from "@/constants/regex";
import { validator } from "@/utils/validator";
import { useAuthStore } from "@/stores/auth";
import Swal from "sweetalert2";
import { defineComponent, ref, watch } from "vue";
import { useRoute } from "vue-router";

enum STEP {
  RESET = 1,
  SUCCESS,
}

interface Form {
  newPassword: string;
  confirmPassword: string;
  hideNewPassword: boolean;
  hideConfirmPassword: boolean;
}

export default defineComponent({
  name: "reset-password",
  components: {
    SvgIcon,
    SuccessTemplate,
  },
  setup() {
    const currentStep = ref<number>(STEP.RESET);
    const store = useAuthStore();
    const route = useRoute();
    const query = route.query;
    const initialValues: Form = {
      newPassword: "",
      confirmPassword: "",
      hideNewPassword: true,
      hideConfirmPassword: true,
    };
    const initialCheckPassFormat = {
      length: {
        isValid: false,
        text: "At least 8 characters",
      },
      lowercase: {
        isValid: false,
        text: "At least 1 lowercase character",
      },
      uppercase: {
        isValid: false,
        text: "At least 1 uppercase character",
      },
      specialCharacter: {
        isValid: false,
        text: "At least 1 number and 1 special character",
      },
    };
    const targetData = ref<Form>({ ...initialValues });
    const loading = ref<boolean>(false);
    const formRef = ref<null | HTMLFormElement>(null);
    const checkPassword = ref({ ...initialCheckPassFormat });

    watch(
      () => targetData.value.newPassword,
      (newValue) => {
        if (newValue !== "") {
        }
      }
    );

    const validateNewPasswordFormat = (value: string) => {
      const errLength = validator.validate(value, {
        pattern: RegexPasswordLength,
        errorsMessage: { pattern: "Incorrect password format." },
      });
      const errLowercase = validator.validate(value, {
        pattern: RegexLowercase,
        errorsMessage: { pattern: "Incorrect password format." },
      });
      const errUppercase = validator.validate(value, {
        pattern: RegexUppercase,
        errorsMessage: { pattern: "Incorrect password format." },
      });
      const errNumberAndChar = validator.validate(value, {
        pattern: RegexSpecialChar,
        errorsMessage: { pattern: "Incorrect password format." },
      });

      checkPassword.value = {
        length: {
          isValid: errLength ? false : true,
          text: "At least 8 characters",
        },
        lowercase: {
          isValid: errLowercase ? false : true,
          text: "At least 1 lowercase character",
        },
        uppercase: {
          isValid: errUppercase ? false : true,
          text: "At least 1 uppercase character",
        },
        specialCharacter: {
          isValid: errNumberAndChar ? false : true,
          text: "At least 1 number and 1 special character",
        },
      };

      const err =
        errLength || errLowercase || errUppercase || errNumberAndChar || "";

      return err;
    };

    const validateNewPass = (_rule: any, value: any, callback: any) => {
      if (value === "") {
        callback(new Error("Please type New Password"));
      } else {
        const err = validateNewPasswordFormat(value);
        if (err !== "") {
          callback(new Error(err));
        } else {
          callback();
        }
      }
    };

    const validateConfirmPass = (_rule: any, value: any, callback: any) => {
      if (value === "") {
        callback(new Error("Please type Confirm Password"));
      } else if (value !== targetData.value.newPassword) {
        callback(new Error("Confirm Password doesn't match New Password!"));
      } else {
        callback();
      }
    };

    const rules = ref({
      newPassword: [
        { validator: validateNewPass, trigger: ["change", "blur"] },
      ],
      confirmPassword: [
        { validator: validateConfirmPass, trigger: ["change", "blur"] },
      ],
    });

    const submit = () => {
      if (!formRef.value) {
        return;
      }

      loading.value = true;
      store.resetPassword({
        params: {
          email: query?.email?.toString() || "",
          otp: query?.otp?.toString() || "",
          newPassword: targetData.value.newPassword,
        },
        callback: {
          onSuccess: () => {
            formRef.value?.validate((valid: boolean) => {
              if (valid) {
                currentStep.value = STEP.SUCCESS;
              }
            });
          },
          onFinish: () => {
            loading.value = false;
          },
          onFailure: (error) => {
            Swal.fire({
              text:
              ExceptionMessages[error?.response?.data?.errorCode as ExceptionCode] ||
                error?.response?.data?.message ||
                error?.message ||
                "Sorry, looks like there are some errors detected, please try again.",
              icon: "error",
              buttonsStyling: false,
              confirmButtonText: "Got it!",
              heightAuto: false,
              customClass: {
                confirmButton: "btn fw-semibold btn-light-danger",
              },
            });
          },
        },
      });
    };

    const toggleEye = (field: string) => {
      if (field === "confirmPassword") {
        targetData.value.hideConfirmPassword =
          !targetData.value.hideConfirmPassword;
      } else if (field === "newPassword") {
        targetData.value.hideNewPassword = !targetData.value.hideNewPassword;
      }
    };

    return {
      currentStep,
      STEP,
      formRef,
      rules,
      loading,
      targetData,
      checkPassword,
      query,
      submit,
      toggleEye,
    };
  },
});
</script>