import{S as k}from"./SvgIcon-CMhyaXWN.js";import{d as x,q as c,A as V,Z as D,_ as M,c as v,l as y,o as h,a as o,b as d,t as q,r as f,w as C,B as w,p as B}from"./index-CGNRhvz7.js";import{u as I}from"./company-oDyd0dWV.js";const P=x({name:"company-modal",components:{SvgIcon:k},props:{isVisible:{type:Boolean,required:!0},close:{type:Function,required:!0},loadPage:{type:Function,required:!1}},setup(e){const a=I(),l=c({name:"",registerNumber:"",description:""}),i=c(null),r=c(!1),s=c(""),u=()=>{e.close(),_()};V(s,t=>{t&&g()});const g=async()=>{s.value&&a.getCompanyById({id:s.value,callback:{onSuccess:t=>{l.value={name:t==null?void 0:t.name,registerNumber:t==null?void 0:t.registerNumber,description:t==null?void 0:t.description}}}})},p=t=>{s.value=t.toString()},b=c({name:[{required:!0,message:"Please type Company Name",trigger:["blur","change"]}],description:[{required:!0,message:"Please select Description",trigger:["blur","change"]}]}),n=()=>{i.value&&i.value.validate(t=>{t&&(s.value?S():N())})},N=async()=>{var t;r.value=!0,a.createCompany({params:{...l.value,registerNumber:((t=l==null?void 0:l.value)==null?void 0:t.registerNumber)||null},callback:{onSuccess:()=>{var m;(m=e==null?void 0:e.loadPage)==null||m.call(e),u()},onFinish:()=>{r.value=!1}}})},S=async()=>{var t;r.value=!0,a.editCompany({id:s.value,params:{...l.value,registerNumber:((t=l==null?void 0:l.value)==null?void 0:t.registerNumber)||null},callback:{onSuccess:()=>{var m;(m=e==null?void 0:e.loadPage)==null||m.call(e),u()},onFinish:()=>{r.value=!1}}})},_=()=>{var t;s.value="",l.value={name:"",registerNumber:"",description:""},(t=i==null?void 0:i.value)==null||t.resetFields()};return{id:s,rules:b,loading:r,targetData:l,formRef:i,rolesOptions:D,closeModal:u,submit:n,setId:p,resetData:_}}}),F={key:0,class:"fixed top-0 right-0 bottom-0 left-0 flex flex-row items-center bg-dark z-40"},$={class:"bg-white h-auto w-11/12 flex flex-col items-center gap-3 mx-auto p-5 rounded-xl"},E={class:"h-auto w-full flex flex-row items-center justify-between"},U={class:"text-lg font-bold text-dark"},j={class:"flex flex-col gap-2"},z={class:"flex flex-col gap-2"},A={class:"flex flex-row items-start mt-4 gap-3"},O=["disabled"],T=["disabled"],Z={key:0,class:"indicator-label"},G={key:1,class:"indicator-progress"};function H(e,a,l,i,r,s){const u=f("SvgIcon"),g=f("el-input"),p=f("el-form-item"),b=f("el-form");return e.isVisible?(h(),v("div",F,[o("div",$,[o("div",E,[o("h3",U,q(e.id?"Edit Company":"New Company"),1),o("span",{class:"cursor-pointer",onClick:a[0]||(a[0]=(...n)=>e.closeModal&&e.closeModal(...n))},[d(u,{icon:"closeModalIcon"})])]),d(b,{id:"product_form",onSubmit:B(e.submit,["prevent"]),model:e.targetData,rules:e.rules,ref:"formRef",class:"h-auto w-full"},{default:C(()=>[o("div",j,[a[4]||(a[4]=o("label",{class:"font-semibold"},[w("Company Name "),o("span",{class:"text-danger-active font-light"},"*")],-1)),d(p,{prop:"name"},{default:C(()=>[d(g,{modelValue:e.targetData.name,"onUpdate:modelValue":a[1]||(a[1]=n=>e.targetData.name=n),placeholder:"",name:"name","show-word-limit":"",maxlength:"150"},null,8,["modelValue"])]),_:1})]),o("div",z,[a[5]||(a[5]=o("label",{class:"font-semibold"},[w("Description "),o("span",{class:"text-danger-active font-light"},"*")],-1)),d(p,{prop:"description",class:"mt-auto"},{default:C(()=>[d(g,{class:"w-100",modelValue:e.targetData.description,"onUpdate:modelValue":a[2]||(a[2]=n=>e.targetData.description=n),placeholder:"",name:"description"},null,8,["modelValue"])]),_:1})]),o("div",A,[o("button",{type:"button",class:"bg-grey-400 hover:bg-grey-500 px-4 py-2 text-gray-700 rounded-md",onClick:a[3]||(a[3]=(...n)=>e.closeModal&&e.closeModal(...n)),disabled:e.loading}," Discard ",8,O),o("button",{class:"bg-primary px-4 py-2 text-white rounded-md",type:"submit",disabled:e.loading},[e.loading?y("",!0):(h(),v("span",Z," Save ")),e.loading?(h(),v("span",G,a[6]||(a[6]=[w(" Please wait... "),o("span",{class:"spinner-border spinner-border-sm align-middle ms-2"},null,-1)]))):y("",!0)],8,T)])]),_:1},8,["onSubmit","model","rules"])])])):y("",!0)}const Q=M(P,[["render",H]]);export{Q as C};
