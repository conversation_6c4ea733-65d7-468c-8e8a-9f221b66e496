<template>
  <div
    v-if="isVisible"
    class="fixed top-0 right-0 bottom-0 left-0 z-40 flex items-center"
  >
    <!-- Dark overlay background -->
    <div class="absolute inset-0 bg-sidebar-background opacity-25"></div>
    <div
      class="relative bg-card-background text-card-text-light max-h-11/12 w-11/12 flex flex-col items-center gap-3 mx-auto my-6 p-5 rounded-xl md:text-lg md:w-4/5 lg:w-2/5 lg:h-auto"    >
      <div class="h-auto w-full flex flex-row items-center justify-between">
        <h3 class="text-lg font-bold">
          {{ `${id ? "Edit Drill String" : "New Drill String"}` }}
        </h3>
        <span class="cursor-pointer" @click="closeModal">
          <SvgIcon icon="closeModalIcon" />
        </span>
      </div>

      <el-form
        id="drill_string_form"
        @submit.prevent="submit"
        :model="targetData"
        :rules="rules"
        ref="formRef"
        class="h-auto w-full"
      >
        <div class="h-auto w-full flex flex-col gap-3 md:flex-row md:gap-4">
          <div class="h-auto w-full flex flex-col gap-3">
            <div class="h-auto w-full flex flex-col gap-1">
              <label class="font-bold"
                >Description<span class="text-danger-active font-light">*</span>
              </label>
              <el-form-item prop="description" class="mt-auto">
                <el-input
                  v-model="targetData.description"
                  placeholder=""
                  name="description"
                ></el-input>
              </el-form-item>
            </div>
            <div class="h-auto w-full flex flex-col gap-1">
              <label class="font-bold"
                >Wt. (Weight) (lb/ft)<el-popover
                  placement="top"
                  :width="300"
                  trigger="hover"
                >
                  <template #reference>
                    <i class="fas fa-exclamation-circle ms-2 fs-7"></i>
                  </template>
                  <span
                    >The Weight of the drill pipe or tool is typically expressed
                    in pounds per foot (lb/ft) and represents the mass of the
                    component per linear foot. It helps determine the buoyancy
                    and load-bearing capacity of the drill string. It is an
                    input and doesn't require additional information.
                  </span>
                </el-popover></label
              >
              <el-form-item prop="weight">
                <el-input
                  type="number"
                  :controls="false"
                  step="any"
                  v-model="targetData.weight"
                  placeholder=""
                  name="weight"
                ></el-input>
              </el-form-item>
            </div>

            <div class="h-auto w-full flex flex-col gap-1">
              <label class="font-bold"
                >Tool Joint (OD) (in)<el-popover
                  placement="top"
                  :width="300"
                  trigger="hover"
                >
                  <template #reference>
                    <i class="fas fa-exclamation-circle ms-2 fs-7"></i>
                  </template>
                  <span
                    >Tool joints are specialized connectors used to join
                    sections of drill pipe. Tool Joint OD is the outer diameter
                    of the tool joint, typically larger than the drill pipe OD.
                    It is an input and doesn't require additional information.
                  </span>
                </el-popover></label
              >
              <el-form-item prop="outsideDiameter">
                <el-input
                  type="number"
                  :controls="false"
                  step="any"
                  v-model="targetData.outsideDiameter"
                  placeholder=""
                  name="outsideDiameter"
                ></el-input>
              </el-form-item>
            </div>
          </div>
          <div class="h-auto w-full flex flex-col gap-3">
            <div class="h-auto w-full flex flex-col gap-1">
              <label class="font-bold">
                Tool Joint ID (Inside Diameter) (in)<el-popover
                  placement="top"
                  :width="300"
                  trigger="hover"
                >
                  <template #reference>
                    <i class="fas fa-exclamation-circle ms-2 fs-7"></i>
                  </template>
                  <span
                    >Tool Joint ID is the inner diameter of the tool joint,
                    representing the clear passage inside the tool joint. It is
                    an input and doesn't require additional information.
                  </span>
                </el-popover>
              </label>

              <el-form-item prop="insideDiameter">
                <el-input
                  type="number"
                  :controls="false"
                  step="any"
                  v-model="targetData.insideDiameter"
                  placeholder=""
                  name="insideDiameter"
                ></el-input>
              </el-form-item>
            </div>

            <div class="h-auto w-full flex flex-col gap-1">
              <label class="font-bold"
                >Length (ft)<el-popover
                  placement="top"
                  :width="300"
                  trigger="hover"
                >
                  <template #reference>
                    <i class="fas fa-exclamation-circle ms-2 fs-7"></i>
                  </template>
                  <span
                    >The Length represents the total length of the drill string
                    component, such as a section of drill pipe or a tool.
                  </span>
                </el-popover></label
              >
              <el-form-item prop="length">
                <el-input
                  type="number"
                  :controls="false"
                  step="any"
                  v-model="targetData.length"
                  placeholder=""
                  name="length"
                ></el-input>
              </el-form-item>
            </div>
          </div>
        </div>
        <div class="h-auto w-full flex flex-row items-center gap-2">
          <button
            type="button"
            class="bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover rounded-md px-4 py-2 font-semibold md:px-4 md:py-3 md:text-xl"
            @click="closeModal"
            :disabled="loading"
          >
            Discard
          </button>
          <button
            class="bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover rounded-md px-4 py-2 font-semibold md:px-4 md:py-3 md:text-xl"
            type="submit"
            :disabled="loading"
          >
            <span v-if="!loading" class="indicator-label"> Save </span>
            <span v-if="loading" class="indicator-progress">
              Please wait...
              <span
                class="spinner-border spinner-border-sm align-middle ms-2"
              ></span>
            </span>
          </button>
        </div>
      </el-form>
    </div>
  </div>
</template>

<script lang="ts">
import SvgIcon from "@/constants/SvgIcon.vue";
import { useDrillStringStore } from "@/stores/drill-string";
import { defineComponent, inject, ref, watch } from "vue";
import { useRoute } from "vue-router";
import type { Provide } from "@/types/injection-types";

interface NewDrillStringData {
  description: string;
  insideDiameter: number | null;
  outsideDiameter: number | null;
  length: number | null;
  weight: number | null;
}

export default defineComponent({
  name: "drill-string-modal",
  components: { SvgIcon },
  props: {
    isVisible: {
      type: Boolean,
      required: true,
    },
    close: {
      type: Function,
      required: true,
    },
    loadPage: {
      type: Function,
      default: () => {},
      required: true,
    },
  },
  setup(props) {
    const route = useRoute();
    const drillStringStore = useDrillStringStore();
    const modal = ref(false);
    const targetData = ref<NewDrillStringData>({
      description: "",
      insideDiameter: null,
      outsideDiameter: null,
      length: null,
      weight: null,
    });
    const formRef = ref<null | HTMLFormElement>(null);
    const loading = ref<boolean>(false);
    const id = ref("");
    const dailyReportProvide = inject<Provide.DailyReport>("dailyReport");

    watch(id, (newValue) => {
      if (newValue !== "") {
        getDrillStringDetails();
        // targetData.value = { ...drillStringList[0] };
      }
    });

    watch(
      () => props.isVisible,
      (newValue) => {
        if (newValue === false) {
          id.value = "";
          reset();
          formRef?.value?.resetFields();
        }
      }
    );

    const getDrillStringDetails = async (): Promise<void> => {
      drillStringStore.getDrillStringDetails({
        id: id.value,
        callback: {
          onSuccess: (res: any) => {
            targetData.value = res;
          },
        },
      });
    };

    const updateDrillString = async (param: any): Promise<void> => {
      loading.value = true;
      drillStringStore.updateDrillString({
        id: id.value,
        params: param,
        callback: {
          onSuccess: (_res: any) => {
            if (props?.loadPage) {
              props?.loadPage();
            }
            props.close();
          },
          onFinish: (_err: any) => {
            loading.value = false;
          },
        },
      });
    };

    const createDrillString = async (param: any): Promise<void> => {
      loading.value = true;
      drillStringStore.createDrillString({
        params: param,
        callback: {
          onSuccess: (_res: any) => {
            if (props?.loadPage) {
              props?.loadPage();
            }
            props.close();
          },
          onFinish: (_err: any) => {
            loading.value = false;
          },
        },
      });
    };

    const closeModal = () => {
      props.close();
    };

    const setId = (drillStringId: string) => {
      id.value = drillStringId.toString();
    };

    const rules = ref({
      description: [
        {
          required: true,
          message: "Please type Description",
          trigger: "blur",
        },
      ],
    });

    const submit = () => {
      if (!formRef.value) {
        return;
      }

      formRef.value.validate((valid: boolean) => {
        if (valid) {
          const param = {
            description: targetData?.value?.description,
            insideDiameter: Number(targetData?.value?.insideDiameter),
            outsideDiameter: Number(targetData?.value?.outsideDiameter),
            length: Number(targetData?.value?.length),
            weight: Number(targetData?.value?.weight),
          };

          if (id?.value) {
            updateDrillString({
              ...param,
              dailyReportId: dailyReportProvide?.getDailyReportId(),
            });
          } else {
            dailyReportProvide?.createDailyReport({
              wellId: route?.params?.id as string,
              callback: {
                onSuccess: (res: string) => {
                  createDrillString({ ...param, dailyReportId: res });
                },
              },
            });
          }
        }
      });
    };

    const reset = () => {
      targetData.value = {
        description: "",
        insideDiameter: null,
        outsideDiameter: null,
        length: null,
        weight: null,
      };
    };

    return {
      id,
      modal,
      rules,
      loading,
      targetData,
      formRef,
      closeModal,
      submit,
      setId,
      reset,
    };
  },
});
</script>
