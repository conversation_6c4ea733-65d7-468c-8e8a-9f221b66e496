import{P as C}from"./PageHeader-l8HvxxsN.js";import{u as f}from"./company-DGE9srvS.js";import{d as D,q as l,j as g,x as v,_ as b,c,l as d,b as _,m as k,a as u,F as B,r as y,o as p}from"./index-DalLS0_6.js";import{C as P}from"./CompanyDetail-Cn12BIxD.js";import"./handleFailure-DrOe_u9W.js";import"./CompanyModal-BoBgr7aV.js";import"./SvgIcon-CfrWCA-H.js";import"./TablePagination-BmkwndgK.js";import"./TableHeader-DGMH-x_O.js";import"./table-bhK9qpe4.js";import"./date-CCTVzEJd.js";import"./customer-CD9RajQq.js";import"./CustomerModal-BJI2MaCc.js";import"./user-UjS69U41.js";import"./index.esm-C3uaQ3c9.js";import"./AssignUserModal-BA3iiGKX.js";import"./UserModal-DfH6xbe7.js";import"./validator-BJ5Qi8qK.js";const S=D({name:"company-overview",components:{CompanyDetail:P,PageHeader:C},setup(){var t,n;const e=l(!1),o=f(),i=g(),s=l(),a=((n=(t=i.params)==null?void 0:t.id)==null?void 0:n.toString())||"";v(()=>{a&&m()});const m=async()=>{e.value=!0,o.getCompanyById({id:a,callback:{onSuccess:r=>{s.value=r},onFinish:()=>{e.value=!1}}})};return{loading:e,companyId:a,companyDetail:s,getCompanyDetails:m}}}),$={key:0,class:"text-center my-auto"};function F(e,o,i,s,a,m){var r;const t=y("PageHeader"),n=y("CompanyDetail");return p(),c(B,null,[e.loading?(p(),c("div",$,o[0]||(o[0]=[u("div",{class:"spinner-border text-primary",role:"status"},[u("span",{class:"sr-only"},"Loading...")],-1)]))):d("",!0),_(t,{title:"Companies",breadcrumbs:["Companies",((r=e.companyDetail)==null?void 0:r.name)||""]},null,8,["breadcrumbs"]),!e.loading&&e.companyDetail?(p(),k(n,{key:1,companyDetail:e.companyDetail,reloadCompanyData:e.getCompanyDetails},null,8,["companyDetail","reloadCompanyData"])):d("",!0)],64)}const K=b(S,[["render",F]]);export{K as default};
