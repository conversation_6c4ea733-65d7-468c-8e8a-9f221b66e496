import{P as he}from"./PageHeader-3hadTn26.js";import{S as G}from"./SvgIcon-CMhyaXWN.js";import{d as Q,e as de,q as x,x as ne,A as oe,_ as K,r as F,c as p,o as c,a as e,b as W,F as U,k as le,B as J,t as a,D as we,h as ve,E as re,G as pe,n as ae,m as be,H as te,I as me,K as xe,J as z,U as Y,l as O,L as $e,M as ke,p as Se,N as Ie}from"./index-CGNRhvz7.js";import{n as fe}from"./numberFormatter-C7uP7NWj.js";import{u as ye}from"./well-Caso2ZGG.js";import{u as ge,s as Ce,a as _e,b as De,B as We}from"./sample-VKOSqYFu.js";import{F as je}from"./Filter-cvywqNLp.js";import{f as se,a as Re}from"./date-CvSHk5ED.js";import"./handleFailure-DtTpu7r3.js";import"./company-oDyd0dWV.js";const Ae=Q({name:"history-tab",props:{closeModal:{type:Function,required:!0},wellId:{type:String,required:!0},date:{type:Array,required:!0}},components:{SvgIcon:G},setup(t){const s=de(),$=ge(),I=x([]),b=x(!1);ne(()=>{t!=null&&t.wellId&&w()}),oe(()=>t==null?void 0:t.wellId,()=>{t!=null&&t.wellId&&w()}),oe(()=>t==null?void 0:t.date,()=>{t!=null&&t.wellId&&w()});const w=async()=>{var y,k;const l={wellId:t==null?void 0:t.wellId,fromDate:((y=t==null?void 0:t.date)==null?void 0:y.length)>0?se(t==null?void 0:t.date[0],"YYYY-MM-DD"):null,toDate:((k=t==null?void 0:t.date)==null?void 0:k.length)>0?se(t==null?void 0:t.date[1],"YYYY-MM-DD"):null,page:1,limit:500};b.value=!0,$.getDailyReports({params:l,callback:{onSuccess:o=>{I.value=o==null?void 0:o.items},onFinish:o=>{b.value=!1}}})};return{loading:b,historyList:I,editReport:l=>{s.push({path:`/wells/${t.wellId}/daily-report/${l}`}),t.closeModal()},formatDate:se,reset:()=>{I.value=[]}}}}),Le={key:0,class:"text-center"},Me={key:1,class:"h-20 w-fullflex flex-row items-center justify-center text-dark font-bold"},Ne={key:2,class:"flex flex-col gap-4"},Fe={class:"flex flex-row items-center"},Ve={class:"flex flex-col"},Te={class:"text-gray-800 font-bold text-sm"},Pe={class:"text-gray-400 font-semibold text-xs"},Ye={class:"text-grey-500 font-semibold text-sm"},Oe={class:"text-primary mr-2"},He={class:"text-primary"},Be={class:"text-uppercase"},Ee=["onClick"];function ze(t,s,$,I,b,w){const h=F("el-date-picker"),g=F("SvgIcon");return t.loading?(c(),p("div",Le,s[1]||(s[1]=[e("div",{class:"spinner-border text-primary",role:"status"},[e("span",{class:"sr-only"},"Loading...")],-1)]))):t.historyList.length===0?(c(),p("div",Me," No Data ")):(c(),p("div",Ne,[W(h,{modelValue:t.date,"onUpdate:modelValue":s[0]||(s[0]=l=>t.date=l),type:"daterange","range-separator":"-","start-placeholder":"From","end-placeholder":"To",format:"MM/DD/YYYY",class:"max-w-full"},null,8,["modelValue"]),(c(!0),p(U,null,le(t.historyList,l=>{var y,k,o,D;return c(),p("div",{key:l.id},[e("div",Fe,[s[5]||(s[5]=e("span",{class:"bg-grey-300 h-[40px] w-3 mr-4"},null,-1)),e("div",Ve,[e("h4",Te,[J(a(`Daily Report ${t.formatDate(l==null?void 0:l.createdAt,"MM/DD/YYYY hh:mm")}`)+" ",1),e("span",Pe,a(t.formatDate(l==null?void 0:l.createdAt,"a")),1)]),e("span",Ye,[s[2]||(s[2]=J(" Created by ")),e("span",Oe,a(`${((y=l==null?void 0:l.createdBy)==null?void 0:y.firstName)||""} ${((k=l==null?void 0:l.createdBy)==null?void 0:k.lastName)||""}`),1),s[3]||(s[3]=J(" Last updated by ")),e("span",He,a(`${((o=l==null?void 0:l.updatedBy)==null?void 0:o.firstName)||""} ${((D=l==null?void 0:l.updatedBy)==null?void 0:D.lastName)||""}`),1),s[4]||(s[4]=J(" at ")),e("span",Be,a(t.formatDate(l==null?void 0:l.updatedAt,"MM/DD/YYYY hh:mm a")),1)])]),e("button",{class:"cursor-pointer h-auto w-auto bg-grey-300 p-0.5 rounded-sm",onClick:()=>t.editReport(l==null?void 0:l.id)},[W(g,{icon:"pencilIcon"})],8,Ee)])])}),128))]))}const Ue=K(Ae,[["render",ze]]);function ee(t){let s=getComputedStyle(document.documentElement).getPropertyValue(t);return s&&s.length>0&&(s=s.trim()),s}const qe=Q({name:"sample-info",components:{SvgIcon:G},props:{sample:{type:Object,required:!0},costSummary:{type:Object,required:!0}},setup(){const t=x(),s=x(null);let $={};const I=[{name:"Shear Stress",data:[30,40,40,90,90,70,70]}];return we(()=>{Object.assign($,Je())}),ne(()=>{}),{chart:$,series:I,chartRef:s,sampleData:t,formatDate:se,getOption:ve,formatTime:Re,numberWithCommas:fe,sampleFromOptions:Ce}}}),Je=()=>{const t=ee("--color-grey-500"),s=ee("--color-grey-200"),$=ee("--color-primary"),I=ee("--color-primary-light");return{chart:{fontFamily:"inherit",type:"area",height:350,toolbar:{show:!1}},plotOptions:{},legend:{show:!1},dataLabels:{enabled:!1},fill:{type:"solid",opacity:1},stroke:{curve:"smooth",show:!0,width:3,colors:[$]},xaxis:{categories:["1","2","3","4","5","6","7"],axisBorder:{show:!1},axisTicks:{show:!1},labels:{style:{colors:t,fontSize:"12px"}},crosshairs:{position:"front",stroke:{color:$,width:1,dashArray:3}},tooltip:{enabled:!1},title:{text:"Shear Rate"}},yaxis:{labels:{style:{colors:t,fontSize:"12px"}},title:{text:"Shear Stress"}},states:{hover:{filter:{type:"none"}},active:{allowMultipleDataPointsSelection:!1,filter:{type:"none"}}},tooltip:{style:{fontSize:"12px"},y:{formatter:function(b){return b.toString()}}},colors:[I],grid:{borderColor:s,strokeDashArray:4,yaxis:{lines:{show:!0}}},markers:{strokeColors:$,strokeWidth:3}}},Ge={class:"flex flex-col gap-3 text-gray-800"},Qe={class:"flex flex-row flex-wrap items-center justify-between"},Ke={class:"font-light"},Xe={class:"flex flex-row flex-wrap items-center justify-between"},Ze={class:"font-light"},et={class:"flex flex-row flex-wrap items-center justify-between"},tt={class:"font-light"},st={class:"flex flex-row flex-wrap items-center justify-between"},ot={class:"font-light"},lt={class:"flex flex-row flex-wrap items-center justify-between"},at={class:"font-light"},nt={class:"flex flex-row flex-wrap items-center justify-between"},it={class:"font-light"},rt={class:"flex flex-row flex-wrap items-center justify-between"},dt={class:"font-light"},ft={class:"flex flex-row flex-wrap items-center justify-between"},ct={class:"font-light"},ut={class:"flex flex-row flex-wrap items-center justify-between"},pt={class:"font-light"},bt={class:"flex flex-row flex-wrap items-center justify-between"},mt={class:"font-light"},yt={class:"flex flex-row flex-wrap items-center justify-between"},gt={class:"font-light"},ht={class:"flex flex-row flex-wrap items-center justify-between"},wt={class:"font-light"},vt={class:"flex flex-row flex-wrap items-center justify-between"},xt={class:"font-light"},$t={class:"flex flex-row flex-wrap items-center justify-between"},kt={class:"font-light"},St={class:"flex flex-row flex-wrap items-center justify-between"},It={class:"font-light"},Ct={class:"flex flex-row flex-wrap items-center justify-between"},_t={class:"font-light"},Dt={class:"flex flex-row flex-wrap items-center justify-between"},Wt={class:"font-light"},jt={class:"d-flex flex-column gap-3 text-gray-800"},Rt={class:"d-flex flex-wrap align-items-center justify-content-between"},At={class:"font-light"},Lt={class:"d-flex flex-wrap align-items-center justify-content-between"},Mt={class:"font-light"},Nt={class:"d-flex flex-wrap align-items-center justify-content-between"},Ft={class:"font-light"},Vt={class:"d-flex flex-wrap align-items-center justify-content-between"},Tt={class:"font-light"},Pt={class:"d-flex flex-wrap align-items-center justify-content-between"},Yt={class:"font-light"},Ot={class:"d-flex flex-wrap align-items-center justify-content-between"},Ht={class:"font-light"},Bt={class:"d-flex flex-wrap align-items-center justify-content-between"},Et={class:"font-light"},zt={class:"d-flex flex-wrap align-items-center justify-content-between"},Ut={class:"font-light"},qt={class:"d-flex flex-wrap align-items-center justify-content-between"},Jt={class:"font-light"},Gt={class:"d-flex flex-wrap align-items-center justify-content-between"},Qt={class:"font-light"};function Kt(t,s,$,I,b,w){var g,l,y,k,o,D,R,A,f,i,u,S,_,C,V,L,j,M,H,B,E,r,m,n,v,T,d,N;const h=F("apexchart");return c(),p(U,null,[e("div",Ge,[e("div",Qe,[s[0]||(s[0]=e("span",{class:"font-bold text-lg"},"Sample from:",-1)),e("span",Ke,a(((l=t.getOption((g=t.sample)==null?void 0:g.sampleFrom,t.sampleFromOptions))==null?void 0:l.label)||""),1)]),e("div",Xe,[s[1]||(s[1]=e("span",{class:"font-bold"},"Time:",-1)),e("span",Ze,a(t.formatTime((y=t.sample)==null?void 0:y.timeSampleTaken)),1)]),e("div",et,[s[2]||(s[2]=e("span",{class:"font-bold"},"MW (ppg or lbs/gal):",-1)),e("span",tt,a((k=t.sample)!=null&&k.weightedMud?"Active":"Inactive"),1)]),e("div",st,[s[3]||(s[3]=e("span",{class:"font-bold"},"Funnel Viscosity (sec/qt):",-1)),e("span",ot,a(t.numberWithCommas((o=t.sample)==null?void 0:o.funnelViscosity)),1)]),e("div",lt,[s[4]||(s[4]=e("span",{class:"font-bold"},"PV (Plastic Viscosity) (cP):",-1)),e("span",at,a(t.numberWithCommas((D=t.sample)==null?void 0:D.plasticViscosity)),1)]),e("div",nt,[s[5]||(s[5]=e("span",{class:"font-bold"},"YP (Yield Point) (lbf/100ft2): ",-1)),e("span",it,a(t.numberWithCommas((R=t.sample)==null?void 0:R.yieldPoint)),1)]),e("div",rt,[s[6]||(s[6]=e("span",{class:"font-bold"},"6 rpm:",-1)),e("span",dt,a(t.numberWithCommas((A=t.sample)==null?void 0:A.shearRate6)),1)]),e("div",ft,[s[7]||(s[7]=e("span",{class:"font-bold"},"API filtrate (ml/30min):",-1)),e("span",ct,a(t.numberWithCommas((f=t.sample)==null?void 0:f.apiFiltrate)),1)]),e("div",ut,[s[8]||(s[8]=e("span",{class:"font-bold"},"API Cake: ",-1)),e("span",pt,a(t.numberWithCommas((i=t.sample)==null?void 0:i.apiCakeThickness)),1)]),e("div",bt,[s[9]||(s[9]=e("span",{class:"font-bold"},"pH:",-1)),e("span",mt,a(t.numberWithCommas((u=t.sample)==null?void 0:u.pH)),1)]),e("div",yt,[s[10]||(s[10]=e("span",{class:"font-bold"},"Mud Alkalinity (Pm) (ml): ",-1)),e("span",gt,a(t.numberWithCommas((S=t.sample)==null?void 0:S.mudAlkalinity)),1)]),e("div",ht,[s[11]||(s[11]=e("span",{class:"font-bold"},"Filtrate Alkalinity (Pf) (ml):",-1)),e("span",wt,a(t.numberWithCommas((_=t.sample)==null?void 0:_.filtrateAlkalinity)),1)]),e("div",vt,[s[12]||(s[12]=e("span",{class:"font-bold"},"Chlorides (mg/L):",-1)),e("span",xt,a(t.numberWithCommas((C=t.sample)==null?void 0:C.chlorides)),1)]),e("div",$t,[s[13]||(s[13]=e("span",{class:"font-bold"},"Total Hardness (mg/L):",-1)),e("span",kt,a(t.numberWithCommas((V=t.sample)==null?void 0:V.totalHardness)),1)]),e("div",St,[s[14]||(s[14]=e("span",{class:"font-bold"},"Linear Gel Strength (LGS) (%):",-1)),e("span",It,a(t.numberWithCommas((L=t.sample)==null?void 0:L.linearGelStrengthPercent)),1)])]),s[29]||(s[29]=e("h3",{class:"font-bold text-lg text-primary py-3"},"Cost",-1)),e("div",Ct,[s[15]||(s[15]=e("span",{class:"font-bold"},"Daily Cost:",-1)),e("span",_t,a(t.numberWithCommas((j=t.costSummary)==null?void 0:j.dailyCost)),1)]),e("div",Dt,[s[16]||(s[16]=e("span",{class:"font-bold"},"Cumulative Cost:",-1)),e("span",Wt,a(t.numberWithCommas((M=t.costSummary)==null?void 0:M.cumulativeCost)),1)]),e("div",null,[s[17]||(s[17]=e("p",{class:"font-semibold"},"Rheological Properties",-1)),W(h,{ref:"chartRef",type:"area",options:t.chart,series:t.series},null,8,["options","series"])]),e("div",jt,[s[28]||(s[28]=e("h3",{class:"font-bold text-lg text-primary py-3"},"Volumes",-1)),e("div",Rt,[s[18]||(s[18]=e("span",{class:"font-bold"},"Mud Lease/Consignment Volume (bbl)",-1)),e("span",At,a(t.numberWithCommas((H=t.sample)==null?void 0:H.mudLeaseOrConsignmentVolume)),1)]),e("div",Lt,[s[19]||(s[19]=e("span",{class:"font-bold"},"Mud Volume on Location (bbl)",-1)),e("span",Mt,a(t.numberWithCommas((B=t.sample)==null?void 0:B.mudVolumeOnLocation)),1)]),e("div",Nt,[s[20]||(s[20]=e("span",{class:"font-bold"},"Mud Volume in Storage (bbl)",-1)),e("span",Ft,a(t.numberWithCommas((E=t.sample)==null?void 0:E.mudVolumeInStorage)),1)]),e("div",Vt,[s[21]||(s[21]=e("span",{class:"font-bold"},"Over/Under Consigned Volume (bbl)",-1)),e("span",Tt,a(t.numberWithCommas((r=t.sample)==null?void 0:r.overOrUnderConsignedVolume)),1)]),e("div",Pt,[s[22]||(s[22]=e("span",{class:"font-bold"},"Total On Location (gal)",-1)),e("span",Yt,a(t.numberWithCommas((m=t.sample)==null?void 0:m.totalOnLocation)),1)]),e("div",Ot,[s[23]||(s[23]=e("span",{class:"font-bold"},"Daily Usage (gal)",-1)),e("span",Ht,a(t.numberWithCommas((n=t.sample)==null?void 0:n.dailyUsage)),1)]),e("div",Bt,[s[24]||(s[24]=e("span",{class:"font-bold"},"Total Usage (gal)",-1)),e("span",Et,a(t.numberWithCommas((v=t.sample)==null?void 0:v.totalUsage)),1)]),e("div",zt,[s[25]||(s[25]=e("span",{class:"font-bold"},"Total Daily Dilution (bbl)",-1)),e("span",Ut,a(t.numberWithCommas((T=t.sample)==null?void 0:T.totalDailyDilution)),1)]),e("div",qt,[s[26]||(s[26]=e("span",{class:"font-bold"},"New Hole Volume (bbl)",-1)),e("span",Jt,a(t.numberWithCommas((d=t.sample)==null?void 0:d.newHoleVolume)),1)]),e("div",Gt,[s[27]||(s[27]=e("span",{class:"font-bold"},"Dilution / New Hole Ratio",-1)),e("span",Qt,a(t.numberWithCommas((N=t.sample)==null?void 0:N.dilutionOrNewHoleRatio)),1)])])],64)}const Xt=K(qe,[["render",Kt]]),Zt=Q({name:"properties-tab",components:{SvgIcon:G,SampleInfo:Xt},props:{wellId:{type:String,required:!0}},setup(t){const s=ge(),$=_e(),I=De(),b=x(""),w=x(!1),h=x([]),g=x([]),l=x(""),y=x({});ne(()=>{t!=null&&t.wellId&&k()}),oe(()=>t==null?void 0:t.wellId,()=>{t!=null&&t.wellId&&k()});const k=async()=>{t!=null&&t.wellId&&(w.value=!0,s.getLatestDailyReport({wellId:t==null?void 0:t.wellId,callback:{onSuccess:f=>{f!=null&&f.id&&(b.value=f==null?void 0:f.id,o(),D())},onFailure:f=>{var i,u,S,_;re.resultAlert(pe[(u=(i=f==null?void 0:f.response)==null?void 0:i.data)==null?void 0:u.errorCode]||((_=(S=f==null?void 0:f.response)==null?void 0:S.data)==null?void 0:_.message)||(f==null?void 0:f.message)||"Sorry, looks like there are some errors detected, please try again.","error")},onFinish:()=>{w.value=!1}}}))},o=async()=>{if(!b.value)return;w.value=!0;const f={dailyReportId:b==null?void 0:b.value,page:1,limit:200};I.getSamples({params:f,callback:{onSuccess:i=>{var u,S;h.value=i==null?void 0:i.items,g.value=(u=i==null?void 0:i.items)==null?void 0:u.map((_,C)=>({value:_==null?void 0:_.id,title:"Sample "+(C+1)})),l.value=(S=i==null?void 0:i.items[0])==null?void 0:S.id},onFinish:i=>{w.value=!1}}})},D=async()=>{if(!b.value)return;w.value=!0;const f={dailyReportId:b==null?void 0:b.value,wellId:t==null?void 0:t.wellId};$.getCostSummary({params:f,callback:{onSuccess:i=>{y.value=i},onFinish:i=>{w.value=!1}}})};return{loading:w,tabs:g,tabIndex:l,sampleList:h,costSummary:y,reset:()=>{b.value="",h.value=[],g.value=[],l.value="",y.value={}},setActiveTab:f=>{const i=f.target;l.value=i.getAttribute("data-tab-index")}}}}),es={key:0,class:"text-center"},ts={key:1},ss={class:"h-auto w-full"},os={class:"h-auto w-full flex flex-row flex-wrap items-center gap-2",role:"tablist"},ls=["data-tab-index"],as={class:"py-4"};function ns(t,s,$,I,b,w){const h=F("el-empty"),g=F("SampleInfo");return t.loading?(c(),p("div",es,s[1]||(s[1]=[e("div",{class:"spinner-border text-primary",role:"status"},[e("span",{class:"sr-only"},"Loading...")],-1)]))):(c(),p("div",ts,[e("div",ss,[e("ul",os,[(c(!0),p(U,null,le(t.tabs,l=>(c(),p("li",{key:l==null?void 0:l.value},[e("div",{class:ae(["whitespace-nowrap cursor-pointer font-semibold hover:text-primary hover:border-b-2 hover:border-primary",{active:t.tabIndex===(l==null?void 0:l.value),"text-primary border-b-2 border-primary":t.tabIndex===(l==null?void 0:l.value),"text-grey-400":t.tabIndex!==(l==null?void 0:l.value)}]),onClick:s[0]||(s[0]=y=>t.setActiveTab(y)),"data-tab-index":l==null?void 0:l.value,role:"tab"},a(l==null?void 0:l.title),11,ls)]))),128))])]),e("div",as,[t.sampleList.length===0?(c(),be(h,{key:0,description:"No Data"})):(c(!0),p(U,{key:1},le(t.sampleList,l=>(c(),p("div",{key:l==null?void 0:l.id},[te(e("div",null,[W(g,{sample:l,costSummary:t.costSummary},null,8,["sample","costSummary"])],512),[[me,t.tabIndex==(l==null?void 0:l.id)]])]))),128))])]))}const is=K(Zt,[["render",ns]]),rs=Q({name:"well-quick-info-modal",components:{SvgIcon:G,HistoryTab:Ue,PropertiesTab:is},props:{isVisible:{type:Boolean,required:!0},close:{type:Function,required:!0},wellId:{type:String,required:!0}},setup(t){const s=de(),$=ye(),I=x([]),b=x("properties"),w=xe(t,"wellId"),h=x(),g=x(!1),l=x(null),y=x(null),k=()=>{t.close(),f()};oe(()=>t.isVisible,i=>{i&&t.wellId&&D(t.wellId)});const o=i=>{const u=i.target;b.value=u.getAttribute("data-tab-index")},D=async i=>{g.value=!0,$.getWellDetails({wellId:i,callback:{onSuccess:u=>{var C,V;const S=(C=u==null?void 0:u.users)==null?void 0:C.filter(L=>{var j;return(j=L.roles)==null?void 0:j.some(M=>M.value===Y.Engineer)}),_=(V=u==null?void 0:u.users)==null?void 0:V.filter(L=>{var j;return(j=L.roles)==null?void 0:j.some(M=>M.value===Y.Supervisor)});h.value={...u,engineers:S,supervisors:_}},onFinish:u=>{g.value=!1}}})},R=()=>{var i,u,S;return Array.isArray((i=h==null?void 0:h.value)==null?void 0:i.dailyReport)?(S=(u=h==null?void 0:h.value)==null?void 0:u.dailyReport)==null?void 0:S.at(-1):null},A=i=>{k(),s.push({path:`/wells/${i}`})},f=()=>{var i,u;h.value=null,(i=l==null?void 0:l.value)==null||i.reset(),(u=y==null?void 0:y.value)==null||u.reset(),I.value=[],b.value="properties"};return{id:w,date:I,wellData:h,tabIndex:b,loading:g,propertiesTabRef:l,isSystemAdmin:z.checkRole(Y.SystemAdmin),setActiveTab:o,numberWithCommas:fe,editWell:A,getLastDailyReport:R,closeModal:k}}}),ds={key:0,class:"fixed top-0 right-0 bottom-0 left-0 overflow-y-scroll bg-dark z-40"},fs={class:"bg-white max-h-11/12 w-11/12 flex flex-col items-center gap-3 mx-auto my-6 p-5 rounded-xl overflow-y-scroll"},cs={class:"flex flex-row h-auto w-full items-center justify-between"},us={class:"text-lg font-bold text-dark"},ps={class:"bg-primary-light flex flex-col gap-3 rounded-lg border border-primary border-dashed px-3 pb-6 font-roboto"},bs={key:0,class:"text-center"},ms={key:1},ys={class:"flex flex-col gap-2 text-gray-800"},gs={key:0,class:"flex flex-row flex-wrap items-center justify-between"},hs={class:"font-light max-w-1/2 text-dark truncate"},ws={class:"flex flex-row flex-wrap items-center justify-between"},vs={class:"font-light max-w-1/2 text-dark truncate"},xs={class:"flex flex-row flex-wrap items-center justify-between"},$s={class:"font-light max-w-1/2 text-dark truncate"},ks={class:"flex flex-row flex-wrap items-center justify-between"},Ss={class:"font-light max-w-1/2 text-dark truncate"},Is={class:"flex flex-row flex-wrap items-center justify-between"},Cs={class:"font-light max-w-1/2 text-dark truncate"},_s={class:"flex flex-row flex-wrap items-center justify-between"},Ds={class:"font-light max-w-1/2 text-dark truncate"},Ws={class:"flex flex-col gap-3 text-gray-800"},js={class:"flex flex-row flex-wrap items-center justify-between"},Rs={class:"font-light max-w-1/2 text-dark truncate"},As={class:"flex flex-row flex-wrap items-center justify-between"},Ls={class:"font-light max-w-1/2 text-dark truncate"},Ms={class:"flex flex-row flex-wrap items-center justify-between"},Ns={class:"font-light max-w-1/2 text-dark truncate"},Fs={class:"flex flex-row flex-wrap items-center justify-between"},Vs={class:"font-light max-w-1/2 text-dark truncate"},Ts={class:"flex flex-row flex-wrap items-center justify-between"},Ps={class:"font-light"},Ys={class:"flex flex-row flex-wrap items-center justify-between"},Os={class:"font-light"},Hs={class:"h-auto w-full pt-3 flex flex-col"},Bs={class:"h-auto w-auto flex flex-row justify-start gap-3",role:"tablist"},Es={class:"nav-item"},zs={class:"nav-item"},Us={key:0,class:"h-auto w-full"},qs={key:0},Js={key:1};function Gs(t,s,$,I,b,w){var y,k,o,D,R,A,f,i,u,S,_,C,V,L,j,M,H,B,E,r,m,n,v,T;const h=F("SvgIcon"),g=F("PropertiesTab"),l=F("HistoryTab");return t.isVisible?(c(),p("div",ds,[e("div",fs,[e("div",cs,[e("h3",us,a(((y=t.wellData)==null?void 0:y.nameOrNo)||""),1),e("span",{class:"cursor-pointer",onClick:s[0]||(s[0]=(...d)=>t.closeModal&&t.closeModal(...d))},[W(h,{icon:"closeModalIcon"})])]),e("div",ps,[t.loading?(c(),p("div",bs,s[3]||(s[3]=[e("div",{class:"spinner-border text-primary",role:"status"},[e("span",{class:"sr-only"},"Loading...")],-1)]))):(c(),p("div",ms,[e("div",ys,[s[10]||(s[10]=e("h3",{class:"text-lg font-bold m-0 text-gray-800 pt-3 pb-2"}," General Information ",-1)),t.isSystemAdmin?(c(),p("div",gs,[s[4]||(s[4]=e("span",{class:"font-bold"},"Company",-1)),e("span",hs,a((o=(k=t.wellData)==null?void 0:k.company)==null?void 0:o.name),1)])):O("",!0),e("div",ws,[s[5]||(s[5]=e("span",{class:"font-bold"},"Customers",-1)),e("span",vs,a((R=(D=t.wellData)==null?void 0:D.customers)==null?void 0:R.map(d=>d==null?void 0:d.customerName).join(", ")),1)]),e("div",xs,[s[6]||(s[6]=e("span",{class:"font-bold"},"Rig Name",-1)),e("span",$s,a((A=t.wellData)==null?void 0:A.rigName),1)]),e("div",ks,[s[7]||(s[7]=e("span",{class:"font-bold"},"Supervisors",-1)),e("span",Ss,a((i=(f=t.wellData)==null?void 0:f.supervisors)==null?void 0:i.map(d=>`${d==null?void 0:d.firstName} ${d==null?void 0:d.lastName}`).join(", ")),1)]),e("div",Is,[s[8]||(s[8]=e("span",{class:"font-bold"},"Engineers",-1)),e("span",Cs,a((S=(u=t.wellData)==null?void 0:u.engineers)==null?void 0:S.map(d=>`${d==null?void 0:d.firstName} ${d==null?void 0:d.lastName}`).join(", ")),1)]),e("div",_s,[s[9]||(s[9]=e("span",{class:"font-bold"},"Planned Depth",-1)),e("span",Ds,a(t.numberWithCommas((_=t.wellData)==null?void 0:_.landingPoint)),1)])]),e("div",Ws,[s[15]||(s[15]=e("h3",{class:"font-bold my-3"},"Last Report",-1)),e("div",js,[s[11]||(s[11]=e("span",{class:"font-bold"},"MD",-1)),e("span",Rs,a((V=(C=t.getLastDailyReport())==null?void 0:C.wellInformation)==null?void 0:V.measuredDepth),1)]),e("div",As,[s[12]||(s[12]=e("span",{class:"font-bold"},"TVG",-1)),e("span",Ls,a((j=(L=t.getLastDailyReport())==null?void 0:L.wellInformation)==null?void 0:j.trueVerticalDepth),1)]),e("div",Ms,[s[13]||(s[13]=e("span",{class:"font-bold"},"Inclination",-1)),e("span",Ns,a((H=(M=t.getLastDailyReport())==null?void 0:M.wellInformation)==null?void 0:H.inclination),1)]),e("div",Fs,[s[14]||(s[14]=e("span",{class:"font-bold"},"Azimuth",-1)),e("span",Vs,a((E=(B=t.getLastDailyReport())==null?void 0:B.wellInformation)==null?void 0:E.azimuth),1)])]),e("div",Ts,[s[16]||(s[16]=e("span",{class:"font-bold"},"Treatment",-1)),e("span",Ps,a((m=(r=t.getLastDailyReport())==null?void 0:r.wellInformation)==null?void 0:m.activity),1)]),e("div",Ys,[s[17]||(s[17]=e("span",{class:"font-bold"},"Notes",-1)),e("span",Os,a((T=(v=(n=t.getLastDailyReport())==null?void 0:n.notes)==null?void 0:v.slice(-1)[0])==null?void 0:T.notes),1)])]))]),e("div",Hs,[e("ul",Bs,[e("li",Es,[e("a",{class:ae(["cursor-pointer font-semibold pt-0 mb-3 text-grey-400 hover:text-primary hover:border-b-2 hover:border-primary",{active:t.tabIndex==="properties","text-primary border-b-2 border-primary":t.tabIndex==="properties","text-grey-400":t.tabIndex!=="properties"}]),"data-bs-toggle":"tab",onClick:s[1]||(s[1]=d=>t.setActiveTab(d)),"data-tab-index":"properties",role:"tab"}," Properties ",2)]),e("li",zs,[e("a",{class:ae(["cursor-pointer font-semibold pt-0 mb-3 text-grey-400 hover:text-primary hover:border-b-2 hover:border-primary",{active:t.tabIndex==="history","text-primary border-b-2 border-primary":t.tabIndex==="history","text-grey-400":t.tabIndex!=="history"}]),"data-bs-toggle":"tab",onClick:s[2]||(s[2]=d=>t.setActiveTab(d)),"data-tab-index":"history",role:"tab"}," History ",2)])])]),t.id?(c(),p("div",Us,[t.tabIndex=="properties"?(c(),p("div",qs,[W(g,{ref:"propertiesTabRef",wellId:t.id},null,8,["wellId"])])):t.tabIndex=="history"?(c(),p("div",Js,[W(l,{closeModal:t.closeModal,wellId:t.id,date:t.date},null,8,["closeModal","wellId","date"])])):O("",!0)])):O("",!0)])])):O("",!0)}const Qs=K(rs,[["render",Gs]]),Ks=Q({name:"wells-overview",components:{SvgIcon:G,WellQuickInfoModal:Qs,BottomTool:We,PageHeader:he,Filter:je},setup(){const t=ye(),s=de(),$=x(!1),I=x(!1),b=x(!1),w=x([]),h=x(null),g=x(""),l=z.isJustEngineer(),y=z.getUserInfo(),k=["Home","Wells"],o=x(!1),D=x({}),R=x("");ne(()=>{C()});const A=r=>{R.value=r,b.value=!b.value},f=()=>{b.value=!b.value},i=(r,m)=>{r.stopPropagation(),s.push({path:`/wells/${m}/daily-report`})},u=(r,m)=>{r.stopPropagation(),s.push({path:`/wells/${m}`})},S=(r,m)=>{r.stopPropagation(),w.value=w.value.map(n=>(n==null?void 0:n.id)===m?{...n,loadingArchive:!0}:n),_(m)},_=async r=>{t.updateWell({id:r,params:{archived:!0},callback:{onSuccess:m=>{re.toast("Archived Successfully!","success","top-right"),C(!1)},onFailure:m=>{var n,v,T,d;re.resultAlert(pe[(v=(n=m==null?void 0:m.response)==null?void 0:n.data)==null?void 0:v.errorCode]||((d=(T=m==null?void 0:m.response)==null?void 0:T.data)==null?void 0:d.message)||(m==null?void 0:m.message)||"Sorry, looks like there are some errors detected, please try again.","error"),w.value=w.value.map(N=>(N==null?void 0:N.id)===r?{...N,loadingArchive:!1}:N)}}})},C=async(r=!0)=>{var n;const m={keyword:g.value.trim(),page:1,limit:200,archived:I.value,companyId:$e()?null:(n=z.getUserInfo())==null?void 0:n.companyId,...D.value};$.value=r,t.getWells({params:m,callback:{onSuccess:v=>{var T;w.value=JSON.parse(JSON.stringify((T=v==null?void 0:v.items)==null?void 0:T.map(d=>{var ce,ue;const N=(ce=d==null?void 0:d.users)==null?void 0:ce.filter(P=>{var q;return(q=P.roles)==null?void 0:q.some(ie=>ie.value===Y.Engineer)}),X=(ue=d==null?void 0:d.users)==null?void 0:ue.filter(P=>{var q;return(q=P.roles)==null?void 0:q.some(ie=>ie.value===Y.Supervisor)});let Z=!1;return z.checkRole(Y.SystemAdmin)||z.checkRole(Y.CompanyAdmin)?Z=!0:(z.checkRole(Y.Engineer)&&(N!=null&&N.find(P=>(P==null?void 0:P.id)===(y==null?void 0:y.id)))&&(Z=!0),z.checkRole(Y.Supervisor)&&(X!=null&&X.find(P=>(P==null?void 0:P.id)===(y==null?void 0:y.id)))&&(Z=!0)),{...d,engineers:N,supervisors:X,canEdit:Z}})))},onFinish:v=>{$.value=!1}}})};return{onFilter:r=>{D.value={...r},C()},toggleFilter:r=>{o.value=r},addNewWell:()=>{s.push({path:"/wells/new"})},toggleQuickInfoModal:A,numberWithCommas:fe,newDailyReport:i,editWell:u,getWells:C,archiveWell:S,getLastDailyReport:r=>{if(r!=null&&r.dailyReport)return r==null?void 0:r.dailyReport[(r==null?void 0:r.dailyReport.length)-1]},searchWell:()=>{C()},toggleArchive:()=>{C()},getReportWithLatestSample:r=>{var m,n,v;return(v=(n=(m=r==null?void 0:r.reportWithLatestSample)==null?void 0:m[0])==null?void 0:n.samples)==null?void 0:v[0]},toggleModal:f,wellQuickInfoModal:h,wellList:w,loading:$,isArchived:I,breadcrumbs:k,search:g,isJustEngineer:l,UserType:Y,isShowFilter:o,isSystemAdmin:z.checkRole(Y.SystemAdmin),isModalVisible:b,selectedWellId:R}}}),Xs={class:"bg-white w-11/12 mx-auto h-auto rounded-xl"},Zs={class:"h-auto w-11/12 flex flex-col gap-6 mx-auto mt-7 px-3 py-4"},eo={class:"px-5 flex flex-col gap-2 w-full"},to={class:"inline-flex items-center cursor-pointer"},so={class:"flex flex-row items-center relative w-full"},oo={class:"absolute top-[0.rem] left-2 z-3"},lo={class:"flex flex-row items-center w-full justify-start gap-4"},ao={class:""},no={key:0,class:"text-center"},io={key:1},ro={key:0,description:"No Data"},fo=["onClick"],co={class:"h-auto w-4/5 py-5 mx-auto flex flex-col gap-2 overflow-y-scroll"},uo={class:"flex flex-row items-center justify-between"},po={class:"text-primary font-semibold"},bo={key:0,class:"bg-danger-light px-3 py-2 rounded-lg font-semibold"},mo={class:"flex flex-col gap-3"},yo={key:0,class:"flex flex-row items-center justify-between pb-3 border-b border-primary-light border-dashed"},go={class:"max-w-1/2 text-dark truncate"},ho={class:"flex flex-row items-center justify-between pb-3 border-b border-primary-light border-dashed"},wo={class:"max-w-1/2 text-dark truncate"},vo={class:"flex flex-row items-center justify-between pb-3 border-b border-primary-light border-dashed"},xo={class:"max-w-1/2 text-dark truncate"},$o={class:"flex flex-row items-center justify-between pb-3 border-b border-primary-light border-dashed"},ko={class:"max-w-1/2 text-dark truncate"},So={class:"flex flex-row items-center justify-between pb-3 border-b border-primary-light border-dashed"},Io={class:"max-w-1/2 text-dark truncate"},Co={class:"flex flex-row items-center justify-between pb-3 border-b border-primary-light border-dashed"},_o={class:"max-w-1/2 text-dark truncate"},Do={class:"flex flex-wrap align-items-center justify-content-center"},Wo={class:"basis-1/2 border border-gray-300 border-dashed rounded p-3"},jo={class:"font-semibold text-gray-700"},Ro={class:"basis-1/2 border border-gray-300 border-dashed rounded p-3"},Ao={class:"font-semibold text-gray-700"},Lo={class:"basis-1/2 border border-gray-300 border-dashed rounded p-3"},Mo={class:"font-semibold text-gray-700"},No={class:"basis-1/2 border border-gray-300 border-dashed rounded p-3"},Fo={class:"font-semibold text-gray-700"},Vo={class:"basis-1/2 border border-gray-300 border-dashed rounded p-3"},To={class:"font-semibold text-gray-700"},Po={class:"flex flex-col text-gray-800"},Yo={class:"truncate"},Oo={class:"flex flex-col text-gray-800"},Ho={class:"truncate-"},Bo={class:"flex flex-row items-center gap-2 absolute top-0 end-0 -translate-y-1/2"},Eo={content:"New Daily Report",placement:"top",effect:"customize"},zo=["onClick"],Uo={key:0,content:"Edit",placement:"top",effect:"customize"},qo=["onClick"],Jo={content:"Export",placement:"top",effect:"customize"},Go={key:1,content:"Archive",placement:"top",effect:"customize"},Qo=["onClick"],Ko={key:0,class:"svg-icon svg-icon-3"},Xo={key:1,class:"spinner-border text-warning",style:{width:"1.35rem",height:"1.35rem"},role:"status"};function Zo(t,s,$,I,b,w){const h=F("PageHeader"),g=F("SvgIcon"),l=F("Filter"),y=F("BottomTool"),k=F("WellQuickInfoModal");return c(),p(U,null,[e("div",null,[W(h,{title:"Home",breadcrumbs:t.breadcrumbs},null,8,["breadcrumbs"])]),e("div",Xs,[e("div",Zs,[e("div",eo,[s[11]||(s[11]=e("h1",{class:"text-gray-900 font-bold text-xl"},"Wells",-1)),e("label",to,[s[7]||(s[7]=e("span",{class:"font-semibold text-gray-400 text-hover-primary"}," Show Archived ",-1)),te(e("input",{class:"hidden peer",type:"checkbox","onUpdate:modelValue":s[0]||(s[0]=o=>t.isArchived=o),onChange:s[1]||(s[1]=(...o)=>t.toggleArchive&&t.toggleArchive(...o))},null,544),[[ke,t.isArchived]]),s[8]||(s[8]=e("div",{class:"ml-2 relative w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600 dark:peer-checked:bg-blue-600"},null,-1))]),e("form",{onSubmit:s[3]||(s[3]=Se((...o)=>t.searchWell&&t.searchWell(...o),["prevent"]))},[e("div",so,[e("span",oo,[W(g,{icon:"searchIcon"})]),e("span",null,[te(e("input",{class:"bg-white h-8 pl-8 pr-2 w-full",placeholder:"Search Wells By Name","onUpdate:modelValue":s[2]||(s[2]=o=>t.search=o),name:"search"},null,512),[[Ie,t.search]])])])],32),e("div",lo,[e("button",{class:ae(["flex flex-row gap-2 rounded-md px-4 py-2",t.isShowFilter?"bg-primary":"bg-grey-300"]),onClick:s[4]||(s[4]=()=>t.toggleFilter(!t.isShowFilter))},[e("span",null,[W(g,{icon:"filterIcon"})]),s[9]||(s[9]=J(" Filter "))],2),t.isJustEngineer?O("",!0):(c(),p("button",{key:0,class:"bg-primary text-white rounded-md px-4 py-2 font-semibold flex flex-row items-center gap-2 justify-between",onClick:s[5]||(s[5]=(...o)=>t.addNewWell&&t.addNewWell(...o))},[e("span",ao,[W(g,{icon:"addIcon"})]),s[10]||(s[10]=e("span",{class:"indicator-label"}," New ",-1))]))])]),te(W(l,{hideFilter:()=>t.toggleFilter(!1),onFilter:t.onFilter},null,8,["hideFilter","onFilter"]),[[me,t.isShowFilter]]),e("div",null,[t.loading?(c(),p("div",no,s[12]||(s[12]=[e("div",{class:"spinner-border text-primary",role:"status"},[e("span",{class:"sr-only"},"Loading...")],-1)]))):(c(),p("div",io,[t.wellList.length===0?(c(),p("div",ro)):(c(!0),p(U,{key:1},le(t.wellList,o=>{var D,R,A,f,i,u,S,_,C,V,L,j,M,H,B,E,r,m;return c(),p("div",{key:o.id,class:"h-auto w-full rounded-xl shadow-lg border-t-2 border-primary relative text-sm my-7 first:mt-0 last:mb-0",onClick:n=>t.toggleQuickInfoModal((o==null?void 0:o.id)||"")},[e("div",co,[e("div",uo,[e("h5",po,a(o==null?void 0:o.nameOrNo),1),o!=null&&o.archived?(c(),p("span",bo,"Archived")):O("",!0)]),e("div",mo,[t.isSystemAdmin?(c(),p("div",yo,[s[13]||(s[13]=e("span",{class:"font-semibold text-gray-700"},"Company Name",-1)),e("span",go,a((D=o==null?void 0:o.company)==null?void 0:D.name),1)])):O("",!0),e("div",ho,[s[14]||(s[14]=e("span",{class:"font-semibold text-gray-700"},"Customer",-1)),e("span",wo,a((R=o==null?void 0:o.customers)==null?void 0:R.map(n=>n==null?void 0:n.customerName).join(", ")),1)]),e("div",vo,[s[15]||(s[15]=e("span",{class:"font-semibold text-gray-700"},"Rig Name",-1)),e("span",xo,a(o==null?void 0:o.rigName),1)]),e("div",$o,[s[16]||(s[16]=e("span",{class:"font-semibold text-gray-700"},"Supervisors",-1)),e("span",ko,a((A=o==null?void 0:o.supervisors)==null?void 0:A.map(n=>`${n==null?void 0:n.firstName} ${n==null?void 0:n.lastName}`).join(", ")),1)]),e("div",So,[s[17]||(s[17]=e("span",{class:"font-semibold text-gray-700"},"Engineers",-1)),e("span",Io,a((f=o==null?void 0:o.engineers)==null?void 0:f.map(n=>`${n==null?void 0:n.firstName} ${n==null?void 0:n.lastName}`).join(", ")),1)]),e("div",Co,[s[18]||(s[18]=e("span",{class:"font-semibold text-gray-700"},"Planned Depth",-1)),e("span",_o,a(`${t.numberWithCommas((o==null?void 0:o.landingPoint)||0)} (ft)`),1)])]),e("div",Do,[e("div",Wo,[e("div",jo,a(`${t.numberWithCommas((u=(i=t.getLastDailyReport(o))==null?void 0:i.wellInformation)==null?void 0:u.measuredDepth)} (ft)`),1),s[19]||(s[19]=e("div",{class:"font-semibold text-primary"},"MD",-1))]),e("div",Ro,[e("div",Ao,a(`${t.numberWithCommas((_=(S=t.getLastDailyReport(o))==null?void 0:S.wellInformation)==null?void 0:_.trueVerticalDepth)} (ft)`),1),s[20]||(s[20]=e("div",{class:"font-semibold text-primary"},"TVD",-1))]),e("div",Lo,[e("div",Mo,a(`${t.numberWithCommas((C=t.getReportWithLatestSample(o))==null?void 0:C.mudWeight)} (ppg)`),1),s[21]||(s[21]=e("div",{class:"font-semibold text-success"},"MW",-1))]),e("div",No,[e("div",Fo,a(`${t.numberWithCommas((L=(V=t.getLastDailyReport(o))==null?void 0:V.wellInformation)==null?void 0:L.inclination)} (deg)`),1),s[22]||(s[22]=e("div",{class:"font-semibold text-success"},"Inclination",-1))]),e("div",Vo,[e("div",To,a(`${t.numberWithCommas((M=(j=t.getLastDailyReport(o))==null?void 0:j.wellInformation)==null?void 0:M.azimuth)} (deg)`),1),s[23]||(s[23]=e("div",{class:"font-semibold text-success"},"Azimuth",-1))])]),e("div",Po,[s[24]||(s[24]=e("span",{class:"font-semibold"},"Treatment:",-1)),e("span",Yo,a((B=(H=t.getLastDailyReport(o))==null?void 0:H.wellInformation)==null?void 0:B.activity),1)]),e("div",Oo,[s[25]||(s[25]=e("span",{class:"font-semibold"},"Notes:",-1)),e("span",Ho,a((m=(r=(E=t.getLastDailyReport(o))==null?void 0:E.notes)==null?void 0:r.slice(-1)[0])==null?void 0:m.notes),1)])]),e("div",Bo,[e("div",Eo,[e("div",{class:"p-2 rounded-full bg-primary",onClick:n=>{var v;return t.newDailyReport(n,((v=o==null?void 0:o.id)==null?void 0:v.toString())||"")}},[W(g,{icon:"addIcon"})],8,zo)]),o!=null&&o.canEdit?(c(),p("div",Uo,[e("div",{class:"rounded-full p-1.5 bg-primary-light",onClick:n=>{var v;return t.editWell(n,((v=o==null?void 0:o.id)==null?void 0:v.toString())||"")}},[W(g,{icon:"pencilIcon"})],8,qo)])):O("",!0),e("div",Jo,[e("button",{class:"rounded-full p-1.5 bg-primary-light",onClick:s[6]||(s[6]=n=>{n.stopPropagation()})},[W(g,{icon:"exportIcon"})])]),o.archived?O("",!0):(c(),p("div",Go,[e("button",{class:"rounded-full p-1.5 bg-primary-light",onClick:n=>{var v;return t.archiveWell(n,((v=o==null?void 0:o.id)==null?void 0:v.toString())||"")}},[o!=null&&o.loadingArchive?(c(),p("div",Xo,s[26]||(s[26]=[e("span",{class:"sr-only"},"Loading...",-1)]))):(c(),p("span",Ko,[W(g,{icon:"archiveIcon"})]))],8,Qo)]))])],8,fo)}),128))]))])])]),t.isJustEngineer?O("",!0):(c(),be(y,{key:0,addNew:t.addNewWell,showHelpInfo:!1},null,8,["addNew"])),W(k,{isVisible:t.isModalVisible,close:t.toggleModal,wellId:t.selectedWellId},null,8,["isVisible","close","wellId"])],64)}const fl=K(Ks,[["render",Zo]]);export{fl as default};
