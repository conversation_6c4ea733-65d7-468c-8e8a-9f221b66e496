<template>
  <ul
    class="h-auto w-11/12 mx-auto flex flex-wrap items-center justify-start gap-2"
    role="tablist"
  >
    <li class="nav-item" v-for="item in mudPropertiesTabs" :key="item.value">
      <div
        class="whitespace-nowrap cursor-pointer font-semibold px-4 py-3 relative md:ml-0.5 md:-mb-0.5"
        :class="{
          active: tabIndex === item?.value,
          'text-active-dark z-10 md:shadow-lg md:bg-card-background md:rounded-t-xl md:text-active':
            tabIndex === item?.value,
          'text-inactive rounded-t-lg': tabIndex !== item?.value,
        }"
        @click="handleActiveTab($event)"
        :data-tab-index="item.value"
        role="tab"
      >
        {{ item.label }}
      </div>
    </li>
  </ul>
  <div
    class="bg-card-background text-card-text-light w-11/12 mx-auto mb-4 h-auto rounded-xl border-2 border-active-border relative md:-mt-0.5"
    :class="{
      'md:rounded-tl-none': tabIndex === EMudPropertiesTab.Samples,
    }"
  >
    <div class="h-auto w-11/12 flex flex-col gap-6 mx-auto px-3 py-4">
      <component :is="currentComponent" ref="currentChildTab" />
    </div>
  </div>
</template>

<script lang="ts">
import SvgIcon from "@/constants/SvgIcon.vue";
import { EMudPropertiesTab, mudPropertiesTabs } from "@/constants/daily-report";
import AlertService from "@/services/AlertService";
import { computed, defineComponent, ref } from "vue";
import Sample from "./samples/Sample.vue";
import Solid from "./solids/Solid.vue";

interface FormComponent {
  isFormDirty?: () => boolean;
}

const tabComponentMap = {
  [EMudPropertiesTab.Samples]: Sample,
  [EMudPropertiesTab.Solids]: Solid,
};

export default defineComponent({
  name: "daily-report-mud-properties",
  components: {
    SvgIcon,
    Sample,
    Solid,
  },
  props: {
    setChildActiveTab: {
      type: Function,
      required: false,
      default: () => {},
    },
  },
  setup(props) {
    const loading = ref<boolean>(false);
    const tabIndex = ref<EMudPropertiesTab>(EMudPropertiesTab.Samples);
    const currentChildTab = ref<FormComponent | null>(null);

    const currentComponent = computed(() => tabComponentMap[tabIndex.value]);

    const setActiveTab = (e: Event) => {
      const target = e.target as HTMLInputElement;
      tabIndex.value = Number(target.getAttribute("data-tab-index"));
      if (props?.setChildActiveTab) {
        props.setChildActiveTab(tabIndex.value);
      }
    };

    const handleActiveTab = async (e: Event) => {
      if (tabIndex.value !== EMudPropertiesTab.Solids) {
        setActiveTab(e);
      } else {
        if (isFormOfChildTabDirty()) {
          AlertService.incompleteFormAlert(
            {
              onConfirmed: () => {
                setActiveTab(e);
              },
            },
            "You have unsaved changes. Are you sure you want to leave?"
          );
        } else {
          setActiveTab(e);
        }
      }
    };

    const isFormOfChildTabDirty = () => {
      if (currentChildTab?.value && currentChildTab?.value?.isFormDirty) {
        return currentChildTab?.value?.isFormDirty();
      }

      return false;
    };

    return {
      mudPropertiesTabs,
      EMudPropertiesTab,
      currentComponent,
      currentChildTab,
      tabIndex,
      loading,
      handleActiveTab,
      isFormOfChildTabDirty,
    };
  },
});
</script>
