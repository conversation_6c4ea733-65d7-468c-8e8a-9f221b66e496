<template>
  <div v-if="loading" class="text-center">
    <div class="spinner-border text-link hover:text-link-hover" role="status">
      <span class="sr-only">Loading...</span>
    </div>
  </div>
  <div
    v-else-if="historyList.length === 0"
    class="h-20 w-full flex flex-row items-center justify-center text-dark font-bold"
  >
    No Data
  </div>
  <div v-else class="h-auto w-full flex flex-col gap-4 md:text-lg">
    <el-date-picker
      v-model="date"
      type="daterange"
      range-separator="-"
      start-placeholder="From"
      end-placeholder="To"
      format="MM/DD/YYYY"
    />
    <div v-for="item in historyList" :key="item.id">
      <div
        class="flex flex-row items-center gap-2 text-sm md:text-lg md:justify-center"
      >
        <span class="bg-history-tab-list-marker h-[40px] w-3 mr-4"></span>
        <div class="flex flex-col">
          <h4 class="font-bold">
            {{
              `Daily Report ${formatDate(item?.createdAt, "MM/DD/YYYY hh:mm")}`
            }}
            <span class="font-semibold text-">
              {{ formatDate(item?.createdAt, "a") }}</span
            >
          </h4>

          <span class="font-semibold">
            Created by
            <span class="text-link hover:text-link-hover mr-2">{{
              `${item?.createdBy?.firstName || ""} ${
                item?.createdBy?.lastName || ""
              }`
            }}</span>
            Last updated by
            <span class="text-link hover:text-link-hover">{{
              `${item?.updatedBy?.firstName || ""} ${
                item?.updatedBy?.lastName || ""
              }`
            }}</span>
            at
            <span class="text-uppercase">{{
              formatDate(item?.updatedAt, "MM/DD/YYYY hh:mm a")
            }}</span>
          </span>
        </div>
        <button
          class="bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover h-8 w-8 rounded-lg pl-[7.5px] pt-[1px] md:h-10 md:w-10 md:pl-[6.52px] md:pt-[2px]"
          @click="() => editReport(item?.id)"
        >
          <SvgIcon icon="pencilIcon" classname="md:h-7 md:w-7" />
        </button>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import SvgIcon from "@/constants/SvgIcon.vue";
import { formatDate } from "@/utils/date";
import { useDailyReportStore } from "@/stores/daily-report";
import { defineComponent, onMounted, ref, watch } from "vue";
import { useRouter } from "vue-router";

export default defineComponent({
  name: "history-tab",
  props: {
    closeModal: {
      type: Function,
      required: true,
    },
    wellId: {
      type: String,
      required: true,
    },
    date: {
      type: Array<any>,
      required: true,
    },
  },
  components: { SvgIcon },
  setup(props) {
    const router = useRouter();
    const dailyReportStore = useDailyReportStore();
    const historyList = ref<any>([]);
    const loading = ref(false);

    onMounted(() => {
      if (props?.wellId) {
        getDailyReport();
      }
    });

    watch(
      () => props?.wellId,
      () => {
        if (props?.wellId) {
          getDailyReport();
        }
      }
    );

    watch(
      () => props?.date,
      () => {
        if (props?.wellId) {
          getDailyReport();
        }
      }
    );

    const getDailyReport = async (): Promise<void> => {
      const params = {
        wellId: props?.wellId,
        fromDate:
          props?.date?.length > 0
            ? formatDate(props?.date[0], "YYYY-MM-DD")
            : null,
        toDate:
          props?.date?.length > 0
            ? formatDate(props?.date[1], "YYYY-MM-DD")
            : null,
        page: 1,
        limit: 500,
      };
      loading.value = true;

      dailyReportStore.getDailyReports({
        params: params,
        callback: {
          onSuccess: (res: any) => {
            historyList.value = res?.items;
          },
          onFinish: (_err: any) => {
            loading.value = false;
          },
        },
      });
    };

    const editReport = (dailyReportId: string) => {
      router.push({
        path: `/wells/${props.wellId}/daily-report/${dailyReportId}`,
      });
      props.closeModal();
    };

    const reset = () => {
      historyList.value = [];
    };

    return {
      loading,
      historyList,
      editReport,
      formatDate,
      reset,
    };
  },
});
</script>

<!-- <style>
.el-date-editor {
  padding: 0;
  max-width: 100%;
  margin: 0 auto;
}

.el-picker-panel {
  max-width: 100%;
  overflow-x: hidden;
}
#el-popper-container-2333 {
  overflow-x: scroll;
}
</style> -->
