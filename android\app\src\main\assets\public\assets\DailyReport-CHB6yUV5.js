import{u as gt,a as bt,B as ce,s as tt,f as lt,b as at}from"./sample-BHLFtNnQ.js";import{d as J,q as v,s as rl,v as dl,P as g,_ as Q,m as te,o as w,w as p,r as k,b as i,B as F,A as Y,x as ae,j as re,i as le,c as $,l as E,a as t,t as D,p as ie,F as O,k as Z,E as se,O as ne,Q as V,J as De,U as Ze,T as ht,W as vt,R as ot,n as be,S as it,H as yt,M as wt,h as et,X as ul}from"./index-BmHWvWFS.js";import{P as pl}from"./PageHeader-Sj9hJFB8.js";import{S as K}from"./SvgIcon-DYvlNVZf.js";import{a as pe,b as Ve,g as cl,m as fl,c as ge,d as ml}from"./daily-report-BYo3Yy2S.js";import{f as ke,h as gl,a as St,i as bl}from"./date-CKteeARj.js";import{n as fe}from"./numberFormatter-C7uP7NWj.js";import{u as hl,a as vl}from"./product-pmYNrpK1.js";import{u as $t}from"./well-CQZaoUoH.js";import{h as H}from"./handleFailure-WBgBpurp.js";import{s as xt}from"./navigation-guard-XGAxdHcH.js";import{u as yl}from"./user-CVSNmFaf.js";import{T as wl}from"./TablePagination-lslz6s2e.js";const Sl=J({__name:"DailyReportProvide",setup(l){const e=v("");return dl("dailyReport",{createDailyReport:({wellId:a,callback:o})=>{const r=g.get(o,"onSuccess",g.noop);e.value?r(e.value):gt().createDailyReport({wellId:a,callback:{onSuccess:s=>{e.value=(s==null?void 0:s.id)||"",r(s==null?void 0:s.id)}}})},resetDailyReportId:()=>{e.value=""},getDailyReportId:()=>e.value,setDailyReportId:a=>{e.value=a}}),(a,o)=>rl(a.$slots,"default")}}),$l=J({name:"no-entries",components:{},props:{addNew:{type:Function,default:()=>{},required:!0}},setup(){return{}}});function xl(l,e,S,h,m,n){const a=k("el-button"),o=k("el-empty");return w(),te(o,null,{default:p(()=>[i(a,{onClick:l.addNew,type:"primary",description:"No entries"},{default:p(()=>e[0]||(e[0]=[F("Click to add new")])),_:1},8,["onClick"])]),_:1})}const me=Q($l,[["render",xl]]),kl=J({name:"cost-modal",components:{SvgIcon:K},props:{isVisible:{type:Boolean,required:!0},close:{type:Function,required:!0},loadPage:{type:Function,default:()=>{},required:!0}},setup(l){const e=re(),S=$t(),h=hl(),m=bt(),n=v(!1),a=v({costSettingId:"",unit:null,quantity:null}),o=v(null),r=v(!1),d=v(!1),s=v(""),u=v([]),f=v(""),c=le("dailyReport");Y(s,A=>{A!==""&&(P(),f.value&&M())}),Y(()=>l.isVisible,A=>{var _;A===!1&&(s.value="",B(),(_=o==null?void 0:o.value)==null||_.resetFields())}),ae(()=>{y()});const y=async()=>{S.getWellDetails({wellId:e.params.id,callback:{onSuccess:A=>{var _;f.value=(_=A==null?void 0:A.company)==null?void 0:_.id,M()},onFinish:A=>{r.value=!1}}})},M=async()=>{f.value&&(d.value=!0,h.getCostSettings({params:{page:1,limit:200,companyId:f.value},callback:{onSuccess:A=>{var _;u.value=(_=A==null?void 0:A.items)==null?void 0:_.map(N=>({value:N==null?void 0:N.id,label:N==null?void 0:N.name}))},onFinish:A=>{d.value=!1}}}))},P=async()=>{m.getCostDetails({id:s.value,callback:{onSuccess:A=>{var _;a.value={...A,costSettingId:(_=A==null?void 0:A.costSetting)==null?void 0:_.id}}}})},x=async A=>{r.value=!0,m.updateCost({id:s.value,params:A,callback:{onSuccess:_=>{l!=null&&l.loadPage&&(l==null||l.loadPage()),l.close()},onFinish:_=>{r.value=!1}}})},b=async A=>{r.value=!0,m.createCost({params:A,callback:{onSuccess:_=>{l!=null&&l.loadPage&&(l==null||l.loadPage()),l.close()},onFinish:_=>{r.value=!1}}})},T=()=>{l.close()},q=A=>{s.value=A.toString()},I=v({costSettingId:[{required:!0,message:"Please select Description",trigger:"change"}],unit:[{required:!0,message:"Please type Unit",trigger:"blur"}],quantity:[{required:!0,message:"Please type Quantity",trigger:"blur"}]}),C=()=>{o.value&&o.value.validate(A=>{var _,N,j;if(A){const z={...a==null?void 0:a.value,unit:Number((_=a==null?void 0:a.value)==null?void 0:_.unit),quantity:Number((N=a==null?void 0:a.value)==null?void 0:N.quantity)};s!=null&&s.value?x({...z,dailyReportId:c==null?void 0:c.getDailyReportId()}):c==null||c.createDailyReport({wellId:(j=e==null?void 0:e.params)==null?void 0:j.id,callback:{onSuccess:R=>{b({...z,dailyReportId:c==null?void 0:c.getDailyReportId()})}}})}})},B=()=>{a.value={costSettingId:"",unit:null,quantity:null}};return{id:s,modal:n,rules:I,loading:r,targetData:a,formRef:o,loadingDescriptionList:d,descriptionOptions:u,closeModal:T,submit:C,setId:q,reset:B}}}),Dl={key:0,class:"fixed top-0 right-0 bottom-0 left-0 z-40 flex items-center"},Vl={class:"relative bg-card-background text-card-text-light max-h-11/12 w-11/12 flex flex-col items-center gap-3 mx-auto my-6 p-5 rounded-xl md:w-3/5 lg:w-2/5 lg:h-auto"},Il={class:"flex flex-row h-auto w-full items-center justify-between"},Pl={class:"text-lg font-bold"},Cl={class:"flex flex-col gap-1"},Tl={class:"flex flex-col gap-1"},Ml={class:"flex flex-col gap-1"},Fl={class:"h-auto w-full flex flex-row items-center gap-2"},Nl=["disabled"],_l=["disabled"],Hl={key:0,class:"indicator-label"},Al={key:1,class:"indicator-progress"};function zl(l,e,S,h,m,n){const a=k("SvgIcon"),o=k("el-option"),r=k("el-select"),d=k("el-form-item"),s=k("el-input"),u=k("el-form");return l.isVisible?(w(),$("div",Dl,[e[9]||(e[9]=t("div",{class:"absolute inset-0 bg-sidebar-background opacity-25"},null,-1)),t("div",Vl,[t("div",Il,[t("h3",Pl,D(`${l.id?"Edit Cost":"New Cost"}`),1),t("span",{class:"cursor-pointer",onClick:e[0]||(e[0]=(...f)=>l.closeModal&&l.closeModal(...f))},[i(a,{icon:"closeModalIcon"})])]),i(u,{id:"cost_form",onSubmit:ie(l.submit,["prevent"]),model:l.targetData,rules:l.rules,ref:"formRef",class:"h-auto w-full flex flex-col gap-3"},{default:p(()=>[t("div",Cl,[e[5]||(e[5]=t("label",{class:"font-bold"},[F("Description"),t("span",{class:"text-danger-active font-light"},"*")],-1)),i(d,{prop:"costSettingId",class:"mt-auto"},{default:p(()=>[i(r,{modelValue:l.targetData.costSettingId,"onUpdate:modelValue":e[1]||(e[1]=f=>l.targetData.costSettingId=f),placeholder:"Select Description",clearable:"",loading:l.loadingDescriptionList},{default:p(()=>[(w(!0),$(O,null,Z(l.descriptionOptions,f=>(w(),te(o,{key:f==null?void 0:f.value,label:f==null?void 0:f.label,value:f==null?void 0:f.value,name:"costSettingId"},null,8,["label","value"]))),128))]),_:1},8,["modelValue","loading"])]),_:1})]),t("div",Tl,[e[6]||(e[6]=t("label",{class:"font-bold"},[F("Unit"),t("span",{class:"text-danger-active font-light"},"*")],-1)),i(d,{prop:"unit"},{default:p(()=>[i(s,{type:"number",controls:!1,step:"any",modelValue:l.targetData.unit,"onUpdate:modelValue":e[2]||(e[2]=f=>l.targetData.unit=f),placeholder:"",name:"unit"},null,8,["modelValue"])]),_:1})]),t("div",Ml,[e[7]||(e[7]=t("label",{class:"font-bold"},[F(" Quantity"),t("span",{class:"text-danger-active font-light"},"*")],-1)),i(d,{prop:"quantity"},{default:p(()=>[i(s,{type:"number",controls:!1,step:"any",modelValue:l.targetData.quantity,"onUpdate:modelValue":e[3]||(e[3]=f=>l.targetData.quantity=f),placeholder:"",name:"quantity"},null,8,["modelValue"])]),_:1})]),t("div",Fl,[t("button",{type:"button",class:"bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover rounded-md px-4 py-2 font-semibold",onClick:e[4]||(e[4]=(...f)=>l.closeModal&&l.closeModal(...f)),disabled:l.loading}," Discard ",8,Nl),t("button",{class:"bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover rounded-md px-4 py-2 font-semibold",type:"submit",disabled:l.loading},[l.loading?E("",!0):(w(),$("span",Hl," Save ")),l.loading?(w(),$("span",Al,e[8]||(e[8]=[F(" Please wait... "),t("span",{class:"spinner-border spinner-border-sm align-middle ms-2"},null,-1)]))):E("",!0)],8,_l)])]),_:1},8,["onSubmit","model","rules"])])])):E("",!0)}const ql=Q(kl,[["render",zl]]),Bl=J({name:"costs",components:{SvgIcon:K,BottomTool:ce,CostModal:ql,NoEntries:me},props:{setChildActiveTab:{type:Function,required:!1,default:()=>{}}},setup(l){const e=bt(),S=v(null),h=v(!1),m=v([]),n=le("dailyReport"),a=v(!1);ae(()=>{n!=null&&n.getDailyReportId()&&o()});const o=async(f={dailyReportId:n==null?void 0:n.getDailyReportId(),page:1,limit:200})=>{h.value=!0,e.getCosts({params:f,callback:{onSuccess:c=>{m.value=JSON.parse(JSON.stringify(c==null?void 0:c.items))},onFinish:c=>{h.value=!1}}})},r=()=>{a.value=!a.value},d=f=>{var c;(c=S==null?void 0:S.value)==null||c.setId(f),a.value=!a.value},s=f=>{se.deletionAlert({onConfirmed:()=>{u(f)}})},u=f=>{h.value=!0,e.deleteCost({id:f,callback:{onSuccess:c=>{o()},onFinish:c=>{h.value=!1}}})};return{loading:h,costList:m,costModal:S,isModalVisible:a,numberWithCommas:fe,toggleAddCostModal:r,toggleEditCostModal:d,deleteCost:s,formatDate:ke,getCosts:o}}}),jl={class:"bg-card-background text-card-text-light w-11/12 mx-auto mb-4 h-auto rounded-xl"},El={class:"h-auto w-11/12 flex flex-col gap-6 mx-auto mt-7 px-3 py-4"},Rl={key:0,class:"text-center"},Ol={key:1},Ll={key:1,class:"h-auto w-full grid grid-cols-1 gap-4 md:grid-cols-2 md:gap-7 lg:grid-cols-3"},Wl={class:"h-auto w-full p-4 flex flex-col gap-2"},Ul={class:"font-bold"},Gl={class:"flex items-center justify-between border-dashed border-b-[1px] py-3"},Yl={class:"text-minicard-text-dark"},Jl={class:"flex items-center justify-between border-dashed border-b-[1px] py-3"},Ql={class:"text-minicard-text-dark"},Kl={class:"flex items-center justify-between border-dashed border-b-[1px] py-3"},Xl={class:"text-minicard-text-dark"},Zl={class:"flex items-center justify-between pt-3"},eo={class:"text-minicard-text-dark"},to={class:"flex flex-row items-center gap-2 absolute top-0 end-0 -translate-y-1/2"},lo=["onClick"],oo={class:"svg-icon svg-icon-3"},so=["onClick"],no={class:"svg-icon svg-icon-3 text-danger"};function ao(l,e,S,h,m,n){const a=k("NoEntries"),o=k("SvgIcon"),r=k("BottomTool"),d=k("CostModal");return w(),$(O,null,[t("div",jl,[t("div",El,[e[5]||(e[5]=t("h1",{class:"font-bold"},"Costs",-1)),l.loading?(w(),$("div",Rl,e[0]||(e[0]=[t("div",{class:"spinner-border text-primary",role:"status"},[t("span",{class:"sr-only"},"Loading...")],-1)]))):(w(),$("div",Ol,[l.costList.length===0?(w(),te(a,{key:0,addNew:l.toggleAddCostModal},null,8,["addNew"])):(w(),$("div",Ll,[(w(!0),$(O,null,Z(l.costList,s=>{var u,f;return w(),$("div",{key:s==null?void 0:s.id,class:"relative h-auto w-full bg-minicard-background text-minicard-text-light p-4 rounded-xl border-t-2 border-active my-2 first:mt-4 last:mb-5 font-semibold md:first:mt-0 md:last:mb-0 md:m-0"},[t("div",Wl,[t("h5",Ul,D((u=s==null?void 0:s.costSetting)==null?void 0:u.name),1),t("div",Gl,[e[1]||(e[1]=t("span",null,"Created Date",-1)),t("span",Yl,D(`${l.formatDate(s==null?void 0:s.createdAt,"DD MMM YYYY")}`),1)]),t("div",Jl,[e[2]||(e[2]=t("span",null,"Unit",-1)),t("span",Ql,D(s==null?void 0:s.unit),1)]),t("div",Kl,[e[3]||(e[3]=t("span",null,"Quantity",-1)),t("span",Xl,D(l.numberWithCommas(s==null?void 0:s.quantity)),1)]),t("div",Zl,[e[4]||(e[4]=t("span",null,"Cost",-1)),t("span",eo,D(l.numberWithCommas((f=s==null?void 0:s.costSetting)==null?void 0:f.cost)),1)])]),t("div",to,[t("button",{class:"p-2 rounded-full bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover",onClick:c=>l.toggleEditCostModal(s==null?void 0:s.id)},[t("span",oo,[i(o,{icon:"pencilIcon"})])],8,lo),t("button",{class:"p-2 rounded-full bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover",onClick:c=>l.deleteCost(s==null?void 0:s.id)},[t("span",no,[i(o,{icon:"trashIcon"})])],8,so)])])}),128))]))]))])]),i(r,{addNew:l.toggleAddCostModal,showHelpInfo:!1},null,8,["addNew"]),i(d,{isVisible:l.isModalVisible,close:l.toggleAddCostModal,ref:"costModal",loadPage:l.getCosts},null,8,["isVisible","close","loadPage"])],64)}const kt=Q(Bl,[["render",ao]]),Dt=ne("drillBit",()=>({getDrillBits:async({params:n,callback:a})=>{var d;const o=g.get(a,"onSuccess",g.noop),r=g.get(a,"onFinish",g.noop);try{V.setHeader();const s=await V.getWithParams("drillBits",n);o(((d=s.data)==null?void 0:d.data)||s.data)}catch(s){H(s,a)}finally{r()}},getDrillBitDetails:async({id:n,callback:a})=>{var d;const o=g.get(a,"onSuccess",g.noop),r=g.get(a,"onFinish",g.noop);try{V.setHeader();const s=await V.get(`drillBits/${n}`);o(((d=s.data)==null?void 0:d.data)||s.data)}catch(s){H(s,a)}finally{r()}},updateDrillBit:async({id:n,params:a,callback:o})=>{var s;const r=g.get(o,"onSuccess",g.noop),d=g.get(o,"onFinish",g.noop);try{V.setHeader();const u=await V.put(`drillBits/${n}`,a);r(((s=u.data)==null?void 0:s.data)||u.data)}catch(u){H(u,o)}finally{d()}},deleteDrillBit:async({id:n,callback:a})=>{var d;const o=g.get(a,"onSuccess",g.noop),r=g.get(a,"onFinish",g.noop);try{V.setHeader();const s=await V.delete(`drillBits/${n}`);o(((d=s.data)==null?void 0:d.data)||s.data)}catch(s){H(s,a)}finally{r()}},createDrillBit:async({params:n,callback:a})=>{var d;const o=g.get(a,"onSuccess",g.noop),r=g.get(a,"onFinish",g.noop);try{V.setHeader();const s=await V.post("drillBits",n);o(((d=s.data)==null?void 0:d.data)||s.data)}catch(s){H(s,a)}finally{r()}}})),io=J({name:"bit-modal",components:{SvgIcon:K},props:{isVisible:{type:Boolean,required:!0},close:{type:Function,required:!0},loadPage:{type:Function,default:()=>{},required:!0}},setup(l){const e=re(),S=Dt(),h=v(!1),m=v({bitNo:"",type:"",iadcType:"",bitSize:null,depth:null,bitRunDuration:null}),n=v(null),a=v(!1),o=v(""),r=le("dailyReport");Y(o,x=>{console.log("id",x),x!==""&&d()}),Y(()=>l.isVisible,x=>{var b;x===!1&&(o.value="",P(),(b=n==null?void 0:n.value)==null||b.resetFields())});const d=async()=>{S.getDrillBitDetails({id:o.value,callback:{onSuccess:x=>{m.value=x,console.log("targetData",m.value)}}})},s=async x=>{a.value=!0,S.updateDrillBit({id:o.value,params:x,callback:{onSuccess:b=>{l!=null&&l.loadPage&&(l==null||l.loadPage()),l.close()},onFinish:b=>{a.value=!1}}})},u=async x=>{a.value=!0,S.createDrillBit({params:x,callback:{onSuccess:b=>{l!=null&&l.loadPage&&(l==null||l.loadPage()),l.close()},onFinish:b=>{a.value=!1}}})},f=()=>{l.close()},c=x=>{o.value=x.toString()},y=v({bitNo:[{required:!0,message:"Please type Bit No.",trigger:"blur"}]}),M=()=>{n.value&&n.value.validate(x=>{var b,T,q,I,C,B,A,_,N;if(x){const j={bitNo:(b=m==null?void 0:m.value)==null?void 0:b.bitNo,type:(T=m==null?void 0:m.value)==null?void 0:T.type,iadcType:(q=m==null?void 0:m.value)==null?void 0:q.iadcType,bitSize:Number((I=m==null?void 0:m.value)==null?void 0:I.bitSize),depth:(C=m==null?void 0:m.value)!=null&&C.depth?Number((B=m==null?void 0:m.value)==null?void 0:B.depth):null,bitRunDuration:(A=m==null?void 0:m.value)!=null&&A.bitRunDuration?Number((_=m==null?void 0:m.value)==null?void 0:_.bitRunDuration):null};o!=null&&o.value?s({...j,dailyReportId:r==null?void 0:r.getDailyReportId()}):r==null||r.createDailyReport({wellId:(N=e==null?void 0:e.params)==null?void 0:N.id,callback:{onSuccess:z=>{u({...j,dailyReportId:z})}}})}})},P=()=>{m.value={bitNo:"",type:"",iadcType:"",bitSize:null,depth:null,bitRunDuration:null}};return{id:o,modal:h,rules:y,loading:a,targetData:m,formRef:n,closeModal:f,submit:M,setId:c,reset:P}}}),ro={key:0,class:"fixed top-0 right-0 bottom-0 left-0 z-40 flex items-center"},uo={class:"relative bg-card-background text-card-text-light max-h-11/12 w-11/12 flex flex-col items-center gap-3 mx-auto my-6 p-5 rounded-xl overflow-y-scroll md:overflow-hidden md:w-4/5 md:text-lg lg:w-2/5 lg:h-auto"},po={class:"h-auto w-full flex flex-row items-center justify-between"},co={class:"text-lg font-bold"},fo={class:"h-auto w-full flex flex-col gap-3 md:flex-row md:gap-4"},mo={class:"h-auto w-full flex flex-col gap-3"},go={class:"h-auto w-full flex flex-col gap-1"},bo={class:"font-bold"},ho={class:"h-auto w-full flex flex-col gap-1"},vo={class:"font-bold"},yo={class:"h-auto w-full flex flex-col gap-1"},wo={class:"font-bold"},So={class:"h-auto w-full flex flex-col gap-3"},$o={class:"h-auto w-full flex flex-col gap-1"},xo={class:"font-bold"},ko={class:"h-auto w-full flex flex-col gap-1"},Do={class:"font-bold"},Vo={class:"h-auto w-full flex flex-col gap-1"},Io={class:"font-bold"},Po={class:"h-auto w-full flex flex-row items-center gap-2"},Co=["disabled"],To=["disabled"],Mo={key:0,class:"indicator-label"},Fo={key:1,class:"indicator-progress"};function No(l,e,S,h,m,n){const a=k("SvgIcon"),o=k("el-popover"),r=k("el-input"),d=k("el-form-item"),s=k("el-form");return l.isVisible?(w(),$("div",ro,[e[28]||(e[28]=t("div",{class:"absolute inset-0 bg-sidebar-background opacity-25"},null,-1)),t("div",uo,[t("div",po,[t("h3",co,D(`${l.id?"Edit Bit":"New Bit"}`),1),t("span",{class:"cursor-pointer",onClick:e[0]||(e[0]=(...u)=>l.closeModal&&l.closeModal(...u))},[i(a,{icon:"closeModalIcon"})])]),i(s,{id:"bit_form",onSubmit:ie(l.submit,["prevent"]),model:l.targetData,rules:l.rules,ref:"formRef",class:"h-auto w-full"},{default:p(()=>[t("div",fo,[t("div",mo,[t("div",go,[t("label",bo,[e[10]||(e[10]=F(" Bit No. ")),e[11]||(e[11]=t("span",{class:"text-danger-active font-light"},"*",-1)),i(o,{placement:"top",width:300,trigger:"hover"},{reference:p(()=>e[8]||(e[8]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7"},null,-1)])),default:p(()=>[e[9]||(e[9]=t("span",null,'The "Bit No." is a unique identifier or serial number assigned to the drill bit. It helps track the usage and performance history of individual bits. ',-1))]),_:1})]),i(d,{prop:"bitNo",class:"mt-auto"},{default:p(()=>[i(r,{modelValue:l.targetData.bitNo,"onUpdate:modelValue":e[1]||(e[1]=u=>l.targetData.bitNo=u),placeholder:"",name:"bitNo"},null,8,["modelValue"])]),_:1})]),t("div",ho,[t("label",vo,[e[14]||(e[14]=F("Type")),i(o,{placement:"top",width:300,trigger:"hover"},{reference:p(()=>e[12]||(e[12]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7"},null,-1)])),default:p(()=>[e[13]||(e[13]=t("span",null,`The "Type" of drill bit refers to the specific design and characteristics of the bit. It can include information about the bit's cutting structure, materials, and intended use. Examples of types include PDC (Polycrystalline Diamond Compact), roller cone, diamond-impregnated, and others. `,-1))]),_:1})]),i(d,{prop:"type"},{default:p(()=>[i(r,{modelValue:l.targetData.type,"onUpdate:modelValue":e[2]||(e[2]=u=>l.targetData.type=u),placeholder:"",name:"bitNo"},null,8,["modelValue"])]),_:1})]),t("div",yo,[t("label",wo,[e[17]||(e[17]=F("IADC Type")),i(o,{placement:"top",width:300,trigger:"hover"},{reference:p(()=>e[15]||(e[15]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7"},null,-1)])),default:p(()=>[e[16]||(e[16]=t("span",null,"Bit Type (IADC Classification): The IADC (International Association of Drilling Contractors) classification code, if applicable, which provides standardized codes for various drill bit types. ",-1))]),_:1})]),i(d,{prop:"iadcType",class:"mt-auto"},{default:p(()=>[i(r,{modelValue:l.targetData.iadcType,"onUpdate:modelValue":e[3]||(e[3]=u=>l.targetData.iadcType=u),placeholder:"",name:"iadcType"},null,8,["modelValue"])]),_:1})])]),t("div",So,[t("div",$o,[t("label",xo,[e[20]||(e[20]=F("Bit Size (inches)")),i(o,{placement:"top",width:300,trigger:"hover"},{reference:p(()=>e[18]||(e[18]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7"},null,-1)])),default:p(()=>[e[19]||(e[19]=t("span",null,"The size of the drill bit, often specified by its diameter in inches or millimeters. Bit size affects the hole diameter and drilling efficiency. ",-1))]),_:1})]),i(d,{prop:"bitSize"},{default:p(()=>[i(r,{type:"number",class:"w-100",controls:!1,step:"any",modelValue:l.targetData.bitSize,"onUpdate:modelValue":e[4]||(e[4]=u=>l.targetData.bitSize=u),placeholder:"",name:"bitSize"},null,8,["modelValue"])]),_:1})]),t("div",ko,[t("label",Do,[e[23]||(e[23]=F("Depth")),i(o,{placement:"top",width:300,trigger:"hover"},{reference:p(()=>e[21]||(e[21]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7"},null,-1)])),default:p(()=>[e[22]||(e[22]=t("span",null,'The "Depth" represents the depth at which the drill bit is used or the depth at which it was pulled out of the wellbore. It is typically measured in feet or meters. ',-1))]),_:1})]),i(d,{prop:"depth",class:"mt-auto"},{default:p(()=>[i(r,{type:"number",class:"w-100",controls:!1,step:"any",modelValue:l.targetData.depth,"onUpdate:modelValue":e[5]||(e[5]=u=>l.targetData.depth=u),placeholder:"",name:"depth"},null,8,["modelValue"])]),_:1})]),t("div",Vo,[t("label",Io,[e[26]||(e[26]=F("Bit Run Duration")),i(o,{placement:"top",width:300,trigger:"hover"},{reference:p(()=>e[24]||(e[24]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7"},null,-1)])),default:p(()=>[e[25]||(e[25]=t("span",null,"The amount of time or footage the drill bit was used before it was pulled out of the hole. It helps assess the bit's performance and longevity. ",-1))]),_:1})]),i(d,{prop:"bitRunDuration"},{default:p(()=>[i(r,{type:"number",class:"w-100",controls:!1,step:"any",modelValue:l.targetData.bitRunDuration,"onUpdate:modelValue":e[6]||(e[6]=u=>l.targetData.bitRunDuration=u),placeholder:"",name:"bitRunDuration"},null,8,["modelValue"])]),_:1})])])]),t("div",Po,[t("button",{type:"button",class:"bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover rounded-md px-4 py-2 font-semibold md:px-4 md:py-3 md:text-xl",onClick:e[7]||(e[7]=(...u)=>l.closeModal&&l.closeModal(...u)),disabled:l.loading}," Discard ",8,Co),t("button",{class:"bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover rounded-md px-4 py-2 font-semibold md:px-4 md:py-3 md:text-xl",type:"submit",disabled:l.loading},[l.loading?E("",!0):(w(),$("span",Mo," Save ")),l.loading?(w(),$("span",Fo,e[27]||(e[27]=[F(" Please wait... "),t("span",{class:"spinner-border spinner-border-sm align-middle ms-2"},null,-1)]))):E("",!0)],8,To)])]),_:1},8,["onSubmit","model","rules"])])])):E("",!0)}const _o=Q(io,[["render",No]]),Ho=J({name:"bits",components:{SvgIcon:K,BottomTool:ce,BitModal:_o,NoEntries:me},props:{showCustomize:{type:Function,required:!1}},setup(l){const e=Dt(),S=v(null),h=v(!1),m=v([]),n=le("dailyReport"),a=v(!1);ae(()=>{n!=null&&n.getDailyReportId()&&o()});const o=async(f={dailyReportId:n==null?void 0:n.getDailyReportId(),page:1,limit:200})=>{h.value=!0,e.getDrillBits({params:f,callback:{onSuccess:c=>{m.value=c==null?void 0:c.items},onFinish:c=>{h.value=!1}}})},r=()=>{a.value=!a.value},d=f=>{var c;(c=S==null?void 0:S.value)==null||c.setId(f),a.value=!a.value},s=f=>{se.deletionAlert({onConfirmed:()=>{u(f)}})},u=async f=>{h.value=!0,e.deleteDrillBit({id:f,callback:{onSuccess:c=>{o()},onFinish:c=>{h.value=!1}}})};return{loading:h,bitModal:S,drillBitList:m,isModalVisible:a,getDrillBits:o,numberWithCommas:fe,deleteDrillBit:s,toggleEditBitModal:d,toggleNewBitModal:r}}}),Ao={class:"bits"},zo={class:"row gap-10"},qo={key:0,class:"text-center"},Bo={key:1,class:"grid grid-cols-1 gap-4 md:grid-cols-2 md:gap-7 lg:grid-cols-3"},jo={class:"h-auto w-full flex flex-col gap-3"},Eo={class:"text-xl font-bold"},Ro={class:"flex items-center justify-between border-dashed border-b-[1px] py-3"},Oo={class:"flex items-center justify-between border-dashed border-b-[1px] py-3"},Lo={class:"flex flex-wrap items-center gap-2"},Wo={class:"flex items-center gap-2 border border-gray-300 border-dashed rounded py-3 px-4"},Uo={class:"flex items-center gap-2 border border-gray-300 border-dashed rounded py-3 px-4"},Go={class:"flex items-center gap-2 border border-gray-300 border-dashed rounded py-3 px-4"},Yo={class:"flex flex-row items-center gap-2 absolute top-0 end-0 -translate-y-1/2"},Jo=["onClick"],Qo={class:"svg-icon svg-icon-3"},Ko=["onClick"],Xo={class:"svg-icon svg-icon-3 text-danger"};function Zo(l,e,S,h,m,n){const a=k("NoEntries"),o=k("SvgIcon"),r=k("BottomTool"),d=k("BitModal");return w(),$(O,null,[t("div",Ao,[t("div",zo,[l.loading?(w(),$("div",qo,e[0]||(e[0]=[t("div",{class:"spinner-border text-primary",role:"status"},[t("span",{class:"sr-only"},"Loading...")],-1)]))):(w(),$(O,{key:1},[l.drillBitList.length===0?(w(),te(a,{key:0,addNew:l.toggleNewBitModal},null,8,["addNew"])):(w(),$("div",Bo,[(w(!0),$(O,null,Z(l.drillBitList,s=>(w(),$("div",{key:s.id,class:"relative h-auto w-full bg-minicard-background text-minicard-text-light flex flex-col items-center p-4 rounded-xl border-t-2 border-active my-2 first:mt-3 last:mb-5 font-semibold md:first:mt-0 md:last:mb-0 md:m-0"},[t("div",jo,[t("h5",Eo,D(s.bitNo),1),t("div",Ro,[e[1]||(e[1]=t("span",null,"Type",-1)),t("span",null,D(s.type),1)]),t("div",Oo,[e[2]||(e[2]=t("span",null,"IADC type",-1)),t("span",null,D(s.iadcType),1)]),t("div",Lo,[t("div",Wo,[t("div",null,D(`${l.numberWithCommas(s.bitSize)} (in)`),1),e[3]||(e[3]=t("div",{class:"text-danger"},"Bit Size",-1))]),t("div",Uo,[t("div",null,D(`${l.numberWithCommas(s.depth)} (in)`),1),e[4]||(e[4]=t("div",null,"Depth",-1))]),t("div",Go,[t("div",null,D(`${l.numberWithCommas(s.bitRunDuration)} (ft)`),1),e[5]||(e[5]=t("div",{class:"text-success"},"Bit Run Duration",-1))])])]),t("div",Yo,[t("button",{class:"p-2 rounded-full bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover",onClick:u=>l.toggleEditBitModal(s.id.toString())},[t("span",Qo,[i(o,{icon:"pencilIcon"})])],8,Jo),t("button",{class:"p-2 rounded-full bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover",onClick:u=>l.deleteDrillBit(s.id.toString())},[t("span",Xo,[i(o,{icon:"trashIcon"})])],8,Ko)])]))),128))]))],64))])]),i(r,{addNew:l.toggleNewBitModal,showHelpWindow:l.showCustomize},null,8,["addNew","showHelpWindow"]),i(d,{isVisible:l.isModalVisible,close:l.toggleNewBitModal,ref:"bitModal",loadPage:l.getDrillBits},null,8,["isVisible","close","loadPage"])],64)}const Vt=Q(Ho,[["render",Zo]]),It=ne("casedHole",()=>({getCasedHoles:async({params:n,callback:a})=>{var d;const o=g.get(a,"onSuccess",g.noop),r=g.get(a,"onFinish",g.noop);try{V.setHeader();const s=await V.getWithParams("casedHoles",n);o(((d=s.data)==null?void 0:d.data)||s.data)}catch(s){H(s,a)}finally{r()}},getCasedHoleDetails:async({id:n,callback:a})=>{var d;const o=g.get(a,"onSuccess",g.noop),r=g.get(a,"onFinish",g.noop);try{V.setHeader();const s=await V.get(`casedHoles/${n}`);o(((d=s.data)==null?void 0:d.data)||s.data)}catch(s){H(s,a)}finally{r()}},updateCasedHole:async({id:n,params:a,callback:o})=>{var s;const r=g.get(o,"onSuccess",g.noop),d=g.get(o,"onFinish",g.noop);try{V.setHeader();const u=await V.put(`casedHoles/${n}`,a);r(((s=u.data)==null?void 0:s.data)||u.data)}catch(u){H(u,o)}finally{d()}},deleteCasedHole:async({id:n,callback:a})=>{var d;const o=g.get(a,"onSuccess",g.noop),r=g.get(a,"onFinish",g.noop);try{V.setHeader();const s=await V.delete(`casedHoles/${n}`);o(((d=s.data)==null?void 0:d.data)||s.data)}catch(s){H(s,a)}finally{r()}},createCasedHole:async({params:n,callback:a})=>{var d;const o=g.get(a,"onSuccess",g.noop),r=g.get(a,"onFinish",g.noop);try{V.setHeader();const s=await V.post("casedHoles",n);o(((d=s.data)==null?void 0:d.data)||s.data)}catch(s){H(s,a)}finally{r()}}})),es=J({name:"cased-hole-modal",components:{SvgIcon:K},props:{isVisible:{type:Boolean,required:!0},close:{type:Function,required:!0},loadPage:{type:Function,default:()=>{},required:!0}},setup(l){const e=re(),S=It(),h=v(!1),m=v({description:"",topDepth:null,casingShoeDepth:null,casingLength:null,outsideDiameter:null,insideDiameter:null,weight:null}),n=v(null),a=v(!1),o=v(""),r=le("dailyReport");Y(o,x=>{x!==""&&d()}),Y(()=>l.isVisible,x=>{var b;x===!1&&(o.value="",P(),(b=n==null?void 0:n.value)==null||b.resetFields())});const d=async()=>{S.getCasedHoleDetails({id:o.value,callback:{onSuccess:x=>{m.value=x}}})},s=async x=>{a.value=!0,S.updateCasedHole({id:o.value,params:x,callback:{onSuccess:b=>{l!=null&&l.loadPage&&(l==null||l.loadPage()),l.close()},onFinish:b=>{a.value=!1}}})},u=async x=>{a.value=!0,S.createCasedHole({params:x,callback:{onSuccess:b=>{l!=null&&l.loadPage&&(l==null||l.loadPage()),l.close()},onFinish:b=>{a.value=!1}}})},f=()=>{l.close()},c=x=>{o.value=x.toString()},y=v({description:[{required:!0,message:"Please type Description",trigger:"blur"}]}),M=()=>{n.value&&n.value.validate(x=>{var b,T,q,I,C,B,A,_,N;if(x){const j={description:(b=m==null?void 0:m.value)==null?void 0:b.description,topDepth:(T=m==null?void 0:m.value)!=null&&T.topDepth?Number((q=m==null?void 0:m.value)==null?void 0:q.topDepth):null,casingShoeDepth:Number((I=m==null?void 0:m.value)==null?void 0:I.casingShoeDepth),casingLength:Number((C=m==null?void 0:m.value)==null?void 0:C.casingLength),outsideDiameter:Number((B=m==null?void 0:m.value)==null?void 0:B.outsideDiameter),insideDiameter:Number((A=m==null?void 0:m.value)==null?void 0:A.insideDiameter),weight:Number((_=m==null?void 0:m.value)==null?void 0:_.weight)};o!=null&&o.value?s({...j,dailyReportId:r==null?void 0:r.getDailyReportId()}):r==null||r.createDailyReport({wellId:(N=e==null?void 0:e.params)==null?void 0:N.id,callback:{onSuccess:z=>{u({...j,dailyReportId:z})}}})}})},P=()=>{m.value={description:"",topDepth:null,casingShoeDepth:null,casingLength:null,outsideDiameter:null,insideDiameter:null,weight:null}};return{id:o,modal:h,rules:y,loading:a,targetData:m,formRef:n,submit:M,setId:c,reset:P,closeModal:f}}}),ts={key:0,class:"fixed top-0 right-0 bottom-0 left-0 z-40 flex items-center"},ls={class:"relative bg-card-background text-card-text-light max-h-3/4 w-11/12 flex flex-col items-center gap-3 mx-auto my-6 p-5 rounded-xl overflow-y-scroll md:overflow-hidden md:text-lg lg:w-2/5 lg:h-auto"},os={class:"flex flex-row h-auto w-full items-center justify-between"},ss={class:"text-lg font-bold"},ns={class:"h-auto w-full flex flex-col gap-3 md:flex-row md:gap-4"},as={class:"h-auto w-full flex flex-col gap-3"},is={class:"h-auto w-full flex flex-col gap-1"},rs={class:"h-auto w-full flex flex-col gap-1"},ds={class:"font-bold"},us={class:"h-auto w-full flex flex-col gap-1"},ps={class:"font-bold"},cs={class:"h-auto w-full flex flex-col gap-1"},fs={class:"font-bold"},ms={class:"h-auto w-full flex flex-col gap-3"},gs={class:"h-auto w-full flex flex-col gap-1"},bs={class:"font-bold"},hs={class:"h-auto w-full flex flex-col gap-1"},vs={class:"font-bold"},ys={class:"h-auto w-full flex flex-col gap-1"},ws={class:"font-bold"},Ss={class:"h-auto w-full flex flex-row items-center gap-2"},$s=["disabled"],xs=["disabled"],ks={key:0,class:"indicator-label"},Ds={key:1,class:"indicator-progress"};function Vs(l,e,S,h,m,n){const a=k("SvgIcon"),o=k("el-input"),r=k("el-form-item"),d=k("el-popover"),s=k("el-form");return l.isVisible?(w(),$("div",ts,[e[29]||(e[29]=t("div",{class:"absolute inset-0 bg-sidebar-background opacity-25"},null,-1)),t("div",ls,[t("div",os,[t("h3",ss,D(`${l.id?"Edit Cased Hole":"New Cased Hole"}`),1),t("span",{class:"cursor-pointer",onClick:e[0]||(e[0]=(...u)=>l.closeModal&&l.closeModal(...u))},[i(a,{icon:"closeModalIcon"})])]),i(s,{id:"cased_hole_form",onSubmit:ie(l.submit,["prevent"]),model:l.targetData,rules:l.rules,ref:"formRef",class:"h-auto w-full"},{default:p(()=>[t("div",ns,[t("div",as,[t("div",is,[e[9]||(e[9]=t("label",{class:"font-bold"},[F("Description"),t("span",{class:"text-danger-active font-light"},"*")],-1)),i(r,{prop:"description"},{default:p(()=>[i(o,{modelValue:l.targetData.description,"onUpdate:modelValue":e[1]||(e[1]=u=>l.targetData.description=u),placeholder:"",name:"description"},null,8,["modelValue"])]),_:1})]),t("div",rs,[t("label",ds,[e[12]||(e[12]=F("Top (Top Depth) (ft)")),i(d,{placement:"top",width:300,trigger:"hover"},{reference:p(()=>e[10]||(e[10]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7"},null,-1)])),default:p(()=>[e[11]||(e[11]=t("span",null,"The Top Depth refers to the depth at which the casing is set or begins in the wellbore.",-1))]),_:1})]),i(r,{prop:"topDepth"},{default:p(()=>[i(o,{type:"number",controls:!1,step:"any",modelValue:l.targetData.topDepth,"onUpdate:modelValue":e[2]||(e[2]=u=>l.targetData.topDepth=u),placeholder:"",name:"topDepth"},null,8,["modelValue"])]),_:1})]),t("div",us,[t("label",ps,[e[15]||(e[15]=F(" OD (Outside Diameter) (in)")),i(d,{placement:"top",width:300,trigger:"hover"},{reference:p(()=>e[13]||(e[13]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7"},null,-1)])),default:p(()=>[e[14]||(e[14]=t("span",null,"The Outside Diameter is the outer measurement of the casing pipe, typically in inches or millimeters.",-1))]),_:1})]),i(r,{prop:"outsideDiameter"},{default:p(()=>[i(o,{type:"number",controls:!1,step:"any",modelValue:l.targetData.outsideDiameter,"onUpdate:modelValue":e[3]||(e[3]=u=>l.targetData.outsideDiameter=u),placeholder:"",name:"outsideDiameter"},null,8,["modelValue"])]),_:1})]),t("div",cs,[t("label",fs,[e[18]||(e[18]=F("Wt (Weight) (lb/ft)")),i(d,{placement:"top",width:300,trigger:"hover"},{reference:p(()=>e[16]||(e[16]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7"},null,-1)])),default:p(()=>[e[17]||(e[17]=t("span",null,"The Weight of the casing pipe is typically expressed in pounds per foot (lb/ft) and represents the mass of the casing material per linear foot.",-1))]),_:1})]),i(r,{prop:"weight"},{default:p(()=>[i(o,{type:"number",controls:!1,step:"any",modelValue:l.targetData.weight,"onUpdate:modelValue":e[4]||(e[4]=u=>l.targetData.weight=u),placeholder:"",name:"weight"},null,8,["modelValue"])]),_:1})])]),t("div",ms,[t("div",gs,[t("label",bs,[e[21]||(e[21]=F(" ID (Inside Diameter) (in)")),i(d,{placement:"top",width:300,trigger:"hover"},{reference:p(()=>e[19]||(e[19]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7"},null,-1)])),default:p(()=>[e[20]||(e[20]=t("span",null,"The Inside Diameter is the inner measurement of the casing pipe, typically in inches or millimeters.",-1))]),_:1})]),i(r,{prop:"insideDiameter"},{default:p(()=>[i(o,{type:"number",controls:!1,step:"any",modelValue:l.targetData.insideDiameter,"onUpdate:modelValue":e[5]||(e[5]=u=>l.targetData.insideDiameter=u),placeholder:"",name:"insideDiameter"},null,8,["modelValue"])]),_:1})]),t("div",hs,[t("label",vs,[e[24]||(e[24]=F("Casing Shoe Depth (ft)")),i(d,{placement:"top",width:300,trigger:"hover"},{reference:p(()=>e[22]||(e[22]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7"},null,-1)])),default:p(()=>[e[23]||(e[23]=t("span",null,"The depth at which the casing shoe is located. This is the point where the casing is designed to support the weight of the wellbore fluids.",-1))]),_:1})]),i(r,{prop:"casingShoeDepth"},{default:p(()=>[i(o,{type:"number",controls:!1,step:"any",modelValue:l.targetData.casingShoeDepth,"onUpdate:modelValue":e[6]||(e[6]=u=>l.targetData.casingShoeDepth=u),placeholder:"",name:"casingShoeDepth"},null,8,["modelValue"])]),_:1})]),t("div",ys,[t("label",ws,[e[27]||(e[27]=F(" Casing Length (ft)")),i(d,{placement:"top",width:300,trigger:"hover"},{reference:p(()=>e[25]||(e[25]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7"},null,-1)])),default:p(()=>[e[26]||(e[26]=t("span",null,"The total length of the casing string in the wellbore, which may include multiple casing joints connected together.",-1))]),_:1})]),i(r,{prop:"casingLength"},{default:p(()=>[i(o,{type:"number",controls:!1,step:"any",modelValue:l.targetData.casingLength,"onUpdate:modelValue":e[7]||(e[7]=u=>l.targetData.casingLength=u),placeholder:"",name:"casingLength"},null,8,["modelValue"])]),_:1})])])]),t("div",Ss,[t("button",{type:"button",class:"bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover rounded-md px-4 py-2 font-semibold md:px-4 md:py-3 md:text-xl",onClick:e[8]||(e[8]=(...u)=>l.closeModal&&l.closeModal(...u)),disabled:l.loading}," Discard ",8,$s),t("button",{class:"bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover rounded-md px-4 py-2 font-semibold md:px-4 md:py-3 md:text-xl",type:"submit",disabled:l.loading},[l.loading?E("",!0):(w(),$("span",ks," Save ")),l.loading?(w(),$("span",Ds,e[28]||(e[28]=[F(" Please wait... "),t("span",{class:"spinner-border spinner-border-sm align-middle ms-2"},null,-1)]))):E("",!0)],8,xs)])]),_:1},8,["onSubmit","model","rules"])])])):E("",!0)}const Is=Q(es,[["render",Vs]]),Ps=J({name:"cased-hole",components:{SvgIcon:K,BottomTool:ce,CasedHoleModal:Is,NoEntries:me},props:{showCustomize:{type:Function,required:!1}},setup(){const l=v(null),e=v(!1),S=It(),h=v([]),m=le("dailyReport"),n=v(!1);ae(()=>{m!=null&&m.getDailyReportId()&&a()});const a=async(u={dailyReportId:m==null?void 0:m.getDailyReportId(),page:1,limit:200})=>{e.value=!0,S.getCasedHoles({params:u,callback:{onSuccess:f=>{h.value=f==null?void 0:f.items},onFinish:f=>{e.value=!1}}})},o=()=>{n.value=!n.value},r=u=>{var f;(f=l==null?void 0:l.value)==null||f.setId(u),n.value=!n.value},d=u=>{se.deletionAlert({onConfirmed:()=>{s(u)}})},s=async u=>{e.value=!0,S.deleteCasedHole({id:u,callback:{onSuccess:f=>{a()},onFinish:f=>{e.value=!1}}})};return{loading:e,casedHoleList:h,casedHoleModal:l,isModalVisible:n,numberWithCommas:fe,getCasedHoles:a,deleteCasedHole:d,toggleEditCasedHoleModal:r,toggleNewCasedHoleModal:o}}}),Cs={class:"cased-hole"},Ts={key:0,class:"text-center"},Ms={key:1,class:"grid grid-cols-1 gap-4 md:grid-cols-2 md:gap-7 lg:grid-cols-3"},Fs={class:"h-auto w-full flex flex-col gap-3"},Ns={class:"text-xl font-bold"},_s={class:"flex items-center justify-between border-dashed border-b-[1px] py-3"},Hs={class:"flex items-center justify-between border-dashed border-b-[1px] py-3"},As={class:"flex items-center justify-between border-dashed border-b-[1px] py-3 mb-3"},zs={class:"flex flex-wrap items-center gap-2"},qs={class:"flex items-center gap-2 border border-gray-300 border-dashed rounded py-3 px-4"},Bs={class:"flex items-center gap-2 border border-gray-300 border-dashed rounded py-3 px-4"},js={class:"flex items-center gap-2 border border-gray-300 border-dashed rounded py-3 px-4"},Es={class:"flex flex-row items-center gap-2 absolute top-0 end-0 -translate-y-1/2"},Rs=["onClick"],Os=["onClick"],Ls={class:"text-danger"};function Ws(l,e,S,h,m,n){const a=k("NoEntries"),o=k("SvgIcon"),r=k("BottomTool"),d=k("CasedHoleModal");return w(),$(O,null,[t("div",Cs,[l.loading?(w(),$("div",Ts,e[0]||(e[0]=[t("div",{class:"spinner-border",role:"status"},[t("span",{class:"sr-only"},"Loading...")],-1)]))):(w(),$(O,{key:1},[l.casedHoleList.length===0?(w(),te(a,{key:0,addNew:l.toggleNewCasedHoleModal},null,8,["addNew"])):(w(),$("div",Ms,[(w(!0),$(O,null,Z(l.casedHoleList,s=>(w(),$("div",{key:s.id,class:"relative h-auto w-full bg-minicard-background text-minicard-text-light flex flex-col items-center p-4 rounded-xl border-t-2 border-active my-2 first:mt-3 last:mb-5 font-semibold md:first:mt-0 md:last:mb-0 md:m-0"},[t("div",Fs,[t("h5",Ns,D(s.description),1),t("div",_s,[e[1]||(e[1]=t("span",null,"Top Depth",-1)),t("span",null,D(`${s.topDepth} (ft)`),1)]),t("div",Hs,[e[3]||(e[3]=t("span",null,"Casing Shoe Depth",-1)),t("span",null,[F(D(l.numberWithCommas(s.casingShoeDepth))+" ",1),e[2]||(e[2]=t("span",null,"(in)",-1))])]),t("div",As,[e[5]||(e[5]=t("span",null,"Casing Length",-1)),t("span",null,[F(D(l.numberWithCommas(s.casingLength))+" ",1),e[4]||(e[4]=t("span",null,"(lb/ft)",-1))])])]),t("div",zs,[t("div",qs,[t("div",null,D(`${l.numberWithCommas(s.outsideDiameter)} (in)`),1),e[6]||(e[6]=t("div",{class:"text-danger"},"OD",-1))]),t("div",Bs,[t("div",null,D(`${l.numberWithCommas(s.insideDiameter)} (in)`),1),e[7]||(e[7]=t("div",null,"ID",-1))]),t("div",js,[t("div",null,D(`${l.numberWithCommas(s.weight)} (lb/ft)`),1),e[8]||(e[8]=t("div",{class:"text-success"},"Wt",-1))])]),t("div",Es,[t("button",{class:"p-2 rounded-full bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover",onClick:u=>l.toggleEditCasedHoleModal(s.id.toString())},[i(o,{icon:"pencilIcon"})],8,Rs),t("button",{class:"p-2 rounded-full bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover",onClick:u=>{var f;return l.deleteCasedHole((f=s==null?void 0:s.id)==null?void 0:f.toString())}},[t("span",Ls,[i(o,{icon:"trashIcon"})])],8,Os)])]))),128))]))],64))]),i(r,{addNew:l.toggleNewCasedHoleModal,showHelpWindow:l.showCustomize},null,8,["addNew","showHelpWindow"]),i(d,{isVisible:l.isModalVisible,close:l.toggleNewCasedHoleModal,ref:"casedHoleModal",loadPage:l.getCasedHoles},null,8,["isVisible","close","loadPage"])],64)}const Pt=Q(Ps,[["render",Ws]]),Ct=ne("drillString",()=>({getDrillStrings:async({params:n,callback:a})=>{var d;const o=g.get(a,"onSuccess",g.noop),r=g.get(a,"onFinish",g.noop);try{V.setHeader();const s=await V.getWithParams("drillStrings",n);o(((d=s.data)==null?void 0:d.data)||s.data)}catch(s){H(s,a)}finally{r()}},getDrillStringDetails:async({id:n,callback:a})=>{var d;const o=g.get(a,"onSuccess",g.noop),r=g.get(a,"onFinish",g.noop);try{V.setHeader();const s=await V.get(`drillStrings/${n}`);o(((d=s.data)==null?void 0:d.data)||s.data)}catch(s){H(s,a)}finally{r()}},updateDrillString:async({id:n,params:a,callback:o})=>{var s;const r=g.get(o,"onSuccess",g.noop),d=g.get(o,"onFinish",g.noop);try{V.setHeader();const u=await V.put(`drillStrings/${n}`,a);r(((s=u.data)==null?void 0:s.data)||u.data)}catch(u){H(u,o)}finally{d()}},deleteDrillString:async({id:n,callback:a})=>{var d;const o=g.get(a,"onSuccess",g.noop),r=g.get(a,"onFinish",g.noop);try{V.setHeader();const s=await V.delete(`drillStrings/${n}`);o(((d=s.data)==null?void 0:d.data)||s.data)}catch(s){H(s,a)}finally{r()}},createDrillString:async({params:n,callback:a})=>{var d;const o=g.get(a,"onSuccess",g.noop),r=g.get(a,"onFinish",g.noop);try{V.setHeader();const s=await V.post("drillStrings",n);o(((d=s.data)==null?void 0:d.data)||s.data)}catch(s){H(s,a)}finally{r()}}})),Us=J({name:"drill-string-modal",components:{SvgIcon:K},props:{isVisible:{type:Boolean,required:!0},close:{type:Function,required:!0},loadPage:{type:Function,default:()=>{},required:!0}},setup(l){const e=re(),S=Ct(),h=v(!1),m=v({description:"",insideDiameter:null,outsideDiameter:null,length:null,weight:null}),n=v(null),a=v(!1),o=v(""),r=le("dailyReport");Y(o,x=>{x!==""&&d()}),Y(()=>l.isVisible,x=>{var b;x===!1&&(o.value="",P(),(b=n==null?void 0:n.value)==null||b.resetFields())});const d=async()=>{S.getDrillStringDetails({id:o.value,callback:{onSuccess:x=>{m.value=x}}})},s=async x=>{a.value=!0,S.updateDrillString({id:o.value,params:x,callback:{onSuccess:b=>{l!=null&&l.loadPage&&(l==null||l.loadPage()),l.close()},onFinish:b=>{a.value=!1}}})},u=async x=>{a.value=!0,S.createDrillString({params:x,callback:{onSuccess:b=>{l!=null&&l.loadPage&&(l==null||l.loadPage()),l.close()},onFinish:b=>{a.value=!1}}})},f=()=>{l.close()},c=x=>{o.value=x.toString()},y=v({description:[{required:!0,message:"Please type Description",trigger:"blur"}]}),M=()=>{n.value&&n.value.validate(x=>{var b,T,q,I,C,B;if(x){const A={description:(b=m==null?void 0:m.value)==null?void 0:b.description,insideDiameter:Number((T=m==null?void 0:m.value)==null?void 0:T.insideDiameter),outsideDiameter:Number((q=m==null?void 0:m.value)==null?void 0:q.outsideDiameter),length:Number((I=m==null?void 0:m.value)==null?void 0:I.length),weight:Number((C=m==null?void 0:m.value)==null?void 0:C.weight)};o!=null&&o.value?s({...A,dailyReportId:r==null?void 0:r.getDailyReportId()}):r==null||r.createDailyReport({wellId:(B=e==null?void 0:e.params)==null?void 0:B.id,callback:{onSuccess:_=>{u({...A,dailyReportId:_})}}})}})},P=()=>{m.value={description:"",insideDiameter:null,outsideDiameter:null,length:null,weight:null}};return{id:o,modal:h,rules:y,loading:a,targetData:m,formRef:n,closeModal:f,submit:M,setId:c,reset:P}}}),Gs={key:0,class:"fixed top-0 right-0 bottom-0 left-0 z-40 flex items-center"},Ys={class:"relative bg-card-background text-card-text-light max-h-11/12 w-11/12 flex flex-col items-center gap-3 mx-auto my-6 p-5 rounded-xl md:text-lg md:w-4/5 lg:w-2/5 lg:h-auto"},Js={class:"h-auto w-full flex flex-row items-center justify-between"},Qs={class:"text-lg font-bold"},Ks={class:"h-auto w-full flex flex-col gap-3 md:flex-row md:gap-4"},Xs={class:"h-auto w-full flex flex-col gap-3"},Zs={class:"h-auto w-full flex flex-col gap-1"},en={class:"h-auto w-full flex flex-col gap-1"},tn={class:"font-bold"},ln={class:"h-auto w-full flex flex-col gap-1"},on={class:"font-bold"},sn={class:"h-auto w-full flex flex-col gap-3"},nn={class:"h-auto w-full flex flex-col gap-1"},an={class:"font-bold"},rn={class:"h-auto w-full flex flex-col gap-1"},dn={class:"font-bold"},un={class:"h-auto w-full flex flex-row items-center gap-2"},pn=["disabled"],cn=["disabled"],fn={key:0,class:"indicator-label"},mn={key:1,class:"indicator-progress"};function gn(l,e,S,h,m,n){const a=k("SvgIcon"),o=k("el-input"),r=k("el-form-item"),d=k("el-popover"),s=k("el-form");return l.isVisible?(w(),$("div",Gs,[e[21]||(e[21]=t("div",{class:"absolute inset-0 bg-sidebar-background opacity-25"},null,-1)),t("div",Ys,[t("div",Js,[t("h3",Qs,D(`${l.id?"Edit Drill String":"New Drill String"}`),1),t("span",{class:"cursor-pointer",onClick:e[0]||(e[0]=(...u)=>l.closeModal&&l.closeModal(...u))},[i(a,{icon:"closeModalIcon"})])]),i(s,{id:"drill_string_form",onSubmit:ie(l.submit,["prevent"]),model:l.targetData,rules:l.rules,ref:"formRef",class:"h-auto w-full"},{default:p(()=>[t("div",Ks,[t("div",Xs,[t("div",Zs,[e[7]||(e[7]=t("label",{class:"font-bold"},[F("Description"),t("span",{class:"text-danger-active font-light"},"*")],-1)),i(r,{prop:"description",class:"mt-auto"},{default:p(()=>[i(o,{modelValue:l.targetData.description,"onUpdate:modelValue":e[1]||(e[1]=u=>l.targetData.description=u),placeholder:"",name:"description"},null,8,["modelValue"])]),_:1})]),t("div",en,[t("label",tn,[e[10]||(e[10]=F("Wt. (Weight) (lb/ft)")),i(d,{placement:"top",width:300,trigger:"hover"},{reference:p(()=>e[8]||(e[8]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7"},null,-1)])),default:p(()=>[e[9]||(e[9]=t("span",null,"The Weight of the drill pipe or tool is typically expressed in pounds per foot (lb/ft) and represents the mass of the component per linear foot. It helps determine the buoyancy and load-bearing capacity of the drill string. It is an input and doesn't require additional information. ",-1))]),_:1})]),i(r,{prop:"weight"},{default:p(()=>[i(o,{type:"number",controls:!1,step:"any",modelValue:l.targetData.weight,"onUpdate:modelValue":e[2]||(e[2]=u=>l.targetData.weight=u),placeholder:"",name:"weight"},null,8,["modelValue"])]),_:1})]),t("div",ln,[t("label",on,[e[13]||(e[13]=F("Tool Joint (OD) (in)")),i(d,{placement:"top",width:300,trigger:"hover"},{reference:p(()=>e[11]||(e[11]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7"},null,-1)])),default:p(()=>[e[12]||(e[12]=t("span",null,"Tool joints are specialized connectors used to join sections of drill pipe. Tool Joint OD is the outer diameter of the tool joint, typically larger than the drill pipe OD. It is an input and doesn't require additional information. ",-1))]),_:1})]),i(r,{prop:"outsideDiameter"},{default:p(()=>[i(o,{type:"number",controls:!1,step:"any",modelValue:l.targetData.outsideDiameter,"onUpdate:modelValue":e[3]||(e[3]=u=>l.targetData.outsideDiameter=u),placeholder:"",name:"outsideDiameter"},null,8,["modelValue"])]),_:1})])]),t("div",sn,[t("div",nn,[t("label",an,[e[16]||(e[16]=F(" Tool Joint ID (Inside Diameter) (in)")),i(d,{placement:"top",width:300,trigger:"hover"},{reference:p(()=>e[14]||(e[14]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7"},null,-1)])),default:p(()=>[e[15]||(e[15]=t("span",null,"Tool Joint ID is the inner diameter of the tool joint, representing the clear passage inside the tool joint. It is an input and doesn't require additional information. ",-1))]),_:1})]),i(r,{prop:"insideDiameter"},{default:p(()=>[i(o,{type:"number",controls:!1,step:"any",modelValue:l.targetData.insideDiameter,"onUpdate:modelValue":e[4]||(e[4]=u=>l.targetData.insideDiameter=u),placeholder:"",name:"insideDiameter"},null,8,["modelValue"])]),_:1})]),t("div",rn,[t("label",dn,[e[19]||(e[19]=F("Length (ft)")),i(d,{placement:"top",width:300,trigger:"hover"},{reference:p(()=>e[17]||(e[17]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7"},null,-1)])),default:p(()=>[e[18]||(e[18]=t("span",null,"The Length represents the total length of the drill string component, such as a section of drill pipe or a tool. ",-1))]),_:1})]),i(r,{prop:"length"},{default:p(()=>[i(o,{type:"number",controls:!1,step:"any",modelValue:l.targetData.length,"onUpdate:modelValue":e[5]||(e[5]=u=>l.targetData.length=u),placeholder:"",name:"length"},null,8,["modelValue"])]),_:1})])])]),t("div",un,[t("button",{type:"button",class:"bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover rounded-md px-4 py-2 font-semibold md:px-4 md:py-3 md:text-xl",onClick:e[6]||(e[6]=(...u)=>l.closeModal&&l.closeModal(...u)),disabled:l.loading}," Discard ",8,pn),t("button",{class:"bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover rounded-md px-4 py-2 font-semibold md:px-4 md:py-3 md:text-xl",type:"submit",disabled:l.loading},[l.loading?E("",!0):(w(),$("span",fn," Save ")),l.loading?(w(),$("span",mn,e[20]||(e[20]=[F(" Please wait... "),t("span",{class:"spinner-border spinner-border-sm align-middle ms-2"},null,-1)]))):E("",!0)],8,cn)])]),_:1},8,["onSubmit","model","rules"])])])):E("",!0)}const bn=Q(Us,[["render",gn]]),hn=J({name:"drill-string",components:{SvgIcon:K,BottomTool:ce,DrillStringModal:bn,NoEntries:me},props:{showCustomize:{type:Function,required:!1}},setup(l){const e=v(null),S=v(!1),h=Ct(),m=v([]),n=le("dailyReport"),a=v(!1);ae(()=>{n!=null&&n.getDailyReportId()&&o()});const o=async(f={dailyReportId:n==null?void 0:n.getDailyReportId(),page:1,limit:200})=>{S.value=!0,h.getDrillStrings({params:f,callback:{onSuccess:c=>{m.value=c==null?void 0:c.items},onFinish:c=>{S.value=!1}}})},r=()=>{a.value=!a.value},d=f=>{var c;(c=e==null?void 0:e.value)==null||c.setId(f),a.value=!a.value},s=f=>{se.deletionAlert({onConfirmed:()=>{u(f)}})},u=async f=>{S.value=!0,h.deleteDrillString({id:f,callback:{onSuccess:c=>{o()},onFinish:c=>{S.value=!1}}})};return{loading:S,drillStringList:m,drillStringModal:e,isModalVisible:a,getDrillStrings:o,numberWithCommas:fe,deleteDrillString:s,toggleEditDrillStringModal:d,toggleNewDrillStringModal:r}}}),vn={class:"drill-string"},yn={key:0,class:"text-center"},wn={key:1,class:"grid grid-cols-1 gap-4 md:grid-cols-2 md:gap-7 lg:grid-cols-3"},Sn={class:"h-auto w-full flex flex-col gap-3"},$n={class:"text-xl font-bold"},xn={class:"flex items-center justify-between border-dashed border-b-[1px] py-3"},kn={class:"flex items-center justify-between border-dashed border-b-[1px] py-3"},Dn={class:"flex items-center justify-between border-dashed border-b-[1px] py-3"},Vn={class:"flex flex-wrap items-center gap-2"},In={class:"flex items-center gap-2 border border-gray-300 border-dashed rounded py-3 px-4"},Pn={class:"flex items-center gap-2 border border-gray-300 border-dashed rounded py-3 px-4"},Cn={class:"flex items-center gap-2 border border-gray-300 border-dashed rounded py-3 px-4"},Tn={class:"flex flex-row items-center gap-2 absolute top-0 end-0 -translate-y-1/2"},Mn=["onClick"],Fn={class:"svg-icon svg-icon-3"},Nn=["onClick"],_n={class:"svg-icon svg-icon-3 text-danger"};function Hn(l,e,S,h,m,n){const a=k("NoEntries"),o=k("SvgIcon"),r=k("BottomTool"),d=k("DrillStringModal");return w(),$(O,null,[t("div",vn,[l.loading?(w(),$("div",yn,e[0]||(e[0]=[t("div",{class:"spinner-border text-primary",role:"status"},[t("span",{class:"sr-only"},"Loading...")],-1)]))):(w(),$(O,{key:1},[l.drillStringList.length===0?(w(),te(a,{key:0,addNew:l.toggleNewDrillStringModal},null,8,["addNew"])):(w(),$("div",wn,[(w(!0),$(O,null,Z(l.drillStringList,s=>(w(),$("div",{key:s==null?void 0:s.id,class:"relative h-auto w-full bg-minicard-background text-minicard-text-light flex flex-col items-center p-4 rounded-xl border-t-2 border-active my-2 first:mt-3 last:mb-5 font-semibold md:first:mt-0 md:last:mb-0 md:m-0"},[t("div",Sn,[t("h5",$n,D(s==null?void 0:s.description),1),t("div",xn,[e[1]||(e[1]=t("span",null,"Tool Joint OD (Outside Diameter)",-1)),t("span",null,D(`${l.numberWithCommas(s==null?void 0:s.outsideDiameter)} (in)`),1)]),t("div",kn,[e[2]||(e[2]=t("span",null,"Tool Joint ID (Inside Diameter)",-1)),t("span",null,D(`${l.numberWithCommas(s==null?void 0:s.insideDiameter)} (in)`),1)]),t("div",Dn,[e[3]||(e[3]=t("span",null,"Length",-1)),t("span",null,D(`${l.numberWithCommas(s==null?void 0:s.length)} (ft)`),1)]),t("div",Vn,[t("div",In,[t("div",null,D(`${l.numberWithCommas(s==null?void 0:s.outsideDiameter)} (in)`),1),e[4]||(e[4]=t("div",{class:"font-semibold text-danger"},"OD",-1))]),t("div",Pn,[t("div",null,D(`${l.numberWithCommas(s==null?void 0:s.insideDiameter)} (in)`),1),e[5]||(e[5]=t("div",null,"ID",-1))]),t("div",Cn,[t("div",null,D(`${l.numberWithCommas(s==null?void 0:s.weight)} (lb/ft)`),1),e[6]||(e[6]=t("div",{class:"font-semibold text-success"},"Wt",-1))])])]),t("div",Tn,[t("button",{class:"p-2 rounded-full bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover",onClick:u=>l.toggleEditDrillStringModal(s==null?void 0:s.id.toString())},[t("span",Fn,[i(o,{icon:"pencilIcon"})])],8,Mn),t("button",{class:"p-2 rounded-full bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover",onClick:u=>l.deleteDrillString(s==null?void 0:s.id.toString())},[t("span",_n,[i(o,{icon:"trashIcon"})])],8,Nn)])]))),128))]))],64))]),i(r,{addNew:l.toggleNewDrillStringModal,showHelpWindow:l.showCustomize},null,8,["addNew","showHelpWindow"]),i(d,{isVisible:l.isModalVisible,close:l.toggleNewDrillStringModal,ref:"drillStringModal",loadPage:l.getDrillStrings},null,8,["isVisible","close","loadPage"])],64)}const Tt=Q(hn,[["render",Hn]]),Mt=ne("nozzle",()=>({getNozzles:async({params:a,callback:o})=>{var s;const r=g.get(o,"onSuccess",g.noop),d=g.get(o,"onFinish",g.noop);try{V.setHeader();const u=await V.getWithParams("nozzles",a);r(((s=u.data)==null?void 0:s.data)||u.data)}catch(u){H(u,o)}finally{d()}},getNozzleDetails:async({id:a,callback:o})=>{var s;const r=g.get(o,"onSuccess",g.noop),d=g.get(o,"onFinish",g.noop);try{V.setHeader();const u=await V.get(`nozzles/${a}`);r(((s=u.data)==null?void 0:s.data)||u.data)}catch(u){H(u,o)}finally{d()}},updateNozzle:async({id:a,params:o,callback:r})=>{var u;const d=g.get(r,"onSuccess",g.noop),s=g.get(r,"onFinish",g.noop);try{V.setHeader();const f=await V.put(`nozzles/${a}`,o);d(((u=f.data)==null?void 0:u.data)||f.data)}catch(f){H(f,r)}finally{s()}},deleteNozzle:async({id:a,callback:o})=>{var s;const r=g.get(o,"onSuccess",g.noop),d=g.get(o,"onFinish",g.noop);try{V.setHeader();const u=await V.delete(`nozzles/${a}`);r(((s=u.data)==null?void 0:s.data)||u.data)}catch(u){H(u,o)}finally{d()}},createNozzle:async({params:a,callback:o})=>{var s;const r=g.get(o,"onSuccess",g.noop),d=g.get(o,"onFinish",g.noop);try{V.setHeader();const u=await V.post("nozzles",a);r(((s=u.data)==null?void 0:s.data)||u.data)}catch(u){H(u,o)}finally{d()}},getNozzleTFA:async({dailyReportId:a,callback:o})=>{var s,u,f;const r=g.get(o,"onSuccess",g.noop),d=g.get(o,"onFinish",g.noop);try{V.setHeader();const c=await V.get(`nozzles/tfa/${a}`);r(((s=c.data)==null?void 0:s.data)!==void 0&&((u=c.data)==null?void 0:u.data)!==null?(f=c.data)==null?void 0:f.data:c.data)}catch(c){H(c,o)}finally{d()}}})),An=J({name:"nozzle-modal",components:{SvgIcon:K},props:{isVisible:{type:Boolean,required:!0},close:{type:Function,required:!0},loadPage:{type:Function,default:()=>{},required:!0}},setup(l){const e=re(),S=Mt(),h=v(!1),m=v({identificationNumber:"",orificeSize:null}),n=v(null),a=v(!1),o=v(""),r=le("dailyReport");Y(o,x=>{x!==""&&d()}),Y(()=>l.isVisible,x=>{var b;x===!1&&(o.value="",P(),(b=n==null?void 0:n.value)==null||b.resetFields())});const d=async()=>{S.getNozzleDetails({id:o.value,callback:{onSuccess:x=>{m.value=x}}})},s=async x=>{a.value=!0,S.updateNozzle({id:o.value,params:x,callback:{onSuccess:b=>{l!=null&&l.loadPage&&(l==null||l.loadPage()),l.close()},onFinish:b=>{a.value=!1}}})},u=async x=>{a.value=!0,S.createNozzle({params:x,callback:{onSuccess:b=>{l!=null&&l.loadPage&&(l==null||l.loadPage()),l.close()},onFinish:b=>{a.value=!1}}})},f=()=>{l.close()},c=x=>{o.value=x.toString()},y=v({identificationNumber:[{required:!0,message:"Please type Identification Number",trigger:"blur"}]}),M=()=>{n.value&&n.value.validate(x=>{var b,T,q;if(x){a.value=!0;const I={identificationNumber:(b=m==null?void 0:m.value)==null?void 0:b.identificationNumber,orificeSize:Number((T=m==null?void 0:m.value)==null?void 0:T.orificeSize)};o!=null&&o.value?s({...I,dailyReportId:r==null?void 0:r.getDailyReportId()}):r==null||r.createDailyReport({wellId:(q=e==null?void 0:e.params)==null?void 0:q.id,callback:{onSuccess:C=>{u({...I,dailyReportId:C})}}})}})},P=()=>{m.value={identificationNumber:"",orificeSize:null}};return{id:o,modal:h,rules:y,loading:a,targetData:m,formRef:n,closeModal:f,submit:M,setId:c,reset:P}}}),zn={key:0,class:"fixed top-0 right-0 bottom-0 left-0 z-40 flex items-center"},qn={class:"relative bg-card-background text-card-text-light max-h-11/12 w-11/12 flex flex-col items-center gap-3 mx-auto my-6 p-5 rounded-xl md:w-4/5 md:text-lg lg:w-2/5 lg:h-auto"},Bn={class:"flex flex-row h-auto w-full items-center justify-between"},jn={class:"text-lg font-bold"},En={class:"flex flex-col gap-1"},Rn={class:"font-bold"},On={class:"flex flex-col gap-1"},Ln={class:"font-bold"},Wn={class:"h-auto w-full flex flex-row items-center gap-2"},Un=["disabled"],Gn=["disabled"],Yn={key:0,class:"indicator-label"},Jn={key:1,class:"indicator-progress"};function Qn(l,e,S,h,m,n){const a=k("SvgIcon"),o=k("el-popover"),r=k("el-input"),d=k("el-form-item"),s=k("el-form");return l.isVisible?(w(),$("div",zn,[e[12]||(e[12]=t("div",{class:"absolute inset-0 bg-sidebar-background opacity-25"},null,-1)),t("div",qn,[t("div",Bn,[t("h3",jn,D(`${l.id?"Edit Nozzle":"New Nozzle"}`),1),t("span",{class:"cursor-pointer",onClick:e[0]||(e[0]=(...u)=>l.closeModal&&l.closeModal(...u))},[i(a,{icon:"closeModalIcon"})])]),i(s,{id:"nozzle_form",onSubmit:ie(l.submit,["prevent"]),model:l.targetData,rules:l.rules,ref:"formRef",class:"h-auto w-full flex flex-col gap-3"},{default:p(()=>[t("div",En,[t("label",Rn,[e[6]||(e[6]=F("Identification Number")),e[7]||(e[7]=t("span",{class:"text-danger-active font-light"},"*",-1)),i(o,{placement:"top",width:300,trigger:"hover"},{reference:p(()=>e[4]||(e[4]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7"},null,-1)])),default:p(()=>[e[5]||(e[5]=t("span",null,'The "No." or Number refers to the identification or serial number assigned to the nozzle. It helps track the specific nozzle being used or replaced. ',-1))]),_:1})]),i(d,{prop:"identificationNumber",class:"mt-auto"},{default:p(()=>[i(r,{modelValue:l.targetData.identificationNumber,"onUpdate:modelValue":e[1]||(e[1]=u=>l.targetData.identificationNumber=u),placeholder:"",name:"identificationNumber"},null,8,["modelValue"])]),_:1})]),t("div",On,[t("label",Ln,[e[10]||(e[10]=t("span",null,"Orifice Size",-1)),i(o,{placement:"top",width:300,trigger:"hover"},{reference:p(()=>e[8]||(e[8]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7"},null,-1)])),default:p(()=>[e[9]||(e[9]=t("span",null,`The "Orifice Size" represents the diameter of the nozzle's orifice, typically measured in 1/32-inch increments. The nozzle size is crucial as it affects the flow rate and pressure of the drilling fluid being pumped through it. `,-1))]),_:1})]),i(d,{prop:"orificeSize"},{default:p(()=>[i(r,{type:"number",controls:!1,step:"any",modelValue:l.targetData.orificeSize,"onUpdate:modelValue":e[2]||(e[2]=u=>l.targetData.orificeSize=u),placeholder:"",name:"orificeSize"},null,8,["modelValue"])]),_:1})]),t("div",Wn,[t("button",{type:"button",class:"bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover rounded-md px-4 py-2 font-semibold md:px-4 md:py-3 md:text-xl",onClick:e[3]||(e[3]=(...u)=>l.closeModal&&l.closeModal(...u)),disabled:l.loading}," Discard ",8,Un),t("button",{class:"bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover rounded-md px-4 py-2 font-semibold md:px-4 md:py-3 md:text-xl",type:"submit",disabled:l.loading},[l.loading?E("",!0):(w(),$("span",Yn," Save ")),l.loading?(w(),$("span",Jn,e[11]||(e[11]=[F(" Please wait... "),t("span",{class:"spinner-border spinner-border-sm align-middle ms-2"},null,-1)]))):E("",!0)],8,Gn)])]),_:1},8,["onSubmit","model","rules"])])])):E("",!0)}const Kn=Q(An,[["render",Qn]]),Xn=J({name:"nozzles",components:{SvgIcon:K,BottomTool:ce,NozzleModal:Kn,NoEntries:me},props:{showCustomize:{type:Function,required:!1}},setup(l){const e=v(null),S=Mt(),h=v(!1),m=v(!1),n=v([]),a=v(""),o=le("dailyReport"),r=v(!1);ae(()=>{o!=null&&o.getDailyReportId()&&d()});const d=()=>{s(),u()},s=async()=>{const P={dailyReportId:o==null?void 0:o.getDailyReportId(),page:1,limit:200};h.value=!0,S.getNozzles({params:P,callback:{onSuccess:x=>{n.value=x==null?void 0:x.items},onFinish:x=>{h.value=!1}}})},u=async()=>{o!=null&&o.getDailyReportId()&&(m.value=!0,S.getNozzleTFA({dailyReportId:o==null?void 0:o.getDailyReportId(),callback:{onSuccess:P=>{a.value=P},onFinish:P=>{m.value=!1}}}))},f=()=>{r.value=!r.value},c=P=>{var x;(x=e==null?void 0:e.value)==null||x.setId(P),r.value=!r.value},y=P=>{se.deletionAlert({onConfirmed:()=>{M(P)}})},M=async P=>{h.value=!0,S.deleteNozzle({id:P,callback:{onSuccess:x=>{s()},onFinish:x=>{h.value=!1}}})};return{tfa:a,loading:h,loadingTFA:m,nozzleList:n,nozzleModal:e,isModalVisible:r,loadPage:d,deleteNozzle:y,numberWithCommas:fe,toggleEditNozzleModal:c,toggleNewNozzleModal:f}}}),Zn={class:"nozzles"},ea={class:"bg-minicard-background text-minicard-text-light rounded-lg p-4"},ta={class:"flex flex-col rounded-xl border border-dashed p-7 d-flex flex-column gap-3 text-sm"},la={key:0,class:"text-center"},oa={class:"font-semibold"},sa={key:0,class:"text-center"},na={key:1,class:"mt-7 grid grid-cols-1 gap-4 md:grid-cols-2 md:gap-7 lg:grid-cols-3"},aa={class:"h-auto w-full flex flex-col gap-3"},ia={class:"text-xl font-bold"},ra={class:"flex items-center justify-between border-dashed border-b-[1px] py-3"},da={class:"flex flex-row items-center gap-2 absolute top-0 end-0 -translate-y-1/2"},ua=["onClick"],pa=["onClick"],ca={class:"text-danger"};function fa(l,e,S,h,m,n){const a=k("NoEntries"),o=k("SvgIcon"),r=k("BottomTool"),d=k("NozzleModal");return w(),$(O,null,[t("div",Zn,[t("div",ea,[e[3]||(e[3]=t("h4",{class:"font-bold mb-2"},"Detail",-1)),t("div",ta,[l.loadingTFA?(w(),$("div",la,e[0]||(e[0]=[t("div",{class:"spinner-border text-primary",role:"status"},[t("span",{class:"sr-only"},"Loading...")],-1)]))):(w(),$(O,{key:1},[e[1]||(e[1]=t("h4",{class:"mb-0 font-semibold"},"TFA (Total Flow Area) (in^2)",-1)),e[2]||(e[2]=t("div",{class:"font-semibold"}," Calculated - Sum of areas of Nozzel section orifice size diameters = sum(pi*(d/2)^2) ",-1)),t("h3",oa,D(`${l.tfa||0} (in^2)`),1)],64))])]),l.loading?(w(),$("div",sa,e[4]||(e[4]=[t("div",{class:"spinner-border text-primary",role:"status"},[t("span",{class:"sr-only"},"Loading...")],-1)]))):(w(),$(O,{key:1},[l.nozzleList.length===0?(w(),te(a,{key:0,addNew:l.toggleNewNozzleModal},null,8,["addNew"])):(w(),$("div",na,[(w(!0),$(O,null,Z(l.nozzleList,s=>(w(),$("div",{key:s.id,class:"relative h-auto w-full bg-minicard-background text-minicard-text-light flex flex-col items-center p-4 rounded-xl border-t-2 border-active my-2 first:mt-4 last:mb-5 font-semibold md:first:mt-0 md:last:mb-0 md:m-0"},[t("div",aa,[t("h5",ia,D(s.identificationNumber),1),t("div",ra,[e[5]||(e[5]=t("span",null,"Orifice size",-1)),t("span",null,D(`${l.numberWithCommas(s.orificeSize)} (1/32in)`),1)])]),t("div",da,[t("button",{class:"p-2 rounded-full bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover",onClick:u=>l.toggleEditNozzleModal(s.id.toString())},[t("span",null,[i(o,{icon:"pencilIcon"})])],8,ua),t("button",{class:"p-2 rounded-full bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover",onClick:u=>l.deleteNozzle(s.id.toString())},[t("span",ca,[i(o,{icon:"trashIcon"})])],8,pa)])]))),128))]))],64))]),i(r,{addNew:l.toggleNewNozzleModal,showHelpWindow:l.showCustomize},null,8,["addNew","showHelpWindow"]),i(d,{isVisible:l.isModalVisible,close:l.toggleNewNozzleModal,ref:"nozzleModal",loadPage:l.loadPage},null,8,["isVisible","close","loadPage"])],64)}const Ft=Q(Xn,[["render",fa]]),Nt=ne("openHole",()=>({getOpenHoles:async({params:n,callback:a})=>{var d;const o=g.get(a,"onSuccess",g.noop),r=g.get(a,"onFinish",g.noop);try{V.setHeader();const s=await V.getWithParams("openHoles",n);o(((d=s.data)==null?void 0:d.data)||s.data)}catch(s){H(s,a)}finally{r()}},getOpenHoleDetails:async({id:n,callback:a})=>{var d;const o=g.get(a,"onSuccess",g.noop),r=g.get(a,"onFinish",g.noop);try{V.setHeader();const s=await V.get(`openHoles/${n}`);o(((d=s.data)==null?void 0:d.data)||s.data)}catch(s){H(s,a)}finally{r()}},updateOpenHole:async({id:n,params:a,callback:o})=>{var s;const r=g.get(o,"onSuccess",g.noop),d=g.get(o,"onFinish",g.noop);try{V.setHeader();const u=await V.put(`openHoles/${n}`,a);r(((s=u.data)==null?void 0:s.data)||u.data)}catch(u){H(u,o)}finally{d()}},deleteOpenHole:async({id:n,callback:a})=>{var d;const o=g.get(a,"onSuccess",g.noop),r=g.get(a,"onFinish",g.noop);try{V.setHeader();const s=await V.delete(`openHoles/${n}`);o(((d=s.data)==null?void 0:d.data)||s.data)}catch(s){H(s,a)}finally{r()}},createOpenHole:async({params:n,callback:a})=>{var d;const o=g.get(a,"onSuccess",g.noop),r=g.get(a,"onFinish",g.noop);try{V.setHeader();const s=await V.post("openHoles",n);o(((d=s.data)==null?void 0:d.data)||s.data)}catch(s){H(s,a)}finally{r()}}})),ma=J({name:"open-hole-modal",components:{SvgIcon:K},props:{isVisible:{type:Boolean,required:!0},close:{type:Function,required:!0},loadPage:{type:Function,default:()=>{},required:!0}},setup(l){const e=re(),S=Nt(),h=v(!1),m=v({description:"",measuredDepth:null,insideDiameter:null,washout:null}),n=v(null),a=v(!1),o=v(""),r=le("dailyReport");Y(o,x=>{x!==""&&d()}),Y(()=>l.isVisible,x=>{var b;x===!1&&(o.value="",P(),(b=n==null?void 0:n.value)==null||b.resetFields())});const d=async()=>{S.getOpenHoleDetails({id:o.value,callback:{onSuccess:x=>{m.value={...x}}}})},s=async x=>{a.value=!0,S.updateOpenHole({id:o.value,params:x,callback:{onSuccess:b=>{l!=null&&l.loadPage&&(l==null||l.loadPage()),l.close()},onFinish:b=>{a.value=!1}}})},u=async x=>{a.value=!0,S.createOpenHole({params:x,callback:{onSuccess:b=>{l!=null&&l.loadPage&&(l==null||l.loadPage()),l.close()},onFinish:b=>{a.value=!1}}})},f=()=>{l.close()},c=x=>{o.value=x.toString()},y=v({description:[{required:!0,message:"Please type Description",trigger:"blur"}]}),M=()=>{n.value&&n.value.validate(x=>{var b,T,q,I,C;if(x){const B={description:(b=m==null?void 0:m.value)==null?void 0:b.description,measuredDepth:Number((T=m==null?void 0:m.value)==null?void 0:T.measuredDepth),insideDiameter:Number((q=m==null?void 0:m.value)==null?void 0:q.insideDiameter),washout:Number((I=m==null?void 0:m.value)==null?void 0:I.washout)};o!=null&&o.value?s({...B,dailyReportId:r==null?void 0:r.getDailyReportId()}):r==null||r.createDailyReport({wellId:(C=e==null?void 0:e.params)==null?void 0:C.id,callback:{onSuccess:A=>{u({...B,dailyReportId:A})}}})}})},P=()=>{m.value={description:"",measuredDepth:null,insideDiameter:null,washout:null}};return{id:o,modal:h,rules:y,loading:a,targetData:m,formRef:n,closeModal:f,submit:M,setId:c,reset:P}}}),ga={key:0,class:"fixed top-0 right-0 bottom-0 left-0 z-40 flex items-center"},ba={class:"relative bg-card-background text-card-text-light max-h-11/12 w-11/12 flex flex-col items-center gap-3 mx-auto my-6 p-5 rounded-xl md:w-4/5 md:text-lg lg:w-2/5 lg:h-auto"},ha={class:"h-auto w-full flex flex-row items-center justify-between"},va={class:"text-lg font-bold"},ya={class:"h-auto w-full flex flex-col gap-3 md:flex-row md:gap-4"},wa={class:"h-auto w-full flex flex-col gap-3"},Sa={class:"h-auto w-full flex flex-col gap-1"},$a={class:"h-auto w-full flex flex-col gap-1"},xa={class:"font-bold"},ka={class:"h-auto w-full flex flex-col gap-3"},Da={class:"h-auto w-full flex flex-col gap-1"},Va={class:"font-bold"},Ia={class:"h-auto w-full flex flex-col gap-1"},Pa={class:"font-bold"},Ca={class:"h-auto w-full flex flex-row items-center gap-2"},Ta=["disabled"],Ma=["disabled"],Fa={key:0,class:"indicator-label"},Na={key:1,class:"indicator-progress"};function _a(l,e,S,h,m,n){const a=k("SvgIcon"),o=k("el-input"),r=k("el-form-item"),d=k("el-popover"),s=k("el-form");return l.isVisible?(w(),$("div",ga,[e[17]||(e[17]=t("div",{class:"absolute inset-0 bg-sidebar-background opacity-25"},null,-1)),t("div",ba,[t("div",ha,[t("h3",va,D(`${l.id?"Edit Open Hole":"New Open Hole"}`),1),t("span",{class:"cursor-pointer",onClick:e[0]||(e[0]=(...u)=>l.closeModal&&l.closeModal(...u))},[i(a,{icon:"closeModalIcon"})])]),i(s,{id:"open_hole_form",onSubmit:ie(l.submit,["prevent"]),model:l.targetData,rules:l.rules,ref:"formRef",class:"h-auto w-full"},{default:p(()=>[t("div",ya,[t("div",wa,[t("div",Sa,[e[6]||(e[6]=t("label",{class:"font-bold"},[F("Description"),t("span",{class:"text-danger-active font-light"},"*")],-1)),i(r,{prop:"description",class:"mt-auto"},{default:p(()=>[i(o,{modelValue:l.targetData.description,"onUpdate:modelValue":e[1]||(e[1]=u=>l.targetData.description=u),placeholder:"",name:"description"},null,8,["modelValue"])]),_:1})]),t("div",$a,[t("label",xa,[e[9]||(e[9]=F("ID (Inside Diameter) (inches)")),i(d,{placement:"top",width:300,trigger:"hover"},{reference:p(()=>e[7]||(e[7]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7"},null,-1)])),default:p(()=>[e[8]||(e[8]=t("span",null,"The Inside Diameter is the inner measurement of the open hole or wellbore, typically measured in inches or millimeters. ",-1))]),_:1})]),i(r,{prop:"insideDiameter"},{default:p(()=>[i(o,{type:"number",controls:!1,step:"any",modelValue:l.targetData.insideDiameter,"onUpdate:modelValue":e[2]||(e[2]=u=>l.targetData.insideDiameter=u),placeholder:"",name:"insideDiameter"},null,8,["modelValue"])]),_:1})])]),t("div",ka,[t("div",Da,[t("label",Va,[e[12]||(e[12]=F("MD (Measured Depth) (ft)")),i(d,{placement:"top",width:300,trigger:"hover"},{reference:p(()=>e[10]||(e[10]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7"},null,-1)])),default:p(()=>[e[11]||(e[11]=t("span",null,"Measured Depth represents the length of the open hole from the surface to a specific depth. ",-1))]),_:1})]),i(r,{prop:"measuredDepth"},{default:p(()=>[i(o,{type:"number",controls:!1,step:"any",modelValue:l.targetData.measuredDepth,"onUpdate:modelValue":e[3]||(e[3]=u=>l.targetData.measuredDepth=u),placeholder:"",name:"measuredDepth"},null,8,["modelValue"])]),_:1})]),t("div",Ia,[t("label",Pa,[e[15]||(e[15]=F(" Washout (%)")),i(d,{placement:"top",width:300,trigger:"hover"},{reference:p(()=>e[13]||(e[13]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7"},null,-1)])),default:p(()=>[e[14]||(e[14]=t("span",null,"Washout refers to the enlargement of the open hole diameter due to erosion or other factors. It is expressed as a percentage and can vary at different depths. It is an input, and additional information may include the depth intervals with washout and the severity of washout (e.g., mild, moderate, severe). ",-1))]),_:1})]),i(r,{prop:"washout"},{default:p(()=>[i(o,{type:"number",controls:!1,step:"any",modelValue:l.targetData.washout,"onUpdate:modelValue":e[4]||(e[4]=u=>l.targetData.washout=u),placeholder:"",name:"washout"},null,8,["modelValue"])]),_:1})])])]),t("div",Ca,[t("button",{type:"button",class:"bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover rounded-md px-4 py-2 font-semibold md:px-4 md:py-3 md:text-xl",onClick:e[5]||(e[5]=(...u)=>l.closeModal&&l.closeModal(...u)),disabled:l.loading}," Discard ",8,Ta),t("button",{class:"bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover rounded-md px-4 py-2 font-semibold md:px-4 md:py-3 md:text-xl",type:"submit",disabled:l.loading},[l.loading?E("",!0):(w(),$("span",Fa," Save ")),l.loading?(w(),$("span",Na,e[16]||(e[16]=[F(" Please wait... "),t("span",{class:"spinner-border spinner-border-sm align-middle ms-2"},null,-1)]))):E("",!0)],8,Ma)])]),_:1},8,["onSubmit","model","rules"])])])):E("",!0)}const Ha=Q(ma,[["render",_a]]),Aa=J({name:"open-hole",components:{SvgIcon:K,BottomTool:ce,OpenHoleModal:Ha,NoEntries:me},props:{showCustomize:{type:Function,required:!1}},setup(){const l=v(null),e=v(!1),S=Nt(),h=v([]),m=le("dailyReport"),n=v(!1);ae(()=>{m!=null&&m.getDailyReportId()&&a()});const a=async(u={dailyReportId:m==null?void 0:m.getDailyReportId(),page:1,limit:200})=>{e.value=!0,S.getOpenHoles({params:u,callback:{onSuccess:f=>{h.value=f==null?void 0:f.items},onFinish:f=>{e.value=!1}}})},o=()=>{n.value=!n.value},r=u=>{var f;(f=l==null?void 0:l.value)==null||f.setId(u),n.value=!n.value},d=u=>{se.deletionAlert({onConfirmed:()=>{s(u)}})},s=async u=>{e.value=!0,S.deleteOpenHole({id:u,callback:{onSuccess:f=>{a()},onFinish:f=>{e.value=!1}}})};return{loading:e,openHoleList:h,openHoleModal:l,isModalVisible:n,getOpenHoles:a,numberWithCommas:fe,deleteOpenHole:d,toggleEditOpenHoleModal:r,toggleNewOpenHoleModal:o}}}),za={class:"open-hole"},qa={class:"row gap-10"},Ba={key:0,class:"text-center"},ja={key:1,class:"grid grid-cols-1 gap-3 md:grid-cols-2 md:gap-7 lg:grid-cols-3"},Ea={class:"h-auto w-full flex flex-col gap-3"},Ra={class:"text-xl font-bold"},Oa={class:"flex items-center justify-between border-dashed border-b-[1px] py-3"},La={class:"flex flex-wrap items-center gap-2"},Wa={class:"flex items-center gap-2 border border-gray-300 border-dashed rounded py-3 px-4"},Ua={class:"flex items-center gap-2 border border-gray-300 border-dashed rounded py-3 px-4"},Ga={class:"text-danger"},Ya={class:"flex flex-row items-center gap-2 absolute top-0 end-0 -translate-y-1/2"},Ja=["onClick"],Qa=["onClick"],Ka={class:"text-danger"};function Xa(l,e,S,h,m,n){const a=k("NoEntries"),o=k("SvgIcon"),r=k("BottomTool"),d=k("OpenHoleModal");return w(),$(O,null,[t("div",za,[t("div",qa,[l.loading?(w(),$("div",Ba,e[0]||(e[0]=[t("div",{class:"spinner-border text-primary",role:"status"},[t("span",{class:"sr-only"},"Loading...")],-1)]))):(w(),$(O,{key:1},[l.openHoleList.length===0?(w(),te(a,{key:0,addNew:l.toggleNewOpenHoleModal},null,8,["addNew"])):(w(),$("div",ja,[(w(!0),$(O,null,Z(l.openHoleList,s=>(w(),$("div",{key:s.id,class:"relative h-auto w-full bg-minicard-background text-minicard-text-light flex flex-col items-center p-4 rounded-xl border-t-2 border-active my-2 first:mt-3 last:mb-5 font-semibold md:first:mt-0 md:last:mb-0 md:m-0"},[t("div",Ea,[t("h5",Ra,D(s.description),1),t("div",Oa,[e[1]||(e[1]=t("span",null,"MD (Measured Depth) (ft)",-1)),t("span",null,D(`${l.numberWithCommas(s.measuredDepth)} (ft)`),1)]),t("div",La,[t("div",Wa,[t("div",null,D(`${l.numberWithCommas(s.insideDiameter)} (in)`),1),e[2]||(e[2]=t("div",null,"ID",-1))]),t("div",Ua,[t("div",Ga,D(`${l.numberWithCommas(s.washout)}%`),1),e[3]||(e[3]=t("div",{class:"text-info"},"Washout",-1))])])]),t("div",Ya,[t("button",{class:"p-2 rounded-full bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover",onClick:u=>l.toggleEditOpenHoleModal(s.id.toString())},[i(o,{icon:"pencilIcon"})],8,Ja),t("button",{class:"p-2 rounded-full bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover",onClick:u=>l.deleteOpenHole(s.id.toString())},[t("span",Ka,[i(o,{icon:"trashIcon"})])],8,Qa)])]))),128))]))],64))])]),i(r,{addNew:l.toggleNewOpenHoleModal,showHelpWindow:l.showCustomize},null,8,["addNew","showHelpWindow"]),i(d,{isVisible:l.isModalVisible,close:l.toggleNewOpenHoleModal,ref:"openHoleModal",loadPage:l.getOpenHoles},null,8,["isVisible","close","loadPage"])],64)}const _t=Q(Aa,[["render",Xa]]),Za=ne("wellInformation",()=>({updateWellInformation:async({id:n,params:a,callback:o})=>{var s;const r=g.get(o,"onSuccess",g.noop),d=g.get(o,"onFinish",g.noop);try{V.setHeader();const u=await V.put(`wellInformations/${n}`,a);r(((s=u.data)==null?void 0:s.data)||u.data)}catch(u){H(u,o)}finally{d()}},deleteWellInformation:async({id:n,callback:a})=>{var d;const o=g.get(a,"onSuccess",g.noop),r=g.get(a,"onFinish",g.noop);try{V.setHeader();const s=await V.delete(`wellInformations/${n}`);o(((d=s.data)==null?void 0:d.data)||s.data)}catch(s){H(s,a)}finally{r()}},getWellInformation:async({params:n,callback:a})=>{var d;const o=g.get(a,"onSuccess",g.noop),r=g.get(a,"onFinish",g.noop);try{V.setHeader();const s=await V.getWithParams("wellInformations",n);o(((d=s.data)==null?void 0:d.data)||s.data)}catch(s){H(s,a)}finally{r()}},getWellInfoToday:async({dailyReportId:n,callback:a})=>{var d;const o=g.get(a,"onSuccess",g.noop),r=g.get(a,"onFinish",g.noop);try{V.setHeader();const s=await V.get(`/wellInformations/wellInformationToday/${n}`);o(((d=s.data)==null?void 0:d.data)||s.data)}catch(s){H(s,a)}finally{r()}},createWellInformation:async({params:n,callback:a})=>{var d;const o=g.get(a,"onSuccess",g.noop),r=g.get(a,"onFinish",g.noop);try{V.setHeader();const s=await V.post("wellInformations",n);o(((d=s.data)==null?void 0:d.data)||s.data)}catch(s){H(s,a)}finally{r()}}})),ei=J({name:"well-information",components:{SvgIcon:K},setup(){var N,j,z;const l=re(),e=yl(),S=Za(),h=gt(),m=v(),n=v([]),a=v(!1),o=v(!1),r=v(),d=(N=l==null?void 0:l.params)==null?void 0:N.id,s=v(!0),u=le("dailyReport"),f=v({dailyReportId:u==null?void 0:u.getDailyReportId(),reportedAt:ke(gl().toDate(),"YYYY-MM-DD HH:mm:ss"),engineerId:!(u!=null&&u.getDailyReportId())&&De.checkRole(Ze.Engineer)?(j=De.getUserInfo())==null?void 0:j.id:null,activity:"",measuredDepth:null,trueVerticalDepth:null,inclination:null,azimuth:null,weightOnBit:null,rotaryWeight:null,standOffWeight:null,pullUpWeight:null,revolutionsPerMinute:null,rateOfPenetration:null,drillingInterval:"",formation:"",depthDrilled:null,totalStringLength:null,totalLength:null,tfa:null}),c=v({...f.value}),y=v(null),M=v(),P=v({reportedAt:[{required:!0,message:"Please select Date",trigger:"change"}],engineerId:[{required:!0,message:"Please select Engineer",trigger:"change"}]});ht((R,L,U)=>{xt(R,L,U,o.value||C())}),Y(()=>c.value.measuredDepth,R=>{var L,U,oe;if(R&&!isNaN(R)){const X=Number(((L=c.value)==null?void 0:L.measuredDepth)||0)-Number(((oe=(U=r==null?void 0:r.value)==null?void 0:U.wellInformation)==null?void 0:oe.measuredDepth)||0);!s.value||!(u!=null&&u.getDailyReportId())?(f.value={...f.value,depthDrilled:X},c.value={...c.value,depthDrilled:X}):s.value=!1}else f.value={...f.value,depthDrilled:null},c.value={...c.value,depthDrilled:null}}),Y(()=>u==null?void 0:u.getDailyReportId(),R=>{R&&b()}),ae(()=>{T(),x(),u!=null&&u.getDailyReportId()&&b()});const x=async()=>{e.getUsers({params:{role:Ze.Engineer,page:1,limit:500},callback:{onSuccess:R=>{n.value=R==null?void 0:R.items.map(L=>({label:`${L==null?void 0:L.firstName} ${L==null?void 0:L.lastName}`,value:L.id}))}}})},b=async()=>{u!=null&&u.getDailyReportId()&&h.getDailyReportById({id:u==null?void 0:u.getDailyReportId(),callback:{onSuccess:R=>{var oe,X;const L=R==null?void 0:R.wellInformation;M.value=L;let U;L?U={...L,dailyReportId:u==null?void 0:u.getDailyReportId(),engineerId:(oe=L==null?void 0:L.engineer)==null?void 0:oe.id}:U={...c.value,engineerId:De.checkRole(Ze.Engineer)?(X=De.getUserInfo())==null?void 0:X.id:null},f.value=JSON.parse(JSON.stringify(U)),c.value=JSON.parse(JSON.stringify(U))}}})},T=async()=>{d&&(a.value=!0,h.getWellInformationTab({wellId:d,callback:{onSuccess:R=>{var oe,X;const[L,U]=R;m.value=(oe=L==null?void 0:L.data)==null?void 0:oe.data,r.value=(X=U==null?void 0:U.data)==null?void 0:X.data},onFinish:()=>{a.value=!1}}}))},q=async R=>{S.createWellInformation({params:R,callback:{onSuccess:L=>{T(),u!=null&&u.getDailyReportId()&&b()},onFinish:()=>{o.value=!1}}})},I=async R=>{var L;o.value=!0,S.updateWellInformation({id:(L=M==null?void 0:M.value)==null?void 0:L.id,params:R,callback:{onSuccess:U=>{T(),u!=null&&u.getDailyReportId()&&b()},onFinish:()=>{o.value=!1}}})},C=()=>!vt.isEqual(c.value,f.value);return{submit:()=>{y.value&&y.value.validate(R=>{var L;if(R){if(o.value||!C())return;const U={...c.value,measuredDepth:Number(c.value.measuredDepth),trueVerticalDepth:Number(c.value.trueVerticalDepth),inclination:Number(c.value.inclination),azimuth:Number(c.value.azimuth),weightOnBit:Number(c.value.weightOnBit),rotaryWeight:Number(c.value.rotaryWeight),standOffWeight:Number(c.value.standOffWeight),pullUpWeight:Number(c.value.pullUpWeight),revolutionsPerMinute:Number(c.value.revolutionsPerMinute),rateOfPenetration:Number(c.value.rateOfPenetration),depthDrilled:(L=c.value)!=null&&L.depthDrilled?Number(c.value.depthDrilled):null,totalStringLength:Number(c.value.totalStringLength),totalLength:Number(c.value.totalLength)};u!=null&&u.getDailyReportId()?M.value?I({...U,dailyReportId:u==null?void 0:u.getDailyReportId()}):q({...U,dailyReportId:u==null?void 0:u.getDailyReportId()}):(o.value=!0,u==null||u.createDailyReport({wellId:d,callback:{onSuccess:oe=>{q({...U,dailyReportId:oe})},onFailure:()=>{o.value=!1}}}))}})},cancel:()=>{se.incompleteFormAlert({onConfirmed:()=>{c.value=JSON.parse(JSON.stringify(f.value))}},"Cancel changes on this section?","Yes")},isFormDirty:C,isValidForm:async()=>{var L;return await((L=y==null?void 0:y.value)==null?void 0:L.validate(U=>U))},loading:a,submitting:o,wellInfo:m,rules:P,engineerList:n,formRef:y,targetData:c,isSystemAdmin:De.checkRole(Ze.SystemAdmin),dailyReportId:(z=l==null?void 0:l.params)==null?void 0:z.dailyReportId}}}),ti={key:0,class:"text-center"},li={key:1,class:"h-auto w-full"},oi={class:"flex flex-row items-center w-full justify-start gap-4"},si=["disabled"],ni=["disabled"],ai={key:0,class:"indicator-label"},ii={key:1,class:"indicator-progress"},ri={class:"h-auto w-full flex flex-col gap-3 md:flex-row"},di={class:"h-auto w-full flex flex-col gap-1 md:justify-between"},ui={key:0,class:"h-auto w-full flex flex-col gap-1"},pi={class:"h-auto w-full flex flex-col gap-1 md:justify-between"},ci={class:"h-auto w-full flex flex-col gap-1 md:justify-between"},fi={class:"h-auto w-full flex flex-col gap-1 md:justify-between"},mi={class:"h-auto w-full flex flex-col gap-3 md:flex-row"},gi={class:"h-auto w-full flex flex-col gap-1 md:justify-between"},bi={class:"font-semibold"},hi={class:"h-auto w-full flex flex-col gap-1 md:justify-between"},vi={class:"font-semibold"},yi={class:"h-auto w-full flex flex-col gap-1 md:justify-between"},wi={class:"font-semibold"},Si={class:"h-auto w-full flex flex-col gap-1 md:justify-between"},$i={class:"font-semibold"},xi={class:"h-auto w-full flex flex-col gap-3 md:flex-row"},ki={class:"h-auto w-full flex flex-col gap-1 md:justify-between"},Di={class:"font-semibold"},Vi={class:"h-auto w-full flex flex-col gap-1 md:justify-between"},Ii={class:"font-semibold"},Pi={class:"h-auto w-full flex flex-col gap-1 md:justify-between"},Ci={class:"font-semibold"},Ti={class:"h-auto w-full flex flex-col gap-1 md:justify-between"},Mi={class:"font-semibold"},Fi={class:"h-auto w-full flex flex-col gap-3 md:flex-row"},Ni={class:"h-auto w-full flex flex-col gap-1 md:justify-between"},_i={class:"font-semibold"},Hi={class:"h-auto w-full flex flex-col gap-1 md:justify-between"},Ai={class:"font-semibold"},zi={class:"h-auto w-full flex flex-col gap-1 md:justify-between"},qi={class:"font-semibold"},Bi={class:"h-auto w-full flex flex-col gap-1 md:justify-between"},ji={class:"font-semibold"},Ei={class:"h-auto w-full flex flex-col gap-3 md:flex-row"},Ri={class:"h-auto w-full flex flex-col gap-1 md:justify-between"},Oi={class:"font-semibold"},Li={class:"h-auto w-full flex flex-col gap-1 md:justify-between"},Wi={class:"font-semibold"},Ui={class:"h-auto w-full flex flex-col gap-1 md:justify-between"},Gi={class:"font-semibold"},Yi={class:"h-auto w-full flex flex-col gap-1 md:justify-between"},Ji={class:"font-semibold"};function Qi(l,e,S,h,m,n){const a=k("el-input"),o=k("el-form-item"),r=k("el-date-picker"),d=k("el-select-v2"),s=k("el-popover"),u=k("el-form");return w(),te(u,{onSubmit:ie(l.submit,["prevent"]),model:l.targetData,rules:l.rules,ref:"formRef",class:"bg-minicard-background text-minicard-text-light rounded-xl p-4 h-auto w-full"},{default:p(()=>[l.loading?(w(),$("div",ti,e[20]||(e[20]=[t("div",{class:"spinner-border text-primary",role:"status"},[t("span",{class:"sr-only"},"Loading...")],-1)]))):(w(),$("div",li,[t("div",oi,[t("button",{type:"button",class:"bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover rounded-md px-4 py-2 font-semibold",onClick:e[0]||(e[0]=(...f)=>l.cancel&&l.cancel(...f)),disabled:l.isFormDirty()!==!0||l.submitting}," Cancel ",8,si),t("button",{class:"bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover rounded-md px-4 py-2 font-semibold",type:"submit",disabled:l.isFormDirty()!==!0||l.submitting},[l.submitting?E("",!0):(w(),$("span",ai," Save ")),l.submitting?(w(),$("span",ii,e[21]||(e[21]=[F(" Please wait... "),t("span",{class:"spinner-border spinner-border-sm align-middle ms-2"},null,-1)]))):E("",!0)],8,ni)]),t("div",ri,[t("div",di,[e[22]||(e[22]=t("label",{class:"font-semibold"},"Well name",-1)),i(o,{prop:"nameOrNo"},{default:p(()=>{var f;return[i(a,{value:(f=l.wellInfo)==null?void 0:f.nameOrNo,placeholder:"",name:"nameOrNo",disabled:""},null,8,["value"])]}),_:1})]),l.isSystemAdmin?(w(),$("div",ui,[e[23]||(e[23]=t("label",{class:"font-semibold"},"Company",-1)),i(o,null,{default:p(()=>{var f,c;return[i(a,{value:(c=(f=l.wellInfo)==null?void 0:f.company)==null?void 0:c.name,disabled:""},null,8,["value"])]}),_:1})])):E("",!0),t("div",pi,[e[24]||(e[24]=t("label",{class:"font-semibold"},[F(" Date Time"),t("span",{class:"text-danger-active font-light"},"*")],-1)),i(o,{prop:"reportedAt"},{default:p(()=>[i(r,{type:"datetime",modelValue:l.targetData.reportedAt,"onUpdate:modelValue":e[1]||(e[1]=f=>l.targetData.reportedAt=f),placeholder:"MM/DD/YYYY",format:"MM/DD/YYYY HH:mm",formatValue:"YYYY-MM-DD HH:mm:ss",name:"reportedAt",disabled:!!l.dailyReportId},null,8,["modelValue","disabled"])]),_:1})]),t("div",ci,[e[25]||(e[25]=t("label",{class:"font-semibold"},[F(" Engineers "),t("span",{class:"text-danger-active font-light"},"*")],-1)),i(o,{prop:"engineerId"},{default:p(()=>[i(d,{modelValue:l.targetData.engineerId,"onUpdate:modelValue":e[2]||(e[2]=f=>l.targetData.engineerId=f),options:l.engineerList,placeholder:"Search Engineer",filterable:"",clearable:"",name:"engineerId"},null,8,["modelValue","options"])]),_:1})])]),t("div",fi,[e[26]||(e[26]=t("label",{class:"font-semibold"},"Activity",-1)),i(o,{prop:"activity"},{default:p(()=>[i(a,{modelValue:l.targetData.activity,"onUpdate:modelValue":e[3]||(e[3]=f=>l.targetData.activity=f),type:"textarea",rows:2,name:"activity",placeholder:""},null,8,["modelValue"])]),_:1})]),t("div",mi,[t("div",gi,[t("label",bi,[e[29]||(e[29]=t("span",null,"MD (Measured Depth) (ft)",-1)),i(s,{placement:"top",width:400,trigger:"hover"},{reference:p(()=>e[27]||(e[27]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7"},null,-1)])),default:p(()=>[e[28]||(e[28]=t("span",null," Measured Depth represents the length of the wellbore from the surface to the current drilling depth. It is an input and doesn't require a calculation. ",-1))]),_:1})]),i(o,{prop:"measuredDepth"},{default:p(()=>[i(a,{type:"number",controls:!1,step:"any",modelValue:l.targetData.measuredDepth,"onUpdate:modelValue":e[4]||(e[4]=f=>l.targetData.measuredDepth=f),placeholder:"",name:"measuredDepth"},null,8,["modelValue"])]),_:1})]),t("div",hi,[t("label",vi,[e[32]||(e[32]=t("span",null,"TVD (True Vertical Depth) (ft)",-1)),i(s,{placement:"top",width:400,trigger:"hover"},{reference:p(()=>e[30]||(e[30]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7"},null,-1)])),default:p(()=>[e[31]||(e[31]=t("span",null," True Vertical Depth represents the vertical distance from the wellbore to the target or the depth below the surface at which the wellbore reaches the target zone. TVD may require calculation based on the well's inclination and azimuth. ",-1))]),_:1})]),i(o,{prop:"trueVerticalDepth"},{default:p(()=>[i(a,{type:"number",controls:!1,step:"any",modelValue:l.targetData.trueVerticalDepth,"onUpdate:modelValue":e[5]||(e[5]=f=>l.targetData.trueVerticalDepth=f),placeholder:"",name:"trueVerticalDepth"},null,8,["modelValue"])]),_:1})]),t("div",yi,[t("label",wi,[e[35]||(e[35]=t("span",null,"Inc. (Inclination) (deg)",-1)),i(s,{placement:"top",width:400,trigger:"hover"},{reference:p(()=>e[33]||(e[33]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7"},null,-1)])),default:p(()=>[e[34]||(e[34]=t("span",null," Inclination is the angle at which the wellbore deviates from vertical. It is an input and doesn't require a calculation. ",-1))]),_:1})]),i(o,{prop:"inclination"},{default:p(()=>[i(a,{type:"number",controls:!1,step:"any",modelValue:l.targetData.inclination,"onUpdate:modelValue":e[6]||(e[6]=f=>l.targetData.inclination=f),placeholder:"",name:"inclination"},null,8,["modelValue"])]),_:1})]),t("div",Si,[t("label",$i,[e[38]||(e[38]=t("span",null,"Azi (Azimuth) (deg)",-1)),i(s,{placement:"top",width:400,trigger:"hover"},{reference:p(()=>e[36]||(e[36]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7"},null,-1)])),default:p(()=>[e[37]||(e[37]=t("span",null," Azimuth is the compass direction in which the wellbore is drilled horizontally. It is an input and doesn't require a calculation. ",-1))]),_:1})]),i(o,{prop:"azimuth"},{default:p(()=>[i(a,{type:"number",controls:!1,step:"any",modelValue:l.targetData.azimuth,"onUpdate:modelValue":e[7]||(e[7]=f=>l.targetData.azimuth=f),placeholder:"",name:"azimuth"},null,8,["modelValue"])]),_:1})])]),t("div",xi,[t("div",ki,[t("label",Di,[e[41]||(e[41]=t("span",null,"WOB (Weight on Bit) (lbf)",-1)),i(s,{placement:"top",width:400,trigger:"hover"},{reference:p(()=>e[39]||(e[39]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7"},null,-1)])),default:p(()=>[e[40]||(e[40]=t("span",null," Weight on Bit is the downward force applied to the drill bit to aid in drilling. It is an input and doesn't require a calculation. ",-1))]),_:1})]),i(o,{prop:"weightOnBit"},{default:p(()=>[i(a,{type:"number",controls:!1,step:"any",modelValue:l.targetData.weightOnBit,"onUpdate:modelValue":e[8]||(e[8]=f=>l.targetData.weightOnBit=f),placeholder:"",name:"weightOnBit"},null,8,["modelValue"])]),_:1})]),t("div",Vi,[t("label",Ii,[e[44]||(e[44]=t("span",null,"Rot. Wt. (Rotary Weight) (lbf)",-1)),i(s,{placement:"top",width:400,trigger:"hover"},{reference:p(()=>e[42]||(e[42]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7"},null,-1)])),default:p(()=>[e[43]||(e[43]=t("span",null," Rotary Weight represents the weight applied to the drill bit due to the rotation of the drill string. It is an input and doesn't require a calculation. ",-1))]),_:1})]),i(o,{prop:"rotaryWeight"},{default:p(()=>[i(a,{type:"number",controls:!1,step:"any",modelValue:l.targetData.rotaryWeight,"onUpdate:modelValue":e[9]||(e[9]=f=>l.targetData.rotaryWeight=f),placeholder:"",name:"rotaryWeight"},null,8,["modelValue"])]),_:1})]),t("div",Pi,[t("label",Ci,[e[47]||(e[47]=t("span",null,"S/O Wt. (Standoff Weight) (lbf)",-1)),i(s,{placement:"top",width:400,trigger:"hover"},{reference:p(()=>e[45]||(e[45]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7"},null,-1)])),default:p(()=>[e[46]||(e[46]=t("span",null," Standoff Weight is the weight applied to the drill bit due to the hydraulic pressure exerted by the drilling mud. It is an input and doesn't require a calculation. ",-1))]),_:1})]),i(o,{prop:"standOffWeight"},{default:p(()=>[i(a,{type:"number",controls:!1,step:"any",modelValue:l.targetData.standOffWeight,"onUpdate:modelValue":e[10]||(e[10]=f=>l.targetData.standOffWeight=f),placeholder:"",name:"standOffWeight"},null,8,["modelValue"])]),_:1})]),t("div",Ti,[t("label",Mi,[e[50]||(e[50]=t("span",null,"P/U Wt. (Pull-Up Weight) (lbf)",-1)),i(s,{placement:"top",width:400,trigger:"hover"},{reference:p(()=>e[48]||(e[48]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7"},null,-1)])),default:p(()=>[e[49]||(e[49]=t("span",null," Pull-Up Weight is the weight applied to the drill bit during the tripping process when the drill string is pulled out of the hole. It is an input and doesn't require a calculation. ",-1))]),_:1})]),i(o,{prop:"pullUpWeight"},{default:p(()=>[i(a,{type:"number",controls:!1,step:"any",modelValue:l.targetData.pullUpWeight,"onUpdate:modelValue":e[11]||(e[11]=f=>l.targetData.pullUpWeight=f),placeholder:"",name:"pullUpWeight"},null,8,["modelValue"])]),_:1})])]),t("div",Fi,[t("div",Ni,[t("label",_i,[e[53]||(e[53]=t("span",null,"RPM (Revolutions Per Minute) (rpm)",-1)),i(s,{placement:"top",width:400,trigger:"hover"},{reference:p(()=>e[51]||(e[51]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7"},null,-1)])),default:p(()=>[e[52]||(e[52]=t("span",null," RPM represents the rotational speed of the drill bit. It is an input and doesn't require a calculation. ",-1))]),_:1})]),i(o,{prop:"revolutionsPerMinute"},{default:p(()=>[i(a,{type:"number",controls:!1,step:"any",modelValue:l.targetData.revolutionsPerMinute,"onUpdate:modelValue":e[12]||(e[12]=f=>l.targetData.revolutionsPerMinute=f),placeholder:"",name:"revolutionsPerMinute"},null,8,["modelValue"])]),_:1})]),t("div",Hi,[t("label",Ai,[e[56]||(e[56]=t("span",null,"ROP (Rate of Penetration) (ft/hr)",-1)),i(s,{placement:"top",width:400,trigger:"hover"},{reference:p(()=>e[54]||(e[54]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7"},null,-1)])),default:p(()=>[e[55]||(e[55]=t("span",null," Rate of Penetration is the speed at which the drill bit advances in feet per hour. It is a calculation based on the depth drilled (ft) and the drilling interval (time). ",-1))]),_:1})]),i(o,{prop:"rateOfPenetration"},{default:p(()=>[i(a,{type:"number",controls:!1,step:"any",modelValue:l.targetData.rateOfPenetration,"onUpdate:modelValue":e[13]||(e[13]=f=>l.targetData.rateOfPenetration=f),placeholder:"",name:"rateOfPenetration"},null,8,["modelValue"])]),_:1})]),t("div",zi,[t("label",qi,[e[59]||(e[59]=t("span",null,"Drilling Interval",-1)),i(s,{placement:"top",width:400,trigger:"hover"},{reference:p(()=>e[57]||(e[57]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7"},null,-1)])),default:p(()=>[e[58]||(e[58]=t("span",null," The drilling interval is the time it takes to drill a certain depth. It is an input and can be used in the ROP calculation. ",-1))]),_:1})]),i(o,{prop:"drillingInterval"},{default:p(()=>[i(a,{modelValue:l.targetData.drillingInterval,"onUpdate:modelValue":e[14]||(e[14]=f=>l.targetData.drillingInterval=f),placeholder:"",name:"drillingInterval"},null,8,["modelValue"])]),_:1})]),t("div",Bi,[t("label",ji,[e[62]||(e[62]=t("span",null,"Formation",-1)),i(s,{placement:"top",width:400,trigger:"hover"},{reference:p(()=>e[60]||(e[60]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7"},null,-1)])),default:p(()=>[e[61]||(e[61]=t("span",null," Formation refers to the geological strata or rock types encountered while drilling. It is an input and doesn't require a calculation. ",-1))]),_:1})]),i(o,{prop:"formation"},{default:p(()=>[i(a,{modelValue:l.targetData.formation,"onUpdate:modelValue":e[15]||(e[15]=f=>l.targetData.formation=f),placeholder:"",name:"formation"},null,8,["modelValue"])]),_:1})])]),t("div",Ei,[t("div",Ri,[t("label",Oi,[e[65]||(e[65]=t("span",null,"Depth Drilled (ft)",-1)),i(s,{placement:"top",width:400,trigger:"hover"},{reference:p(()=>e[63]||(e[63]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7"},null,-1)])),default:p(()=>[e[64]||(e[64]=t("span",null," Depth Drilled represents the amount of wellbore drilled during a specific drilling operation. It is an input and can be used in the ROP calculation. ",-1))]),_:1})]),i(o,{prop:"depthDrilled"},{default:p(()=>[i(a,{type:"number",controls:!1,step:"any",modelValue:l.targetData.depthDrilled,"onUpdate:modelValue":e[16]||(e[16]=f=>l.targetData.depthDrilled=f),placeholder:"",name:"depthDrilled",disabled:""},null,8,["modelValue"])]),_:1})]),t("div",Li,[t("label",Wi,[e[68]||(e[68]=t("span",null,"Total String Length = MD",-1)),i(s,{placement:"top",width:400,trigger:"hover"},{reference:p(()=>e[66]||(e[66]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7"},null,-1)])),default:p(()=>[e[67]||(e[67]=t("span",null," Total String Length is the overall length of the drill string, which is typically equal to the Measured Depth (MD). It is an input and doesn't require a separate calculation. ",-1))]),_:1})]),i(o,{prop:"totalStringLength"},{default:p(()=>[i(a,{type:"number",controls:!1,step:"any",modelValue:l.targetData.totalStringLength,"onUpdate:modelValue":e[17]||(e[17]=f=>l.targetData.totalStringLength=f),placeholder:"",name:"totalStringLength"},null,8,["modelValue"])]),_:1})]),t("div",Ui,[t("label",Gi,[e[71]||(e[71]=t("span",null,"Total Length = MD",-1)),i(s,{placement:"top",width:400,trigger:"hover"},{reference:p(()=>e[69]||(e[69]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7"},null,-1)])),default:p(()=>[e[70]||(e[70]=t("span",null," Total Length can refer to the overall length of the wellbore from the surface to the target zone. It may require a calculation based on MD and TVD: Total Length = √(MD^2 + TVD^2) ",-1))]),_:1})]),i(o,{prop:"totalLength"},{default:p(()=>[i(a,{type:"number",controls:!1,step:"any",modelValue:l.targetData.totalLength,"onUpdate:modelValue":e[18]||(e[18]=f=>l.targetData.totalLength=f),placeholder:"",name:"totalLength"},null,8,["modelValue"])]),_:1})]),t("div",Yi,[t("label",Ji,[e[74]||(e[74]=t("span",null,"TFA (Total Flow Area) (in^2)",-1)),i(s,{placement:"top",width:400,trigger:"hover"},{reference:p(()=>e[72]||(e[72]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7"},null,-1)])),default:p(()=>[e[73]||(e[73]=t("span",null,[F(' "Total Flow Area" measured in square inches (in^2). Total Flow Area in this context typically pertains to the collective cross-sectional area available for the passage of drilling mud or fluids through the components of the drill string, such as drill pipes, drill bits, and nozzles.'),t("br"),t("br"),F(" Total Flow Area is an essential parameter to consider when designing and optimizing the drill string and associated equipment. It affects the flow rate, fluid velocity, and pressure drop within the drill string. Properly managing TFA is important for maintaining effective drilling fluid circulation, ensuring efficient cooling of the drill bit, and carrying drilled cuttings and formation samples to the surface for mud logging and analysis. Adjusting TFA, for example by selecting appropriate nozzles or pipe sizes, can help optimize drilling performance and hole cleaning. ")],-1))]),_:1})]),i(o,{prop:"tfa"},{default:p(()=>[i(a,{type:"number",controls:!1,step:"any",modelValue:l.targetData.tfa,"onUpdate:modelValue":e[19]||(e[19]=f=>l.targetData.tfa=f),placeholder:"",name:"tfa",disabled:""},null,8,["modelValue"])]),_:1})])])]))]),_:1},8,["onSubmit","model","rules"])}const Ht=Q(ei,[["render",Qi],["__scopeId","data-v-2481adcf"]]),Ki=[{value:pe.CasedHole,title:"What is a Cased Hole?",content:'<p class="mb-0"> Cased holes are sections of a wellbore where casing (metallic or non-metallic pipe) is installed to provide structural support, isolation, and control of fluids. The type of cased hole depends on its specific purpose and location within the well. Here are some common types of cased holes that you may encounter:<br/><br/>Conductor Casing: Conductor casing is the first casing string set in a well, and it extends from the surface to a certain depth below the ground or seabed. Its primary function is to provide structural support for the wellbore and prevent collapse of the hole near the surface.<br/><br/>Surface Casing: Surface casing is set below the conductor casing and typically extends to a shallower depth. It serves to isolate shallow, potentially unstable formations, protect freshwater aquifers, and provide a foundation for the subsequent casing strings.<br/><br/>Intermediate Casing: Intermediate casing is set at intermediate depths within the wellbore. Its purpose varies but may include isolating troublesome zones, controlling lost circulation, or providing additional structural support.<br/><br/>Production Casing: Production casing is set to isolate and support the production zone or reservoir. It serves as a conduit for hydrocarbon production from the reservoir to the surface. Its size and specifications depend on the production requirements.<br/><br/>Liner: A liner is a casing string that is installed inside another casing string, typically in the lower part of the well. It may be used to extend the casing to a greater depth, isolate problematic zones, or provide additional support without reaching the surface.<br/><br/>Scab Liner: A scab liner is a relatively short casing string used to repair a section of damaged or corroded casing. It is typically installed inside the damaged section to restore well integrity.<br/><br/>Cemented Casing: This refers to casing strings that have been cemented in place to ensure wellbore integrity, isolation of formations, and zonal isolation.<br/><br/>Slotted Liner or Screen: Slotted liners or screens are used in wells to allow fluid flow from the formation while preventing the ingress of sand or other debris. They are often used in open-hole completions.<br/><br/>Hanger Casing: Hanger casing is used in wells with subsea completions, where a casing hanger supports the casing strings within a subsea wellhead assembly.<br/><br/>Packer Casing: Packer casing is used in wells with packer systems, which are devices that create seals or isolate zones within the wellbore.</p>'},{value:pe.OpenHold,title:"What is an Open Hole?",content:`<p class="mb-0"> An "Open Hole" refers to the section of a wellbore that has been drilled but is not yet cased or lined with casing or tubing. In open-hole drilling, the wellbore is left in its natural state, allowing for direct contact with the surrounding geological formations. Open holes are typically encountered during the drilling process before casing is installed. The purpose of open-hole sections varies and may include geological evaluation, wellbore stability assessment, and the potential for hydrocarbon discovery.<br/><br/>Types of Open Hole:<br/><br/>Different types of open holes can be encountered during drilling operations, depending on the well's objectives and geological conditions. Some common types of open holes include:<br/><br/>Exploratory Open Hole: This is the initial open-hole section drilled to explore the subsurface and evaluate geological formations for potential hydrocarbon reservoirs.<br/><br/>Production Open Hole: In some cases, a well may have an open-hole production section where hydrocarbons are produced directly from the formation without casing.<br/><br/>Logging Open Hole: An open-hole section may be specifically drilled to facilitate logging tools, such as wireline or logging while drilling (LWD/MWD) tools, to collect formation evaluation data.<br/><br/>Evaluation Open Hole: Open holes are often drilled for the purpose of evaluating formation properties, including porosity, permeability, and lithology.<br/><br/>Directional Open Hole: In directional drilling, open-hole sections may be drilled with specific inclinations and azimuths to intersect target zones.<br/><br/>Geotechnical Open Hole: Open-hole sections may be drilled for geotechnical purposes, such as assessing rock and soil properties for civil engineering projects.</p>`},{value:pe.DrillString,title:"What is a Drill String?",content:'<p class="mb-0">A "Drill String" refers to the assembly of drilling tools, drill pipes, and other components used in drilling operations to advance the wellbore into the subsurface. The drill string is typically composed of multiple sections of drill pipe, each joined by tool joints, and may include other specialized tools and equipment.<br/><br/>Types of Drill Strings:<br/><br/>Different types of drill strings may be used in drilling operations, depending on the drilling objectives, well design, and geological conditions. Some common types of drill strings include:<br/><br/>Standard Drill String: This is a typical drill string configuration consisting of drill pipe sections joined by tool joints. It is commonly used in various drilling applications.<br/><br/>Heavyweight Drill String: Heavyweight drill strings include heavyweight drill pipe or drill collars to provide additional weight on bit (WOB) and stability in challenging drilling conditions.<br/><br/>irectional Drill String: Directional drilling often involves specialized drill string configurations designed to achieve specific wellbore trajectories, including bent sub assemblies and steerable tools.<br/><br/>Slim Hole Drill String: Slim hole drill strings are used in narrow wellbores or when smaller-diameter wells are desired, such as in slim hole drilling or geothermal drilling.<br/><br/>Wireline Drill String: Wireline drill strings are used in wireline coring and well logging operations, where tools are lowered into the wellbore on a wireline cable.<br/><br/>Coiled Tubing: Coiled tubing drill strings consist of continuous tubing wound on a reel and are used in various well intervention and completion operations.</p>'},{value:pe.Bits,title:"What is a Drill Bit?",content:`<p class="mb-0">A "Drill Bit" is a cutting tool used in drilling operations to create a hole in the earth's subsurface. Drill bits are attached to the bottom of the drill string and are responsible for breaking and removing rock or other materials as the wellbore is advanced. Drill bits are crucial components in the drilling process and come in various types and designs to suit different geological conditions and drilling objectives.<br/><br/>Types of Drill Bits:<br/><br/>There are several types of drill bits used in drilling operations, each designed for specific applications and geological conditions. Some common types of drill bits include:<br/><br/>PDC (Polycrystalline Diamond Compact) Bits: PDC bits use diamond-impregnated cutters and are known for their efficiency in drilling through soft to medium-hard formations.<br/><br/>Roller Cone Bits: Roller cone bits have rotating cones with hardened teeth that crush and cut the rock. They are versatile and can be used in a wide range of formations.<br/><br/>Diamond-Impregnated Bits: These bits have a matrix embedded with industrial diamonds. They are suitable for drilling through hard and abrasive formations.<br/><br/>Fixed Cutter Bits: Fixed cutter bits, such as diamond bits and PDC bits, have non-rotating cutting elements that shear or scrape the rock. They are used in various applications, including oil and gas drilling and mining.<br/><br/>Tri-cone Bits: Tri-cone bits have three cones with different tooth designs and are often used in medium to hard formations.<br/><br/>Reaming Bits: Reaming bits are used to enlarge the hole diameter after the initial drilling and can be essential for well completion and casing installation.<br/><br/>Directional and Steerable Bits: These bits are designed for directional drilling and are equipped with mechanisms that allow them to control the wellbore's trajectory.<br/><br/>Underreamer Bits: Underreamer bits are used to enlarge the hole diameter below a restriction in the wellbore, such as a previously set casing.<br/><br/>The choice of drill bit type depends on the geological formations, drilling objectives, and wellbore conditions. Drill bit selection is a critical factor in drilling efficiency and wellbore integrity.</p>`},{value:pe.Nozzles,title:"What is a Nozzle?",content:'<p class="mb-0">In the context of drilling operations, a "Nozzle" refers to a specialized component that directs the flow of drilling fluids, such as drilling mud or drilling fluid additives, from the mud pumps into the wellbore. Nozzles are typically located in the drill string or drill pipe near the bottom of the well, and they play a crucial role in various aspects of drilling, including cleaning the bottom of the hole, cooling the drill bit, and optimizing fluid flow.<br/><br/>Types of Nozzles:<br/><br/>There are several types of nozzles used in drilling operations, each designed for specific purposes and fluid flow characteristics. Some common types of nozzles include:<br/><br/>Fan Nozzles: These nozzles emit a fan-shaped spray pattern, which is useful for cleaning the bottom of the wellbore and distributing drilling fluids evenly.<br/><br/>Jet Nozzles: Jet nozzles produce a high-velocity, focused stream of drilling fluid. They are often used for drilling hard formations and cutting through tough materials.<br/><br/>Crossflow Nozzles: Crossflow nozzles have multiple orifices that emit fluid in multiple directions simultaneously, improving hole cleaning efficiency.<br/><br/>Variable Flow Nozzles: These nozzles allow for adjustable flow rates, enabling operators to control the amount of fluid being pumped through them.<br/><br/>Solid Nozzles: Solid nozzles have a single, fixed orifice and are commonly used for specific applications, such as fluid sampling or pressure testing.<br/><br/>Replaceable Nozzles: Some nozzles are designed to be easily replaceable, allowing for quick changes to optimize drilling performance.<br/><br/>The choice of nozzle type and size depends on the drilling objectives, wellbore conditions, and the specific requirements of the drilling operation. Proper nozzle selection is important for efficient drilling fluid management and hole cleaning.</p>'},{value:Ve.Solids,title:"What is Solid?",content:`<p class="mb-0">Characterization and measurement of various solid components present in the drilling mud. It includes parameters related to the composition and properties of solids, which can significantly impact the mud's performance and behavior.</p>`}],Xi=J({name:"customize-layout",props:{currentTab:{type:Number,required:!0},isVisible:{type:Boolean,required:!0},toggleCustomize:{type:Function,required:!0}},components:{SvgIcon:K},setup(l){return{currentInfo:ot(()=>Ki.filter(S=>S.value===l.currentTab)[0]||null)}}}),Zi={key:0,class:"fixed top-0 right-0 bottom-0 left-0 z-40 flex items-center"},er={class:"relative bg-card-background text-card-text-light max-h-11/12 w-11/12 flex flex-col items-center gap-3 mx-auto my-6 p-5 rounded-xl overflow-y-scroll md:overflow-hidden md:w-1/2 md:h-1/2 md:text-lg"},tr={class:"h-auto w-full flex flex-row items-center justify-between"},lr={class:"text-lg font-bold"},or={class:"h-auto w-full overflow-y-auto"},sr=["innerHTML"];function nr(l,e,S,h,m,n){var o,r;const a=k("SvgIcon");return l.isVisible?(w(),$("div",Zi,[e[1]||(e[1]=t("div",{class:"absolute inset-0 bg-sidebar-background opacity-25"},null,-1)),t("div",er,[t("div",tr,[t("h3",lr,D(((o=l.currentInfo)==null?void 0:o.title)||""),1),t("span",{class:"cursor-pointer",onClick:e[0]||(e[0]=(...d)=>l.toggleCustomize&&l.toggleCustomize(...d))},[i(a,{icon:"closeModalIcon"})])]),t("div",or,[(r=l.currentInfo)!=null&&r.content?(w(),$("div",{key:0,innerHTML:l.currentInfo.content},null,8,sr)):E("",!0)])])])):E("",!0)}const ar=Q(Xi,[["render",nr]]),ir={[pe.WellInformation]:Ht,[pe.CasedHole]:Pt,[pe.OpenHold]:_t,[pe.DrillString]:Tt,[pe.Bits]:Vt,[pe.Nozzles]:Ft},rr=J({name:"daily-report-general",components:{WellInformation:Ht,CasedHole:Pt,OpenHold:_t,DrillString:Tt,Bits:Vt,Nozzles:Ft,Customize:ar},props:{setChildActiveTab:{type:Function,required:!1,default:()=>{}}},setup(l){const e=v(pe.WellInformation),S=v(null),h=ot(()=>ir[e.value]),m=v(!1),n=v(pe.WellInformation),a=s=>{const u=s.target;e.value=Number(u.getAttribute("data-tab-index")),l!=null&&l.setChildActiveTab&&l.setChildActiveTab(e.value)},o=async s=>{e.value!==pe.WellInformation?(a(s),n.value=e.value):d()?se.incompleteFormAlert({onConfirmed:()=>{a(s)}},"You have unsaved changes. Are you sure you want to leave?"):(a(s),n.value=e.value)},r=()=>{n.value=e.value,m.value=!m.value},d=()=>{var s,u;return S!=null&&S.value&&((s=S==null?void 0:S.value)!=null&&s.isFormDirty)?(u=S==null?void 0:S.value)==null?void 0:u.isFormDirty():!1};return{handleActiveTab:o,isFormOfChildTabDirty:d,toggleCustomize:r,currentChildTab:S,EGeneralTab:pe,generalTabs:cl,tabIndex:e,currentComponent:h,currentHelpTab:n,displayCustomize:m}}}),dr={class:"h-auto w-11/12 mx-auto flex flex-wrap items-end justify-start gap-1 lg:w-4/5 lg:mx-auto lg:min-w-[1560px]",role:"tablist"},ur=["data-tab-index"],pr={class:"h-auto w-11/12 flex flex-col gap-6 mx-auto px-3 py-4"};function cr(l,e,S,h,m,n){const a=k("Customize");return w(),$(O,null,[t("ul",dr,[(w(!0),$(O,null,Z(l.generalTabs,o=>(w(),$("li",{class:"nav-item",key:o.value},[t("div",{class:be(["whitespace-nowrap cursor-pointer font-semibold px-4 py-3 relative md:ml-0.5 md:-mb-0.5",{"text-active-dark z-10 md:shadow-lg md:bg-card-background md:rounded-t-xl md:text-active":l.tabIndex===(o==null?void 0:o.value),"text-inactive rounded-t-lg":l.tabIndex!==(o==null?void 0:o.value)}]),onClick:e[0]||(e[0]=r=>l.handleActiveTab(r)),"data-tab-index":o.value,role:"tab"},D(o.label),11,ur)]))),128))]),t("div",{class:be(["bg-card-background text-card-text-light w-11/12 mx-auto mb-4 h-auto rounded-xl border-2 border-active-border relative md:-mt-0.5 lg:w-4/5 lg:mx-auto lg:min-w-[1560px]",{"md:rounded-tl-none":l.tabIndex===l.EGeneralTab.WellInformation}])},[t("div",pr,[(w(),te(it(l.currentComponent),{ref:"currentChildTab",showCustomize:l.toggleCustomize},null,8,["showCustomize"]))]),i(a,{currentTab:l.currentHelpTab,isVisible:l.displayCustomize,toggleCustomize:l.toggleCustomize},null,8,["currentTab","isVisible","toggleCustomize"])],2)],64)}const At=Q(rr,[["render",cr]]),zt=ne("targetProperty",()=>({getTargetPropertyDetails:async({wellId:S,callback:h})=>{var a;const m=g.get(h,"onSuccess",g.noop),n=g.get(h,"onFinish",g.noop);try{V.setHeader();const o=await V.get(`targetProperties/${S}`);m(((a=o.data)==null?void 0:a.data)||o.data)}catch(o){H(o,h)}finally{n()}},updateTargetProperty:async({id:S,params:h,callback:m})=>{var o;const n=g.get(m,"onSuccess",g.noop),a=g.get(m,"onFinish",g.noop);try{V.setHeader();const r=await V.put(`targetProperties/${S}`,h);n(((o=r.data)==null?void 0:o.data)||r.data)}catch(r){H(r,m)}finally{a()}}})),fr=J({name:"edit-targeted-properties-modal",components:{SvgIcon:K},props:{isVisible:{type:Boolean,required:!0},close:{type:Function,required:!0},loadPage:{type:Function,default:()=>{},required:!0},wellId:{type:String,required:!0}},setup(l){const e=zt(),S={fluidType:null,mudWeight:null,funnelViscosity:null,plasticViscosity:null,yieldPoint:null,apiFiltrate:null,apiCakeThickness:null,pH:null,mudAlkalinity:null,filtrateAlkalinity:null,chlorides:null,totalHardness:null,linearGelStrengthPercent:null,rpm:null},h=v(JSON.parse(JSON.stringify(S))),m=v(null),n=v(!1),a=v("");Y(()=>l.isVisible,y=>{var M;y==!0?o():(c(),(M=m==null?void 0:m.value)==null||M.resetFields())});const o=async()=>{n.value=!0,e.getTargetPropertyDetails({wellId:l==null?void 0:l.wellId,callback:{onSuccess:y=>{h.value=JSON.parse(JSON.stringify(y))},onFinish:y=>{n.value=!1}}})},r=async y=>{var M;n.value=!0,e.updateTargetProperty({id:((M=h==null?void 0:h.value)==null?void 0:M.id)||"",params:y,callback:{onSuccess:P=>{l!=null&&l.loadPage&&(l==null||l.loadPage()),s(),console.log("target property updated")},onFinish:P=>{n.value=!1}}})},d=y=>{a.value=y.toString()},s=()=>{l.close()},u=v({fluidType:[{required:!0,message:"Please select Fluid Type",trigger:"change"}]}),f=()=>{m.value&&m.value.validate(y=>{var M,P,x,b,T,q,I,C,B,A,_,N,j,z;if(y){const R={fluidType:Number((M=h==null?void 0:h.value)==null?void 0:M.fluidType),mudWeight:Number((P=h==null?void 0:h.value)==null?void 0:P.mudWeight),funnelViscosity:Number((x=h==null?void 0:h.value)==null?void 0:x.funnelViscosity),plasticViscosity:Number((b=h==null?void 0:h.value)==null?void 0:b.plasticViscosity),yieldPoint:Number((T=h==null?void 0:h.value)==null?void 0:T.yieldPoint),apiFiltrate:Number((q=h==null?void 0:h.value)==null?void 0:q.apiFiltrate),apiCakeThickness:Number((I=h==null?void 0:h.value)==null?void 0:I.apiCakeThickness),pH:Number((C=h==null?void 0:h.value)==null?void 0:C.pH),mudAlkalinity:Number((B=h==null?void 0:h.value)==null?void 0:B.mudAlkalinity),filtrateAlkalinity:Number((A=h==null?void 0:h.value)==null?void 0:A.filtrateAlkalinity),chlorides:Number((_=h==null?void 0:h.value)==null?void 0:_.chlorides),totalHardness:Number((N=h==null?void 0:h.value)==null?void 0:N.totalHardness),linearGelStrengthPercent:Number((j=h==null?void 0:h.value)==null?void 0:j.linearGelStrengthPercent),rpm:Number((z=h==null?void 0:h.value)==null?void 0:z.rpm)};r(R)}})},c=()=>{h.value=JSON.parse(JSON.stringify(S))};return{rules:u,loading:n,targetData:h,fluidTypeOptions:lt,sampleFromOptions:tt,formRef:m,setId:d,closeModal:s,submit:f,reset:c}}}),mr={key:0,class:"fixed top-0 right-0 bottom-0 left-0 z-40 flex items-center"},gr={class:"relative bg-card-background text-card-text-light max-h-11/12 w-11/12 flex flex-col items-center gap-3 mx-auto my-6 p-5 rounded-xl overflow-y-scroll md:overflow-hidden lg:w-3/5 lg:h-auto"},br={class:"flex flex-row h-auto w-full items-center justify-between"},hr={class:"h-auto w-full flex flex-col gap-3 md:flex-row"},vr={class:"h-auto w-full flex flex-col gap-3"},yr={class:"flex flex-col gap-1"},wr={class:"flex flex-col gap-1"},Sr={class:"flex flex-col gap-1"},$r={class:"flex flex-col gap-1"},xr={class:"flex flex-col gap-1"},kr={class:"flex flex-col gap-1"},Dr={class:"flex flex-col gap-1"},Vr={class:"flex flex-col gap-1"},Ir={class:"h-auto w-full flex flex-col gap-3"},Pr={class:"flex flex-col gap-1"},Cr={class:"flex flex-col gap-1"},Tr={class:"flex flex-col gap-1"},Mr={class:"flex flex-col gap-1"},Fr={class:"flex flex-col gap-1"},Nr={class:"flex flex-col gap-1"},_r={class:"flex flex-col gap-1"},Hr={class:"h-auto w-full flex flex-row items-center gap-2"},Ar=["disabled"],zr=["disabled"],qr={key:0,class:"indicator-label"},Br={key:1,class:"indicator-progress"};function jr(l,e,S,h,m,n){const a=k("SvgIcon"),o=k("el-popover"),r=k("el-option"),d=k("el-select"),s=k("el-form-item"),u=k("el-input"),f=k("el-form");return l.isVisible?(w(),$("div",mr,[e[64]||(e[64]=t("div",{class:"absolute inset-0 bg-sidebar-background opacity-25"},null,-1)),t("div",gr,[t("div",br,[e[17]||(e[17]=t("h3",{class:"text-lg font-bold"},"Edit Targeted Properties",-1)),t("span",{class:"cursor-pointer",onClick:e[0]||(e[0]=(...c)=>l.closeModal&&l.closeModal(...c))},[i(a,{icon:"closeModalIcon"})])]),i(f,{id:"edit_targeted_properties_form",onSubmit:ie(l.submit,["prevent"]),model:l.targetData,rules:l.rules,ref:"formRef",class:"h-auto w-full font-semibold"},{default:p(()=>[t("div",hr,[t("div",vr,[t("div",yr,[t("label",null,[e[20]||(e[20]=F("Fluid Type")),e[21]||(e[21]=t("span",{class:"text-danger-active font-light"},"*",-1)),i(o,{placement:"top",width:400,trigger:"hover"},{reference:p(()=>e[18]||(e[18]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7"},null,-1)])),default:p(()=>[e[19]||(e[19]=t("span",null,[F(" Water-based drilling muds are the most common type. They use water as the base fluid and may contain various additives. The key differences for WBM include water quality, filtration properties, and clay content."),t("br"),t("br"),F(" Oil-based drilling muds use oil as the base fluid. Key considerations for OBM include oil composition, oil/water ratio, and rheological properties."),t("br"),t("br"),F(" Synthetic-based drilling muds use synthetic fluids as the base, such as esters or olefins. Specific data requirements depend on the type of synthetic fluid used. ")],-1))]),_:1})]),i(s,{prop:"fluidType"},{default:p(()=>[i(d,{modelValue:l.targetData.fluidType,"onUpdate:modelValue":e[1]||(e[1]=c=>l.targetData.fluidType=c),placeholder:"",clearable:""},{default:p(()=>[(w(!0),$(O,null,Z(l.fluidTypeOptions,c=>(w(),te(r,{key:c.value,label:c.label,value:c.value,name:"fluidType"},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),t("div",wr,[t("label",null,[e[24]||(e[24]=F(" MW (ppg or lbs/gal)")),e[25]||(e[25]=t("span",{class:"text-danger-active font-light"},"*",-1)),i(o,{placement:"bottom",width:400,trigger:"hover"},{reference:p(()=>e[22]||(e[22]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7 me-4"},null,-1)])),default:p(()=>[e[23]||(e[23]=t("span",null," Mud Weight, also known as mud density, is the density of the drilling mud, typically measured in pounds per gallon (ppg). For different types of drilling fluids, the density requirements can vary. ",-1))]),_:1})]),i(s,{prop:"mudWeight"},{default:p(()=>[i(u,{type:"number",controls:!1,step:"any",modelValue:l.targetData.mudWeight,"onUpdate:modelValue":e[2]||(e[2]=c=>l.targetData.mudWeight=c),placeholder:"",name:"mudWeight"},null,8,["modelValue"])]),_:1})]),t("div",Sr,[t("label",null,[e[28]||(e[28]=t("span",null,"Funnel Viscosity (sec/qt) ",-1)),i(o,{placement:"top",width:400,trigger:"hover"},{reference:p(()=>e[26]||(e[26]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7"},null,-1)])),default:p(()=>[e[27]||(e[27]=t("span",null," The funnel viscosity measures the thickness or viscosity of the drilling mud. It is typically measured in seconds per quart (sec/qt). ",-1))]),_:1})]),i(s,{prop:"funnelViscosity"},{default:p(()=>[i(u,{type:"number",controls:!1,step:"any",modelValue:l.targetData.funnelViscosity,"onUpdate:modelValue":e[3]||(e[3]=c=>l.targetData.funnelViscosity=c),placeholder:"",name:"funnelViscosity"},null,8,["modelValue"])]),_:1})]),t("div",$r,[t("label",null,[e[31]||(e[31]=t("span",null,"PV (Plastic Viscosity) (cP)",-1)),i(o,{placement:"top",width:400,trigger:"hover"},{reference:p(()=>e[29]||(e[29]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7"},null,-1)])),default:p(()=>[e[30]||(e[30]=t("span",null," Plastic Viscosity is a measure of the resistance to flow of the drilling mud. It is typically measured in centipoise (cP). ",-1))]),_:1})]),i(s,{prop:"plasticViscosity"},{default:p(()=>[i(u,{type:"number",controls:!1,step:"any",modelValue:l.targetData.plasticViscosity,"onUpdate:modelValue":e[4]||(e[4]=c=>l.targetData.plasticViscosity=c),placeholder:"",name:"plasticViscosity"},null,8,["modelValue"])]),_:1})]),t("div",xr,[t("label",null,[e[34]||(e[34]=t("span",null,"Yield Point (YP) (lbf/100ft2)",-1)),i(o,{placement:"top",width:400,trigger:"hover"},{reference:p(()=>e[32]||(e[32]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7 me-4"},null,-1)])),default:p(()=>[e[33]||(e[33]=t("span",null," Yield Point is the amount of force required to initiate mud flow. It's typically measured in pounds per 100 square feet (lbf/100ft^2). ",-1))]),_:1})]),i(s,{prop:"yieldPoint"},{default:p(()=>[i(u,{type:"number",controls:!1,step:"any",modelValue:l.targetData.yieldPoint,"onUpdate:modelValue":e[5]||(e[5]=c=>l.targetData.yieldPoint=c),placeholder:"",name:"yieldPoint"},null,8,["modelValue"])]),_:1})]),t("div",kr,[e[35]||(e[35]=t("label",null,[t("span",null,"6 rpm")],-1)),i(s,{prop:"rpm"},{default:p(()=>[i(u,{type:"number",controls:!1,step:"any",modelValue:l.targetData.rpm,"onUpdate:modelValue":e[6]||(e[6]=c=>l.targetData.rpm=c),placeholder:"",name:"rpm"},null,8,["modelValue"])]),_:1})]),t("div",Dr,[t("label",null,[e[38]||(e[38]=t("span",null,"API filtrate (ml/30min)",-1)),i(o,{placement:"top",width:400,trigger:"hover"},{reference:p(()=>e[36]||(e[36]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7"},null,-1)])),default:p(()=>[e[37]||(e[37]=t("span",null,[F(" The volume of mud filtrate that passes through a standard filter paper in 30 minutes, measured in milliliters."),t("br"),t("br"),F(" WBM: API filtrate and cake thickness are commonly measured for Water-Based Mud. The filtration properties may be different for various types of WBM formulations. OBM: Oil-Based Mud may have different filtration characteristics compared to WBM due to the presence of oil. The API filtrate and cake thickness may have specific requirements for OBM. SBM: Synthetic-Based Mud may also have unique filtration properties, and API filtrate and cake thickness measurements should consider the specific synthetic fluid used. ")],-1))]),_:1})]),i(s,{prop:"apiFiltrate"},{default:p(()=>[i(u,{type:"number",controls:!1,step:"any",modelValue:l.targetData.apiFiltrate,"onUpdate:modelValue":e[7]||(e[7]=c=>l.targetData.apiFiltrate=c),placeholder:"",name:"apiFiltrate"},null,8,["modelValue"])]),_:1})]),t("div",Vr,[t("label",null,[e[41]||(e[41]=t("span",null,"API Cake",-1)),i(o,{placement:"top",width:400,trigger:"hover"},{reference:p(()=>e[39]||(e[39]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7 me-4"},null,-1)])),default:p(()=>[e[40]||(e[40]=t("span",null,[F(" The thickness of the filter cake formed by the drilling mud on the filter paper, measured in 1/32 of an inch."),t("br"),t("br"),F(" WBM: API filtrate and cake thickness are commonly measured for Water-Based Mud. The filtration properties may be different for various types of WBM formulations. OBM: Oil-Based Mud may have different filtration characteristics compared to WBM due to the presence of oil. The API filtrate and cake thickness may have specific requirements for OBM. SBM: Synthetic-Based Mud may also have unique filtration properties, and API filtrate and cake thickness measurements should consider the specific synthetic fluid used. ")],-1))]),_:1})]),i(s,{prop:"apiCakeThickness"},{default:p(()=>[i(u,{type:"number",controls:!1,step:"any",modelValue:l.targetData.apiCakeThickness,"onUpdate:modelValue":e[8]||(e[8]=c=>l.targetData.apiCakeThickness=c),placeholder:"",name:"apiCakeThickness"},null,8,["modelValue"])]),_:1})])]),t("div",Ir,[t("div",Pr,[t("label",null,[e[44]||(e[44]=t("span",null,"pH",-1)),i(o,{placement:"top",width:400,trigger:"hover"},{reference:p(()=>e[42]||(e[42]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7"},null,-1)])),default:p(()=>[e[43]||(e[43]=t("span",null," The pH level of the drilling mud, which can affect the performance of mud additives. ",-1))]),_:1})]),i(s,{prop:"pH"},{default:p(()=>[i(u,{type:"number",controls:!1,step:"any",modelValue:l.targetData.pH,"onUpdate:modelValue":e[9]||(e[9]=c=>l.targetData.pH=c),placeholder:"",name:"pH"},null,8,["modelValue"])]),_:1})]),t("div",Cr,[t("label",null,[e[47]||(e[47]=t("span",null,"Mud Alkalinity (Pm) (ml)",-1)),i(o,{placement:"top",width:400,trigger:"hover"},{reference:p(()=>e[45]||(e[45]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7 me-4"},null,-1)])),default:p(()=>[e[46]||(e[46]=t("span",null," Alkalinity measurements that can provide insights into the mud's pH buffering capacity and stability. ",-1))]),_:1})]),i(s,{prop:"mudAlkalinity"},{default:p(()=>[i(u,{type:"number",controls:!1,step:"any",modelValue:l.targetData.mudAlkalinity,"onUpdate:modelValue":e[10]||(e[10]=c=>l.targetData.mudAlkalinity=c),placeholder:"",name:"mudAlkalinity"},null,8,["modelValue"])]),_:1})]),t("div",Tr,[t("label",null,[e[50]||(e[50]=t("span",null,"Filtrate Alkalinity (Pf) (ml)",-1)),i(o,{placement:"top",width:400,trigger:"hover"},{reference:p(()=>e[48]||(e[48]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7"},null,-1)])),default:p(()=>[e[49]||(e[49]=t("span",null," Alkalinity measurements that can provide insights into the mud's pH buffering capacity and stability. ",-1))]),_:1})]),i(s,{prop:"filtrateAlkalinity"},{default:p(()=>[i(u,{type:"number",controls:!1,step:"any",modelValue:l.targetData.filtrateAlkalinity,"onUpdate:modelValue":e[11]||(e[11]=c=>l.targetData.filtrateAlkalinity=c),placeholder:"",name:"filtrateAlkalinity"},null,8,["modelValue"])]),_:1})]),t("div",Mr,[t("label",null,[e[53]||(e[53]=t("span",null,"Filtrate Alkalinity (Mf) (ml)",-1)),i(o,{placement:"top",width:400,trigger:"hover"},{reference:p(()=>e[51]||(e[51]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7"},null,-1)])),default:p(()=>[e[52]||(e[52]=t("span",null," Alkalinity measurements that can provide insights into the mud's pH buffering capacity and stability. ",-1))]),_:1})]),i(s,{prop:"filtrateAlkalinity"},{default:p(()=>[i(u,{type:"number",controls:!1,step:"any",modelValue:l.targetData.filtrateAlkalinity,"onUpdate:modelValue":e[12]||(e[12]=c=>l.targetData.filtrateAlkalinity=c),placeholder:"",name:"filtrateAlkalinity"},null,8,["modelValue"])]),_:1})]),t("div",Fr,[t("label",null,[e[56]||(e[56]=t("span",null,"Chlorides (mg/L)",-1)),i(o,{placement:"top",width:400,trigger:"hover"},{reference:p(()=>e[54]||(e[54]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7"},null,-1)])),default:p(()=>[e[55]||(e[55]=t("span",null," Measurements of various ions in the drilling mud, which can affect mud chemistry and performance. ",-1))]),_:1})]),i(s,{prop:"chlorides"},{default:p(()=>[i(u,{type:"number",controls:!1,step:"any",modelValue:l.targetData.chlorides,"onUpdate:modelValue":e[13]||(e[13]=c=>l.targetData.chlorides=c),placeholder:"",name:"chlorides"},null,8,["modelValue"])]),_:1})]),t("div",Nr,[t("label",null,[e[59]||(e[59]=t("span",null,"Total Hardness (mg/L)",-1)),i(o,{placement:"top",width:400,trigger:"hover"},{reference:p(()=>e[57]||(e[57]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7 me-4"},null,-1)])),default:p(()=>[e[58]||(e[58]=t("span",null," Measurements of various ions in the drilling mud, which can affect mud chemistry and performance. ",-1))]),_:1})]),i(s,{prop:"totalHardness"},{default:p(()=>[i(u,{type:"number",controls:!1,step:"any",modelValue:l.targetData.totalHardness,"onUpdate:modelValue":e[14]||(e[14]=c=>l.targetData.totalHardness=c),placeholder:"",name:"totalHardness"},null,8,["modelValue"])]),_:1})]),t("div",_r,[t("label",null,[e[62]||(e[62]=t("span",null,"Linear Gel Strength (LGS) (%)",-1)),i(o,{placement:"top",width:400,trigger:"hover"},{reference:p(()=>e[60]||(e[60]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7"},null,-1)])),default:p(()=>[e[61]||(e[61]=t("span",null," LGS (%) = (Gel Strength (lbs/100 ft²) / Mud Weight (lbs/gal)) x 100 ",-1))]),_:1})]),i(s,{prop:"linearGelStrengthPercent"},{default:p(()=>[i(u,{type:"number",controls:!1,step:"any",max:100,min:0,modelValue:l.targetData.linearGelStrengthPercent,"onUpdate:modelValue":e[15]||(e[15]=c=>l.targetData.linearGelStrengthPercent=c),placeholder:"",name:"linearGelStrengthPercent"},null,8,["modelValue"])]),_:1})])])]),t("div",Hr,[t("button",{type:"button",class:"bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover rounded-md px-4 py-2 font-semibold",onClick:e[16]||(e[16]=(...c)=>l.reset&&l.reset(...c)),disabled:l.loading}," Discard ",8,Ar),t("button",{class:"bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover rounded-md px-4 py-2 font-semibold",type:"submit",disabled:l.loading},[l.loading?E("",!0):(w(),$("span",qr," Save ")),l.loading?(w(),$("span",Br,e[63]||(e[63]=[F(" Please wait... "),t("span",{class:"spinner-border spinner-border-sm align-middle ms-2"},null,-1)]))):E("",!0)],8,zr)])]),_:1},8,["onSubmit","model","rules"])])])):E("",!0)}const Er=Q(fr,[["render",jr]]),Rr=J({name:"sample-modal",components:{SvgIcon:K},props:{isVisible:{type:Boolean,required:!0},close:{type:Function,required:!0},loadPage:{type:Function,default:()=>{},required:!0}},setup(l){const e=re(),S=le("dailyReport"),h=at(),m={id:"",dailyReportId:"",fluidType:null,weightedMud:!1,sampleFrom:"",timeSampleTaken:"",flowlineTemperature:null,measuredDepth:null,mudWeight:null,funnelViscosity:null,temperatureForPlasticViscosity:null,plasticViscosity:null,yieldPoint:null,gelStrength10s:null,gelStrength10m:null,gelStrength30m:null,apiFiltrate:null,apiCakeThickness:null,temperatureForHTHP:null,hthpFiltrate:null,hthpCakeThickness:null,solids:null,oil:null,water:null,sandContent:null,mbtCapacity:null,pH:null,mudAlkalinity:null,filtrateAlkalinity:null,calcium:null,chlorides:null,totalHardness:null,excessLime:null,kPlus:null,makeUpWater:null,solidsAdjustedForSalt:null,fineLCM:null,coarseLCM:null,linearGelStrengthPercent:null,linearGelStrengthLbBbl:null,highGelStrengthPercent:null,highGelStrengthLbBbl:null,bentoniteConcentrationPercent:null,bentoniteConcentrationLbBbl:null,drillSolidsConcentrationPercent:null,drillSolidsConcentrationLbBbl:null,drillSolidsToBentoniteRatio:null,averageSpecificGravityOfSolids:null,shearRate600:null,shearRate300:null,shearRate200:null,shearRate100:null,shearRate6:null,shearRate3:null,apparentViscosity:null,shearRate:null,shearStress:null},n=v(JSON.parse(JSON.stringify(m))),a=v(null),o=v(!1),r=v("");Y(r,x=>{x!==""&&d()}),Y(()=>l.isVisible,x=>{var b;x===!1&&(r.value="",P(),(b=a==null?void 0:a.value)==null||b.resetFields())});const d=async()=>{h.getSampleDetails({id:r.value,callback:{onSuccess:x=>{n.value={...x}}}})},s=async x=>{o.value=!0,h.updateSample({id:r.value,params:x,callback:{onSuccess:b=>{l!=null&&l.loadPage&&(l==null||l.loadPage()),f()},onFinish:b=>{o.value=!1}}})},u=async x=>{o.value=!0,h.createSample({params:x,callback:{onSuccess:b=>{l!=null&&l.loadPage&&(l==null||l.loadPage()),f()},onFinish:b=>{o.value=!1}}})},f=()=>{l.close()},c=x=>{r.value=x.toString()},y=v({fluidType:[{required:!0,message:"Please select Fluid Type",trigger:["change","blur"]}],sampleFrom:[{required:!0,message:"Please select Sample From",trigger:["change","blur"]}],mudWeight:[{required:!0,message:"Please input MW",trigger:["change","blur"]}],plasticViscosity:[{required:!0,message:"Please input Plastic Viscosity",trigger:["change","blur"]}],solids:[{required:!0,message:"Please input Solids",trigger:["change","blur"]}],oil:[{required:!0,message:"Please input Oil",trigger:["change","blur"]}],water:[{required:!0,message:"Please input Water",trigger:["change","blur"]}],chlorides:[{required:!0,message:"Please input Chlorides",trigger:["change","blur"]}],timeSampleTaken:[{required:!0,message:"Please select Time Sample Taken",trigger:["change","blur"]}]}),M=()=>{a.value&&a.value.validate(x=>{var b,T,q,I,C,B,A,_,N,j,z,R,L,U,oe,X,W,G,ee,de,he,ve,$e,xe,we,Ie,Pe,Ce,Te,Me,Fe,Ne,_e,He,Ae,ze,qe,Be,je,Ee,Re,Oe,Le,We,Ue,Ge,Ye,Je,Qe,Ke,Xe,ye,ut,pt,ct;if(x){const ft={...n.value,sampleFrom:(T=(b=n==null?void 0:n.value)==null?void 0:b.sampleFrom)==null?void 0:T.toString(),fluidType:Number((q=n==null?void 0:n.value)==null?void 0:q.fluidType),flowlineTemperature:Number((I=n==null?void 0:n.value)==null?void 0:I.flowlineTemperature),measuredDepth:Number((C=n==null?void 0:n.value)==null?void 0:C.measuredDepth),mudWeight:Number((B=n==null?void 0:n.value)==null?void 0:B.mudWeight),funnelViscosity:Number((A=n==null?void 0:n.value)==null?void 0:A.funnelViscosity),temperatureForPlasticViscosity:Number((_=n==null?void 0:n.value)==null?void 0:_.temperatureForPlasticViscosity),plasticViscosity:Number((N=n==null?void 0:n.value)==null?void 0:N.plasticViscosity),yieldPoint:Number((j=n==null?void 0:n.value)==null?void 0:j.yieldPoint),gelStrength10s:Number((z=n==null?void 0:n.value)==null?void 0:z.gelStrength10s),gelStrength10m:Number((R=n==null?void 0:n.value)==null?void 0:R.gelStrength10m),gelStrength30m:Number((L=n==null?void 0:n.value)==null?void 0:L.gelStrength30m),apiFiltrate:Number((U=n==null?void 0:n.value)==null?void 0:U.apiFiltrate),apiCakeThickness:Number((oe=n==null?void 0:n.value)==null?void 0:oe.apiCakeThickness),temperatureForHTHP:Number((X=n==null?void 0:n.value)==null?void 0:X.temperatureForHTHP),hthpFiltrate:Number((W=n==null?void 0:n.value)==null?void 0:W.hthpFiltrate),hthpCakeThickness:Number((G=n==null?void 0:n.value)==null?void 0:G.hthpCakeThickness),solids:Number((ee=n==null?void 0:n.value)==null?void 0:ee.solids),oil:Number((de=n==null?void 0:n.value)==null?void 0:de.oil),water:Number((he=n==null?void 0:n.value)==null?void 0:he.water),sandContent:Number((ve=n==null?void 0:n.value)==null?void 0:ve.sandContent),mbtCapacity:Number(($e=n==null?void 0:n.value)==null?void 0:$e.mbtCapacity),pH:Number((xe=n==null?void 0:n.value)==null?void 0:xe.pH),mudAlkalinity:Number((we=n==null?void 0:n.value)==null?void 0:we.mudAlkalinity),filtrateAlkalinity:Number((Ie=n==null?void 0:n.value)==null?void 0:Ie.filtrateAlkalinity),calcium:Number((Pe=n==null?void 0:n.value)==null?void 0:Pe.calcium),chlorides:Number((Ce=n==null?void 0:n.value)==null?void 0:Ce.chlorides),totalHardness:Number((Te=n==null?void 0:n.value)==null?void 0:Te.totalHardness),excessLime:Number((Me=n==null?void 0:n.value)==null?void 0:Me.excessLime),kPlus:Number((Fe=n==null?void 0:n.value)==null?void 0:Fe.kPlus),makeUpWater:Number((Ne=n==null?void 0:n.value)==null?void 0:Ne.makeUpWater),solidsAdjustedForSalt:Number((_e=n==null?void 0:n.value)==null?void 0:_e.solidsAdjustedForSalt),fineLCM:Number((He=n==null?void 0:n.value)==null?void 0:He.fineLCM),coarseLCM:Number((Ae=n==null?void 0:n.value)==null?void 0:Ae.coarseLCM),linearGelStrengthPercent:Number((ze=n==null?void 0:n.value)==null?void 0:ze.linearGelStrengthPercent),linearGelStrengthLbBbl:Number((qe=n==null?void 0:n.value)==null?void 0:qe.linearGelStrengthLbBbl),highGelStrengthPercent:Number((Be=n==null?void 0:n.value)==null?void 0:Be.highGelStrengthPercent),highGelStrengthLbBbl:Number((je=n==null?void 0:n.value)==null?void 0:je.highGelStrengthLbBbl),bentoniteConcentrationPercent:Number((Ee=n==null?void 0:n.value)==null?void 0:Ee.bentoniteConcentrationPercent),bentoniteConcentrationLbBbl:Number((Re=n==null?void 0:n.value)==null?void 0:Re.bentoniteConcentrationLbBbl),drillSolidsConcentrationPercent:Number((Oe=n==null?void 0:n.value)==null?void 0:Oe.drillSolidsConcentrationPercent),drillSolidsConcentrationLbBbl:Number((Le=n==null?void 0:n.value)==null?void 0:Le.drillSolidsConcentrationLbBbl),drillSolidsToBentoniteRatio:Number((We=n==null?void 0:n.value)==null?void 0:We.drillSolidsToBentoniteRatio),averageSpecificGravityOfSolids:Number((Ue=n==null?void 0:n.value)==null?void 0:Ue.averageSpecificGravityOfSolids),shearRate600:Number((Ge=n==null?void 0:n.value)==null?void 0:Ge.shearRate600),shearRate300:Number((Ye=n==null?void 0:n.value)==null?void 0:Ye.shearRate300),shearRate200:Number((Je=n==null?void 0:n.value)==null?void 0:Je.shearRate200),shearRate100:Number((Qe=n==null?void 0:n.value)==null?void 0:Qe.shearRate100),shearRate6:Number((Ke=n==null?void 0:n.value)==null?void 0:Ke.shearRate6),shearRate3:Number((Xe=n==null?void 0:n.value)==null?void 0:Xe.shearRate3),apparentViscosity:Number((ye=n==null?void 0:n.value)==null?void 0:ye.apparentViscosity),shearRate:Number((ut=n==null?void 0:n.value)==null?void 0:ut.shearRate),shearStress:Number((pt=n==null?void 0:n.value)==null?void 0:pt.shearStress)};r!=null&&r.value?s({...ft,dailyReportId:S==null?void 0:S.getDailyReportId()}):S==null||S.createDailyReport({wellId:(ct=e==null?void 0:e.params)==null?void 0:ct.id,callback:{onSuccess:kw=>{u({...ft,dailyReportId:S==null?void 0:S.getDailyReportId()})}}})}})},P=()=>{n.value=JSON.parse(JSON.stringify(m))};return{id:r,rules:y,loading:o,targetData:n,formRef:a,fluidTypeOptions:lt,sampleFromOptions:tt,closeModal:f,submit:M,setId:c,reset:P}}}),Or={key:0,class:"fixed top-0 right-0 bottom-0 left-0 z-40 flex items-center"},Lr={class:"relative bg-card-background text-card-text-light max-h-4/5 w-11/12 flex flex-col items-center gap-3 mx-auto my-6 p-5 rounded-xl overflow-y-scroll lg:w-4/5 lg:h-auto"},Wr={class:"flex flex-row h-auto w-full items-center justify-between"},Ur={class:"text-lg font-bold"},Gr={class:"flex flex-col gap-3"},Yr={class:"flex flex-col gap-3 md:flex-row md:gap-4 md:justify-between"},Jr={class:"h-auto w-full flex flex-col gap-1"},Qr={class:"font-bold"},Kr={class:"h-auto w-full flex items-center gap-4 pb-2"},Xr={class:"font-bold"},Zr={class:"h-auto w-full flex flex-col gap-1"},ed={class:"font-bold"},td={class:"h-auto w-full flex flex-col gap-1"},ld={class:"font-bold"},od={class:"flex flex-col gap-3 md:flex-row md:gap-4 md:justify-between"},sd={class:"h-auto w-full flex flex-col gap-1"},nd={class:"font-bold"},ad={class:"h-auto w-full flex flex-col gap-1"},id={class:"font-bold"},rd={class:"h-auto w-full flex flex-col gap-1"},dd={class:"font-bold"},ud={class:"h-auto w-full flex flex-col gap-1"},pd={class:"font-bold"},cd={class:"flex flex-col gap-3 md:flex-row md:gap-4 md:justify-between"},fd={class:"h-auto w-full flex flex-col gap-1"},md={class:"font-bold"},gd={class:"h-auto w-full flex flex-col gap-1"},bd={class:"font-bold"},hd={class:"h-auto w-full flex flex-col gap-1"},vd={class:"font-bold"},yd={class:"h-auto w-full flex flex-col gap-1"},wd={class:"font-bold"},Sd={class:"flex flex-col gap-3 md:flex-row md:gap-4 md:justify-between"},$d={class:"h-auto w-full flex flex-col gap-1"},xd={class:"font-bold"},kd={class:"h-auto w-full flex flex-col gap-1"},Dd={class:"font-bold"},Vd={class:"h-auto w-full flex flex-col gap-1"},Id={class:"font-bold"},Pd={class:"h-auto w-full flex flex-col gap-1"},Cd={class:"font-bold"},Td={class:"flex flex-col gap-3 md:flex-row md:gap-4 md:justify-between"},Md={class:"h-auto w-full flex flex-col gap-1"},Fd={class:"font-bold"},Nd={class:"h-auto w-full flex flex-col gap-1"},_d={class:"font-bold"},Hd={class:"h-auto w-full flex flex-col gap-1"},Ad={class:"font-bold"},zd={class:"h-auto w-full flex flex-col gap-1"},qd={class:"font-bold"},Bd={class:"flex flex-col gap-3 md:flex-row md:gap-4 md:justify-between"},jd={class:"h-auto w-full flex flex-col gap-1"},Ed={class:"font-bold"},Rd={class:"h-auto w-full flex flex-col gap-1"},Od={class:"font-bold"},Ld={class:"h-auto w-full flex flex-col gap-1"},Wd={class:"font-bold"},Ud={class:"h-auto w-full flex flex-col gap-1"},Gd={class:"font-bold"},Yd={class:"flex flex-col gap-3 md:flex-row md:gap-4 md:justify-between"},Jd={class:"h-auto w-full flex flex-col gap-1"},Qd={class:"font-bold"},Kd={class:"h-auto w-full flex flex-col gap-1"},Xd={class:"font-bold"},Zd={class:"h-auto w-full flex flex-col gap-1"},eu={class:"font-bold"},tu={class:"h-auto w-full flex flex-col gap-1"},lu={class:"font-bold"},ou={class:"flex flex-col gap-3 md:flex-row md:gap-4 md:justify-between"},su={class:"h-auto w-full flex flex-col gap-1"},nu={class:"font-bold"},au={class:"h-auto w-full flex flex-col gap-1"},iu={class:"font-bold"},ru={class:"h-auto w-full flex flex-col gap-1"},du={class:"font-bold"},uu={class:"h-auto w-full flex flex-col gap-1"},pu={class:"font-bold"},cu={class:"flex flex-col gap-3 md:flex-row md:gap-4 md:justify-between"},fu={class:"h-auto w-full flex flex-col gap-1"},mu={class:"font-bold"},gu={class:"h-auto w-full flex flex-col gap-1"},bu={class:"font-bold"},hu={class:"h-auto w-full flex flex-col gap-1"},vu={class:"font-bold"},yu={class:"h-auto w-full flex flex-col gap-1"},wu={class:"font-bold"},Su={class:"flex flex-col gap-3 md:flex-row md:gap-4 md:justify-between"},$u={class:"h-auto w-full flex flex-col gap-1"},xu={class:"font-bold"},ku={class:"flex flex-col gap-3 md:flex-row md:gap-4 md:justify-between"},Du={class:"h-auto w-full flex flex-col gap-1"},Vu={class:"h-auto w-full flex flex-col gap-1"},Iu={class:"h-auto w-full flex flex-col gap-1"},Pu={class:"h-auto w-full flex flex-col gap-1"},Cu={class:"flex flex-col gap-3 md:flex-row md:gap-4 md:justify-between"},Tu={class:"h-auto w-full flex flex-col gap-1"},Mu={class:"h-auto w-full flex flex-col gap-1"},Fu={class:"h-auto w-full flex flex-col gap-1"},Nu={class:"font-bold"},_u={class:"h-auto w-full flex flex-col gap-1"},Hu={class:"font-bold"},Au={class:"flex flex-col gap-3 md:flex-row md:gap-4 md:justify-between"},zu={class:"h-auto w-full flex flex-col gap-1"},qu={class:"font-bold"},Bu={class:"h-auto w-full flex flex-col gap-1"},ju={class:"font-bold"},Eu={class:"h-auto w-full flex flex-col gap-1"},Ru={class:"font-bold"},Ou={class:"h-auto w-full flex flex-row items-center gap-2"},Lu=["disabled"],Wu=["disabled"],Uu={key:0,class:"indicator-label"},Gu={key:1,class:"indicator-progress"};function Yu(l,e,S,h,m,n){const a=k("SvgIcon"),o=k("el-popover"),r=k("el-option"),d=k("el-select"),s=k("el-form-item"),u=k("el-time-picker"),f=k("el-input"),c=k("el-form");return l.isVisible?(w(),$("div",Or,[e[200]||(e[200]=t("div",{class:"absolute inset-0 bg-sidebar-background opacity-25"},null,-1)),t("div",Lr,[t("div",Wr,[t("h3",Ur,D(`${l.id?"Edit Sample":"New Sample"}`),1),t("span",{class:"cursor-pointer",onClick:e[0]||(e[0]=(...y)=>l.closeModal&&l.closeModal(...y))},[i(a,{icon:"closeModalIcon"})])]),i(c,{onSubmit:ie(l.submit,["prevent"]),model:l.targetData,rules:l.rules,ref:"formRef",class:"h-auto w-full"},{default:p(()=>[t("div",Gr,[t("div",Yr,[t("div",Jr,[t("label",Qr,[e[52]||(e[52]=F("Fluid Type")),e[53]||(e[53]=t("span",{class:"text-danger-active font-light"},"*",-1)),i(o,{placement:"top",width:400,trigger:"hover"},{reference:p(()=>e[50]||(e[50]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7"},null,-1)])),default:p(()=>[e[51]||(e[51]=t("span",null,[F(" Water-based drilling muds are the most common type. They use water as the base fluid and may contain various additives. The key differences for WBM include water quality, filtration properties, and clay content."),t("br"),t("br"),F(" Oil-based drilling muds use oil as the base fluid. Key considerations for OBM include oil composition, oil/water ratio, and rheological properties."),t("br"),t("br"),F(" Synthetic-based drilling muds use synthetic fluids as the base, such as esters or olefins. Specific data requirements depend on the type of synthetic fluid used. ")],-1))]),_:1})]),i(s,{prop:"fluidType",class:"mt-auto"},{default:p(()=>[i(d,{modelValue:l.targetData.fluidType,"onUpdate:modelValue":e[1]||(e[1]=y=>l.targetData.fluidType=y),placeholder:"",clearable:""},{default:p(()=>[(w(!0),$(O,null,Z(l.fluidTypeOptions,y=>(w(),te(r,{key:y.value,label:y.label,value:y.value,name:"fluidType"},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),t("div",Kr,[t("label",Xr,[e[56]||(e[56]=F("Weighted Mud")),e[57]||(e[57]=t("span",{class:"text-danger-active font-light"},"*",-1)),i(o,{placement:"top",width:400,trigger:"hover"},{reference:p(()=>e[54]||(e[54]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7"},null,-1)])),default:p(()=>[e[55]||(e[55]=t("span",null," Weighted mud refers to drilling fluids that have been weighted with solid materials, such as barite, to increase their density. For weighted mud, you may need to include additional data points related to the type and concentration of weighting agents, as well as their impact on rheological properties and filtration control. ",-1))]),_:1})]),i(s,{prop:"weightedMud",style:{"margin-bottom":"0"}},{default:p(()=>[yt(t("input",{class:"h-4 w-4",type:"checkbox",placeholder:"",name:"weightedMud","onUpdate:modelValue":e[2]||(e[2]=y=>l.targetData.weightedMud=y)},null,512),[[wt,l.targetData.weightedMud]])]),_:1})]),t("div",Zr,[t("label",ed,[e[60]||(e[60]=F("Sample From")),e[61]||(e[61]=t("span",{class:"text-danger-active font-light"},"*",-1)),i(o,{placement:"top",width:400,trigger:"hover"},{reference:p(()=>e[58]||(e[58]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7"},null,-1)])),default:p(()=>[e[59]||(e[59]=t("span",null," Sample From: Refers to the source or location from which the mud sample was obtained. It indicates where in the drilling system the mud sample was extracted or collected for analysis. ",-1))]),_:1})]),i(s,{prop:"sampleFrom",class:"mt-auto"},{default:p(()=>[i(d,{modelValue:l.targetData.sampleFrom,"onUpdate:modelValue":e[3]||(e[3]=y=>l.targetData.sampleFrom=y),placeholder:"",clearable:""},{default:p(()=>[(w(!0),$(O,null,Z(l.sampleFromOptions,y=>(w(),te(r,{key:y.value,label:y.label,value:y.value,name:"sampleFrom"},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),t("div",td,[t("label",ld,[e[64]||(e[64]=F("Time Sample Taken (hh:mm)")),e[65]||(e[65]=t("span",{class:"text-danger-active font-light"},"*",-1)),i(o,{placement:"top",width:400,trigger:"hover"},{reference:p(()=>e[62]||(e[62]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7"},null,-1)])),default:p(()=>[e[63]||(e[63]=t("span",null," The time of day that the sample is taken (24-hour) ",-1))]),_:1})]),i(s,{prop:"timeSampleTaken",class:"mt-auto"},{default:p(()=>[i(u,{modelValue:l.targetData.timeSampleTaken,"onUpdate:modelValue":e[4]||(e[4]=y=>l.targetData.timeSampleTaken=y),placeholder:"hh:mm",name:"timeSampleTaken",format:"HH:mm","value-format":"HH:mm"},null,8,["modelValue"])]),_:1})])]),t("div",od,[t("div",sd,[t("label",nd,[e[68]||(e[68]=t("span",null,"Flowline Temperature (F)",-1)),i(o,{placement:"top",width:400,trigger:"hover"},{reference:p(()=>e[66]||(e[66]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7"},null,-1)])),default:p(()=>[e[67]||(e[67]=t("span",null," The temperature of the drilling mud in the flowline as it returns to the surface. ",-1))]),_:1})]),i(s,{prop:"flowlineTemperature",class:"mt-auto"},{default:p(()=>[i(f,{type:"number",controls:!1,step:"any",modelValue:l.targetData.flowlineTemperature,"onUpdate:modelValue":e[5]||(e[5]=y=>l.targetData.flowlineTemperature=y),placeholder:"",name:"flowlineTemperature"},null,8,["modelValue"])]),_:1})]),t("div",ad,[t("label",id,[e[71]||(e[71]=t("span",null,"Depth (ft)",-1)),i(o,{placement:"top",width:400,trigger:"hover"},{reference:p(()=>e[69]||(e[69]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7"},null,-1)])),default:p(()=>[e[70]||(e[70]=t("span",null," The depth at which drilling operations are taking place. ",-1))]),_:1})]),i(s,{prop:"measuredDepth",class:"mt-auto"},{default:p(()=>[i(f,{type:"number",controls:!1,step:"any",modelValue:l.targetData.measuredDepth,"onUpdate:modelValue":e[6]||(e[6]=y=>l.targetData.measuredDepth=y),placeholder:"",name:"measuredDepth"},null,8,["modelValue"])]),_:1})]),t("div",rd,[t("label",dd,[e[74]||(e[74]=F(" MW (ppg or lbs/gal)")),e[75]||(e[75]=t("span",{class:"text-danger-active font-light"},"*",-1)),i(o,{placement:"bottom",width:400,trigger:"hover"},{reference:p(()=>e[72]||(e[72]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7 me-4"},null,-1)])),default:p(()=>[e[73]||(e[73]=t("span",null," Mud Weight, also known as mud density, is the density of the drilling mud, typically measured in pounds per gallon (ppg). For different types of drilling fluids, the density requirements can vary. ",-1))]),_:1})]),i(s,{prop:"mudWeight",class:"mt-auto"},{default:p(()=>[i(f,{type:"number",controls:!1,step:"any",modelValue:l.targetData.mudWeight,"onUpdate:modelValue":e[7]||(e[7]=y=>l.targetData.mudWeight=y),placeholder:"",name:"mudWeight"},null,8,["modelValue"])]),_:1})]),t("div",ud,[t("label",pd,[e[78]||(e[78]=t("span",null,"Funnel Viscosity (sec/qt) ",-1)),i(o,{placement:"top",width:400,trigger:"hover"},{reference:p(()=>e[76]||(e[76]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7"},null,-1)])),default:p(()=>[e[77]||(e[77]=t("span",null," The funnel viscosity measures the thickness or viscosity of the drilling mud. It is typically measured in seconds per quart (sec/qt). ",-1))]),_:1})]),i(s,{prop:"funnelViscosity",class:"mt-auto"},{default:p(()=>[i(f,{type:"number",controls:!1,step:"any",modelValue:l.targetData.funnelViscosity,"onUpdate:modelValue":e[8]||(e[8]=y=>l.targetData.funnelViscosity=y),placeholder:"",name:"funnelViscosity"},null,8,["modelValue"])]),_:1})])]),t("div",cd,[t("div",fd,[t("label",md,[e[81]||(e[81]=t("span",null,"Temperature for Plastic Viscosity (f)",-1)),i(o,{placement:"top",width:400,trigger:"hover"},{reference:p(()=>e[79]||(e[79]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7"},null,-1)])),default:p(()=>[e[80]||(e[80]=t("span",null," The temperature at which Plastic Viscosity (PV) is measured, which affects mud rheology. ",-1))]),_:1})]),i(s,{prop:"temperatureForPlasticViscosity",class:"mt-auto"},{default:p(()=>[i(f,{type:"number",controls:!1,step:"any",modelValue:l.targetData.temperatureForPlasticViscosity,"onUpdate:modelValue":e[9]||(e[9]=y=>l.targetData.temperatureForPlasticViscosity=y),placeholder:"",name:"temperatureForPlasticViscosity"},null,8,["modelValue"])]),_:1})]),t("div",gd,[t("label",bd,[e[84]||(e[84]=F("PV (Plastic Viscosity) (cP)")),e[85]||(e[85]=t("span",{class:"text-danger-active font-light"},"*",-1)),i(o,{placement:"top",width:400,trigger:"hover"},{reference:p(()=>e[82]||(e[82]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7"},null,-1)])),default:p(()=>[e[83]||(e[83]=t("span",null," Plastic Viscosity is a measure of the resistance to flow of the drilling mud. It is typically measured in centipoise (cP). ",-1))]),_:1})]),i(s,{prop:"plasticViscosity",class:"mt-auto"},{default:p(()=>[i(f,{type:"number",controls:!1,step:"any",modelValue:l.targetData.plasticViscosity,"onUpdate:modelValue":e[10]||(e[10]=y=>l.targetData.plasticViscosity=y),placeholder:"",name:"plasticViscosity"},null,8,["modelValue"])]),_:1})]),t("div",hd,[t("label",vd,[e[88]||(e[88]=F(" YP (lbf/100ft2)")),e[89]||(e[89]=t("span",{class:"text-danger-active font-light"},"*",-1)),i(o,{placement:"top",width:400,trigger:"hover"},{reference:p(()=>e[86]||(e[86]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7 me-4"},null,-1)])),default:p(()=>[e[87]||(e[87]=t("span",null," Yield Point is the amount of force required to initiate mud flow. It's typically measured in pounds per 100 square feet (lbf/100ft^2). ",-1))]),_:1})]),i(s,{prop:"yieldPoint",class:"mt-auto"},{default:p(()=>[i(f,{type:"number",controls:!1,step:"any",modelValue:l.targetData.yieldPoint,"onUpdate:modelValue":e[11]||(e[11]=y=>l.targetData.yieldPoint=y),placeholder:"",name:"yieldPoint"},null,8,["modelValue"])]),_:1})]),t("div",yd,[t("label",wd,[e[92]||(e[92]=t("span",null,"Gel Str. 10s (lbf/100ft2)",-1)),i(o,{placement:"top",width:400,trigger:"hover"},{reference:p(()=>e[90]||(e[90]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7"},null,-1)])),default:p(()=>[e[91]||(e[91]=t("span",null," Gel Strength measures the mud's ability to suspend cuttings. The numbers (10s, 10m, 30m) indicate the time at which the measurement is taken. ",-1))]),_:1})]),i(s,{prop:"gelStrength10s",class:"mt-auto"},{default:p(()=>[i(f,{type:"number",controls:!1,step:"any",modelValue:l.targetData.gelStrength10s,"onUpdate:modelValue":e[12]||(e[12]=y=>l.targetData.gelStrength10s=y),placeholder:"",name:"gelStrength10s"},null,8,["modelValue"])]),_:1})])]),t("div",Sd,[t("div",$d,[t("label",xd,[e[95]||(e[95]=t("span",null,"Gel Str. 10m (lbf/100ft2)",-1)),i(o,{placement:"top",width:400,trigger:"hover"},{reference:p(()=>e[93]||(e[93]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7"},null,-1)])),default:p(()=>[e[94]||(e[94]=t("span",null," Gel Strength measures the mud's ability to suspend cuttings. The numbers (10s, 10m, 30m) indicate the time at which the measurement is taken. ",-1))]),_:1})]),i(s,{prop:"gelStrength10m",class:"mt-auto"},{default:p(()=>[i(f,{type:"number",controls:!1,step:"any",modelValue:l.targetData.gelStrength10m,"onUpdate:modelValue":e[13]||(e[13]=y=>l.targetData.gelStrength10m=y),placeholder:"",name:"gelStrength10m"},null,8,["modelValue"])]),_:1})]),t("div",kd,[t("label",Dd,[e[98]||(e[98]=t("span",null,"Gel Str. 30m (lbf/100ft2)",-1)),i(o,{placement:"top",width:400,trigger:"hover"},{reference:p(()=>e[96]||(e[96]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7"},null,-1)])),default:p(()=>[e[97]||(e[97]=t("span",null," Gel Strength measures the mud's ability to suspend cuttings. The numbers (10s, 10m, 30m) indicate the time at which the measurement is taken. ",-1))]),_:1})]),i(s,{prop:"gelStrength30m",class:"mt-auto"},{default:p(()=>[i(f,{type:"number",controls:!1,step:"any",modelValue:l.targetData.gelStrength30m,"onUpdate:modelValue":e[14]||(e[14]=y=>l.targetData.gelStrength30m=y),placeholder:"",name:"gelStrength30m"},null,8,["modelValue"])]),_:1})]),t("div",Vd,[t("label",Id,[e[101]||(e[101]=F("API filtrate (ml/30min)")),e[102]||(e[102]=t("span",{class:"text-danger-active font-light"},"*",-1)),i(o,{placement:"top",width:400,trigger:"hover"},{reference:p(()=>e[99]||(e[99]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7"},null,-1)])),default:p(()=>[e[100]||(e[100]=t("span",null,[F(" The volume of mud filtrate that passes through a standard filter paper in 30 minutes, measured in milliliters."),t("br"),t("br"),F(" WBM: API filtrate and cake thickness are commonly measured for Water-Based Mud. The filtration properties may be different for various types of WBM formulations. OBM: Oil-Based Mud may have different filtration characteristics compared to WBM due to the presence of oil. The API filtrate and cake thickness may have specific requirements for OBM. SBM: Synthetic-Based Mud may also have unique filtration properties, and API filtrate and cake thickness measurements should consider the specific synthetic fluid used. ")],-1))]),_:1})]),i(s,{prop:"apiFiltrate",class:"mt-auto"},{default:p(()=>[i(f,{type:"number",controls:!1,step:"any",modelValue:l.targetData.apiFiltrate,"onUpdate:modelValue":e[15]||(e[15]=y=>l.targetData.apiFiltrate=y),placeholder:"",name:"apiFiltrate"},null,8,["modelValue"])]),_:1})]),t("div",Pd,[t("label",Cd,[e[105]||(e[105]=F(" API cake thickness (1/32in)")),e[106]||(e[106]=t("span",{class:"text-danger-active font-light"},"*",-1)),i(o,{placement:"top",width:400,trigger:"hover"},{reference:p(()=>e[103]||(e[103]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7 me-4"},null,-1)])),default:p(()=>[e[104]||(e[104]=t("span",null,[F(" The thickness of the filter cake formed by the drilling mud on the filter paper, measured in 1/32 of an inch."),t("br"),t("br"),F(" WBM: API filtrate and cake thickness are commonly measured for Water-Based Mud. The filtration properties may be different for various types of WBM formulations. OBM: Oil-Based Mud may have different filtration characteristics compared to WBM due to the presence of oil. The API filtrate and cake thickness may have specific requirements for OBM. SBM: Synthetic-Based Mud may also have unique filtration properties, and API filtrate and cake thickness measurements should consider the specific synthetic fluid used. ")],-1))]),_:1})]),i(s,{prop:"apiCakeThickness",class:"mt-auto"},{default:p(()=>[i(f,{type:"number",controls:!1,step:"any",modelValue:l.targetData.apiCakeThickness,"onUpdate:modelValue":e[16]||(e[16]=y=>l.targetData.apiCakeThickness=y),placeholder:"",name:"apiCakeThickness"},null,8,["modelValue"])]),_:1})])]),t("div",Td,[t("div",Md,[t("label",Fd,[e[109]||(e[109]=t("span",null,"Temperature for HTHP (F)",-1)),i(o,{placement:"top",width:400,trigger:"hover"},{reference:p(()=>e[107]||(e[107]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7"},null,-1)])),default:p(()=>[e[108]||(e[108]=t("span",null," The temperature at which High-Temperature, High-Pressure (HTHP) tests are conducted, which is important for evaluating mud stability under downhole conditions. ",-1))]),_:1})]),i(s,{prop:"temperatureForHTHP",class:"mt-auto"},{default:p(()=>[i(f,{type:"number",controls:!1,step:"any",modelValue:l.targetData.temperatureForHTHP,"onUpdate:modelValue":e[17]||(e[17]=y=>l.targetData.temperatureForHTHP=y),placeholder:"",name:"temperatureForHTHP"},null,8,["modelValue"])]),_:1})]),t("div",Nd,[t("label",_d,[e[112]||(e[112]=t("span",null,"HTHP filtrate (ml/30min)",-1)),i(o,{placement:"top",width:400,trigger:"hover"},{reference:p(()=>e[110]||(e[110]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7"},null,-1)])),default:p(()=>[e[111]||(e[111]=t("span",null," Similar to API filtrate, but measured under high-temperature, high-pressure conditions. ",-1))]),_:1})]),i(s,{prop:"hthpFiltrate",class:"mt-auto"},{default:p(()=>[i(f,{type:"number",controls:!1,step:"any",modelValue:l.targetData.hthpFiltrate,"onUpdate:modelValue":e[18]||(e[18]=y=>l.targetData.hthpFiltrate=y),placeholder:"",name:"hthpFiltrate"},null,8,["modelValue"])]),_:1})]),t("div",Hd,[t("label",Ad,[e[115]||(e[115]=t("span",null,"HTHP cake thickness (1/32in)",-1)),i(o,{placement:"top",width:400,trigger:"hover"},{reference:p(()=>e[113]||(e[113]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7"},null,-1)])),default:p(()=>[e[114]||(e[114]=t("span",null," Similar to API cake thickness, but measured under high-temperature, high-pressure conditions. ",-1))]),_:1})]),i(s,{prop:"hthpCakeThickness",class:"mt-auto"},{default:p(()=>[i(f,{type:"number",controls:!1,step:"any",modelValue:l.targetData.hthpCakeThickness,"onUpdate:modelValue":e[19]||(e[19]=y=>l.targetData.hthpCakeThickness=y),placeholder:"",name:"hthpCakeThickness"},null,8,["modelValue"])]),_:1})]),t("div",zd,[t("label",qd,[e[118]||(e[118]=F("Solids (%)")),e[119]||(e[119]=t("span",{class:"text-danger-active font-light"},"*",-1)),i(o,{placement:"top",width:400,trigger:"hover"},{reference:p(()=>e[116]||(e[116]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7"},null,-1)])),default:p(()=>[e[117]||(e[117]=t("span",null," The percentage of solid materials in the drilling mud, including drill cuttings and additives. ",-1))]),_:1})]),i(s,{prop:"solids",class:"mt-auto"},{default:p(()=>[i(f,{type:"number",controls:!1,step:"any",modelValue:l.targetData.solids,"onUpdate:modelValue":e[20]||(e[20]=y=>l.targetData.solids=y),placeholder:"",name:"solids"},null,8,["modelValue"])]),_:1})])]),t("div",Bd,[t("div",jd,[t("label",Ed,[e[122]||(e[122]=F(" Oil (%)")),e[123]||(e[123]=t("span",{class:"text-danger-active font-light"},"*",-1)),i(o,{placement:"top",width:400,trigger:"hover"},{reference:p(()=>e[120]||(e[120]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7 me-4"},null,-1)])),default:p(()=>[e[121]||(e[121]=t("span",null," These percentages represent the proportions of oil in the mud, which can be important for characterizing the mud composition. ",-1))]),_:1})]),i(s,{prop:"oil",class:"mt-auto"},{default:p(()=>[i(f,{type:"number",controls:!1,step:"any",modelValue:l.targetData.oil,"onUpdate:modelValue":e[21]||(e[21]=y=>l.targetData.oil=y),placeholder:"",name:"oil"},null,8,["modelValue"])]),_:1})]),t("div",Rd,[t("label",Od,[e[126]||(e[126]=F("Water (%)")),e[127]||(e[127]=t("span",{class:"text-danger-active font-light"},"*",-1)),i(o,{placement:"top",width:400,trigger:"hover"},{reference:p(()=>e[124]||(e[124]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7"},null,-1)])),default:p(()=>[e[125]||(e[125]=t("span",null," These percentages represent the proportions of water in the mud, which can be important for characterizing the mud composition. ",-1))]),_:1})]),i(s,{prop:"water",class:"mt-auto"},{default:p(()=>[i(f,{type:"number",controls:!1,step:"any",modelValue:l.targetData.water,"onUpdate:modelValue":e[22]||(e[22]=y=>l.targetData.water=y),placeholder:"",name:"water"},null,8,["modelValue"])]),_:1})]),t("div",Ld,[t("label",Wd,[e[130]||(e[130]=t("span",null,"Sand Content (%)",-1)),i(o,{placement:"top",width:400,trigger:"hover"},{reference:p(()=>e[128]||(e[128]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7"},null,-1)])),default:p(()=>[e[129]||(e[129]=t("span",null," These percentages represent the proportions of sand in the mud, which can be important for characterizing the mud composition. ",-1))]),_:1})]),i(s,{prop:"sandContent",class:"mt-auto"},{default:p(()=>[i(f,{type:"number",controls:!1,step:"any",modelValue:l.targetData.sandContent,"onUpdate:modelValue":e[23]||(e[23]=y=>l.targetData.sandContent=y),placeholder:"",name:"sandContent"},null,8,["modelValue"])]),_:1})]),t("div",Ud,[t("label",Gd,[e[133]||(e[133]=t("span",null,"MBT capacity (lb/bbl)",-1)),i(o,{placement:"top",width:400,trigger:"hover"},{reference:p(()=>e[131]||(e[131]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7"},null,-1)])),default:p(()=>[e[132]||(e[132]=t("span",null," The Mud Balance Test (MBT) measures the mud's density and is used to calculate the mud weight in pounds per barrel (lb/bbl). ",-1))]),_:1})]),i(s,{prop:"mbtCapacity",class:"mt-auto"},{default:p(()=>[i(f,{type:"number",controls:!1,step:"any",modelValue:l.targetData.mbtCapacity,"onUpdate:modelValue":e[24]||(e[24]=y=>l.targetData.mbtCapacity=y),placeholder:"",name:"mbtCapacity"},null,8,["modelValue"])]),_:1})])]),t("div",Yd,[t("div",Jd,[t("label",Qd,[e[136]||(e[136]=t("span",null,"pH",-1)),i(o,{placement:"top",width:400,trigger:"hover"},{reference:p(()=>e[134]||(e[134]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7"},null,-1)])),default:p(()=>[e[135]||(e[135]=t("span",null," The pH level of the drilling mud, which can affect the performance of mud additives. ",-1))]),_:1})]),i(s,{prop:"pH",class:"mt-auto"},{default:p(()=>[i(f,{type:"number",controls:!1,step:"any",modelValue:l.targetData.pH,"onUpdate:modelValue":e[25]||(e[25]=y=>l.targetData.pH=y),placeholder:"",name:"pH"},null,8,["modelValue"])]),_:1})]),t("div",Kd,[t("label",Xd,[e[139]||(e[139]=F(" Mud Alkalinity (Pm) (ml) ")),i(o,{placement:"top",width:400,trigger:"hover"},{reference:p(()=>e[137]||(e[137]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7 me-4"},null,-1)])),default:p(()=>[e[138]||(e[138]=t("span",null," Alkalinity measurements that can provide insights into the mud's pH buffering capacity and stability. ",-1))]),_:1})]),i(s,{prop:"mudAlkalinity",class:"mt-auto"},{default:p(()=>[i(f,{type:"number",controls:!1,step:"any",modelValue:l.targetData.mudAlkalinity,"onUpdate:modelValue":e[26]||(e[26]=y=>l.targetData.mudAlkalinity=y),placeholder:"",name:"mudAlkalinity"},null,8,["modelValue"])]),_:1})]),t("div",Zd,[t("label",eu,[e[142]||(e[142]=t("span",null,"Filtrate Alkalinity (Pf) (ml)",-1)),i(o,{placement:"top",width:400,trigger:"hover"},{reference:p(()=>e[140]||(e[140]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7"},null,-1)])),default:p(()=>[e[141]||(e[141]=t("span",null," Alkalinity measurements that can provide insights into the mud's pH buffering capacity and stability. ",-1))]),_:1})]),i(s,{prop:"filtrateAlkalinity",class:"mt-auto"},{default:p(()=>[i(f,{type:"number",controls:!1,step:"any",modelValue:l.targetData.filtrateAlkalinity,"onUpdate:modelValue":e[27]||(e[27]=y=>l.targetData.filtrateAlkalinity=y),placeholder:"",name:"filtrateAlkalinity"},null,8,["modelValue"])]),_:1})]),t("div",tu,[t("label",lu,[e[145]||(e[145]=t("span",null,"Filtrate Alkalinity (Mf) (ml)",-1)),i(o,{placement:"top",width:400,trigger:"hover"},{reference:p(()=>e[143]||(e[143]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7"},null,-1)])),default:p(()=>[e[144]||(e[144]=t("span",null," Alkalinity measurements that can provide insights into the mud's pH buffering capacity and stability. ",-1))]),_:1})]),i(s,{prop:"filtrateAlkalinity",class:"mt-auto"},{default:p(()=>[i(f,{type:"number",controls:!1,step:"any",modelValue:l.targetData.filtrateAlkalinity,"onUpdate:modelValue":e[28]||(e[28]=y=>l.targetData.filtrateAlkalinity=y),placeholder:"",name:"filtrateAlkalinity"},null,8,["modelValue"])]),_:1})])]),t("div",ou,[t("div",su,[t("label",nu,[e[148]||(e[148]=t("span",null,"Calcium (mg/L)",-1)),i(o,{placement:"top",width:400,trigger:"hover"},{reference:p(()=>e[146]||(e[146]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7"},null,-1)])),default:p(()=>[e[147]||(e[147]=t("span",null," Measurements of various ions in the drilling mud, which can affect mud chemistry and performance. ",-1))]),_:1})]),i(s,{prop:"calcium",class:"mt-auto"},{default:p(()=>[i(f,{type:"number",controls:!1,step:"any",modelValue:l.targetData.calcium,"onUpdate:modelValue":e[29]||(e[29]=y=>l.targetData.calcium=y),placeholder:"",name:"calcium"},null,8,["modelValue"])]),_:1})]),t("div",au,[t("label",iu,[e[151]||(e[151]=F("Chlorides (mg/L)")),e[152]||(e[152]=t("span",{class:"text-danger-active font-light"},"*",-1)),i(o,{placement:"top",width:400,trigger:"hover"},{reference:p(()=>e[149]||(e[149]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7"},null,-1)])),default:p(()=>[e[150]||(e[150]=t("span",null," Measurements of various ions in the drilling mud, which can affect mud chemistry and performance. ",-1))]),_:1})]),i(s,{prop:"chlorides",class:"mt-auto"},{default:p(()=>[i(f,{type:"number",controls:!1,step:"any",modelValue:l.targetData.chlorides,"onUpdate:modelValue":e[30]||(e[30]=y=>l.targetData.chlorides=y),placeholder:"",name:"chlorides"},null,8,["modelValue"])]),_:1})]),t("div",ru,[t("label",du,[e[155]||(e[155]=F(" Total Hardness (mg/L) ")),i(o,{placement:"top",width:400,trigger:"hover"},{reference:p(()=>e[153]||(e[153]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7 me-4"},null,-1)])),default:p(()=>[e[154]||(e[154]=t("span",null," Measurements of various ions in the drilling mud, which can affect mud chemistry and performance. ",-1))]),_:1})]),i(s,{prop:"totalHardness",class:"mt-auto"},{default:p(()=>[i(f,{type:"number",controls:!1,step:"any",modelValue:l.targetData.totalHardness,"onUpdate:modelValue":e[31]||(e[31]=y=>l.targetData.totalHardness=y),placeholder:"",name:"totalHardness"},null,8,["modelValue"])]),_:1})]),t("div",uu,[t("label",pu,[e[158]||(e[158]=t("span",null,"Excess Lime (lb/bbl)",-1)),i(o,{placement:"top",width:400,trigger:"hover"},{reference:p(()=>e[156]||(e[156]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7"},null,-1)])),default:p(()=>[e[157]||(e[157]=t("span",null," The amount of excess lime added to the mud to control pH and alkalinity. ",-1))]),_:1})]),i(s,{prop:"excessLime",class:"mt-auto"},{default:p(()=>[i(f,{type:"number",controls:!1,step:"any",modelValue:l.targetData.excessLime,"onUpdate:modelValue":e[32]||(e[32]=y=>l.targetData.excessLime=y),placeholder:"",name:"excessLime"},null,8,["modelValue"])]),_:1})])]),t("div",cu,[t("div",fu,[t("label",mu,[e[161]||(e[161]=t("span",null,"K+ (mg/L)",-1)),i(o,{placement:"top",width:400,trigger:"hover"},{reference:p(()=>e[159]||(e[159]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7"},null,-1)])),default:p(()=>[e[160]||(e[160]=t("span",null," Measurement of potassium ion concentration in the mud. ",-1))]),_:1})]),i(s,{prop:"kPlus",class:"mt-auto"},{default:p(()=>[i(f,{type:"number",controls:!1,step:"any",modelValue:l.targetData.kPlus,"onUpdate:modelValue":e[33]||(e[33]=y=>l.targetData.kPlus=y),placeholder:"",name:"kPlus"},null,8,["modelValue"])]),_:1})]),t("div",gu,[t("label",bu,[e[164]||(e[164]=t("span",null,"Make up water: Chlorides (mg/L)",-1)),i(o,{placement:"top",width:400,trigger:"hover"},{reference:p(()=>e[162]||(e[162]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7"},null,-1)])),default:p(()=>[e[163]||(e[163]=t("span",null," Chloride concentration in makeup water used to dilute or maintain the mud's properties. ",-1))]),_:1})]),i(s,{prop:"makeUpWater",class:"mt-auto"},{default:p(()=>[i(f,{type:"number",controls:!1,step:"any",modelValue:l.targetData.makeUpWater,"onUpdate:modelValue":e[34]||(e[34]=y=>l.targetData.makeUpWater=y),placeholder:"",name:"makeUpWater"},null,8,["modelValue"])]),_:1})]),t("div",hu,[t("label",vu,[e[167]||(e[167]=t("span",null,"Solids adjusted for salt (%)",-1)),i(o,{placement:"top",width:400,trigger:"hover"},{reference:p(()=>e[165]||(e[165]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7"},null,-1)])),default:p(()=>[e[166]||(e[166]=t("span",null," The percentage of solids adjusted for the presence of salt, which can affect the density calculations. ",-1))]),_:1})]),i(s,{prop:"solidsAdjustedForSalt",class:"mt-auto"},{default:p(()=>[i(f,{type:"number",controls:!1,step:"any",modelValue:l.targetData.solidsAdjustedForSalt,"onUpdate:modelValue":e[35]||(e[35]=y=>l.targetData.solidsAdjustedForSalt=y),placeholder:"",name:"solidsAdjustedForSalt"},null,8,["modelValue"])]),_:1})]),t("div",yu,[t("label",wu,[e[170]||(e[170]=F(" Fine LCM (lb/bbl) ")),i(o,{placement:"top",width:400,trigger:"hover"},{reference:p(()=>e[168]||(e[168]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7 me-4"},null,-1)])),default:p(()=>[e[169]||(e[169]=t("span",null," The amount of fine LCM added to the mud to control lost circulation. ",-1))]),_:1})]),i(s,{prop:"fineLCM",class:"mt-auto"},{default:p(()=>[i(f,{type:"number",controls:!1,step:"any",modelValue:l.targetData.fineLCM,"onUpdate:modelValue":e[36]||(e[36]=y=>l.targetData.fineLCM=y),placeholder:"",name:"fineLCM"},null,8,["modelValue"])]),_:1})])]),t("div",Su,[t("div",$u,[t("label",xu,[e[173]||(e[173]=t("span",null,"Coarse LCM (lb/bbl)",-1)),i(o,{placement:"top",width:400,trigger:"hover"},{reference:p(()=>e[171]||(e[171]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7"},null,-1)])),default:p(()=>[e[172]||(e[172]=t("span",null," The amount of coarse LCM added to the mud for more severe lost circulation issues. ",-1))]),_:1})]),i(s,{prop:"coarseLCM",class:"mt-auto"},{default:p(()=>[i(f,{type:"number",controls:!1,step:"any",modelValue:l.targetData.coarseLCM,"onUpdate:modelValue":e[37]||(e[37]=y=>l.targetData.coarseLCM=y),placeholder:"",name:"coarseLCM"},null,8,["modelValue"])]),_:1})]),e[174]||(e[174]=t("div",{class:"h-auto w-full"},null,-1)),e[175]||(e[175]=t("div",{class:"h-auto w-full"},null,-1)),e[176]||(e[176]=t("div",{class:"h-auto w-full"},null,-1))]),e[198]||(e[198]=t("h3",{class:"font-bold h-auto w-full border-b-2 border-dashed pb-2"}," Rheological Properties ",-1)),t("div",ku,[t("div",Du,[e[177]||(e[177]=t("label",{class:"font-bold"},[t("span",null,"Shear Rate: 600 (sec^-1) (rpm)")],-1)),i(s,{prop:"shearRate600",class:"mt-auto"},{default:p(()=>[i(f,{type:"number",controls:!1,step:"any",modelValue:l.targetData.shearRate600,"onUpdate:modelValue":e[38]||(e[38]=y=>l.targetData.shearRate600=y),placeholder:"",name:"shearRate600"},null,8,["modelValue"])]),_:1})]),t("div",Vu,[e[178]||(e[178]=t("label",{class:"font-bold"}," Shear Rate: 300 (sec^-1) (rpm) ",-1)),i(s,{prop:"shearRate300",class:"mt-auto"},{default:p(()=>[i(f,{type:"number",controls:!1,step:"any",modelValue:l.targetData.shearRate300,"onUpdate:modelValue":e[39]||(e[39]=y=>l.targetData.shearRate300=y),placeholder:"",name:"shearRate300"},null,8,["modelValue"])]),_:1})]),t("div",Iu,[e[179]||(e[179]=t("label",{class:"font-bold"},[t("span",null,"Shear Rate: 200 (sec^-1) (rpm)")],-1)),i(s,{prop:"shearRate200",class:"mt-auto"},{default:p(()=>[i(f,{type:"number",controls:!1,step:"any",modelValue:l.targetData.shearRate200,"onUpdate:modelValue":e[40]||(e[40]=y=>l.targetData.shearRate200=y),placeholder:"",name:"shearRate200"},null,8,["modelValue"])]),_:1})]),t("div",Pu,[e[180]||(e[180]=t("label",{class:"font-bold"},[t("span",null,"Shear Rate: 100 (sec^-1) (rpm)")],-1)),i(s,{prop:"shearRate100",class:"mt-auto"},{default:p(()=>[i(f,{type:"number",controls:!1,step:"any",modelValue:l.targetData.shearRate100,"onUpdate:modelValue":e[41]||(e[41]=y=>l.targetData.shearRate100=y),placeholder:"",name:"shearRate100"},null,8,["modelValue"])]),_:1})])]),t("div",Cu,[t("div",Tu,[e[181]||(e[181]=t("label",{class:"font-bold"}," Shear Rate: 6 (sec^-1) (rpm) ",-1)),i(s,{prop:"shearRate6",class:"mt-auto"},{default:p(()=>[i(f,{type:"number",controls:!1,step:"any",modelValue:l.targetData.shearRate6,"onUpdate:modelValue":e[42]||(e[42]=y=>l.targetData.shearRate6=y),placeholder:"",name:"shearRate6"},null,8,["modelValue"])]),_:1})]),t("div",Mu,[e[182]||(e[182]=t("label",{class:"font-bold"},[t("span",null,"Shear Rate: 3 (sec^-1) (rpm)")],-1)),i(s,{prop:"shearRate3",class:"mt-auto"},{default:p(()=>[i(f,{type:"number",controls:!1,step:"any",modelValue:l.targetData.shearRate3,"onUpdate:modelValue":e[43]||(e[43]=y=>l.targetData.shearRate3=y),placeholder:"",name:"shearRate3"},null,8,["modelValue"])]),_:1})]),t("div",Fu,[t("label",Nu,[e[185]||(e[185]=t("span",null,"Plastic Viscosity (PV) (cP)",-1)),i(o,{placement:"top",width:400,trigger:"hover"},{reference:p(()=>e[183]||(e[183]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7"},null,-1)])),default:p(()=>[e[184]||(e[184]=t("span",null," Plastic Viscosity is a measure of the resistance to flow of the drilling mud. It is typically measured in centipoise (cP). ",-1))]),_:1})]),i(s,{prop:"plasticViscosity",class:"mt-auto"},{default:p(()=>[i(f,{type:"number",controls:!1,step:"any",modelValue:l.targetData.plasticViscosity,"onUpdate:modelValue":e[44]||(e[44]=y=>l.targetData.plasticViscosity=y),placeholder:"",name:"plasticViscosity"},null,8,["modelValue"])]),_:1})]),t("div",_u,[t("label",Hu,[e[188]||(e[188]=F(" Yield Point (YP) (lbf/100ft2) ")),i(o,{placement:"top",width:400,trigger:"hover"},{reference:p(()=>e[186]||(e[186]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7 me-4"},null,-1)])),default:p(()=>[e[187]||(e[187]=t("span",null," Yield Point is the amount of force required to initiate mud flow. It's typically measured in pounds per 100 square feet (lbf/100ft^2). ",-1))]),_:1})]),i(s,{prop:"yieldPoint",class:"mt-auto"},{default:p(()=>[i(f,{type:"number",controls:!1,step:"any",modelValue:l.targetData.yieldPoint,"onUpdate:modelValue":e[45]||(e[45]=y=>l.targetData.yieldPoint=y),placeholder:"",name:"yieldPoint"},null,8,["modelValue"])]),_:1})])]),t("div",Au,[t("div",zu,[t("label",qu,[e[191]||(e[191]=t("span",null,"Apparent Viscosity (AV) (cP)",-1)),i(o,{placement:"top",width:400,trigger:"hover"},{reference:p(()=>e[189]||(e[189]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7"},null,-1)])),default:p(()=>[e[190]||(e[190]=t("span",null," Apparent Viscosity (AV) (cP) is a measure of the overall resistance to flow of the drilling mud. It's the sum of the plastic viscosity and half of the yield point. ",-1))]),_:1})]),i(s,{prop:"apparentViscosity",class:"mt-auto"},{default:p(()=>[i(f,{type:"number",controls:!1,step:"any",modelValue:l.targetData.apparentViscosity,"onUpdate:modelValue":e[46]||(e[46]=y=>l.targetData.apparentViscosity=y),placeholder:"AV (cP) = PV + YP / 2",name:"apparentViscosity",disabled:""},null,8,["modelValue"])]),_:1})]),t("div",Bu,[t("label",ju,[e[194]||(e[194]=t("span",null,"Shear Rate",-1)),i(o,{placement:"top",width:400,trigger:"hover"},{reference:p(()=>e[192]||(e[192]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7"},null,-1)])),default:p(()=>[e[193]||(e[193]=t("span",null," Shear rate is a measure of how fast the mud is flowing. ",-1))]),_:1})]),i(s,{prop:"shearRate",class:"mt-auto"},{default:p(()=>[i(f,{type:"number",controls:!1,step:"any",modelValue:l.targetData.shearRate,"onUpdate:modelValue":e[47]||(e[47]=y=>l.targetData.shearRate=y),placeholder:"γ = (2 x Pump Speed (rpm)) / (Dial Reading (sec^-1))",name:"shearRate",disabled:""},null,8,["modelValue"])]),_:1})]),t("div",Eu,[t("label",Ru,[e[197]||(e[197]=F(" Shear Stress τ (lbf/100ft²) ")),i(o,{placement:"top",width:400,trigger:"hover"},{reference:p(()=>e[195]||(e[195]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7 me-4"},null,-1)])),default:p(()=>[e[196]||(e[196]=t("span",null," Shear stress is the force applied per unit area on the mud. ",-1))]),_:1})]),i(s,{prop:"shearStress",class:"mt-auto"},{default:p(()=>[i(f,{type:"number",controls:!1,step:"any",modelValue:l.targetData.shearStress,"onUpdate:modelValue":e[48]||(e[48]=y=>l.targetData.shearStress=y),placeholder:"τ (lbf/100ft²) = PV + (YP x Shear Rate)",name:"shearStress",disabled:""},null,8,["modelValue"])]),_:1})])])]),t("div",Ou,[t("button",{type:"button",class:"bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover rounded-md px-4 py-2 font-semibold",onClick:e[49]||(e[49]=(...y)=>l.closeModal&&l.closeModal(...y)),disabled:l.loading}," Discard ",8,Lu),t("button",{class:"bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover rounded-md px-4 py-2 font-semibold",type:"submit",disabled:l.loading},[l.loading?E("",!0):(w(),$("span",Uu," Save ")),l.loading?(w(),$("span",Gu,e[199]||(e[199]=[F(" Please wait... "),t("span",{class:"spinner-border spinner-border-sm align-middle ms-2"},null,-1)]))):E("",!0)],8,Wu)])]),_:1},8,["onSubmit","model","rules"])])])):E("",!0)}const Ju=Q(Rr,[["render",Yu]]),Qu=J({name:"view-sample-modal",components:{SvgIcon:K},props:{isVisible:{type:Boolean,required:!0},close:{type:Function,required:!0}},emits:["editModal"],setup(l,{emit:e}){var P,x;const S=at(),h=v(""),m=v(),n=v(!1),a=v({shearRate:[],shearStress:[]}),o=v([{name:"Shear Stress",data:(P=a==null?void 0:a.value)==null?void 0:P.shearStress}]),r=v({chart:{type:"area",height:350,toolbar:{show:!1}},xaxis:{categories:(x=a==null?void 0:a.value)==null?void 0:x.shearRate,title:{text:"Shear Rate"},tooltip:{enabled:!1}},yaxis:{title:{text:"Shear Stress"}},dataLabels:{enabled:!1},stroke:{curve:"smooth"},tooltip:{x:{show:!1}}});Y(h,b=>{b!==""&&(d(),s())});const d=async()=>{n.value=!0,S.getSampleDetails({id:h.value,callback:{onSuccess:b=>{m.value={...b}},onFinish:()=>{n.value=!1}}})},s=async()=>{S.getSampleChartInfo({id:h.value,callback:{onSuccess:b=>{var T,q,I,C,B,A,_;b!=null&&b.length&&b.sort((N,j)=>(N.shearRate??0)-(j.shearRate??0));for(let N=0;N<(b==null?void 0:b.length);N++)((T=b[N])==null?void 0:T.shearRate)>=3&&((C=(q=a==null?void 0:a.value)==null?void 0:q.shearRate)==null||C.push(Math.round(((I=b[N])==null?void 0:I.shearRate)*100)/100),(_=(B=a==null?void 0:a.value)==null?void 0:B.shearStress)==null||_.push(Math.round(((A=b[N])==null?void 0:A.shearStress)*100)/100));u()}}})},u=()=>{var b,T;o.value=[{name:"Shear Stress",data:(b=a==null?void 0:a.value)==null?void 0:b.shearStress}],r.value={...r.value,xaxis:{categories:(T=a==null?void 0:a.value)==null?void 0:T.shearRate,title:{text:"Shear Rate"}}}},f=()=>{l.close()};return{id:h,series:o,sampleData:m,fluidTypeOptions:lt,sampleFromOptions:tt,chartOptions:r,closeModal:f,setId:b=>{h.value=b.toString()},reset:()=>{h.value="",m.value=null,a.value={shearRate:[],shearStress:[]},u()},getOption:et,formatTime:St,clickEdit:()=>{e("editModal",h.value),f()},loading:n}}}),Ku={key:0,class:"fixed top-0 right-0 bottom-0 left-0 z-40 flex items-center"},Xu={class:"relative bg-card-background text-card-text-light max-h-11/12 w-11/12 flex flex-col items-center gap-3 mx-auto my-6 p-5 rounded-xl overflow-y-scroll md:overflow-hidden lg:w-auto lg:h-auto"},Zu={class:"flex flex-row h-auto w-full items-center justify-between"},ep={key:0,class:"text-center"},tp={key:1,class:"font-semibold"},lp={class:"flex flex-col gap-2 md:gap-7"},op={class:"flex flex-col gap-2 md:flex-row md:gap-4"},sp={class:"flex flex-col gap-2"},np={class:"h-auto w-full flex items-center justify-between"},ap={class:"h-auto w-full flex items-center justify-between"},ip=["checked"],rp={class:"h-auto w-full flex items-center justify-between"},dp={class:"h-auto w-full flex items-center justify-between"},up={class:"h-auto w-full flex items-center justify-between"},pp={class:"h-auto w-full flex items-center justify-between"},cp={class:"h-auto w-full flex items-center justify-between"},fp={class:"h-auto w-full flex items-center justify-between"},mp={class:"h-auto w-full flex items-center justify-between"},gp={class:"h-auto w-full flex items-center justify-between"},bp={class:"h-auto w-full flex items-center justify-between"},hp={class:"h-auto w-full flex items-center justify-between"},vp={class:"h-auto w-full flex items-center justify-between"},yp={class:"h-auto w-full flex items-center justify-between"},wp={class:"h-auto w-full flex items-center justify-between"},Sp={class:"h-auto w-full flex items-center justify-between"},$p={class:"flex flex-col gap-2"},xp={class:"h-auto w-full flex items-center justify-between"},kp={class:"h-auto w-full flex items-center justify-between"},Dp={class:"h-auto w-full flex items-center justify-between"},Vp={class:"h-auto w-full flex items-center justify-between"},Ip={class:"h-auto w-full flex items-center justify-between"},Pp={class:"h-auto w-full flex items-center justify-between"},Cp={class:"h-auto w-full flex items-center justify-between"},Tp={class:"h-auto w-full flex items-center justify-between"},Mp={class:"h-auto w-full flex items-center justify-between"},Fp={class:"h-auto w-full flex items-center justify-between"},Np={class:"h-auto w-full flex items-center justify-between"},_p={class:"h-auto w-full flex items-center justify-between"},Hp={class:"h-auto w-full flex items-center justify-between"},Ap={class:"h-auto w-full flex items-center justify-between"},zp={class:"h-auto w-full flex items-center justify-between"},qp={class:"h-auto w-full flex items-center justify-between"},Bp={class:"flex flex-col gap-2"},jp={class:"h-auto w-full flex items-center justify-between"},Ep={class:"h-auto w-full flex items-center justify-between"},Rp={class:"h-auto w-full flex items-center justify-between"},Op={class:"h-auto w-full flex items-center justify-between"},Lp={class:"h-auto w-full flex items-center justify-between"},Wp={class:"h-auto w-full flex items-center justify-between"},Up={class:"h-auto w-full flex items-center justify-between"},Gp={class:"h-auto w-full flex items-center justify-between"},Yp={class:"h-auto w-full flex items-center justify-between"},Jp={class:"h-auto w-full flex items-center justify-between"},Qp={class:"h-auto w-full flex items-center justify-between"},Kp={class:"h-auto w-full flex items-center justify-between"},Xp={class:"h-auto w-full flex items-center justify-between"},Zp={class:"h-auto w-full flex items-center justify-between"},ec={class:"h-auto w-full flex items-center justify-between"},tc={class:"flex flex-col gap-2 md:flex-row md:gap-4"},lc={class:"flex flex-col gap-2"},oc={class:"h-auto w-full flex items-center justify-between"},sc={class:"h-auto w-full flex items-center justify-between"},nc={class:"h-auto w-full flex items-center justify-between"},ac={class:"h-auto w-full flex items-center justify-between"},ic={class:"h-auto w-full flex items-center justify-between"},rc={class:"h-auto w-full flex items-center justify-between"},dc={class:"h-auto w-full flex items-center justify-between"},uc={class:"h-auto w-full flex items-center justify-between"},pc={class:"h-auto w-full flex items-center justify-between"},cc={class:"h-auto w-full flex items-center justify-between"},fc={class:"h-auto w-full flex items-center justify-between"},mc={class:"h-auto w-full flex flex-row items-center gap-2"};function gc(l,e,S,h,m,n){var s,u,f,c,y,M,P,x,b,T,q,I,C,B,A,_,N,j,z,R,L,U,oe,X,W,G,ee,de,he,ve,$e,xe,we,Ie,Pe,Ce,Te,Me,Fe,Ne,_e,He,Ae,ze,qe,Be,je,Ee,Re,Oe,Le,We,Ue,Ge,Ye,Je,Qe,Ke,Xe;const a=k("SvgIcon"),o=k("el-form-item"),r=k("el-popover"),d=k("apexchart");return l.isVisible?(w(),$("div",Ku,[e[90]||(e[90]=t("div",{class:"absolute inset-0 bg-sidebar-background opacity-25"},null,-1)),t("div",Xu,[t("div",Zu,[e[3]||(e[3]=t("h3",{class:"text-lg font-bold"},"View Sample",-1)),t("span",{class:"cursor-pointer ms-auto",onClick:e[0]||(e[0]=(...ye)=>l.closeModal&&l.closeModal(...ye))},[i(a,{icon:"closeModalIcon"})])]),l.loading?(w(),$("div",ep,e[4]||(e[4]=[t("div",{class:"spinner-border text-primary",role:"status"},[t("span",{class:"sr-only"},"Loading...")],-1)]))):(w(),$("div",tp,[t("div",lp,[t("div",op,[t("div",sp,[t("div",np,[e[5]||(e[5]=t("span",null,"Fluid Type: ",-1)),t("span",null,D(((u=l.getOption((s=l.sampleData)==null?void 0:s.fluidType,l.fluidTypeOptions))==null?void 0:u.label)||""),1)]),t("div",ap,[e[6]||(e[6]=t("span",null,"Weighted Mud",-1)),i(o,{prop:"weightedMud",style:{"margin-bottom":"0"}},{default:p(()=>{var ye;return[t("input",{class:"h-4 w-4",type:"checkbox",checked:(ye=l.sampleData)==null?void 0:ye.weightedMud,disabled:""},null,8,ip)]}),_:1})]),t("div",rp,[e[7]||(e[7]=t("span",null,"Sample From",-1)),t("span",null,D(((c=l.getOption((f=l.sampleData)==null?void 0:f.sampleFrom,l.sampleFromOptions))==null?void 0:c.label)||""),1)]),t("div",dp,[e[8]||(e[8]=t("span",null,"Time Sample Taken (hh:mm) ",-1)),t("span",null,D(l.formatTime((y=l.sampleData)==null?void 0:y.timeSampleTaken)||""),1)]),t("div",up,[e[9]||(e[9]=t("span",null,"Flowline Temperature (F)",-1)),t("span",null,D(((M=l.sampleData)==null?void 0:M.flowlineTemperature)||""),1)]),t("div",pp,[e[10]||(e[10]=t("span",null,"Measured Depth (ft) ",-1)),t("span",null,D(((P=l.sampleData)==null?void 0:P.measuredDepth)||""),1)]),t("div",cp,[e[11]||(e[11]=t("span",null,"MW (ppg or lbs/gal)",-1)),t("span",null,D(((x=l.sampleData)==null?void 0:x.weightedMud)||""),1)]),t("div",fp,[e[12]||(e[12]=t("span",null,"Funnel Viscosity (sec/qt) ",-1)),t("span",null,D(((b=l.sampleData)==null?void 0:b.funnelViscosity)||""),1)]),t("div",mp,[e[13]||(e[13]=t("span",null,"Temperature for Plastic Viscosity (f) ",-1)),t("span",null,D(((T=l.sampleData)==null?void 0:T.temperatureForPlasticViscosity)||""),1)]),t("div",gp,[e[14]||(e[14]=t("span",null,"PV (Plastic Viscosity) (cP) ",-1)),t("span",null,D(((q=l.sampleData)==null?void 0:q.plasticViscosity)||""),1)]),t("div",bp,[e[15]||(e[15]=t("span",null,"YP (Yield Point) (lbf/100ft2)",-1)),t("span",null,D(((I=l.sampleData)==null?void 0:I.yieldPoint)||""),1)]),t("div",hp,[e[16]||(e[16]=t("span",null,"Gel Str. 10s (lbf/100ft2) ",-1)),t("span",null,D(((C=l.sampleData)==null?void 0:C.gelStrength10s)||""),1)]),t("div",vp,[e[17]||(e[17]=t("span",null,"Gel Str. 10m (lbf/100ft2) ",-1)),t("span",null,D(((B=l.sampleData)==null?void 0:B.gelStrength10m)||""),1)]),t("div",yp,[e[18]||(e[18]=t("span",null,"Gel Str. 30m (lbf/100ft2) ",-1)),t("span",null,D(((A=l.sampleData)==null?void 0:A.gelStrength30m)||""),1)]),t("div",wp,[e[19]||(e[19]=t("span",null,"API filtrate (ml/30min) ",-1)),t("span",null,D(((_=l.sampleData)==null?void 0:_.apiFiltrate)||""),1)]),t("div",Sp,[e[20]||(e[20]=t("span",null,"API cake thickness (1/32in)",-1)),t("span",null,D(((N=l.sampleData)==null?void 0:N.apiCakeThickness)||""),1)])]),t("div",$p,[t("div",xp,[e[21]||(e[21]=t("span",null,"Temperature for HTHP (F) ",-1)),t("span",null,D(((j=l.sampleData)==null?void 0:j.temperatureForHTHP)||""),1)]),t("div",kp,[e[22]||(e[22]=t("span",null,"HTHP filtrate (ml/30min)",-1)),t("span",null,D(((z=l.sampleData)==null?void 0:z.hthpFiltrate)||""),1)]),t("div",Dp,[e[23]||(e[23]=t("span",null,"HTHP cake thickness (1/32in)",-1)),t("span",null,D(((R=l.sampleData)==null?void 0:R.hthpCakeThickness)||""),1)]),t("div",Vp,[e[24]||(e[24]=t("span",null,"Solids (%)",-1)),t("span",null,D(((L=l.sampleData)==null?void 0:L.solids)||""),1)]),t("div",Ip,[e[25]||(e[25]=t("span",null,"Oil (%)",-1)),t("span",null,D(((U=l.sampleData)==null?void 0:U.oil)||""),1)]),t("div",Pp,[e[26]||(e[26]=t("span",null,"Water (%)",-1)),t("span",null,D(((oe=l.sampleData)==null?void 0:oe.water)||""),1)]),t("div",Cp,[e[27]||(e[27]=t("span",null,"Sand Content (%)",-1)),t("span",null,D(((X=l.sampleData)==null?void 0:X.sandContent)||""),1)]),t("div",Tp,[e[28]||(e[28]=t("span",null,"MBT capacity (lb/bbl)",-1)),t("span",null,D(((W=l.sampleData)==null?void 0:W.mbtCapacity)||""),1)]),t("div",Mp,[e[29]||(e[29]=t("span",null,"pH ",-1)),t("span",null,D(((G=l.sampleData)==null?void 0:G.pH)||""),1)]),t("div",Fp,[e[30]||(e[30]=t("span",null,"Mud Alkalinity (Pm) (ml)",-1)),t("span",null,D(((ee=l.sampleData)==null?void 0:ee.mudAlkalinity)||""),1)]),t("div",Np,[e[31]||(e[31]=t("span",null,"Filtrate Alkalinity (Pf) (ml)",-1)),t("span",null,D(((de=l.sampleData)==null?void 0:de.filtrateAlkalinity)||""),1)]),t("div",_p,[e[32]||(e[32]=t("span",null,"Filtrate Alkalinity (Pf) (ml)",-1)),t("span",null,D(((he=l.sampleData)==null?void 0:he.filtrateAlkalinity)||""),1)]),t("div",Hp,[e[33]||(e[33]=t("span",null,"Calcium (mg/L) ",-1)),t("span",null,D(((ve=l.sampleData)==null?void 0:ve.calcium)||""),1)]),t("div",Ap,[e[34]||(e[34]=t("span",null,"Chlorides (mg/L) ",-1)),t("span",null,D((($e=l.sampleData)==null?void 0:$e.chlorides)||""),1)]),t("div",zp,[e[35]||(e[35]=t("span",null,"Total Hardness (mg/L)",-1)),t("span",null,D(((xe=l.sampleData)==null?void 0:xe.totalHardness)||""),1)]),t("div",qp,[e[36]||(e[36]=t("span",null,"Excess Lime (lb/bbl)",-1)),t("span",null,D(((we=l.sampleData)==null?void 0:we.excessLime)||""),1)])]),t("div",Bp,[t("div",jp,[e[37]||(e[37]=t("span",null,"K+ (mg/L)",-1)),t("span",null,D(((Ie=l.sampleData)==null?void 0:Ie.kPlus)||""),1)]),t("div",Ep,[e[38]||(e[38]=t("span",null,"Make up water: Chlorides (mg/L)",-1)),t("span",null,D(((Pe=l.sampleData)==null?void 0:Pe.makeUpWater)||""),1)]),t("div",Rp,[e[39]||(e[39]=t("span",null,"Solids adjusted for salt (%)",-1)),t("span",null,D(((Ce=l.sampleData)==null?void 0:Ce.solidsAdjustedForSalt)||""),1)]),t("div",Op,[e[40]||(e[40]=t("span",null,"Fine LCM (lb/bbl)",-1)),t("span",null,D(((Te=l.sampleData)==null?void 0:Te.fineLCM)||""),1)]),t("div",Lp,[e[41]||(e[41]=t("span",null,"Coarse LCM (lb/bbl)",-1)),t("span",null,D(((Me=l.sampleData)==null?void 0:Me.coarseLCM)||""),1)]),t("div",Wp,[t("span",null,[e[44]||(e[44]=F("Linear Gel Strength (LGS) (%) ")),i(r,{placement:"top",width:300,trigger:"hover"},{reference:p(()=>e[42]||(e[42]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7"},null,-1)])),default:p(()=>[e[43]||(e[43]=t("span",null," LGS (%) = (Gel Strength (lbs/100 ft²) / Mud Weight (lbs/gal)) x 100 ",-1))]),_:1})]),t("span",null,D(((Fe=l.sampleData)==null?void 0:Fe.linearGelStrengthPercent)||""),1)]),t("div",Up,[t("span",null,[e[47]||(e[47]=F("Linear Gel Strength (LGS) (lb/bbl)")),i(r,{placement:"top",width:300,trigger:"hover"},{reference:p(()=>e[45]||(e[45]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7"},null,-1)])),default:p(()=>[e[46]||(e[46]=t("span",null," LGS (lb/bbl) = (Gel Strength (lbs/100 ft²) / (Mud Weight (lbs/gal) * 42)) x 100 ",-1))]),_:1})]),t("span",null,D(((Ne=l.sampleData)==null?void 0:Ne.linearGelStrengthLbBbl)||""),1)]),t("div",Gp,[t("span",null,[e[50]||(e[50]=F("High Gel Strength (HGS) (%)")),i(r,{placement:"top",width:300,trigger:"hover"},{reference:p(()=>e[48]||(e[48]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7"},null,-1)])),default:p(()=>[e[49]||(e[49]=t("span",null," HGS (%) = (Gel Str. 30m (lbf/100ft²) / MW (lb/gal)) x 100 ",-1))]),_:1})]),t("span",null,D(((_e=l.sampleData)==null?void 0:_e.highGelStrengthPercent)||""),1)]),t("div",Yp,[t("span",null,[e[53]||(e[53]=F("High Gel Strength (HGS) (lb/bbl)")),i(r,{placement:"top",width:300,trigger:"hover"},{reference:p(()=>e[51]||(e[51]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7"},null,-1)])),default:p(()=>[e[52]||(e[52]=t("span",null," HGS (lb/bbl) = Gel Str. 30m (lbf/100ft²) / 100 ",-1))]),_:1})]),t("span",null,D(((He=l.sampleData)==null?void 0:He.highGelStrengthLbBbl)||""),1)]),t("div",Jp,[t("span",null,[e[56]||(e[56]=F("Bentonite Concentration (%)")),i(r,{placement:"top",width:300,trigger:"hover"},{reference:p(()=>e[54]||(e[54]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7"},null,-1)])),default:p(()=>[e[55]||(e[55]=t("span",null," Bentonite (%) = (MBT capacity (lb/bbl) / MW (lb/gal)) x 100 ",-1))]),_:1})]),t("span",null,D(((Ae=l.sampleData)==null?void 0:Ae.bentoniteConcentrationPercent)||""),1)]),t("div",Qp,[t("span",null,[e[59]||(e[59]=F("Bentonite Concentration (lb/bbl)")),i(r,{placement:"top",width:300,trigger:"hover"},{reference:p(()=>e[57]||(e[57]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7"},null,-1)])),default:p(()=>[e[58]||(e[58]=t("span",null," Bentonite (lb/bbl) = MBT capacity (lb/bbl) ",-1))]),_:1})]),t("span",null,D(((ze=l.sampleData)==null?void 0:ze.bentoniteConcentrationLbBbl)||""),1)]),t("div",Kp,[t("span",null,[e[62]||(e[62]=F("Drill Solids Concentration (%)")),i(r,{placement:"top",width:300,trigger:"hover"},{reference:p(()=>e[60]||(e[60]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7"},null,-1)])),default:p(()=>[e[61]||(e[61]=t("span",null," Drill Solids (%) = Solids (%) - Bentonite (%) ",-1))]),_:1})]),t("span",null,D(((qe=l.sampleData)==null?void 0:qe.drillSolidsConcentrationPercent)||""),1)]),t("div",Xp,[t("span",null,[e[65]||(e[65]=F("Drill Solids Concentration (lb/bbl)")),i(r,{placement:"top",width:300,trigger:"hover"},{reference:p(()=>e[63]||(e[63]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7"},null,-1)])),default:p(()=>[e[64]||(e[64]=t("span",null," Drill Solids (lb/bbl) = (Solids (%) - Bentonite (%)) x MW (lb/gal) / 100 ",-1))]),_:1})]),t("span",null,D(((Be=l.sampleData)==null?void 0:Be.drillSolidsConcentrationLbBbl)||""),1)]),t("div",Zp,[t("span",null,[e[68]||(e[68]=F("DS/Bent Ratio (Drill Solids to Bentonite Ratio)")),i(r,{placement:"top",width:300,trigger:"hover"},{reference:p(()=>e[66]||(e[66]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7"},null,-1)])),default:p(()=>[e[67]||(e[67]=t("span",null," DS/Bent Ratio = (Drill Solids (lb/bbl) / Bentonite (lb/bbl)) ",-1))]),_:1})]),t("span",null,D(((je=l.sampleData)==null?void 0:je.drillSolidsToBentoniteRatio)||""),1)]),t("div",ec,[t("span",null,[e[71]||(e[71]=F("Average Specific Gravity of Solids")),i(r,{placement:"top",width:300,trigger:"hover"},{reference:p(()=>e[69]||(e[69]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7"},null,-1)])),default:p(()=>[e[70]||(e[70]=t("span",null," Average SG of Solids = Σ(Specific Gravity of Each Solid x Volume Fraction of Each Solid) ",-1))]),_:1})]),t("span",null,D(((Ee=l.sampleData)==null?void 0:Ee.averageSpecificGravityOfSolids)||""),1)])])]),t("div",tc,[t("div",lc,[e[89]||(e[89]=t("h3",{class:"font-bold h-auto w-full border-b-2 border-dashed pb-2"}," Rheological Properties ",-1)),t("div",oc,[e[72]||(e[72]=t("span",null,"Shear Rate: 600 (sec^-1) (rpm) ",-1)),t("span",null,D(((Re=l.sampleData)==null?void 0:Re.shearRate600)||""),1)]),t("div",sc,[e[73]||(e[73]=t("span",null,"Shear Rate: 300 (sec^-1) (rpm)",-1)),t("span",null,D(((Oe=l.sampleData)==null?void 0:Oe.shearRate300)||""),1)]),t("div",nc,[e[74]||(e[74]=t("span",null,"Shear Rate: 200 (sec^-1) (rpm)",-1)),t("span",null,D(((Le=l.sampleData)==null?void 0:Le.shearRate200)||""),1)]),t("div",ac,[e[75]||(e[75]=t("span",null,"Shear Rate: 100 (sec^-1) (rpm)",-1)),t("span",null,D(((We=l.sampleData)==null?void 0:We.shearRate100)||""),1)]),t("div",ic,[e[76]||(e[76]=t("span",null,"Shear Rate: 6 (sec^-1) (rpm)",-1)),t("span",null,D(((Ue=l.sampleData)==null?void 0:Ue.shearRate6)||""),1)]),t("div",rc,[e[77]||(e[77]=t("span",null,"Shear Rate: 3 (sec^-1) (rpm)",-1)),t("span",null,D(((Ge=l.sampleData)==null?void 0:Ge.shearRate3)||""),1)]),t("div",dc,[e[78]||(e[78]=t("span",null,"Plastic Viscosity (PV) (cP)",-1)),t("span",null,D(((Ye=l.sampleData)==null?void 0:Ye.plasticViscosity)||""),1)]),t("div",uc,[e[79]||(e[79]=t("span",null,"Yield Point (YP) (lbf/100ft2)",-1)),t("span",null,D(((Je=l.sampleData)==null?void 0:Je.yieldPoint)||""),1)]),t("div",pc,[t("span",null,[e[82]||(e[82]=F("Apparent Viscosity (AV) (cP) ")),i(r,{placement:"top",width:300,trigger:"hover"},{reference:p(()=>e[80]||(e[80]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7"},null,-1)])),default:p(()=>[e[81]||(e[81]=t("span",null,[F(" Apparent Viscosity (AV) (cP) is a measure of the overall resistance to flow of the drilling mud. It's the sum of the plastic viscosity and half of the yield point."),t("br"),t("br"),F(" AV (cP) = PV + YP / 2 ")],-1))]),_:1})]),t("span",null,D(((Qe=l.sampleData)==null?void 0:Qe.apparentViscosity)||""),1)]),t("div",cc,[t("span",null,[e[85]||(e[85]=F("Shear Rate")),i(r,{placement:"top",width:300,trigger:"hover"},{reference:p(()=>e[83]||(e[83]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7"},null,-1)])),default:p(()=>[e[84]||(e[84]=t("span",null,[F(" Shear rate is a measure of how fast the mud is flowing. "),t("br"),t("br"),F(" γ = (2 x Pump Speed (rpm)) / (Dial Reading (sec^-1)) ")],-1))]),_:1})]),t("span",null,D(((Ke=l.sampleData)==null?void 0:Ke.shearRate)||""),1)]),t("div",fc,[t("span",null,[e[88]||(e[88]=F("Shear Stress τ (lbf/100ft²)")),i(r,{placement:"top",width:300,trigger:"hover"},{reference:p(()=>e[86]||(e[86]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7"},null,-1)])),default:p(()=>[e[87]||(e[87]=t("span",null,[F(" Shear stress is the force applied per unit area on the mud. "),t("br"),t("br"),F(" τ (lbf/100ft²) = PV + (YP x Shear Rate) ")],-1))]),_:1})]),t("span",null,D(((Xe=l.sampleData)==null?void 0:Xe.shearStress)||""),1)])]),i(d,{type:"area",options:l.chartOptions,series:l.series,class:"h-auto w-3/4"},null,8,["options","series"])])])])),t("div",mc,[t("button",{type:"button",class:"bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover rounded-md px-4 py-2 font-semibold md:px-4 md:py-3 md:text-xl",onClick:e[1]||(e[1]=(...ye)=>l.closeModal&&l.closeModal(...ye))}," Close "),t("button",{class:"bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover rounded-md px-4 py-2 font-semibold md:px-4 md:py-3 md:text-xl",type:"button",onClick:e[2]||(e[2]=(...ye)=>l.clickEdit&&l.clickEdit(...ye))}," Edit ")])])])):E("",!0)}const bc=Q(Qu,[["render",gc]]),hc=J({name:"sample",components:{SvgIcon:K,BottomTool:ce,SampleModal:Ju,EditTargetedPropertiesModal:Er,ViewSampleModal:bc,NoEntries:me},setup(){var C,B;const l=re(),e=at(),S=zt(),h=v(!1),m=v(),n=v(null),a=v(null),o=v(null),r=v([]),d=(B=(C=l.params)==null?void 0:C.id)==null?void 0:B.toString(),s=le("dailyReport"),u=v(!1),f=v(!1),c=v(!1);ae(()=>{s!=null&&s.getDailyReportId()&&M(),y()});const y=async()=>{h.value=!0,S.getTargetPropertyDetails({wellId:d,callback:{onSuccess:A=>{m.value=A},onFinish:A=>{h.value=!1}}})},M=async(A={dailyReportId:s==null?void 0:s.getDailyReportId(),page:1,limit:200})=>{h.value=!0,e.getSamples({params:A,callback:{onSuccess:_=>{r.value=_==null?void 0:_.items},onFinish:_=>{h.value=!1}}})},P=A=>{var _;A&&((_=a==null?void 0:a.value)==null||_.setId(A)),f.value=!f.value},x=()=>{u.value=!u.value},b=A=>{var _;(_=n==null?void 0:n.value)==null||_.setId(A),u.value=!u.value},T=()=>{c.value=!c.value},q=A=>{se.deletionAlert({onConfirmed:()=>{I(A)}})},I=async A=>{h.value=!0,e.deleteSample({id:A,callback:{onSuccess:_=>{M()},onFinish:_=>{h.value=!1}}})};return{wellId:d,sampleList:r,loading:h,sampleModal:n,targetPropertyData:m,fluidTypeOptions:lt,viewSampleModal:a,sampleFromOptions:tt,editTargetedPropertiesModal:o,isSampleModalVisible:u,isViewSampleModalVisible:f,isEditSampleModalVisible:c,getOption:et,getSamples:M,formatTime:St,deleteSample:q,numberWithCommas:fe,getTargetProperty:y,toggleNewSampleModal:x,toggleEditSampleModal:b,toggleViewSampleModal:P,toggleEditTargetedPropertiesModal:T}}}),vc={class:"relative h-auto w-full mx-auto bg-minicard-background text-minicard-text-light mt-4 p-4 rounded-xl border-t-2 border-active font-semibold"},yc={class:"flex flex-row items-center gap-2 absolute top-0 end-0 -translate-y-1/2"},wc={class:"h-auto w-full font-semibold"},Sc={class:"h-auto w-full flex flex-col gap-3 md:flex-row md:gap-4"},$c={class:"h-auto w-full flex flex-col gap-3"},xc={class:"flex items-center justify-between border-dashed border-b-[1px] py-3"},kc={class:"flex items-center justify-between border-dashed border-b-[1px] py-3"},Dc={class:"flex items-center justify-between border-dashed border-b-[1px] py-3"},Vc={class:"flex items-center justify-between border-dashed border-b-[1px] py-3"},Ic={class:"flex items-center justify-between border-dashed border-b-[1px] py-3"},Pc={class:"flex items-center justify-between border-dashed border-b-[1px] py-3"},Cc={class:"flex items-center justify-between border-dashed border-b-[1px] py-3"},Tc={class:"flex items-center justify-between border-dashed border-b-[1px] py-3 md:border-b-0"},Mc={class:"h-auto w-full flex flex-col gap-3"},Fc={class:"flex items-center justify-between border-dashed border-b-[1px] py-3"},Nc={class:"flex items-center justify-between border-dashed border-b-[1px] py-3"},_c={class:"flex items-center justify-between border-dashed border-b-[1px] py-3"},Hc={class:"flex items-center justify-between border-dashed border-b-[1px] py-3"},Ac={class:"flex items-center justify-between border-dashed border-b-[1px] py-3"},zc={class:"flex items-center justify-between border-dashed border-b-[1px] py-3"},qc={class:"flex items-center justify-between border-dashed border-b-[1px] py-3"},Bc={key:0,class:"text-center"},jc={key:1,class:"grid grid-cols-1 gap-4 md:grid-cols-2 md:gap-7 lg:grid-cols-3"},Ec=["onClick"],Rc={class:"text-xl font-bold"},Oc={class:"d-flex flex-column gap-3"},Lc={class:"flex items-center justify-between border-dashed border-b-[1px] py-3"},Wc={class:"text-success"},Uc={class:"flex items-center justify-between border-dashed border-b-[1px] py-3"},Gc={class:"text-success"},Yc={class:"flex items-center justify-between pt-3"},Jc={class:"flex flex-wrap items-center gap-2"},Qc={class:"flex items-center gap-2 border border-gray-300 border-dashed rounded py-3 px-4"},Kc={class:"flex items-center gap-2 border border-gray-300 border-dashed rounded py-3 px-4"},Xc={class:"flex flex-row items-center gap-2 absolute top-0 end-0 -translate-y-1/2"},Zc=["onClick"],ef={class:"svg-icon svg-icon-3"},tf=["onClick"],lf={class:"text-danger"};function of(l,e,S,h,m,n){var f,c,y,M,P,x,b,T,q,I,C,B,A,_,N,j;const a=k("SvgIcon"),o=k("NoEntries"),r=k("BottomTool"),d=k("SampleModal"),s=k("EditTargetedPropertiesModal"),u=k("ViewSampleModal");return w(),$(O,null,[t("div",vc,[t("div",yc,[t("button",{class:"p-2 rounded-full bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover",onClick:e[0]||(e[0]=(...z)=>l.toggleEditTargetedPropertiesModal&&l.toggleEditTargetedPropertiesModal(...z))},[t("span",null,[i(a,{icon:"pencilIcon"})])])]),t("div",wc,[e[16]||(e[16]=t("h5",{class:"font-bold h-auto w-full border-b-2 border-dashed pb-2"}," Targeted Properties ",-1)),t("div",Sc,[t("div",$c,[t("div",xc,[e[1]||(e[1]=t("span",null,"Fluid Type: ",-1)),t("span",null,D(((c=l.getOption((f=l.targetPropertyData)==null?void 0:f.fluidType,l.fluidTypeOptions))==null?void 0:c.label)||""),1)]),t("div",kc,[e[2]||(e[2]=t("span",null,"MW (ppg or lbs/gal):",-1)),t("span",null,D((y=l.targetPropertyData)==null?void 0:y.mudWeight),1)]),t("div",Dc,[e[3]||(e[3]=t("span",null,"Funnel Viscosity (sec/qt):",-1)),t("span",null,D((M=l.targetPropertyData)==null?void 0:M.funnelViscosity),1)]),t("div",Vc,[e[4]||(e[4]=t("span",null,"PV (Plastic Viscosity) (cP):",-1)),t("span",null,D((P=l.targetPropertyData)==null?void 0:P.plasticViscosity),1)]),t("div",Ic,[e[5]||(e[5]=t("span",null,"YP (Yield Point) (lbf/100ft2): ",-1)),t("span",null,D((x=l.targetPropertyData)==null?void 0:x.yieldPoint),1)]),t("div",Pc,[e[6]||(e[6]=t("span",null,"6 rpm:",-1)),t("span",null,D((b=l.targetPropertyData)==null?void 0:b.rpm),1)]),t("div",Cc,[e[7]||(e[7]=t("span",null,"API filtrate (ml/30min):",-1)),t("span",null,D((T=l.targetPropertyData)==null?void 0:T.apiFiltrate),1)]),t("div",Tc,[e[8]||(e[8]=t("span",null,"API Cake: ",-1)),t("span",null,D((q=l.targetPropertyData)==null?void 0:q.apiCakeThickness),1)])]),t("div",Mc,[t("div",Fc,[e[9]||(e[9]=t("span",null,"pH:",-1)),t("span",null,D((I=l.targetPropertyData)==null?void 0:I.pH),1)]),t("div",Nc,[e[10]||(e[10]=t("span",null,"Mud Alkalinity (Pm) (ml): ",-1)),t("span",null,D((C=l.targetPropertyData)==null?void 0:C.mudAlkalinity),1)]),t("div",_c,[e[11]||(e[11]=t("span",null,"Filtrate Alkalinity (Pf) (ml):",-1)),t("span",null,D((B=l.targetPropertyData)==null?void 0:B.filtrateAlkalinity),1)]),t("div",Hc,[e[12]||(e[12]=t("span",null,"Filtrate Alkalinity (Mf) (ml):",-1)),t("span",null,D((A=l.targetPropertyData)==null?void 0:A.filtrateAlkalinity),1)]),t("div",Ac,[e[13]||(e[13]=t("span",null,"Chlorides (mg/L):",-1)),t("span",null,D((_=l.targetPropertyData)==null?void 0:_.chlorides),1)]),t("div",zc,[e[14]||(e[14]=t("span",null,"Total Hardness (mg/L):",-1)),t("span",null,D((N=l.targetPropertyData)==null?void 0:N.totalHardness),1)]),t("div",qc,[e[15]||(e[15]=t("span",null,"Linear Gel Strength (LGS) (%):",-1)),t("span",null,D((j=l.targetPropertyData)==null?void 0:j.linearGelStrengthPercent),1)])])])])]),l.loading?(w(),$("div",Bc,e[17]||(e[17]=[t("div",{class:"spinner-border text-primary",role:"status"},[t("span",{class:"sr-only"},"Loading...")],-1)]))):(w(),$(O,{key:1},[l.sampleList.length===0?(w(),te(o,{key:0,addNew:l.toggleNewSampleModal},null,8,["addNew"])):(w(),$("div",jc,[(w(!0),$(O,null,Z(l.sampleList,z=>{var R,L;return w(),$("div",{key:z==null?void 0:z.id,class:"relative h-auto w-11/12 mx-auto bg-minicard-background text-minicard-text-light flex flex-col items-center p-4 rounded-xl border-t-2 border-active my-7 first:mt-4 last:mb-5 font-semibold md:first:mt-0 md:last:mb-0 md:m-0"},[t("div",{class:"h-auto w-full flex flex-col gap-3",onClick:U=>l.toggleViewSampleModal(z==null?void 0:z.id.toString())},[t("h5",Rc,D(z==null?void 0:z.name),1),t("div",Oc,[t("div",Lc,[e[18]||(e[18]=t("span",null,"Sample From",-1)),t("span",Wc,D(((R=l.getOption(z==null?void 0:z.sampleFrom,l.sampleFromOptions))==null?void 0:R.label)||""),1)]),t("div",Uc,[e[19]||(e[19]=t("span",null,"Type",-1)),t("span",Gc,D((L=l.getOption(z==null?void 0:z.fluidType,l.fluidTypeOptions))==null?void 0:L.label),1)]),t("div",Yc,[e[20]||(e[20]=t("span",null,"Time Sample Taken",-1)),t("span",null,D(l.formatTime(z==null?void 0:z.timeSampleTaken)),1)])]),t("div",Jc,[t("div",Qc,[t("div",null,D(`${l.numberWithCommas(z==null?void 0:z.mudWeight)} (ppg)`),1),e[21]||(e[21]=t("div",{class:"text-danger"},"MW",-1))]),t("div",Kc,[t("div",null,D(`${l.numberWithCommas(z==null?void 0:z.measuredDepth)} (in)`),1),e[22]||(e[22]=t("div",null,"Depth",-1))])])],8,Ec),t("div",Xc,[t("button",{class:"p-2 rounded-full bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover",onClick:U=>l.toggleEditSampleModal(z==null?void 0:z.id.toString())},[t("span",ef,[i(a,{icon:"pencilIcon"})])],8,Zc),t("button",{class:"p-2 rounded-full bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover",onClick:U=>l.deleteSample(z==null?void 0:z.id.toString())},[t("span",lf,[i(a,{icon:"trashIcon"})])],8,tf)])])}),128))]))],64)),i(r,{addNew:l.toggleNewSampleModal,showHelpInfo:!1},null,8,["addNew"]),i(d,{isVisible:l.isSampleModalVisible,close:l.toggleNewSampleModal,ref:"sampleModal",loadPage:l.getSamples},null,8,["isVisible","close","loadPage"]),i(s,{isVisible:l.isEditSampleModalVisible,close:l.toggleEditTargetedPropertiesModal,ref:"editTargetedPropertiesModal",wellId:l.wellId,loadPage:l.getTargetProperty},null,8,["isVisible","close","wellId","loadPage"]),i(u,{isVisible:l.isViewSampleModalVisible,close:l.toggleViewSampleModal,ref:"viewSampleModal",onEditModal:l.toggleEditSampleModal},null,8,["isVisible","close","onEditModal"])],64)}const qt=Q(hc,[["render",of]]),sf=ne("solid",()=>({getSolidDetails:async({dailyReportId:m,callback:n})=>{var r;const a=g.get(n,"onSuccess",g.noop),o=g.get(n,"onFinish",g.noop);try{V.setHeader();const d=await V.get(`solids/${m}`);a(((r=d.data)==null?void 0:r.data)||d.data)}catch(d){H(d,n)}finally{o()}},updateSolid:async({id:m,params:n,callback:a})=>{var d;const o=g.get(a,"onSuccess",g.noop),r=g.get(a,"onFinish",g.noop);try{V.setHeader();const s=await V.put(`solids/${m}`,n);o(((d=s.data)==null?void 0:d.data)||s.data)}catch(s){H(s,a)}finally{r()}},deleteSolid:async({id:m,callback:n})=>{var r;const a=g.get(n,"onSuccess",g.noop),o=g.get(n,"onFinish",g.noop);try{V.setHeader();const d=await V.delete(`solids/${m}`);a(((r=d.data)==null?void 0:r.data)||d.data)}catch(d){H(d,n)}finally{o()}},createSolid:async({params:m,callback:n})=>{var r;const a=g.get(n,"onSuccess",g.noop),o=g.get(n,"onFinish",g.noop);try{V.setHeader();const d=await V.post("solids",m);a(((r=d.data)==null?void 0:r.data)||d.data)}catch(d){H(d,n)}finally{o()}}})),nf=J({name:"solid",components:{SvgIcon:K,BottomTool:ce},setup(){const l=re(),e=le("dailyReport"),S=sf(),h=v(null),m=v({shaleCEC:null,bentCEC:null,highGelStrength:null,linearGelStrength:null}),n=v(!1),a=v(!1),o=v({...m.value}),r=v({});ht((P,x,b)=>{xt(P,x,b,a.value||f())}),ae(()=>{d()});const d=async()=>{e!=null&&e.getDailyReportId()&&(n.value=!0,S.getSolidDetails({dailyReportId:e==null?void 0:e.getDailyReportId(),callback:{onSuccess:P=>{P&&(o.value={...P},m.value={...P})},onFinish:P=>{n.value=!1}}}))},s=async P=>{var x;a.value=!0,S.updateSolid({id:((x=o==null?void 0:o.value)==null?void 0:x.id)||"",params:P,callback:{onSuccess:b=>{d()},onFinish:()=>{a.value=!1}}})},u=async P=>{S.createSolid({params:P,callback:{onSuccess:x=>{d()},onFinish:()=>{a.value=!1}}})},f=()=>!vt.isEqual(o.value,m.value);return{cancel:()=>{se.incompleteFormAlert({onConfirmed:()=>{o.value=JSON.parse(JSON.stringify(m.value))}},"Cancel changes on this section?","Yes")},submit:()=>{h.value&&h.value.validate(P=>{var x,b,T,q,I,C;if(P){if(a.value||!f())return;const B={shaleCEC:Number((x=o==null?void 0:o.value)==null?void 0:x.shaleCEC),bentCEC:Number((b=o==null?void 0:o.value)==null?void 0:b.bentCEC),highGelStrength:Number((T=o==null?void 0:o.value)==null?void 0:T.highGelStrength),linearGelStrength:Number((q=o==null?void 0:o.value)==null?void 0:q.linearGelStrength)};(I=o==null?void 0:o.value)!=null&&I.id?s({...B,dailyReportId:e==null?void 0:e.getDailyReportId()}):(a.value=!0,e==null||e.createDailyReport({wellId:(C=l==null?void 0:l.params)==null?void 0:C.id,callback:{onSuccess:A=>{u({...B,dailyReportId:e==null?void 0:e.getDailyReportId()})},onFailure:()=>{a.value=!1}}}))}})},isFormDirty:f,isValidForm:async()=>{var x;return await((x=h==null?void 0:h.value)==null?void 0:x.validate(b=>b))},rules:r,loading:n,submitting:a,formRef:h,targetData:o}}}),af={key:0,class:"text-center"},rf={key:1},df={class:"flex flex-col gap-1 md:flex-row md:gap-4"},uf={class:"h-auto w-full flex flex-col gap-1"},pf={class:"h-auto w-full flex flex-col gap-1"},cf={class:"font-bold text-sm"},ff={class:"h-auto w-full flex flex-col gap-1"},mf={class:"font-bold text-sm"},gf={class:"h-auto w-full flex flex-col gap-1"},bf={class:"h-auto w-full flex flex-col gap-1"},hf={class:"font-bold text-sm"},vf={class:"h-auto w-full flex flex-col gap-1"},yf={class:"font-bold text-sm"},wf={class:"h-auto w-full flex flex-row items-center gap-2"},Sf=["disabled"],$f=["disabled"],xf={key:0,class:"indicator-label"},kf={key:1,class:"indicator-progress"};function Df(l,e,S,h,m,n){const a=k("el-popover"),o=k("el-input"),r=k("el-form-item"),d=k("el-form"),s=k("BottomTool");return w(),$(O,null,[i(d,{onSubmit:ie(l.submit,["prevent"]),model:l.targetData,rules:l.rules,ref:"formRef",class:"bg-minicard-background text-minicard-text-light h-auto w-11/12 mx-auto p-4 rounded-xl"},{default:p(()=>[l.loading?(w(),$("div",af,e[5]||(e[5]=[t("div",{class:"spinner-border text-primary",role:"status"},[t("span",{class:"sr-only"},"Loading...")],-1)]))):(w(),$("div",rf,[t("div",df,[t("div",uf,[t("div",pf,[t("label",cf,[e[8]||(e[8]=t("span",null,"Shale CEC (Cation Exchange Capacity) (mg/g)",-1)),i(a,{placement:"top",width:400,trigger:"hover"},{reference:p(()=>e[6]||(e[6]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7"},null,-1)])),default:p(()=>[e[7]||(e[7]=t("span",null," Shale CEC is a measure of the cation exchange capacity of shale particles present in the drilling mud. It quantifies the shale's ability to adsorb and exchange ions with the mud. High CEC can lead to mud instability and unwanted interactions. ",-1))]),_:1})]),i(r,{prop:"shaleCEC"},{default:p(()=>[i(o,{type:"number",controls:!1,step:"any",modelValue:l.targetData.shaleCEC,"onUpdate:modelValue":e[0]||(e[0]=u=>l.targetData.shaleCEC=u),placeholder:"",name:"shaleCEC"},null,8,["modelValue"])]),_:1})]),t("div",ff,[t("label",mf,[e[11]||(e[11]=t("span",null,"Bent CEC (Cation Exchange Capacity) (mg/g)",-1)),i(a,{placement:"top",width:400,trigger:"hover"},{reference:p(()=>e[9]||(e[9]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7"},null,-1)])),default:p(()=>[e[10]||(e[10]=t("span",null," Bentonite CEC is a measure of the cation exchange capacity of bentonite clay used as an additive in drilling mud. It assesses the clay's capacity to exchange ions with the mud. Bentonite is often added to enhance mud properties. ",-1))]),_:1})]),i(r,{prop:"bentCEC"},{default:p(()=>[i(o,{type:"number",controls:!1,step:"any",modelValue:l.targetData.bentCEC,"onUpdate:modelValue":e[1]||(e[1]=u=>l.targetData.bentCEC=u),placeholder:"",name:"bentCEC"},null,8,["modelValue"])]),_:1})])]),t("div",gf,[t("div",bf,[t("label",hf,[e[14]||(e[14]=t("span",null,"HGS (High Gel Strength) Specific Gravity",-1)),i(a,{placement:"top",width:400,trigger:"hover"},{reference:p(()=>e[12]||(e[12]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7"},null,-1)])),default:p(()=>[e[13]||(e[13]=t("span",null," HGS Specific Gravity refers to the specific gravity (density) of the high gel strength component of the drilling mud. It assesses the density of the solid materials responsible for high gel strength. ",-1))]),_:1})]),i(r,{prop:"highGelStrength"},{default:p(()=>[i(o,{type:"number",controls:!1,step:"any",modelValue:l.targetData.highGelStrength,"onUpdate:modelValue":e[2]||(e[2]=u=>l.targetData.highGelStrength=u),placeholder:"",name:"highGelStrength"},null,8,["modelValue"])]),_:1})]),t("div",vf,[t("label",yf,[e[17]||(e[17]=t("span",null,"LGS (Linear Gel Strength) Specific Gravity",-1)),i(a,{placement:"top",width:400,trigger:"hover"},{reference:p(()=>e[15]||(e[15]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7"},null,-1)])),default:p(()=>[e[16]||(e[16]=t("span",null," LGS Specific Gravity refers to the specific gravity (density) of the linear gel strength component of the drilling mud. It assesses the density of the solid materials responsible for linear gel strength. ",-1))]),_:1})]),i(r,{prop:"linearGelStrength"},{default:p(()=>[i(o,{type:"number",controls:!1,step:"any",modelValue:l.targetData.linearGelStrength,"onUpdate:modelValue":e[3]||(e[3]=u=>l.targetData.linearGelStrength=u),placeholder:"",name:"linearGelStrength"},null,8,["modelValue"])]),_:1})])])]),t("div",wf,[t("button",{type:"button",class:"bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover rounded-md px-4 py-2 font-semibold",onClick:e[4]||(e[4]=(...u)=>l.cancel&&l.cancel(...u)),disabled:l.isFormDirty()!==!0||l.submitting}," Cancel ",8,Sf),t("button",{class:"bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover rounded-md px-4 py-2 font-semibold",type:"submit",disabled:l.isFormDirty()!==!0||l.submitting},[l.submitting?E("",!0):(w(),$("span",xf," Save ")),l.submitting?(w(),$("span",kf,e[18]||(e[18]=[F(" Please wait... "),t("span",{class:"spinner-border spinner-border-sm align-middle ms-2"},null,-1)]))):E("",!0)],8,$f)])]))]),_:1},8,["onSubmit","model","rules"]),i(s,{showAddNew:!1})],64)}const Bt=Q(nf,[["render",Df]]),Vf={[Ve.Samples]:qt,[Ve.Solids]:Bt},If=J({name:"daily-report-mud-properties",components:{SvgIcon:K,Sample:qt,Solid:Bt},props:{setChildActiveTab:{type:Function,required:!1,default:()=>{}}},setup(l){const e=v(!1),S=v(Ve.Samples),h=v(null),m=ot(()=>Vf[S.value]),n=r=>{const d=r.target;S.value=Number(d.getAttribute("data-tab-index")),l!=null&&l.setChildActiveTab&&l.setChildActiveTab(S.value)},a=async r=>{S.value!==Ve.Solids?n(r):o()?se.incompleteFormAlert({onConfirmed:()=>{n(r)}},"You have unsaved changes. Are you sure you want to leave?"):n(r)},o=()=>{var r,d;return h!=null&&h.value&&((r=h==null?void 0:h.value)!=null&&r.isFormDirty)?(d=h==null?void 0:h.value)==null?void 0:d.isFormDirty():!1};return{mudPropertiesTabs:fl,EMudPropertiesTab:Ve,currentComponent:m,currentChildTab:h,tabIndex:S,loading:e,handleActiveTab:a,isFormOfChildTabDirty:o}}}),Pf={class:"h-auto w-11/12 mx-auto flex flex-wrap items-center justify-start gap-2",role:"tablist"},Cf=["data-tab-index"],Tf={class:"h-auto w-11/12 flex flex-col gap-6 mx-auto px-3 py-4"};function Mf(l,e,S,h,m,n){return w(),$(O,null,[t("ul",Pf,[(w(!0),$(O,null,Z(l.mudPropertiesTabs,a=>(w(),$("li",{class:"nav-item",key:a.value},[t("div",{class:be(["whitespace-nowrap cursor-pointer font-semibold px-4 py-3 relative md:ml-0.5 md:-mb-0.5",{active:l.tabIndex===(a==null?void 0:a.value),"text-active-dark z-10 md:shadow-lg md:bg-card-background md:rounded-t-xl md:text-active":l.tabIndex===(a==null?void 0:a.value),"text-inactive rounded-t-lg":l.tabIndex!==(a==null?void 0:a.value)}]),onClick:e[0]||(e[0]=o=>l.handleActiveTab(o)),"data-tab-index":a.value,role:"tab"},D(a.label),11,Cf)]))),128))]),t("div",{class:be(["bg-card-background text-card-text-light w-11/12 mx-auto mb-4 h-auto rounded-xl border-2 border-active-border relative md:-mt-0.5",{"md:rounded-tl-none":l.tabIndex===l.EMudPropertiesTab.Samples}])},[t("div",Tf,[(w(),te(it(l.currentComponent),{ref:"currentChildTab"},null,512))])],2)],64)}const jt=Q(If,[["render",Mf]]),Et=ne("note",()=>({getNotes:async({params:n,callback:a})=>{var d;const o=g.get(a,"onSuccess",g.noop),r=g.get(a,"onFinish",g.noop);try{V.setHeader();const s=await V.getWithParams("notes",n);o(((d=s.data)==null?void 0:d.data)||s.data)}catch(s){H(s,a)}finally{r()}},getNoteDetails:async({id:n,callback:a})=>{var d;const o=g.get(a,"onSuccess",g.noop),r=g.get(a,"onFinish",g.noop);try{V.setHeader();const s=await V.get(`notes/${n}`);o(((d=s.data)==null?void 0:d.data)||s.data)}catch(s){H(s,a)}finally{r()}},updateNote:async({id:n,params:a,callback:o})=>{var s;const r=g.get(o,"onSuccess",g.noop),d=g.get(o,"onFinish",g.noop);try{V.setHeader();const u=await V.put(`notes/${n}`,a);r(((s=u.data)==null?void 0:s.data)||u.data)}catch(u){H(u,o)}finally{d()}},deleteNote:async({id:n,callback:a})=>{var d;const o=g.get(a,"onSuccess",g.noop),r=g.get(a,"onFinish",g.noop);try{V.setHeader();const s=await V.delete(`notes/${n}`);o(((d=s.data)==null?void 0:d.data)||s.data)}catch(s){H(s,a)}finally{r()}},createNote:async({params:n,callback:a})=>{var d;const o=g.get(a,"onSuccess",g.noop),r=g.get(a,"onFinish",g.noop);try{V.setHeader();const s=await V.post("notes",n);o(((d=s.data)==null?void 0:d.data)||s.data)}catch(s){H(s,a)}finally{r()}}})),Ff=J({name:"notes-modal",components:{SvgIcon:K},props:{isVisible:{type:Boolean,required:!0},close:{type:Function,required:!0},loadPage:{type:Function,default:()=>{},required:!0}},setup(l){const e=re(),S=Et(),h=v(!1),m=v({title:"",notes:""}),n=v(null),a=v(null),o=v(!1),r=v(""),d=le("dailyReport");Y(r,I=>{I!==""&&s()}),Y(()=>l.isVisible,I=>{var C;I===!1&&(r.value="",x(),(C=n==null?void 0:n.value)==null||C.resetFields())});const s=async()=>{S.getNoteDetails({id:r.value,callback:{onSuccess:I=>{var C;m.value={...I,costSettingId:(C=I==null?void 0:I.costSetting)==null?void 0:C.id}}}})},u=async I=>{o.value=!0,S.updateNote({id:r.value,params:I,callback:{onSuccess:C=>{l!=null&&l.loadPage&&(l==null||l.loadPage()),c()},onFinish:C=>{o.value=!1}}})},f=async I=>{o.value=!0,S.createNote({params:I,callback:{onSuccess:C=>{l!=null&&l.loadPage&&(l==null||l.loadPage()),c()},onFinish:C=>{o.value=!1}}})},c=()=>{l.close()},y=I=>{r.value=I.toString()},M=v({title:[{required:!0,message:"Please select Title",trigger:"change"}],notes:[{required:!0,message:"Please type Notes",trigger:"blur"}]}),P=()=>{n.value&&n.value.validate(I=>{var C;I&&(r!=null&&r.value?u({...m==null?void 0:m.value,dailyReportId:d==null?void 0:d.getDailyReportId()}):d==null||d.createDailyReport({wellId:(C=e==null?void 0:e.params)==null?void 0:C.id,callback:{onSuccess:B=>{f({...m==null?void 0:m.value,dailyReportId:d==null?void 0:d.getDailyReportId()})}}}))})},x=()=>{m.value={title:"",notes:""}};return{id:r,modal:h,rules:M,loading:o,targetData:m,formRef:n,inputFile:a,closeModal:c,submit:P,setId:y,reset:x,deleteFile:I=>{var B,A,_;const C=(A=(B=m==null?void 0:m.value)==null?void 0:B.files)==null?void 0:A.indexOf(I);C!==-1&&((_=m==null?void 0:m.value.files)==null||_.splice(C,1))},addFile:()=>{var I;(I=a==null?void 0:a.value)==null||I.click()},handleFile:I=>{var C,B;((B=(C=I.target)==null?void 0:C.files)==null?void 0:B.length)>0}}}}),Nf={key:0,class:"fixed top-0 right-0 bottom-0 left-0 z-40 flex items-center"},_f={class:"relative bg-card-background text-card-text-light max-h-11/12 w-11/12 flex flex-col items-center gap-3 mx-auto my-6 p-5 rounded-xl md:overflow-hidden md:w-3/5 lg:w-2/5 lg:h-auto"},Hf={class:"flex flex-row h-auto w-full items-center justify-between"},Af={class:"text-lg font-bold"},zf={class:"flex flex-col gap-1"},qf={class:"flex flex-col gap-1"},Bf={class:"h-auto w-full flex flex-row items-center gap-2"},jf=["disabled"],Ef=["disabled"],Rf={key:0,class:"indicator-label"},Of={key:1,class:"indicator-progress"};function Lf(l,e,S,h,m,n){const a=k("SvgIcon"),o=k("el-input"),r=k("el-form-item"),d=k("el-form");return l.isVisible?(w(),$("div",Nf,[e[7]||(e[7]=t("div",{class:"absolute inset-0 bg-sidebar-background opacity-25"},null,-1)),t("div",_f,[t("div",Hf,[t("h3",Af,D(`${l.id?"Edit Note":"New Note"}`),1),t("span",{class:"cursor-pointer ms-auto",onClick:e[0]||(e[0]=(...s)=>l.closeModal&&l.closeModal(...s))},[i(a,{icon:"closeModalIcon"})])]),i(d,{id:"cost_form",onSubmit:ie(l.submit,["prevent"]),model:l.targetData,rules:l.rules,ref:"formRef",class:"h-auto w-full flex flex-col gap-3"},{default:p(()=>[t("div",zf,[e[4]||(e[4]=t("label",{class:"font-bold"},[F("Title"),t("span",{class:"text-danger-active font-light"},"*")],-1)),i(r,{prop:"title",class:"mt-auto"},{default:p(()=>[i(o,{modelValue:l.targetData.title,"onUpdate:modelValue":e[1]||(e[1]=s=>l.targetData.title=s),placeholder:"",name:"title"},null,8,["modelValue"])]),_:1})]),t("div",qf,[e[5]||(e[5]=t("label",{class:"font-bold"},[F("Notes"),t("span",{class:"text-danger-active font-light"},"*")],-1)),i(r,{prop:"notes"},{default:p(()=>[i(o,{modelValue:l.targetData.notes,"onUpdate:modelValue":e[2]||(e[2]=s=>l.targetData.notes=s),placeholder:"",name:"notes",type:"textarea",rows:5},null,8,["modelValue"])]),_:1})]),t("div",Bf,[t("button",{type:"button",class:"bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover rounded-md px-4 py-2 font-semibold",onClick:e[3]||(e[3]=(...s)=>l.closeModal&&l.closeModal(...s)),disabled:l.loading}," Discard ",8,jf),t("button",{class:"bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover rounded-md px-4 py-2 font-semibold",type:"submit",disabled:l.loading},[l.loading?E("",!0):(w(),$("span",Rf," Save ")),l.loading?(w(),$("span",Of,e[6]||(e[6]=[F(" Please wait... "),t("span",{class:"spinner-border spinner-border-sm align-middle ms-2"},null,-1)]))):E("",!0)],8,Ef)])]),_:1},8,["onSubmit","model","rules"])])])):E("",!0)}const Wf=Q(Ff,[["render",Lf]]),Uf=J({name:"notes",components:{SvgIcon:K,BottomTool:ce,NoteModal:Wf,NoEntries:me},props:{setChildActiveTab:{type:Function,required:!1,default:()=>{}}},setup(l){const e=Et(),S=v(null),h=v(!1),m=v([]),n=le("dailyReport"),a=v(!1);ae(()=>{n!=null&&n.getDailyReportId()&&o()});const o=async(u={dailyReportId:n==null?void 0:n.getDailyReportId(),page:1,limit:200})=>{h.value=!0,e.getNotes({params:u,callback:{onSuccess:f=>{m.value=JSON.parse(JSON.stringify(f==null?void 0:f.items))},onFinish:f=>{h.value=!1}}})},r=u=>{var f;u&&((f=S==null?void 0:S.value)==null||f.setId(u)),a.value=!a.value},d=u=>{se.deletionAlert({onConfirmed:()=>{s(u)}})},s=u=>{h.value=!0,e.deleteNote({id:u,callback:{onSuccess:f=>{o()},onFinish:f=>{h.value=!1}}})};return{loading:h,noteList:m,noteModal:S,isModalVisible:a,numberWithCommas:fe,toggleNoteModal:r,deleteNote:d,formatDate:ke,getNotes:o}}}),Gf={class:"bg-card-background text-card-text-light w-11/12 mx-auto mb-4 h-auto rounded-xl"},Yf={class:"h-auto w-11/12 flex flex-col gap-6 mx-auto mt-7 px-3 py-4"},Jf={class:"card-body"},Qf={key:0,class:"text-center"},Kf={key:1,class:"h-auto w-full"},Xf={key:1,class:"grid grid-cols-1 gap-4 md:grid-cols-2 md:gap-7 lg:grid-cols-3"},Zf={class:"h-auto w-full flex flex-col gap-3"},em={class:"h-auto w-full flex justify-between"},tm={class:"overflow-hidden"},lm={class:"line-clamp-4"},om={class:"flex flex-row items-center gap-2 absolute top-0 end-0 -translate-y-1/2"},sm=["onClick"],nm=["onClick"],am={class:"text-danger"};function im(l,e,S,h,m,n){const a=k("NoEntries"),o=k("SvgIcon"),r=k("BottomTool"),d=k("NoteModal");return w(),$(O,null,[t("div",Gf,[t("div",Yf,[e[1]||(e[1]=t("h1",{class:"font-bold"},"Notes",-1)),t("div",Jf,[l.loading?(w(),$("div",Qf,e[0]||(e[0]=[t("div",{class:"spinner-border text-primary",role:"status"},[t("span",{class:"sr-only"},"Loading...")],-1)]))):(w(),$("div",Kf,[l.noteList.length===0?(w(),te(a,{key:0,addNew:()=>l.toggleNoteModal()},null,8,["addNew"])):(w(),$("div",Xf,[(w(!0),$(O,null,Z(l.noteList,s=>(w(),$("div",{key:s==null?void 0:s.id,class:"relative h-auto w-full mx-auto bg-minicard-background text-minicard-text-light flex flex-col items-center p-4 pt-7 rounded-xl border-t-2 border-active my-2 first:mt-4 last:mb-0 font-semibold md:first:mt-0 md:last:mb-0 md:m-0"},[t("div",Zf,[t("div",em,[t("h5",null,D(s==null?void 0:s.title),1),F(" "+D(l.formatDate(s==null?void 0:s.createdAt,"DD MMM YYYY")),1)]),t("div",tm,[t("p",lm,D(s==null?void 0:s.notes),1)])]),t("div",om,[t("button",{class:"p-2 rounded-full bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover",onClick:u=>l.toggleNoteModal(s==null?void 0:s.id)},[i(o,{icon:"pencilIcon"})],8,sm),t("button",{class:"p-2 rounded-full bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover",onClick:u=>l.deleteNote(s==null?void 0:s.id)},[t("span",am,[i(o,{icon:"trashIcon"})])],8,nm)])]))),128))]))]))])])]),i(r,{addNew:l.toggleNoteModal,showHelpInfo:!1},null,8,["addNew"]),i(d,{isVisible:l.isModalVisible,close:l.toggleNoteModal,ref:"noteModal",loadPage:l.getNotes},null,8,["isVisible","close","loadPage"])],64)}const Rt=Q(Uf,[["render",im]]);var ue=(l=>(l[l.initial=1]="initial",l[l.received=2]="received",l[l.used=3]="used",l[l.adjusted=4]="adjusted",l[l.returned=5]="returned",l))(ue||{});const Ot=[{value:1,label:"Initial"},{value:2,label:"Received"},{value:3,label:"Used"},{value:4,label:"Adjusted"},{value:5,label:"Returned"}],Lt=ne("productAndPackageInventory",()=>({deleteProductPackageInventory:async({id:S,callback:h})=>{var a;const m=g.get(h,"onSuccess",g.noop),n=g.get(h,"onFinish",g.noop);try{V.setHeader();const o=await V.delete(`productAndPackageInventories/${S}`);m(((a=o.data)==null?void 0:a.data)||o.data)}catch(o){H(o,h)}finally{n()}},getProductPackageInventories:async({dailyReportId:S,callback:h})=>{var a;const m=g.get(h,"onSuccess",g.noop),n=g.get(h,"onFinish",g.noop);try{V.setHeader();const o=await V.get(`productAndPackageInventories/${S}`);m(((a=o.data)==null?void 0:a.data)||o.data)}catch(o){H(o,h)}finally{n()}}})),Wt=ne("productPackageItem",()=>({getProductPackageItems:async({params:n,callback:a})=>{var d;const o=g.get(a,"onSuccess",g.noop),r=g.get(a,"onFinish",g.noop);try{V.setHeader();const s=await V.getWithParams("productAndPackageInventoryItems",n);o(((d=s.data)==null?void 0:d.data)||s.data)}catch(s){H(s,a)}finally{r()}},getProductPackageItemDetails:async({id:n,callback:a})=>{var d;const o=g.get(a,"onSuccess",g.noop),r=g.get(a,"onFinish",g.noop);try{V.setHeader();const s=await V.get(`productAndPackageInventoryItems/${n}`);o(((d=s.data)==null?void 0:d.data)||s.data)}catch(s){H(s,a)}finally{r()}},createProductPackageItem:async({params:n,callback:a})=>{var d;const o=g.get(a,"onSuccess",g.noop),r=g.get(a,"onFinish",g.noop);try{V.setHeader();const s=await V.post("productAndPackageInventoryItems",n);o(((d=s.data)==null?void 0:d.data)||s.data)}catch(s){H(s,a)}finally{r()}},updateProductPackageItem:async({id:n,params:a,callback:o})=>{var s;const r=g.get(o,"onSuccess",g.noop),d=g.get(o,"onFinish",g.noop);try{V.setHeader();const u=await V.put(`productAndPackageInventoryItems/${n}`,a);r(((s=u.data)==null?void 0:s.data)||u.data)}catch(u){H(u,o)}finally{d()}},deleteProductPackageItem:async({id:n,callback:a})=>{var d;const o=g.get(a,"onSuccess",g.noop),r=g.get(a,"onFinish",g.noop);try{V.setHeader();const s=await V.delete(`productAndPackageInventoryItems/${n}`);o(((d=s.data)==null?void 0:d.data)||s.data)}catch(s){H(s,a)}finally{r()}}})),rt=ne("productPackageReport",()=>({getProductPackageReports:async({params:m,callback:n})=>{var r;const a=g.get(n,"onSuccess",g.noop),o=g.get(n,"onFinish",g.noop);try{V.setHeader();const d=await V.getWithParams("productAndPackageInventoryReports",m);a(((r=d.data)==null?void 0:r.data)||d.data)}catch(d){H(d,n)}finally{o()}},getProductPackageReportDetails:async({id:m,callback:n})=>{var r;const a=g.get(n,"onSuccess",g.noop),o=g.get(n,"onFinish",g.noop);try{V.setHeader();const d=await V.get(`productAndPackageInventoryReports/${m}`);a(((r=d.data)==null?void 0:r.data)||d.data)}catch(d){H(d,n)}finally{o()}},createProductPackageReport:async({params:m,callback:n})=>{var r;const a=g.get(n,"onSuccess",g.noop),o=g.get(n,"onFinish",g.noop);try{V.setHeader();const d=await V.post("productAndPackageInventoryReports",m);a(((r=d.data)==null?void 0:r.data)||d.data)}catch(d){H(d,n)}finally{o()}},deleteProductPackageReport:async({id:m,callback:n})=>{var r;const a=g.get(n,"onSuccess",g.noop),o=g.get(n,"onFinish",g.noop);try{V.setHeader();const d=await V.delete(`productAndPackageInventoryReports/${m}`);a(((r=d.data)==null?void 0:r.data)||d.data)}catch(d){H(d,n)}finally{o()}}})),st=ne("volumeTracking",()=>({getVolumeTrackings:async({params:n,callback:a})=>{var d;const o=g.get(a,"onSuccess",g.noop),r=g.get(a,"onFinish",g.noop);try{V.setHeader();const s=await V.getWithParams("volumeTrackings",n);o(((d=s.data)==null?void 0:d.data)||s.data)}catch(s){H(s,a)}finally{r()}},getVolumeTrackingDetails:async({id:n,callback:a})=>{var d;const o=g.get(a,"onSuccess",g.noop),r=g.get(a,"onFinish",g.noop);try{V.setHeader();const s=await V.get(`volumeTrackings/${n}`);o(((d=s.data)==null?void 0:d.data)||s.data)}catch(s){H(s,a)}finally{r()}},updateVolumeTracking:async({id:n,params:a,callback:o})=>{var s;const r=g.get(o,"onSuccess",g.noop),d=g.get(o,"onFinish",g.noop);try{V.setHeader();const u=await V.put(`volumeTrackings/${n}`,a);r(((s=u.data)==null?void 0:s.data)||u.data)}catch(u){H(u,o)}finally{d()}},deleteVolumeTracking:async({id:n,callback:a})=>{var d;const o=g.get(a,"onSuccess",g.noop),r=g.get(a,"onFinish",g.noop);try{V.setHeader();const s=await V.delete(`volumeTrackings/${n}`);o(((d=s.data)==null?void 0:d.data)||s.data)}catch(s){H(s,a)}finally{r()}},createVolumeTracking:async({params:n,callback:a})=>{var d;const o=g.get(a,"onSuccess",g.noop),r=g.get(a,"onFinish",g.noop);try{V.setHeader();const s=await V.post("volumeTrackings",n);o(((d=s.data)==null?void 0:d.data)||s.data)}catch(s){H(s,a)}finally{r()}}}));var Se=(l=>(l[l.Addition=1]="Addition",l[l.Loss=2]="Loss",l[l.Transfer=3]="Transfer",l))(Se||{});const rm=[{value:1,label:"Addition"},{value:2,label:"Loss"},{value:3,label:"Transfer"}],dm=[{value:1,label:"Active"},{value:2,label:"Inactive"}],um=J({name:"storage-or-pit-modal",components:{SvgIcon:K},props:{isVisible:{type:Boolean,required:!0},close:{type:Function,required:!0},loadPage:{type:Function,default:()=>{},required:!0}},setup(l){const e=re(),S=st(),h=v(!1),m={name:"",storageType:"",status:null,measuredVolume:null,mudWeight:null,mudType:null},n=v(JSON.parse(JSON.stringify(m))),a=v(null),o=v(!1),r=v(null),d=v(""),s=le("dailyReport");Y(d,b=>{b!==""&&u()}),Y(()=>l.isVisible,b=>{var T;b===!1&&(d.value="",x(),(T=a==null?void 0:a.value)==null||T.resetFields())});const u=async()=>{S.getVolumeTrackingDetails({id:d.value,callback:{onSuccess:b=>{n.value={...b}}}})},f=async b=>{o.value=!0,S.updateVolumeTracking({id:d.value,params:b,callback:{onSuccess:T=>{l!=null&&l.loadPage&&(l==null||l.loadPage()),y()},onFinish:T=>{o.value=!1}}})},c=async b=>{o.value=!0,S.createVolumeTracking({params:b,callback:{onSuccess:T=>{l!=null&&l.loadPage&&(l==null||l.loadPage()),y()},onFinish:T=>{o.value=!1}}})},y=()=>{l.close()},M=b=>{d.value=b},P=()=>{a.value&&a.value.validate(b=>{var T,q,I,C,B;if(b){const A={...n==null?void 0:n.value,status:Number((T=n==null?void 0:n.value)==null?void 0:T.status),measuredVolume:Number((q=n==null?void 0:n.value)==null?void 0:q.measuredVolume),mudWeight:Number((I=n==null?void 0:n.value)==null?void 0:I.mudWeight),mudType:Number((C=n==null?void 0:n.value)==null?void 0:C.mudType)};d!=null&&d.value?f({...A,dailyReportId:s==null?void 0:s.getDailyReportId()}):s==null||s.createDailyReport({wellId:(B=e==null?void 0:e.params)==null?void 0:B.id,callback:{onSuccess:_=>{c({...A,dailyReportId:s==null?void 0:s.getDailyReportId()})}}})}})},x=()=>{n.value=JSON.parse(JSON.stringify(m))};return{id:d,type:r,modal:h,loading:o,targetData:n,volumeTrackingStatusOptions:dm,formRef:a,closeModal:y,submit:P,reset:x,setId:M}}}),pm={key:0,class:"fixed top-0 right-0 bottom-0 left-0 z-40 flex items-center"},cm={class:"relative bg-card-background text-card-text-light max-h-11/12 w-11/12 flex flex-col items-center gap-3 mx-auto my-6 p-5 rounded-xl overflow-y-scroll md:overflow-hidden lg:w-2/5 lg:h-auto"},fm={class:"flex flex-row h-auto w-full items-center justify-between"},mm={class:"text-lg font-bold"},gm={class:"flex flex-col gap-3 md:flex-row"},bm={class:"h-auto w-full flex flex-col gap-3"},hm={class:"flex flex-col gap-1"},vm={class:"flex flex-col gap-1"},ym={class:"flex flex-col gap-1"},wm={class:"h-auto w-full flex flex-col gap-3"},Sm={class:"flex flex-col gap-1"},$m={class:"flex flex-col gap-1"},xm={class:"flex flex-col gap-1"},km={class:"h-auto w-full flex flex-row items-center gap-2"},Dm=["disabled"],Vm=["disabled"],Im={key:0,class:"indicator-label"},Pm={key:1,class:"indicator-progress"};function Cm(l,e,S,h,m,n){const a=k("SvgIcon"),o=k("el-input"),r=k("el-form-item"),d=k("el-option"),s=k("el-select"),u=k("el-form");return l.isVisible?(w(),$("div",pm,[e[15]||(e[15]=t("div",{class:"absolute inset-0 bg-sidebar-background opacity-25"},null,-1)),t("div",cm,[t("div",fm,[t("h3",mm,D(`${l.id?"Edit Storage or Pit":"Add Storage or Pit"}`),1),t("span",{class:"cursor-pointer ms-auto",onClick:e[0]||(e[0]=(...f)=>l.closeModal&&l.closeModal(...f))},[i(a,{icon:"closeModalIcon"})])]),i(u,{id:"storage_or_pit_form",onSubmit:ie(l.submit,["prevent"]),model:l.targetData,ref:"formRef",class:"h-auto w-full"},{default:p(()=>[t("div",gm,[t("div",bm,[t("div",hm,[e[8]||(e[8]=t("label",{class:"font-bold"},"Name ",-1)),i(r,{prop:"name",rules:{required:!0,message:"Please type Name",trigger:"blur"}},{default:p(()=>[i(o,{modelValue:l.targetData.name,"onUpdate:modelValue":e[1]||(e[1]=f=>l.targetData.name=f),placeholder:"",name:"name"},null,8,["modelValue"])]),_:1})]),t("div",vm,[e[9]||(e[9]=t("label",{class:"font-bold"},"Storage Type ",-1)),i(r,{prop:"storageType"},{default:p(()=>[i(o,{modelValue:l.targetData.storageType,"onUpdate:modelValue":e[2]||(e[2]=f=>l.targetData.storageType=f),placeholder:"",name:"storageType"},null,8,["modelValue"])]),_:1})]),t("div",ym,[e[10]||(e[10]=t("label",{class:"font-bold"},"Status ",-1)),i(r,{prop:"status"},{default:p(()=>[i(s,{modelValue:l.targetData.status,"onUpdate:modelValue":e[3]||(e[3]=f=>l.targetData.status=f),placeholder:"Select Status",clearable:""},{default:p(()=>[(w(!0),$(O,null,Z(l.volumeTrackingStatusOptions,f=>(w(),te(d,{key:f==null?void 0:f.value,label:f==null?void 0:f.label,value:f==null?void 0:f.value,name:"status"},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})])]),t("div",wm,[t("div",Sm,[e[11]||(e[11]=t("label",{class:"font-bold"},"Measured Volume (bbl) ",-1)),i(r,{prop:"measuredVolume"},{default:p(()=>[i(o,{type:"number",controls:!1,step:"any",modelValue:l.targetData.measuredVolume,"onUpdate:modelValue":e[4]||(e[4]=f=>l.targetData.measuredVolume=f),placeholder:"",name:"measuredVolume"},null,8,["modelValue"])]),_:1})]),t("div",$m,[e[12]||(e[12]=t("label",{class:"font-bold"},"Mud Weight (ppg) ",-1)),i(r,{prop:"mudWeight"},{default:p(()=>[i(o,{type:"number",controls:!1,step:"any",modelValue:l.targetData.mudWeight,"onUpdate:modelValue":e[5]||(e[5]=f=>l.targetData.mudWeight=f),placeholder:"",name:"mudWeight"},null,8,["modelValue"])]),_:1})]),t("div",xm,[e[13]||(e[13]=t("label",{class:"font-bold"},"Mud Type ",-1)),i(r,{prop:"mudType"},{default:p(()=>[i(o,{type:"number",controls:!1,step:"any",modelValue:l.targetData.mudType,"onUpdate:modelValue":e[6]||(e[6]=f=>l.targetData.mudType=f),placeholder:"",name:"mudType"},null,8,["modelValue"])]),_:1})])])]),t("div",km,[t("button",{type:"button",class:"bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover rounded-md px-4 py-2 font-semibold",onClick:e[7]||(e[7]=(...f)=>l.closeModal&&l.closeModal(...f)),disabled:l.loading}," Discard ",8,Dm),t("button",{class:"bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover rounded-md px-4 py-2 font-semibold",type:"submit",disabled:l.loading},[l.loading?E("",!0):(w(),$("span",Im," Save ")),l.loading?(w(),$("span",Pm,e[14]||(e[14]=[F(" Please wait... "),t("span",{class:"spinner-border spinner-border-sm align-middle ms-2"},null,-1)]))):E("",!0)],8,Vm)])]),_:1},8,["onSubmit","model"])])])):E("",!0)}const Ut=Q(um,[["render",Cm]]),Tm=J({name:"form-modal",components:{SvgIcon:K,StorageOrPitModal:Ut},props:{isVisible:{type:Boolean,required:!0},close:{type:Function,required:!0},loadTable:{type:Function,default:()=>{},required:!1},productAndPackageInventoryId:{type:String,required:!0}},setup(l){const e=re(),S=$t(),h=vl(),m=rt(),n=Wt(),a=st(),o=v({productAndPackageInventoryReportId:"",productId:"",locationId:"",type:null,quantity:null,cost:null,bolNo:"",notes:""}),r=v([]),d=v([]),s=v(null),u=v(!1),f=v(""),c=v(null),y=v(null),M=v(""),P=le("dailyReport"),x=v(!1);ae(()=>{b()}),Y(f,W=>{W!==""&&q()}),Y(()=>o.value.productId,W=>{var G;o.value={...o.value,cost:(G=X(W))==null?void 0:G.price}}),Y(()=>l.isVisible,W=>{var G;W==!0?(P!=null&&P.getDailyReportId()&&I(),!f.value&&M.value&&C()):(oe(),(G=s==null?void 0:s.value)==null||G.resetFields())});const b=async()=>{S.getWellDetails({wellId:e.params.id,callback:{onSuccess:W=>{var G;M.value=(G=W==null?void 0:W.company)==null?void 0:G.id,C()},onFinish:W=>{u.value=!1}}})},T=W=>{var G;W&&((G=y==null?void 0:y.value)==null||G.setId(W)),x.value=!x.value},q=async()=>{n.getProductPackageItemDetails({id:f.value,callback:{onSuccess:W=>{var G,ee,de,he,ve;o.value={...o.value,...W,locationId:(G=W==null?void 0:W.location)==null?void 0:G.id},r.value=[{value:(de=(ee=W==null?void 0:W.productAndPackageInventoryReport)==null?void 0:ee.product)==null?void 0:de.id,label:(ve=(he=W==null?void 0:W.productAndPackageInventoryReport)==null?void 0:he.product)==null?void 0:ve.name,price:W==null?void 0:W.cost}]}}})},I=async(W={dailyReportId:P==null?void 0:P.getDailyReportId(),page:1,limit:200})=>{a.getVolumeTrackings({params:W,callback:{onSuccess:G=>{var ee;d.value=(ee=G==null?void 0:G.items)==null?void 0:ee.map(de=>({value:de==null?void 0:de.id,key:de==null?void 0:de.name}))},onFinish:G=>{}}})},C=async()=>{h.getProducts({params:{page:1,limit:200,companyId:M.value},callback:{onSuccess:W=>{var G;r.value=(G=W==null?void 0:W.items)==null?void 0:G.map(ee=>({value:ee==null?void 0:ee.id,key:ee==null?void 0:ee.name,price:ee==null?void 0:ee.price}))}}})},B=async W=>{h.getProductDetails({id:W,callback:{onSuccess:G=>{o.value={...o.value,cost:G==null?void 0:G.price}}}})},A=async W=>{u.value=!0,n.updateProductPackageItem({id:f.value,params:W,callback:{onSuccess:G=>{l!=null&&l.loadTable&&(l==null||l.loadTable()),j()},onFinish:G=>{u.value=!1}}})},_=async W=>{u.value=!0,n.createProductPackageItem({params:W,callback:{onSuccess:G=>{l!=null&&l.loadTable&&(l==null||l.loadTable()),j()},onFinish:G=>{u.value=!1}}})},N=async W=>{u.value=!0,m.createProductPackageReport({params:W,callback:{onSuccess:G=>{l!=null&&l.loadTable&&(l==null||l.loadTable()),j()},onFinish:G=>{u.value=!1}}})},j=()=>{l.close()},z=v({productId:[{required:!0,message:"Please choose Product",trigger:"change"}],locationId:[{required:!0,message:"Please choose location",trigger:"change"}],quantity:[{required:!0,message:"Please type quantity",trigger:"blur"}]}),R=(W,G,ee)=>{f.value=G,o.value={...o.value,productId:ee,productAndPackageInventoryReportId:W},f.value||B(ee)},L=W=>{c.value=W},U=()=>{s.value&&s.value.validate(W=>{var G,ee,de,he,ve,$e,xe;if(W){let we={productAndPackageInventoryId:l==null?void 0:l.productAndPackageInventoryId,productAndPackageInventoryReportId:(G=o.value)==null?void 0:G.productAndPackageInventoryReportId,locationId:(ee=o==null?void 0:o.value)==null?void 0:ee.locationId,type:c.value,quantity:Number((de=o==null?void 0:o.value)==null?void 0:de.quantity),notes:((he=o==null?void 0:o.value)==null?void 0:he.notes)||null,productId:(ve=o==null?void 0:o.value)==null?void 0:ve.productId,cost:Number(($e=o==null?void 0:o.value)==null?void 0:$e.cost),bolNo:((xe=o==null?void 0:o.value)==null?void 0:xe.bolNo)||null};f!=null&&f.value?A(we):c.value!=ue.initial?_(we):N(we)}})},oe=()=>{f.value="",c.value=null,r.value=[],d.value=[],o.value={productAndPackageInventoryReportId:"",productId:"",locationId:"",type:null,quantity:null,cost:null,bolNo:"",notes:""}},X=W=>{var G;return((G=r==null?void 0:r.value)==null?void 0:G.find(ee=>W==ee.value))||null};return{id:f,type:c,rules:z,loading:u,targetData:o,formRef:s,productOptions:r,locationOptions:d,productAndPackageInventoryOptions:Ot,isSoPModalVisible:x,ProductPackageEnum:ue,storageOrPitModal:y,closeModal:j,submit:U,reset:oe,setId:R,setType:L,getOption:et,getVolumeTrackings:I,toggleStorageOrPitModal:T}}}),Mm={key:0,class:"fixed top-0 right-0 bottom-0 left-0 z-40 flex items-center"},Fm={class:"relative bg-card-background text-card-text-light max-h-11/12 w-11/12 flex flex-col items-center gap-3 mx-auto my-6 p-5 rounded-xl overflow-y-scroll md:overflow-hidden lg:w-2/5 lg:h-auto"},Nm={class:"flex flex-row h-auto w-full items-center justify-between"},_m={class:"text-lg font-bold"},Hm={class:"flex flex-col gap-3 md:flex-row"},Am={class:"h-auto w-full flex flex-col gap-3"},zm={class:"flex flex-col gap-1"},qm={class:"flex flex-col gap-2"},Bm={class:"flex justify-between items-center"},jm={class:"flex flex-col gap-1"},Em={class:"h-auto w-full flex flex-col gap-3"},Rm={class:"flex flex-col gap-1"},Om={class:"flex flex-col gap-1"},Lm={class:"flex flex-col gap-1"},Wm={class:"h-auto w-full flex flex-row items-center gap-2"},Um=["disabled"],Gm=["disabled"],Ym={key:0,class:"indicator-label"},Jm={key:1,class:"indicator-progress"};function Qm(l,e,S,h,m,n){var c;const a=k("SvgIcon"),o=k("el-option"),r=k("el-select"),d=k("el-form-item"),s=k("el-input"),u=k("el-form"),f=k("StorageOrPitModal");return w(),$(O,null,[l.isVisible?(w(),$("div",Mm,[e[16]||(e[16]=t("div",{class:"absolute inset-0 bg-sidebar-background opacity-25"},null,-1)),t("div",Fm,[t("div",Nm,[t("h3",_m,D((c=l.getOption(l.type,l.productAndPackageInventoryOptions))==null?void 0:c.label),1),t("span",{class:"cursor-pointer",onClick:e[0]||(e[0]=(...y)=>l.closeModal&&l.closeModal(...y))},[i(a,{icon:"closeModalIcon"})])]),i(u,{id:"product_package_form",onSubmit:ie(l.submit,["prevent"]),model:l.targetData,rules:l.rules,ref:"formRef",class:"h-auto w-full"},{default:p(()=>[t("div",Hm,[t("div",Am,[t("div",zm,[e[9]||(e[9]=t("label",{class:"font-bold"},[F("Product "),t("span",{class:"text-danger-active font-light"},"*")],-1)),i(d,{prop:"productId"},{default:p(()=>[i(r,{modelValue:l.targetData.productId,"onUpdate:modelValue":e[1]||(e[1]=y=>l.targetData.productId=y),placeholder:"Select Product",clearable:"",disabled:l.type!==l.ProductPackageEnum.initial||l.type==l.ProductPackageEnum.initial&&l.id!=""},{default:p(()=>[(w(!0),$(O,null,Z(l.productOptions,y=>(w(),te(o,{key:y.value,label:y.key,value:y.value,name:"productId"},null,8,["label","value"]))),128))]),_:1},8,["modelValue","disabled"])]),_:1})]),t("div",qm,[t("div",Bm,[e[10]||(e[10]=t("label",{class:"font-bold"},"Location ",-1)),t("button",{type:"button",class:"bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover rounded-md px-4 py-2 font-semibold",onClick:e[2]||(e[2]=()=>l.toggleStorageOrPitModal())}," New Location ")]),i(d,{prop:"locationId"},{default:p(()=>[i(r,{modelValue:l.targetData.locationId,"onUpdate:modelValue":e[3]||(e[3]=y=>l.targetData.locationId=y),placeholder:"Select Location",clearable:""},{default:p(()=>[(w(!0),$(O,null,Z(l.locationOptions,y=>(w(),te(o,{key:y.value,label:y.key,value:y.value,name:"locationId"},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),t("div",jm,[e[11]||(e[11]=t("label",{class:"font-bold"},[F("Quantity "),t("span",{class:"text-danger-active font-light"},"*")],-1)),i(d,{prop:"quantity"},{default:p(()=>[i(s,{type:"number",controls:!1,step:"any",modelValue:l.targetData.quantity,"onUpdate:modelValue":e[4]||(e[4]=y=>l.targetData.quantity=y),placeholder:"",name:"quantity"},null,8,["modelValue"])]),_:1})])]),t("div",Em,[l.type!==l.ProductPackageEnum.used?(w(),$(O,{key:0},[t("div",Rm,[e[12]||(e[12]=t("label",{class:"font-bold"},"Cost ",-1)),i(d,{prop:"cost"},{default:p(()=>[i(s,{disabled:"",modelValue:l.targetData.cost,"onUpdate:modelValue":e[5]||(e[5]=y=>l.targetData.cost=y),placeholder:"",name:"cost"},null,8,["modelValue"])]),_:1})]),t("div",Om,[e[13]||(e[13]=t("div",{class:"md:h-11 md:flex md:items-center"},[t("label",{class:"font-bold"},"BOL No ")],-1)),i(d,{prop:"bolNo"},{default:p(()=>[i(s,{modelValue:l.targetData.bolNo,"onUpdate:modelValue":e[6]||(e[6]=y=>l.targetData.bolNo=y),placeholder:"",name:"bolNo"},null,8,["modelValue"])]),_:1})])],64)):E("",!0)])]),t("div",Lm,[e[14]||(e[14]=t("label",{class:"font-bold"},"Notes ",-1)),i(d,{prop:"notes"},{default:p(()=>[i(s,{modelValue:l.targetData.notes,"onUpdate:modelValue":e[7]||(e[7]=y=>l.targetData.notes=y),placeholder:"",name:"notes",type:"textarea",rows:2},null,8,["modelValue"])]),_:1})]),t("div",Wm,[t("button",{type:"button",class:"bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover rounded-md px-4 py-2 font-semibold",onClick:e[8]||(e[8]=(...y)=>l.closeModal&&l.closeModal(...y)),disabled:l.loading}," Discard ",8,Um),t("button",{class:"bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover rounded-md px-4 py-2 font-semibold",type:"submit",disabled:l.loading},[l.loading?E("",!0):(w(),$("span",Ym," Save ")),l.loading?(w(),$("span",Jm,e[15]||(e[15]=[F(" Please wait... "),t("span",{class:"spinner-border spinner-border-sm align-middle ms-2"},null,-1)]))):E("",!0)],8,Gm)])]),_:1},8,["onSubmit","model","rules"])])])):E("",!0),i(f,{isVisible:l.isSoPModalVisible,close:()=>l.toggleStorageOrPitModal(),ref:"storageOrPitModal",loadPage:l.getVolumeTrackings},null,8,["isVisible","close","loadPage"])],64)}const Gt=Q(Tm,[["render",Qm]]),Km=J({name:"product-package",components:{SvgIcon:K,FormModal:Gt,BottomTool:ce,TablePagination:wl,NoEntries:me},props:{setChildActiveTab:{type:Function,required:!1,default:()=>{}}},setup(l){const e=re(),S=rt(),h=Lt(),m=Wt(),n=v(!1),a=v(!1),o=v(),r=v([]),d=v(null),s=v(""),u=le("dailyReport"),f=v(!1),c=v(new Set);ae(()=>{u!=null&&u.getDailyReportId()?M():y()});const y=async()=>{var N;u==null||u.createDailyReport({wellId:(N=e==null?void 0:e.params)==null?void 0:N.id,callback:{onSuccess:j=>{M()}}})},M=async()=>{u!=null&&u.getDailyReportId()&&(n.value=!0,h.getProductPackageInventories({dailyReportId:u==null?void 0:u.getDailyReportId(),callback:{onSuccess:N=>{o.value=JSON.parse(JSON.stringify(N)),s.value=N==null?void 0:N.id,N!=null&&N.id&&P(N.id)},onFinish:N=>{n.value=!1}}}))},P=async N=>{a.value=!0,S.getProductPackageReports({params:{productAndPackageInventoryId:N,page:1,limit:200},callback:{onSuccess:j=>{var z,R,L;r.value=JSON.parse(JSON.stringify(j==null?void 0:j.items));for(let U=0;U<((z=r==null?void 0:r.value)==null?void 0:z.length);U++){const oe={...r==null?void 0:r.value[U],historyList:[],pageCount:0,totalElements:0,currentPage:1,loading:!1};r.value[U]=oe,(R=r.value[U])!=null&&R.id&&((L=r.value[U])!=null&&L.currentPage)&&x(U,r.value[U].id,r.value[U].currentPage)}},onFinish:j=>{a.value=!1}}})},x=async(N,j,z)=>{r.value[N].loading=!0,m.getProductPackageItems({params:{productAndPackageInventoryReportId:j,page:z,limit:10},callback:{onSuccess:R=>{r.value[N].historyList=JSON.parse(JSON.stringify(R==null?void 0:R.items)),r.value[N].pageCount=R==null?void 0:R.totalPage,r.value[N].totalElements=R==null?void 0:R.total,r.value[N].currentPage=R==null?void 0:R.page},onFinish:R=>{r.value[N].loading=!1}}})},b=(N,j,z)=>{r.value[N].currentPage=z,x(N,j,z)},T=N=>{se.deletionAlert({onConfirmed:()=>{q(N)}})},q=async N=>{S.deleteProductPackageReport({id:N,callback:{onSuccess:j=>{M()}}})},I=N=>{se.deletionAlert({onConfirmed:()=>{C(N)}})},C=async N=>{m.deleteProductPackageItem({id:N,callback:{onSuccess:j=>{M()}}})};return{packageInfo:o,packageList:r,formModal:d,loadingPackageInfo:n,loadingPackageList:a,productAndPackageInventoryOptions:Ot,productAndPackageInventoryId:s,ProductAndPackageInventoryType:ue,isModalVisible:f,openAccordions:c,formatDate:ke,getOption:et,getPackageInfo:M,pageChange:b,deleteProductPackage:T,deleteProductPackageItem:I,numberWithCommas:fe,toggleItem:(N,j,z,R)=>{var L,U,oe,X,W,G,ee;z==ue.received?(L=d==null?void 0:d.value)==null||L.setType(ue.received):z==ue.used?(U=d==null?void 0:d.value)==null||U.setType(ue.used):z==ue.adjusted?(oe=d==null?void 0:d.value)==null||oe.setType(ue.adjusted):z==ue.returned?(X=d==null?void 0:d.value)==null||X.setType(ue.returned):z==ue.initial&&((W=d==null?void 0:d.value)==null||W.setType(ue.initial)),(G=d==null?void 0:d.value)==null||G.setId(N,j,R),(ee=d==null?void 0:d.value)==null||ee.show()},toggleAddInitial:()=>{var N;(N=d==null?void 0:d.value)==null||N.setType(ue.initial),f.value=!f.value},toggleAccordion:N=>{c.value.has(N)?c.value.delete(N):c.value.add(N)}}}}),Xm={class:"bg-card-background text-card-text-light w-11/12 mx-auto mb-4 h-auto rounded-xl"},Zm={class:"card-body"},eg={key:0,class:"text-center"},tg={key:1,class:"h-auto w-11/12 mx-auto rounded-xl border-[1px] border-dashed p-4 font-semibold"},lg={class:"flex items-center justify-between"},og={class:"h-auto w-11/12 flex flex-col gap-3 mx-auto p-4"},sg={class:"accordion accordion-flush",id:"accordionPanelsStayOpenExample"},ng={key:0,class:"text-center"},ag={key:1},ig={key:1,class:"grid grid-cols-1 gap-4 md:grid-cols-2 md:gap-7 lg:grid-cols-3"},rg=["onClick"],dg={class:"h-auto w-full flex gap-2 items-center"},ug={class:"h-auto w-full flex flex-col gap-0.5"},pg={class:"text-success"},cg={class:"text-danger"},fg={class:"flex items-center gap-2"},mg=["onClick"],gg=["onClick"],bg=["onClick"],hg={class:"svg-icon svg-icon-3"},vg=["onClick"],yg=["onClick"],wg={class:"text-danger"},Sg={class:"h-auto w-full overflow-x-scroll"},$g={key:0,colspan:"6"},xg={class:"p-4"},kg={class:"p-4"},Dg={class:"text-break"},Vg={class:"p-4"},Ig={class:"p-4"},Pg={class:"p-4"},Cg={class:"h-auto w-full flex flex-row items-center gap-2 justify-between"},Tg=["onClick"],Mg=["onClick","disabled"],Fg={class:"text-danger"},Ng={class:"h-auto w-full flex flex-col items-center"},_g={key:0};function Hg(l,e,S,h,m,n){var f;const a=k("NoEntries"),o=k("SvgIcon"),r=k("el-tooltip"),d=k("TablePagination"),s=k("BottomTool"),u=k("FormModal");return w(),$(O,null,[t("div",Xm,[e[8]||(e[8]=t("div",{class:"h-auto w-11/12 mx-auto mt-7 px-3 py-4"},[t("h1",{class:"font-bold"},"Product & Package Inventory")],-1)),t("div",Zm,[l.loadingPackageInfo?(w(),$("div",eg,e[0]||(e[0]=[t("div",{class:"spinner-border text-primary",role:"status"},[t("span",{class:"sr-only"},"Loading...")],-1)]))):(w(),$("div",tg,[e[2]||(e[2]=t("h5",{class:"font-bold"},"Details",-1)),t("div",lg,[e[1]||(e[1]=t("span",null,"Total cost ",-1)),t("span",null,D(`$${l.numberWithCommas((f=l.packageInfo)==null?void 0:f.totalCost)}`),1)])])),t("div",og,[t("div",sg,[l.loadingPackageList?(w(),$("div",ng,e[3]||(e[3]=[t("div",{class:"spinner-border text-primary",role:"status"},[t("span",{class:"sr-only"},"Loading...")],-1)]))):(w(),$("div",ag,[l.packageList.length===0?(w(),te(a,{key:0,addNew:l.toggleAddInitial},null,8,["addNew"])):(w(),$("div",ig,[(w(!0),$(O,null,Z(l.packageList,(c,y)=>{var M,P,x;return w(),$("div",{key:c==null?void 0:c.id,class:be(["w-full my-2 first:mt-0 last:mb-0 bg-minicard-background text-minicard-text-light rounded-lg border-[1px] border-dashed transition-all duration-300 ease-in-out md:first:mt-0 md:last:mb-0 md:m-0",l.openAccordions.has(c==null?void 0:c.id)?"h-80 md:h-90":"h-40"])},[t("div",{class:"h-auto w-full p-4 pb-0 flex flex-col items-center rounded p4 gap-3 justify-between font-semibold",onClick:b=>l.toggleAccordion(c==null?void 0:c.id)},[t("div",dg,[e[4]||(e[4]=t("span",{class:"bg-success rounded-lg h-15 w-2"},null,-1)),t("div",ug,[t("h4",null,D((M=c==null?void 0:c.product)==null?void 0:M.name),1),t("span",pg,D(`Quantity: ${c==null?void 0:c.quantity}`),1),t("span",cg,D(`Total Cost: $${l.numberWithCommas(c==null?void 0:c.totalCost)}`),1)])]),t("div",fg,[i(r,{content:"Received",placement:"top",effect:"customize"},{default:p(()=>[t("button",{class:"p-2 rounded-full bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover",onClick:b=>{var T;return l.toggleItem(c==null?void 0:c.id,"",l.ProductAndPackageInventoryType.received,(T=c==null?void 0:c.product)==null?void 0:T.id)}},[i(o,{icon:"addIcon",color:"#43a047"})],8,mg)]),_:2},1024),i(r,{content:"Used",placement:"top",effect:"customize"},{default:p(()=>[t("button",{class:"p-2 rounded-full bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover",onClick:b=>{var T;return l.toggleItem(c==null?void 0:c.id,"",l.ProductAndPackageInventoryType.used,(T=c==null?void 0:c.product)==null?void 0:T.id)}},[i(o,{icon:"minusIcon"})],8,gg)]),_:2},1024),i(r,{content:"Returned",placement:"top",effect:"customize"},{default:p(()=>[t("button",{class:"p-2 rounded-full bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover",onClick:b=>{var T;return l.toggleItem(c==null?void 0:c.id,"",l.ProductAndPackageInventoryType.returned,(T=c==null?void 0:c.product)==null?void 0:T.id)}},[t("span",hg,[i(o,{icon:"refreshIcon"})])],8,bg)]),_:2},1024),i(r,{content:"Adjusted",placement:"top",effect:"customize"},{default:p(()=>[t("button",{class:"p-2 rounded-full bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover",onClick:b=>{var T;return l.toggleItem(c==null?void 0:c.id,"",l.ProductAndPackageInventoryType.adjusted,(T=c==null?void 0:c.product)==null?void 0:T.id)}},[i(o,{icon:"pencilIcon"})],8,vg)]),_:2},1024),i(r,{content:"Delete",placement:"top",effect:"customize"},{default:p(()=>[t("button",{class:"p-2 rounded-full bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover",onClick:b=>l.deleteProductPackage(c==null?void 0:c.id)},[t("span",wg,[i(o,{icon:"trashIcon"})])],8,yg)]),_:2},1024)])],8,rg),t("div",{class:be(["h-auto w-full p-4 pt-0 transition-all",l.openAccordions.has(c==null?void 0:c.id)?"opacity-100 pt-4 duration-300 ease-in":"opacity-0 duration-200 ease-in"])},[t("div",Sg,[e[7]||(e[7]=t("p",{class:"font-bold text-sm"},"History",-1)),t("table",null,[e[6]||(e[6]=t("thead",null,[t("tr",{class:"font-bold"},[t("th",null,"Date"),t("th",null,"Type"),t("th",null,"Location"),t("th",null,"Quantity"),t("th",null,"Cost"),t("th")])],-1)),t("tbody",null,[c!=null&&c.loading?(w(),$("td",$g,e[5]||(e[5]=[t("div",{class:"text-center"},[t("div",{class:"spinner-border text-primary",role:"status"},[t("span",{class:"sr-only"},"Loading...")])],-1)]))):(w(!0),$(O,{key:1},Z(c==null?void 0:c.historyList,b=>{var T,q;return w(),$("tr",{key:c.id,class:"font-bold"},[t("td",xg,D(l.formatDate(b==null?void 0:b.createdAt)),1),t("td",kg,D((T=l.getOption(b==null?void 0:b.type,l.productAndPackageInventoryOptions))==null?void 0:T.label),1),t("td",Dg,D((q=b==null?void 0:b.location)==null?void 0:q.name),1),t("td",Vg,D(l.numberWithCommas(b==null?void 0:b.quantity)),1),t("td",Ig,D(b!=null&&b.cost?`$${l.numberWithCommas(b==null?void 0:b.cost)}`:""),1),t("td",Pg,[t("div",Cg,[t("button",{class:"bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover font-semibold h-8 w-8 rounded-full px-2 py-0.5",onClick:I=>{var C;return l.toggleItem(c==null?void 0:c.id,b==null?void 0:b.id,b==null?void 0:b.type,(C=c==null?void 0:c.product)==null?void 0:C.id)}},[i(o,{icon:"newReportIcon"})],8,Tg),t("button",{class:"bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover font-semibold h-8 w-8 rounded-full px-2 pb-1",onClick:I=>l.deleteProductPackageItem(b==null?void 0:b.id),disabled:(b==null?void 0:b.type)==l.ProductAndPackageInventoryType.initial},[t("span",Fg,[i(o,{icon:"trashIcon"})])],8,Mg)])])])}),128))])])]),t("div",Ng,[(P=c==null?void 0:c.historyList)!=null&&P.length?(w(),$("div",_g,D(`Showing ${((c==null?void 0:c.currentPage)-1)*10+1} to ${(x=c==null?void 0:c.historyList)==null?void 0:x.length} of ${c==null?void 0:c.totalElements} entries`),1)):E("",!0),(c==null?void 0:c.pageCount)>=1?(w(),te(d,{key:1,"total-pages":c==null?void 0:c.pageCount,total:c==null?void 0:c.totalElements,"per-page":10,"current-page":c==null?void 0:c.currentPage,onPageChange:b=>l.pageChange(y,c==null?void 0:c.id,b)},null,8,["total-pages","total","current-page","onPageChange"])):E("",!0)])],2)],2)}),128))]))]))])])])]),i(s,{addNew:l.toggleAddInitial,showHelpInfo:!1},null,8,["addNew"]),i(u,{isVisible:l.isModalVisible,close:l.toggleAddInitial,ref:"formModal",productAndPackageInventoryId:l.productAndPackageInventoryId,loadTable:l.getPackageInfo},null,8,["isVisible","close","productAndPackageInventoryId","loadTable"])],64)}const Yt=Q(Km,[["render",Hg]]),nt=ne("pump",()=>({getPumpSummary:async({dailyReportId:a,callback:o})=>{var s;const r=g.get(o,"onSuccess",g.noop),d=g.get(o,"onFinish",g.noop);try{V.setHeader();const u=await V.get(`pumps/summary/${a}`);r(((s=u.data)==null?void 0:s.data)||u.data)}catch(u){H(u,o)}finally{d()}},getPumps:async({params:a,callback:o})=>{var s;const r=g.get(o,"onSuccess",g.noop),d=g.get(o,"onFinish",g.noop);try{V.setHeader();const u=await V.getWithParams("pumps",a);r(((s=u.data)==null?void 0:s.data)||u.data)}catch(u){H(u,o)}finally{d()}},getPumpDetails:async({id:a,callback:o})=>{var s;const r=g.get(o,"onSuccess",g.noop),d=g.get(o,"onFinish",g.noop);try{V.setHeader();const u=await V.get(`pumps/${a}`);r(((s=u.data)==null?void 0:s.data)||u.data)}catch(u){H(u,o)}finally{d()}},updatePump:async({id:a,params:o,callback:r})=>{var u;const d=g.get(r,"onSuccess",g.noop),s=g.get(r,"onFinish",g.noop);try{V.setHeader();const f=await V.put(`pumps/${a}`,o);d(((u=f.data)==null?void 0:u.data)||f.data)}catch(f){H(f,r)}finally{s()}},deletePump:async({id:a,callback:o})=>{var s;const r=g.get(o,"onSuccess",g.noop),d=g.get(o,"onFinish",g.noop);try{V.setHeader();const u=await V.delete(`pumps/${a}`);r(((s=u.data)==null?void 0:s.data)||u.data)}catch(u){H(u,o)}finally{d()}},createPump:async({params:a,callback:o})=>{var s;const r=g.get(o,"onSuccess",g.noop),d=g.get(o,"onFinish",g.noop);try{V.setHeader();const u=await V.post("pumps",a);r(((s=u.data)==null?void 0:s.data)||u.data)}catch(u){H(u,o)}finally{d()}}})),Jt=ne("pumpDuration",()=>({getPumpDurationDetails:async({id:m,callback:n})=>{var r;const a=g.get(n,"onSuccess",g.noop),o=g.get(n,"onFinish",g.noop);try{V.setHeader();const d=await V.get(`pumpDurations/${m}`);a(((r=d.data)==null?void 0:r.data)||d.data)}catch(d){H(d,n)}finally{o()}},updatePumpDuration:async({id:m,params:n,callback:a})=>{var d;const o=g.get(a,"onSuccess",g.noop),r=g.get(a,"onFinish",g.noop);try{V.setHeader();const s=await V.put(`pumpDurations/${m}`,n);o(((d=s.data)==null?void 0:d.data)||s.data)}catch(s){H(s,a)}finally{r()}},deletePumpDuration:async({id:m,callback:n})=>{var r;const a=g.get(n,"onSuccess",g.noop),o=g.get(n,"onFinish",g.noop);try{V.setHeader();const d=await V.delete(`pumpDurations/${m}`);a(((r=d.data)==null?void 0:r.data)||d.data)}catch(d){H(d,n)}finally{o()}},createPumpDuration:async({params:m,callback:n})=>{var r;const a=g.get(n,"onSuccess",g.noop),o=g.get(n,"onFinish",g.noop);try{V.setHeader();const d=await V.post("pumpDurations",m);a(((r=d.data)==null?void 0:r.data)||d.data)}catch(d){H(d,n)}finally{o()}}})),Ag=J({name:"pump-durations-modal",components:{SvgIcon:K},props:{isVisible:{type:Boolean,required:!0},close:{type:Function,required:!0},loadPage:{type:Function,required:!1}},setup(l){const e=Jt(),S=v(!1),h=v({durations:null}),m=v(null),n=v(!1),a=v(""),o=v("");Y(o,P=>{P!==""&&r()}),Y(()=>l.isVisible,P=>{var x;P===!1&&(o.value="",a.value="",M(),(x=m==null?void 0:m.value)==null||x.resetFields())});const r=async()=>{e.getPumpDurationDetails({id:o.value,callback:{onSuccess:P=>{h.value={...P}}}})},d=async P=>{n.value=!0,e.updatePumpDuration({id:o.value,params:P,callback:{onSuccess:x=>{l!=null&&l.loadPage&&(l==null||l.loadPage()),u()},onFinish:x=>{n.value=!1}}})},s=async P=>{n.value=!0,e.createPumpDuration({params:P,callback:{onSuccess:x=>{l!=null&&l.loadPage&&(l==null||l.loadPage()),u()},onFinish:x=>{n.value=!1}}})},u=()=>{l.close()},f=(P,x)=>{a.value=P,o.value=x},c=v({durations:[{required:!0,message:"Please type Duration",trigger:"blur"}]}),y=()=>{m.value&&m.value.validate(P=>{var x;if(P){const b={pumpId:a==null?void 0:a.value,durations:Number((x=h==null?void 0:h.value)==null?void 0:x.durations)};o!=null&&o.value?d(b):s(b)}})},M=()=>{h.value={durations:null}};return{id:o,modal:S,rules:c,loading:n,targetData:h,formRef:m,closeModal:u,submit:y,setId:f,reset:M}}}),zg={key:0,class:"fixed top-0 right-0 bottom-0 left-0 z-40 flex items-center"},qg={class:"relative bg-card-background text-card-text-light max-h-11/12 w-11/12 flex flex-col items-center gap-3 mx-auto my-6 p-4 rounded-xl md:w-1/4"},Bg={class:"flex flex-row h-auto w-full items-center justify-between"},jg={class:"text-lg font-bold"},Eg={class:"flex flex-col gap-1"},Rg={class:"h-auto w-full flex flex-row items-center gap-2"},Og=["disabled"],Lg=["disabled"],Wg={key:0,class:"indicator-label"},Ug={key:1,class:"indicator-progress"};function Gg(l,e,S,h,m,n){const a=k("SvgIcon"),o=k("el-input"),r=k("el-form-item"),d=k("el-form");return l.isVisible?(w(),$("div",zg,[e[5]||(e[5]=t("div",{class:"absolute inset-0 bg-sidebar-background opacity-25"},null,-1)),t("div",qg,[t("div",Bg,[t("h3",jg,D(`${l.id?"Edit Pump Duration":"Add Pump Duration"}`),1),t("span",{class:"cursor-pointer",onClick:e[0]||(e[0]=(...s)=>l.closeModal&&l.closeModal(...s))},[i(a,{icon:"closeModalIcon"})])]),i(d,{id:"pump_duration_form",onSubmit:ie(l.submit,["prevent"]),model:l.targetData,rules:l.rules,ref:"formRef",class:"h-auto w-full flex flex-col gap-3"},{default:p(()=>[t("div",Eg,[e[3]||(e[3]=t("label",{class:"font-bold"},[F("Duration (hr)"),t("span",{class:"text-danger-active font-light"},"*")],-1)),i(r,{prop:"durations",class:"mt-auto"},{default:p(()=>[i(o,{type:"number",controls:!1,step:"any",modelValue:l.targetData.durations,"onUpdate:modelValue":e[1]||(e[1]=s=>l.targetData.durations=s),placeholder:"",name:"durations"},null,8,["modelValue"])]),_:1})]),t("div",Rg,[t("button",{type:"button",class:"bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover rounded-md px-4 py-2 font-semibold",onClick:e[2]||(e[2]=(...s)=>l.closeModal&&l.closeModal(...s)),disabled:l.loading}," Discard ",8,Og),t("button",{class:"bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover rounded-md px-4 py-2 font-semibold",type:"submit",disabled:l.loading},[l.loading?E("",!0):(w(),$("span",Wg," Save ")),l.loading?(w(),$("span",Ug,e[4]||(e[4]=[F(" Please wait... "),t("span",{class:"spinner-border spinner-border-sm align-middle ms-2"},null,-1)]))):E("",!0)],8,Lg)])]),_:1},8,["onSubmit","model","rules"])])])):E("",!0)}const Qt=Q(Ag,[["render",Gg]]),Yg=J({name:"pump-modal",components:{SvgIcon:K},props:{isVisible:{type:Boolean,required:!0},close:{type:Function,required:!0},loadPage:{type:Function,default:()=>{},required:!0}},setup(l){const e=re(),S=nt(),h=v(!1),m={description:"",inUse:!1,model:"",linearID:null,rodOD:null,strokeLength:null,efficiency:null,stroke:null,displacement:null,rate:null},n=v(JSON.parse(JSON.stringify(m))),a=v(null),o=v(!1),r=v(""),d=le("dailyReport");Y(r,b=>{b!==""&&s()}),Y(()=>l.isVisible,b=>{var T;b===!1&&(r.value="",x(),(T=a==null?void 0:a.value)==null||T.resetFields())});const s=async()=>{S.getPumpDetails({id:r.value,callback:{onSuccess:b=>{n.value=JSON.parse(JSON.stringify(b))}}})},u=async b=>{o.value=!0,S.updatePump({id:r.value,params:b,callback:{onSuccess:T=>{l!=null&&l.loadPage&&(l==null||l.loadPage()),c()},onFinish:T=>{o.value=!1}}})},f=async b=>{o.value=!0,S.createPump({params:b,callback:{onSuccess:T=>{l!=null&&l.loadPage&&(l==null||l.loadPage()),c()},onFinish:T=>{o.value=!1}}})},c=()=>{l.close()},y=b=>{r.value=b.toString()},M=v({description:[{required:!0,message:"Please type Description",trigger:"blur"}]}),P=()=>{a.value&&a.value.validate(b=>{var T,q,I,C,B,A,_,N,j,z,R;if(b){const L={description:(T=n==null?void 0:n.value)==null?void 0:T.description,inUse:(q=n==null?void 0:n.value)==null?void 0:q.inUse,model:(I=n==null?void 0:n.value)==null?void 0:I.model,linearID:Number((C=n==null?void 0:n.value)==null?void 0:C.linearID),rodOD:Number((B=n==null?void 0:n.value)==null?void 0:B.rodOD),strokeLength:Number((A=n==null?void 0:n.value)==null?void 0:A.strokeLength),efficiency:Number((_=n==null?void 0:n.value)==null?void 0:_.efficiency),stroke:Number((N=n==null?void 0:n.value)==null?void 0:N.stroke),displacement:Number((j=n==null?void 0:n.value)==null?void 0:j.displacement),rate:Number((z=n==null?void 0:n.value)==null?void 0:z.rate)};r!=null&&r.value?u({...L,dailyReportId:d==null?void 0:d.getDailyReportId()}):d==null||d.createDailyReport({wellId:(R=e==null?void 0:e.params)==null?void 0:R.id,callback:{onSuccess:U=>{f({...L,dailyReportId:d==null?void 0:d.getDailyReportId()})}}})}})},x=()=>{n.value=JSON.parse(JSON.stringify(m))};return{id:r,modal:h,rules:M,loading:o,targetData:n,formRef:a,closeModal:c,submit:P,setId:y,reset:x}}}),Jg={key:0,class:"fixed top-0 right-0 bottom-0 left-0 z-40 flex items-center"},Qg={class:"relative bg-card-background text-card-text-light max-h-11/12 w-11/12 flex flex-col items-center gap-3 mx-auto my-6 p-5 rounded-xl overflow-y-scroll md:overflow-hidden md:w-4/5 lg:w-2/5 lg:h-auto 7"},Kg={class:"flex flex-row h-auto w-full items-center justify-between"},Xg={class:"text-lg font-bold"},Zg={class:"h-auto w-full flex flex-col md:flex-row md:gap-4"},eb={class:"h-auto w-full flex flex-col gap-3"},tb={class:"flex flex-col gap-1"},lb={class:"flex flex-col gap-1"},ob={class:"font-bold"},sb={class:"flex flex-col gap-1"},nb={class:"flex flex-col gap-1"},ab={class:"font-bold"},ib={class:"flex flex-col gap-1"},rb={class:"h-auto w-full flex flex-col gap-3"},db={class:"h-auto w-full flex gap-6 items-center md:h-20"},ub={class:"font-bold"},pb={class:"flex flex-col gap-1"},cb={class:"font-bold"},fb={class:"flex flex-col gap-1"},mb={class:"font-bold"},gb={class:"flex flex-col gap-1"},bb={class:"font-bold"},hb={class:"flex flex-col gap-1"},vb={class:"font-bold"},yb={class:"h-auto w-full flex flex-row items-center gap-2"},wb=["disabled"],Sb=["disabled"],$b={key:0,class:"indicator-label"},xb={key:1,class:"indicator-progress"};function kb(l,e,S,h,m,n){const a=k("SvgIcon"),o=k("el-input"),r=k("el-form-item"),d=k("el-popover"),s=k("el-form");return l.isVisible?(w(),$("div",Jg,[e[37]||(e[37]=t("div",{class:"absolute inset-0 bg-sidebar-background opacity-25"},null,-1)),t("div",Qg,[t("div",Kg,[t("h3",Xg,D(`${l.id?"Edit Pump":"Add Pump"}`),1),t("span",{class:"cursor-pointer",onClick:e[0]||(e[0]=(...u)=>l.closeModal&&l.closeModal(...u))},[i(a,{icon:"closeModalIcon"})])]),i(s,{id:"pump_form",onSubmit:ie(l.submit,["prevent"]),model:l.targetData,rules:l.rules,ref:"formRef",class:"h-auto w-full"},{default:p(()=>[t("div",Zg,[t("div",eb,[t("div",tb,[e[12]||(e[12]=t("label",{class:"font-bold"},"Description ",-1)),i(r,{prop:"description",class:"mt-auto"},{default:p(()=>[i(o,{modelValue:l.targetData.description,"onUpdate:modelValue":e[1]||(e[1]=u=>l.targetData.description=u),placeholder:"",name:"description"},null,8,["modelValue"])]),_:1})]),t("div",lb,[t("label",ob,[e[15]||(e[15]=t("span",null,"Model",-1)),i(d,{placement:"top",width:250,trigger:"hover"},{reference:p(()=>e[13]||(e[13]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7"},null,-1)])),default:p(()=>[e[14]||(e[14]=t("span",null,"Specifies the model or type of the pump. ",-1))]),_:1})]),i(r,{prop:"model"},{default:p(()=>[i(o,{modelValue:l.targetData.model,"onUpdate:modelValue":e[2]||(e[2]=u=>l.targetData.model=u),placeholder:"",name:"model"},null,8,["modelValue"])]),_:1})]),t("div",sb,[e[16]||(e[16]=t("label",{class:"font-bold"}," Linear ID (in) ",-1)),i(r,{prop:"linearID"},{default:p(()=>[i(o,{type:"number",controls:!1,step:"any",modelValue:l.targetData.linearID,"onUpdate:modelValue":e[3]||(e[3]=u=>l.targetData.linearID=u),placeholder:"",name:"linearID"},null,8,["modelValue"])]),_:1})]),t("div",nb,[t("label",ab,[e[19]||(e[19]=t("span",null,"Rod OD (in)",-1)),i(d,{placement:"top",width:250,trigger:"hover"},{reference:p(()=>e[17]||(e[17]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7"},null,-1)])),default:p(()=>[e[18]||(e[18]=t("span",null,"The outside diameter of the pump's rod or plunger. ",-1))]),_:1})]),i(r,{prop:"rodOD"},{default:p(()=>[i(o,{type:"number",controls:!1,step:"any",modelValue:l.targetData.rodOD,"onUpdate:modelValue":e[4]||(e[4]=u=>l.targetData.rodOD=u),placeholder:"",name:"rodOD"},null,8,["modelValue"])]),_:1})]),t("div",ib,[e[20]||(e[20]=t("label",{class:"font-bold"}," Stroke Length (in) ",-1)),i(r,{prop:"strokeLength"},{default:p(()=>[i(o,{type:"number",controls:!1,step:"any",modelValue:l.targetData.strokeLength,"onUpdate:modelValue":e[5]||(e[5]=u=>l.targetData.strokeLength=u),placeholder:"",name:"strokeLength"},null,8,["modelValue"])]),_:1})])]),t("div",rb,[t("div",db,[t("label",ub,[e[23]||(e[23]=F("In Use ")),i(d,{placement:"top",width:200,trigger:"hover"},{reference:p(()=>e[21]||(e[21]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7"},null,-1)])),default:p(()=>[e[22]||(e[22]=t("span",null," Indicates whether the pump is currently in use or not. ",-1))]),_:1})]),i(r,{prop:"inUse",style:"margin-bottom: 0;"},{default:p(()=>[yt(t("input",{class:"h-4 w-4",type:"checkbox",placeholder:"",name:"inUse","onUpdate:modelValue":e[6]||(e[6]=u=>l.targetData.inUse=u)},null,512),[[wt,l.targetData.inUse]])]),_:1})]),t("div",pb,[t("label",cb,[e[26]||(e[26]=t("span",null,"Efficiency (%)",-1)),i(d,{placement:"top",width:250,trigger:"hover"},{reference:p(()=>e[24]||(e[24]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7"},null,-1)])),default:p(()=>[e[25]||(e[25]=t("span",null,"The efficiency of the pump, indicating how effectively it converts mechanical power to hydraulic power. ",-1))]),_:1})]),i(r,{prop:"efficiency"},{default:p(()=>[i(o,{type:"number",controls:!1,step:"any",modelValue:l.targetData.efficiency,"onUpdate:modelValue":e[7]||(e[7]=u=>l.targetData.efficiency=u),placeholder:"",name:"efficiency",min:0,max:100},null,8,["modelValue"])]),_:1})]),t("div",fb,[t("label",mb,[e[29]||(e[29]=t("span",null,"Stroke (stroke/min)",-1)),i(d,{placement:"top",width:250,trigger:"hover"},{reference:p(()=>e[27]||(e[27]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7"},null,-1)])),default:p(()=>[e[28]||(e[28]=t("span",null,"The number of strokes (up-and-down movements) the pump makes per minute. ",-1))]),_:1})]),i(r,{prop:"stroke"},{default:p(()=>[i(o,{type:"number",controls:!1,step:"any",modelValue:l.targetData.stroke,"onUpdate:modelValue":e[8]||(e[8]=u=>l.targetData.stroke=u),placeholder:"",name:"stroke"},null,8,["modelValue"])]),_:1})]),t("div",gb,[t("label",bb,[e[32]||(e[32]=t("span",null,"Displacement (bbl/stroke)",-1)),i(d,{placement:"top",width:250,trigger:"hover"},{reference:p(()=>e[30]||(e[30]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7"},null,-1)])),default:p(()=>[e[31]||(e[31]=t("span",null,"The volume of fluid displaced by the pump with each stroke. ",-1))]),_:1})]),i(r,{prop:"displacement"},{default:p(()=>[i(o,{type:"number",controls:!1,step:"any",modelValue:l.targetData.displacement,"onUpdate:modelValue":e[9]||(e[9]=u=>l.targetData.displacement=u),placeholder:"",name:"displacement"},null,8,["modelValue"])]),_:1})]),t("div",hb,[t("label",vb,[e[35]||(e[35]=t("span",null,"Rate (gpm)",-1)),i(d,{placement:"top",width:250,trigger:"hover"},{reference:p(()=>e[33]||(e[33]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7"},null,-1)])),default:p(()=>[e[34]||(e[34]=t("span",null,"The flow rate of fluid delivered by the pump. ",-1))]),_:1})]),i(r,{prop:"rate"},{default:p(()=>[i(o,{type:"number",controls:!1,step:"any",modelValue:l.targetData.rate,"onUpdate:modelValue":e[10]||(e[10]=u=>l.targetData.rate=u),placeholder:"",name:"rate"},null,8,["modelValue"])]),_:1})])])]),t("div",yb,[t("button",{type:"button",class:"bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover rounded-md px-4 py-2 font-semibold",onClick:e[11]||(e[11]=(...u)=>l.closeModal&&l.closeModal(...u)),disabled:l.loading}," Discard ",8,wb),t("button",{class:"bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover rounded-md px-4 py-2 font-semibold",type:"submit",disabled:l.loading},[l.loading?E("",!0):(w(),$("span",$b," Save ")),l.loading?(w(),$("span",xb,e[36]||(e[36]=[F(" Please wait... "),t("span",{class:"spinner-border spinner-border-sm align-middle ms-2"},null,-1)]))):E("",!0)],8,Sb)])]),_:1},8,["onSubmit","model","rules"])])])):E("",!0)}const Kt=Q(Yg,[["render",kb]]),Db=J({name:"pump-details-modal",components:{SvgIcon:K,PumpModal:Kt,PumpDurationModal:Qt},props:{isVisible:{type:Boolean,required:!0},close:{type:Function,required:!0},loadPage:{type:Function,required:!1}},setup(l){const e=Jt(),S=nt(),h=v(!1),m=v(null),n=v(!1),a=v(""),o=v(null),r=v(null),d=v(!1),s=v(!1);Y(a,T=>{T!==""&&u()}),Y(()=>l.isVisible,T=>{T==!0&&u()});const u=async()=>{n.value=!0,S.getPumpDetails({id:a.value,callback:{onSuccess:T=>{m.value={...T}},onFinish:T=>{n.value=!1}}})},f=()=>{l.close()},c=T=>{a.value=T.toString()},y=()=>{u(),l!=null&&l.loadPage&&l.loadPage()},M=T=>{var q;(q=r==null?void 0:r.value)==null||q.setId(a.value,T||""),s.value=!s.value},P=T=>{var q;d.value||(q=o==null?void 0:o.value)==null||q.setId(T),d.value=!d.value},x=T=>{se.deletionAlert({onConfirmed:()=>{b(T)}})},b=T=>{e.deletePumpDuration({id:T,callback:{onSuccess:q=>{u(),l!=null&&l.loadPage&&l.loadPage()}}})};return{id:a,modal:h,loading:n,data:m,pumpModal:o,pumpDurationModal:r,isEditPumpModalVisible:d,isPumpDurationModalVisible:s,setId:c,formatDate:ke,reloadPage:y,numberWithCommas:fe,toggleDurationModal:M,toggleEditPumpModal:P,deleteDuration:x,closeModal:f}}}),Vb={key:0,class:"fixed top-0 right-0 bottom-0 left-0 z-40 flex items-center"},Ib={class:"relative bg-card-background text-card-text-light max-h-11/12 w-11/12 flex flex-col items-center gap-3 mx-auto my-6 p-4 rounded-xl overflow-y-scroll md:overflow-hidden md:w-3/5 lg:w-2/5 lg:h-auto lg:max-h-3/5"},Pb={class:"flex flex-row h-auto w-full items-center justify-between"},Cb={key:0,class:"text-center"},Tb={key:1,class:"h-auto w-full flex flex-col gap-3 font-semibold"},Mb={class:"font-bold"},Fb={class:"h-auto w-full flex flex-col gap-3"},Nb={class:"h-auto w-full flex items-center justify-between border-b-[1px] border-dashed pb-3"},_b={class:"h-auto w-full flex items-center justify-between border-b-[1px] border-dashed pb-3"},Hb={class:"h-auto w-full flex items-center justify-between border-b-[1px] border-dashed pb-3"},Ab={class:"flex flex-wrap gap-3 items-center justify-center"},zb={class:"border border-active border-dashed rounded p-4"},qb={class:"border border-active border-dashed rounded p-4"},Bb={class:"border border-active border-dashed rounded p-4"},jb={class:"border border-active border-dashed rounded p-4"},Eb={class:"border border-active border-dashed rounded p-4"},Rb={class:"border border-active border-dashed rounded p-4"},Ob={class:"border border-active border-dashed rounded p-4"},Lb={class:"flex items-center gap-3"},Wb=["onClick"],Ub=["onClick"],Gb={class:"text-danger"},Yb={class:"h-auto w-full flex items-center justify-between"},Jb=["disabled"],Qb=["disabled"],Kb=["disabled"];function Xb(l,e,S,h,m,n){var s,u,f,c,y,M,P,x,b,T,q,I,C,B,A,_,N,j,z,R,L,U,oe;const a=k("SvgIcon"),o=k("el-empty"),r=k("PumpModal"),d=k("PumpDurationModal");return w(),$(O,null,[l.isVisible?(w(),$("div",Vb,[e[17]||(e[17]=t("div",{class:"absolute inset-0 bg-sidebar-background opacity-25"},null,-1)),t("div",Ib,[t("div",Pb,[e[4]||(e[4]=t("h3",{class:"text-lg font-bold"},"Pump Details",-1)),t("span",{class:"cursor-pointer ms-auto",onClick:e[0]||(e[0]=(...X)=>l.closeModal&&l.closeModal(...X))},[i(a,{icon:"closeModalIcon"})])]),l.loading?(w(),$("div",Cb,e[5]||(e[5]=[t("div",{class:"spinner-border text-primary",role:"status"},[t("span",{class:"sr-only"},"Loading...")],-1)]))):(w(),$("div",Tb,[t("h3",Mb,D(((s=l.data)==null?void 0:s.description)||""),1),t("div",Fb,[t("div",Nb,[e[6]||(e[6]=t("span",null,"In Use",-1)),t("span",{class:be({"text-success":(u=l.data)==null?void 0:u.inUse,"text-danger":!((f=l.data)!=null&&f.inUse)})},D((c=l.data)!=null&&c.inUse?"Active":"Inactive"),3)]),t("div",_b,[e[7]||(e[7]=t("span",null,"Model",-1)),t("span",null,D(((y=l.data)==null?void 0:y.model)||""),1)]),t("div",Hb,[e[8]||(e[8]=t("span",null,"Total Duration",-1)),t("span",null,D(`${((M=l.data)==null?void 0:M.totalDurations)||"0"} hrs`),1)])]),t("div",Ab,[t("div",zb,[t("div",null,D(`${(P=l.data)!=null&&P.linearID?l.numberWithCommas((x=l.data)==null?void 0:x.linearID):""} (in)`),1),e[9]||(e[9]=t("div",{class:"text-danger"},"Linear ID",-1))]),t("div",qb,[t("div",null,D(`${(b=l.data)!=null&&b.rodOD?l.numberWithCommas((T=l.data)==null?void 0:T.rodOD):""} (in)`),1),e[10]||(e[10]=t("div",{class:"text-primary"},"Rod OD",-1))]),t("div",Bb,[t("div",null,D(`${(q=l.data)!=null&&q.strokeLength?l.numberWithCommas((I=l.data)==null?void 0:I.strokeLength):""} (in)`),1),e[11]||(e[11]=t("div",{class:"text-primary"},"Stroke Length",-1))]),t("div",jb,[t("div",null,D(`${(C=l.data)!=null&&C.efficiency?l.numberWithCommas((B=l.data)==null?void 0:B.efficiency):""} (%)`),1),e[12]||(e[12]=t("div",{class:"text-primary"},"Efficiency",-1))]),t("div",Eb,[t("div",null,D(`${(A=l.data)!=null&&A.stroke?l.numberWithCommas((_=l.data)==null?void 0:_.stroke):""} (stroke/min)`),1),e[13]||(e[13]=t("div",{class:"text-primary"},"Stroke",-1))]),t("div",Rb,[t("div",null,D(`${(N=l.data)!=null&&N.displacement?l.numberWithCommas((j=l.data)==null?void 0:j.displacement):""} (bbl/stroke)`),1),e[14]||(e[14]=t("div",{class:"text-primary"},"Displacement",-1))]),t("div",Ob,[t("div",null,D(`${(z=l.data)!=null&&z.rate?l.numberWithCommas((R=l.data)==null?void 0:R.rate):""} (gpm)`),1),e[15]||(e[15]=t("div",{class:"text-primary"},"Rate",-1))])]),e[16]||(e[16]=t("p",{class:"font-bold"},"Time Distribution",-1)),((U=(L=l.data)==null?void 0:L.durations)==null?void 0:U.length)===0?(w(),te(o,{key:0,"image-size":100})):(w(!0),$(O,{key:1},Z((oe=l.data)==null?void 0:oe.durations,X=>(w(),$("div",{key:X.id,class:"h-auto w-full flex flex-row items-center justify-between gap-2"},[t("span",null,D(l.formatDate(X==null?void 0:X.createdAt,"DD MMM YYYY HH:mm")),1),t("span",null,D(`${X==null?void 0:X.durations} hrs`),1),t("div",Lb,[t("button",{class:"p-2 rounded-full bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover",onClick:()=>l.toggleDurationModal(X==null?void 0:X.id)},[i(a,{icon:"newReportIcon"})],8,Wb),t("button",{class:"p-2 rounded-full bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover",onClick:W=>l.deleteDuration(X==null?void 0:X.id)},[t("span",Gb,[i(a,{icon:"trashIcon"})])],8,Ub)])]))),128))])),t("div",Yb,[t("button",{type:"button",class:"bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover rounded-md px-4 py-2 font-semibold",disabled:l.loading,onClick:e[1]||(e[1]=(...X)=>l.closeModal&&l.closeModal(...X))}," Close ",8,Jb),t("button",{class:"bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover rounded-md px-4 py-2 font-semibold",type:"button",disabled:l.loading,onClick:e[2]||(e[2]=()=>{var X,W;return l.toggleEditPumpModal(((W=(X=l.data)==null?void 0:X.id)==null?void 0:W.toString())||"")})}," Edit Pump ",8,Qb),t("button",{class:"bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover rounded-md px-4 py-2 font-semibold",type:"button",disabled:l.loading,onClick:e[3]||(e[3]=()=>l.toggleDurationModal())}," Add Duration ",8,Kb)])])])):E("",!0),i(r,{isVisible:l.isEditPumpModalVisible,close:l.toggleEditPumpModal,ref:"pumpModal",loadPage:l.reloadPage},null,8,["isVisible","close","loadPage"]),i(d,{isVisible:l.isPumpDurationModalVisible,close:l.toggleDurationModal,ref:"pumpDurationModal",loadPage:l.reloadPage},null,8,["isVisible","close","loadPage"])],64)}const Zb=Q(Db,[["render",Xb],["__scopeId","data-v-a4c52e1b"]]),eh=J({name:"pump-equipment",components:{SvgIcon:K,PumpModal:Kt,PumpDetailsModal:Zb,PumpDurationModal:Qt,NoEntries:me},props:{loadPage:{type:Function,required:!0}},setup(l){const e=nt(),S=v(null),h=v(null),m=v(null),n=v([]),a=v(!1),o=le("dailyReport"),r=v(!1),d=v(!1),s=v(!1);ae(()=>{o!=null&&o.getDailyReportId()&&f()});const u=()=>{f(),l!=null&&l.loadPage&&(l==null||l.loadPage())},f=async(b={dailyReportId:o==null?void 0:o.getDailyReportId(),page:1,limit:200})=>{a.value=!0,e.getPumps({params:b,callback:{onSuccess:T=>{n.value=T==null?void 0:T.items},onFinish:T=>{a.value=!1}}})},c=b=>{var T;b&&((T=S==null?void 0:S.value)==null||T.setId(b)),r.value=!r.value},y=(b,T)=>{var q;(q=m==null?void 0:m.value)==null||q.setId(b,T||""),s.value=!s.value},M=b=>{var T;b&&((T=h==null?void 0:h.value)==null||T.setId(b)),d.value=!d.value},P=b=>{se.deletionAlert({onConfirmed:()=>{x(b)}})},x=async b=>{a.value=!0,e.deletePump({id:b,callback:{onSuccess:T=>{u()},onFinish:T=>{a.value=!1}}})};return{pumpModal:S,loadingPump:a,pumpDetailsModal:h,pumpDurationModal:m,pumpEquipmentList:n,isPumpModalVisible:r,isPumpDetailsModalVisible:d,isPumpDurationModalVisible:s,deletePump:P,loadData:u,togglePumpModal:c,togglePumpDetailsModal:M,togglePumpDurationModal:y}}}),th={class:"h-auto w-full my-4 p-4 bg-minicard-background text-minicard-text-light rounded-lg border-[1px] border-dashed font-semibold"},lh={key:0,class:"text-center"},oh={key:1,class:"grid grid-cols-1 gap-4 md:grid-cols-2 md:gap-7 lg:grid-cols-3"},sh={class:"flex flex-col gap-3 p-4"},nh=["onClick"],ah={class:"flex items-center justify-between border-b-[1px] border-dashed pb-3"},ih={class:"flex items-center justify-between border-b-[1px] border-dashed pb-3"},rh={class:"flex items-center justify-between border-b-[1px] border-dashed pb-3"},dh={class:"flex flex-row items-center gap-2 absolute top-0 end-0 -translate-y-1/2"},uh=["onClick"],ph=["onClick"],ch=["onClick"],fh={class:"text-danger"};function mh(l,e,S,h,m,n){const a=k("NoEntries"),o=k("SvgIcon"),r=k("el-tooltip"),d=k("PumpModal"),s=k("PumpDetailsModal"),u=k("PumpDurationModal");return w(),$(O,null,[t("div",th,[e[4]||(e[4]=t("h4",{class:"font-bold mb-4"},"Pump Equipment",-1)),l.loadingPump?(w(),$("div",lh,e[0]||(e[0]=[t("div",{class:"spinner-border text-primary",role:"status"},[t("span",{class:"sr-only"},"Loading...")],-1)]))):(w(),$(O,{key:1},[l.pumpEquipmentList.length===0?(w(),te(a,{key:0,addNew:()=>l.togglePumpModal()},null,8,["addNew"])):(w(),$("div",oh,[(w(!0),$(O,null,Z(l.pumpEquipmentList,f=>(w(),$("div",{key:f==null?void 0:f.id,class:"rounded-lg border-[1px] border-dashed relative cursor-pointer my-2 first:mt-0 last:mb-0 md:first:mt-0 md:last:mb-0 md:m-0"},[t("div",sh,[t("h5",{class:"font-bold underline underline-offset-2",onClick:c=>l.togglePumpDetailsModal((f==null?void 0:f.id)||"")},D(f==null?void 0:f.description),9,nh),t("div",ah,[e[1]||(e[1]=t("span",null,"In Use",-1)),t("span",{class:be({"text-success":f==null?void 0:f.inUse,"text-danger":!(f!=null&&f.inUse)})},D(f!=null&&f.inUse?"Active":"Inactive"),3)]),t("div",ih,[e[2]||(e[2]=t("span",null,"Model",-1)),t("span",null,D(f==null?void 0:f.model),1)]),t("div",rh,[e[3]||(e[3]=t("span",null,"Total Duration",-1)),t("span",null,D(`${(f==null?void 0:f.totalDurations)||"0"} hrs`),1)])]),t("div",dh,[i(r,{content:"Add Duration",placement:"top",effect:"customize"},{default:p(()=>[t("button",{class:"p-2 rounded-full bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover",onClick:()=>l.togglePumpDurationModal((f==null?void 0:f.id)||"")},[i(o,{icon:"durationIcon"})],8,uh)]),_:2},1024),i(r,{content:"Edit Pump",placement:"top",effect:"customize"},{default:p(()=>[t("button",{class:"p-2 rounded-full bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover",onClick:c=>l.togglePumpModal((f==null?void 0:f.id)||"")},[i(o,{icon:"pencilIcon"})],8,ph)]),_:2},1024),i(r,{content:"Delete Pump",placement:"top",effect:"customize"},{default:p(()=>[t("button",{class:"p-2 rounded-full bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover",onClick:c=>l.deletePump((f==null?void 0:f.id)||"")},[t("span",fh,[i(o,{icon:"trashIcon"})])],8,ch)]),_:2},1024)])]))),128))]))],64))]),i(d,{isVisible:l.isPumpModalVisible,close:l.togglePumpModal,ref:"pumpModal",loadPage:l.loadData},null,8,["isVisible","close","loadPage"]),i(s,{isVisible:l.isPumpDetailsModalVisible,close:l.togglePumpDetailsModal,ref:"pumpDetailsModal",loadPage:l.loadData},null,8,["isVisible","close","loadPage"]),i(u,{isVisible:l.isPumpDurationModalVisible,close:l.togglePumpDurationModal,ref:"pumpDurationModal",loadPage:l.loadData},null,8,["isVisible","close","loadPage"])],64)}const gh=Q(eh,[["render",mh]]),dt=ne("solidControlEquipment",()=>({getSolidControlEquipments:async({params:n,callback:a})=>{var d;const o=g.get(a,"onSuccess",g.noop),r=g.get(a,"onFinish",g.noop);try{V.setHeader();const s=await V.getWithParams("solidControlEquipments",n);o(((d=s.data)==null?void 0:d.data)||s.data)}catch(s){H(s,a)}finally{r()}},getSolidControlEquipmentDetails:async({id:n,callback:a})=>{var d;const o=g.get(a,"onSuccess",g.noop),r=g.get(a,"onFinish",g.noop);try{V.setHeader();const s=await V.get(`solidControlEquipments/${n}`);o(((d=s.data)==null?void 0:d.data)||s.data)}catch(s){H(s,a)}finally{r()}},updateSolidControlEquipment:async({id:n,params:a,callback:o})=>{var s;const r=g.get(o,"onSuccess",g.noop),d=g.get(o,"onFinish",g.noop);try{V.setHeader();const u=await V.put(`solidControlEquipments/${n}`,a);r(((s=u.data)==null?void 0:s.data)||u.data)}catch(u){H(u,o)}finally{d()}},deleteSolidControlEquipment:async({id:n,callback:a})=>{var d;const o=g.get(a,"onSuccess",g.noop),r=g.get(a,"onFinish",g.noop);try{V.setHeader();const s=await V.delete(`solidControlEquipments/${n}`);o(((d=s.data)==null?void 0:d.data)||s.data)}catch(s){H(s,a)}finally{r()}},createSolidControlEquipment:async({params:n,callback:a})=>{var d;const o=g.get(a,"onSuccess",g.noop),r=g.get(a,"onFinish",g.noop);try{V.setHeader();const s=await V.post("solidControlEquipments",n);o(((d=s.data)==null?void 0:d.data)||s.data)}catch(s){H(s,a)}finally{r()}}})),Xt=ne("solidControlEquipmentInput",()=>({getSolidControlEquipmentInputDetails:async({id:m,callback:n})=>{var r;const a=g.get(n,"onSuccess",g.noop),o=g.get(n,"onFinish",g.noop);try{V.setHeader();const d=await V.get(`solidControlEquipmentInputs/${m}`);a(((r=d.data)==null?void 0:r.data)||d.data)}catch(d){H(d,n)}finally{o()}},updateSolidControlEquipmentInput:async({id:m,params:n,callback:a})=>{var d;const o=g.get(a,"onSuccess",g.noop),r=g.get(a,"onFinish",g.noop);try{V.setHeader();const s=await V.put(`solidControlEquipmentInputs/${m}`,n);o(((d=s.data)==null?void 0:d.data)||s.data)}catch(s){H(s,a)}finally{r()}},deleteSolidControlEquipmentInput:async({id:m,callback:n})=>{var r;const a=g.get(n,"onSuccess",g.noop),o=g.get(n,"onFinish",g.noop);try{V.setHeader();const d=await V.delete(`solidControlEquipmentInputs/${m}`);a(((r=d.data)==null?void 0:r.data)||d.data)}catch(d){H(d,n)}finally{o()}},createSolidControlEquipmentInput:async({params:m,callback:n})=>{var r;const a=g.get(n,"onSuccess",g.noop),o=g.get(n,"onFinish",g.noop);try{V.setHeader();const d=await V.post("solidControlEquipmentInputs",m);a(((r=d.data)==null?void 0:r.data)||d.data)}catch(d){H(d,n)}finally{o()}}})),Zt=ne("solidControlEquipmentTime",()=>({getSolidControlEquipmentTimeDetails:async({id:m,callback:n})=>{var r;const a=g.get(n,"onSuccess",g.noop),o=g.get(n,"onFinish",g.noop);try{V.setHeader();const d=await V.get(`solidControlEquipmentTimes/${m}`);a(((r=d.data)==null?void 0:r.data)||d.data)}catch(d){H(d,n)}finally{o()}},updateSolidControlEquipmentTime:async({id:m,params:n,callback:a})=>{var d;const o=g.get(a,"onSuccess",g.noop),r=g.get(a,"onFinish",g.noop);try{V.setHeader();const s=await V.put(`solidControlEquipmentTimes/${m}`,n);o(((d=s.data)==null?void 0:d.data)||s.data)}catch(s){H(s,a)}finally{r()}},deleteSolidControlEquipmentTime:async({id:m,callback:n})=>{var r;const a=g.get(n,"onSuccess",g.noop),o=g.get(n,"onFinish",g.noop);try{V.setHeader();const d=await V.delete(`solidControlEquipmentTimes/${m}`);a(((r=d.data)==null?void 0:r.data)||d.data)}catch(d){H(d,n)}finally{o()}},createSolidControlEquipmentTime:async({params:m,callback:n})=>{var r;const a=g.get(n,"onSuccess",g.noop),o=g.get(n,"onFinish",g.noop);try{V.setHeader();const d=await V.post("solidControlEquipmentTimes",m);a(((r=d.data)==null?void 0:r.data)||d.data)}catch(d){H(d,n)}finally{o()}}})),bh=J({name:"solid-duration-modal",components:{SvgIcon:K},props:{isVisible:{type:Boolean,required:!0},close:{type:Function,required:!0},loadPage:{type:Function,required:!1},zIndex:{type:Number,default:40,required:!1}},setup(l){const e=Zt(),S=v(!1),h=v({duration:null}),m=v(null),n=v(!1),a=v(""),o=v("");Y(o,P=>{P!==""&&r()}),Y(()=>l.isVisible,P=>{var x;P===!1&&(o.value="",a.value="",M(),(x=m==null?void 0:m.value)==null||x.resetFields())});const r=async()=>{e.getSolidControlEquipmentTimeDetails({id:o.value,callback:{onSuccess:P=>{h.value={...P}}}})},d=async P=>{n.value=!0,e.updateSolidControlEquipmentTime({id:o.value,params:P,callback:{onSuccess:x=>{l!=null&&l.loadPage&&(l==null||l.loadPage()),u()},onFinish:x=>{n.value=!1}}})},s=async P=>{n.value=!0,e.createSolidControlEquipmentTime({params:P,callback:{onSuccess:x=>{l!=null&&l.loadPage&&(l==null||l.loadPage()),u()},onFinish:x=>{n.value=!1}}})},u=()=>{l.close()},f=(P,x)=>{a.value=P,o.value=x},c=v({duration:[{required:!0,message:"Please type Duration",trigger:"blur"}]}),y=()=>{m.value&&m.value.validate(P=>{var x;if(P){const b={solidControlEquipmentId:a==null?void 0:a.value,duration:Number((x=h==null?void 0:h.value)==null?void 0:x.duration)};o!=null&&o.value?d(b):s(b)}})},M=()=>{h.value={duration:null}};return{id:o,modal:S,rules:c,loading:n,targetData:h,formRef:m,closeModal:u,submit:y,setId:f,reset:M}}}),hh={key:0,class:"fixed top-0 right-0 bottom-0 left-0 z-40 flex items-center"},vh={class:"relative bg-card-background text-card-text-light max-h-11/12 w-11/12 flex flex-col items-center gap-3 mx-auto my-6 p-4 rounded-xl md:w-1/4"},yh={class:"flex flex-row h-auto w-full items-center justify-between"},wh={class:"text-lg font-bold"},Sh={class:"flex flex-col gap-1"},$h={class:"h-auto w-full flex flex-row items-center gap-2"},xh=["disabled"],kh=["disabled"],Dh={key:0,class:"indicator-label"},Vh={key:1,class:"indicator-progress"};function Ih(l,e,S,h,m,n){const a=k("SvgIcon"),o=k("el-input"),r=k("el-form-item"),d=k("el-form");return l.isVisible?(w(),$("div",hh,[e[5]||(e[5]=t("div",{class:"absolute inset-0 bg-sidebar-background opacity-25"},null,-1)),t("div",vh,[t("div",yh,[t("h3",wh,D(`${l.id?"Edit Solid duration":"Add Solid duration"}`),1),t("span",{class:"cursor-pointer ms-auto",onClick:e[0]||(e[0]=(...s)=>l.closeModal&&l.closeModal(...s))},[i(a,{icon:"closeModalIcon"})])]),i(d,{id:"solid_duration_form",onSubmit:ie(l.submit,["prevent"]),model:l.targetData,rules:l.rules,ref:"formRef",class:"h-auto w-full flex flex-col gap-3"},{default:p(()=>[t("div",Sh,[e[3]||(e[3]=t("label",{class:"font-bold"},"Duration (hr) ",-1)),i(r,{prop:"duration",class:"mt-auto"},{default:p(()=>[i(o,{type:"number",controls:!1,step:"any",modelValue:l.targetData.duration,"onUpdate:modelValue":e[1]||(e[1]=s=>l.targetData.duration=s),placeholder:"",name:"duration"},null,8,["modelValue"])]),_:1})]),t("div",$h,[t("button",{type:"button",class:"bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover rounded-md px-4 py-2 font-semibold",onClick:e[2]||(e[2]=(...s)=>l.closeModal&&l.closeModal(...s)),disabled:l.loading}," Discard ",8,xh),t("button",{class:"bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover rounded-md px-4 py-2 font-semibold",type:"submit",disabled:l.loading},[l.loading?E("",!0):(w(),$("span",Dh," Save ")),l.loading?(w(),$("span",Vh,e[4]||(e[4]=[F(" Please wait... "),t("span",{class:"spinner-border spinner-border-sm align-middle ms-2"},null,-1)]))):E("",!0)],8,kh)])]),_:1},8,["onSubmit","model","rules"])])])):E("",!0)}const el=Q(bh,[["render",Ih]]),Ph=J({name:"solid-input-modal",components:{SvgIcon:K},props:{isVisible:{type:Boolean,required:!0},close:{type:Function,required:!0},loadPage:{type:Function,required:!1},zIndex:{type:Number,default:40,required:!1}},setup(l){const e=Xt(),S=v(!1),h=v({description:"",value:null,units:""}),m=v(null),n=v(!1),a=v(""),o=v("");Y(o,P=>{P!==""&&r()}),Y(()=>l.isVisible,P=>{var x;P===!1&&(o.value="",a.value="",M(),(x=m==null?void 0:m.value)==null||x.resetFields())});const r=async()=>{e.getSolidControlEquipmentInputDetails({id:o.value,callback:{onSuccess:P=>{h.value={...P}}}})},d=async P=>{n.value=!0,e.updateSolidControlEquipmentInput({id:o.value,params:P,callback:{onSuccess:x=>{l!=null&&l.loadPage&&(l==null||l.loadPage()),u()},onFinish:x=>{n.value=!1}}})},s=async P=>{n.value=!0,e.createSolidControlEquipmentInput({params:P,callback:{onSuccess:x=>{l!=null&&l.loadPage&&(l==null||l.loadPage()),u()},onFinish:x=>{n.value=!1}}})},u=()=>{l.close()},f=(P,x)=>{a.value=P,o.value=x},c=v({description:[{required:!0,message:"Please type Description",trigger:"blur"}],value:[{required:!0,message:"Please type Value",trigger:"blur"}],units:[{required:!0,message:"Please type Units",trigger:"blur"}]}),y=()=>{m.value&&m.value.validate(P=>{var x;if(P){const b={...h==null?void 0:h.value,solidControlEquipmentId:a==null?void 0:a.value,value:Number((x=h==null?void 0:h.value)==null?void 0:x.value)};o!=null&&o.value?d(b):s(b)}})},M=()=>{h.value={description:"",value:null,units:""}};return{id:o,modal:S,rules:c,loading:n,targetData:h,formRef:m,closeModal:u,submit:y,setId:f,reset:M}}}),Ch={key:0,class:"fixed top-0 right-0 bottom-0 left-0 z-40 flex items-center"},Th={class:"relative bg-card-background text-card-text-light max-h-11/12 w-11/12 flex flex-col items-center gap-3 mx-auto my-6 p-4 rounded-xl md:w-2/5"},Mh={class:"flex flex-row h-auto w-full items-center justify-between"},Fh={class:"text-lg font-bold"},Nh={class:"flex flex-col gap-1"},_h={class:"flex flex-col gap-1"},Hh={class:"flex flex-col gap-1"},Ah={class:"h-auto w-full flex flex-row items-center gap-2"},zh=["disabled"],qh=["disabled"],Bh={key:0,class:"indicator-label"},jh={key:1,class:"indicator-progress"};function Eh(l,e,S,h,m,n){const a=k("SvgIcon"),o=k("el-input"),r=k("el-form-item"),d=k("el-form");return l.isVisible?(w(),$("div",Ch,[e[9]||(e[9]=t("div",{class:"absolute inset-0 bg-sidebar-background opacity-25"},null,-1)),t("div",Th,[t("div",Mh,[t("h3",Fh,D(`${l.id?"Edit Input":"Add Input"}`),1),t("span",{class:"cursor-pointer",onClick:e[0]||(e[0]=(...s)=>l.closeModal&&l.closeModal(...s))},[i(a,{icon:"closeModalIcon"})])]),i(d,{id:"solid_duration_form",onSubmit:ie(l.submit,["prevent"]),model:l.targetData,rules:l.rules,ref:"formRef",class:"h-auto w-full flex flex-col gap-3"},{default:p(()=>[t("div",Nh,[e[5]||(e[5]=t("label",{class:"font-bold"},"Description ",-1)),i(r,{prop:"description",class:"mt-auto"},{default:p(()=>[i(o,{modelValue:l.targetData.description,"onUpdate:modelValue":e[1]||(e[1]=s=>l.targetData.description=s),placeholder:"",name:"description"},null,8,["modelValue"])]),_:1})]),t("div",_h,[e[6]||(e[6]=t("label",{class:"font-bold"},"Value ",-1)),i(r,{prop:"value",class:"mt-auto"},{default:p(()=>[i(o,{modelValue:l.targetData.value,"onUpdate:modelValue":e[2]||(e[2]=s=>l.targetData.value=s),placeholder:"",name:"value",type:"number",controls:!1,step:"any"},null,8,["modelValue"])]),_:1})]),t("div",Hh,[e[7]||(e[7]=t("label",{class:"font-bold"},"Unit ",-1)),i(r,{prop:"units",class:"mt-auto"},{default:p(()=>[i(o,{modelValue:l.targetData.units,"onUpdate:modelValue":e[3]||(e[3]=s=>l.targetData.units=s),placeholder:"",name:"units"},null,8,["modelValue"])]),_:1})]),t("div",Ah,[t("button",{type:"button",class:"bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover rounded-md px-4 py-2 font-semibold",onClick:e[4]||(e[4]=(...s)=>l.closeModal&&l.closeModal(...s)),disabled:l.loading}," Discard ",8,zh),t("button",{class:"bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover rounded-md px-4 py-2 font-semibold",type:"submit",disabled:l.loading},[l.loading?E("",!0):(w(),$("span",Bh," Save ")),l.loading?(w(),$("span",jh,e[8]||(e[8]=[F(" Please wait... "),t("span",{class:"spinner-border spinner-border-sm align-middle ms-2"},null,-1)]))):E("",!0)],8,qh)])]),_:1},8,["onSubmit","model","rules"])])])):E("",!0)}const tl=Q(Ph,[["render",Eh]]),Rh=ne("solidControlEquipmentType",()=>({getSolidControlEquipmentTypes:async({callback:e})=>{var m;const S=g.get(e,"onSuccess",g.noop),h=g.get(e,"onFinish",g.noop);try{V.setHeader();const n=await V.get("solidControlEquipmentTypes");S(((m=n.data)==null?void 0:m.data)||n.data)}catch(n){H(n,e)}finally{h()}}})),Oh=J({name:"solid-modal",components:{SvgIcon:K},props:{isVisible:{type:Boolean,required:!0},close:{type:Function,required:!0},loadPage:{type:Function,default:()=>{},required:!0},zIndex:{type:Number,default:40,required:!1}},setup(l){const e=re(),S=dt(),h=Rh(),m=v(!1),n={dailyReportId:"",typeId:null,screen:""},a=v(JSON.parse(JSON.stringify(n))),o=v([]),r=v(null),d=v(!1),s=v(""),u=le("dailyReport");Y(s,I=>{I!==""&&(c(),f())}),Y(()=>l.isVisible,I=>{var C;I===!1&&(s.value="",q(),(C=r==null?void 0:r.value)==null||C.resetFields())}),ae(()=>{f()});const f=async()=>{h.getSolidControlEquipmentTypes({callback:{onSuccess:I=>{o.value=I==null?void 0:I.map(C=>({value:C==null?void 0:C.id,key:C==null?void 0:C.name}))}}})},c=async()=>{S.getSolidControlEquipmentDetails({id:s.value,callback:{onSuccess:I=>{var C;a.value={dailyReportId:u==null?void 0:u.getDailyReportId(),typeId:(C=I==null?void 0:I.type)==null?void 0:C.id,screen:I==null?void 0:I.screen}}}})},y=async I=>{d.value=!0,S.updateSolidControlEquipment({id:s.value,params:I,callback:{onSuccess:C=>{l!=null&&l.loadPage&&(l==null||l.loadPage()),P()},onFinish:C=>{d.value=!1}}})},M=async I=>{d.value=!0,S.createSolidControlEquipment({params:I,callback:{onSuccess:C=>{l!=null&&l.loadPage&&(l==null||l.loadPage()),P()},onFinish:C=>{d.value=!1}}})},P=()=>{l.close()},x=I=>{s.value=I.toString()},b=v({typeId:[{required:!0,message:"Please select Type",trigger:"change"}],screen:[{required:!0,message:"Please type Screen",trigger:["change","blur"]}]}),T=()=>{r.value&&r.value.validate(I=>{var C;I&&(s!=null&&s.value?y({...a==null?void 0:a.value,dailyReportId:u==null?void 0:u.getDailyReportId()}):u==null||u.createDailyReport({wellId:(C=e==null?void 0:e.params)==null?void 0:C.id,callback:{onSuccess:B=>{M({...a==null?void 0:a.value,dailyReportId:u==null?void 0:u.getDailyReportId()})}}}))})},q=()=>{a.value=JSON.parse(JSON.stringify(n))};return{id:s,modal:m,rules:b,loading:d,targetData:a,formRef:r,solidTypes:o,closeModal:P,submit:T,setId:x,reset:q}}}),Lh={key:0,class:"fixed top-0 right-0 bottom-0 left-0 z-40 flex items-center"},Wh={class:"relative bg-card-background text-card-text-light max-h-11/12 w-11/12 flex flex-col items-center gap-3 mx-auto my-6 p-5 rounded-xl md:w-2/5 lg:w-2/5 lg:h-auto"},Uh={class:"flex flex-row h-auto w-full items-center justify-between"},Gh={class:"text-lg font-bold"},Yh={class:"flex flex-col gap-1"},Jh={class:"flex flex-col gap-1"},Qh={class:"font-bold"},Kh={class:"h-auto w-full flex flex-row items-center gap-2"},Xh=["disabled"],Zh=["disabled"],e1={key:0,class:"indicator-label"},t1={key:1,class:"indicator-progress"};function l1(l,e,S,h,m,n){const a=k("SvgIcon"),o=k("el-option"),r=k("el-select"),d=k("el-form-item"),s=k("el-popover"),u=k("el-input"),f=k("el-form");return l.isVisible?(w(),$("div",Lh,[e[10]||(e[10]=t("div",{class:"absolute inset-0 bg-sidebar-background opacity-25"},null,-1)),t("div",Wh,[t("div",Uh,[t("h3",Gh,D(`${l.id?"Edit Solids Control Equipment":"Add Solids Control Equipment"}`),1),t("span",{class:"cursor-pointer",onClick:e[0]||(e[0]=(...c)=>l.closeModal&&l.closeModal(...c))},[i(a,{icon:"closeModalIcon"})])]),i(f,{id:"solid_form",onSubmit:ie(l.submit,["prevent"]),model:l.targetData,rules:l.rules,ref:"formRef",class:"h-auto w-full flex flex-col gap-3"},{default:p(()=>[t("div",Yh,[e[4]||(e[4]=t("label",{class:"font-bold"},[F("Type"),t("span",{class:"text-danger-active font-light"},"*")],-1)),i(d,{prop:"typeId"},{default:p(()=>[i(r,{modelValue:l.targetData.typeId,"onUpdate:modelValue":e[1]||(e[1]=c=>l.targetData.typeId=c),placeholder:"",clearable:""},{default:p(()=>[(w(!0),$(O,null,Z(l.solidTypes,c=>(w(),te(o,{key:c.value,label:c.key,value:c.value,name:"typeId"},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),t("div",Jh,[t("label",Qh,[e[7]||(e[7]=F("Screen")),e[8]||(e[8]=t("span",{class:"text-danger-active font-light"},"*",-1)),i(s,{placement:"top",width:350,trigger:"hover"},{reference:p(()=>e[5]||(e[5]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7"},null,-1)])),default:p(()=>[e[6]||(e[6]=t("span",null,`The "Screen" typically refers to the mesh size and configuration of the screens used in the equipment. The screen mesh size defines the size of particles that can pass through the screen, while the number of layers may indicate the screen's efficiency in capturing solids. (i.e. 140 x 4)`,-1))]),_:1})]),i(d,{prop:"screen"},{default:p(()=>[i(u,{modelValue:l.targetData.screen,"onUpdate:modelValue":e[2]||(e[2]=c=>l.targetData.screen=c),placeholder:"",name:"screen"},null,8,["modelValue"])]),_:1})]),t("div",Kh,[t("button",{type:"button",class:"bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover rounded-md px-4 py-2 font-semibold",onClick:e[3]||(e[3]=(...c)=>l.closeModal&&l.closeModal(...c)),disabled:l.loading}," Discard ",8,Xh),t("button",{class:"bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover rounded-md px-4 py-2 font-semibold",type:"submit",disabled:l.loading},[l.loading?E("",!0):(w(),$("span",e1," Save ")),l.loading?(w(),$("span",t1,e[9]||(e[9]=[F(" Please wait... "),t("span",{class:"spinner-border spinner-border-sm align-middle ms-2"},null,-1)]))):E("",!0)],8,Zh)])]),_:1},8,["onSubmit","model","rules"])])])):E("",!0)}const ll=Q(Oh,[["render",l1]]),o1=J({name:"solid-details-modal",components:{SvgIcon:K,SolidModal:ll,SolidDurationModal:el,SolidInputModal:tl},props:{isVisible:{type:Boolean,required:!0},close:{type:Function,required:!0},loadPage:{type:Function,required:!1},zIndex:{type:Number,default:40,required:!1}},setup(l){const e=Zt(),S=Xt(),h=dt(),m=v(!1),n=v(null),a=v(!1),o=v(""),r=v(null),d=v(null),s=v(null),u=v(!1),f=v(!1),c=v(!1);Y(o,_=>{_!==""&&y()}),Y(()=>l.isVisible,_=>{_===!0&&y()});const y=async()=>{a.value=!0,h.getSolidControlEquipmentDetails({id:o.value,callback:{onSuccess:_=>{n.value={..._}},onFinish:_=>{a.value=!1}}})},M=()=>{o.value="",n.value=null,l.close()},P=()=>{y(),l!=null&&l.loadPage&&l.loadPage()},x=_=>{o.value=_.toString()},b=_=>{var N;(N=d==null?void 0:d.value)==null||N.setId(o.value,_||""),f.value=!f.value},T=_=>{var N;_&&((N=s==null?void 0:s.value)==null||N.setId(o==null?void 0:o.value,_)),c.value=!c.value},q=_=>{var N;_&&((N=r==null?void 0:r.value)==null||N.setId(_)),u.value=!u.value},I=_=>{se.deletionAlert({onConfirmed:()=>{C(_)}})},C=_=>{S.deleteSolidControlEquipmentInput({id:_,callback:{onSuccess:N=>{y(),l!=null&&l.loadPage&&l.loadPage()}}})},B=(_,N)=>{_.stopPropagation(),se.deletionAlert({onConfirmed:()=>{A(N)}})},A=_=>{e.deleteSolidControlEquipmentTime({id:_,callback:{onSuccess:N=>{y(),l!=null&&l.loadPage&&l.loadPage()}}})};return{id:o,modal:m,loading:a,data:n,solidModal:r,solidInputModal:s,solidDurationModal:d,isSolidModalVisible:u,isSolidDurationModalVisible:f,isSolidInputModalVisible:c,closeModal:M,setId:x,reloadPage:P,deleteInput:I,deleteDuration:B,formatDate:ke,numberWithCommas:fe,toggleSolidModal:q,toggleSolidDurationModal:b,toggleSolidInputModal:T}}}),s1={key:0,class:"fixed top-0 right-0 bottom-0 left-0 z-40 flex items-center"},n1={class:"relative bg-card-background text-card-text-light max-h-11/12 w-11/12 flex flex-col items-center gap-3 mx-auto my-6 p-4 rounded-xl overflow-y-scroll md:overflow-hidden md:w-3/5 lg:w-2/5 lg:h-auto"},a1={class:"flex flex-row h-auto w-full items-center justify-between"},i1={key:0,class:"text-center"},r1={key:1,class:"h-auto w-full flex flex-col gap-3 font-semibold"},d1={key:0,class:"flex flex-col gap-3"},u1={class:"flex justify-between items-center"},p1={class:"flex items-center gap-3"},c1=["onClick"],f1=["onClick"],m1={class:"text-danger"},g1={class:"flex items-center justify-between gap-4"},b1={key:2,class:"overflow-hidden-y flex flex-col"},h1={class:"flex items-center justify-between gap-3"},v1=["onClick"],y1=["onClick"],w1={class:"text-danger"},S1={class:"h-auto w-full flex items-center justify-between text-sm"},$1=["disabled"],x1=["disabled"],k1=["disabled"];function D1(l,e,S,h,m,n){var u,f,c,y,M,P,x,b,T,q;const a=k("SvgIcon"),o=k("el-empty"),r=k("SolidModal"),d=k("SolidDurationModal"),s=k("SolidInputModal");return l.isVisible?(w(),$("div",s1,[e[8]||(e[8]=t("div",{class:"absolute inset-0 bg-sidebar-background opacity-25"},null,-1)),t("div",n1,[t("div",a1,[e[4]||(e[4]=t("h3",{class:"text-lg font-bold"},"Solid Control Details",-1)),t("span",{class:"cursor-pointer",onClick:e[0]||(e[0]=(...I)=>l.closeModal&&l.closeModal(...I))},[i(a,{icon:"closeModalIcon"})])]),l.loading?(w(),$("div",i1,e[5]||(e[5]=[t("div",{class:"spinner-border text-primary",role:"status"},[t("span",{class:"sr-only"},"Loading...")],-1)]))):(w(),$("div",r1,[t("h3",null,D(`${((f=(u=l.data)==null?void 0:u.type)==null?void 0:f.name)||""} ${((c=l.data)==null?void 0:c.screen)||""}`),1),((M=(y=l.data)==null?void 0:y.inputs)==null?void 0:M.length)>0?(w(),$("div",d1,[(w(!0),$(O,null,Z((P=l.data)==null?void 0:P.inputs,I=>(w(),$("div",{key:I==null?void 0:I.id,class:"flex justify-between items-center"},[t("div",null,[t("span",null,D(`${I==null?void 0:I.description} : `),1)]),t("div",u1,[t("span",null,D(`${I==null?void 0:I.value} ${I==null?void 0:I.units}`),1)]),t("div",p1,[t("button",{class:"p-2 rounded-full bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover",onClick:C=>l.toggleSolidInputModal((I==null?void 0:I.id)||"")},[i(a,{icon:"newReportIcon"})],8,c1),t("button",{class:"p-2 rounded-full bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover",onClick:C=>l.deleteInput((I==null?void 0:I.id)||"")},[t("span",m1,[i(a,{icon:"trashIcon"})])],8,f1)])]))),128))])):E("",!0),t("div",g1,[e[6]||(e[6]=t("span",null,"Total Duration",-1)),t("span",null,D(`${((x=l.data)==null?void 0:x.totalDurations)||"0"} hrs`),1)]),e[7]||(e[7]=t("p",null,"Time Distribution",-1)),((T=(b=l.data)==null?void 0:b.durations)==null?void 0:T.length)===0?(w(),te(o,{key:1,"image-size":100})):(w(),$("div",b1,[(w(!0),$(O,null,Z((q=l.data)==null?void 0:q.durations,I=>(w(),$("div",{key:I.id,class:"h-auto w-full flex justify-between items-center p-1"},[t("span",null,D(l.formatDate(I==null?void 0:I.createdAt,"DD MMM YYYY HH:mm")),1),t("span",null,D(`${I==null?void 0:I.duration} hrs`),1),t("div",h1,[t("button",{class:"p-2 rounded-full bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover",onClick:()=>l.toggleSolidDurationModal(I==null?void 0:I.id)},[i(a,{icon:"newReportIcon"})],8,v1),t("button",{class:"p-2 rounded-full bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover",onClick:C=>l.deleteDuration(C,(I==null?void 0:I.id)||"")},[t("span",w1,[i(a,{icon:"trashIcon"})])],8,y1)])]))),128))]))])),t("div",S1,[t("button",{type:"button",class:"bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover rounded-md px-3 py-2 font-semibold",disabled:l.loading,onClick:e[1]||(e[1]=(...I)=>l.closeModal&&l.closeModal(...I))}," Close ",8,$1),t("button",{class:"bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover rounded-md px-1 py-2 font-semibold",type:"button",disabled:l.loading,onClick:e[2]||(e[2]=I=>{var C;return l.toggleSolidModal(((C=l.data)==null?void 0:C.id)||"")})}," Edit Solid Equipment ",8,x1),t("button",{class:"bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover rounded-md px-2 py-2 font-semibold",type:"button",disabled:l.loading,onClick:e[3]||(e[3]=()=>l.toggleSolidDurationModal())}," Add Duration ",8,k1)])]),i(r,{isVisible:l.isSolidModalVisible,close:l.toggleSolidModal,ref:"solidModal",loadPage:l.reloadPage},null,8,["isVisible","close","loadPage"]),i(d,{isVisible:l.isSolidDurationModalVisible,close:l.toggleSolidDurationModal,ref:"solidDurationModal",loadPage:l.reloadPage},null,8,["isVisible","close","loadPage"]),i(s,{isVisible:l.isSolidInputModalVisible,close:l.toggleSolidInputModal,ref:"solidInputModal",loadPage:l.reloadPage},null,8,["isVisible","close","loadPage"])])):E("",!0)}const V1=Q(o1,[["render",D1]]),I1=J({name:"site-equipment",components:{SvgIcon:K,SolidModal:ll,SolidDetailsModal:V1,SolidDurationModal:el,SolidInputModal:tl,NoEntries:me},props:{loadPage:{type:Function,required:!0}},setup(l){const e=dt(),S=v(null),h=v(null),m=v(null),n=v(null),a=v(!1),o=v([]),r=le("dailyReport"),d=v(!1),s=v(!1),u=v(!1),f=v(!1);ae(()=>{r!=null&&r.getDailyReportId()&&y()});const c=()=>{y(),l!=null&&l.loadPage&&(l==null||l.loadPage())},y=async(I={dailyReportId:r==null?void 0:r.getDailyReportId(),page:1,limit:200})=>{a.value=!0,e.getSolidControlEquipments({params:I,callback:{onSuccess:C=>{o.value=C==null?void 0:C.items},onFinish:C=>{a.value=!1}}})},M=I=>{var C;I&&((C=S==null?void 0:S.value)==null||C.setId(I)),d.value=!d.value},P=I=>{var C;I&&((C=n==null?void 0:n.value)==null||C.setId(I,"")),s.value=!s.value},x=(I,C="")=>{var B;I&&((B=m==null?void 0:m.value)==null||B.setId(I,C)),u.value=!u.value},b=I=>{var C;I&&((C=h==null?void 0:h.value)==null||C.setId(I)),f.value=!f.value},T=I=>{se.deletionAlert({onConfirmed:()=>{q(I)}})},q=async I=>{a.value=!0,e.deleteSolidControlEquipment({id:I,callback:{onSuccess:C=>{c()},onFinish:C=>{a.value=!1}}})};return{loading:a,solidModal:S,solidInputModal:n,solidDetailsModal:h,solidDurationModal:m,solidControlEquipmentList:o,isSolidModalVisible:d,isSolidInputModalVisible:s,isSolidDurationModalVisible:u,isSolidDetailsModalVisible:f,loadData:c,deleteSolid:T,toggleSolidModal:M,toggleSolidInputModal:P,toggleSolidDetailsModal:b,toggleSolidDurationModal:x}}}),P1={class:"h-auto w-full mt-4 p-4 bg-minicard-background text-minicard-text-light rounded-lg border-[1px] border-dashed font-semibold"},C1={key:0,class:"text-center"},T1={key:1,class:"grid grid-cols-1 gap-4 md:grid-cols-2 md:gap-7 lg:grid-cols-3"},M1={class:"flex flex-col gap-3 p-4"},F1=["onClick"],N1={class:"flex flex-col gap-3"},_1={class:"flex flex-col items-center justify-between border-b-[1px] border-dashed pb-3"},H1={key:0,class:"h-auto w-full flex items-center justify-between"},A1={class:"ms-auto"},z1={key:1,class:"h-auto w-full flex items-center justify-between"},q1={key:2,class:"italic cursor-pointer"},B1={class:"h-auto w-full flex items-center justify-between"},j1={class:"flex flex-row items-center gap-2 absolute top-0 end-0 -translate-y-1/2"},E1=["onClick"],R1=["onClick"],O1=["onClick"],L1=["onClick"],W1={class:"text-danger"};function U1(l,e,S,h,m,n){const a=k("NoEntries"),o=k("SvgIcon"),r=k("el-tooltip"),d=k("SolidModal"),s=k("SolidInputModal"),u=k("SolidDurationModal"),f=k("SolidDetailsModal");return w(),$(O,null,[t("div",P1,[e[2]||(e[2]=t("h4",{class:"font-bold mb-4"},"Solid Control Equipment",-1)),l.loading?(w(),$("div",C1,e[0]||(e[0]=[t("div",{class:"spinner-border text-primary",role:"status"},[t("span",{class:"sr-only"},"Loading...")],-1)]))):(w(),$(O,{key:1},[l.solidControlEquipmentList.length===0?(w(),te(a,{key:0,addNew:()=>l.toggleSolidModal()},null,8,["addNew"])):(w(),$("div",T1,[(w(!0),$(O,null,Z(l.solidControlEquipmentList,c=>{var y;return w(),$("div",{key:c==null?void 0:c.id,class:"rounded-lg border-[1px] border-dashed relative cursor-pointer my-6 first:mt-0 last: mb-0 md:first:mt-0 md:last:mb-0 md:m-0"},[t("div",M1,[t("h5",{class:"font-bold underline underline-offset-2",onClick:M=>l.toggleSolidDetailsModal((c==null?void 0:c.id.toString())||"")},D(`${(y=c==null?void 0:c.type)==null?void 0:y.name} ${c==null?void 0:c.screen}`),9,F1),t("div",N1,[t("div",_1,[c!=null&&c.inputs[0]?(w(),$("div",H1,[t("span",null,D(c==null?void 0:c.inputs[0].description),1),t("span",A1,D(`${c==null?void 0:c.inputs[0].value} ${c==null?void 0:c.inputs[0].units}`),1)])):E("",!0),c!=null&&c.inputs[1]?(w(),$("div",z1,[t("span",null,D(c==null?void 0:c.inputs[1].description),1),t("span",null,D(`${c==null?void 0:c.inputs[1].value} ${c==null?void 0:c.inputs[1].units}`),1)])):E("",!0),(c==null?void 0:c.inputs.length)>2?(w(),$("div",q1," More... ")):E("",!0)]),t("div",B1,[e[1]||(e[1]=t("span",null,"Total Duration",-1)),t("span",null,D(`${(c==null?void 0:c.totalDurations)||"0"} hrs`),1)])])]),t("div",j1,[i(r,{content:"Add Input",placement:"top",effect:"customize"},{default:p(()=>[t("button",{class:"p-2 rounded-full bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover",onClick:M=>l.toggleSolidInputModal((c==null?void 0:c.id)||"")},[i(o,{icon:"addIcon"})],8,E1)]),_:2},1024),i(r,{content:"Add Duration",placement:"top",effect:"customize"},{default:p(()=>[t("button",{class:"p-2 rounded-full bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover",onClick:M=>l.toggleSolidDurationModal((c==null?void 0:c.id)||"")},[i(o,{icon:"durationIcon"})],8,R1)]),_:2},1024),i(r,{content:"Edit",placement:"top",effect:"customize"},{default:p(()=>[t("button",{class:"p-2 rounded-full bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover",onClick:M=>l.toggleSolidModal((c==null?void 0:c.id.toString())||"")},[i(o,{icon:"pencilIcon"})],8,O1)]),_:2},1024),i(r,{content:"Delete",placement:"top",effect:"customize"},{default:p(()=>[t("button",{class:"p-2 rounded-full bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover",onClick:M=>l.deleteSolid((c==null?void 0:c.id.toString())||"")},[t("span",W1,[i(o,{icon:"trashIcon"})])],8,L1)]),_:2},1024)])])}),128))]))],64))]),i(d,{isVisible:l.isSolidModalVisible,close:l.toggleSolidModal,ref:"solidModal",loadPage:l.loadData,zIndex:40},null,8,["isVisible","close","loadPage"]),i(s,{isVisible:l.isSolidInputModalVisible,close:l.toggleSolidInputModal,ref:"solidInputModal",loadPage:l.loadData,zIndex:40},null,8,["isVisible","close","loadPage"]),i(u,{isVisible:l.isSolidDurationModalVisible,close:l.toggleSolidDurationModal,ref:"solidDurationModal",loadPage:l.loadData,zIndex:40},null,8,["isVisible","close","loadPage"]),i(f,{isVisible:l.isSolidDetailsModalVisible,close:l.toggleSolidDetailsModal,ref:"solidDetailsModal",loadPage:l.loadData,zIndex:40},null,8,["isVisible","close","loadPage"])],64)}const G1=Q(I1,[["render",U1]]),Y1=J({name:"site-equipment",components:{SvgIcon:K,Pump:gh,Solid:G1,BottomTool:ce},props:{setChildActiveTab:{type:Function,required:!1,default:()=>{}}},setup(l){const e=nt(),S=v(null),h=v(null),m=v(!1),n=v([]),a=le("dailyReport");ae(()=>{o()});const o=async()=>{a!=null&&a.getDailyReportId()&&(m.value=!0,e.getPumpSummary({dailyReportId:a==null?void 0:a.getDailyReportId(),callback:{onSuccess:s=>{n.value=s},onFinish:s=>{m.value=!1}}}))};return{loading:m,summaryData:n,pumpRef:S,solidRef:h,toggleAddPumpModal:()=>{var s;(s=S==null?void 0:S.value)==null||s.togglePumpModal()},toggleAddSolidModal:()=>{var s;(s=h==null?void 0:h.value)==null||s.toggleSolidModal()},getPumpSummary:o}}}),J1={class:"site-equipment"},Q1={class:"bg-card-background text-card-text-light w-11/12 mx-auto mb-4 h-auto rounded-xl"},K1={class:"h-auto w-11/12 flex flex-col gap-6 mt-7 mx-auto px-3 py-4"},X1={class:"h-auto w-full p-4 bg-minicard-background text-minicard-text-light rounded-lg border-[1px] border-dashed font-semibold"},Z1={key:0,class:"text-center"},ev={key:1,class:"flex flex-col gap-3 md:flex-row md:gap-4"},tv={class:"h-auto w-full p-4 rounded-lg border-[1px] border-active border-dashed flex flex-col gap-3"},lv={class:"h-auto w-full p-4 rounded-lg border-[1px] border-active border-dashed flex flex-col gap-3"},ov={class:"flex flex-wrap items-center"},sv={class:"bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover hover:border-[2px] hover:border-button-hover-outline rounded-full p-4"};function nv(l,e,S,h,m,n){var y,M;const a=k("el-popover"),o=k("Pump"),r=k("Solid"),d=k("SvgIcon"),s=k("el-dropdown-item"),u=k("el-dropdown-menu"),f=k("el-dropdown"),c=k("BottomTool");return w(),$(O,null,[t("div",J1,[t("div",Q1,[t("div",K1,[e[10]||(e[10]=t("h1",{class:"font-bold mb-4"},"Site Equipment",-1)),t("div",X1,[e[9]||(e[9]=t("h4",{class:"mb-4"},"Detail",-1)),l.loading?(w(),$("div",Z1,e[0]||(e[0]=[t("div",{class:"spinner-border text-primary",role:"status"},[t("span",{class:"sr-only"},"Loading...")],-1)]))):(w(),$("div",ev,[t("div",tv,[t("h4",null,[e[3]||(e[3]=F(" Total Rate")),i(a,{placement:"top",width:300,trigger:"hover"},{reference:p(()=>e[1]||(e[1]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7 me-4"},null,-1)])),default:p(()=>[e[2]||(e[2]=t("span",null," The total flow rate of fluid delivered by all active pumps combined. ",-1))]),_:1})]),e[4]||(e[4]=t("p",{class:"text-sm"}," Total Rate (gpm) = Sum(Rate (gpm) of all active pumps) ",-1)),t("h3",null,D(`${((y=l.summaryData)==null?void 0:y.totalRate)||"0"} (gmp)`),1)]),t("div",lv,[t("h4",null,[e[7]||(e[7]=F(" Pump pressure")),i(a,{placement:"top",width:200,trigger:"hover"},{reference:p(()=>e[5]||(e[5]=[t("i",{class:"fas fa-exclamation-circle ms-2 fs-7 me-4"},null,-1)])),default:p(()=>[e[6]||(e[6]=t("span",null," The pressure generated by the pumps. ",-1))]),_:1})]),e[8]||(e[8]=t("p",{class:"text-sm"}," Pump Pressure (psi) = (Displacement (bbl/stroke) * Rate (gpm) * 8.33) / (Efficiency (%) * 2 * Stroke (stroke/min)) ",-1)),t("h3",null,D(`${((M=l.summaryData)==null?void 0:M.pumpPressure)||"0"} (psi)`),1)])]))]),i(o,{ref:"pumpRef",loadPage:l.getPumpSummary},null,8,["loadPage"]),i(r,{ref:"solidRef",loadPage:l.getPumpSummary},null,8,["loadPage"])])])]),i(c,{showHelpInfo:!1},{header:p(()=>[t("div",ov,[i(f,{placement:"top"},{dropdown:p(()=>[i(u,{class:"p-4"},{default:p(()=>[i(s,{class:"customize-dropdown-item mb-3",onClick:l.toggleAddPumpModal},{default:p(()=>e[11]||(e[11]=[F("Add Pump")])),_:1},8,["onClick"]),i(s,{class:"customize-dropdown-item",onClick:l.toggleAddSolidModal},{default:p(()=>e[12]||(e[12]=[F("Add Solids Control")])),_:1},8,["onClick"])]),_:1})]),default:p(()=>[t("button",sv,[i(d,{icon:"addIcon"})])]),_:1})])]),_:1})],64)}const ol=Q(Y1,[["render",nv]]),sl=ne("task",()=>({getTasks:async({params:n,callback:a})=>{var d;const o=g.get(a,"onSuccess",g.noop),r=g.get(a,"onFinish",g.noop);try{V.setHeader();const s=await V.getWithParams("tasks",n);o(((d=s.data)==null?void 0:d.data)||s.data)}catch(s){H(s,a)}finally{r()}},getTaskDetails:async({id:n,callback:a})=>{var d;const o=g.get(a,"onSuccess",g.noop),r=g.get(a,"onFinish",g.noop);try{V.setHeader();const s=await V.get(`tasks/${n}`);o(((d=s.data)==null?void 0:d.data)||s.data)}catch(s){H(s,a)}finally{r()}},updateTask:async({id:n,params:a,callback:o})=>{var s;const r=g.get(o,"onSuccess",g.noop),d=g.get(o,"onFinish",g.noop);try{V.setHeader();const u=await V.put(`tasks/${n}`,a);r(((s=u.data)==null?void 0:s.data)||u.data)}catch(u){H(u,o)}finally{d()}},deleteTask:async({id:n,callback:a})=>{var d;const o=g.get(a,"onSuccess",g.noop),r=g.get(a,"onFinish",g.noop);try{V.setHeader();const s=await V.delete(`tasks/${n}`);o(((d=s.data)==null?void 0:d.data)||s.data)}catch(s){H(s,a)}finally{r()}},createTask:async({params:n,callback:a})=>{var d;const o=g.get(a,"onSuccess",g.noop),r=g.get(a,"onFinish",g.noop);try{V.setHeader();const s=await V.post("tasks",n);o(((d=s.data)==null?void 0:d.data)||s.data)}catch(s){H(s,a)}finally{r()}}})),av=J({name:"task-modal",components:{SvgIcon:K},props:{isVisible:{type:Boolean,required:!0},close:{type:Function,required:!0},loadPage:{type:Function,default:()=>{},required:!0}},setup(l){const e=re(),S=sl(),h=v(!1),m={description:"",durations:null},n=v(JSON.parse(JSON.stringify(m))),a=v(null),o=v(!1),r=v(""),d=le("dailyReport");Y(r,b=>{b!==""&&s()}),Y(()=>l.isVisible,b=>{var T;b===!1&&(r.value="",P(),(T=a==null?void 0:a.value)==null||T.resetFields())});const s=async()=>{S.getTaskDetails({id:r.value,callback:{onSuccess:b=>{n.value={...b}}}})},u=async b=>{o.value=!0,S.updateTask({id:r.value,params:b,callback:{onSuccess:T=>{l!=null&&l.loadPage&&(l==null||l.loadPage()),f()},onFinish:T=>{o.value=!1}}})},f=()=>{l.close()},c=v({description:[{required:!0,message:"Please type Description",trigger:"blur"}],durations:[{required:!0,message:"Please type Duration",trigger:"blur"}]}),y=()=>{a.value&&a.value.validate(b=>{var T,q,I;if(b){const C={description:(T=n==null?void 0:n.value)==null?void 0:T.description,durations:Number((q=n==null?void 0:n.value)==null?void 0:q.durations)};r!=null&&r.value?u({...C,dailyReportId:d==null?void 0:d.getDailyReportId()}):d==null||d.createDailyReport({wellId:(I=e==null?void 0:e.params)==null?void 0:I.id,callback:{onSuccess:B=>{M({...C,dailyReportId:d==null?void 0:d.getDailyReportId()})}}})}})},M=async b=>{o.value=!0,S.createTask({params:b,callback:{onSuccess:T=>{l!=null&&l.loadPage&&(l==null||l.loadPage()),f()},onFinish:T=>{o.value=!1}}})},P=()=>{n.value=JSON.parse(JSON.stringify(m))};return{id:r,modal:h,rules:c,loading:o,targetData:n,formRef:a,closeModal:f,submit:y,reset:P,setId:b=>{r.value=b.toString()}}}}),iv={key:0,class:"fixed top-0 right-0 bottom-0 left-0 z-40 flex items-center"},rv={class:"relative bg-card-background text-card-text-light max-h-11/12 w-11/12 flex flex-col items-center gap-3 mx-auto my-6 p-5 rounded-xl md:w-3/5 lg:w-2/5 lg:h-auto"},dv={class:"flex flex-row h-auto w-full items-center justify-between"},uv={class:"text-lg font-bold"},pv={class:"flex flex-col gap-1"},cv={class:"flex flex-col gap-1"},fv={class:"h-auto w-full flex flex-row items-center gap-2"},mv=["disabled"],gv=["disabled"],bv={key:0,class:"indicator-label"},hv={key:1,class:"indicator-progress"};function vv(l,e,S,h,m,n){const a=k("SvgIcon"),o=k("el-input"),r=k("el-form-item"),d=k("el-form");return l.isVisible?(w(),$("div",iv,[e[7]||(e[7]=t("div",{class:"absolute inset-0 bg-sidebar-background opacity-25"},null,-1)),t("div",rv,[t("div",dv,[t("h3",uv,D(`${l.id?"Edit Task":"Add Task"}`),1),t("span",{class:"cursor-pointer",onClick:e[0]||(e[0]=(...s)=>l.closeModal&&l.closeModal(...s))},[i(a,{icon:"closeModalIcon"})])]),i(d,{id:"add_task_form",onSubmit:ie(l.submit,["prevent"]),model:l.targetData,rules:l.rules,ref:"formRef",class:"h-auto w-full flex flex-col gap-3"},{default:p(()=>[t("div",pv,[e[4]||(e[4]=t("label",{class:"font-bold"},[F("Description"),t("span",{class:"text-danger-active font-light"},"*")],-1)),i(r,{prop:"description",class:"mt-auto"},{default:p(()=>[i(o,{modelValue:l.targetData.description,"onUpdate:modelValue":e[1]||(e[1]=s=>l.targetData.description=s),placeholder:"",name:"description"},null,8,["modelValue"])]),_:1})]),t("div",cv,[e[5]||(e[5]=t("label",{class:"font-bold"},[F("Duration (hr)"),t("span",{class:"text-danger-active font-light"},"*")],-1)),i(r,{prop:"durations",class:"mt-auto"},{default:p(()=>[i(o,{type:"number",controls:!1,step:"any",modelValue:l.targetData.durations,"onUpdate:modelValue":e[2]||(e[2]=s=>l.targetData.durations=s),placeholder:"",name:"durations"},null,8,["modelValue"])]),_:1})]),t("div",fv,[t("button",{type:"button",class:"bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover rounded-md px-4 py-2 font-semibold",onClick:e[3]||(e[3]=(...s)=>l.closeModal&&l.closeModal(...s)),disabled:l.loading}," Discard ",8,mv),t("button",{class:"bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover rounded-md px-4 py-2 font-semibold",type:"submit",disabled:l.loading},[l.loading?E("",!0):(w(),$("span",bv," Save ")),l.loading?(w(),$("span",hv,e[6]||(e[6]=[F(" Please wait... "),t("span",{class:"spinner-border spinner-border-sm align-middle ms-2"},null,-1)]))):E("",!0)],8,gv)])]),_:1},8,["onSubmit","model","rules"])])])):E("",!0)}const yv=Q(av,[["render",vv]]),wv=J({name:"tasks",components:{SvgIcon:K,BottomTool:ce,TaskModal:yv,NoEntries:me},props:{setChildActiveTab:{type:Function,required:!1,default:()=>{}}},setup(l){const e=v(!1),S=sl(),h=v([]),m=v(null),n=le("dailyReport"),a=v(!1);ae(()=>{n!=null&&n.getDailyReportId()&&o()});const o=async(f={dailyReportId:n==null?void 0:n.getDailyReportId(),page:1,limit:200})=>{e.value=!0,S.getTasks({params:f,callback:{onSuccess:c=>{h.value=c==null?void 0:c.items},onFinish:c=>{e.value=!1}}})},r=()=>{a.value=!a.value},d=f=>{var c;(c=m==null?void 0:m.value)==null||c.setId(f),a.value=!a.value},s=(f,c)=>{f.stopPropagation(),se.deletionAlert({onConfirmed:()=>{u(c)}})},u=async f=>{e.value=!0,S.deleteTask({id:f,callback:{onSuccess:c=>{o()},onFinish:c=>{e.value=!1}}})};return{loading:e,taskList:h,taskModal:m,isModalVisible:a,getTasks:o,deleteTask:s,toggleAddTaskModal:r,toggleEditDurationModal:d}}}),Sv={class:"bg-card-background text-card-text-light w-11/12 mx-auto mb-4 h-auto rounded-xl"},$v={class:"h-auto w-11/12 mx-auto mt-7 px-3 py-4"},xv={class:"card-body"},kv={key:0,class:"text-center"},Dv={key:1,class:"h-auto w-full grid grid-cols-1 gap-4 md:grid-cols-2 md:gap-7 lg:grid-cols-3"},Vv={class:"h-auto w-full p-4 flex flex-row items-center gap-2"},Iv={class:"h-auto w-full flex flex-col gap-2"},Pv={class:"font-bold"},Cv={class:"font-semibold text-sm"},Tv={class:"flex items-center gap-2"},Mv=["onClick"],Fv=["onClick"],Nv={class:"text-danger"};function _v(l,e,S,h,m,n){const a=k("NoEntries"),o=k("SvgIcon"),r=k("el-tooltip"),d=k("TaskModal"),s=k("BottomTool");return w(),$(O,null,[t("div",Sv,[t("div",$v,[e[2]||(e[2]=t("h1",{class:"font-bold mb-4"},"Tasks",-1)),t("div",xv,[l.loading?(w(),$("div",kv,e[0]||(e[0]=[t("div",{class:"spinner-border text-primary",role:"status"},[t("span",{class:"sr-only"},"Loading...")],-1)]))):(w(),$(O,{key:1},[l.taskList.length===0?(w(),te(a,{key:0,addNew:l.toggleAddTaskModal},null,8,["addNew"])):(w(),$("div",Dv,[(w(!0),$(O,null,Z(l.taskList,u=>(w(),$("div",{key:u==null?void 0:u.id,class:"relative h-auto w-full bg-minicard-background text-minicard-text-light p-4 rounded-xl border-t-2 border-active my-2 first:mt-4 last:mb-5 font-semibold md:first:mt-0 md:last:mb-0 md:m-0"},[t("div",Vv,[e[1]||(e[1]=t("span",{class:"bg-success rounded-lg h-12 w-2"},null,-1)),t("div",Iv,[t("h4",Pv,D(u==null?void 0:u.description),1),t("span",Cv,D(`${u==null?void 0:u.durations} hours`),1)]),t("div",Tv,[i(r,{content:"Edit Task",placement:"top",effect:"customize"},{default:p(()=>[t("button",{class:"p-2 rounded-full bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover",onClick:f=>l.toggleEditDurationModal(u==null?void 0:u.id)},[i(o,{icon:"pencilIcon"})],8,Mv)]),_:2},1024),i(r,{content:"Delete",placement:"top",effect:"customize"},{default:p(()=>[t("button",{class:"p-2 rounded-full bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover",onClick:f=>l.deleteTask(f,u==null?void 0:u.id)},[t("span",Nv,[i(o,{icon:"trashIcon"})])],8,Fv)]),_:2},1024)])])]))),128))]))],64))])])]),i(d,{isVisible:l.isModalVisible,close:l.toggleAddTaskModal,ref:"taskModal",loadPage:l.getTasks},null,8,["isVisible","close","loadPage"]),i(s,{addNew:l.toggleAddTaskModal,showHelpInfo:!1},null,8,["addNew"])],64)}const nl=Q(wv,[["render",_v]]),al=ne("volumeTrackingItem",()=>({getVolumeTrackingItemDetails:async({id:m,callback:n})=>{var r;const a=g.get(n,"onSuccess",g.noop),o=g.get(n,"onFinish",g.noop);try{V.setHeader();const d=await V.get(`volumeTrackingItems/${m}`);a(((r=d.data)==null?void 0:r.data)||d.data)}catch(d){H(d,n)}finally{o()}},updateVolumeTrackingItem:async({id:m,params:n,callback:a})=>{var d;const o=g.get(a,"onSuccess",g.noop),r=g.get(a,"onFinish",g.noop);try{V.setHeader();const s=await V.put(`volumeTrackingItems/${m}`,n);o(((d=s.data)==null?void 0:d.data)||s.data)}catch(s){H(s,a)}finally{r()}},deleteVolumeTrackingItem:async({id:m,callback:n})=>{var r;const a=g.get(n,"onSuccess",g.noop),o=g.get(n,"onFinish",g.noop);try{V.setHeader();const d=await V.delete(`volumeTrackingItems/${m}`);a(((r=d.data)==null?void 0:r.data)||d.data)}catch(d){H(d,n)}finally{o()}},createVolumeTrackingItem:async({params:m,callback:n})=>{var r;const a=g.get(n,"onSuccess",g.noop),o=g.get(n,"onFinish",g.noop);try{V.setHeader();const d=await V.post("volumeTrackingItems",m);a(((r=d.data)==null?void 0:r.data)||d.data)}catch(d){H(d,n)}finally{o()}}})),Hv=J({name:"add-volume-modal",components:{SvgIcon:K,FormModal:Gt},props:{isVisible:{type:Boolean,required:!0},close:{type:Function,required:!0},loadPage:{type:Function,required:!1},productAndPackageInventoryId:{type:String,default:""}},setup(l){const e=al(),S=rt(),h=v(!1),m={description:"",volume:null,type:null,productId:null},n=v(JSON.parse(JSON.stringify(m))),a=v(null),o=v(!1),r=v(null),d=v(""),s=v(""),u=v(null),f=v([]),c=v(!1),y=v(!1);Y(s,j=>{j!=""&&x()}),Y(()=>l==null?void 0:l.productAndPackageInventoryId,j=>{j!==""&&P(j)}),Y(()=>l.isVisible,j=>{var z;j===!1&&(d.value="",s.value="",r.value=null,_(),(z=a==null?void 0:a.value)==null||z.resetFields())});const M=()=>{var j;(j=u==null?void 0:u.value)==null||j.setType(ue.initial),y.value=!y.value},P=async j=>{c.value=!0,S.getProductPackageReports({params:{productAndPackageInventoryId:j,page:1,limit:200},callback:{onSuccess:z=>{f.value=z==null?void 0:z.items.map(R=>{var L,U;return{value:(L=R==null?void 0:R.product)==null?void 0:L.id,key:(U=R==null?void 0:R.product)==null?void 0:U.name}})},onFinish:z=>{c.value=!1}}})},x=async()=>{e.getVolumeTrackingItemDetails({id:s.value,callback:{onSuccess:j=>{n.value={...j}}}})},b=async j=>{o.value=!0,e.updateVolumeTrackingItem({id:s.value,params:j,callback:{onSuccess:z=>{l!=null&&l.loadPage&&(l==null||l.loadPage()),q()},onFinish:z=>{o.value=!1}}})},T=async j=>{o.value=!0,e.createVolumeTrackingItem({params:j,callback:{onSuccess:z=>{l!=null&&l.loadPage&&(l==null||l.loadPage()),q()},onFinish:z=>{o.value=!1}}})},q=()=>{_(),l.close()},I=v({description:[{required:!0,message:"Please type Description",trigger:"blur"}],productId:[{required:!0,message:"Please select Product",trigger:"blur"}],volume:[{required:!0,message:"Please type Volume",trigger:"blur"}]}),C=(j,z)=>{d.value=j,s.value=z},B=j=>{r.value=j},A=()=>{a.value&&a.value.validate(j=>{var z,R;if(j){const L={volumeTrackingId:d==null?void 0:d.value,description:(z=n==null?void 0:n.value)==null?void 0:z.description,volume:Number((R=n==null?void 0:n.value)==null?void 0:R.volume),type:Number(r==null?void 0:r.value)};s!=null&&s.value?b(L):T(L)}})},_=()=>{n.value=JSON.parse(JSON.stringify(m))};return{id:s,type:r,modal:h,rules:I,loading:o,targetData:n,formRef:a,productList:f,newProductModal:u,isModalVisible:y,toggleAddNewProductModal:M,setId:C,closeModal:q,submit:A,reset:_,setType:B,getTitle:()=>{var j;return((j=et(r.value,rm))==null?void 0:j.label)||""},getPackageReportInfo:P}}}),Av={key:0,class:"fixed top-0 right-0 bottom-0 left-0 z-40 flex items-center"},zv={class:"relative bg-card-background text-card-text-light max-h-11/12 w-11/12 flex flex-col items-center gap-3 mx-auto my-6 p-5 rounded-xl md:w-3/5 lg:w-2/5 lg:h-auto"},qv={class:"flex flex-row h-auto w-full items-center justify-between"},Bv={class:"text-lg font-bold"},jv={class:"flex flex-col gap-1"},Ev={class:"flex flex-col justify-between gap-1"},Rv={class:"flex flex-row justify-between items-center gap-1"},Ov={class:"flex flex-col gap-1"},Lv={class:"h-auto w-full flex flex-row items-center gap-2"},Wv=["disabled"],Uv=["disabled"],Gv={key:0,class:"indicator-label"},Yv={key:1,class:"indicator-progress"};function Jv(l,e,S,h,m,n){const a=k("SvgIcon"),o=k("el-input"),r=k("el-form-item"),d=k("el-option"),s=k("el-select"),u=k("el-form"),f=k("FormModal");return w(),$(O,null,[l.isVisible?(w(),$("div",Av,[e[10]||(e[10]=t("div",{class:"absolute inset-0 bg-sidebar-background opacity-25"},null,-1)),t("div",zv,[t("div",qv,[t("h3",Bv,D(l.id?`Edit ${l.getTitle()}`:`Add ${l.getTitle()}`),1),t("span",{class:"cursor-pointer ms-auto",onClick:e[0]||(e[0]=(...c)=>l.closeModal&&l.closeModal(...c))},[i(a,{icon:"closeModalIcon"})])]),i(u,{id:"add_volume_form",onSubmit:ie(l.submit,["prevent"]),model:l.targetData,rules:l.rules,ref:"formRef",class:"h-auto w-full flex flex-col gap-3"},{default:p(()=>[t("div",jv,[e[6]||(e[6]=t("label",{class:"font-bold"},[F("Volume (bbl)"),t("span",{class:"text-danger-active font-light"},"*")],-1)),i(r,{prop:"volume",class:"mt-auto"},{default:p(()=>[i(o,{type:"number",controls:!1,step:"any",modelValue:l.targetData.volume,"onUpdate:modelValue":e[1]||(e[1]=c=>l.targetData.volume=c),placeholder:"",name:"volume"},null,8,["modelValue"])]),_:1})]),t("div",Ev,[t("div",Rv,[e[7]||(e[7]=t("label",{class:"font-bold"},[F("Product"),t("span",{class:"text-danger-active font-light"},"*")],-1)),t("button",{type:"button",class:"bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover rounded-md px-4 py-2 font-semibold",onClick:e[2]||(e[2]=(...c)=>l.toggleAddNewProductModal&&l.toggleAddNewProductModal(...c))}," New Product ")]),i(r,{prop:"productId",class:"mt-auto"},{default:p(()=>[i(s,{modelValue:l.targetData.productId,"onUpdate:modelValue":e[3]||(e[3]=c=>l.targetData.productId=c),placeholder:"Select Product",clearable:""},{default:p(()=>[(w(!0),$(O,null,Z(l.productList,c=>(w(),te(d,{key:c.value,label:c.key,value:c.value,name:"productId"},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),t("div",Ov,[e[8]||(e[8]=t("label",{class:"font-bold"},[F("Description"),t("span",{class:"text-danger-active font-light"},"*")],-1)),i(r,{prop:"description",class:"mt-auto"},{default:p(()=>[i(o,{type:"textarea",rows:3,modelValue:l.targetData.description,"onUpdate:modelValue":e[4]||(e[4]=c=>l.targetData.description=c),placeholder:"",name:"description"},null,8,["modelValue"])]),_:1})]),t("div",Lv,[t("button",{type:"button",class:"bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover rounded-md px-4 py-2 font-semibold",onClick:e[5]||(e[5]=(...c)=>l.closeModal&&l.closeModal(...c)),disabled:l.loading}," Discard ",8,Wv),t("button",{class:"bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover rounded-md px-4 py-2 font-semibold",type:"submit",disabled:l.loading},[l.loading?E("",!0):(w(),$("span",Gv," Save ")),l.loading?(w(),$("span",Yv,e[9]||(e[9]=[F(" Please wait... "),t("span",{class:"spinner-border spinner-border-sm align-middle ms-2"},null,-1)]))):E("",!0)],8,Uv)])]),_:1},8,["onSubmit","model","rules"])])])):E("",!0),i(f,{isVisible:l.isModalVisible,close:l.toggleAddNewProductModal,ref:"newProductModal",productAndPackageInventoryId:l.productAndPackageInventoryId,loadTable:()=>l.getPackageReportInfo(l.productAndPackageInventoryId)},null,8,["isVisible","close","productAndPackageInventoryId","loadTable"])],64)}const Qv=Q(Hv,[["render",Jv]]),mt=[{value:Se.Addition,key:"Additions"},{value:Se.Loss,key:"Losses"},{value:Se.Transfer,key:"Transfers"}],Kv=J({name:"volume-details-modal",components:{SvgIcon:K},props:{isVisible:{type:Boolean,required:!0},close:{type:Function,required:!0},loadPage:{type:Function,default:()=>{},required:!0},editVolume:{type:Function,required:!1}},setup(l){const e=st(),S=al(),h=v(!1),m=v(),n=v(mt[0].value.toString()),a=v("");Y(a,y=>{y!==""&&o()});const o=async()=>{h.value=!0,e.getVolumeTrackingDetails({id:a.value,callback:{onSuccess:y=>{y.additions=[],y.losses=[],y.transfers=[];for(const M of y==null?void 0:y.volumeItems)(M==null?void 0:M.type)==Se.Addition?y.additions.push(M):(M==null?void 0:M.type)==Se.Loss?y.losses.push(M):(M==null?void 0:M.type)==Se.Transfer&&y.transfers.push(M);m.value=JSON.parse(JSON.stringify(y))},onFinish:y=>{h.value=!1}}})},r=y=>{a.value=y},d=()=>{l.close()},s=y=>{const M=y.target;n.value=M.getAttribute("data-tab-index")},u=y=>{se.deletionAlert({onConfirmed:()=>{f(y)}})},f=y=>{S.deleteVolumeTrackingItem({id:y,callback:{onSuccess:M=>{o(),l!=null&&l.loadPage&&l.loadPage()}}})};return{loading:h,tabs:mt,tabIndex:n,targetData:m,VolumeTracking:Se,closeModal:d,setId:r,deleteItem:u,numberWithCommas:fe,setActiveTab:s,formatDate:ke,editVolumeModal:(y,M)=>{l.editVolume&&l.editVolume(a==null?void 0:a.value,M,y)}}}}),Xv={key:0,class:"fixed top-0 right-0 bottom-0 left-0 z-40 flex items-center"},Zv={key:0,class:"rounded-3 shadow-sm border-top border-top-2 border-primary d-flex flex-column gap-6 pt-10 card-body overflow-x-scroll md:overflow-hidden"},ey={key:1,class:"relative bg-card-background text-card-text-light max-h-11/12 w-11/12 flex flex-col items-center gap-3 mx-auto my-6 p-5 rounded-xl overflow-y-scroll font-semibold md:w-4/5 md:overflow-hidden lg:w-2/5 lg:h-auto"},ty={class:"font-bold"},ly={class:"h-auto w-full flex items-center justify-between"},oy={class:"h-auto w-full flex flex-col gap-3 md:flex-row md:gap-4"},sy={class:"h-auto w-full flex flex-col gap-3"},ny={class:"h-auto w-full flex justify-between"},ay={class:"h-auto w-full flex justify-between"},iy={class:"h-auto w-full flex justify-between"},ry={class:"h-auto w-full flex flex-wrap gap-3 text-sm"},dy={class:"border border-gray-300 border-dashed rounded p-3"},uy={class:"border border-gray-300 border-dashed rounded p-3"},py={class:"h-auto w-full"},cy={class:"h-auto w-full flex flex-row flex-wrap items-center gap-2",role:"tablist"},fy=["data-tab-index"],my={key:0,class:"h-auto w-full overflow-x-scroll p-4 md:overflow-hidden"},gy={class:"p-4 whitespace-nowrap"},by={class:"p-4 whitespace-nowrap"},hy={class:"p-4 whitespace-nowrap"},vy={class:"flex items-center gap-3"},yy=["onClick"],wy={class:"svg-icon svg-icon-3"},Sy=["onClick"],$y={class:"svg-icon svg-icon-3 text-danger"},xy={key:1,class:"h-auto w-full overflow-x-scroll p-4 md:overflow-hidden"},ky={class:"p-4 whitespace-nowrap"},Dy={class:"p-4 whitespace-nowrap"},Vy={class:"p-4 whitespace-nowrap"},Iy={class:"flex items-center gap-3"},Py=["onClick"],Cy={class:"svg-icon svg-icon-3"},Ty=["onClick"],My={class:"text-danger"},Fy={key:2,class:"h-auto w-full overflow-x-scroll p-4 md:overflow-hidden"},Ny={class:"table"},_y={class:"p-4 whitespace-nowrap"},Hy={class:"p-4 whitespace-nowrap"},Ay={class:"p-4 whitespace-nowrap"},zy={class:"flex items-center gap-3"},qy=["onClick"],By=["onClick"],jy={class:"text-danger"};function Ey(l,e,S,h,m,n){var o,r,d,s,u,f,c,y,M,P,x;const a=k("SvgIcon");return l.isVisible?(w(),$("div",Xv,[e[9]||(e[9]=t("div",{class:"absolute inset-0 bg-sidebar-background opacity-25"},null,-1)),l.loading?(w(),$("div",Zv,e[2]||(e[2]=[t("div",{class:"text-center"},[t("div",{class:"spinner-border text-primary",role:"status"},[t("span",{class:"sr-only"},"Loading...")])],-1)]))):(w(),$("div",ey,[t("h5",ty,D((o=l.targetData)==null?void 0:o.name),1),t("div",ly,[t("span",{class:be(["rounded-lg px-2 py-1",((r=l.targetData)==null?void 0:r.status)===1?"bg-success":"bg-danger"])},D(((d=l.targetData)==null?void 0:d.status)===1?"Active":"Inactive"),3),t("span",{class:"cursor-pointer",onClick:e[0]||(e[0]=(...b)=>l.closeModal&&l.closeModal(...b))},[i(a,{icon:"closeModalIcon"})])]),t("div",oy,[t("div",sy,[e[8]||(e[8]=t("div",{class:"h-auto w-full flex justify-between"},[t("span",null,"Type"),t("span",null," Storage Type ")],-1)),t("div",ny,[e[3]||(e[3]=t("span",null,"Total Additions",-1)),t("span",null,D(l.numberWithCommas(((s=l.targetData)==null?void 0:s.totalAdditions)||0)),1)]),t("div",ay,[e[4]||(e[4]=t("span",null,"Total Losses",-1)),t("span",null,D(l.numberWithCommas(((u=l.targetData)==null?void 0:u.totalLosses)||0)),1)]),t("div",iy,[e[5]||(e[5]=t("span",null,"Total Transfers",-1)),t("span",null,D(l.numberWithCommas(((f=l.targetData)==null?void 0:f.totalTransfers)||0)),1)]),t("div",ry,[t("div",dy,[t("div",null,D(`${l.numberWithCommas(((c=l.targetData)==null?void 0:c.calculatedVolume)||0)} (bbl)`),1),e[6]||(e[6]=t("div",{class:"fw-semibold fs-7 text-success"},"Calculated Volume",-1))]),t("div",uy,[t("div",null,D(`${l.numberWithCommas((y=l.targetData)==null?void 0:y.measuredVolume)} (bbl)`),1),e[7]||(e[7]=t("div",{class:"fw-semibold fs-7 text-primary"},"Measured Volume",-1))])])]),t("div",py,[t("ul",cy,[(w(!0),$(O,null,Z(l.tabs,b=>(w(),$("li",{class:"nav-item",key:b==null?void 0:b.value},[t("div",{class:be(["whitespace-nowrap cursor-pointer font-semibold hover:text-active-hover hover:border-b-2 hover:border-active-border-hover",{active:l.tabIndex===(b==null?void 0:b.value.toString()),"text-active border-b-2 border-active-border":l.tabIndex===(b==null?void 0:b.value.toString()),"text-inactive border-b-2 border-inactive-border":l.tabIndex!==(b==null?void 0:b.value.toString())}]),"data-bs-toggle":"tab",onClick:e[1]||(e[1]=T=>l.setActiveTab(T)),"data-tab-index":b==null?void 0:b.value,role:"tab"},D(b==null?void 0:b.key),11,fy)]))),128))]),l.tabIndex==l.VolumeTracking.Addition.toString()?(w(),$("div",my,[t("table",null,[t("tbody",null,[(w(!0),$(O,null,Z((M=l.targetData)==null?void 0:M.additions,b=>(w(),$("tr",{key:b.id},[t("td",gy,D(l.formatDate(b==null?void 0:b.createdAt,"DD MMM YYYY")),1),t("td",by,D(b==null?void 0:b.description),1),t("td",hy,D(`${b==null?void 0:b.volume} (bbl)`),1),t("td",null,[t("div",vy,[t("button",{class:"bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover font-semibold h-8 w-8 rounded-lg px-1.5 py-0.5",onClick:T=>l.editVolumeModal(b==null?void 0:b.id,l.VolumeTracking.Addition)},[t("span",wy,[i(a,{icon:"newReportIcon"})])],8,yy),t("button",{class:"bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover font-semibold h-8 w-8 rounded-lg px-1.5 pb-1",onClick:T=>l.deleteItem(b==null?void 0:b.id)},[t("span",$y,[i(a,{icon:"trashIcon"})])],8,Sy)])])]))),128))])])])):E("",!0),l.tabIndex==l.VolumeTracking.Loss.toString()?(w(),$("div",xy,[t("table",null,[t("tbody",null,[(w(!0),$(O,null,Z((P=l.targetData)==null?void 0:P.losses,b=>(w(),$("tr",{key:b.id},[t("td",ky,D(l.formatDate(b==null?void 0:b.createdAt,"DD MMM YYYY")),1),t("td",Dy,D(b==null?void 0:b.description),1),t("td",Vy,D(`${b==null?void 0:b.volume} (bbl)`),1),t("td",null,[t("div",Iy,[t("button",{class:"bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover font-semibold h-8 w-8 rounded-lg px-1.5 py-0.5",onClick:T=>l.editVolumeModal(b==null?void 0:b.id,l.VolumeTracking.Loss)},[t("span",Cy,[i(a,{icon:"newReportIcon"})])],8,Py),t("button",{class:"bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover font-semibold h-8 w-8 rounded-lg px-1.5 py-0.5",onClick:T=>l.deleteItem(b==null?void 0:b.id)},[t("span",My,[i(a,{icon:"trashIcon"})])],8,Ty)])])]))),128))])])])):E("",!0),l.tabIndex==l.VolumeTracking.Transfer.toString()?(w(),$("div",Fy,[t("table",Ny,[t("tbody",null,[(w(!0),$(O,null,Z((x=l.targetData)==null?void 0:x.transfers,b=>(w(),$("tr",{key:b.id},[t("td",_y,D(l.formatDate(b==null?void 0:b.createdAt,"DD MMM YYYY")),1),t("td",Hy,D(b==null?void 0:b.description),1),t("td",Ay,D(`${b==null?void 0:b.volume} (bbl)`),1),t("td",null,[t("div",zy,[t("button",{class:"bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover font-semibold h-8 w-8 rounded-lg px-1.5 py-0.5",onClick:T=>l.editVolumeModal(b==null?void 0:b.id,l.VolumeTracking.Transfer)},[i(a,{icon:"newReportIcon"})],8,qy),t("button",{class:"bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover font-semibold h-8 w-8 rounded-lg px-1.5 py-0.5",onClick:T=>l.deleteItem(b==null?void 0:b.id)},[t("span",jy,[i(a,{icon:"trashIcon"})])],8,By)])])]))),128))])])])):E("",!0)])])]))])):E("",!0)}const Ry=Q(Kv,[["render",Ey]]),Oy=J({name:"volume-tracking",components:{SvgIcon:K,BottomTool:ce,VolumeModal:Qv,StorageOrPitModal:Ut,VolumeDetailsModal:Ry,NoEntries:me},props:{setChildActiveTab:{type:Function,required:!1,default:()=>{}}},setup(l){const e=re(),S=Lt(),h=st(),m=v(null),n=v(null),a=v(null),o=v(!1),r=v([]),d=v(""),s=le("dailyReport"),u=v(!1),f=v(!1),c=v(!1);ae(()=>{s!=null&&s.getDailyReportId()?(M(),P()):y()});const y=async()=>{var C;s==null||s.createDailyReport({wellId:(C=e==null?void 0:e.params)==null?void 0:C.id,callback:{onSuccess:B=>{M(),P()}}})},M=async(C={dailyReportId:s==null?void 0:s.getDailyReportId(),page:1,limit:200})=>{o.value=!0,h.getVolumeTrackings({params:C,callback:{onSuccess:B=>{r.value=JSON.parse(JSON.stringify(B==null?void 0:B.items))},onFinish:B=>{o.value=!1}}})},P=async()=>{s!=null&&s.getDailyReportId()&&(o.value=!0,S.getProductPackageInventories({dailyReportId:s==null?void 0:s.getDailyReportId(),callback:{onSuccess:C=>{d.value=C==null?void 0:C.id},onFinish:C=>{o.value=!1}}}))},x=(C,B,A)=>{var _,N;A&&(c.value=!1),C&&((_=m==null?void 0:m.value)==null||_.setId(C,A||"")),B&&((N=m==null?void 0:m.value)==null||N.setType(B)),u.value=!u.value},b=C=>{var B;C&&((B=n==null?void 0:n.value)==null||B.setId(C)),f.value=!f.value},T=C=>{var B;C&&((B=a==null?void 0:a.value)==null||B.setId(C)),c.value=!c.value},q=C=>{se.deletionAlert({onConfirmed:()=>{I(C)}})},I=C=>{o.value=!0,h.deleteVolumeTracking({id:C,callback:{onSuccess:B=>{M()},onFinish:B=>{o.value=!1}}})};return{getVolumeTrackings:M,deleteVolumeTracking:q,numberWithCommas:fe,toggleVolumeModal:x,toggleStorageOrPitModal:b,toggleVolumeDetailsModal:T,loading:o,volumeTrackingList:r,volumeModal:m,storageOrPitModal:n,isSoPModalVisible:f,volumeDetailsModal:a,productAndPackageInventoryId:d,isVolumeModalVisible:u,isDetailsModalVisible:c,VolumeTracking:Se}}}),Ly={class:"bg-card-background text-card-text-light w-11/12 mx-auto mb-4 h-auto rounded-xl"},Wy={class:"card-body"},Uy={key:0,class:"text-center"},Gy={key:1,class:"h-auto w-full"},Yy={key:1,class:"h-auto w-full mx-auto p-4 grid grid-cols-1 gap-4 md:grid-cols-2 md:gap-7 lg:grid-cols-3"},Jy={class:"h-auto w-full flex justify-between items-center gap-3"},Qy=["onClick"],Ky={class:"h-auto w-full flex items-center justify-between border-dashed border-b-[1px] py-3"},Xy={class:"h-auto w-full flex items-center justify-between border-dashed border-b-[1px] py-3"},Zy={class:"h-auto w-full flex items-center justify-between border-dashed border-b-[1px] py-3"},ew={class:"h-auto w-full flex items-center justify-between py-4"},tw={class:"h-auto w-full flex flex-wrap items-center gap-3"},lw={class:"h-auto w-full flex items-center justify-between border border-gray-300 border-dashed rounded p-3"},ow={class:"h-auto w-full flex items-center justify-between border border-gray-300 border-dashed rounded p-3"},sw={class:"flex flex-row items-center gap-2 absolute top-0 end-0 -translate-y-1/2"},nw=["onClick"],aw=["onClick"],iw=["onClick"],rw=["onClick"],dw=["onClick"],uw={class:"text-danger"};function pw(l,e,S,h,m,n){const a=k("NoEntries"),o=k("SvgIcon"),r=k("el-tooltip"),d=k("BottomTool"),s=k("VolumeModal"),u=k("StorageOrPitModal"),f=k("VolumeDetailsModal");return w(),$(O,null,[t("div",Ly,[e[7]||(e[7]=t("div",{class:"h-auto w-11/12 flex flex-col gap-6 mx-auto mt-7 px-3 py-4"},[t("h1",{class:"font-bold"},"Volume Tracking")],-1)),t("div",Wy,[l.loading?(w(),$("div",Uy,e[0]||(e[0]=[t("div",{class:"spinner-border text-primary",role:"status"},[t("span",{class:"sr-only"},"Loading...")],-1)]))):(w(),$("div",Gy,[l.volumeTrackingList.length===0?(w(),te(a,{key:0,addNew:()=>l.toggleStorageOrPitModal()},null,8,["addNew"])):(w(),$("div",Yy,[(w(!0),$(O,null,Z(l.volumeTrackingList,c=>(w(),$("div",{key:c==null?void 0:c.id,class:"relative h-auto w-full mx-auto bg-minicard-background text-minicard-text-light flex flex-col items-center p-4 pt-7 rounded-xl border-t-2 border-active my-2 first:mt-4 last:mb-5 font-semibold md:first:mt-0 md:last:mb-0 md:m-0"},[t("div",Jy,[t("h5",{class:"h-auto w-full text-xl font-bold underline underline-offset-3",onClick:()=>l.toggleVolumeDetailsModal(c==null?void 0:c.id)},D(c==null?void 0:c.name),9,Qy),t("span",{class:be(["rounded-lg px-2 py-1",(c==null?void 0:c.status)===1?"bg-success":"bg-danger"])},D((c==null?void 0:c.status)===1?"Active":"Inactive"),3)]),t("div",Ky,[e[1]||(e[1]=t("span",null,"Type",-1)),t("span",null,D(c==null?void 0:c.storageType),1)]),t("div",Xy,[e[2]||(e[2]=t("span",null,"Total Additions",-1)),t("span",null,D(l.numberWithCommas((c==null?void 0:c.totalAdditions)||0)),1)]),t("div",Zy,[e[3]||(e[3]=t("span",null,"Total Losses",-1)),t("span",null,D(l.numberWithCommas((c==null?void 0:c.totalLosses)||0)),1)]),t("div",ew,[e[4]||(e[4]=t("span",null,"Total Transfers",-1)),t("span",null,D(l.numberWithCommas((c==null?void 0:c.totalTransfers)||0)),1)]),t("div",tw,[t("div",lw,[t("span",null,D(`${l.numberWithCommas((c==null?void 0:c.calculatedVolume)||0)} (bbl)`),1),e[5]||(e[5]=t("span",null,"Calculated Volume",-1))]),t("div",ow,[t("span",null,D(`${l.numberWithCommas(c==null?void 0:c.measuredVolume)} (bbl)`),1),e[6]||(e[6]=t("span",null,"Measured Volume",-1))])]),t("div",sw,[i(r,{content:"Addition",placement:"top",effect:"customize"},{default:p(()=>[t("button",{class:"p-2 rounded-full bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover",onClick:()=>l.toggleVolumeModal(c==null?void 0:c.id,l.VolumeTracking.Addition)},[i(o,{icon:"addIcon"})],8,nw)]),_:2},1024),i(r,{content:"Loss",placement:"top",effect:"customize"},{default:p(()=>[t("button",{class:"p-2 rounded-full bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover",onClick:()=>l.toggleVolumeModal(c==null?void 0:c.id,l.VolumeTracking.Loss)},[i(o,{icon:"minusIcon"})],8,aw)]),_:2},1024),i(r,{content:"Transfer",placement:"top",effect:"customize"},{default:p(()=>[t("button",{class:"p-2 rounded-full bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover",onClick:()=>l.toggleVolumeModal(c==null?void 0:c.id,l.VolumeTracking.Transfer)},[i(o,{icon:"arrowFromLeft"})],8,iw)]),_:2},1024),i(r,{content:"Edit",placement:"top",effect:"customize"},{default:p(()=>[t("button",{class:"p-2 rounded-full bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover",onClick:()=>l.toggleStorageOrPitModal(c==null?void 0:c.id)},[i(o,{icon:"pencilIcon"})],8,rw)]),_:2},1024),i(r,{content:"Delete",placement:"top",effect:"customize"},{default:p(()=>[t("button",{class:"p-2 rounded-full bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover",onClick:y=>l.deleteVolumeTracking(c==null?void 0:c.id)},[t("span",uw,[i(o,{icon:"trashIcon"})])],8,dw)]),_:2},1024)])]))),128))]))]))])]),i(d,{addNew:()=>l.toggleStorageOrPitModal(),showHelpInfo:!1},null,8,["addNew"]),i(s,{isVisible:l.isVolumeModalVisible,close:l.toggleVolumeModal,ref:"volumeModal",loadPage:l.getVolumeTrackings,productAndPackageInventoryId:l.productAndPackageInventoryId},null,8,["isVisible","close","loadPage","productAndPackageInventoryId"]),i(u,{isVisible:l.isSoPModalVisible,close:()=>l.toggleStorageOrPitModal(),ref:"storageOrPitModal",loadPage:l.getVolumeTrackings},null,8,["isVisible","close","loadPage"]),i(f,{isVisible:l.isDetailsModalVisible,close:()=>l.toggleVolumeDetailsModal(),ref:"volumeDetailsModal",loadPage:l.getVolumeTrackings,editVolume:l.toggleVolumeModal},null,8,["isVisible","close","loadPage","editVolume"])],64)}const il=Q(Oy,[["render",pw]]),cw=["Home","Daily Report"],fw={[ge.General]:At,[ge.MudProperties]:jt,[ge.SiteEquipment]:ol,[ge.Tasks]:nl,[ge.Product]:Yt,[ge.VolumeTracking]:il,[ge.Costs]:kt,[ge.Notes]:Rt},mw=J({name:"daily-report",components:{SvgIcon:K,General:At,MudProperties:jt,SiteEquipment:ol,Tasks:nl,ProductPackage:Yt,VolumeTracking:il,Costs:kt,Notes:Rt,PageHeader:pl},setup(){const l=re(),e=v(ge.General),S=v(-1),h=v(),m=ot(()=>fw[e.value]),n=v(null),a=v(!1),o=le("dailyReport");ae(()=>{var u,f;(u=l==null?void 0:l.params)!=null&&u.dailyReportId?o==null||o.setDailyReportId((f=l==null?void 0:l.params)==null?void 0:f.dailyReportId):o==null||o.resetDailyReportId()}),ul(()=>{o==null||o.resetDailyReportId()});const r=async u=>{const f=u.target;e.value!==ge.General&&e.value!==ge.MudProperties?e.value=Number(f.getAttribute("data-tab-index")):d()?se.incompleteFormAlert({onConfirmed:()=>{e.value=Number(f.getAttribute("data-tab-index"))}},"You have unsaved changes. Are you sure you want to leave?"):e.value=Number(f.getAttribute("data-tab-index"))},d=()=>{var u,f;if(n!=null&&n.value&&((u=n==null?void 0:n.value)!=null&&u.isFormOfChildTabDirty))return(f=n==null?void 0:n.value)==null?void 0:f.isFormOfChildTabDirty()},s=u=>{S.value=u};return{currentTab:n,loading:a,EDailyTab:ge,dailyTabs:ml,breadcrumbs:cw,wellData:h,tabIndex:e,currentComponent:m,currentChildTabIndex:S,isSystemAdmin:De.checkRole(Ze.SystemAdmin),formatDate:ke,isValidDate:bl,setActiveTab:r,setChildActiveTab:s}}}),gw={class:"p-4"},bw={role:"tablist",class:"flex flex-wrap items-center justify-start gap-2"},hw=["data-tab-index"],vw={key:0,class:"text-center mt-7"},yw={key:1};function ww(l,e,S,h,m,n){const a=k("PageHeader");return w(),$(O,null,[i(a,{title:"Daily Report General",breadcrumbs:l.breadcrumbs},null,8,["breadcrumbs"]),t("div",gw,[t("ul",bw,[(w(!0),$(O,null,Z(l.dailyTabs,o=>(w(),$("li",{class:"nav-item",key:o==null?void 0:o.value},[t("div",{class:be(["whitespace-nowrap cursor-pointer font-semibold hover:text-active-hover hover:border-b-2 hover:border-active-border-hover",{active:l.tabIndex===(o==null?void 0:o.value),"text-active-dark border-b-2 border-active-border-dark":l.tabIndex===(o==null?void 0:o.value),"text-inactive border-b-2 border-inactive-border":l.tabIndex!==(o==null?void 0:o.value)}]),onClick:e[0]||(e[0]=r=>l.setActiveTab(r)),"data-tab-index":o==null?void 0:o.value,role:"tab"},D(o==null?void 0:o.label),11,hw)]))),128))])]),l.loading?(w(),$("div",vw,e[1]||(e[1]=[t("div",{class:"spinner-border text-primary",role:"status"},[t("span",{class:"sr-only"},"Loading...")],-1)]))):(w(),$("div",yw,[(w(),te(it(l.currentComponent),{setChildActiveTab:l.setChildActiveTab,ref:"currentTab"},null,8,["setChildActiveTab"]))]))],64)}const Sw=Q(mw,[["render",ww]]),$w=J({name:"provide-component",components:{DailyReportProvide:Sl,DailyReportContent:Sw},setup(){return{}}});function xw(l,e,S,h,m,n){const a=k("DailyReportContent"),o=k("DailyReportProvide");return w(),te(o,null,{default:p(()=>[i(a)]),_:1})}const qw=Q($w,[["render",xw]]);export{qw as default};
