<template>
  <div
    class="relative h-auto w-full mx-auto bg-minicard-background text-minicard-text-light mt-4 p-4 rounded-xl border-t-2 border-active font-semibold"
  >
    <div
      class="flex flex-row items-center gap-2 absolute top-0 end-0 -translate-y-1/2"
    >
      <button
        class="p-2 rounded-full bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover"
        @click="toggleEditTargetedPropertiesModal"
      >
        <span>
          <SvgIcon icon="pencilIcon" />
        </span>
      </button>
    </div>
    <div class="h-auto w-full font-semibold">
      <h5 class="font-bold h-auto w-full border-b-2 border-dashed pb-2">
        Targeted Properties
      </h5>
      <div class="h-auto w-full flex flex-col gap-3 md:flex-row md:gap-4">
        <div class="h-auto w-full flex flex-col gap-3">
          <div
            class="flex items-center justify-between border-dashed border-b-[1px] py-3"
          >
            <span>Fluid Type: </span>
            <span>{{
              getOption(targetPropertyData?.fluidType, fluidTypeOptions)
                ?.label || ""
            }}</span>
          </div>
          <div
            class="flex items-center justify-between border-dashed border-b-[1px] py-3"
          >
            <span>MW (ppg or lbs/gal):</span>
            <span>{{ targetPropertyData?.mudWeight }}</span>
          </div>
          <div
            class="flex items-center justify-between border-dashed border-b-[1px] py-3"
          >
            <span>Funnel Viscosity (sec/qt):</span>
            <span>{{ targetPropertyData?.funnelViscosity }}</span>
          </div>
          <div
            class="flex items-center justify-between border-dashed border-b-[1px] py-3"
          >
            <span>PV (Plastic Viscosity) (cP):</span>
            <span>{{ targetPropertyData?.plasticViscosity }}</span>
          </div>
          <div
            class="flex items-center justify-between border-dashed border-b-[1px] py-3"
          >
            <span>YP (Yield Point) (lbf/100ft2): </span>
            <span>{{ targetPropertyData?.yieldPoint }}</span>
          </div>
          <div
            class="flex items-center justify-between border-dashed border-b-[1px] py-3"
          >
            <span>6 rpm:</span>
            <span>{{ targetPropertyData?.rpm }}</span>
          </div>
          <div
            class="flex items-center justify-between border-dashed border-b-[1px] py-3"
          >
            <span>API filtrate (ml/30min):</span>
            <span>{{ targetPropertyData?.apiFiltrate }}</span>
          </div>
          <div
            class="flex items-center justify-between border-dashed border-b-[1px] py-3 md:border-b-0"
          >
            <span>API Cake: </span>
            <span>{{ targetPropertyData?.apiCakeThickness }}</span>
          </div>
        </div>
        <div class="h-auto w-full flex flex-col gap-3">
          <div
            class="flex items-center justify-between border-dashed border-b-[1px] py-3"
          >
            <span>pH:</span>
            <span>{{ targetPropertyData?.pH }}</span>
          </div>
          <div
            class="flex items-center justify-between border-dashed border-b-[1px] py-3"
          >
            <span>Mud Alkalinity (Pm) (ml): </span>
            <span>{{ targetPropertyData?.mudAlkalinity }}</span>
          </div>
          <div
            class="flex items-center justify-between border-dashed border-b-[1px] py-3"
          >
            <span>Filtrate Alkalinity (Pf) (ml):</span>
            <span>{{ targetPropertyData?.filtrateAlkalinity }}</span>
          </div>
          <div
            class="flex items-center justify-between border-dashed border-b-[1px] py-3"
          >
            <span>Filtrate Alkalinity (Mf) (ml):</span>
            <span>{{ targetPropertyData?.filtrateAlkalinity }}</span>
          </div>
          <div
            class="flex items-center justify-between border-dashed border-b-[1px] py-3"
          >
            <span>Chlorides (mg/L):</span>
            <span>{{ targetPropertyData?.chlorides }}</span>
          </div>
          <div
            class="flex items-center justify-between border-dashed border-b-[1px] py-3"
          >
            <span>Total Hardness (mg/L):</span>
            <span>{{ targetPropertyData?.totalHardness }}</span>
          </div>
          <div
            class="flex items-center justify-between border-dashed border-b-[1px] py-3"
          >
            <span>Linear Gel Strength (LGS) (%):</span>
            <span>{{ targetPropertyData?.linearGelStrengthPercent }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div v-if="loading" class="text-center">
    <div class="spinner-border text-primary" role="status">
      <span class="sr-only">Loading...</span>
    </div>
  </div>
  <template v-else>
    <NoEntries v-if="sampleList.length === 0" :addNew="toggleNewSampleModal" />
    <div v-else class="grid grid-cols-1 gap-4 md:grid-cols-2 md:gap-7 lg:grid-cols-3">
      <div
        v-for="item in sampleList"
        :key="item?.id"
        class="relative h-auto w-11/12 mx-auto bg-minicard-background text-minicard-text-light flex flex-col items-center p-4 rounded-xl border-t-2 border-active my-7 first:mt-4 last:mb-5 font-semibold md:first:mt-0 md:last:mb-0 md:m-0"
      >
        <div
          class="h-auto w-full flex flex-col gap-3"
          @click="toggleViewSampleModal(item?.id.toString())"
        >
          <h5 class="text-xl font-bold">{{ item?.name }}</h5>
          <div class="d-flex flex-column gap-3">
            <div
              class="flex items-center justify-between border-dashed border-b-[1px] py-3"
            >
              <span>Sample From</span>
              <span class="text-success">
                {{
                  getOption(item?.sampleFrom, sampleFromOptions)?.label || ""
                }}
              </span>
            </div>
            <div
              class="flex items-center justify-between border-dashed border-b-[1px] py-3"
            >
              <span>Type</span>
              <span class="text-success">
                {{ getOption(item?.fluidType, fluidTypeOptions)?.label }}
              </span>
            </div>
            <div class="flex items-center justify-between pt-3">
              <span>Time Sample Taken</span>
              <span>
                {{ formatTime(item?.timeSampleTaken) }}
              </span>
            </div>
          </div>
          <div class="flex flex-wrap items-center gap-2">
            <div
              class="flex items-center gap-2 border border-gray-300 border-dashed rounded py-3 px-4"
            >
              <div>
                {{ `${numberWithCommas(item?.mudWeight)} (ppg)` }}
              </div>
              <div class="text-danger">MW</div>
            </div>
            <div
              class="flex items-center gap-2 border border-gray-300 border-dashed rounded py-3 px-4"
            >
              <div>
                {{ `${numberWithCommas(item?.measuredDepth)} (in)` }}
              </div>
              <div>Depth</div>
            </div>
          </div>
        </div>
        <div
          class="flex flex-row items-center gap-2 absolute top-0 end-0 -translate-y-1/2"
        >
          <button
            class="p-2 rounded-full bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover"
            @click="toggleEditSampleModal(item?.id.toString())"
          >
            <span class="svg-icon svg-icon-3">
              <SvgIcon icon="pencilIcon" />
            </span>
          </button>
          <button
            class="p-2 rounded-full bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover"
            @click="deleteSample(item?.id.toString())"
          >
            <span class="text-danger">
              <SvgIcon icon="trashIcon" />
            </span>
          </button>
        </div>
      </div>
    </div>
  </template>

  <BottomTool :addNew="toggleNewSampleModal" :showHelpInfo="false" />
  <SampleModal
    :isVisible="isSampleModalVisible"
    :close="toggleNewSampleModal"
    ref="sampleModal"
    :loadPage="getSamples"
  />
  <EditTargetedPropertiesModal
    :isVisible="isEditSampleModalVisible"
    :close="toggleEditTargetedPropertiesModal"
    ref="editTargetedPropertiesModal"
    :wellId="wellId"
    :loadPage="getTargetProperty"
  />
  <ViewSampleModal
    :isVisible="isViewSampleModalVisible"
    :close="toggleViewSampleModal"
    ref="viewSampleModal"
    @editModal="toggleEditSampleModal"
  />
</template>

<script lang="ts">
import NoEntries from "@/components/NoEntries.vue";
import SvgIcon from "@/constants/SvgIcon.vue";
import { getOption } from "@/utils/option";
import { fluidTypeOptions, sampleFromOptions } from "@/constants/sample";
import { formatTime } from "@/utils/date";
import { numberWithCommas } from "@/utils/numberFormatter";
import AlertService from "@/services/AlertService";
import { useSampleStore } from "@/stores/sample";
import { useTargetPropertyStore } from "@/stores/target-property";
import { defineComponent, inject, onMounted, ref, type Ref } from "vue";
import { useRoute } from "vue-router";
import BottomTool from "@/components/common/BottomTool.vue";
import EditTargetedPropertiesModal from "./EditTargetedPropertiesModal.vue";
import SampleModal from "./SampleModal.vue";
import ViewSampleModal from "./ViewSampleModal.vue";
import type { Provide } from "@/types/injection-types";

export default defineComponent({
  name: "sample",
  components: {
    SvgIcon,
    BottomTool,
    SampleModal,
    EditTargetedPropertiesModal,
    ViewSampleModal,
    NoEntries,
  },
  setup() {
    const route = useRoute();
    const sampleStore = useSampleStore();
    const targetPropertyStore = useTargetPropertyStore();
    const loading = ref(false);
    const targetPropertyData = ref();
    const sampleModal: Ref<any> = ref<typeof SampleModal | null>(null);
    const viewSampleModal: Ref<any> = ref<typeof ViewSampleModal | null>(null);
    const editTargetedPropertiesModal: Ref<any> = ref<
      typeof EditTargetedPropertiesModal | null
    >(null);
    const sampleList = ref<any>([]);
    const wellId = route.params?.id?.toString();
    const dailyReportProvide = inject<Provide.DailyReport>("dailyReport");
    const isSampleModalVisible = ref(false);
    const isViewSampleModalVisible = ref(false);
    const isEditSampleModalVisible = ref(false);

    onMounted(() => {
      if (dailyReportProvide?.getDailyReportId()) {
        getSamples();
      }
      getTargetProperty();
    });

    const getTargetProperty = async (): Promise<void> => {
      loading.value = true;

      targetPropertyStore.getTargetPropertyDetails({
        wellId: wellId,
        callback: {
          onSuccess: (res: any) => {
            targetPropertyData.value = res;
          },
          onFinish: (_err: any) => {
            loading.value = false;
          },
        },
      });
    };

    const getSamples = async (
      params = {
        dailyReportId: dailyReportProvide?.getDailyReportId(),
        page: 1,
        limit: 200,
      }
    ): Promise<void> => {
      loading.value = true;

      sampleStore.getSamples({
        params: params,
        callback: {
          onSuccess: (res: any) => {
            sampleList.value = res?.items;
          },
          onFinish: (_err: any) => {
            loading.value = false;
          },
        },
      });
    };

    const toggleViewSampleModal = (id: string): void => {
      if (id) {
        viewSampleModal?.value?.setId(id);
      }
      isViewSampleModalVisible.value = !isViewSampleModalVisible.value;
    };

    const toggleNewSampleModal = () => {
      isSampleModalVisible.value = !isSampleModalVisible.value;
    };

    const toggleEditSampleModal = (id: string): void => {
      sampleModal?.value?.setId(id);
      isSampleModalVisible.value = !isSampleModalVisible.value;
    };

    const toggleEditTargetedPropertiesModal = (): void => {
      isEditSampleModalVisible.value = !isEditSampleModalVisible.value;
    };

    const deleteSample = (id: string) => {
      AlertService.deletionAlert({
        onConfirmed: () => {
          deleteSampleById(id);
        },
      });
    };

    const deleteSampleById = async (id: string): Promise<void> => {
      loading.value = true;
      sampleStore.deleteSample({
        id: id,
        callback: {
          onSuccess: (_res: any) => {
            getSamples();
          },
          onFinish: (_err: any) => {
            loading.value = false;
          },
        },
      });
    };

    return {
      wellId,
      sampleList,
      loading,
      sampleModal,
      targetPropertyData,
      fluidTypeOptions,
      viewSampleModal,
      sampleFromOptions,
      editTargetedPropertiesModal,
      isSampleModalVisible,
      isViewSampleModalVisible,
      isEditSampleModalVisible,
      getOption,
      getSamples,
      formatTime,
      deleteSample,
      numberWithCommas,
      getTargetProperty,
      toggleNewSampleModal,
      toggleEditSampleModal,
      toggleViewSampleModal,
      toggleEditTargetedPropertiesModal,
    };
  },
});
</script>
