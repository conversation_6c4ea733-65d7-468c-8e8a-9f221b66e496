import{d as V,q as f,x as j,L as x,Y as A,Z as $,_ as M,c as a,o as i,a as t,p as N,l as U,H as u,N as q,$ as b,F as y,k as c,t as v}from"./index-CGNRhvz7.js";import{u as B}from"./company-oDyd0dWV.js";const k={value:"",label:"All"},S={value:0,label:"All"},E=V({name:"user-management-filter",components:{},props:{hideFilter:{type:Function,required:!1,default:()=>{}},onFilter:{type:Function,required:!0}},setup(e){const l=B(),m=f(!1),p=f([k]),g=[S,...$],F=[S,...A],o={email:"",status:0,role:0,companyId:""},s=f({...o});j(()=>{x()&&C()});const C=async()=>{m.value=!0,l.getCompanies({params:{page:1,limit:500},callback:{onSuccess:r=>{var d;p.value=[k,...(d=r==null?void 0:r.items)==null?void 0:d.map(n=>({value:n==null?void 0:n.id,label:n==null?void 0:n.name}))]},onFinish:()=>{m.value=!1}}})},O=()=>{e!=null&&e.hideFilter&&(e==null||e.hideFilter())},I=()=>{s.value={...o},w()},w=()=>{var r,d,n,h;e.onFilter({email:((r=s.value)==null?void 0:r.email)||null,status:((d=s.value)==null?void 0:d.status)||null,role:((n=s.value)==null?void 0:n.role)||null,companyId:((h=s.value)==null?void 0:h.companyId)||null})};return{loading:m,isSystemAdmin:x,filterForm:s,roleOptions:g,companyOptions:p,statusOptions:F,apply:w,hideFilter:O,resetFilter:I}}}),R={class:"bg-purple-300 h-auto max-w-full my-8 rounded-xl shadow-sm mx-auto px-2 py-4"},D={class:"bg-amber-300 h-auto w-full flex flex-row items-center justify-between"},L={class:"flex flex-row gap-2"},H={class:"bg-blue-300"},T=["model"],Y={class:"row g-9"},Z={class:"col-md-6 col-lg-3 fv-row d-flex flex-column justify-content-stretch"},z={prop:"email"},G={class:"col-md-6 col-lg-3 fv-row d-flex flex-column justify-content-stretch"},J={prop:"status"},K={class:"col-md-6 col-lg-3 fv-row d-flex flex-column justify-content-stretch"},P={prop:"role"},Q=["options"],W={key:0,class:"col-md-6 col-lg-3 fv-row d-flex flex-column justify-content-stretch"},X={prop:"companyId"},_=["loading"];function ee(e,l,m,p,g,F){return i(),a("div",R,[t("div",D,[l[8]||(l[8]=t("div",null,[t("h5",{class:"mb-0 me-4 text-gray-900"},"Filter")],-1)),t("div",L,[t("button",{class:"px-4 py-2 font-semibold text-sm text-grey-400 hover:bg-grey-300 rounded-md",onClick:l[0]||(l[0]=(...o)=>e.resetFilter&&e.resetFilter(...o))}," Reset "),t("button",{class:"px-4 py-2 font-semibold text-sm text-primary hover:bg-primary hover:text-white rounded-md",onClick:l[1]||(l[1]=(...o)=>e.hideFilter&&e.hideFilter(...o))}," Close "),t("button",{class:"px-4 py-2 font-semibold text-sm bg-primary text-white hover:bg-primary-active hover:text-white rounded-md",type:"button",onClick:l[2]||(l[2]=(...o)=>e.apply&&e.apply(...o))}," Apply ")])]),t("div",H,[t("form",{class:"form new-report-form",model:e.filterForm,onSubmit:l[7]||(l[7]=N((...o)=>e.apply&&e.apply(...o),["prevent"]))},[t("div",null,[t("div",Y,[t("div",Z,[l[9]||(l[9]=t("label",{class:"d-flex align-items-center fs-6 fw-semibold mb-2 text-break"},"Email",-1)),t("span",z,[u(t("input",{class:"h-auto w-full bg-white rounded",placeholder:"Email",name:"email","onUpdate:modelValue":l[3]||(l[3]=o=>e.filterForm.email=o)},null,512),[[q,e.filterForm.email]])])]),t("div",G,[l[10]||(l[10]=t("label",{class:"d-flex align-items-center fs-6 fw-semibold mb-2 text-break"}," Status ",-1)),t("span",J,[u(t("select",{class:"w-full",placeholder:"Status",name:"status","onUpdate:modelValue":l[4]||(l[4]=o=>e.filterForm.status=o)},[(i(!0),a(y,null,c(e.statusOptions,(o,s)=>(i(),a("option",{key:s},v(o.label),1))),128))],512),[[b,e.filterForm.status]])])]),t("div",K,[l[11]||(l[11]=t("label",{class:"d-flex align-items-center fs-6 fw-semibold mb-2 text-break"}," Roles ",-1)),t("span",P,[u(t("select",{class:"w-full",options:e.roleOptions,placeholder:"Roles",name:"role","onUpdate:modelValue":l[5]||(l[5]=o=>e.filterForm.role=o)},[(i(!0),a(y,null,c(e.roleOptions,(o,s)=>(i(),a("option",{key:s},v(o.label),1))),128))],8,Q),[[b,e.filterForm.role]])])]),e.isSystemAdmin()?(i(),a("div",W,[l[12]||(l[12]=t("label",{class:"d-flex align-items-center fs-6 fw-semibold mb-2 text-break"}," Company ",-1)),t("span",X,[u(t("select",{class:"w-full",placeholder:"Company",name:"companyId","onUpdate:modelValue":l[6]||(l[6]=o=>e.filterForm.companyId=o),loading:e.loading},[(i(!0),a(y,null,c(e.companyOptions,(o,s)=>(i(),a("option",{key:s},v(o.label),1))),128))],8,_),[[b,e.filterForm.companyId]])])])):U("",!0)])]),l[13]||(l[13]=t("button",{class:"btn btn-sm btn-primary d-none",type:"submit"},null,-1))],40,T)])])}const oe=M(E,[["render",ee]]);export{oe as F};
