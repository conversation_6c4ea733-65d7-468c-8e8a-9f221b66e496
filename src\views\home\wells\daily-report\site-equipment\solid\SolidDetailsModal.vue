<template>
  <div
    v-if="isVisible"
    class="fixed top-0 right-0 bottom-0 left-0 z-40 flex items-center"
  >
    <!-- Dark overlay background -->
    <div class="absolute inset-0 bg-sidebar-background opacity-25"></div>
    <div
      class="relative bg-card-background text-card-text-light max-h-11/12 w-11/12 flex flex-col items-center gap-3 mx-auto my-6 p-4 rounded-xl overflow-y-scroll md:overflow-hidden md:w-3/5 lg:w-2/5 lg:h-auto"
    >
      <div class="flex flex-row h-auto w-full items-center justify-between">
        <h3 class="text-lg font-bold">Solid Control Details</h3>
        <span class="cursor-pointer" @click="closeModal">
          <SvgIcon icon="closeModalIcon" />
        </span>
      </div>

      <div v-if="loading" class="text-center">
        <div class="spinner-border text-primary" role="status">
          <span class="sr-only">Loading...</span>
        </div>
      </div>
      <div v-else class="h-auto w-full flex flex-col gap-3 font-semibold">
        <h3>
          {{ `${data?.type?.name || ""} ${data?.screen || ""}` }}
        </h3>
        <div v-if="data?.inputs?.length > 0" class="flex flex-col gap-3">
          <div
            v-for="item in data?.inputs"
            :key="item?.id"
            class="flex justify-between items-center"
          >
            <div>
              <span>{{ `${item?.description} : ` }}</span>
            </div>
            <div class="flex justify-between items-center">
              <span>{{ `${item?.value} ${item?.units}` }}</span>
            </div>
            <div class="flex items-center gap-3">
              <button
                class="p-2 rounded-full bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover"
                @click="toggleSolidInputModal(item?.id || '')"
              >
                <SvgIcon icon="newReportIcon" />
              </button>
              <button
                class="p-2 rounded-full bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover"
                @click="deleteInput(item?.id || '')"
              >
                <span class="text-danger">
                  <SvgIcon icon="trashIcon" />
                </span>
              </button>
            </div>
          </div>
        </div>
        <div class="flex items-center justify-between gap-4">
          <span>Total Duration</span>
          <span>{{ `${data?.totalDurations || "0"} hrs` }}</span>
        </div>
        <p>Time Distribution</p>
        <el-empty v-if="data?.durations?.length === 0" :image-size="100" />
        <div v-else class="overflow-hidden-y flex flex-col">
          <div
            v-for="item in data?.durations"
            :key="item.id"
            class="h-auto w-full flex justify-between items-center p-1"
          >
            <span>
              {{ formatDate(item?.createdAt, "DD MMM YYYY HH:mm") }}
            </span>
            <span>{{ `${item?.duration} hrs` }}</span>
            <div class="flex items-center justify-between gap-3">
              <button
                class="p-2 rounded-full bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover"
                @click="() => toggleSolidDurationModal(item?.id)"
              >
                <SvgIcon icon="newReportIcon" />
              </button>
              <button
                class="p-2 rounded-full bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover"
                @click="(e) => deleteDuration(e, item?.id || '')"
              >
                <span class="text-danger">
                  <SvgIcon icon="trashIcon" />
                </span>
              </button>
            </div>
          </div>
        </div>
      </div>

      <div class="h-auto w-full flex items-center justify-between text-sm">
        <button
          type="button"
          class="bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover rounded-md px-3 py-2 font-semibold"
          :disabled="loading"
          @click="closeModal"
        >
          Close
        </button>
        <button
          class="bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover rounded-md px-1 py-2 font-semibold"
          type="button"
          :disabled="loading"
          @click="toggleSolidModal(data?.id || '')"
        >
          Edit Solid Equipment
        </button>
        <button
          class="bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover rounded-md px-2 py-2 font-semibold"
          type="button"
          :disabled="loading"
          @click="() => toggleSolidDurationModal()"
        >
          Add Duration
        </button>
      </div>
    </div>
    <SolidModal
      :isVisible="isSolidModalVisible"
      :close="toggleSolidModal"
      ref="solidModal"
      :loadPage="reloadPage"
    />
    <SolidDurationModal
      :isVisible="isSolidDurationModalVisible"
      :close="toggleSolidDurationModal"
      ref="solidDurationModal"
      :loadPage="reloadPage"
    />
    <SolidInputModal
      :isVisible="isSolidInputModalVisible"
      :close="toggleSolidInputModal"
      ref="solidInputModal"
      :loadPage="reloadPage"
    />
  </div>
</template>

<script lang="ts">
import SvgIcon from "@/constants/SvgIcon.vue";
import { formatDate } from "@/utils/date";
import { numberWithCommas } from "@/utils/numberFormatter";
import AlertService from "@/services/AlertService";
import { useSolidControlEquipmentStore } from "@/stores/solid-control-equipment";
import { useSolidControlEquipmentInputStore } from "@/stores/solid-control-equipment-input";
import { useSolidControlEquipmentTimeStore } from "@/stores/solid-control-equipment-time";
import { defineComponent, ref, type Ref, watch } from "vue";
import SolidDurationModal from "./SolidDurationModal.vue";
import SolidInputModal from "./SolidInputModal.vue";
import SolidModal from "./SolidModal.vue";

export default defineComponent({
  name: "solid-details-modal",
  components: { SvgIcon, SolidModal, SolidDurationModal, SolidInputModal },
  props: {
    isVisible: {
      type: Boolean,
      required: true,
    },
    close: {
      type: Function,
      required: true,
    },
    loadPage: {
      type: Function,
      required: false,
    },
    zIndex: {
      type: Number,
      default: 40,
      required: false,
    },
  },
  setup(props) {
    const solidControlEquipmentTimeStore = useSolidControlEquipmentTimeStore();
    const solidControlEquipmentInputStore =
      useSolidControlEquipmentInputStore();
    const solidControlEquipmentStore = useSolidControlEquipmentStore();
    const modal = ref(false);
    const data = ref<any>(null);
    const loading = ref<boolean>(false);
    const id = ref("");
    const solidModal: Ref<any> = ref<typeof SolidModal | null>(null);
    const solidDurationModal: Ref<any> = ref<typeof SolidDurationModal | null>(
      null
    );
    const solidInputModal: Ref<any> = ref<typeof SolidInputModal | null>(null);
    const isSolidModalVisible = ref(false);
    const isSolidDurationModalVisible = ref(false);
    const isSolidInputModalVisible = ref(false);

    watch(id, (newValue) => {
      if (newValue !== "") {
        getSolidDetails();
      }
    });

    watch(
      () => props.isVisible,
      (newValue) => {
        if (newValue === true) {
          getSolidDetails();
        }
      }
    );

    const getSolidDetails = async (): Promise<void> => {
      loading.value = true;
      solidControlEquipmentStore.getSolidControlEquipmentDetails({
        id: id.value,
        callback: {
          onSuccess: (res: any) => {
            data.value = { ...res };
          },
          onFinish: (_err: any) => {
            loading.value = false;
          },
        },
      });
    };

    const closeModal = () => {
      id.value = "";
      data.value = null;
      props.close();
    };

    const reloadPage = () => {
      getSolidDetails();
      if (props?.loadPage) {
        props.loadPage();
      }
    };

    const setId = (solidId: string) => {
      id.value = solidId.toString();
    };

    const toggleSolidDurationModal = (durationId?: string) => {
      solidDurationModal?.value?.setId(id.value, durationId || "");
      isSolidDurationModalVisible.value = !isSolidDurationModalVisible.value;
    };

    const toggleSolidInputModal = (inputId?: string) => {
      if (inputId) {
        solidInputModal?.value?.setId(id?.value, inputId);
      }

      isSolidInputModalVisible.value = !isSolidInputModalVisible.value;
    };

    const toggleSolidModal = (solidId?: string) => {
      if (solidId) {
        solidModal?.value?.setId(solidId);
      }
      isSolidModalVisible.value = !isSolidModalVisible.value;
    };

    const deleteInput = (id: string) => {
      AlertService.deletionAlert({
        onConfirmed: () => {
          deleteInputById(id);
        },
      });
    };

    const deleteInputById = (id: string) => {
      solidControlEquipmentInputStore.deleteSolidControlEquipmentInput({
        id: id,
        callback: {
          onSuccess: (_res: any) => {
            getSolidDetails();
            if (props?.loadPage) {
              props.loadPage();
            }
          },
        },
      });
    };

    const deleteDuration = (e: any, id: string) => {
      e.stopPropagation();
      AlertService.deletionAlert({
        onConfirmed: () => {
          deleteDurationById(id);
        },
      });
    };

    const deleteDurationById = (id: string) => {
      solidControlEquipmentTimeStore.deleteSolidControlEquipmentTime({
        id: id,
        callback: {
          onSuccess: (_res: any) => {
            getSolidDetails();
            if (props?.loadPage) {
              props.loadPage();
            }
          },
        },
      });
    };

    return {
      id,
      modal,
      loading,
      data,
      solidModal,
      solidInputModal,
      solidDurationModal,
      isSolidModalVisible,
      isSolidDurationModalVisible,
      isSolidInputModalVisible,
      closeModal,
      setId,
      reloadPage,
      deleteInput,
      deleteDuration,
      formatDate,
      numberWithCommas,
      toggleSolidModal,
      toggleSolidDurationModal,
      toggleSolidInputModal,
    };
  },
});
</script>
