<template>
  <div
    v-if="isVisible"
    class="fixed top-0 right-0 bottom-0 left-0 z-40 flex items-center"
  >
    <!-- Dark overlay background -->
    <div class="absolute inset-0 bg-sidebar-background opacity-25"></div>
    <div
      class="relative bg-card-background text-card-text-light max-h-4/5 w-11/12 flex flex-col items-center gap-3 mx-auto my-6 p-5 rounded-xl overflow-y-scroll lg:w-4/5 lg:h-auto"
    >
      <div class="flex flex-row h-auto w-full items-center justify-between">
        <h3 class="text-lg font-bold">
          {{ `${id ? "Edit Sample" : "New Sample"}` }}
        </h3>
        <span class="cursor-pointer" @click="closeModal">
          <SvgIcon icon="closeModalIcon" />
        </span>
      </div>

      <el-form
        @submit.prevent="submit"
        :model="targetData"
        :rules="rules"
        ref="formRef"
        class="h-auto w-full"
      >
        <div class="flex flex-col gap-3">
          <div
            class="flex flex-col gap-3 md:flex-row md:gap-4 md:justify-between"
          >
            <div class="h-auto w-full flex flex-col gap-1">
              <label class="font-bold"
                >Fluid Type<span class="text-danger-active font-light">*</span>
                <el-popover placement="top" :width="400" trigger="hover">
                  <template #reference>
                    <i class="fas fa-exclamation-circle ms-2 fs-7"></i>
                  </template>
                  <span>
                    Water-based drilling muds are the most common type. They use
                    water as the base fluid and may contain various additives.
                    The key differences for WBM include water quality,
                    filtration properties, and clay content.<br /><br />
                    Oil-based drilling muds use oil as the base fluid. Key
                    considerations for OBM include oil composition, oil/water
                    ratio, and rheological properties.<br /><br />
                    Synthetic-based drilling muds use synthetic fluids as the
                    base, such as esters or olefins. Specific data requirements
                    depend on the type of synthetic fluid used.
                  </span>
                </el-popover></label
              >
              <el-form-item prop="fluidType" class="mt-auto">
                <el-select
                  v-model="targetData.fluidType"
                  placeholder=""
                  clearable
                >
                  <el-option
                    v-for="item in fluidTypeOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                    name="fluidType"
                  />
                </el-select>
              </el-form-item>
            </div>
            <div class="h-auto w-full flex items-center gap-4 pb-2">
              <label class="font-bold"
                >Weighted Mud<span class="text-danger-active font-light"
                  >*</span
                >
                <el-popover placement="top" :width="400" trigger="hover">
                  <template #reference>
                    <i class="fas fa-exclamation-circle ms-2 fs-7"></i>
                  </template>
                  <span>
                    Weighted mud refers to drilling fluids that have been
                    weighted with solid materials, such as barite, to increase
                    their density. For weighted mud, you may need to include
                    additional data points related to the type and concentration
                    of weighting agents, as well as their impact on rheological
                    properties and filtration control.
                  </span>
                </el-popover></label
              >
              <el-form-item prop="weightedMud" style="margin-bottom: 0">
                <input
                  class="h-4 w-4"
                  type="checkbox"
                  placeholder=""
                  name="weightedMud"
                  v-model="targetData.weightedMud"
                />
              </el-form-item>
            </div>
            <div class="h-auto w-full flex flex-col gap-1">
              <label class="font-bold"
                >Sample From<span class="text-danger-active font-light">*</span>
                <el-popover placement="top" :width="400" trigger="hover">
                  <template #reference>
                    <i class="fas fa-exclamation-circle ms-2 fs-7"></i>
                  </template>
                  <span>
                    Sample From: Refers to the source or location from which the
                    mud sample was obtained. It indicates where in the drilling
                    system the mud sample was extracted or collected for
                    analysis.
                  </span>
                </el-popover></label
              >
              <el-form-item prop="sampleFrom" class="mt-auto">
                <el-select
                  v-model="targetData.sampleFrom"
                  placeholder=""
                  clearable
                >
                  <el-option
                    v-for="item in sampleFromOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                    name="sampleFrom"
                  />
                </el-select>
              </el-form-item>
            </div>
            <div class="h-auto w-full flex flex-col gap-1">
              <label class="font-bold"
                >Time Sample Taken (hh:mm)<span
                  class="text-danger-active font-light"
                  >*</span
                >
                <el-popover placement="top" :width="400" trigger="hover">
                  <template #reference>
                    <i class="fas fa-exclamation-circle ms-2 fs-7"></i>
                  </template>
                  <span>
                    The time of day that the sample is taken (24-hour)
                  </span>
                </el-popover></label
              >
              <el-form-item prop="timeSampleTaken" class="mt-auto">
                <el-time-picker
                  v-model="targetData.timeSampleTaken"
                  placeholder="hh:mm"
                  name="timeSampleTaken"
                  format="HH:mm"
                  value-format="HH:mm"
                />
              </el-form-item>
            </div>
          </div>
          <div
            class="flex flex-col gap-3 md:flex-row md:gap-4 md:justify-between"
          >
            <div class="h-auto w-full flex flex-col gap-1">
              <label class="font-bold"
                ><span>Flowline Temperature (F)</span>
                <el-popover placement="top" :width="400" trigger="hover">
                  <template #reference>
                    <i class="fas fa-exclamation-circle ms-2 fs-7"></i>
                  </template>
                  <span>
                    The temperature of the drilling mud in the flowline as it
                    returns to the surface.
                  </span>
                </el-popover></label
              >
              <el-form-item prop="flowlineTemperature" class="mt-auto">
                <el-input
                  type="number"
                  :controls="false"
                  step="any"
                  v-model="targetData.flowlineTemperature"
                  placeholder=""
                  name="flowlineTemperature"
                ></el-input>
              </el-form-item>
            </div>
            <div class="h-auto w-full flex flex-col gap-1">
              <label class="font-bold"
                ><span>Depth (ft)</span>
                <el-popover placement="top" :width="400" trigger="hover">
                  <template #reference>
                    <i class="fas fa-exclamation-circle ms-2 fs-7"></i>
                  </template>
                  <span>
                    The depth at which drilling operations are taking place.
                  </span>
                </el-popover></label
              >
              <el-form-item prop="measuredDepth" class="mt-auto">
                <el-input
                  type="number"
                  :controls="false"
                  step="any"
                  v-model="targetData.measuredDepth"
                  placeholder=""
                  name="measuredDepth"
                ></el-input>
              </el-form-item>
            </div>
            <div class="h-auto w-full flex flex-col gap-1">
              <label class="font-bold">
                MW (ppg or lbs/gal)<span class="text-danger-active font-light"
                  >*</span
                >
                <el-popover placement="bottom" :width="400" trigger="hover">
                  <template #reference>
                    <i class="fas fa-exclamation-circle ms-2 fs-7 me-4"></i>
                  </template>
                  <span>
                    Mud Weight, also known as mud density, is the density of the
                    drilling mud, typically measured in pounds per gallon (ppg).
                    For different types of drilling fluids, the density
                    requirements can vary.
                  </span>
                </el-popover>
              </label>
              <el-form-item prop="mudWeight" class="mt-auto">
                <el-input
                  type="number"
                  :controls="false"
                  step="any"
                  v-model="targetData.mudWeight"
                  placeholder=""
                  name="mudWeight"
                ></el-input
              ></el-form-item>
            </div>
            <div class="h-auto w-full flex flex-col gap-1">
              <label class="font-bold"
                ><span>Funnel Viscosity (sec/qt) </span>
                <el-popover placement="top" :width="400" trigger="hover">
                  <template #reference>
                    <i class="fas fa-exclamation-circle ms-2 fs-7"></i>
                  </template>
                  <span>
                    The funnel viscosity measures the thickness or viscosity of
                    the drilling mud. It is typically measured in seconds per
                    quart (sec/qt).
                  </span>
                </el-popover></label
              >
              <el-form-item prop="funnelViscosity" class="mt-auto">
                <el-input
                  type="number"
                  :controls="false"
                  step="any"
                  v-model="targetData.funnelViscosity"
                  placeholder=""
                  name="funnelViscosity"
                ></el-input>
              </el-form-item>
            </div>
          </div>
          <div
            class="flex flex-col gap-3 md:flex-row md:gap-4 md:justify-between"
          >
            <div class="h-auto w-full flex flex-col gap-1">
              <label class="font-bold"
                ><span>Temperature for Plastic Viscosity (f)</span>
                <el-popover placement="top" :width="400" trigger="hover">
                  <template #reference>
                    <i class="fas fa-exclamation-circle ms-2 fs-7"></i>
                  </template>
                  <span>
                    The temperature at which Plastic Viscosity (PV) is measured,
                    which affects mud rheology.
                  </span>
                </el-popover></label
              >
              <el-form-item
                prop="temperatureForPlasticViscosity"
                class="mt-auto"
              >
                <el-input
                  type="number"
                  :controls="false"
                  step="any"
                  v-model="targetData.temperatureForPlasticViscosity"
                  placeholder=""
                  name="temperatureForPlasticViscosity"
                ></el-input>
              </el-form-item>
            </div>
            <div class="h-auto w-full flex flex-col gap-1">
              <label class="font-bold"
                >PV (Plastic Viscosity) (cP)<span
                  class="text-danger-active font-light"
                  >*</span
                >
                <el-popover placement="top" :width="400" trigger="hover">
                  <template #reference>
                    <i class="fas fa-exclamation-circle ms-2 fs-7"></i>
                  </template>
                  <span>
                    Plastic Viscosity is a measure of the resistance to flow of
                    the drilling mud. It is typically measured in centipoise
                    (cP).
                  </span>
                </el-popover></label
              >
              <el-form-item prop="plasticViscosity" class="mt-auto">
                <el-input
                  type="number"
                  :controls="false"
                  step="any"
                  v-model="targetData.plasticViscosity"
                  placeholder=""
                  name="plasticViscosity"
                ></el-input>
              </el-form-item>
            </div>
            <div class="h-auto w-full flex flex-col gap-1">
              <label class="font-bold">
                YP (lbf/100ft2)<span class="text-danger-active font-light"
                  >*</span
                >
                <el-popover placement="top" :width="400" trigger="hover">
                  <template #reference>
                    <i class="fas fa-exclamation-circle ms-2 fs-7 me-4"></i>
                  </template>
                  <span>
                    Yield Point is the amount of force required to initiate mud
                    flow. It's typically measured in pounds per 100 square feet
                    (lbf/100ft^2).
                  </span>
                </el-popover> </label
              ><el-form-item prop="yieldPoint" class="mt-auto">
                <el-input
                  type="number"
                  :controls="false"
                  step="any"
                  v-model="targetData.yieldPoint"
                  placeholder=""
                  name="yieldPoint"
                ></el-input
              ></el-form-item>
            </div>
            <div class="h-auto w-full flex flex-col gap-1">
              <label class="font-bold"
                ><span>Gel Str. 10s (lbf/100ft2)</span>
                <el-popover placement="top" :width="400" trigger="hover">
                  <template #reference>
                    <i class="fas fa-exclamation-circle ms-2 fs-7"></i>
                  </template>
                  <span>
                    Gel Strength measures the mud's ability to suspend cuttings.
                    The numbers (10s, 10m, 30m) indicate the time at which the
                    measurement is taken.
                  </span>
                </el-popover></label
              >
              <el-form-item prop="gelStrength10s" class="mt-auto">
                <el-input
                  type="number"
                  :controls="false"
                  step="any"
                  v-model="targetData.gelStrength10s"
                  placeholder=""
                  name="gelStrength10s"
                ></el-input>
              </el-form-item>
            </div>
          </div>
          <div
            class="flex flex-col gap-3 md:flex-row md:gap-4 md:justify-between"
          >
            <div class="h-auto w-full flex flex-col gap-1">
              <label class="font-bold"
                ><span>Gel Str. 10m (lbf/100ft2)</span>
                <el-popover placement="top" :width="400" trigger="hover">
                  <template #reference>
                    <i class="fas fa-exclamation-circle ms-2 fs-7"></i>
                  </template>
                  <span>
                    Gel Strength measures the mud's ability to suspend cuttings.
                    The numbers (10s, 10m, 30m) indicate the time at which the
                    measurement is taken.
                  </span>
                </el-popover></label
              >
              <el-form-item prop="gelStrength10m" class="mt-auto">
                <el-input
                  type="number"
                  :controls="false"
                  step="any"
                  v-model="targetData.gelStrength10m"
                  placeholder=""
                  name="gelStrength10m"
                ></el-input>
              </el-form-item>
            </div>
            <div class="h-auto w-full flex flex-col gap-1">
              <label class="font-bold"
                ><span>Gel Str. 30m (lbf/100ft2)</span>
                <el-popover placement="top" :width="400" trigger="hover">
                  <template #reference>
                    <i class="fas fa-exclamation-circle ms-2 fs-7"></i>
                  </template>
                  <span>
                    Gel Strength measures the mud's ability to suspend cuttings.
                    The numbers (10s, 10m, 30m) indicate the time at which the
                    measurement is taken.
                  </span>
                </el-popover></label
              >
              <el-form-item prop="gelStrength30m" class="mt-auto">
                <el-input
                  type="number"
                  :controls="false"
                  step="any"
                  v-model="targetData.gelStrength30m"
                  placeholder=""
                  name="gelStrength30m"
                ></el-input>
              </el-form-item>
            </div>
            <div class="h-auto w-full flex flex-col gap-1">
              <label class="font-bold"
                >API filtrate (ml/30min)<span
                  class="text-danger-active font-light"
                  >*</span
                >
                <el-popover placement="top" :width="400" trigger="hover">
                  <template #reference>
                    <i class="fas fa-exclamation-circle ms-2 fs-7"></i>
                  </template>
                  <span>
                    The volume of mud filtrate that passes through a standard
                    filter paper in 30 minutes, measured in milliliters.<br /><br />

                    WBM: API filtrate and cake thickness are commonly measured
                    for Water-Based Mud. The filtration properties may be
                    different for various types of WBM formulations. OBM:
                    Oil-Based Mud may have different filtration characteristics
                    compared to WBM due to the presence of oil. The API filtrate
                    and cake thickness may have specific requirements for OBM.
                    SBM: Synthetic-Based Mud may also have unique filtration
                    properties, and API filtrate and cake thickness measurements
                    should consider the specific synthetic fluid used.
                  </span>
                </el-popover></label
              >
              <el-form-item prop="apiFiltrate" class="mt-auto">
                <el-input
                  type="number"
                  :controls="false"
                  step="any"
                  v-model="targetData.apiFiltrate"
                  placeholder=""
                  name="apiFiltrate"
                ></el-input>
              </el-form-item>
            </div>
            <div class="h-auto w-full flex flex-col gap-1">
              <label class="font-bold">
                API cake thickness (1/32in)<span
                  class="text-danger-active font-light"
                  >*</span
                >
                <el-popover placement="top" :width="400" trigger="hover">
                  <template #reference>
                    <i class="fas fa-exclamation-circle ms-2 fs-7 me-4"></i>
                  </template>
                  <span>
                    The thickness of the filter cake formed by the drilling mud
                    on the filter paper, measured in 1/32 of an inch.<br /><br />

                    WBM: API filtrate and cake thickness are commonly measured
                    for Water-Based Mud. The filtration properties may be
                    different for various types of WBM formulations. OBM:
                    Oil-Based Mud may have different filtration characteristics
                    compared to WBM due to the presence of oil. The API filtrate
                    and cake thickness may have specific requirements for OBM.
                    SBM: Synthetic-Based Mud may also have unique filtration
                    properties, and API filtrate and cake thickness measurements
                    should consider the specific synthetic fluid used.
                  </span>
                </el-popover> </label
              ><el-form-item prop="apiCakeThickness" class="mt-auto">
                <el-input
                  type="number"
                  :controls="false"
                  step="any"
                  v-model="targetData.apiCakeThickness"
                  placeholder=""
                  name="apiCakeThickness"
                ></el-input
              ></el-form-item>
            </div>
          </div>
          <div
            class="flex flex-col gap-3 md:flex-row md:gap-4 md:justify-between"
          >
            <div class="h-auto w-full flex flex-col gap-1">
              <label class="font-bold"
                ><span>Temperature for HTHP (F)</span>
                <el-popover placement="top" :width="400" trigger="hover">
                  <template #reference>
                    <i class="fas fa-exclamation-circle ms-2 fs-7"></i>
                  </template>
                  <span>
                    The temperature at which High-Temperature, High-Pressure
                    (HTHP) tests are conducted, which is important for
                    evaluating mud stability under downhole conditions.
                  </span>
                </el-popover></label
              >
              <el-form-item prop="temperatureForHTHP" class="mt-auto">
                <el-input
                  type="number"
                  :controls="false"
                  step="any"
                  v-model="targetData.temperatureForHTHP"
                  placeholder=""
                  name="temperatureForHTHP"
                ></el-input>
              </el-form-item>
            </div>
            <div class="h-auto w-full flex flex-col gap-1">
              <label class="font-bold"
                ><span>HTHP filtrate (ml/30min)</span>
                <el-popover placement="top" :width="400" trigger="hover">
                  <template #reference>
                    <i class="fas fa-exclamation-circle ms-2 fs-7"></i>
                  </template>
                  <span>
                    Similar to API filtrate, but measured under
                    high-temperature, high-pressure conditions.
                  </span>
                </el-popover></label
              >
              <el-form-item prop="hthpFiltrate" class="mt-auto">
                <el-input
                  type="number"
                  :controls="false"
                  step="any"
                  v-model="targetData.hthpFiltrate"
                  placeholder=""
                  name="hthpFiltrate"
                ></el-input>
              </el-form-item>
            </div>
            <div class="h-auto w-full flex flex-col gap-1">
              <label class="font-bold"
                ><span>HTHP cake thickness (1/32in)</span>
                <el-popover placement="top" :width="400" trigger="hover">
                  <template #reference>
                    <i class="fas fa-exclamation-circle ms-2 fs-7"></i>
                  </template>
                  <span>
                    Similar to API cake thickness, but measured under
                    high-temperature, high-pressure conditions.
                  </span>
                </el-popover></label
              >
              <el-form-item prop="hthpCakeThickness" class="mt-auto">
                <el-input
                  type="number"
                  :controls="false"
                  step="any"
                  v-model="targetData.hthpCakeThickness"
                  placeholder=""
                  name="hthpCakeThickness"
                ></el-input>
              </el-form-item>
            </div>
            <div class="h-auto w-full flex flex-col gap-1">
              <label class="font-bold"
                >Solids (%)<span class="text-danger-active font-light">*</span>
                <el-popover placement="top" :width="400" trigger="hover">
                  <template #reference>
                    <i class="fas fa-exclamation-circle ms-2 fs-7"></i>
                  </template>
                  <span>
                    The percentage of solid materials in the drilling mud,
                    including drill cuttings and additives.
                  </span>
                </el-popover></label
              >
              <el-form-item prop="solids" class="mt-auto">
                <el-input
                  type="number"
                  :controls="false"
                  step="any"
                  v-model="targetData.solids"
                  placeholder=""
                  name="solids"
                ></el-input>
              </el-form-item>
            </div>
          </div>
          <div
            class="flex flex-col gap-3 md:flex-row md:gap-4 md:justify-between"
          >
            <div class="h-auto w-full flex flex-col gap-1">
              <label class="font-bold">
                Oil (%)<span class="text-danger-active font-light">*</span>
                <el-popover placement="top" :width="400" trigger="hover">
                  <template #reference>
                    <i class="fas fa-exclamation-circle ms-2 fs-7 me-4"></i>
                  </template>
                  <span>
                    These percentages represent the proportions of oil in the
                    mud, which can be important for characterizing the mud
                    composition.
                  </span>
                </el-popover> </label
              ><el-form-item prop="oil" class="mt-auto">
                <el-input
                  type="number"
                  :controls="false"
                  step="any"
                  v-model="targetData.oil"
                  placeholder=""
                  name="oil"
                ></el-input
              ></el-form-item>
            </div>
            <div class="h-auto w-full flex flex-col gap-1">
              <label class="font-bold"
                >Water (%)<span class="text-danger-active font-light">*</span>
                <el-popover placement="top" :width="400" trigger="hover">
                  <template #reference>
                    <i class="fas fa-exclamation-circle ms-2 fs-7"></i>
                  </template>
                  <span>
                    These percentages represent the proportions of water in the
                    mud, which can be important for characterizing the mud
                    composition.
                  </span>
                </el-popover></label
              >
              <el-form-item prop="water" class="mt-auto">
                <el-input
                  type="number"
                  :controls="false"
                  step="any"
                  v-model="targetData.water"
                  placeholder=""
                  name="water"
                ></el-input>
              </el-form-item>
            </div>
            <div class="h-auto w-full flex flex-col gap-1">
              <label class="font-bold"
                ><span>Sand Content (%)</span>
                <el-popover placement="top" :width="400" trigger="hover">
                  <template #reference>
                    <i class="fas fa-exclamation-circle ms-2 fs-7"></i>
                  </template>
                  <span>
                    These percentages represent the proportions of sand in the
                    mud, which can be important for characterizing the mud
                    composition.
                  </span>
                </el-popover></label
              >
              <el-form-item prop="sandContent" class="mt-auto">
                <el-input
                  type="number"
                  :controls="false"
                  step="any"
                  v-model="targetData.sandContent"
                  placeholder=""
                  name="sandContent"
                ></el-input>
              </el-form-item>
            </div>
            <div class="h-auto w-full flex flex-col gap-1">
              <label class="font-bold"
                ><span>MBT capacity (lb/bbl)</span>
                <el-popover placement="top" :width="400" trigger="hover">
                  <template #reference>
                    <i class="fas fa-exclamation-circle ms-2 fs-7"></i>
                  </template>
                  <span>
                    The Mud Balance Test (MBT) measures the mud's density and is
                    used to calculate the mud weight in pounds per barrel
                    (lb/bbl).
                  </span>
                </el-popover></label
              >
              <el-form-item prop="mbtCapacity" class="mt-auto">
                <el-input
                  type="number"
                  :controls="false"
                  step="any"
                  v-model="targetData.mbtCapacity"
                  placeholder=""
                  name="mbtCapacity"
                ></el-input>
              </el-form-item>
            </div>
          </div>
          <div
            class="flex flex-col gap-3 md:flex-row md:gap-4 md:justify-between"
          >
            <div class="h-auto w-full flex flex-col gap-1">
              <label class="font-bold"
                ><span>pH</span>
                <el-popover placement="top" :width="400" trigger="hover">
                  <template #reference>
                    <i class="fas fa-exclamation-circle ms-2 fs-7"></i>
                  </template>
                  <span>
                    The pH level of the drilling mud, which can affect the
                    performance of mud additives.
                  </span>
                </el-popover></label
              >
              <el-form-item prop="pH" class="mt-auto">
                <el-input
                  type="number"
                  :controls="false"
                  step="any"
                  v-model="targetData.pH"
                  placeholder=""
                  name="pH"
                ></el-input>
              </el-form-item>
            </div>
            <div class="h-auto w-full flex flex-col gap-1">
              <label class="font-bold">
                Mud Alkalinity (Pm) (ml)
                <el-popover placement="top" :width="400" trigger="hover">
                  <template #reference>
                    <i class="fas fa-exclamation-circle ms-2 fs-7 me-4"></i>
                  </template>
                  <span>
                    Alkalinity measurements that can provide insights into the
                    mud's pH buffering capacity and stability.
                  </span>
                </el-popover> </label
              ><el-form-item prop="mudAlkalinity" class="mt-auto">
                <el-input
                  type="number"
                  :controls="false"
                  step="any"
                  v-model="targetData.mudAlkalinity"
                  placeholder=""
                  name="mudAlkalinity"
                ></el-input
              ></el-form-item>
            </div>
            <div class="h-auto w-full flex flex-col gap-1">
              <label class="font-bold"
                ><span>Filtrate Alkalinity (Pf) (ml)</span>
                <el-popover placement="top" :width="400" trigger="hover">
                  <template #reference>
                    <i class="fas fa-exclamation-circle ms-2 fs-7"></i>
                  </template>
                  <span>
                    Alkalinity measurements that can provide insights into the
                    mud's pH buffering capacity and stability.
                  </span>
                </el-popover></label
              >
              <el-form-item prop="filtrateAlkalinity" class="mt-auto">
                <el-input
                  type="number"
                  :controls="false"
                  step="any"
                  v-model="targetData.filtrateAlkalinity"
                  placeholder=""
                  name="filtrateAlkalinity"
                ></el-input>
              </el-form-item>
            </div>
            <div class="h-auto w-full flex flex-col gap-1">
              <label class="font-bold"
                ><span>Filtrate Alkalinity (Mf) (ml)</span>
                <el-popover placement="top" :width="400" trigger="hover">
                  <template #reference>
                    <i class="fas fa-exclamation-circle ms-2 fs-7"></i>
                  </template>
                  <span>
                    Alkalinity measurements that can provide insights into the
                    mud's pH buffering capacity and stability.
                  </span>
                </el-popover></label
              >
              <el-form-item prop="filtrateAlkalinity" class="mt-auto">
                <el-input
                  type="number"
                  :controls="false"
                  step="any"
                  v-model="targetData.filtrateAlkalinity"
                  placeholder=""
                  name="filtrateAlkalinity"
                ></el-input>
              </el-form-item>
            </div>
          </div>
          <div
            class="flex flex-col gap-3 md:flex-row md:gap-4 md:justify-between"
          >
            <div class="h-auto w-full flex flex-col gap-1">
              <label class="font-bold"
                ><span>Calcium (mg/L)</span>
                <el-popover placement="top" :width="400" trigger="hover">
                  <template #reference>
                    <i class="fas fa-exclamation-circle ms-2 fs-7"></i>
                  </template>
                  <span>
                    Measurements of various ions in the drilling mud, which can
                    affect mud chemistry and performance.
                  </span>
                </el-popover></label
              >
              <el-form-item prop="calcium" class="mt-auto">
                <el-input
                  type="number"
                  :controls="false"
                  step="any"
                  v-model="targetData.calcium"
                  placeholder=""
                  name="calcium"
                ></el-input>
              </el-form-item>
            </div>
            <div class="h-auto w-full flex flex-col gap-1">
              <label class="font-bold"
                >Chlorides (mg/L)<span class="text-danger-active font-light"
                  >*</span
                >
                <el-popover placement="top" :width="400" trigger="hover">
                  <template #reference>
                    <i class="fas fa-exclamation-circle ms-2 fs-7"></i>
                  </template>
                  <span>
                    Measurements of various ions in the drilling mud, which can
                    affect mud chemistry and performance.
                  </span>
                </el-popover></label
              >
              <el-form-item prop="chlorides" class="mt-auto">
                <el-input
                  type="number"
                  :controls="false"
                  step="any"
                  v-model="targetData.chlorides"
                  placeholder=""
                  name="chlorides"
                ></el-input>
              </el-form-item>
            </div>
            <div class="h-auto w-full flex flex-col gap-1">
              <label class="font-bold">
                Total Hardness (mg/L)
                <el-popover placement="top" :width="400" trigger="hover">
                  <template #reference>
                    <i class="fas fa-exclamation-circle ms-2 fs-7 me-4"></i>
                  </template>
                  <span>
                    Measurements of various ions in the drilling mud, which can
                    affect mud chemistry and performance.
                  </span>
                </el-popover> </label
              ><el-form-item prop="totalHardness" class="mt-auto">
                <el-input
                  type="number"
                  :controls="false"
                  step="any"
                  v-model="targetData.totalHardness"
                  placeholder=""
                  name="totalHardness"
                ></el-input
              ></el-form-item>
            </div>
            <div class="h-auto w-full flex flex-col gap-1">
              <label class="font-bold"
                ><span>Excess Lime (lb/bbl)</span>
                <el-popover placement="top" :width="400" trigger="hover">
                  <template #reference>
                    <i class="fas fa-exclamation-circle ms-2 fs-7"></i>
                  </template>
                  <span>
                    The amount of excess lime added to the mud to control pH and
                    alkalinity.
                  </span>
                </el-popover></label
              >
              <el-form-item prop="excessLime" class="mt-auto">
                <el-input
                  type="number"
                  :controls="false"
                  step="any"
                  v-model="targetData.excessLime"
                  placeholder=""
                  name="excessLime"
                ></el-input>
              </el-form-item>
            </div>
          </div>
          <div
            class="flex flex-col gap-3 md:flex-row md:gap-4 md:justify-between"
          >
            <div class="h-auto w-full flex flex-col gap-1">
              <label class="font-bold"
                ><span>K+ (mg/L)</span>
                <el-popover placement="top" :width="400" trigger="hover">
                  <template #reference>
                    <i class="fas fa-exclamation-circle ms-2 fs-7"></i>
                  </template>
                  <span>
                    Measurement of potassium ion concentration in the mud.
                  </span>
                </el-popover></label
              >
              <el-form-item prop="kPlus" class="mt-auto">
                <el-input
                  type="number"
                  :controls="false"
                  step="any"
                  v-model="targetData.kPlus"
                  placeholder=""
                  name="kPlus"
                ></el-input>
              </el-form-item>
            </div>
            <div class="h-auto w-full flex flex-col gap-1">
              <label class="font-bold"
                ><span>Make up water: Chlorides (mg/L)</span>
                <el-popover placement="top" :width="400" trigger="hover">
                  <template #reference>
                    <i class="fas fa-exclamation-circle ms-2 fs-7"></i>
                  </template>
                  <span>
                    Chloride concentration in makeup water used to dilute or
                    maintain the mud's properties.
                  </span>
                </el-popover></label
              >
              <el-form-item prop="makeUpWater" class="mt-auto">
                <el-input
                  type="number"
                  :controls="false"
                  step="any"
                  v-model="targetData.makeUpWater"
                  placeholder=""
                  name="makeUpWater"
                ></el-input>
              </el-form-item>
            </div>

            <div class="h-auto w-full flex flex-col gap-1">
              <label class="font-bold"
                ><span>Solids adjusted for salt (%)</span>
                <el-popover placement="top" :width="400" trigger="hover">
                  <template #reference>
                    <i class="fas fa-exclamation-circle ms-2 fs-7"></i>
                  </template>
                  <span>
                    The percentage of solids adjusted for the presence of salt,
                    which can affect the density calculations.
                  </span>
                </el-popover></label
              >
              <el-form-item prop="solidsAdjustedForSalt" class="mt-auto">
                <el-input
                  type="number"
                  :controls="false"
                  step="any"
                  v-model="targetData.solidsAdjustedForSalt"
                  placeholder=""
                  name="solidsAdjustedForSalt"
                ></el-input>
              </el-form-item>
            </div>
            <div class="h-auto w-full flex flex-col gap-1">
              <label class="font-bold">
                Fine LCM (lb/bbl)
                <el-popover placement="top" :width="400" trigger="hover">
                  <template #reference>
                    <i class="fas fa-exclamation-circle ms-2 fs-7 me-4"></i>
                  </template>
                  <span>
                    The amount of fine LCM added to the mud to control lost
                    circulation.
                  </span>
                </el-popover> </label
              ><el-form-item prop="fineLCM" class="mt-auto">
                <el-input
                  type="number"
                  :controls="false"
                  step="any"
                  v-model="targetData.fineLCM"
                  placeholder=""
                  name="fineLCM"
                ></el-input
              ></el-form-item>
            </div>
          </div>
          <div
            class="flex flex-col gap-3 md:flex-row md:gap-4 md:justify-between"
          >
            <div class="h-auto w-full flex flex-col gap-1">
              <label class="font-bold"
                ><span>Coarse LCM (lb/bbl)</span>
                <el-popover placement="top" :width="400" trigger="hover">
                  <template #reference>
                    <i class="fas fa-exclamation-circle ms-2 fs-7"></i>
                  </template>
                  <span>
                    The amount of coarse LCM added to the mud for more severe
                    lost circulation issues.
                  </span>
                </el-popover></label
              >
              <el-form-item prop="coarseLCM" class="mt-auto">
                <el-input
                  type="number"
                  :controls="false"
                  step="any"
                  v-model="targetData.coarseLCM"
                  placeholder=""
                  name="coarseLCM"
                ></el-input>
              </el-form-item>
            </div>
            <div class="h-auto w-full"></div>
            <div class="h-auto w-full"></div>
            <div class="h-auto w-full"></div>
          </div>
          <h3 class="font-bold h-auto w-full border-b-2 border-dashed pb-2">
            Rheological Properties
          </h3>
          <div
            class="flex flex-col gap-3 md:flex-row md:gap-4 md:justify-between"
          >
            <div class="h-auto w-full flex flex-col gap-1">
              <label class="font-bold"
                ><span>Shear Rate: 600 (sec^-1) (rpm)</span>
                <!-- <el-popover placement="top" :width="400" trigger="hover">
                <template #reference>
                  <i class="fas fa-exclamation-circle ms-2 fs-7"></i>
                </template>
                <span>
                  
                </span>
              </el-popover> -->
              </label>
              <el-form-item prop="shearRate600" class="mt-auto">
                <el-input
                  type="number"
                  :controls="false"
                  step="any"
                  v-model="targetData.shearRate600"
                  placeholder=""
                  name="shearRate600"
                ></el-input>
              </el-form-item>
            </div>
            <div class="h-auto w-full flex flex-col gap-1">
              <label class="font-bold">
                Shear Rate: 300 (sec^-1) (rpm)
                <!-- <el-popover placement="top" :width="400" trigger="hover">
                  <template #reference>
                    <i class="fas fa-exclamation-circle ms-2 fs-7 me-4"></i>
                  </template>
                  <span>
                    The amount of fine LCM added to the mud to control lost
                    circulation.
                  </span>
                </el-popover> --> </label
              ><el-form-item prop="shearRate300" class="mt-auto">
                <el-input
                  type="number"
                  :controls="false"
                  step="any"
                  v-model="targetData.shearRate300"
                  placeholder=""
                  name="shearRate300"
                ></el-input
              ></el-form-item>
            </div>
            <div class="h-auto w-full flex flex-col gap-1">
              <label class="font-bold"
                ><span>Shear Rate: 200 (sec^-1) (rpm)</span>
                <!-- <el-popover placement="top" :width="400" trigger="hover">
                <template #reference>
                  <i class="fas fa-exclamation-circle ms-2 fs-7"></i>
                </template>
                <span>
                  The amount of coarse LCM added to the mud for more severe lost
                  circulation issues.
                </span>
              </el-popover> -->
              </label>
              <el-form-item prop="shearRate200" class="mt-auto">
                <el-input
                  type="number"
                  :controls="false"
                  step="any"
                  v-model="targetData.shearRate200"
                  placeholder=""
                  name="shearRate200"
                ></el-input>
              </el-form-item>
            </div>
            <div class="h-auto w-full flex flex-col gap-1">
              <label class="font-bold"
                ><span>Shear Rate: 100 (sec^-1) (rpm)</span>
                <!-- <el-popover placement="top" :width="400" trigger="hover">
                <template #reference>
                  <i class="fas fa-exclamation-circle ms-2 fs-7"></i>
                </template>
                <span>
                  
                </span>
              </el-popover> -->
              </label>
              <el-form-item prop="shearRate100" class="mt-auto">
                <el-input
                  type="number"
                  :controls="false"
                  step="any"
                  v-model="targetData.shearRate100"
                  placeholder=""
                  name="shearRate100"
                ></el-input>
              </el-form-item>
            </div>
          </div>
          <div
            class="flex flex-col gap-3 md:flex-row md:gap-4 md:justify-between"
          >
            <div class="h-auto w-full flex flex-col gap-1">
              <label class="font-bold">
                Shear Rate: 6 (sec^-1) (rpm)
                <!-- <el-popover placement="top" :width="400" trigger="hover">
                  <template #reference>
                    <i class="fas fa-exclamation-circle ms-2 fs-7 me-4"></i>
                  </template>
                  <span>
                    The amount of fine LCM added to the mud to control lost
                    circulation.
                  </span>
                </el-popover> --> </label
              ><el-form-item prop="shearRate6" class="mt-auto">
                <el-input
                  type="number"
                  :controls="false"
                  step="any"
                  v-model="targetData.shearRate6"
                  placeholder=""
                  name="shearRate6"
                ></el-input
              ></el-form-item>
            </div>
            <div class="h-auto w-full flex flex-col gap-1">
              <label class="font-bold"
                ><span>Shear Rate: 3 (sec^-1) (rpm)</span>
                <!-- <el-popover placement="top" :width="400" trigger="hover">
                <template #reference>
                  <i class="fas fa-exclamation-circle ms-2 fs-7"></i>
                </template>
                <span>
                  The amount of coarse LCM added to the mud for more severe lost
                  circulation issues.
                </span>
              </el-popover> -->
              </label>
              <el-form-item prop="shearRate3" class="mt-auto">
                <el-input
                  type="number"
                  :controls="false"
                  step="any"
                  v-model="targetData.shearRate3"
                  placeholder=""
                  name="shearRate3"
                ></el-input>
              </el-form-item>
            </div>
            <div class="h-auto w-full flex flex-col gap-1">
              <label class="font-bold"
                ><span>Plastic Viscosity (PV) (cP)</span>
                <el-popover placement="top" :width="400" trigger="hover">
                  <template #reference>
                    <i class="fas fa-exclamation-circle ms-2 fs-7"></i>
                  </template>
                  <span>
                    Plastic Viscosity is a measure of the resistance to flow of
                    the drilling mud. It is typically measured in centipoise
                    (cP).
                  </span>
                </el-popover>
              </label>
              <el-form-item prop="plasticViscosity" class="mt-auto">
                <el-input
                  type="number"
                  :controls="false"
                  step="any"
                  v-model="targetData.plasticViscosity"
                  placeholder=""
                  name="plasticViscosity"
                ></el-input>
              </el-form-item>
            </div>
            <div class="h-auto w-full flex flex-col gap-1">
              <label class="font-bold">
                Yield Point (YP) (lbf/100ft2)
                <el-popover placement="top" :width="400" trigger="hover">
                  <template #reference>
                    <i class="fas fa-exclamation-circle ms-2 fs-7 me-4"></i>
                  </template>
                  <span>
                    Yield Point is the amount of force required to initiate mud
                    flow. It's typically measured in pounds per 100 square feet
                    (lbf/100ft^2).
                  </span>
                </el-popover> </label
              ><el-form-item prop="yieldPoint" class="mt-auto">
                <el-input
                  type="number"
                  :controls="false"
                  step="any"
                  v-model="targetData.yieldPoint"
                  placeholder=""
                  name="yieldPoint"
                ></el-input
              ></el-form-item>
            </div>
          </div>
          <div
            class="flex flex-col gap-3 md:flex-row md:gap-4 md:justify-between"
          >
            <div class="h-auto w-full flex flex-col gap-1">
              <label class="font-bold"
                ><span>Apparent Viscosity (AV) (cP)</span>
                <el-popover placement="top" :width="400" trigger="hover">
                  <template #reference>
                    <i class="fas fa-exclamation-circle ms-2 fs-7"></i>
                  </template>
                  <span>
                    Apparent Viscosity (AV) (cP) is a measure of the overall
                    resistance to flow of the drilling mud. It's the sum of the
                    plastic viscosity and half of the yield point.
                  </span>
                </el-popover>
              </label>
              <el-form-item prop="apparentViscosity" class="mt-auto">
                <el-input
                  type="number"
                  :controls="false"
                  step="any"
                  v-model="targetData.apparentViscosity"
                  placeholder="AV (cP) = PV + YP / 2"
                  name="apparentViscosity"
                  disabled
                ></el-input>
              </el-form-item>
            </div>
            <div class="h-auto w-full flex flex-col gap-1">
              <label class="font-bold"
                ><span>Shear Rate</span>
                <el-popover placement="top" :width="400" trigger="hover">
                  <template #reference>
                    <i class="fas fa-exclamation-circle ms-2 fs-7"></i>
                  </template>
                  <span>
                    Shear rate is a measure of how fast the mud is flowing.
                  </span>
                </el-popover>
              </label>
              <el-form-item prop="shearRate" class="mt-auto">
                <el-input
                  type="number"
                  :controls="false"
                  step="any"
                  v-model="targetData.shearRate"
                  placeholder="γ = (2 x Pump Speed (rpm)) / (Dial Reading (sec^-1))"
                  name="shearRate"
                  disabled
                ></el-input>
              </el-form-item>
            </div>
            <div class="h-auto w-full flex flex-col gap-1">
              <label class="font-bold">
                Shear Stress τ (lbf/100ft²)
                <el-popover placement="top" :width="400" trigger="hover">
                  <template #reference>
                    <i class="fas fa-exclamation-circle ms-2 fs-7 me-4"></i>
                  </template>
                  <span>
                    Shear stress is the force applied per unit area on the mud.
                  </span>
                </el-popover> </label
              ><el-form-item prop="shearStress" class="mt-auto">
                <el-input
                  type="number"
                  :controls="false"
                  step="any"
                  v-model="targetData.shearStress"
                  placeholder="τ (lbf/100ft²) = PV + (YP x Shear Rate)"
                  name="shearStress"
                  disabled
                ></el-input
              ></el-form-item>
            </div>
          </div>
        </div>
        <div class="h-auto w-full flex flex-row items-center gap-2">
          <button
            type="button"
            class="bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover rounded-md px-4 py-2 font-semibold"
            @click="closeModal"
            :disabled="loading"
          >
            Discard
          </button>
          <button
            class="bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover rounded-md px-4 py-2 font-semibold"
            type="submit"
            :disabled="loading"
          >
            <span v-if="!loading" class="indicator-label"> Save </span>
            <span v-if="loading" class="indicator-progress">
              Please wait...
              <span
                class="spinner-border spinner-border-sm align-middle ms-2"
              ></span>
            </span>
          </button>
        </div>
      </el-form>
    </div>
  </div>
</template>

<script lang="ts">
import SvgIcon from "@/constants/SvgIcon.vue";
import { fluidTypeOptions, sampleFromOptions } from "@/constants/sample";
import { useSampleStore } from "@/stores/sample";
import type { SampleInformation } from "@/types/sample";
import { defineComponent, inject, ref, watch } from "vue";
import { useRoute } from "vue-router";
import type { Provide } from "@/types/injection-types";

export default defineComponent({
  name: "sample-modal",
  components: { SvgIcon },
  props: {
    isVisible: {
      type: Boolean,
      required: true,
    },
    close: {
      type: Function,
      required: true,
    },
    loadPage: {
      type: Function,
      default: () => {},
      required: true,
    },
  },
  setup(props) {
    const route = useRoute();
    const dailyReportProvide = inject<Provide.DailyReport>("dailyReport");
    const sampleStore = useSampleStore();
    const initialForm: SampleInformation = {
      id: "",
      dailyReportId: "",
      fluidType: null,
      weightedMud: false,
      sampleFrom: "",
      timeSampleTaken: "",
      flowlineTemperature: null,
      measuredDepth: null,
      mudWeight: null,
      funnelViscosity: null,
      temperatureForPlasticViscosity: null,
      plasticViscosity: null,
      yieldPoint: null,
      gelStrength10s: null,
      gelStrength10m: null,
      gelStrength30m: null,
      apiFiltrate: null,
      apiCakeThickness: null,
      temperatureForHTHP: null,
      hthpFiltrate: null,
      hthpCakeThickness: null,
      solids: null,
      oil: null,
      water: null,
      sandContent: null,
      mbtCapacity: null,
      pH: null,
      mudAlkalinity: null,
      filtrateAlkalinity: null,
      calcium: null,
      chlorides: null,
      totalHardness: null,
      excessLime: null,
      kPlus: null,
      makeUpWater: null,
      solidsAdjustedForSalt: null,
      fineLCM: null,
      coarseLCM: null,
      linearGelStrengthPercent: null,
      linearGelStrengthLbBbl: null,
      highGelStrengthPercent: null,
      highGelStrengthLbBbl: null,
      bentoniteConcentrationPercent: null,
      bentoniteConcentrationLbBbl: null,
      drillSolidsConcentrationPercent: null,
      drillSolidsConcentrationLbBbl: null,
      drillSolidsToBentoniteRatio: null,
      averageSpecificGravityOfSolids: null,
      shearRate600: null,
      shearRate300: null,
      shearRate200: null,
      shearRate100: null,
      shearRate6: null,
      shearRate3: null,
      apparentViscosity: null,
      shearRate: null,
      shearStress: null,
    };
    const targetData = ref<SampleInformation>(
      JSON.parse(JSON.stringify(initialForm))
    );
    const formRef = ref<null | HTMLFormElement>(null);
    const loading = ref<boolean>(false);
    const id = ref("");

    watch(id, (newValue) => {
      if (newValue !== "") {
        getSampleDetails();
      }
    });

    watch(
      () => props.isVisible,
      (newValue) => {
        if (newValue === false) {
          id.value = "";
          reset();
          formRef?.value?.resetFields();
        }
      }
    );

    const getSampleDetails = async (): Promise<void> => {
      sampleStore.getSampleDetails({
        id: id.value,
        callback: {
          onSuccess: (res: any) => {
            targetData.value = { ...res };
          },
        },
      });
    };

    const updateSample = async (param: any): Promise<void> => {
      loading.value = true;
      sampleStore.updateSample({
        id: id.value,
        params: param,
        callback: {
          onSuccess: (_res: any) => {
            if (props?.loadPage) {
              props?.loadPage();
            }
            closeModal();
          },
          onFinish: (_err: any) => {
            loading.value = false;
          },
        },
      });
    };

    const createSample = async (param: any): Promise<void> => {
      loading.value = true;
      sampleStore.createSample({
        params: param,
        callback: {
          onSuccess: (_res: any) => {
            if (props?.loadPage) {
              props?.loadPage();
            }
            closeModal();
          },
          onFinish: (_err: any) => {
            loading.value = false;
          },
        },
      });
    };

    const closeModal = () => {
      props.close();
    };

    const setId = (sampleId: string) => {
      id.value = sampleId.toString();
    };

    const rules = ref({
      fluidType: [
        {
          required: true,
          message: "Please select Fluid Type",
          trigger: ["change", "blur"],
        },
      ],
      sampleFrom: [
        {
          required: true,
          message: "Please select Sample From",
          trigger: ["change", "blur"],
        },
      ],
      mudWeight: [
        {
          required: true,
          message: "Please input MW",
          trigger: ["change", "blur"],
        },
      ],
      plasticViscosity: [
        {
          required: true,
          message: "Please input Plastic Viscosity",
          trigger: ["change", "blur"],
        },
      ],
      solids: [
        {
          required: true,
          message: "Please input Solids",
          trigger: ["change", "blur"],
        },
      ],
      oil: [
        {
          required: true,
          message: "Please input Oil",
          trigger: ["change", "blur"],
        },
      ],
      water: [
        {
          required: true,
          message: "Please input Water",
          trigger: ["change", "blur"],
        },
      ],
      chlorides: [
        {
          required: true,
          message: "Please input Chlorides",
          trigger: ["change", "blur"],
        },
      ],
      timeSampleTaken: [
        {
          required: true,
          message: "Please select Time Sample Taken",
          trigger: ["change", "blur"],
        },
      ],
    });

    const submit = () => {
      if (!formRef.value) {
        return;
      }

      formRef.value.validate((valid: boolean) => {
        if (valid) {
          const param = {
            ...targetData.value,
            sampleFrom: targetData?.value?.sampleFrom?.toString(),
            fluidType: Number(targetData?.value?.fluidType),
            flowlineTemperature: Number(targetData?.value?.flowlineTemperature),
            measuredDepth: Number(targetData?.value?.measuredDepth),
            mudWeight: Number(targetData?.value?.mudWeight),
            funnelViscosity: Number(targetData?.value?.funnelViscosity),
            temperatureForPlasticViscosity: Number(
              targetData?.value?.temperatureForPlasticViscosity
            ),
            plasticViscosity: Number(targetData?.value?.plasticViscosity),
            yieldPoint: Number(targetData?.value?.yieldPoint),
            gelStrength10s: Number(targetData?.value?.gelStrength10s),
            gelStrength10m: Number(targetData?.value?.gelStrength10m),
            gelStrength30m: Number(targetData?.value?.gelStrength30m),
            apiFiltrate: Number(targetData?.value?.apiFiltrate),
            apiCakeThickness: Number(targetData?.value?.apiCakeThickness),
            temperatureForHTHP: Number(targetData?.value?.temperatureForHTHP),
            hthpFiltrate: Number(targetData?.value?.hthpFiltrate),
            hthpCakeThickness: Number(targetData?.value?.hthpCakeThickness),
            solids: Number(targetData?.value?.solids),
            oil: Number(targetData?.value?.oil),
            water: Number(targetData?.value?.water),
            sandContent: Number(targetData?.value?.sandContent),
            mbtCapacity: Number(targetData?.value?.mbtCapacity),
            pH: Number(targetData?.value?.pH),
            mudAlkalinity: Number(targetData?.value?.mudAlkalinity),
            filtrateAlkalinity: Number(targetData?.value?.filtrateAlkalinity),
            calcium: Number(targetData?.value?.calcium),
            chlorides: Number(targetData?.value?.chlorides),
            totalHardness: Number(targetData?.value?.totalHardness),
            excessLime: Number(targetData?.value?.excessLime),
            kPlus: Number(targetData?.value?.kPlus),
            makeUpWater: Number(targetData?.value?.makeUpWater),
            solidsAdjustedForSalt: Number(
              targetData?.value?.solidsAdjustedForSalt
            ),
            fineLCM: Number(targetData?.value?.fineLCM),
            coarseLCM: Number(targetData?.value?.coarseLCM),
            linearGelStrengthPercent: Number(
              targetData?.value?.linearGelStrengthPercent
            ),
            linearGelStrengthLbBbl: Number(
              targetData?.value?.linearGelStrengthLbBbl
            ),
            highGelStrengthPercent: Number(
              targetData?.value?.highGelStrengthPercent
            ),
            highGelStrengthLbBbl: Number(
              targetData?.value?.highGelStrengthLbBbl
            ),
            bentoniteConcentrationPercent: Number(
              targetData?.value?.bentoniteConcentrationPercent
            ),
            bentoniteConcentrationLbBbl: Number(
              targetData?.value?.bentoniteConcentrationLbBbl
            ),
            drillSolidsConcentrationPercent: Number(
              targetData?.value?.drillSolidsConcentrationPercent
            ),
            drillSolidsConcentrationLbBbl: Number(
              targetData?.value?.drillSolidsConcentrationLbBbl
            ),
            drillSolidsToBentoniteRatio: Number(
              targetData?.value?.drillSolidsToBentoniteRatio
            ),
            averageSpecificGravityOfSolids: Number(
              targetData?.value?.averageSpecificGravityOfSolids
            ),
            shearRate600: Number(targetData?.value?.shearRate600),
            shearRate300: Number(targetData?.value?.shearRate300),
            shearRate200: Number(targetData?.value?.shearRate200),
            shearRate100: Number(targetData?.value?.shearRate100),
            shearRate6: Number(targetData?.value?.shearRate6),
            shearRate3: Number(targetData?.value?.shearRate3),
            apparentViscosity: Number(targetData?.value?.apparentViscosity),
            shearRate: Number(targetData?.value?.shearRate),
            shearStress: Number(targetData?.value?.shearStress),
          };

          if (id?.value) {
            updateSample({
              ...param,
              dailyReportId: dailyReportProvide?.getDailyReportId(),
            });
          } else {
            dailyReportProvide?.createDailyReport({
              wellId: route?.params?.id as string,
              callback: {
                onSuccess: (_res: string) => {
                  createSample({
                    ...param,
                    dailyReportId: dailyReportProvide?.getDailyReportId(),
                  });
                },
              },
            });
          }
        }
      });
    };

    const reset = () => {
      targetData.value = JSON.parse(JSON.stringify(initialForm));
    };

    return {
      id,
      rules,
      loading,
      targetData,
      formRef,
      fluidTypeOptions,
      sampleFromOptions,
      closeModal,
      submit,
      setId,
      reset,
    };
  },
});
</script>
