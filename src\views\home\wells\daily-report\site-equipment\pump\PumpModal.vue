<template>
  <div
    v-if="isVisible"
    class="fixed top-0 right-0 bottom-0 left-0 z-40 flex items-center"
  >
    <!-- Dark overlay background -->
    <div class="absolute inset-0 bg-sidebar-background opacity-25"></div>
    <div
      class="relative bg-card-background text-card-text-light max-h-11/12 w-11/12 flex flex-col items-center gap-3 mx-auto my-6 p-5 rounded-xl overflow-y-scroll md:overflow-hidden md:w-4/5 lg:w-2/5 lg:h-auto
7"
    >
      <div class="flex flex-row h-auto w-full items-center justify-between">
        <h3 class="text-lg font-bold">
          {{ `${id ? "Edit Pump" : "Add Pump"}` }}
        </h3>
        <span class="cursor-pointer" @click="closeModal">
          <SvgIcon icon="closeModalIcon" />
        </span>
      </div>

      <el-form
        id="pump_form"
        @submit.prevent="submit"
        :model="targetData"
        :rules="rules"
        ref="formRef"
        class="h-auto w-full"
      >
        <div class="h-auto w-full flex flex-col md:flex-row md:gap-4">
          <div class="h-auto w-full flex flex-col gap-3">
            <div class="flex flex-col gap-1">
              <label class="font-bold">Description </label>
              <el-form-item prop="description" class="mt-auto">
                <el-input
                  v-model="targetData.description"
                  placeholder=""
                  name="description"
                ></el-input>
              </el-form-item>
            </div>
            <div class="flex flex-col gap-1">
              <label class="font-bold"
                ><span>Model</span
                ><el-popover placement="top" :width="250" trigger="hover">
                  <template #reference>
                    <i class="fas fa-exclamation-circle ms-2 fs-7"></i>
                  </template>
                  <span>Specifies the model or type of the pump. </span>
                </el-popover></label
              >
              <el-form-item prop="model">
                <el-input
                  v-model="targetData.model"
                  placeholder=""
                  name="model"
                ></el-input>
              </el-form-item>
            </div>
            <div class="flex flex-col gap-1">
              <label class="font-bold"> Linear ID (in) </label>

              <el-form-item prop="linearID">
                <el-input
                  type="number"
                  :controls="false"
                  step="any"
                  v-model="targetData.linearID"
                  placeholder=""
                  name="linearID"
                ></el-input>
              </el-form-item>
            </div>
            <div class="flex flex-col gap-1">
              <label class="font-bold"
                ><span>Rod OD (in)</span
                ><el-popover placement="top" :width="250" trigger="hover">
                  <template #reference>
                    <i class="fas fa-exclamation-circle ms-2 fs-7"></i>
                  </template>
                  <span
                    >The outside diameter of the pump's rod or plunger.
                  </span>
                </el-popover></label
              >
              <el-form-item prop="rodOD">
                <el-input
                  type="number"
                  :controls="false"
                  step="any"
                  v-model="targetData.rodOD"
                  placeholder=""
                  name="rodOD"
                ></el-input>
              </el-form-item>
            </div>
            <div class="flex flex-col gap-1">
              <label class="font-bold"> Stroke Length (in) </label>

              <el-form-item prop="strokeLength">
                <el-input
                  type="number"
                  :controls="false"
                  step="any"
                  v-model="targetData.strokeLength"
                  placeholder=""
                  name="strokeLength"
                ></el-input>
              </el-form-item>
            </div>
          </div>
          <div class="h-auto w-full flex flex-col gap-3">
            <div class="h-auto w-full flex gap-6 items-center md:h-20">
              <label class="font-bold"
                >In Use
                <el-popover placement="top" :width="200" trigger="hover">
                  <template #reference>
                    <i class="fas fa-exclamation-circle ms-2 fs-7"></i>
                  </template>
                  <span>
                    Indicates whether the pump is currently in use or not.
                  </span>
                </el-popover></label
              >
              <el-form-item prop="inUse" :style="'margin-bottom: 0;'">
                <input
                  class="h-4 w-4"
                  type="checkbox"
                  placeholder=""
                  name="inUse"
                  v-model="targetData.inUse"
                />
              </el-form-item>
            </div>
            <div class="flex flex-col gap-1">
              <label class="font-bold"
                ><span>Efficiency (%)</span
                ><el-popover placement="top" :width="250" trigger="hover">
                  <template #reference>
                    <i class="fas fa-exclamation-circle ms-2 fs-7"></i>
                  </template>
                  <span
                    >The efficiency of the pump, indicating how effectively it
                    converts mechanical power to hydraulic power.
                  </span>
                </el-popover></label
              >
              <el-form-item prop="efficiency">
                <el-input
                  type="number"
                  :controls="false"
                  step="any"
                  v-model="targetData.efficiency"
                  placeholder=""
                  name="efficiency"
                  :min="0"
                  :max="100"
                ></el-input>
              </el-form-item>
            </div>
            <div class="flex flex-col gap-1">
              <label class="font-bold"
                ><span>Stroke (stroke/min)</span
                ><el-popover placement="top" :width="250" trigger="hover">
                  <template #reference>
                    <i class="fas fa-exclamation-circle ms-2 fs-7"></i>
                  </template>
                  <span
                    >The number of strokes (up-and-down movements) the pump
                    makes per minute.
                  </span>
                </el-popover></label
              >

              <el-form-item prop="stroke">
                <el-input
                  type="number"
                  :controls="false"
                  step="any"
                  v-model="targetData.stroke"
                  placeholder=""
                  name="stroke"
                ></el-input>
              </el-form-item>
            </div>
            <div class="flex flex-col gap-1">
              <label class="font-bold"
                ><span>Displacement (bbl/stroke)</span
                ><el-popover placement="top" :width="250" trigger="hover">
                  <template #reference>
                    <i class="fas fa-exclamation-circle ms-2 fs-7"></i>
                  </template>
                  <span
                    >The volume of fluid displaced by the pump with each stroke.
                  </span>
                </el-popover></label
              >
              <el-form-item prop="displacement">
                <el-input
                  type="number"
                  :controls="false"
                  step="any"
                  v-model="targetData.displacement"
                  placeholder=""
                  name="displacement"
                ></el-input>
              </el-form-item>
            </div>
            <div class="flex flex-col gap-1">
              <label class="font-bold"
                ><span>Rate (gpm)</span
                ><el-popover placement="top" :width="250" trigger="hover">
                  <template #reference>
                    <i class="fas fa-exclamation-circle ms-2 fs-7"></i>
                  </template>
                  <span>The flow rate of fluid delivered by the pump. </span>
                </el-popover></label
              >
              <el-form-item prop="rate">
                <el-input
                  type="number"
                  :controls="false"
                  step="any"
                  v-model="targetData.rate"
                  placeholder=""
                  name="rate"
                ></el-input>
              </el-form-item>
            </div>
          </div>
        </div>

        <div class="h-auto w-full flex flex-row items-center gap-2">
          <button
            type="button"
            class="bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover rounded-md px-4 py-2 font-semibold"
            @click="closeModal"
            :disabled="loading"
          >
            Discard
          </button>
          <button
            class="bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover rounded-md px-4 py-2 font-semibold"
            type="submit"
            :disabled="loading"
          >
            <span v-if="!loading" class="indicator-label"> Save </span>
            <span v-if="loading" class="indicator-progress">
              Please wait...
              <span
                class="spinner-border spinner-border-sm align-middle ms-2"
              ></span>
            </span>
          </button>
        </div>
      </el-form>
    </div>
  </div>
</template>

<script lang="ts">
import SvgIcon from "@/constants/SvgIcon.vue";
import { usePumpStore } from "@/stores/pump";
import { defineComponent, inject, ref, watch } from "vue";
import { useRoute } from "vue-router";
import type { Provide } from "@/types/injection-types";

export default defineComponent({
  name: "pump-modal",
  components: { SvgIcon },
  props: {
    isVisible: {
      type: Boolean,
      required: true,
    },
    close: {
      type: Function,
      required: true,
    },
    loadPage: {
      type: Function,
      default: () => {},
      required: true,
    },
  },
  setup(props) {
    const route = useRoute();
    const pumpStore = usePumpStore();
    const modal = ref(false);
    const initialForm = {
      description: "",
      inUse: false,
      model: "",
      linearID: null,
      rodOD: null,
      strokeLength: null,
      efficiency: null,
      stroke: null,
      displacement: null,
      rate: null,
    };
    const targetData = ref<any>(JSON.parse(JSON.stringify(initialForm)));
    const formRef = ref<null | HTMLFormElement>(null);
    const loading = ref<boolean>(false);
    const id = ref("");
    const dailyReportProvide = inject<Provide.DailyReport>("dailyReport");

    watch(id, (newValue) => {
      if (newValue !== "") {
        getPumpDetails();
      }
    });

    watch(
      () => props.isVisible,
      (newValue) => {
        if (newValue === false) {
          id.value = "";
          reset();
          formRef?.value?.resetFields();
        }
      }
    );

    const getPumpDetails = async (): Promise<void> => {
      pumpStore.getPumpDetails({
        id: id.value,
        callback: {
          onSuccess: (res: any) => {
            targetData.value = JSON.parse(JSON.stringify(res));
          },
        },
      });
    };

    const updatePump = async (param: any): Promise<void> => {
      loading.value = true;
      pumpStore.updatePump({
        id: id.value,
        params: param,
        callback: {
          onSuccess: (_res: any) => {
            if (props?.loadPage) {
              props?.loadPage();
            }
            closeModal();
          },
          onFinish: (_err: any) => {
            loading.value = false;
          },
        },
      });
    };

    const createPump = async (param: any): Promise<void> => {
      loading.value = true;
      pumpStore.createPump({
        params: param,
        callback: {
          onSuccess: (_res: any) => {
            if (props?.loadPage) {
              props?.loadPage();
            }
            closeModal();
          },
          onFinish: (_err: any) => {
            loading.value = false;
          },
        },
      });
    };

    const closeModal = () => {
      props.close();
    };

    const setId = (pumpId: string) => {
      id.value = pumpId.toString();
    };

    const rules = ref({
      description: [
        {
          required: true,
          message: "Please type Description",
          trigger: "blur",
        },
      ],
    });

    const submit = () => {
      if (!formRef.value) {
        return;
      }

      formRef.value.validate((valid: boolean) => {
        if (valid) {
          const param = {
            description: targetData?.value?.description,
            inUse: targetData?.value?.inUse,
            model: targetData?.value?.model,
            linearID: Number(targetData?.value?.linearID),
            rodOD: Number(targetData?.value?.rodOD),
            strokeLength: Number(targetData?.value?.strokeLength),
            efficiency: Number(targetData?.value?.efficiency),
            stroke: Number(targetData?.value?.stroke),
            displacement: Number(targetData?.value?.displacement),
            rate: Number(targetData?.value?.rate),
          };

          if (id?.value) {
            updatePump({
              ...param,
              dailyReportId: dailyReportProvide?.getDailyReportId(),
            });
          } else {
            dailyReportProvide?.createDailyReport({
              wellId: route?.params?.id as string,
              callback: {
                onSuccess: (_res: string) => {
                  createPump({
                    ...param,
                    dailyReportId: dailyReportProvide?.getDailyReportId(),
                  });
                },
              },
            });
          }
        }
      });
    };

    const reset = () => {
      targetData.value = JSON.parse(JSON.stringify(initialForm));
    };

    return {
      id,
      modal,
      rules,
      loading,
      targetData,
      formRef,
      closeModal,
      submit,
      setId,
      reset,
    };
  },
});
</script>
