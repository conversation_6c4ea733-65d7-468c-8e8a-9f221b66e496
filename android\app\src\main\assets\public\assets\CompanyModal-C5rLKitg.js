import{S as _}from"./SvgIcon-DYvlNVZf.js";import{d as k,q as c,A as V,Z as D,_ as M,c as v,l as y,o as h,a,b as d,t as q,r as b,w as x,B as C,p as B}from"./index-BmHWvWFS.js";import{u as I}from"./company-KQxnMnUF.js";const P=k({name:"company-modal",components:{SvgIcon:_},props:{isVisible:{type:Boolean,required:!0},close:{type:Function,required:!0},loadPage:{type:Function,required:!1}},setup(e){const o=I(),l=c({name:"",registerNumber:"",description:""}),i=c(null),r=c(!1),n=c(""),u=()=>{e.close(),w()};V(n,t=>{t&&g()});const g=async()=>{n.value&&o.getCompanyById({id:n.value,callback:{onSuccess:t=>{l.value={name:t==null?void 0:t.name,registerNumber:t==null?void 0:t.registerNumber,description:t==null?void 0:t.description}}}})},p=t=>{n.value=t.toString()},f=c({name:[{required:!0,message:"Please type Company Name",trigger:["blur","change"]}],description:[{required:!0,message:"Please select Description",trigger:["blur","change"]}]}),s=()=>{i.value&&i.value.validate(t=>{t&&(n.value?S():N())})},N=async()=>{var t;r.value=!0,o.createCompany({params:{...l.value,registerNumber:((t=l==null?void 0:l.value)==null?void 0:t.registerNumber)||null},callback:{onSuccess:()=>{var m;(m=e==null?void 0:e.loadPage)==null||m.call(e),u()},onFinish:()=>{r.value=!1}}})},S=async()=>{var t;r.value=!0,o.editCompany({id:n.value,params:{...l.value,registerNumber:((t=l==null?void 0:l.value)==null?void 0:t.registerNumber)||null},callback:{onSuccess:()=>{var m;(m=e==null?void 0:e.loadPage)==null||m.call(e),u()},onFinish:()=>{r.value=!1}}})},w=()=>{var t;n.value="",l.value={name:"",registerNumber:"",description:""},(t=i==null?void 0:i.value)==null||t.resetFields()};return{id:n,rules:f,loading:r,targetData:l,formRef:i,rolesOptions:D,closeModal:u,submit:s,setId:p,resetData:w}}}),F={key:0,class:"fixed top-0 right-0 bottom-0 left-0 z-40 flex items-center"},$={class:"relative bg-card-background text-card-text-light h-auto w-11/12 flex flex-col items-center gap-3 mx-auto p-5 rounded-xl md:w-4/5 md:text-lg lg:w-2/5"},E={class:"h-auto w-full flex flex-row items-center justify-between"},U={class:"text-lg font-bold"},j={class:"flex flex-col gap-2"},z={class:"flex flex-col gap-2"},A={class:"flex flex-row items-start mt-4 gap-3"},O=["disabled"],T=["disabled"],Z={key:0,class:"indicator-label"},G={key:1,class:"indicator-progress"};function H(e,o,l,i,r,n){const u=b("SvgIcon"),g=b("el-input"),p=b("el-form-item"),f=b("el-form");return e.isVisible?(h(),v("div",F,[o[7]||(o[7]=a("div",{class:"absolute inset-0 bg-sidebar-background opacity-25"},null,-1)),a("div",$,[a("div",E,[a("h3",U,q(e.id?"Edit Company":"New Company"),1),a("span",{class:"cursor-pointer",onClick:o[0]||(o[0]=(...s)=>e.closeModal&&e.closeModal(...s))},[d(u,{icon:"closeModalIcon"})])]),d(f,{id:"product_form",onSubmit:B(e.submit,["prevent"]),model:e.targetData,rules:e.rules,ref:"formRef",class:"h-auto w-full"},{default:x(()=>[a("div",j,[o[4]||(o[4]=a("label",{class:"font-semibold"},[C("Company Name "),a("span",{class:"text-danger-active font-light"},"*")],-1)),d(p,{prop:"name"},{default:x(()=>[d(g,{modelValue:e.targetData.name,"onUpdate:modelValue":o[1]||(o[1]=s=>e.targetData.name=s),placeholder:"",name:"name","show-word-limit":"",maxlength:"150"},null,8,["modelValue"])]),_:1})]),a("div",z,[o[5]||(o[5]=a("label",{class:"font-semibold"},[C("Description "),a("span",{class:"text-danger-active font-light"},"*")],-1)),d(p,{prop:"description",class:"mt-auto"},{default:x(()=>[d(g,{class:"w-100",modelValue:e.targetData.description,"onUpdate:modelValue":o[2]||(o[2]=s=>e.targetData.description=s),placeholder:"",name:"description"},null,8,["modelValue"])]),_:1})]),a("div",A,[a("button",{type:"button",class:"bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover rounded-md px-4 py-2 font-semibold md:px-4 md:py-3 md:text-xl",onClick:o[3]||(o[3]=(...s)=>e.closeModal&&e.closeModal(...s)),disabled:e.loading}," Discard ",8,O),a("button",{class:"bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover rounded-md px-4 py-2 font-semibold md:px-4 md:py-3 md:text-xl",type:"submit",disabled:e.loading},[e.loading?y("",!0):(h(),v("span",Z," Save ")),e.loading?(h(),v("span",G,o[6]||(o[6]=[C(" Please wait... "),a("span",{class:"spinner-border spinner-border-sm align-middle ms-2"},null,-1)]))):y("",!0)],8,T)])]),_:1},8,["onSubmit","model","rules"])])])):y("",!0)}const Q=M(P,[["render",H]]);export{Q as C};
