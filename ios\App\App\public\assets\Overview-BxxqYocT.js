import{P as te}from"./PageHeader-3hadTn26.js";import{T as ne}from"./TablePagination-BmVxunEG.js";import{S as ae}from"./SvgIcon-CMhyaXWN.js";import{T as re}from"./TableHeader-C1CWTWQa.js";import{d as ee,q as m,Z as ie,x as se,L as B,Y as de,_ as oe,c as p,o as d,a as s,b as a,w as f,l as S,r as i,p as le,a0 as j,A as Y,h as ce,a4 as ue,e as me,E as Z,g as pe,m as q,H as x,n as G,B as T,I as be,M as J,t as y,F as fe,k as ge}from"./index-CGNRhvz7.js";import{S as Q,a as ve}from"./table-bhK9qpe4.js";import{u as he}from"./user-KFDu8xJF.js";import{u as ye}from"./company-oDyd0dWV.js";import{U as ke}from"./UserModal-Ck8RxOB2.js";import"./d-left-arrow-079-B3YbfCzd.js";import"./handleFailure-DtTpu7r3.js";import"./validator-6laVLK0J.js";import"./index.esm-DXW765zG.js";const W={value:"",label:"All"},X={value:0,label:"All"},we=ee({name:"user-management-filter",components:{},props:{hideFilter:{type:Function,required:!1,default:()=>{}},onFilter:{type:Function,required:!0}},setup(e){const o=ye(),C=m(!1),g=m([W]),U=[X,...ie],A=[X,...de],k={email:"",status:0,role:0,companyId:""},n=m({...k});se(()=>{B()&&b()});const b=async()=>{C.value=!0,o.getCompanies({params:{page:1,limit:500},callback:{onSuccess:c=>{var v;g.value=[W,...(v=c==null?void 0:c.items)==null?void 0:v.map(u=>({value:u==null?void 0:u.id,label:u==null?void 0:u.name}))]},onFinish:()=>{C.value=!1}}})},_=()=>{e!=null&&e.hideFilter&&(e==null||e.hideFilter())},r=()=>{n.value={...k},w()},w=()=>{var c,v,u,$;e.onFilter({email:((c=n.value)==null?void 0:c.email)||null,status:((v=n.value)==null?void 0:v.status)||null,role:((u=n.value)==null?void 0:u.role)||null,companyId:(($=n.value)==null?void 0:$.companyId)||null})};return{loading:C,isSystemAdmin:B,filterForm:n,roleOptions:U,companyOptions:g,statusOptions:A,apply:w,hideFilter:_,resetFilter:r}}}),_e={class:"card h-100 w-1000 my-8 shadow-sm user-filter"},Fe={class:"card-header align-items-center"},Se={class:"card-toolbar gap-3 ms-auto"},Ce={class:"card-body"},Ue={class:"row g-9"},Ae={class:"col-md-6 col-lg-3 fv-row d-flex flex-column justify-content-stretch"},$e={class:"col-md-6 col-lg-3 fv-row d-flex flex-column justify-content-stretch"},Pe={class:"col-md-6 col-lg-3 fv-row d-flex flex-column justify-content-stretch"},Ve={key:0,class:"col-md-6 col-lg-3 fv-row d-flex flex-column justify-content-stretch"};function Ne(e,o,C,g,U,A){const k=i("el-input"),n=i("el-form-item"),b=i("el-select-v2"),_=i("el-form");return d(),p("div",_e,[s("div",Fe,[o[7]||(o[7]=s("div",null,[s("h5",{class:"mb-0 me-4 text-gray-900"},"Filter")],-1)),s("div",Se,[s("button",{class:"btn btn-active-light btn-color-muted fw-semibold btn-sm",onClick:o[0]||(o[0]=(...r)=>e.resetFilter&&e.resetFilter(...r))}," Reset "),s("button",{class:"btn btn-light-primary btn-sm",onClick:o[1]||(o[1]=(...r)=>e.hideFilter&&e.hideFilter(...r))}," Close "),s("button",{class:"btn btn-sm btn-primary",type:"button",onClick:o[2]||(o[2]=(...r)=>e.apply&&e.apply(...r))}," Apply ")])]),s("div",Ce,[a(_,{class:"form new-report-form",model:e.filterForm,onSubmit:le(e.apply,["prevent"])},{default:f(()=>[s("div",null,[s("div",Ue,[s("div",Ae,[o[8]||(o[8]=s("label",{class:"d-flex align-items-center fs-6 fw-semibold mb-2 text-break"},"Email",-1)),a(n,{prop:"email"},{default:f(()=>[a(k,{placeholder:"Email",name:"email",modelValue:e.filterForm.email,"onUpdate:modelValue":o[3]||(o[3]=r=>e.filterForm.email=r)},null,8,["modelValue"])]),_:1})]),s("div",$e,[o[9]||(o[9]=s("label",{class:"d-flex align-items-center fs-6 fw-semibold mb-2 text-break"}," Status ",-1)),a(n,{prop:"status"},{default:f(()=>[a(b,{class:"w-100",options:e.statusOptions,placeholder:"Status",name:"status",modelValue:e.filterForm.status,"onUpdate:modelValue":o[4]||(o[4]=r=>e.filterForm.status=r)},null,8,["options","modelValue"])]),_:1})]),s("div",Pe,[o[10]||(o[10]=s("label",{class:"d-flex align-items-center fs-6 fw-semibold mb-2 text-break"}," Roles ",-1)),a(n,{prop:"role"},{default:f(()=>[a(b,{class:"w-100",options:e.roleOptions,placeholder:"Roles",name:"role",modelValue:e.filterForm.role,"onUpdate:modelValue":o[5]||(o[5]=r=>e.filterForm.role=r)},null,8,["options","modelValue"])]),_:1})]),e.isSystemAdmin()?(d(),p("div",Ve,[o[11]||(o[11]=s("label",{class:"d-flex align-items-center fs-6 fw-semibold mb-2 text-break"}," Company ",-1)),a(n,{prop:"companyId"},{default:f(()=>[a(b,{class:"w-100",options:e.companyOptions,placeholder:"Company",name:"companyId",modelValue:e.filterForm.companyId,"onUpdate:modelValue":o[6]||(o[6]=r=>e.filterForm.companyId=r),loading:e.loading},null,8,["options","modelValue","loading"])]),_:1})])):S("",!0)])]),o[12]||(o[12]=s("button",{class:"btn btn-sm btn-primary d-none",type:"submit"},null,-1))]),_:1},8,["model","onSubmit"])])])}const Oe=oe(we,[["render",Ne]]),Ee=ee({name:"user-overview",components:{PageHeader:te,SvgIcon:ae,Filter:Oe,TablePagination:ne,UserModal:ke,TableHeader:re},setup(){const e=he(),o=me(),C=["User Management","Overview"],g=m([]),U=m(!1),A=m(0),k=m(0),n=m(1),b=m(!1),_=m(""),r=m(!1),w=m(null),c=m([]),v=m({}),u=m({sortDirection:ve.ASC,sortBy:Q.Name}),$=[{label:"",class:"w-25px",display:j()},{label:"FULL NAME",sortBy:Q.Name,class:"min-w-150px"},{label:"ADDRESS",class:"min-w-150px"},{label:"OFFICE NUMBER",class:"min-w-120px"},{label:"MOBILE NUMBER",class:"min-w-120px"},{label:"STATUS",class:"min-w-150px"},{label:"COMPANY",class:"min-w-150px",display:B()},{label:"ROLES",class:"min-w-120px"},{label:"ACTIONS",class:"min-w-60px",display:j()}];se(()=>{h()}),Y(n,()=>{h()});const h=async()=>{b.value=!0,e.getUsers({params:{page:n.value,limit:10,name:_.value.trim()||null,...u.value,...v.value},callback:{onSuccess:t=>{var N;const P=(N=t==null?void 0:t.items)==null?void 0:N.map(R=>{var z;return{...R,roles:(z=R==null?void 0:R.roles)==null?void 0:z.map(H=>{var K;return((K=ce(H==null?void 0:H.value,pe))==null?void 0:K.label)||""})}});c.value=[...P],A.value=t==null?void 0:t.totalPage,k.value=t==null?void 0:t.total,n.value=t==null?void 0:t.page},onFinish:()=>{b.value=!1}}})},M=t=>{u.value={...t},h()},D=()=>{n.value!==1?n.value=1:h()},L=t=>{n.value=t},O=()=>{var t;(t=w==null?void 0:w.value)==null||t.show()};Y(g,t=>{U.value=c.value.length!==0&&t.length===c.value.length});const E=t=>{var P;(P=t==null?void 0:t.target)!=null&&P.checked?g.value=c.value.map(N=>N.id):g.value=[]},l=t=>{o.push({path:`/users/${t}`})},F=t=>{Z.deletionAlert({onConfirmed:()=>{V([t])}})},I=()=>{Z.deletionAlert({onConfirmed:()=>{V(g.value,!0)}})},V=async(t,P=!1)=>{b.value=!0,e.removeUsers({ids:t,callback:{onSuccess:N=>{P&&(g.value=[]),h()},onFinish:()=>{b.value=!1}}})};return{sortParams:u,tableHeader:$,search:_,loading:b,UserStatus:ue,breadcrumbs:C,checkedRows:g,checkAll:U,listUser:c,currentPage:n,totalElements:k,pageCount:A,isShowFilter:r,userModal:w,isSystemAdmin:B,toggleNewUser:O,pageChange:L,deleteUser:F,view:l,isAdmin:j,onToggleCheckAll:E,onRemove:I,onFilter:t=>{v.value={...t},n.value!==1?n.value=1:h()},toggleFilter:t=>{r.value=t},searchUsers:D,getUserList:h,reloadUserList:()=>{h()},onSort:M}}}),Ie={class:"card h-100 my-8 user-overview"},Re={class:"card-header py-4 align-items-center"},Te={class:"card-toolbar gap-3 ms-auto"},Be={class:"d-flex align-items-center"},Me={class:"svg-icon svg-icon-2"},De={class:"svg-icon svg-icon-2"},Le={class:"svg-icon svg-icon-2"},He={class:"btn btn-flex btn-sm btn-success btn-export-success"},je={class:"svg-icon svg-icon-2 rotate-90"},qe={key:0,class:"text-center p-5"},xe={key:2},ze={class:"table-responsive mt-3 mx-8"},Ke={class:"table table-row-dashed table-row-gray-300 align-middle gs-0 gy-2"},Ye={class:"fw-bold text-gray-400"},Ze={key:0},Ge={class:"form-check form-check-sm form-check-custom form-check-solid"},Je={key:1},Qe={key:0},We={class:"form-check form-check-sm form-check-custom form-check-solid"},Xe=["value"],es={class:"d-flex align-items-center"},ss={class:"symbol symbol-45px symbol-circle me-5"},os=["src"],ls={class:"d-flex justify-content-start flex-column"},ts={class:"text-gray-400 fw-semibold d-block fs-5"},ns={class:"text-gray-600 fw-semibold d-block fs-5"},as={class:"text-gray-600 fw-semibold d-block fs-5"},rs={class:"text-gray-600 fw-semibold d-block fs-5"},is={key:1},ds={class:"text-gray-600 fw-semibold d-block fs-5"},cs={class:"d-flex align-items-center"},us={class:"d-flex justify-content-start flex-column"},ms={class:"text-gray-600 fw-semibold d-block fs-5"},ps={key:2},bs={class:"d-flex align-items-center"},fs=["onClick"],gs={class:"svg-icon svg-icon-3"},vs=["onClick"],hs={class:"svg-icon svg-icon-3 text-danger"},ys={class:"d-flex flex-wrap align-items-center mt-4 mb-5 mx-8"},ks={key:0,class:"text-gray-700 fw-semibold fs-6 me-auto"};function ws(e,o,C,g,U,A){const k=i("PageHeader"),n=i("SvgIcon"),b=i("el-icon"),_=i("el-input"),r=i("el-form-item"),w=i("el-form"),c=i("inline-svg"),v=i("Filter"),u=i("el-empty"),$=i("TableHeader"),h=i("router-link"),M=i("TablePagination"),D=i("UserModal"),L=i("KTContent");return d(),q(L,null,{header:f(()=>[a(k,{title:"User Management",breadcrumbs:e.breadcrumbs},null,8,["breadcrumbs"])]),body:f(()=>{var O,E;return[s("div",Ie,[s("div",Re,[o[10]||(o[10]=s("div",null,[s("h1",{class:"mb-0 me-4 text-gray-900"},"Users management")],-1)),s("div",Te,[a(w,{onSubmit:le(e.searchUsers,["prevent"])},{default:f(()=>[s("div",Be,[a(r,{class:"mb-0"},{default:f(()=>[a(_,{class:"w-250px",placeholder:"Search",modelValue:e.search,"onUpdate:modelValue":o[0]||(o[0]=l=>e.search=l),name:"search",size:"large"},{prefix:f(()=>[a(b,{class:"el-input__icon"},{default:f(()=>[s("span",Me,[a(n,{icon:"searchIcon"})])]),_:1})]),_:1},8,["modelValue"])]),_:1})])]),_:1},8,["onSubmit"]),s("button",{class:G(["btn btn-flex btn-sm",e.isShowFilter?"btn-primary":"btn-secondary"]),onClick:o[1]||(o[1]=()=>e.toggleFilter(!e.isShowFilter))},[s("span",De,[a(c,{src:"/media/icons/duotune/general/gen031.svg"})]),o[7]||(o[7]=T(" Filter "))],2),e.checkedRows.length!==0&&e.isAdmin()?(d(),p("button",{key:0,class:"btn btn-flex btn-sm btn-danger",onClick:o[2]||(o[2]=(...l)=>e.onRemove&&e.onRemove(...l))}," Remove ")):S("",!0),e.isAdmin()?(d(),p("button",{key:1,class:"btn btn-flex btn-sm btn-primary",onClick:o[3]||(o[3]=(...l)=>e.toggleNewUser&&e.toggleNewUser(...l))},[s("span",Le,[a(c,{src:"/media/icons/duotune/arrows/arr075.svg"})]),o[8]||(o[8]=T(" New "))])):S("",!0),s("button",He,[s("span",je,[a(c,{src:"/media/icons/duotune/arrows/arr092.svg"})]),o[9]||(o[9]=T(" Export "))])])]),x(a(v,{hideFilter:()=>e.toggleFilter(!1),onFilter:e.onFilter},null,8,["hideFilter","onFilter"]),[[be,e.isShowFilter]]),e.loading?(d(),p("div",qe,o[11]||(o[11]=[s("div",{class:"spinner-border text-primary",role:"status"},[s("span",{class:"sr-only"},"Loading...")],-1)]))):e.listUser.length===0?(d(),q(u,{key:1,description:"No Data"})):(d(),p("div",xe,[s("div",ze,[s("table",Ke,[s("thead",null,[s("tr",Ye,[a($,{headers:e.tableHeader,sortBy:e.sortParams.sortBy,sortDirection:e.sortParams.sortDirection,onSort:e.onSort},{customHeader:f(({header:l})=>[l.label===""?(d(),p("div",Ze,[s("div",Ge,[x(s("input",{class:"form-check-input",type:"checkbox","onUpdate:modelValue":o[4]||(o[4]=F=>e.checkAll=F),onChange:o[5]||(o[5]=(...F)=>e.onToggleCheckAll&&e.onToggleCheckAll(...F))},null,544),[[J,e.checkAll]])])])):(d(),p("div",Je,y(l.label),1))]),_:1},8,["headers","sortBy","sortDirection","onSort"])])]),s("tbody",null,[(d(!0),p(fe,null,ge(e.listUser,l=>{var F,I;return d(),p("tr",{key:l.id},[e.isAdmin()?(d(),p("td",Qe,[s("div",We,[x(s("input",{class:"form-check-input widget-9-check",type:"checkbox",value:l.id,"onUpdate:modelValue":o[6]||(o[6]=V=>e.checkedRows=V)},null,8,Xe),[[J,e.checkedRows]])])])):S("",!0),s("td",null,[s("div",es,[s("div",ss,[s("img",{src:(l==null?void 0:l.avatar)||"media/avatars/blank.png",alt:""},null,8,os)]),s("div",ls,[a(h,{to:`/users/${l==null?void 0:l.id}`,class:"text-dark fw-bold text-hover-primary fs-6"},{default:f(()=>[T(y(`${l==null?void 0:l.firstName} ${l==null?void 0:l.lastName}`),1)]),_:2},1032,["to"]),s("span",ts,y(l==null?void 0:l.email),1)])])]),s("td",null,[s("span",ns,y(l==null?void 0:l.address),1)]),s("td",null,[s("span",as,y(l==null?void 0:l.officePhone),1)]),s("td",null,[s("span",rs,y(l==null?void 0:l.mobilePhone),1)]),s("td",null,[s("span",{class:G(["badge",(l==null?void 0:l.status)===e.UserStatus.Active?"badge-light-success":"badge-light-danger"])},y((l==null?void 0:l.status)===e.UserStatus.Active?"Active":"Deactive"),3)]),e.isSystemAdmin()?(d(),p("td",is,[s("span",ds,y((F=l==null?void 0:l.company)==null?void 0:F.name),1)])):S("",!0),s("td",null,[s("div",cs,[s("div",us,[s("span",ms,y((I=l==null?void 0:l.roles)==null?void 0:I.join(", ")),1)])])]),e.isAdmin()?(d(),p("td",ps,[s("div",bs,[s("button",{class:"btn btn-icon btn-sm btn-blue me-3",onClick:()=>e.view((l==null?void 0:l.id)??"")},[s("span",gs,[a(n,{icon:"eyeIcon"})])],8,fs),s("button",{class:"btn btn-icon btn-sm btn-blue me-3",onClick:V=>e.deleteUser(l.id)},[s("span",hs,[a(n,{icon:"trashIcon"})])],8,vs)])])):S("",!0)])}),128))])])]),s("div",ys,[(O=e.listUser)!=null&&O.length?(d(),p("div",ks,y(`Showing ${(e.currentPage-1)*10+1} to ${(E=e.listUser)==null?void 0:E.length} of ${e.totalElements} entries`),1)):S("",!0),e.pageCount>=1?(d(),q(M,{key:1,"total-pages":e.pageCount,total:e.totalElements,"per-page":10,"current-page":e.currentPage,onPageChange:e.pageChange},null,8,["total-pages","total","current-page","onPageChange"])):S("",!0)])]))]),a(D,{ref:"userModal",loadPage:e.reloadUserList},null,8,["loadPage"])]}),_:1})}const Ms=oe(Ee,[["render",ws]]);export{Ms as default};
