<template>
  <slot></slot>
</template>

<script setup lang="ts">
import { useDailyReportStore } from "../stores/daily-report";
import { get, noop } from "lodash";
import { provide, ref } from "vue";
import type { Callback } from "../types/common";

const dailyReportId = ref("");

const createDailyReport = ({
  wellId,
  callback,
}: {
  wellId: string;
  callback: Callback;
}) => {
  const onSuccess = get(callback, "onSuccess", noop);

  if (dailyReportId.value) {
    onSuccess(dailyReportId.value);
  } else {
    const dailyReportStore = useDailyReportStore();
    dailyReportStore.createDailyReport({
      wellId,
      callback: {
        onSuccess: (res) => {
          dailyReportId.value = res?.id || "";
          onSuccess(res?.id);
        },
      },
    });
  }
};

const resetDailyReportId = () => {
  dailyReportId.value = "";
};

const setDailyReportId = (value: string) => {
  dailyReportId.value = value;
};

const getDailyReportId = () => {
  return dailyReportId.value;
};

provide("dailyReport", {
  createDailyReport,
  resetDailyReportId,
  getDailyReportId,
  setDailyReportId,
});
</script>
