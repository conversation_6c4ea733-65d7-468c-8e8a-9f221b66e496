<template>
  <div
    v-if="isVisible"
    class="fixed top-0 right-0 bottom-0 left-0 z-40 flex items-center"
  >
    <!-- Dark overlay background -->
    <div class="absolute inset-0 bg-sidebar-background opacity-25"></div>
    <div
      class="relative bg-card-background text-card-text-light h-auto w-11/12 flex flex-col items-center gap-3 mx-auto p-5 rounded-xl md:w-4/5 md:text-lg lg:w-2/5"
    >
      <div class="h-auto w-full flex flex-row items-center justify-between">
        <h3 class="text-lg font-bold">
          {{ id ? "Edit Company" : "New Company" }}
        </h3>
        <span class="cursor-pointer" @click="closeModal">
          <SvgIcon icon="closeModalIcon" />
        </span>
      </div>

      <el-form
        id="product_form"
        @submit.prevent="submit"
        :model="targetData"
        :rules="rules"
        ref="formRef"
        class="h-auto w-full"
      >
        <div class="flex flex-col gap-2">
          <label class="font-semibold"
            >Company Name
            <span class="text-danger-active font-light">*</span>
          </label>
          <el-form-item prop="name">
            <el-input
              v-model="targetData.name"
              placeholder=""
              name="name"
              show-word-limit
              maxlength="150"
            ></el-input>
          </el-form-item>
        </div>
        <!-- <div class="fv-row d-flex flex-column justify-content-stretch mt-2">
          <label
            class="d-flex align-items-center fs-6 fw-semibold mb-2 text-break"
            >Company Register Number
          </label>
          <el-form-item prop="registerNumber" class="mt-auto">
            <el-input
              class="w-100"
              v-model="targetData.registerNumber"
              placeholder=""
              name="registerNumber"
              show-word-limit
              maxlength="150"
            ></el-input>
          </el-form-item>
        </div> -->
        <div class="flex flex-col gap-2">
          <label class="font-semibold"
            >Description
            <span class="text-danger-active font-light">*</span></label
          >
          <el-form-item prop="description" class="mt-auto">
            <el-input
              class="w-100"
              v-model="targetData.description"
              placeholder=""
              name="description"
            ></el-input>
          </el-form-item>
        </div>
        <div class="flex flex-row items-start mt-4 gap-3">
          <button
            type="button"
            class="bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover rounded-md px-4 py-2 font-semibold md:px-4 md:py-3 md:text-xl"
            @click="closeModal"
            :disabled="loading"
          >
            Discard
          </button>
          <button
            class="bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover rounded-md px-4 py-2 font-semibold md:px-4 md:py-3 md:text-xl"
            type="submit"
            :disabled="loading"
          >
            <span v-if="!loading" class="indicator-label"> Save </span>
            <span v-if="loading" class="indicator-progress">
              Please wait...
              <span
                class="spinner-border spinner-border-sm align-middle ms-2"
              ></span>
            </span>
          </button>
        </div>
      </el-form>
    </div>
  </div>
</template>

<script lang="ts">
import SvgIcon from "@/constants/SvgIcon.vue";
import { rolesOptions } from "@/constants/user";
import { useCompanyStore } from "@/stores/company";
import type { FormRules } from "element-plus";
import { defineComponent, ref, watch } from "vue";

export default defineComponent({
  name: "company-modal",
  components: { SvgIcon },
  props: {
    isVisible: {
      type: Boolean,
      required: true,
    },
    close: {
      type: Function,
      required: true,
    },
    loadPage: {
      type: Function,
      required: false,
    },
  },
  setup(props) {
    const companyStore = useCompanyStore();
    const targetData = ref({
      name: "",
      registerNumber: "",
      description: "",
    });
    const formRef = ref<null | HTMLFormElement>(null);
    const loading = ref<boolean>(false);
    const id = ref("");

    const closeModal = () => {
      props.close();
      resetData();
    };

    watch(id, (newValue) => {
      if (newValue) {
        getCompanyDetails();
      }
    });

    const getCompanyDetails = async (): Promise<void> => {
      if (!id.value) return;
      companyStore.getCompanyById({
        id: id.value,
        callback: {
          onSuccess: (res: any) => {
            targetData.value = {
              name: res?.name,
              registerNumber: res?.registerNumber,
              description: res?.description,
            };
          },
        },
      });
    };

    const setId = (companyId: string) => {
      id.value = companyId.toString();
    };

    const rules = ref<FormRules<any>>({
      name: [
        {
          required: true,
          message: "Please type Company Name",
          trigger: ["blur", "change"],
        },
      ],
      // registerNumber: [
      //   {
      //     required: false,
      //     message: "Please type Register Number",
      //     trigger: ["blur", "change"],
      //   },
      // ],
      description: [
        {
          required: true,
          message: "Please select Description",
          trigger: ["blur", "change"],
        },
      ],
    });

    const submit = () => {
      if (!formRef.value) {
        return;
      }

      formRef.value.validate((valid: boolean) => {
        if (valid) {
          if (id.value) {
            editCompany();
          } else {
            createCompany();
          }
        }
      });
    };

    const createCompany = async (): Promise<void> => {
      loading.value = true;
      companyStore.createCompany({
        params: {
          ...targetData.value,
          registerNumber: targetData?.value?.registerNumber || null,
        },
        callback: {
          onSuccess: () => {
            props?.loadPage?.();
            closeModal();
          },
          onFinish: () => {
            loading.value = false;
          },
        },
      });
    };

    const editCompany = async (): Promise<void> => {
      loading.value = true;
      companyStore.editCompany({
        id: id.value,
        params: {
          ...targetData.value,
          registerNumber: targetData?.value?.registerNumber || null,
        },
        callback: {
          onSuccess: () => {
            props?.loadPage?.();
            closeModal();
          },

          onFinish: () => {
            loading.value = false;
          },
        },
      });
    };

    const resetData = () => {
      id.value = "";
      targetData.value = {
        name: "",
        registerNumber: "",
        description: "",
      };
      formRef?.value?.resetFields();
    };

    return {
      id,
      rules,
      loading,
      targetData,
      formRef,
      rolesOptions,
      closeModal,
      submit,
      setId,
      resetData,
    };
  },
});
</script>
