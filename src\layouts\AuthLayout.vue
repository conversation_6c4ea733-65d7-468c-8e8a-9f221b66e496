<template>
  <div
    class="z-10 min-h-screen min-w-full flex flex-col items-center justify-center bg-screen-background md:bg-card-background"
  >
    <div
      class="z-20 absolute bg-[url('/media/well-background.png')] bg-no-repeat bg-[center_40%] bg-cover inset-0 w-full h-full"
    ></div>
    <div class="h-full w-full z-30 md:min-h-screen md:flex md:flex-col md:items-center md:justify-center">
      <router-view></router-view>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent } from "vue";
export default defineComponent({
  name: "auth-layout",
  components: {},
  setup() {},
});
</script>
