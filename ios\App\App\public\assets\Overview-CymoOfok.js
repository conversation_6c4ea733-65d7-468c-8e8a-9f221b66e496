import{h as Ie,g as Ue,d as te,q as w,U as F,a4 as _e,a0 as Q,J as L,j as he,i as Ve,_ as ae,c as g,o as c,a as t,n as G,b as l,r as I,s as Se,t as R,l as D,w as p,B as M,Z as xe,E as W,m as X,k as Pe,F as ee,p as se,x as Ae,L as H}from"./index-CGNRhvz7.js";import{u as le}from"./user-KFDu8xJF.js";import{v as Z,y as K}from"./validator-6laVLK0J.js";import{u as Re}from"./company-oDyd0dWV.js";import{c as Ne}from"./index.esm-DXW765zG.js";import{S as Ee}from"./SvgIcon-CMhyaXWN.js";import{R as je,a as Me,b as qe,c as Fe}from"./regex-BLjctcPP.js";const Oe=e=>e.map(s=>{var U;return{value:s==null?void 0:s.value,key:((U=Ie(s==null?void 0:s.value,Ue))==null?void 0:U.label)||""}})||[],Le=te({name:"user-info",components:{},props:{userDetail:{type:Object,required:!0}},setup(e){var a,j,E,A;const s=le(),U=he(),N=w(null),b=w(((a=e.userDetail)==null?void 0:a.avatar)||""),_=w(null),n=w(!1),f=Ve("userInfo"),$=Oe(((j=e==null?void 0:e.userDetail)==null?void 0:j.roles)||[]).map(o=>o.key).join(", "),C=!!((A=(E=e==null?void 0:e.userDetail)==null?void 0:E.roles)!=null&&A.find(o=>o.value===F.SystemAdmin)),P=()=>{var o,u,v,h;return!!(((u=(o=e==null?void 0:e.userDetail)==null?void 0:o.roles)==null?void 0:u.length)===1&&((h=(v=e==null?void 0:e.userDetail)==null?void 0:v.roles)!=null&&h.find(x=>(x==null?void 0:x.value)===F.Engineer)))},i=()=>U.name==="my-profile",V=o=>{var u,v;if(o.target.files[0]){N.value=URL.createObjectURL(o.target.files[0]);const h=new FormData;h.append("file",o.target.files[0],o.target.files[0].name),h.append("userId",i()?(u=L.getUserInfo())==null?void 0:u.id:(v=e==null?void 0:e.userDetail)==null?void 0:v.id),S(h)}},S=async o=>{n.value=!0,s.uploadAvatar({params:o,callback:{onSuccess:u=>{var v,h,x;b.value=u==null?void 0:u.url,(((h=(v=U.params)==null?void 0:v.id)==null?void 0:h.toString())===((x=L.getUserInfo())==null?void 0:x.id)||i())&&(f==null||f.updateUserInfo({avatar:u==null?void 0:u.url}))},onFinish:()=>{n.value=!1}}})};return{isAdmin:Q,UserStatus:_e,userRoles:$,avatar:b,fileImage:N,fileRef:_,uploadingAvatar:n,isUserDetailSystemAdmin:C,userProfileIsEngineer:P,handleChooseImage:V,clickInputFile:()=>{var o;(o=_==null?void 0:_.value)==null||o.click()}}}}),Be={class:"d-flex flex-wrap flex-sm-nowrap"},Te={class:"me-7 mb-4"},ze={class:"symbol symbol-100px symbol-lg-160px symbol-fixed position-relative"},Je=["src"],Ye={class:"btn-edit-image"},Ze=["disabled"],Ge={key:0,class:"svg-icon svg-icon-4"},He={key:1,class:"spinner-border spinner-border-sm align-middle text-primary"},Ke={class:"flex-grow-1"},Qe={class:"d-flex justify-content-between align-items-start flex-wrap mb-2"},We={class:"d-flex flex-column"},Xe={class:"d-flex align-items-center mb-2"},es={href:"#",class:"text-gray-800 text-hover-primary fs-2 fw-bold me-1"},ss={class:"d-flex flex-wrap"},ts={class:"d-flex flex-wrap flex-column fw-semibold fs-6 pe-2"},as={class:"d-flex gap-2 text-gray-400 text-hover-primary me-5 mb-2 mt-2"},ls={class:"svg-icon svg-icon-4 me-1"},os={class:"mb-0"},ns={class:"d-flex gap-2 text-gray-400 text-hover-primary me-5 mb-2 mt-2"},rs={class:"svg-icon svg-icon-4 me-1"},is={class:"mb-0"},ds={class:"d-flex gap-2 text-gray-400 text-hover-primary me-5 mb-2 mt-2"},us={class:"svg-icon svg-icon-4 me-1"},ms={class:"mb-0"},cs={class:"d-flex flex-wrap flex-column fw-semibold fs-6 pe-2"},fs={class:"d-flex gap-2 text-gray-400 text-hover-primary me-5 mb-2 mt-2"},gs={class:"svg-icon svg-icon-4 me-1"},bs={class:"d-flex gap-2 text-gray-400 text-hover-primary me-5 mb-2 mt-2"},vs={class:"svg-icon svg-icon-4 me-1"},ps={class:"mb-0"},ws={class:"d-flex gap-2 text-gray-400 text-hover-primary me-5 mb-2 mt-2"},ys={class:"svg-icon svg-icon-4 me-1"},hs={class:"mb-0"},Ps={class:"d-flex flex-wrap flex-column fw-semibold fs-6 pe-2"},ks={key:0,class:"d-flex gap-2 text-gray-400 text-hover-primary me-5 mb-2 mt-2"},Ds={class:"svg-icon svg-icon-4 me-1"},$s={class:"mb-0"},Cs={key:1,class:"d-flex gap-2 text-gray-400 text-hover-primary me-5 mb-2 mt-2"},Is={class:"svg-icon svg-icon-4 me-1"},Us={key:0,class:"d-flex align-items-center mt-4"},_s={class:"symbol symbol-45px symbol-circle me-5"},Vs=["src"],Ss={class:"d-flex justify-content-start flex-column"},xs={class:"text-gray-400 fw-semibold d-block fs-5"};function As(e,s,U,N,b,_){var $,C,P,i,V,S,y,a,j,E,A,o,u,v,h,x,O,B;const n=I("inline-svg"),f=I("router-link");return c(),g("div",Be,[t("div",Te,[t("div",ze,[t("img",{src:e.avatar?e.avatar:e.fileImage||"/media/avatars/blank.png",alt:"image"},null,8,Je),t("input",{class:"d-none",type:"file",ref:"fileRef",onChange:s[0]||(s[0]=(...q)=>e.handleChooseImage&&e.handleChooseImage(...q))},null,544),t("div",Ye,[t("button",{type:"button",class:G(`btn btn-sm btn-light me-3 ${e.isAdmin()?"":"d-none"}`),disabled:e.uploadingAvatar,onClick:s[1]||(s[1]=(...q)=>e.clickInputFile&&e.clickInputFile(...q))},[e.uploadingAvatar?(c(),g("span",He)):(c(),g("span",Ge,[l(n,{src:"media/icons/duotune/general/pencil.svg"})]))],10,Ze)]),t("div",{class:G(`position-absolute translate-middle bottom-0 start-100 mb-6 rounded-circle border-4 border-white h-20px w-20px ${(($=e.userDetail)==null?void 0:$.status)===e.UserStatus.Active?"bg-success":"bg-danger"}`)},null,2)])]),t("div",Ke,[t("div",Qe,[t("div",We,[t("div",Xe,[t("a",es,R(`${((C=e.userDetail)==null?void 0:C.firstName)||""} ${((P=e.userDetail)==null?void 0:P.lastName)||""}`),1)]),t("div",ss,[t("div",ts,[t("div",as,[t("span",ls,[l(n,{src:"media/icons/duotune/general/protect.svg"})]),t("div",null,[s[2]||(s[2]=t("h6",{class:"mb-0 text-gray-600 user-info"},"Roles",-1)),t("p",os,R(e.userRoles),1)])]),t("div",ns,[t("span",rs,[l(n,{src:"media/icons/duotune/general/duotone.svg"})]),t("div",null,[s[3]||(s[3]=t("h6",{class:"mb-0 text-gray-600 user-info"},"Email",-1)),t("p",is,R(((i=e.userDetail)==null?void 0:i.email)||""),1)])]),t("div",ds,[t("span",us,[l(n,{src:"media/icons/duotune/general/active-call.svg"})]),t("div",null,[s[4]||(s[4]=t("h6",{class:"mb-0 text-gray-600 user-info"},"Office Phone",-1)),t("p",ms,R(((V=e.userDetail)==null?void 0:V.officePhone)||""),1)])])]),t("div",cs,[t("div",fs,[t("span",gs,[l(n,{src:"media/icons/duotune/general/gen029.svg"})]),t("div",null,[s[5]||(s[5]=t("h6",{class:"mb-0 text-gray-600 user-info"},"Status",-1)),t("span",{class:G(["p-0 user-status",((S=e.userDetail)==null?void 0:S.status)===e.UserStatus.Active?"text-green-400":"text-danger"])},R(((y=e.userDetail)==null?void 0:y.status)===e.UserStatus.Active?"Active":"Deactive"),3)])]),t("div",bs,[t("span",vs,[l(n,{src:"media/icons/duotune/general/gen018.svg"})]),t("div",null,[s[6]||(s[6]=t("h6",{class:"mb-0 text-gray-600 user-info"},"Address",-1)),t("p",ps,R(((a=e.userDetail)==null?void 0:a.address)||""),1)])]),t("div",ws,[t("span",ys,[l(n,{src:"/media/icons/duotune/general/active-call.svg"})]),t("div",null,[s[7]||(s[7]=t("h6",{class:"mb-0 text-gray-600 user-info"},"Mobile Phone",-1)),t("p",hs,R(((j=e.userDetail)==null?void 0:j.mobilePhone)||""),1)])])]),t("div",Ps,[e.isUserDetailSystemAdmin?D("",!0):(c(),g("div",ks,[t("span",Ds,[l(n,{src:"/media/icons/duotune/general/company.svg"})]),t("div",null,[s[8]||(s[8]=t("h6",{class:"mb-0 text-gray-600 user-info"},"Company",-1)),t("p",$s,R(((A=(E=e.userDetail)==null?void 0:E.company)==null?void 0:A.name)||""),1)])])),e.userProfileIsEngineer()?(c(),g("div",Cs,[t("span",Is,[l(n,{src:"/media/icons/duotune/general/user.svg"})]),t("div",null,[s[9]||(s[9]=t("h6",{class:"mb-0 text-gray-600 user-info"},"Supervisor",-1)),(o=e.userDetail)!=null&&o.supervisor?(c(),g("div",Us,[t("div",_s,[t("img",{src:((v=(u=e.userDetail)==null?void 0:u.supervisor)==null?void 0:v.avatar)||"media/avatars/blank.png",alt:"Avatar"},null,8,Vs)]),t("div",Ss,[l(f,{to:`/users/${(x=(h=e.userDetail)==null?void 0:h.supervisor)==null?void 0:x.id}`,class:"text-dark fw-bold text-hover-primary fs-6"},{default:p(()=>{var q,T,z,J;return[M(R(`${((T=(q=e.userDetail)==null?void 0:q.supervisor)==null?void 0:T.firstName)||""} ${((J=(z=e.userDetail)==null?void 0:z.supervisor)==null?void 0:J.lastName)||""}`),1)]}),_:1},8,["to"]),t("span",xs,R((B=(O=e.userDetail)==null?void 0:O.supervisor)==null?void 0:B.email),1)])])):D("",!0)])])):D("",!0)])])]),Se(e.$slots,"toolbar")])])])}const Tt=ae(Le,[["render",As]]),Rs=te({name:"change-password-modal",components:{SvgIcon:Ee},setup(){const e=le(),s=w(!1),U={currentPassword:"",newPassword:"",confirmPassword:"",hideCurrentPassword:!0,hideNewPassword:!0,hideConfirmPassword:!0},N={length:{isValid:!1,text:"At least 8 characters"},lowercase:{isValid:!1,text:"At least 1 lowercase character"},uppercase:{isValid:!1,text:"At least 1 uppercase character"},specialCharacter:{isValid:!1,text:"At least 1 number and 1 special character"}},b=w({...U}),_=w(!1),n=w(null),f=w({...N}),$=o=>{const u=Z.validate(o,{pattern:je,errorsMessage:{pattern:"Incorrect password format."}}),v=Z.validate(o,{pattern:Me,errorsMessage:{pattern:"Incorrect password format."}}),h=Z.validate(o,{pattern:qe,errorsMessage:{pattern:"Incorrect password format."}}),x=Z.validate(o,{pattern:Fe,errorsMessage:{pattern:"Incorrect password format."}});return f.value={length:{isValid:!u,text:"At least 8 characters"},lowercase:{isValid:!v,text:"At least 1 lowercase character"},uppercase:{isValid:!h,text:"At least 1 uppercase character"},specialCharacter:{isValid:!x,text:"At least 1 number and 1 special character"}},u||v||h||x||""},C=()=>{i(),s.value=!0},P=()=>{s.value=!1},i=()=>{var o;b.value={...U},f.value={...N},(o=n==null?void 0:n.value)==null||o.resetFields()},y=w({currentPassword:[{required:!0,message:"Please type Current Password",trigger:["change","blur"]}],newPassword:[{validator:(o,u,v)=>{if(u==="")v(new Error("Please type New Password"));else{const h=$(u);h!==""?v(new Error(h)):v()}},trigger:["change","blur"]}],confirmPassword:[{validator:(o,u,v)=>{u===""?v(new Error("Please type Confirm Password")):u!==b.value.newPassword?v(new Error("Confirm Password doesn't match New Password!")):v()},trigger:["change","blur"]}]}),a=()=>{n.value&&n.value.validate(o=>{o&&E()})},j=o=>{o==="currentPassword"?b.value.hideCurrentPassword=!b.value.hideCurrentPassword:o==="confirmPassword"?b.value.hideConfirmPassword=!b.value.hideConfirmPassword:o==="newPassword"&&(b.value.hideNewPassword=!b.value.hideNewPassword)},E=()=>{W.alert("Are you sure you want to change your password?",{confirmButtonText:"Yes, Change it!",cancelButtonText:"No, cancel!",confirmButtonClass:"btn fw-bold btn-primary btn-sm",cancelButtonClass:"btn fw-bold btn-blue btn-sm",callback:{onConfirmed:()=>{A()}}},"warning")},A=()=>{var o,u;e.changePassword({params:{currentPassword:(o=b==null?void 0:b.value)==null?void 0:o.currentPassword,newPassword:(u=b==null?void 0:b.value)==null?void 0:u.newPassword},callback:{onSuccess:v=>{P(),W.resultAlert("Password is changed!","success")}}})};return{formRef:n,rules:y,modal:s,loading:_,targetData:b,rolesOptions:xe,checkPassword:f,show:C,hide:P,reset:i,submit:a,toggleEye:j}}}),Ns={class:"d-flex align-items-center w-100"},Es={class:"row"},js={class:"col-12 fv-row d-flex flex-column justify-content-stretch mb-7"},Ms={class:"position-relative input-password"},qs={class:"col-12 fv-row d-flex flex-column justify-content-stretch mb-7"},Fs={class:"position-relative input-password"},Os={class:"col-12 fv-row d-flex flex-column justify-content-stretch"},Ls={class:"position-relative input-password"},Bs={className:"validate-password my-5"},Ts=["className"],zs={class:"ms-2"},Js={class:"modal-footer d-flex justify-content-center align-items-center gap-7"},Ys=["disabled"],Zs=["data-kt-indicator","disabled"],Gs={key:0,class:"indicator-label"},Hs={key:1,class:"indicator-progress"};function Ks(e,s,U,N,b,_){const n=I("SvgIcon"),f=I("el-input"),$=I("el-form-item"),C=I("el-form"),P=I("el-dialog");return c(),X(P,{modelValue:e.modal,"onUpdate:modelValue":s[8]||(s[8]=i=>e.modal=i),"show-close":!1,width:"500","align-center":""},{header:p(()=>[t("div",Ns,[s[9]||(s[9]=t("h3",{class:"modal-title"},"Change Password",-1)),t("span",{class:"cursor-pointer ms-auto",onClick:s[0]||(s[0]=(...i)=>e.hide&&e.hide(...i))},[l(n,{icon:"closeModalIcon"})])])]),default:p(()=>[t("div",null,[l(C,{id:"change_pass_form",onSubmit:se(e.submit,["prevent"]),model:e.targetData,rules:e.rules,ref:"formRef",class:"form"},{default:p(()=>{var i,V,S;return[t("div",Es,[t("div",js,[s[10]||(s[10]=t("label",{class:"d-flex align-items-center fs-6 fw-semibold mb-2 text-break required"},"Current Password ",-1)),t("div",Ms,[l($,{prop:"currentPassword",class:"mt-auto mb-0"},{default:p(()=>{var y;return[l(f,{size:"large",class:"w-100",modelValue:e.targetData.currentPassword,"onUpdate:modelValue":s[1]||(s[1]=a=>e.targetData.currentPassword=a),name:"currentPassword",type:(y=e.targetData)!=null&&y.hideCurrentPassword?"password":"input"},null,8,["modelValue","type"])]}),_:1}),t("span",{class:"svg-icon svg-icon-1 position-absolute top-50 eye-icon",onClick:s[2]||(s[2]=()=>e.toggleEye("currentPassword"))},[l(n,{icon:(i=e.targetData)!=null&&i.hideCurrentPassword?"hidePasswordIcon":"showPasswordIcon"},null,8,["icon"])])])]),t("div",qs,[s[11]||(s[11]=t("label",{class:"d-flex align-items-center fs-6 fw-semibold mb-2 text-break required"},"New Password ",-1)),t("div",Fs,[l($,{prop:"newPassword",class:"mt-auto mb-0"},{default:p(()=>{var y;return[l(f,{size:"large",class:"w-100",modelValue:e.targetData.newPassword,"onUpdate:modelValue":s[3]||(s[3]=a=>e.targetData.newPassword=a),name:"newPassword",type:(y=e.targetData)!=null&&y.hideNewPassword?"password":"input"},null,8,["modelValue","type"])]}),_:1}),t("span",{class:"svg-icon svg-icon-1 position-absolute top-50 eye-icon",onClick:s[4]||(s[4]=()=>e.toggleEye("newPassword"))},[l(n,{icon:(V=e.targetData)!=null&&V.hideNewPassword?"hidePasswordIcon":"showPasswordIcon"},null,8,["icon"])])])]),t("div",Os,[s[12]||(s[12]=t("label",{class:"d-flex align-items-center fs-6 fw-semibold mb-2 required"}," Confirm Password ",-1)),t("div",Ls,[l($,{prop:"confirmPassword",class:"mt-auto mb-0"},{default:p(()=>{var y;return[l(f,{size:"large",class:"w-100",modelValue:e.targetData.confirmPassword,"onUpdate:modelValue":s[5]||(s[5]=a=>e.targetData.confirmPassword=a),name:"confirmPassword",type:(y=e.targetData)!=null&&y.hideConfirmPassword?"password":"input"},null,8,["modelValue","type"])]}),_:1}),t("span",{class:"svg-icon svg-icon-1 position-absolute top-50 eye-icon",onClick:s[6]||(s[6]=()=>e.toggleEye("confirmPassword"))},[l(n,{icon:(S=e.targetData)!=null&&S.hideConfirmPassword?"hidePasswordIcon":"showPasswordIcon"},null,8,["icon"])])])])]),t("div",Bs,[s[13]||(s[13]=t("p",null,"New password must contain:",-1)),t("div",null,[(c(!0),g(ee,null,Pe(Object.entries(e.checkPassword),([y,a])=>(c(),g("div",{key:y,className:`d-flex align-items-center mt-1 ${a.isValid?"valid":"invalid"}`},[l(n,{icon:a.isValid?"checkMarkIcon":"exclamationMarkIcon"},null,8,["icon"]),t("span",zs,R(a.text),1)],8,Ts))),128))])]),t("div",Js,[t("button",{type:"button",id:"kt_modal_add_customer_cancel",class:"btn text-gray-700 btn-sm btn-blue",onClick:s[7]||(s[7]=(...y)=>e.hide&&e.hide(...y)),disabled:e.loading}," Discard ",8,Ys),t("button",{"data-kt-indicator":e.loading?"on":null,class:"btn btn-sm btn-primary",type:"submit",disabled:e.loading},[e.loading?D("",!0):(c(),g("span",Gs," Save ")),e.loading?(c(),g("span",Hs,s[14]||(s[14]=[M(" Please wait... "),t("span",{class:"spinner-border spinner-border-sm align-middle ms-2"},null,-1)]))):D("",!0)],8,Zs)])]}),_:1},8,["onSubmit","model","rules"])])]),_:1},8,["modelValue"])}const Qs=ae(Rs,[["render",Ks],["__scopeId","data-v-eba06f4a"]]),Ws=te({name:"account-overview",components:{ChangePasswordModal:Qs},props:{userDetail:{type:Object,required:!0},hideRole:{type:Boolean,required:!1,default:!1},reloadUserData:{type:Function,required:!0}},setup(e){var re,ie,de,ue;const s=Re(),U=le(),N=he(),b=w(!1),_=w(!1),n=w(!1),f=w(!1),$=w(!1),C=w(null),P=w(null),i=w({...e.userDetail,companyId:(ie=(re=e==null?void 0:e.userDetail)==null?void 0:re.company)==null?void 0:ie.id}),V=w([]),S=!!((ue=(de=e==null?void 0:e.userDetail)==null?void 0:de.roles)!=null&&ue.find(r=>r.value===F.SystemAdmin));Ae(()=>{y()});const y=()=>{H()?a():j()},a=async()=>{s.getCompanies({params:{page:1,limit:500},callback:{onSuccess:r=>{var m;V.value=(m=r==null?void 0:r.items)==null?void 0:m.map(d=>({value:d==null?void 0:d.id,label:d==null?void 0:d.name}))}}})},j=async()=>{var m,d;const r=(d=(m=L)==null?void 0:m.getUserInfo())==null?void 0:d.companyId;r&&s.getCompanyById({id:r,callback:{onSuccess:k=>{V.value=[{value:k==null?void 0:k.id,label:k==null?void 0:k.name}]}}})},E=()=>{var m;const r=[];if((m=e==null?void 0:e.userDetail)!=null&&m.roles)for(const d of e.userDetail.roles)d.value!==F.SystemAdmin&&r.push(d.value);return r},A=w({userRoles:E()}),o=w(null),u=Ne().shape({email:K.emailAddress,mobilePhone:K.mobilePhone,officePhone:K.officePhone}),v={firstName:[{required:!0,message:"Please type First Name",trigger:["blur","change"]}],lastName:[{required:!0,message:"Please type Last Name",trigger:["blur","change"]}],email:[{required:!0,validator:(r,m,d)=>{u.fields.email.validate(m).then(()=>{d()}).catch(k=>{d(new Error(k.errors[0]))})},trigger:["blur","change"]}],mobilePhone:[{required:!0,validator:(r,m,d)=>{u.fields.mobilePhone.validate(m).then(()=>{d()}).catch(k=>{d(new Error(k.errors[0]))})},trigger:["blur","change"]}],officePhone:[{validator:(r,m,d)=>{m?u.fields.officePhone.validate(m).then(()=>{d()}).catch(k=>{d(new Error(k.errors[0]))}):d()},trigger:["blur","change"]}]},h=w(H()||S?{...v,companyId:[{required:!0,message:"Please select Company",trigger:["change","blur"]}]}:v),x=w({userRoles:[{type:"array",required:!0,message:"Please choose role",trigger:"change"}]}),O=()=>{b.value=!0},B=()=>{var r,m,d;b.value=!1,(r=C==null?void 0:C.value)==null||r.resetFields(),i.value={...e.userDetail,companyId:(d=(m=e==null?void 0:e.userDetail)==null?void 0:m.company)==null?void 0:d.id}},q=()=>{var r;_.value=!1,(r=P==null?void 0:P.value)==null||r.resetFields(),A.value={userRoles:E()}},T=()=>{_.value=!0},z=()=>{C.value&&C.value.validate(r=>{var m,d,k,me,ce,fe,ge,be,ve,pe,we;if(r){n.value=!0;const ye={id:(m=i.value)==null?void 0:m.id,email:((d=i.value)==null?void 0:d.email)!==((k=e==null?void 0:e.userDetail)==null?void 0:k.email)?(me=i.value)==null?void 0:me.email:null,firstName:(ce=i.value)==null?void 0:ce.firstName,lastName:(fe=i.value)==null?void 0:fe.lastName,address:((ge=i.value)==null?void 0:ge.address)||null,note:((be=i.value)==null?void 0:be.note)||null,officePhone:((ve=i.value)==null?void 0:ve.officePhone)||null,mobilePhone:(pe=i.value)==null?void 0:pe.mobilePhone,companyId:(we=i.value)==null?void 0:we.companyId};Y()?oe(ye,()=>{n.value=!1,b.value=!1}):ne(ye,()=>{n.value=!1,b.value=!1})}})},J=()=>{P.value&&P.value.validate(r=>{var m;r&&(f.value=!0,Y()?oe(A.value,()=>{f.value=!1,_.value=!1}):ne({...A.value,id:(m=i.value)==null?void 0:m.id},()=>{f.value=!1,_.value=!1}))})},ke=()=>{$.value=!0,U.resetUserPassword({params:{email:e.userDetail.email||""},callback:{onSuccess:()=>{$.value=!1,W.resultAlert("An email is sent to user’s email with the link to reset user password. Please check with your user!",void 0,"/media/reset-pass.jpg",70)},onFinish:()=>{$.value=!1}}})},De=()=>L.checkRole(F.CompanyAdmin),Y=()=>{var r,m;return N.name==="my-profile"||((r=L.getUserInfo())==null?void 0:r.id)===((m=e==null?void 0:e.userDetail)==null?void 0:m.id)},$e=()=>Q()||Y(),Ce=()=>{var r;(r=o==null?void 0:o.value)==null||r.show()},oe=(r,m)=>{U.updateMyProfile({params:r,callback:{onSuccess:d=>{var k;(k=e==null?void 0:e.reloadUserData)==null||k.call(e)},onFinish:m}})},ne=(r,m)=>{U.updateUserProfile({id:r==null?void 0:r.id,params:r,callback:{onSuccess:()=>{var d;(d=e==null?void 0:e.reloadUserData)==null||d.call(e)},onFinish:m}})};return{isSystemAdmin:H,isUserDetailSystemAdmin:S,UserType:F,roleRules:x,userInfoRules:h,formData:i,roleData:A,userInfoFormRef:C,roleFormRef:P,isEditMode:b,isEditRole:_,submittingUserInfo:n,submittingRole:f,changePasswordModal:o,companyList:V,loading:$,submit:z,resetPass:ke,submitRole:J,isAdmin:Q,editUserInfo:O,cancelEditUserInfo:B,isOwner:Y,isShowPassword:$e,isCompanyAdmin:De,editRole:T,cancelEditRole:q,toggleChangePassword:Ce}}}),Xs={class:"row gx-9"},et={class:"col-12 col-md-9 h-full mb-5 mb-xl-10 user-into-wrap",id:"kt_profile_details_view"},st={class:"card"},tt={class:"card-header cursor-pointer align-items-center"},at={key:0},lt={key:1,class:"align-self-center d-flex gap-4"},ot=["disabled"],nt={key:0,class:"indicator-label"},rt={key:1,class:"indicator-progress"},it={class:"card-body p-9"},dt={class:"row g-4"},ut={class:"col-md-6 col-xl-4 fv-row d-flex flex-column justify-content-stretch"},mt={class:"col-md-6 col-xl-4 fv-row d-flex flex-column justify-content-stretch"},ct={class:"col-md-6 col-xl-4 fv-row d-flex flex-column justify-content-stretch"},ft={class:"col-md-6 col-xl-4 fv-row d-flex flex-column justify-content-stretch"},gt={class:"col-md-6 col-xl-4 fv-row d-flex flex-column justify-content-stretch"},bt={key:0,class:"col-md-6 col-xl-4 fv-row d-flex flex-column justify-content-stretch"},vt={class:"col-12 fv-row d-flex flex-column justify-content-stretch"},pt={class:"col-12 fv-row d-flex flex-column justify-content-stretch"},wt={class:"col-12 col-md-3 h-full mb-5 mb-xl-10 security",id:"kt_profile_details_view"},yt={class:"card h-full"},ht={class:"px-7 py-5"},Pt={class:"d-flex align-items-center justify-content-between mb-6 flex-wrap"},kt={key:0},Dt={key:1,class:"align-self-center d-flex gap-4"},$t=["disabled"],Ct={type:"submit",class:"btn btn-flex btn-sm btn-success"},It={key:0,class:"indicator-label"},Ut={key:1,class:"indicator-progress"},_t={class:"d-flex gap-5 flex-column"},Vt={key:1,class:"mb-10"},St={class:"d-flex flex-column justify-content-end security-action"},xt={class:"svg-icon svg-icon-1 me-1"},At={key:0},Rt={key:1,class:"indicator-progress"},Nt={class:"svg-icon svg-icon-1 me-1"};function Et(e,s,U,N,b,_){const n=I("el-input"),f=I("el-form-item"),$=I("el-option"),C=I("el-select"),P=I("el-form"),i=I("el-checkbox"),V=I("el-checkbox-group"),S=I("inline-svg"),y=I("ChangePasswordModal");return c(),g(ee,null,[t("div",Xs,[t("div",et,[t("div",st,[t("div",tt,[s[17]||(s[17]=t("div",{class:"card-title m-0"},[t("h2",{class:"text-gray-800 text-hover-primary fs-2 fw-bold"}," User information ")],-1)),e.isAdmin()||e.isOwner()?(c(),g("div",at,[e.isEditMode?(c(),g("div",lt,[t("button",{class:"btn btn-flex btn-sm btn-danger",onClick:s[1]||(s[1]=(...a)=>e.cancelEditUserInfo&&e.cancelEditUserInfo(...a)),disabled:e.submittingUserInfo}," Cancel ",8,ot),t("button",{class:"btn btn-flex btn-sm btn-success",type:"button",onClick:s[2]||(s[2]=(...a)=>e.submit&&e.submit(...a))},[e.submittingUserInfo?D("",!0):(c(),g("span",nt," Save ")),e.submittingUserInfo?(c(),g("span",rt,s[16]||(s[16]=[M(" Please wait... "),t("span",{class:"spinner-border spinner-border-sm align-middle ms-2"},null,-1)]))):D("",!0)])])):(c(),g("button",{key:0,class:"btn btn-primary btn-sm align-self-center",onClick:s[0]||(s[0]=(...a)=>e.editUserInfo&&e.editUserInfo(...a))}," Edit "))])):D("",!0)]),t("div",it,[l(P,{id:"user_form",onSubmit:se(e.submit,["prevent"]),model:e.formData,rules:e.userInfoRules,ref:"userInfoFormRef",class:"form",disabled:!e.isEditMode||e.submittingUserInfo},{default:p(()=>[t("div",dt,[t("div",ut,[s[18]||(s[18]=t("label",{class:"d-flex align-items-center fs-6 fw-semibold mb-2 text-break required"},"First Name",-1)),l(f,{prop:"firstName"},{default:p(()=>[l(n,{placeholder:"First Name",name:"firstName",modelValue:e.formData.firstName,"onUpdate:modelValue":s[3]||(s[3]=a=>e.formData.firstName=a)},null,8,["modelValue"])]),_:1})]),t("div",mt,[s[19]||(s[19]=t("label",{class:"d-flex align-items-center fs-6 fw-semibold mb-2 text-break required"},"Last name",-1)),l(f,{prop:"lastName"},{default:p(()=>[l(n,{placeholder:"Last Name",name:"lastName",modelValue:e.formData.lastName,"onUpdate:modelValue":s[4]||(s[4]=a=>e.formData.lastName=a)},null,8,["modelValue"])]),_:1})]),t("div",ct,[s[20]||(s[20]=t("label",{class:"d-flex align-items-center fs-6 fw-semibold mb-2 text-break required"},"Email Address",-1)),l(f,{prop:"email"},{default:p(()=>[l(n,{placeholder:"Email",name:"email",modelValue:e.formData.email,"onUpdate:modelValue":s[5]||(s[5]=a=>e.formData.email=a)},null,8,["modelValue"])]),_:1})]),t("div",ft,[s[21]||(s[21]=t("label",{class:"d-flex align-items-center fs-6 fw-semibold mb-2 text-break"},"Office Phone",-1)),l(f,{prop:"officePhone"},{default:p(()=>[l(n,{placeholder:"Office Phone",name:"officePhone",modelValue:e.formData.officePhone,"onUpdate:modelValue":s[6]||(s[6]=a=>e.formData.officePhone=a)},null,8,["modelValue"])]),_:1})]),t("div",gt,[s[22]||(s[22]=t("label",{class:"d-flex align-items-center fs-6 fw-semibold mb-2 text-break required"},"Mobile Phone",-1)),l(f,{prop:"mobilePhone"},{default:p(()=>[l(n,{placeholder:"Mobile Phone",name:"mobilePhone",modelValue:e.formData.mobilePhone,"onUpdate:modelValue":s[7]||(s[7]=a=>e.formData.mobilePhone=a)},null,8,["modelValue"])]),_:1})]),e.isUserDetailSystemAdmin?D("",!0):(c(),g("div",bt,[s[23]||(s[23]=t("label",{class:"d-flex align-items-center fs-6 fw-semibold mb-2 text-break required"},"Company",-1)),l(f,{prop:"companyId",class:"mt-auto"},{default:p(()=>[l(C,{modelValue:e.formData.companyId,"onUpdate:modelValue":s[8]||(s[8]=a=>e.formData.companyId=a),placeholder:"Select company",class:"w-100",clearable:"",disabled:!e.isSystemAdmin()},{default:p(()=>[(c(!0),g(ee,null,Pe(e.companyList,a=>(c(),X($,{key:a.value,label:a.label,value:a.value,name:"companyId"},null,8,["label","value"]))),128))]),_:1},8,["modelValue","disabled"])]),_:1})])),t("div",vt,[s[24]||(s[24]=t("label",{class:"d-flex align-items-center fs-6 fw-semibold mb-2 text-break"},"Address",-1)),l(f,{prop:"address"},{default:p(()=>[l(n,{placeholder:"Address",name:"address",modelValue:e.formData.address,"onUpdate:modelValue":s[9]||(s[9]=a=>e.formData.address=a)},null,8,["modelValue"])]),_:1})]),t("div",pt,[s[25]||(s[25]=t("label",{class:"d-flex align-items-center fs-6 fw-semibold mb-2 text-break"},"Note",-1)),l(f,{prop:"note",class:"mt-auto"},{default:p(()=>[l(n,{class:"w-100",placeholder:"",type:"textarea",rows:"2",name:"note",modelValue:e.formData.note,"onUpdate:modelValue":s[10]||(s[10]=a=>e.formData.note=a)},null,8,["modelValue"])]),_:1})])]),s[26]||(s[26]=t("button",{class:"d-none",ype:"submit"},null,-1))]),_:1},8,["onSubmit","model","rules","disabled"])])])]),t("div",wt,[t("div",yt,[s[35]||(s[35]=t("div",{class:"card-header"},[t("div",{class:"card-title m-0"},[t("h2",{class:"text-gray-800 text-hover-primary fs-2 fw-bold"}," Security ")])],-1)),s[36]||(s[36]=t("div",{class:"separator border-gray-200"},null,-1)),t("div",ht,[e.hideRole?D("",!0):(c(),X(P,{key:0,id:"role_form",onSubmit:se(e.submitRole,["prevent"]),model:e.roleData,ref:"roleFormRef",class:"form",disabled:!e.isEditRole,rules:e.roleRules},{default:p(()=>[t("div",null,[t("div",Pt,[s[28]||(s[28]=t("label",{class:"form-label fw-bold"},"Roles",-1)),e.isAdmin()?(c(),g("div",kt,[e.isEditRole?(c(),g("div",Dt,[t("button",{type:"button",class:"btn btn-flex btn-sm btn-danger",onClick:s[12]||(s[12]=(...a)=>e.cancelEditRole&&e.cancelEditRole(...a)),disabled:e.submittingRole}," Cancel ",8,$t),t("button",Ct,[e.submittingRole?D("",!0):(c(),g("span",It," Save ")),e.submittingRole?(c(),g("span",Ut,s[27]||(s[27]=[M(" Please wait... "),t("span",{class:"spinner-border spinner-border-sm align-middle ms-2"},null,-1)]))):D("",!0)])])):(c(),g("button",{key:0,type:"button",class:"btn btn-primary btn-sm align-self-center",onClick:s[11]||(s[11]=(...a)=>e.editRole&&e.editRole(...a))}," Edit "))])):D("",!0)]),t("div",_t,[l(f,{prop:"userRoles"},{default:p(()=>[l(V,{modelValue:e.roleData.userRoles,"onUpdate:modelValue":s[13]||(s[13]=a=>e.roleData.userRoles=a),class:"d-flex flex-column"},{default:p(()=>[l(i,{label:e.UserType.CompanyAdmin,name:"role"},{default:p(()=>s[29]||(s[29]=[M(" Company Admin ")])),_:1},8,["label"]),l(i,{label:e.UserType.Supervisor,name:"role"},{default:p(()=>s[30]||(s[30]=[M(" Supervisor ")])),_:1},8,["label"]),l(i,{label:e.UserType.Engineer,name:"role"},{default:p(()=>s[31]||(s[31]=[M(" Engineer ")])),_:1},8,["label"])]),_:1},8,["modelValue"])]),_:1})])])]),_:1},8,["onSubmit","model","disabled","rules"])),e.isShowPassword()?(c(),g("div",Vt,[s[34]||(s[34]=t("label",{class:"form-label fw-bold"},"Password",-1)),t("div",St,[e.isAdmin()?(c(),g("button",{key:0,type:"button",class:"btn btn-sm btn-primary w-fit",onClick:s[14]||(s[14]=(...a)=>e.resetPass&&e.resetPass(...a))},[t("span",xt,[l(S,{src:"/media/icons/duotune/coding/cod001.svg"})]),e.loading?D("",!0):(c(),g("span",At," Reset password ")),e.loading?(c(),g("span",Rt,s[32]||(s[32]=[M(" Please wait... "),t("span",{class:"spinner-border spinner-border-sm align-middle ms-2"},null,-1)]))):D("",!0)])):D("",!0),e.isOwner()?(c(),g("button",{key:1,type:"button",class:"btn btn-sm btn-primary w-fit","data-kt-menu-dismiss":"true",onClick:s[15]||(s[15]=(...a)=>e.toggleChangePassword&&e.toggleChangePassword(...a))},[t("span",Nt,[l(S,{src:"/media/icons/duotune/coding/cod001.svg"})]),s[33]||(s[33]=M(" Change Password "))])):D("",!0)])])):D("",!0)])])])]),l(y,{ref:"changePasswordModal"},null,512)],64)}const zt=ae(Ws,[["render",Et]]);export{zt as O,Tt as U};
