import ApiService from "../services/ApiService";
import { get, noop } from "lodash";
import { defineStore } from "pinia";
import { handleFailure } from "../utils/handleFailure";
import type { Callback } from "../types/common";

export const useSolidControlEquipmentStore = defineStore(
  "solidControlEquipment",
  () => {
    const getSolidControlEquipments = async ({
      params,
      callback,
    }: {
      params: any;
      callback: Callback;
    }): Promise<void> => {
      const onSuccess = get(callback, "onSuccess", noop);
      const onFinish = get(callback, "onFinish", noop);

      try {
        ApiService.setHeader();
        const response = await ApiService.getWithParams(
          "solidControlEquipments",
          params
        );
        onSuccess(response.data?.data || response.data);
      } catch (error) {
        handleFailure(error, callback);
      } finally {
        onFinish();
      }
    };

    const getSolidControlEquipmentDetails = async ({
      id,
      callback,
    }: {
      id: string;
      callback: Callback;
    }): Promise<void> => {
      const onSuccess = get(callback, "onSuccess", noop);
      const onFinish = get(callback, "onFinish", noop);

      try {
        ApiService.setHeader();
        const response = await ApiService.get(`solidControlEquipments/${id}`);
        onSuccess(response.data?.data || response.data);
      } catch (error) {
        handleFailure(error, callback);
      } finally {
        onFinish();
      }
    };

    const updateSolidControlEquipment = async ({
      id,
      params,
      callback,
    }: {
      id: string;
      params: any;
      callback: Callback;
    }): Promise<void> => {
      const onSuccess = get(callback, "onSuccess", noop);
      const onFinish = get(callback, "onFinish", noop);

      try {
        ApiService.setHeader();
        const response = await ApiService.put(
          `solidControlEquipments/${id}`,
          params
        );
        onSuccess(response.data?.data || response.data);
      } catch (error) {
        handleFailure(error, callback);
      } finally {
        onFinish();
      }
    };

    const deleteSolidControlEquipment = async ({
      id,
      callback,
    }: {
      id: string;
      callback: Callback;
    }): Promise<void> => {
      const onSuccess = get(callback, "onSuccess", noop);
      const onFinish = get(callback, "onFinish", noop);

      try {
        ApiService.setHeader();
        const response = await ApiService.delete(
          `solidControlEquipments/${id}`
        );
        onSuccess(response.data?.data || response.data);
      } catch (error) {
        handleFailure(error, callback);
      } finally {
        onFinish();
      }
    };

    const createSolidControlEquipment = async ({
      params,
      callback,
    }: {
      params: any;
      callback: Callback;
    }): Promise<void> => {
      const onSuccess = get(callback, "onSuccess", noop);
      const onFinish = get(callback, "onFinish", noop);

      try {
        ApiService.setHeader();
        const response = await ApiService.post(
          "solidControlEquipments",
          params
        );
        onSuccess(response.data?.data || response.data);
      } catch (error) {
        handleFailure(error, callback);
      } finally {
        onFinish();
      }
    };

    return {
      getSolidControlEquipments,
      getSolidControlEquipmentDetails,
      updateSolidControlEquipment,
      deleteSolidControlEquipment,
      createSolidControlEquipment,
    };
  }
);
