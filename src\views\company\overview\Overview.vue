<template>
  <PageHeader title="Companies" :breadcrumbs="breadcrumbs" />
  <div
    class="bg-card-background text-card-text-light h-auto w-11/12 mx-auto my-4 rounded-xl flex flex-col items-center lg:w-4/5 lg:min-w-[1560px]"
  >
    <div class="h-auto w-11/12 flex flex-col items-start my-4 px-8 gap-3">
      <h1 class="font-bold">Companies</h1>
      <el-form @submit.prevent="searchCompanies" class="h-auto w-full">
        <el-form-item>
          <el-input
            placeholder="Search"
            v-model="search"
            name="search"
            size="large"
            ><template #prefix>
              <el-icon class="el-input__icon">
                <SvgIcon icon="searchIcon"
              /></el-icon> </template
          ></el-input> </el-form-item
      ></el-form>
      <button
        class="bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover rounded-md px-4 py-2 font-semibold flex flex-row items-center gap-2 justify-between"
        @click="toggleModal"
      >
        <SvgIcon icon="addIcon" />
        New
      </button>
    </div>
    <div v-if="loading" class="text-center p-5">
      <div class="spinner-border text-primary" role="status">
        <span class="sr-only">Loading...</span>
      </div>
    </div>
    <el-empty v-else-if="companyList.length === 0" description="No Data" />
    <div v-else class="h-auto w-11/12 overflow-x-scroll md:overflow-hidden">
      <table class="lg:mx-auto">
        <thead>
          <tr class="font-bold whitespace-nowrap">
            <th class="p-4">COMPANY ID</th>
            <th class="p-4">
              <div class="h-auto w-full flex items-center bg-blue gap-2">
                <span>COMPANY NAME</span>
                <span @click="onClickSort">
                  <SvgIcon :icon="arrowPath" />
                </span>
              </div>
            </th>
            <!-- <th class="min-w-200px">COMPANY REGISTER NUMBER</th> -->
            <th class="p-4">COMPANY ADMIN</th>
            <th class="p-4">CREATED DATE</th>
            <th class="p-4">ACTIONS</th>
          </tr>
        </thead>
        <tbody>
          <template v-for="item in companyList" :key="item.id">
            <tr
              class="font-bold my-2 text-center border-b-[1px] border-light-border border-dashed"
            >
              <td>
                <span class="font-semibold">{{ item?.id || "" }}</span>
              </td>
              <td class="p-4">
                <router-link
                  :to="`/companies/${item.id}`"
                  class="font-bold text-hover-primary underline underline-offset-[3px] decoration-2"
                  >{{ item?.name }}</router-link
                >
              </td>
              <!-- <td class="bg-red-300">
                      <span class="fw-semibold d-block fs-5">{{
                        item?.registerNumber || ""
                      }}</span>
                    </td> -->
              <td class="w-36 p-4">
                <div
                  class="flex flex-row items-center gap-3"
                  v-if="item?.users && item?.users.length"
                >
                  <img
                    class="h-11 w-11 rounded-full"
                    :src="
                      item?.users?.[0]?.avatar || '/media/avatars/blank.png'
                    "
                    alt="Avatar"
                  />
                  <div class="flex flex-col text-start overflow-auto">
                    <router-link
                      :to="`/users/${item?.users?.[0]?.id}`"
                      class="text-link font-semi hover:text-primary"
                      >{{
                        `${item?.users?.[0]?.firstName || ""} ${
                          item?.users?.[0]?.lastName || ""
                        }`
                      }}</router-link
                    >
                    <span class="font-semibold truncate">{{
                      item?.users?.[0]?.email || ""
                    }}</span>
                  </div>
                </div>
              </td>
              <td v-if="isSystemAdmin()" class="p-4">
                <span class="font-semibold whitespace-nowrap">{{
                  formatDate(item?.createdAt, "MMM DD, YYYY")
                }}</span>
              </td>
              <td class="p-4">
                <div
                  class="h-auto w-full flex flex-row gap-2 items-center justify-evenly"
                >
                  <button
                    class="bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover h-8 w-8 rounded-lg pl-[7.5px] pt-[1px] md:h-10 md:w-10 md:pl-[6.52px] md:pt-[2px]"
                    @click="toggleEditCompany(item?.id!)"
                  >
                    <SvgIcon icon="newReportIcon" classname="md:h-6 md:w-6" />
                  </button>
                  <button
                    class="bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover h-8 w-8 rounded-lg pl-[7.5px] pt-[1px] md:h-10 md:w-10 md:pl-[6.52px] md:pb-[5px]"
                    @click="deleteCompany(item?.id!)"
                  >
                    <span class="text-danger">
                      <SvgIcon icon="trashIcon" classname="md:h-7 md:w-7" />
                    </span>
                  </button>
                </div>
              </td>
            </tr>
          </template>
        </tbody>
      </table>
    </div>
    <div class="flex flex-col items-center my-5">
      <div v-if="companyList?.length" class="font-semibold">
        {{
          `Showing ${(currentPage - 1) * 10 + 1} to ${
            companyList?.length
          } of ${totalElements} entries`
        }}
      </div>
      <TablePagination
        v-if="pageCount >= 1"
        :total-pages="pageCount"
        :total="totalElements"
        :per-page="10"
        :current-page="currentPage"
        @page-change="pageChange"
      />
    </div>
  </div>
  <CompanyModal
    :isVisible="isModalVisible"
    :close="toggleModal"
    :loadPage="getCompanyList"
    ref="companyModal"
  />
</template>

<script lang="ts">
import PageHeader from "@/components/PageHeader.vue";
import TablePagination from "@/components/common/TablePagination.vue";
import SvgIcon from "@/constants/SvgIcon.vue";
import { SortByEnum, SortDirectionEnum } from "@/constants/table";
import { formatDate } from "@/utils/date";
import AlertService from "@/services/AlertService";
import { isSystemAdmin } from "@/services/JwtService";
import { useCompanyStore } from "@/stores/company";
import {
  computed,
  defineComponent,
  onMounted,
  ref,
  type Ref,
  watch,
} from "vue";
import CompanyModal from "./CompanyModal.vue";

export default defineComponent({
  name: "companies-overview",
  components: {
    PageHeader,
    SvgIcon,
    TablePagination,
    CompanyModal,
  },
  setup() {
    const companyStore = useCompanyStore();
    const breadcrumbs = ["Companies", "Overview"];
    const checkedRows = ref<Array<string>>([]);
    const checkAll = ref<boolean>(false);
    const pageCount = ref(0);
    const totalElements = ref(0);
    const currentPage = ref(1);
    const loading = ref(false);
    const search = ref<string>("");
    const companyModal: Ref<any> = ref<typeof CompanyModal | null>(null);
    const isModalVisible = ref(false);
    const companyList = ref<Company.Info[]>([]);
    const sortDirection = ref<SortDirectionEnum>(SortDirectionEnum.ASC);

    onMounted(() => {
      getCompanyList();
    });

    watch(currentPage, () => {
      getCompanyList();
    });

    watch(sortDirection, () => {
      getCompanyList();
    });

    const arrowPath = computed(() => {
      return sortDirection.value === SortDirectionEnum.ASC
        ? "sortArrowAsc"
        : "sortArrowDesc";
    });

    const onClickSort = () => {
      if (sortDirection.value === SortDirectionEnum.ASC) {
        sortDirection.value = SortDirectionEnum.DESC;
      } else {
        sortDirection.value = SortDirectionEnum.ASC;
      }
    };

    const getCompanyList = async (): Promise<void> => {
      loading.value = true;
      companyStore.getCompanies({
        params: {
          sortBy: SortByEnum.Name,
          sortDirection: sortDirection.value,
          page: currentPage.value,
          limit: 10,
          keyword: search.value.trim() || null,
        },
        callback: {
          onSuccess: (res: any) => {
            companyList.value = [...res?.items];
            pageCount.value = res?.totalPage;
            totalElements.value = res?.total;
            currentPage.value = res?.page;
          },
          onFinish: () => {
            loading.value = false;
          },
        },
      });
    };

    const searchCompanies = () => {
      currentPage.value = 1;
      getCompanyList();
    };

    const pageChange = (newPage: number) => {
      currentPage.value = newPage;
    };

    const toggleModal = (): void => {
      isModalVisible.value = !isModalVisible.value;
    };

    const toggleEditCompany = (id: string): void => {
      companyModal?.value?.setId(id);
      isModalVisible.value = !isModalVisible.value;
    };

    watch(checkedRows, (newValue) => {
      checkAll.value =
        companyList.value.length !== 0 &&
        newValue.length === companyList.value.length;
    });

    const onToggleCheckAll = (e: any) => {
      if (e?.target?.checked) {
        checkedRows.value = companyList.value.map((company) => company.id!);
      } else {
        checkedRows.value = [];
      }
    };

    const deleteCompany = (id: string) => {
      AlertService.deletionAlert({
        onConfirmed: () => {
          deleteCompanyById(id);
        },
      });
    };

    // const onRemove = () => {
    //   SwalPopup.swalDeletePopup({
    //     onConfirmed: () => {
    //       deleteCompanyById(id, true);
    //     },
    //   });
    // };

    const deleteCompanyById = async (id: string): Promise<void> => {
      loading.value = true;
      companyStore.deleteCompanyById({
        id,
        callback: {
          onSuccess: (_res: any) => {
            getCompanyList();
          },
          onFinish: () => {
            loading.value = false;
          },
        },
      });
    };

    return {
      sortDirection,
      loading,
      search,
      breadcrumbs,
      checkedRows,
      checkAll,
      companyList,
      currentPage,
      totalElements,
      pageCount,
      companyModal,
      isModalVisible,
      isSystemAdmin,
      arrowPath,
      formatDate,
      toggleModal,
      pageChange,
      deleteCompany,
      toggleEditCompany,
      onToggleCheckAll,
      onClickSort,
      // onRemove,
      searchCompanies,
      getCompanyList,
    };
  },
});
</script>
