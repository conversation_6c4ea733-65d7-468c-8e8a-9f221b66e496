<template>
  <div
    v-if="isVisible"
    class="fixed top-0 right-0 bottom-0 left-0 z-40 flex items-center"
  >
    <!-- Dark overlay background -->
    <div class="absolute inset-0 bg-sidebar-background opacity-25"></div>
    <div
      class="relative bg-card-background text-card-text max-h-11/12 w-11/12 overflow-x-scroll flex flex-col items-center gap-3 mx-auto p-5 rounded-xl md:overflow-hidden md:w-4/5 md:text-lg lg:w-2/5 lg:h-auto"
    >
      <div class="h-auto w-full flex flex-row items-center justify-between">
        <h3 class="text-lg font-bold">
          {{ id ? "Edit Contact" : "New Contact" }}
        </h3>
        <span class="cursor-pointer" @click="closeModal">
          <SvgIcon icon="closeModalIcon" />
        </span>
      </div>

      <el-form
        id="product_form"
        @submit.prevent="submit"
        :model="targetData"
        :rules="rules"
        ref="formRef"
        class="h-auto w-full flex flex-col gap-3"
      >
        <div class="h-auto w-full flex flex-col gap-3 md:flex-row md:gap-4">
          <div class="h-auto w-full flex flex-col gap-3">
            <div class="h-auto w-full flex flex-col gap-2">
              <label class="font-semibold">Name </label>
              <el-form-item prop="name" class="mt-auto">
                <el-input
                  v-model="targetData.name"
                  placeholder=""
                  name="name"
                ></el-input>
              </el-form-item>
            </div>
            <div class="h-auto w-full flex flex-col gap-2">
              <label class="font-semibold">Email Address </label>
              <el-form-item prop="emailAddress" class="mt-auto">
                <el-input
                  v-model="targetData.emailAddress"
                  placeholder=""
                  name="emailAddress"
                ></el-input>
              </el-form-item>
            </div>
            <div class="h-auto w-full flex flex-col gap-2">
              <label class="font-semibold">Address </label>
              <el-form-item prop="address" class="mt-auto">
                <el-input
                  class="w-100"
                  v-model="targetData.address"
                  placeholder=""
                  name="address"
                ></el-input>
              </el-form-item>
            </div>
          </div>
          <div class="h-auto w-full flex flex-col gap-3">
            <div class="h-auto w-full flex flex-col gap-2">
              <label class="font-semibold">Mobile Phone </label>
              <el-form-item prop="mobilePhone" class="mt-auto">
                <el-input
                  v-model="targetData.mobilePhone"
                  placeholder=""
                  name="mobilePhone"
                ></el-input>
              </el-form-item>
            </div>
            <div class="h-auto w-full flex flex-col gap-2">
              <label class="font-semibold">Office Phone </label>
              <el-form-item prop="officePhone" class="mt-auto">
                <el-input
                  class="w-100"
                  v-model="targetData.officePhone"
                  placeholder=""
                  name="officePhone"
                ></el-input>
              </el-form-item>
            </div>
          </div>
        </div>

        <div class="h-auto w-full flex flex-col gap-2">
          <label class="font-semibold">Note </label>
          <el-form-item prop="notes" class="mt-auto">
            <el-input
              class="h-auto w-full"
              v-model="targetData.notes"
              placeholder=""
              name="notes"
              type="textarea"
              :rows="3"
            ></el-input>
          </el-form-item>
        </div>
        <div class="h-auto w-full flex flex-row items-center justify-between">
          <label class="font-semibold">Primary Contact? </label>
          <el-form-item prop="primaryContact" style="margin-bottom: 0">
            <el-checkbox
              v-model="targetData.primaryContact"
              name="primaryContact"
            ></el-checkbox>
          </el-form-item>
        </div>
        <div class="h-auto w-full flex flex-row items-center justify-between">
          <label class="font-semibold">Notify on new report </label>
          <el-form-item prop="notifyOnNewReport" style="margin-bottom: 0">
            <el-checkbox
              v-model="targetData.notifyOnNewReport"
              name="notifyOnNewReport"
            ></el-checkbox>
          </el-form-item>
        </div>

        <div class="h-auto w-full flex flex-row items-start mt-4 gap-3">
          <button
            type="button"
            class="bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover rounded-md px-4 py-2 font-semibold"
            @click="closeModal"
            :disabled="loading"
          >
            Discard
          </button>
          <button
            class="bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover rounded-md px-4 py-2 font-semibold"
            type="submit"
            :disabled="loading"
          >
            <span v-if="!loading" class="indicator-label"> Save </span>
            <span v-if="loading" class="indicator-progress">
              Please wait...
              <span
                class="spinner-border spinner-border-sm align-middle ms-2"
              ></span>
            </span>
          </button>
        </div>
      </el-form>
    </div>
  </div>
</template>

<script lang="ts">
import SvgIcon from "@/constants/SvgIcon.vue";
import { rolesOptions } from "@/constants/user";
import { yupValidate } from "@/utils/validator";
import { useCustomerContactStore } from "@/stores/customerContact";
import type { FormRules } from "element-plus";
import { defineComponent, ref, watch } from "vue";
import { useRoute } from "vue-router";
import * as yup from "yup";

export default defineComponent({
  name: "customer-modal",
  components: { SvgIcon },
  props: {
    isVisible: {
      type: Boolean,
      required: true,
    },
    close: {
      type: Function,
      required: true,
    },
    loadPage: {
      type: Function,
      required: false,
    },
  },
  setup(props) {
    const route = useRoute();
    const modal = ref(false);
    const customerContactStore = useCustomerContactStore();
    const initialValue: Customer.Contact = {
      customerId: "",
      name: "",
      emailAddress: "",
      mobilePhone: "",
      officePhone: "",
      address: "",
      notes: "",
      primaryContact: false,
      notifyOnNewReport: false,
    };

    const targetData = ref<Customer.Contact>({ ...initialValue });
    const formRef = ref<null | HTMLFormElement>(null);
    const loading = ref<boolean>(false);
    const id = ref("");
    const customerId = route.params?.customerId?.toString() || "";

    watch(modal, (newValue) => {
      if (newValue === false) {
        reset();
      } else {
        getContacts();
      }
    });

    const getContacts = async (): Promise<void> => {
      if (!id.value) return;

      loading.value = true;
      customerContactStore.getContactDetails({
        id: id.value,
        callback: {
          onSuccess: (res: any) => {
            targetData.value = { ...res };
          },
          onFinish: () => {
            loading.value = false;
          },
        },
      });
    };

    const closeModal = () => {
      props.close();
    };

    const setId = (solidId: string) => {
      id.value = solidId.toString();
    };

    const yupRule = yup.object({
      emailAddress: yupValidate.emailAddress,
      mobilePhone: yupValidate.mobilePhone,
      officePhone: yupValidate.officePhone,
    }) satisfies yup.ObjectSchema<any>;

    const rules = ref<FormRules<any>>({
      name: [
        {
          required: true,
          message: "Please type Contact Name",
          trigger: ["blur", "change"],
        },
      ],
      emailAddress: [
        {
          required: true,
          validator: (_: any, value: any, callback: any) => {
            (yupRule.fields.emailAddress as yup.AnySchema)
              .validate(value)
              .then(() => {
                callback();
              })
              .catch((error: any) => {
                callback(new Error(error.errors[0]));
              });
          },
          trigger: ["blur", "change"],
        },
      ],
      mobilePhone: [
        {
          required: true,
          validator: (_: any, value: any, callback: any) => {
            (yupRule.fields.mobilePhone as yup.AnySchema)
              .validate(value)
              .then(() => {
                callback();
              })
              .catch((error: any) => {
                callback(new Error(error.errors[0]));
              });
          },
          trigger: ["blur", "change"],
        },
      ],
      officePhone: [
        {
          validator: (_: any, value: any, callback: any) => {
            if (value) {
              (yupRule.fields.officePhone as yup.AnySchema)
                .validate(value)
                .then(() => {
                  callback();
                })
                .catch((error: any) => {
                  callback(new Error(error.errors[0]));
                });
            } else {
              callback();
            }
          },
          trigger: ["blur", "change"],
        },
      ],
    });

    const submit = () => {
      if (!formRef.value) {
        return;
      }

      formRef.value.validate((valid: boolean) => {
        if (valid) {
          const params = {
            ...targetData.value,
            officePhone: targetData.value?.officePhone || null,
            address: targetData.value?.address || null,
            note: targetData.value?.notes || null,
            customerId: customerId,
          };

          if (id.value) {
            updateContact(params);
          } else {
            addContact(params);
          }
        }
      });
    };

    const addContact = async (params: Customer.Contact): Promise<void> => {
      loading.value = true;

      customerContactStore.createContact({
        params,
        callback: {
          onSuccess: (_res: any) => {
            props.close();
            props?.loadPage?.();
          },
          onFinish: () => {
            loading.value = false;
          },
        },
      });
    };

    const updateContact = async (params: Customer.Contact): Promise<void> => {
      if (!id.value) return;
      loading.value = true;

      customerContactStore.updateCustomerContact({
        id: id.value,
        params,
        callback: {
          onSuccess: (_res: any) => {
            props.close();
            props?.loadPage?.();
          },
          onFinish: () => {
            loading.value = false;
          },
        },
      });
    };

    const reset = () => {
      id.value = "";
      targetData.value = { ...initialValue };
      formRef?.value?.resetFields();
    };

    return {
      id,
      modal,
      rules,
      loading,
      targetData,
      formRef,
      rolesOptions,
      submit,
      setId,
      reset,
      closeModal,
    };
  },
});
</script>
