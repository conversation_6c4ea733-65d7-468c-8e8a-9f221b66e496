{"name": "muddysoft-web-main", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc -b && vite build", "preview": "vite preview"}, "dependencies": {"@capacitor/android": "^7.2.0", "@capacitor/core": "^7.2.0", "@capacitor/ios": "^7.2.0", "@fortawesome/fontawesome-free": "^6.7.2", "@tailwindcss/vite": "^4.1.5", "apexcharts": "^4.7.0", "axios": "^1.9.0", "clipboard": "^2.0.11", "element-plus": "^2.9.10", "lodash": "^4.17.21", "moment": "^2.30.1", "moment-timezone": "^0.5.48", "pinia": "^3.0.2", "sweetalert2": "^11.20.0", "tailwindcss": "^4.1.5", "vee-validate": "^4.15.0", "vue": "^3.5.13", "vue-axios": "^3.5.2", "vue-i18n": "^11.1.3", "vue-router": "^4.5.1", "vue3-apexcharts": "^1.8.0", "vue3-otp-input": "^0.5.30", "yup": "^1.6.1"}, "devDependencies": {"@capacitor/cli": "^7.2.0", "@types/lodash": "^4.17.16", "@vitejs/plugin-vue": "^5.2.2", "@vue/tsconfig": "^0.7.0", "typescript": "~5.7.2", "vite": "^6.3.1", "vue-tsc": "^2.2.8"}}