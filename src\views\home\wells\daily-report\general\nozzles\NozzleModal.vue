<template>
  <div
    v-if="isVisible"
    class="fixed top-0 right-0 bottom-0 left-0 z-40 flex items-center"
  >
    <!-- Dark overlay background -->
    <div class="absolute inset-0 bg-sidebar-background opacity-25"></div>
    <!-- Modal content -->
    <div
      class="relative bg-card-background text-card-text-light max-h-11/12 w-11/12 flex flex-col items-center gap-3 mx-auto my-6 p-5 rounded-xl md:w-4/5 md:text-lg lg:w-2/5 lg:h-auto"
    >
      <div class="flex flex-row h-auto w-full items-center justify-between">
        <h3 class="text-lg font-bold">
          {{ `${id ? "Edit Nozzle" : "New Nozzle"}` }}
        </h3>
        <span class="cursor-pointer" @click="closeModal">
          <SvgIcon icon="closeModalIcon" />
        </span>
      </div>

      <el-form
        id="nozzle_form"
        @submit.prevent="submit"
        :model="targetData"
        :rules="rules"
        ref="formRef"
        class="h-auto w-full flex flex-col gap-3"
      >
        <div class="flex flex-col gap-1">
          <label class="font-bold"
            >Identification Number<span class="text-danger-active font-light"
              >*</span
            ><el-popover placement="top" :width="300" trigger="hover">
              <template #reference>
                <i class="fas fa-exclamation-circle ms-2 fs-7"></i>
              </template>
              <span
                >The "No." or Number refers to the identification or serial
                number assigned to the nozzle. It helps track the specific
                nozzle being used or replaced.
              </span>
            </el-popover></label
          >
          <el-form-item prop="identificationNumber" class="mt-auto">
            <el-input
              v-model="targetData.identificationNumber"
              placeholder=""
              name="identificationNumber"
            ></el-input>
          </el-form-item>
        </div>

        <div class="flex flex-col gap-1">
          <label class="font-bold"
            ><span>Orifice Size</span
            ><el-popover placement="top" :width="300" trigger="hover">
              <template #reference>
                <i class="fas fa-exclamation-circle ms-2 fs-7"></i>
              </template>
              <span
                >The "Orifice Size" represents the diameter of the nozzle's
                orifice, typically measured in 1/32-inch increments. The nozzle
                size is crucial as it affects the flow rate and pressure of the
                drilling fluid being pumped through it.
              </span>
            </el-popover></label
          >
          <el-form-item prop="orificeSize">
            <el-input
              type="number"
              :controls="false"
              step="any"
              v-model="targetData.orificeSize"
              placeholder=""
              name="orificeSize"
            ></el-input>
          </el-form-item>
        </div>

        <div class="h-auto w-full flex flex-row items-center gap-2">
          <button
            type="button"
            class="bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover rounded-md px-4 py-2 font-semibold md:px-4 md:py-3 md:text-xl"
            @click="closeModal"
            :disabled="loading"
          >
            Discard
          </button>
          <button
            class="bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover rounded-md px-4 py-2 font-semibold md:px-4 md:py-3 md:text-xl"
            type="submit"
            :disabled="loading"
          >
            <span v-if="!loading" class="indicator-label"> Save </span>
            <span v-if="loading" class="indicator-progress">
              Please wait...
              <span
                class="spinner-border spinner-border-sm align-middle ms-2"
              ></span>
            </span>
          </button>
        </div>
      </el-form>
    </div>
  </div>
</template>

<script lang="ts">
import SvgIcon from "@/constants/SvgIcon.vue";
import { useNozzleStore } from "@/stores/nozzle";
import { defineComponent, inject, ref, watch } from "vue";
import { useRoute } from "vue-router";
import type { Provide } from "@/types/injection-types";

interface NewNozzleData {
  identificationNumber: string;
  orificeSize: number | null;
}

export default defineComponent({
  name: "nozzle-modal",
  components: { SvgIcon },
  props: {
    isVisible: {
      type: Boolean,
      required: true,
    },
    close: {
      type: Function,
      required: true,
    },
    loadPage: {
      type: Function,
      default: () => {},
      required: true,
    },
  },
  setup(props) {
    const route = useRoute();
    const nozzleStore = useNozzleStore();
    const modal = ref(false);
    const targetData = ref<NewNozzleData>({
      identificationNumber: "",
      orificeSize: null,
    });
    const formRef = ref<null | HTMLFormElement>(null);
    const loading = ref<boolean>(false);
    const id = ref("");
    const dailyReportProvide = inject<Provide.DailyReport>("dailyReport");

    watch(id, (newValue) => {
      if (newValue !== "") {
        getNozzleDetails();
      }
    });

    watch(
      () => props.isVisible,
      (newValue) => {
        if (newValue === false) {
          id.value = "";
          reset();
          formRef?.value?.resetFields();
        }
      }
    );

    const getNozzleDetails = async (): Promise<void> => {
      nozzleStore.getNozzleDetails({
        id: id.value,
        callback: {
          onSuccess: (res: any) => {
            targetData.value = res;
          },
        },
      });
    };

    const updateNozzle = async (param: any): Promise<void> => {
      loading.value = true;
      nozzleStore.updateNozzle({
        id: id.value,
        params: param,
        callback: {
          onSuccess: (_res: any) => {
            if (props?.loadPage) {
              props?.loadPage();
            }
            props.close();
          },
          onFinish: (_err: any) => {
            loading.value = false;
          },
        },
      });
    };

    const createNozzle = async (param: any): Promise<void> => {
      loading.value = true;
      nozzleStore.createNozzle({
        params: param,
        callback: {
          onSuccess: (_res: any) => {
            if (props?.loadPage) {
              props?.loadPage();
            }
            props.close();
          },
          onFinish: (_err: any) => {
            loading.value = false;
          },
        },
      });
    };

    const closeModal = () => {
      props.close();
    };

    const setId = (wellId: string) => {
      id.value = wellId.toString();
    };

    const rules = ref({
      identificationNumber: [
        {
          required: true,
          message: "Please type Identification Number",
          trigger: "blur",
        },
      ],
    });

    const submit = () => {
      if (!formRef.value) {
        return;
      }

      formRef.value.validate((valid: boolean) => {
        if (valid) {
          loading.value = true;
          const param = {
            identificationNumber: targetData?.value?.identificationNumber,
            orificeSize: Number(targetData?.value?.orificeSize),
          };

          if (id?.value) {
            updateNozzle({
              ...param,
              dailyReportId: dailyReportProvide?.getDailyReportId(),
            });
          } else {
            dailyReportProvide?.createDailyReport({
              wellId: route?.params?.id as string,
              callback: {
                onSuccess: (res: string) => {
                  createNozzle({ ...param, dailyReportId: res });
                },
              },
            });
          }
        }
      });
    };

    const reset = () => {
      targetData.value = {
        identificationNumber: "",
        orificeSize: null,
      };
    };

    return {
      id,
      modal,
      rules,
      loading,
      targetData,
      formRef,
      closeModal,
      submit,
      setId,
      reset,
    };
  },
});
</script>
