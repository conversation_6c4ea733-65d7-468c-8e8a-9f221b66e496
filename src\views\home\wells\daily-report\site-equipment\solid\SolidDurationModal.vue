<template>
  <div
    v-if="isVisible"
    class="fixed top-0 right-0 bottom-0 left-0 z-40 flex items-center"
  >
    <!-- Dark overlay background -->
    <div class="absolute inset-0 bg-sidebar-background opacity-25"></div>
    <div
      class="relative bg-card-background text-card-text-light max-h-11/12 w-11/12 flex flex-col items-center gap-3 mx-auto my-6 p-4 rounded-xl md:w-1/4"
    >
      <div class="flex flex-row h-auto w-full items-center justify-between">
        <h3 class="text-lg font-bold">
          {{ `${id ? "Edit Solid duration" : "Add Solid duration"}` }}
        </h3>
        <span class="cursor-pointer ms-auto" @click="closeModal">
          <SvgIcon icon="closeModalIcon" />
        </span>
      </div>

      <el-form
        id="solid_duration_form"
        @submit.prevent="submit"
        :model="targetData"
        :rules="rules"
        ref="formRef"
        class="h-auto w-full flex flex-col gap-3"
      >
        <div class="flex flex-col gap-1">
          <label class="font-bold">Duration (hr) </label>
          <el-form-item prop="duration" class="mt-auto">
            <el-input
              type="number"
              :controls="false"
              step="any"
              v-model="targetData.duration"
              placeholder=""
              name="duration"
            ></el-input>
          </el-form-item>
        </div>

        <div class="h-auto w-full flex flex-row items-center gap-2">
          <button
            type="button"
            class="bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover rounded-md px-4 py-2 font-semibold"
            @click="closeModal"
            :disabled="loading"
          >
            Discard
          </button>
          <button
            class="bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover rounded-md px-4 py-2 font-semibold"
            type="submit"
            :disabled="loading"
          >
            <span v-if="!loading" class="indicator-label"> Save </span>
            <span v-if="loading" class="indicator-progress">
              Please wait...
              <span
                class="spinner-border spinner-border-sm align-middle ms-2"
              ></span>
            </span>
          </button>
        </div>
      </el-form>
    </div>
  </div>
</template>

<script lang="ts">
import SvgIcon from "@/constants/SvgIcon.vue";
import { useSolidControlEquipmentTimeStore } from "@/stores/solid-control-equipment-time";
import { defineComponent, ref, watch } from "vue";

export default defineComponent({
  name: "solid-duration-modal",
  components: { SvgIcon },
  props: {
    isVisible: {
      type: Boolean,
      required: true,
    },
    close: {
      type: Function,
      required: true,
    },
    loadPage: {
      type: Function,
      required: false,
    },
    zIndex: {
      type: Number,
      default: 40,
      required: false,
    },
  },
  setup(props) {
    const solidControlEquipmentTimeStore = useSolidControlEquipmentTimeStore();
    const modal = ref(false);
    const targetData = ref({
      duration: null,
    });
    const formRef = ref<null | HTMLFormElement>(null);
    const loading = ref<boolean>(false);
    const solidControlEquipmentId = ref("");
    const id = ref("");

    watch(id, (newValue) => {
      if (newValue !== "") {
        getSolidDuration();
      }
    });

    watch(
      () => props.isVisible,
      (newValue) => {
        if (newValue === false) {
          id.value = "";
          solidControlEquipmentId.value = "";
          reset();
          formRef?.value?.resetFields();
        }
      }
    );

    const getSolidDuration = async (): Promise<void> => {
      solidControlEquipmentTimeStore.getSolidControlEquipmentTimeDetails({
        id: id.value,
        callback: {
          onSuccess: (res: any) => {
            targetData.value = { ...res };
          },
        },
      });
    };

    const updateSolidDuration = async (param: any): Promise<void> => {
      loading.value = true;
      solidControlEquipmentTimeStore.updateSolidControlEquipmentTime({
        id: id.value,
        params: param,
        callback: {
          onSuccess: (_res: any) => {
            if (props?.loadPage) {
              props?.loadPage();
            }
            closeModal();
          },
          onFinish: (_err: any) => {
            loading.value = false;
          },
        },
      });
    };

    const createSolidDuration = async (param: any): Promise<void> => {
      loading.value = true;
      solidControlEquipmentTimeStore.createSolidControlEquipmentTime({
        params: param,
        callback: {
          onSuccess: (_res: any) => {
            if (props?.loadPage) {
              props?.loadPage();
            }
            closeModal();
          },
          onFinish: (_err: any) => {
            loading.value = false;
          },
        },
      });
    };

    const closeModal = () => {
      props.close();
    };

    const setId = (solid: string, duration: string) => {
      solidControlEquipmentId.value = solid;
      id.value = duration;
    };

    const rules = ref({
      duration: [
        {
          required: true,
          message: "Please type Duration",
          trigger: "blur",
        },
      ],
    });

    const submit = () => {
      if (!formRef.value) {
        return;
      }

      formRef.value.validate((valid: boolean) => {
        if (valid) {
          const param = {
            solidControlEquipmentId: solidControlEquipmentId?.value,
            duration: Number(targetData?.value?.duration),
          };

          if (id?.value) {
            updateSolidDuration(param);
          } else {
            createSolidDuration(param);
          }
        }
      });
    };

    const reset = () => {
      targetData.value = {
        duration: null,
      };
    };

    return {
      id,
      modal,
      rules,
      loading,
      targetData,
      formRef,
      closeModal,
      submit,
      setId,
      reset,
    };
  },
});
</script>
