<template>
  <div
    class="bg-card-background text-card-text-light w-11/12 mx-auto mb-4 h-auto rounded-xl"
  >
    <div class="h-auto w-11/12 flex flex-col gap-6 mx-auto mt-7 px-3 py-4">
      <h1 class="font-bold">Volume Tracking</h1>
    </div>
    <div class="card-body">
      <div v-if="loading" class="text-center">
        <div class="spinner-border text-primary" role="status">
          <span class="sr-only">Loading...</span>
        </div>
      </div>
      <div v-else class="h-auto w-full">
        <NoEntries
          v-if="volumeTrackingList.length === 0"
          :addNew="() => toggleStorageOrPitModal()"
        />
        <div
          v-else
          class="h-auto w-full mx-auto p-4 grid grid-cols-1 gap-4 md:grid-cols-2 md:gap-7 lg:grid-cols-3"
        >
          <div
            v-for="item in volumeTrackingList"
            :key="item?.id"
            class="relative h-auto w-full mx-auto bg-minicard-background text-minicard-text-light flex flex-col items-center p-4 pt-7 rounded-xl border-t-2 border-active my-2 first:mt-4 last:mb-5 font-semibold md:first:mt-0 md:last:mb-0 md:m-0"
          >
            <div class="h-auto w-full flex justify-between items-center gap-3">
              <h5
                class="h-auto w-full text-xl font-bold underline underline-offset-3"
                @click="() => toggleVolumeDetailsModal(item?.id)"
              >
                {{ item?.name }}
              </h5>
              <span
                class="rounded-lg px-2 py-1"
                :class="item?.status === 1 ? 'bg-success' : 'bg-danger'"
                >{{ item?.status === 1 ? "Active" : "Inactive" }}</span
              >
            </div>

            <div
              class="h-auto w-full flex items-center justify-between border-dashed border-b-[1px] py-3"
            >
              <span>Type</span>
              <span>{{ item?.storageType }} </span>
            </div>
            <div
              class="h-auto w-full flex items-center justify-between border-dashed border-b-[1px] py-3"
            >
              <span>Total Additions</span>
              <span>
                {{ numberWithCommas(item?.totalAdditions || 0) }}
              </span>
            </div>
            <div
              class="h-auto w-full flex items-center justify-between border-dashed border-b-[1px] py-3"
            >
              <span>Total Losses</span>
              <span>
                {{ numberWithCommas(item?.totalLosses || 0) }}
              </span>
            </div>
            <div class="h-auto w-full flex items-center justify-between py-4">
              <span>Total Transfers</span>
              <span>
                {{ numberWithCommas(item?.totalTransfers || 0) }}
              </span>
            </div>
            <div class="h-auto w-full flex flex-wrap items-center gap-3">
              <div
                class="h-auto w-full flex items-center justify-between border border-gray-300 border-dashed rounded p-3"
              >
                <span>
                  {{ `${numberWithCommas(item?.calculatedVolume || 0)} (bbl)` }}
                </span>
                <span>Calculated Volume</span>
              </div>
              <div
                class="h-auto w-full flex items-center justify-between border border-gray-300 border-dashed rounded p-3"
              >
                <span>
                  {{ `${numberWithCommas(item?.measuredVolume)} (bbl)` }}
                </span>
                <span>Measured Volume</span>
              </div>
            </div>

            <div
              class="flex flex-row items-center gap-2 absolute top-0 end-0 -translate-y-1/2"
            >
              <el-tooltip content="Addition" placement="top" effect="customize">
                <button
                  class="p-2 rounded-full bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover"
                  @click="
                    () => toggleVolumeModal(item?.id, VolumeTracking.Addition)
                  "
                >
                  <SvgIcon icon="addIcon" />
                </button>
              </el-tooltip>
              <el-tooltip content="Loss" placement="top" effect="customize">
                <button
                  class="p-2 rounded-full bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover"
                  @click="
                    () => toggleVolumeModal(item?.id, VolumeTracking.Loss)
                  "
                >
                  <SvgIcon icon="minusIcon" />
                </button>
              </el-tooltip>
              <el-tooltip content="Transfer" placement="top" effect="customize">
                <button
                  class="p-2 rounded-full bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover"
                  @click="
                    () => toggleVolumeModal(item?.id, VolumeTracking.Transfer)
                  "
                >
                  <SvgIcon icon="arrowFromLeft" />
                </button>
              </el-tooltip>
              <el-tooltip content="Edit" placement="top" effect="customize">
                <button
                  class="p-2 rounded-full bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover"
                  @click="() => toggleStorageOrPitModal(item?.id)"
                >
                  <SvgIcon icon="pencilIcon" />
                </button>
              </el-tooltip>
              <el-tooltip content="Delete" placement="top" effect="customize">
                <button
                  class="p-2 rounded-full bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover"
                  @click="deleteVolumeTracking(item?.id)"
                >
                  <span class="text-danger">
                    <SvgIcon icon="trashIcon" />
                  </span>
                </button>
              </el-tooltip>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <BottomTool :addNew="() => toggleStorageOrPitModal()" :showHelpInfo="false" />
  <VolumeModal
    :isVisible="isVolumeModalVisible"
    :close="toggleVolumeModal"
    ref="volumeModal"
    :loadPage="getVolumeTrackings"
    :productAndPackageInventoryId="productAndPackageInventoryId"
  />
  <StorageOrPitModal
    :isVisible="isSoPModalVisible"
    :close="() => toggleStorageOrPitModal()"
    ref="storageOrPitModal"
    :loadPage="getVolumeTrackings"
  />
  <VolumeDetailsModal
    :isVisible="isDetailsModalVisible"
    :close="() => toggleVolumeDetailsModal()"
    ref="volumeDetailsModal"
    :loadPage="getVolumeTrackings"
    :editVolume="toggleVolumeModal"
  />
</template>

<script lang="ts">
import NoEntries from "@/components/NoEntries.vue";
import { defineComponent, ref, type Ref, onMounted, inject } from "vue";
import SvgIcon from "@/constants/SvgIcon.vue";
import BottomTool from "@/components/common/BottomTool.vue";
import { numberWithCommas } from "@/utils/numberFormatter";
import VolumeModal from "./VolumeModal.vue";
import { VolumeTracking } from "@/constants/volume-tracking";
import StorageOrPitModal from "./StorageOrPitModal.vue";
import VolumeDetailsModal from "./VolumeDetailsModal.vue";
import { useVolumeTrackingStore } from "@/stores/volume-tracking";
import AlertService from "@/services/AlertService";
import { useProductPackageInventoryStore } from "@/stores/product-package-inventory";
import { useRoute } from "vue-router";
import type { Provide } from "@/types/injection-types";

export default defineComponent({
  name: "volume-tracking",
  components: {
    SvgIcon,
    BottomTool,
    VolumeModal,
    StorageOrPitModal,
    VolumeDetailsModal,
    NoEntries,
  },
  props: {
    setChildActiveTab: {
      type: Function,
      required: false,
      default: () => {},
    },
  },
  setup(_props) {
    const route = useRoute();
    const productPackageStore = useProductPackageInventoryStore();
    const volumeTrackingStore = useVolumeTrackingStore();
    const volumeModal: Ref<any> = ref<typeof VolumeModal | null>(null);
    const storageOrPitModal: Ref<any> = ref<typeof StorageOrPitModal | null>(
      null
    );
    const volumeDetailsModal: Ref<any> = ref<typeof VolumeDetailsModal | null>(
      null
    );
    const loading = ref(false);
    const volumeTrackingList = ref<any>([]);
    const productAndPackageInventoryId = ref("");
    const dailyReportProvide = inject<Provide.DailyReport>("dailyReport");
    const isVolumeModalVisible = ref(false);
    const isSoPModalVisible = ref(false);
    const isDetailsModalVisible = ref(false);

    onMounted(() => {
      if (dailyReportProvide?.getDailyReportId()) {
        getVolumeTrackings();
        getPackageInfo();
      } else {
        createDailyReport();
      }
    });

    const createDailyReport = async (): Promise<void> => {
      dailyReportProvide?.createDailyReport({
        wellId: route?.params?.id as string,
        callback: {
          onSuccess: (_res: string) => {
            getVolumeTrackings();
            getPackageInfo();
          },
        },
      });
    };

    const getVolumeTrackings = async (
      params = {
        dailyReportId: dailyReportProvide?.getDailyReportId(),
        page: 1,
        limit: 200,
      }
    ): Promise<void> => {
      loading.value = true;

      volumeTrackingStore.getVolumeTrackings({
        params: params,
        callback: {
          onSuccess: (res: any) => {
            volumeTrackingList.value = JSON.parse(JSON.stringify(res?.items));
          },
          onFinish: (_err: any) => {
            loading.value = false;
          },
        },
      });
    };

    const getPackageInfo = async (): Promise<void> => {
      if (!dailyReportProvide?.getDailyReportId()) return;
      loading.value = true;

      productPackageStore.getProductPackageInventories({
        dailyReportId: dailyReportProvide?.getDailyReportId(),
        callback: {
          onSuccess: (res: any) => {
            productAndPackageInventoryId.value = res?.id;
          },
          onFinish: (_err: any) => {
            loading.value = false;
          },
        },
      });
    };

    const toggleVolumeModal = (
      volumeId?: string,
      type?: VolumeTracking,
      itemId?: string
    ) => {
      if (itemId) {
        isDetailsModalVisible.value = false;
      }
      if (volumeId) {
        volumeModal?.value?.setId(volumeId, itemId || "");
      }
      if (type) {
        volumeModal?.value?.setType(type);
      }
      isVolumeModalVisible.value = !isVolumeModalVisible.value;
    };

    const toggleStorageOrPitModal = (id?: string) => {
      if (id) {
        storageOrPitModal?.value?.setId(id);
      }
      isSoPModalVisible.value = !isSoPModalVisible.value;
    };

    const toggleVolumeDetailsModal = (id?: string) => {
      if (id) {
        volumeDetailsModal?.value?.setId(id);
      }
      isDetailsModalVisible.value = !isDetailsModalVisible.value;
    };

    const deleteVolumeTracking = (id: string) => {
      AlertService.deletionAlert({
        onConfirmed: () => {
          deleteVolumeTrackingById(id);
        },
      });
    };

    const deleteVolumeTrackingById = (id: string) => {
      loading.value = true;
      volumeTrackingStore.deleteVolumeTracking({
        id: id,
        callback: {
          onSuccess: (_res: any) => {
            getVolumeTrackings();
          },
          onFinish: (_err: any) => {
            loading.value = false;
          },
        },
      });
    };

    return {
      getVolumeTrackings,
      deleteVolumeTracking,
      numberWithCommas,
      toggleVolumeModal,
      toggleStorageOrPitModal,
      toggleVolumeDetailsModal,
      loading,
      volumeTrackingList,
      volumeModal,
      storageOrPitModal,
      isSoPModalVisible,
      volumeDetailsModal,
      productAndPackageInventoryId,
      isVolumeModalVisible,
      isDetailsModalVisible,
      VolumeTracking,
    };
  },
});
</script>
