<template>
  <div
    v-if="isVisible"
    class="fixed top-0 right-0 bottom-0 left-0 z-40 flex items-center"
  >
    <!-- Dark overlay background -->
    <div class="absolute inset-0 bg-sidebar-background opacity-25"></div>
    <div
      class="relative bg-card-background text-card-text-light max-h-11/12 w-11/12 flex flex-col items-center gap-3 mx-auto my-6 p-5 rounded-xl md:w-3/5 lg:w-2/5 lg:h-auto"
    >
      <div class="flex flex-row h-auto w-full items-center justify-between">
        <h3 class="text-lg font-bold">
          {{ `${id ? "Edit Task" : "Add Task"}` }}
        </h3>
        <span class="cursor-pointer" @click="closeModal">
          <SvgIcon icon="closeModalIcon" />
        </span>
      </div>

      <el-form
        id="add_task_form"
        @submit.prevent="submit"
        :model="targetData"
        :rules="rules"
        ref="formRef"
        class="h-auto w-full flex flex-col gap-3"
      >
        <div class="flex flex-col gap-1">
          <label class="font-bold"
            >Description<span class="text-danger-active font-light">*</span>
          </label>
          <el-form-item prop="description" class="mt-auto">
            <el-input
              v-model="targetData.description"
              placeholder=""
              name="description"
            ></el-input>
          </el-form-item>
        </div>
        <div class="flex flex-col gap-1">
          <label class="font-bold"
            >Duration (hr)<span class="text-danger-active font-light">*</span>
          </label>
          <el-form-item prop="durations" class="mt-auto">
            <el-input
              type="number"
              :controls="false"
              step="any"
              v-model="targetData.durations"
              placeholder=""
              name="durations"
            ></el-input>
          </el-form-item>
        </div>
        <div class="h-auto w-full flex flex-row items-center gap-2">
          <button
            type="button"
            class="bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover rounded-md px-4 py-2 font-semibold"
            @click="closeModal"
            :disabled="loading"
          >
            Discard
          </button>
          <button
            class="bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover rounded-md px-4 py-2 font-semibold"
            type="submit"
            :disabled="loading"
          >
            <span v-if="!loading" class="indicator-label"> Save </span>
            <span v-if="loading" class="indicator-progress">
              Please wait...
              <span
                class="spinner-border spinner-border-sm align-middle ms-2"
              ></span>
            </span>
          </button>
        </div>
      </el-form>
    </div>
  </div>
</template>

<script lang="ts">
import SvgIcon from "@/constants/SvgIcon.vue";
import { useTaskStore } from "@/stores/task";
import { defineComponent, inject, ref, watch } from "vue";
import { useRoute } from "vue-router";
import type { Provide } from "@/types/injection-types";

export default defineComponent({
  name: "task-modal",
  components: { SvgIcon },
  props: {
    isVisible: {
      type: Boolean,
      required: true,
    },
    close: {
      type: Function,
      required: true,
    },
    loadPage: {
      type: Function,
      default: () => {},
      required: true,
    },
  },
  setup(props) {
    const route = useRoute();
    const taskStore = useTaskStore();
    const modal = ref(false);
    const initialForm = {
      description: "",
      durations: null,
    };
    const targetData = ref(JSON.parse(JSON.stringify(initialForm)));
    const formRef = ref<null | HTMLFormElement>(null);
    const loading = ref<boolean>(false);
    const id = ref("");
    const dailyReportProvide = inject<Provide.DailyReport>("dailyReport");

    watch(id, (newValue) => {
      if (newValue !== "") {
        getTaskDetails();
      }
    });

    watch(
      () => props.isVisible,
      (newValue) => {
        if (newValue === false) {
          id.value = "";
          reset();
          formRef?.value?.resetFields();
        }
      }
    );

    const getTaskDetails = async (): Promise<void> => {
      taskStore.getTaskDetails({
        id: id.value,
        callback: {
          onSuccess: (res: any) => {
            targetData.value = { ...res };
          },
        },
      });
    };

    const updateTask = async (param: any): Promise<void> => {
      loading.value = true;
      taskStore.updateTask({
        id: id.value,
        params: param,
        callback: {
          onSuccess: (_res: any) => {
            if (props?.loadPage) {
              props?.loadPage();
            }
            closeModal();
          },
          onFinish: (_err: any) => {
            loading.value = false;
          },
        },
      });
    };

    const closeModal = () => {
      props.close();
    };

    const rules = ref({
      description: [
        {
          required: true,
          message: "Please type Description",
          trigger: "blur",
        },
      ],
      durations: [
        {
          required: true,
          message: "Please type Duration",
          trigger: "blur",
        },
      ],
    });

    const submit = () => {
      if (!formRef.value) {
        return;
      }

      formRef.value.validate((valid: boolean) => {
        if (valid) {
          const param = {
            description: targetData?.value?.description,
            durations: Number(targetData?.value?.durations),
          };

          if (id?.value) {
            updateTask({
              ...param,
              dailyReportId: dailyReportProvide?.getDailyReportId(),
            });
          } else {
            dailyReportProvide?.createDailyReport({
              wellId: route?.params?.id as string,
              callback: {
                onSuccess: (_res: string) => {
                  createTask({
                    ...param,
                    dailyReportId: dailyReportProvide?.getDailyReportId(),
                  });
                },
              },
            });
          }
        }
      });
    };

    const createTask = async (param: any): Promise<void> => {
      loading.value = true;
      taskStore.createTask({
        params: param,
        callback: {
          onSuccess: (_res: any) => {
            if (props?.loadPage) {
              props?.loadPage();
            }
            closeModal();
          },
          onFinish: (_err: any) => {
            loading.value = false;
          },
        },
      });
    };

    const reset = () => {
      targetData.value = JSON.parse(JSON.stringify(initialForm));
    };

    const setId = (wellId: string) => {
      id.value = wellId.toString();
    };

    return {
      id,
      modal,
      rules,
      loading,
      targetData,
      formRef,
      closeModal,
      submit,
      reset,
      setId,
    };
  },
});
</script>
