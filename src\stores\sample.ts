import ApiService from "../services/ApiService";
import { get, noop } from "lodash";
import { defineStore } from "pinia";
import { handleFailure } from "../utils/handleFailure";
import type { Callback } from "../types/common";

export const useSampleStore = defineStore("sample", () => {
  const getSamples = async ({
    params,
    callback,
  }: {
    params: any;
    callback: Callback;
  }): Promise<void> => {
    const onSuccess = get(callback, "onSuccess", noop);
    const onFinish = get(callback, "onFinish", noop);

    try {
      ApiService.setHeader();
      const response = await ApiService.getWithParams("samples", params);
      onSuccess(response.data?.data || response.data);
    } catch (error) {
      handleFailure(error, callback);
    } finally {
      onFinish();
    }
  };

  const getSampleDetails = async ({
    id,
    callback,
  }: {
    id: string;
    callback: Callback;
  }): Promise<void> => {
    const onSuccess = get(callback, "onSuccess", noop);
    const onFinish = get(callback, "onFinish", noop);

    try {
      ApiService.setHeader();
      const response = await ApiService.get(`samples/${id}`);
      onSuccess(response.data?.data || response.data);
    } catch (error) {
      handleFailure(error, callback);
    } finally {
      onFinish();
    }
  };

  const updateSample = async ({
    id,
    params,
    callback,
  }: {
    id: string;
    params: any;
    callback: Callback;
  }): Promise<void> => {
    const onSuccess = get(callback, "onSuccess", noop);
    const onFinish = get(callback, "onFinish", noop);

    try {
      ApiService.setHeader();
      const response = await ApiService.put(`samples/${id}`, params);
      onSuccess(response.data?.data || response.data);
    } catch (error) {
      handleFailure(error, callback);
    } finally {
      onFinish();
    }
  };

  const deleteSample = async ({
    id,
    callback,
  }: {
    id: string;
    callback: Callback;
  }): Promise<void> => {
    const onSuccess = get(callback, "onSuccess", noop);
    const onFinish = get(callback, "onFinish", noop);

    try {
      ApiService.setHeader();
      const response = await ApiService.delete(`samples/${id}`);
      onSuccess(response.data?.data || response.data);
    } catch (error) {
      handleFailure(error, callback);
    } finally {
      onFinish();
    }
  };

  const createSample = async ({
    params,
    callback,
  }: {
    params: any;
    callback: Callback;
  }): Promise<void> => {
    const onSuccess = get(callback, "onSuccess", noop);
    const onFinish = get(callback, "onFinish", noop);

    try {
      ApiService.setHeader();
      const response = await ApiService.post("samples", params);
      onSuccess(response.data?.data || response.data);
    } catch (error) {
      handleFailure(error, callback);
    } finally {
      onFinish();
    }
  };

  const getSampleChartInfo = async ({
    id,
    callback,
  }: {
    id: string;
    callback: Callback;
  }): Promise<void> => {
    const onSuccess = get(callback, "onSuccess", noop);
    const onFinish = get(callback, "onFinish", noop);

    try {
      ApiService.setHeader();
      const response = await ApiService.get(`samples/chart/${id}`);
      onSuccess(response.data?.data || response.data);
    } catch (error) {
      handleFailure(error, callback);
    } finally {
      onFinish();
    }
  };

  return {
    getSamples,
    getSampleDetails,
    updateSample,
    deleteSample,
    createSample,
    getSampleChartInfo,
  };
});
