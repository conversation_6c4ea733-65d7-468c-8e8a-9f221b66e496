export enum ExceptionCode {
    IncorrectEmailPassword = "INCORRECT_EMAIL_PASSWORD",
    UserNotFound = "USER_NOT_FOUND",
    IncorrectEmailExists = "INCORRECT_EMAIL_EXISTS",
  }
  
  export const ExceptionMessages: Record<ExceptionCode, string> = {
    [ExceptionCode.IncorrectEmailPassword]: "The email or password is incorrect",
    [ExceptionCode.UserNotFound]: "The user is not found",
    [ExceptionCode.IncorrectEmailExists]: "The email does not exist",
  };
  