import{P as b}from"./PageHeader-l8HvxxsN.js";import{S as m}from"./SvgIcon-CfrWCA-H.js";import{W as p}from"./WellGeneralInfo-CntYZnXa.js";import{d as u,q as f,_ as v,c as g,b as n,a as r,n as x,F as _,r as s,o as h}from"./index-DalLS0_6.js";import"./date-CCTVzEJd.js";import"./navigation-guard-9jXpDdGb.js";import"./company-DGE9srvS.js";import"./handleFailure-DrOe_u9W.js";import"./customer-CD9RajQq.js";import"./user-UjS69U41.js";import"./well-8ACiu0oO.js";const I=u({name:"wells-new",components:{SvgIcon:m,WellGeneralInfo:p,PageHeader:b},setup(){const e=["Home","Wells","New"],t=f("general");return{breadcrumbs:e,tabIndex:t,setActiveTab:a=>{const o=a.target;t.value=o.getAttribute("data-tab-index")}}}}),w={class:"h-auto w-full bg-header-background pb-3 px-4 mx-auto flex flex-row gap-3 items-center justify-start",role:"tablist"},k={class:"bg-card-background text-card-text-dark h-auto w-11/12 rounded-xl mx-auto mt-7 px-3 py-4 flex flex-col items-center mb-4"};function W(e,t,l,a,o,H){const c=s("PageHeader"),d=s("WellGeneralInfo");return h(),g(_,null,[n(c,{title:"Home",breadcrumbs:e.breadcrumbs},null,8,["breadcrumbs"]),r("ul",w,[r("li",null,[r("a",{class:x(["cursor-pointer font-semibold hover:text-active-hover hover:border-b-2 hover:border-active-border-hover",{active:e.tabIndex==="general","text-inactive border-b-2 border-inactive-borderactive":e.tabIndex==="general","text-active border-b-2 border-active-border":e.tabIndex!=="general"}]),onClick:t[0]||(t[0]=i=>e.setActiveTab(i)),"data-tab-index":"general"}," General ",2)])]),r("div",k,[n(d)])],64)}const V=v(I,[["render",W]]);export{V as default};
