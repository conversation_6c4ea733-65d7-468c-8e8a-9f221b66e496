import{a as r}from"./index.esm-C3uaQ3c9.js";class l{static isRequired(e){return e instanceof Array?e!=null&&e!=null&&e.length>0:e!=""&&e!=null&&e!=null}static isEqualsPattern(e,t){return e==null||e==null||e==""?!1:t.test(e)}static isEqualsLength(e,t){return e==null||e==null||e==""?!1:e.length>=t}static validate(e,t){var s,i,n;return t.required&&!this.isRequired(e)?((s=t.errorsMessage)==null?void 0:s.required)||"This field is required.":t.pattern&&!this.isEqualsPattern(e,t.pattern)?((i=t.errorsMessage)==null?void 0:i.pattern)||"This field has incorrect format.":t.length&&!this.isEqualsLength(e,t.length)?((n=t.errorsMessage)==null?void 0:n.length)||`This field is required at least ${t.length} characters.`:null}}const m={emailAddress:r().email("Invalid Email").required("Please type Email"),mobilePhone:r().matches(/^\d+$/,"Phone number must contain only digits").min(10,"Phone number must be at least 10 digits long").max(15,"Phone number must be at most 15 digits long").required("Phone number is required").nullable(),officePhone:r().matches(/^\d+$/,"Phone number must contain only digits").min(10,"Phone number must be at least 10 digits long").max(15,"Phone number must be at most 15 digits long").notRequired()};export{l as v,m as y};
