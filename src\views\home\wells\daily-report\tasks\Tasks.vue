<template>
  <div
    class="bg-card-background text-card-text-light w-11/12 mx-auto mb-4 h-auto rounded-xl"
  >
    <div class="h-auto w-11/12 mx-auto mt-7 px-3 py-4">
      <h1 class="font-bold mb-4">Tasks</h1>

      <div class="card-body">
        <div v-if="loading" class="text-center">
          <div class="spinner-border text-primary" role="status">
            <span class="sr-only">Loading...</span>
          </div>
        </div>
        <template v-else>
          <NoEntries
            v-if="taskList.length === 0"
            :addNew="toggleAddTaskModal"
          />
          <div
            v-else
            class="h-auto w-full grid grid-cols-1 gap-4 md:grid-cols-2 md:gap-7 lg:grid-cols-3"
          >
            <div
              v-for="item in taskList"
              :key="item?.id"
              class="relative h-auto w-full bg-minicard-background text-minicard-text-light p-4 rounded-xl border-t-2 border-active my-2 first:mt-4 last:mb-5 font-semibold md:first:mt-0 md:last:mb-0 md:m-0"
            >
              <div class="h-auto w-full p-4 flex flex-row items-center gap-2">
                <span class="bg-success rounded-lg h-12 w-2"></span>
                <div class="h-auto w-full flex flex-col gap-2">
                  <h4 class="font-bold">{{ item?.description }}</h4>

                  <span class="font-semibold text-sm">
                    {{ `${item?.durations} hours` }}
                  </span>
                </div>
                <div class="flex items-center gap-2">
                  <el-tooltip
                    content="Edit Task"
                    placement="top"
                    effect="customize"
                  >
                    <button
                      class="p-2 rounded-full bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover"
                      @click="toggleEditDurationModal(item?.id)"
                    >
                      <SvgIcon icon="pencilIcon" />
                    </button>
                  </el-tooltip>

                  <el-tooltip
                    content="Delete"
                    placement="top"
                    effect="customize"
                  >
                    <button
                      class="p-2 rounded-full bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover"
                      @click="(e) => deleteTask(e, item?.id)"
                    >
                      <span class="text-danger">
                        <SvgIcon icon="trashIcon" />
                      </span>
                    </button>
                  </el-tooltip>
                </div>
              </div>
            </div>
          </div>
        </template>
      </div>
    </div>
  </div>
  <TaskModal
    :isVisible="isModalVisible"
    :close="toggleAddTaskModal"
    ref="taskModal"
    :loadPage="getTasks"
  />
  <BottomTool :addNew="toggleAddTaskModal" :showHelpInfo="false" />
</template>

<script lang="ts">
import NoEntries from "@/components/NoEntries.vue";
import SvgIcon from "@/constants/SvgIcon.vue";
import AlertService from "@/services/AlertService";
import { useTaskStore } from "@/stores/task";
import { defineComponent, inject, onMounted, ref, type Ref } from "vue";
import BottomTool from "@/components/common/BottomTool.vue";
import TaskModal from "./TaskModal.vue";
import type { Provide } from "@/types/injection-types";

export default defineComponent({
  name: "tasks",
  components: {
    SvgIcon,
    BottomTool,
    TaskModal,
    NoEntries,
  },
  props: {
    setChildActiveTab: {
      type: Function,
      required: false,
      default: () => {},
    },
  },
  setup(_props) {
    const loading = ref(false);
    const taskStore = useTaskStore();
    const taskList = ref<any>([]);
    const taskModal: Ref<any> = ref<typeof TaskModal | null>(null);
    const dailyReportProvide = inject<Provide.DailyReport>("dailyReport");
    const isModalVisible = ref(false);

    onMounted(() => {
      if (dailyReportProvide?.getDailyReportId()) {
        getTasks();
      }
    });

    const getTasks = async (
      params = {
        dailyReportId: dailyReportProvide?.getDailyReportId(),
        page: 1,
        limit: 200,
      }
    ): Promise<void> => {
      loading.value = true;

      taskStore.getTasks({
        params: params,
        callback: {
          onSuccess: (res: any) => {
            taskList.value = res?.items;
          },
          onFinish: (_err: any) => {
            loading.value = false;
          },
        },
      });
    };

    const toggleAddTaskModal = () => {
      isModalVisible.value = !isModalVisible.value;
    };

    const toggleEditDurationModal = (taskId: string) => {
      taskModal?.value?.setId(taskId);
      isModalVisible.value = !isModalVisible.value;
    };

    const deleteTask = (e: any, id: string) => {
      e.stopPropagation();
      AlertService.deletionAlert({
        onConfirmed: () => {
          deleteTaskById(id);
        },
      });
    };

    const deleteTaskById = async (id: string): Promise<void> => {
      loading.value = true;
      taskStore.deleteTask({
        id: id,
        callback: {
          onSuccess: (_res: any) => {
            getTasks();
          },
          onFinish: (_err: any) => {
            loading.value = false;
          },
        },
      });
    };

    return {
      loading,
      taskList,
      taskModal,
      isModalVisible,
      getTasks,
      deleteTask,
      toggleAddTaskModal,
      toggleEditDurationModal,
    };
  },
});
</script>
