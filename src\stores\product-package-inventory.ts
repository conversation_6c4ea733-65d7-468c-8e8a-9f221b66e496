import ApiService from "../services/ApiService";
import { get, noop } from "lodash";
import { defineStore } from "pinia";
import { handleFailure } from "../utils/handleFailure";
import type { Callback } from "../types/common";

export const useProductPackageInventoryStore = defineStore(
  "productAndPackageInventory",
  () => {
    const getProductPackageInventories = async ({
      dailyReportId,
      callback,
    }: {
      dailyReportId: string;
      callback: Callback;
    }): Promise<void> => {
      const onSuccess = get(callback, "onSuccess", noop);
      const onFinish = get(callback, "onFinish", noop);

      try {
        ApiService.setHeader();
        const response = await ApiService.get(
          `productAndPackageInventories/${dailyReportId}`
        );
        onSuccess(response.data?.data || response.data);
      } catch (error) {
        handleFailure(error, callback);
      } finally {
        onFinish();
      }
    };

    const deleteProductPackageInventory = async ({
      id,
      callback,
    }: {
      id: string;
      callback: Callback;
    }): Promise<void> => {
      const onSuccess = get(callback, "onSuccess", noop);
      const onFinish = get(callback, "onFinish", noop);

      try {
        ApiService.setHeader();
        const response = await ApiService.delete(
          `productAndPackageInventories/${id}`
        );
        onSuccess(response.data?.data || response.data);
      } catch (error) {
        handleFailure(error, callback);
      } finally {
        onFinish();
      }
    };

    return {
      deleteProductPackageInventory,
      getProductPackageInventories,
    };
  }
);
