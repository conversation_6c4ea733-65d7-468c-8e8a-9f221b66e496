import{S as g}from"./SvgIcon-CfrWCA-H.js";import{a as n}from"./table-bhK9qpe4.js";import{d as k,_ as B,c as r,o as i,k as b,F as a,l as _,n as D,s as l,a as d,B as u,t as m,b as v,r as w}from"./index-DalLS0_6.js";const A=k({name:"table-header",components:{SvgIcon:g},props:{headers:{type:Object,require:!0},sortBy:{type:String,require:!0},sortDirection:{type:String,require:!0},onSort:{type:Function,require:!0}},setup(t){return{getArrowPath:o=>t.sortBy===o?t.sortDirection===n.ASC?"/media/icons/arrows/order-by-asc.svg":"/media/icons/arrows/order-by-des.svg":"/media/icons/arrows/order-by-base.svg",onClickSort:o=>{var c;let s={};(t==null?void 0:t.sortBy)!==o?s={sortBy:o,sortDirection:n.ASC}:s={sortBy:t==null?void 0:t.sortBy,sortDirection:(t==null?void 0:t.sortDirection)===n.DESC?n.ASC:n.DESC},(c=t==null?void 0:t.onSort)==null||c.call(t,s)}}}}),$=["onClick"],q={class:"sort-icon"};function E(t,y,S,o,s,c){const f=w("SvgIcon");return i(!0),r(a,null,b(t.headers,(e,C)=>(i(),r(a,{key:C},[(e==null?void 0:e.display)!==!1?(i(),r("th",{key:0,class:D(e==null?void 0:e.class)},[e!=null&&e.sortBy?(i(),r("div",{key:0,class:"flex flex-row justify-center items-center",onClick:()=>t.onClickSort(e.sortBy)},[l(t.$slots,"customHeader",{header:e},()=>[u(m(e.label),1)]),d("span",q,[v(f,{icon:t.getArrowPath(e.sortBy)},null,8,["icon"])])],8,$)):l(t.$slots,"customHeader",{key:1,header:e},()=>[u(m(e.label),1)])],2)):_("",!0)],64))),128)}const H=B(A,[["render",E]]);export{H as T};
