<template>
  <div
    class="h-auto w-11/12 mx-auto my-4 flex flex-col items-center gap-3"
  >
    <div class="bg-card-background text-card-text rounded-xl h-auto w-full p-4 lg:w-4/5 lg:min-w-[1560px]">
      <ul
        class="h-auto w-full py-4 flex flex-row items-center justify-start gap-3 overflow-x-scroll md:overflow-hidden"
      >
        <li v-for="item in tabs" :key="item.value">
          <div
            class="cursor-pointer font-semibold hover:text-active-hover hover:border-b-2 hover:border-active-border-hover"
            :class="{
              active: tabIndex === item.value,
              'text-active border-b-2 border-active-border': tabIndex === item?.value,
              'text-inactive border-b-2 border-inactive-border':
                tabIndex !== item?.value,
            }"
            @click="setActiveTab($event)"
            :data-tab-index="item.value"
            role="tab"
          >
            {{ item.key }}
          </div>
        </li>
      </ul>
      <!--begin::Card header-->
      <div
        class="pb-4 flex flex-col gap-2 items-start border-b-2 border-solid border-light-border"
      >
        <h1 class="font-bold text-lg">Company Overview</h1>
        <div
          class="h-auto w-full flex flex-row justify-between items-center md:justify-start md:gap-4"
          v-if="isAdmin()"
        >
          <button
            class="bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover rounded-md px-4 py-2 font-semibold"
            @click="toggleInvitation"
          >
            Send Invitation
          </button>
          <button
            class="bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover rounded-md px-4 py-2 font-semibold"
            @click="toggleEditCompany"
          >
            Edit
          </button>
          <button
            type="button"
            class="bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover rounded-md px-4 py-2 font-semibold"
            @click="back"
          >
            Back
          </button>
        </div>
      </div>
      <!--end::Card header-->

      <div class="p-4 text-sm">
        <!--begin::Info-->
        <div class="flex flex-col gap-2 md:flex-row md:justify-evenly">
          <div class="hover:text-card-text-hover">
            <h4 class="text-md font-bold">Company ID</h4>
            <p>
              {{ companyDetail?.id || "" }}
            </p>
          </div>
          <!-- <div
                    class="d-flex gap-2 text-hover-primary me-5 mb-2 mt-2"
                  >
                    <div>
                      <h4 class="mb-4 user-info text-md">
                        Company Register Number/ID
                      </h4>
                      <p class="mb-0">
                        {{ companyDetail?.registerNumber || "" }}
                      </p>
                    </div>
                  </div> -->
          <div>
            <h4 class="text-md font-bold">Company Name</h4>
            <p class="hover:text-card-text-light">
              {{ companyDetail?.name || "" }}
            </p>
          </div>
          <div>
            <h4 class="text-md font-bold">Description</h4>
            <p class="hover:text-card-text-hover">
              {{ companyDetail?.description || "" }}
            </p>
          </div>
        </div>
      </div>
    </div>
    <div v-if="tabIndex !== tabs[3].value" class="h-auto w-full">
      <UserList :listType="tabIndex" />
    </div>
    <div v-else class="h-auto w-full">
      <CustomerList />
    </div>
    <CompanyModal
      :isVisible="isCompanyModalVisible"
      :close="toggleEditCompany"
      ref="companyModal"
      :loadPage="reloadCompanyData"
    />
    <InvitationModal
      v-if="companyDetail?.id"
      :isVisible="isInvitationModalVisible"
      :close="toggleInvitation"
      ref="invitationModal"
      :companyId="companyDetail.id"
    />
  </div>
</template>

<script lang="ts">
import { CompanyUser, UserType } from "@/constants/user";
import { isAdmin } from "@/services/JwtService";
import { defineComponent, ref, type PropType, type Ref } from "vue";
import { useRouter } from "vue-router";
import CompanyModal from "@/views/company/overview/CompanyModal.vue";
import CustomerList from "./CustomerList.vue";
import InvitationModal from "./InvitationModal.vue";
import UserList from "./UserList.vue";

const tabs = [
  { value: UserType.CompanyAdmin, key: "Administration" },
  { value: UserType.Supervisor, key: "Supervisors" },
  { value: UserType.Engineer, key: "Engineers" },
  { value: CompanyUser.Customer, key: "Customer" },
];
const isCompanyModalVisible = ref(false);
const isInvitationModalVisible = ref(false);

export default defineComponent({
  name: "company-detail",
  components: {
    UserList,
    CompanyModal,
    InvitationModal,
    CustomerList,
  },
  props: {
    companyDetail: {
      type: Object as PropType<Company.Info>,
      required: true,
    },
    reloadCompanyData: {
      type: Function,
      required: true,
    },
  },
  setup(props) {
    const router = useRouter();
    const companyModal: Ref<any> = ref<typeof CompanyModal | null>(null);
    const invitationModal: Ref<any> = ref<typeof InvitationModal | null>(null);
    const tabIndex = ref<number>(tabs[0].value);

    const setActiveTab = (e: Event) => {
      const target = e.target as HTMLInputElement;
      tabIndex.value = Number(target.getAttribute("data-tab-index"));
    };

    const toggleEditCompany = (): void => {
      companyModal?.value?.setId(props?.companyDetail?.id);
      isCompanyModalVisible.value = !isCompanyModalVisible.value;
    };

    const toggleInvitation = (): void => {
      isInvitationModalVisible.value = !isInvitationModalVisible.value;
    };

    const back = () => {
      router.go(-1);
    };

    return {
      companyModal,
      invitationModal,
      tabs,
      tabIndex,
      isCompanyModalVisible,
      isInvitationModalVisible,
      back,
      setActiveTab,
      toggleEditCompany,
      toggleInvitation,
      isAdmin,
    };
  },
});
</script>
