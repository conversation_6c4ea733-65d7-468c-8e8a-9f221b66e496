import{O as p,P as s,Q as i}from"./index-CGNRhvz7.js";import{h as c}from"./handleFailure-DtTpu7r3.js";const x=p("user",()=>({addUser:async({params:t,callback:n})=>{var o;const r=s.get(n,"onSuccess",s.noop),a=s.get(n,"onFinish",s.noop);try{i.setHeader();const e=await i.post("users/add",t);r(((o=e.data)==null?void 0:o.data)||e.data)}catch(e){c(e,n)}finally{a()}},getUsers:async({params:t,callback:n})=>{var o;const r=s.get(n,"onSuccess",s.noop),a=s.get(n,"onFinish",s.noop);try{i.setHeader();const e=await i.getWithParams("users",t);r(((o=e.data)==null?void 0:o.data)||e.data)}catch(e){c(e,n)}finally{a()}},getMyProfile:async({callback:t})=>{var a;const n=s.get(t,"onSuccess",s.noop),r=s.get(t,"onFinish",s.noop);try{i.setHeader();const o=await i.get("users/profile");n(((a=o.data)==null?void 0:a.data)||o.data)}catch(o){c(o,t)}finally{r()}},getUserProfile:async({id:t,callback:n})=>{var o;const r=s.get(n,"onSuccess",s.noop),a=s.get(n,"onFinish",s.noop);try{i.setHeader();const e=await i.get(`users/profile/${t}`);r(((o=e.data)==null?void 0:o.data)||e.data)}catch(e){c(e,n)}finally{a()}},changePassword:async({params:t,callback:n})=>{var o;const r=s.get(n,"onSuccess",s.noop),a=s.get(n,"onFinish",s.noop);try{i.setHeader();const e=await i.put("users/change-password",t);r(((o=e.data)==null?void 0:o.data)||e.data)}catch(e){c(e,n)}finally{a()}},updateMyProfile:async({params:t,callback:n})=>{var o;const r=s.get(n,"onSuccess",s.noop),a=s.get(n,"onFinish",s.noop);try{i.setHeader();const e=await i.put("users/profile",t);r(((o=e.data)==null?void 0:o.data)||e.data)}catch(e){c(e,n)}finally{a()}},updateUserProfile:async({id:t,params:n,callback:r})=>{var e;const a=s.get(r,"onSuccess",s.noop),o=s.get(r,"onFinish",s.noop);try{i.setHeader();const d=await i.put(`users/profile/${t}`,n);a(((e=d.data)==null?void 0:e.data)||d.data)}catch(d){c(d,r)}finally{o()}},getAssignedEngineers:async({params:t,callback:n})=>{var o;const r=s.get(n,"onSuccess",s.noop),a=s.get(n,"onFinish",s.noop);try{i.setHeader();const e=await i.getWithParams("users/engineers/assigned",t);r(((o=e.data)==null?void 0:o.data)||e.data)}catch(e){c(e,n)}finally{a()}},assignEngineers:async({params:t,callback:n})=>{var o;const r=s.get(n,"onSuccess",s.noop),a=s.get(n,"onFinish",s.noop);try{i.setHeader();const e=await i.post("users/engineers/assigned/add",t);r(((o=e.data)==null?void 0:o.data)||e.data)}catch(e){c(e,n)}finally{a()}},removeEngineers:async({engineerIds:t,callback:n})=>{var o;const r=s.get(n,"onSuccess",s.noop),a=s.get(n,"onFinish",s.noop);try{i.setHeader();const e=await i.post("users/engineers/assigned/remove",{engineerIds:t});r(((o=e.data)==null?void 0:o.data)||e.data)}catch(e){c(e,n)}finally{a()}},uploadAvatar:async({params:t,callback:n})=>{var o;const r=s.get(n,"onSuccess",s.noop),a=s.get(n,"onFinish",s.noop);try{i.setHeader();const e={headers:{"Content-Type":"multipart/form-data"}},d=await i.post("users/upload-avatar",t,e);r(((o=d.data)==null?void 0:o.data)||d.data)}catch(e){c(e,n)}finally{a()}},removeUsers:async({ids:t,callback:n})=>{var o;const r=s.get(n,"onSuccess",s.noop),a=s.get(n,"onFinish",s.noop);try{i.setHeader();const e=await i.post("users/delete",{ids:t});r(((o=e.data)==null?void 0:o.data)||e.data)}catch(e){c(e,n)}finally{a()}},inviteUsers:async({params:t,callback:n})=>{var o;const r=s.get(n,"onSuccess",s.noop),a=s.get(n,"onFinish",s.noop);try{i.setHeader();const e=await i.post("users/invite",t);r(((o=e.data)==null?void 0:o.data)||e.data)}catch(e){c(e,n)}finally{a()}},acceptInviteToken:async({token:t,callback:n})=>{var o;const r=s.get(n,"onSuccess",s.noop),a=s.get(n,"onFinish",s.noop);try{i.setHeader();const e=await i.post(`users/invite/accept/${t}`,null);r(((o=e.data)==null?void 0:o.data)||e.data)}catch(e){c(e,n)}finally{a()}},registerInvitedUser:async({params:t,callback:n})=>{const r=s.get(n,"onSuccess",s.noop),a=s.get(n,"onFinish",s.noop);try{i.setHeader();const o=await Promise.all([i.put("users/profile",t.userInfo),i.put("users/change-password",t.changePass)]);r(o)}catch(o){c(o,n)}finally{a()}},resetUserPassword:async({params:t,callback:n})=>{var o;const r=s.get(n,"onSuccess",s.noop),a=s.get(n,"onFinish",s.noop);try{i.setHeader();const e=await i.post("users/reset-password",t);r(((o=e.data)==null?void 0:o.data)||e.data)}catch(e){c(e,n)}finally{a()}},getInvitedUser:async({callback:t})=>{var a;const n=s.get(t,"onSuccess",s.noop),r=s.get(t,"onFinish",s.noop);try{i.setHeader();const o=await i.get("users/invited");n(((a=o.data)==null?void 0:a.data)||o.data)}catch(o){c(o,t)}finally{r()}}}));export{x as u};
