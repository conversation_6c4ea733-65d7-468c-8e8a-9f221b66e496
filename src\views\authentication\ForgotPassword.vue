<template>
  <div
    class="h-auto w-4/5 rounded-3xl mx-auto py-6 bg-card-background text-card-text-light rounded-3 align-self-center flex flex-col justify-center items-center md:text-lg lg:max-w-2/5"
  >
    <VForm
      v-if="currentStep === STEP.ENTER_EMAIL"
      class="flex flex-col items-center w-4/5"
      @submit="onSubmitForgotPassword"
      :validation-schema="forgotPassword"
    >
      <img class="h-30 w-auto" src="/media/logos/opslink-light.png" />

      <div class="text-center mb-10">
        <h1 class="mb-3 font-bold">Forgot Password ?</h1>
        <div class="font-bold fs-4">
          Enter your email to reset your password.
        </div>
      </div>

      <div class="flex flex-col mb-8 mx-3 h-auto w-full">
        <label class="form-label tracking-wide font-bold">Email</label>
        <Field
          class="rounded-lg bg-input-background text-input-text-dark pl-3"
          type="email"
          placeholder="Enter email"
          name="email"
          autocomplete="off"
        />
        <div class="text-danger mt-[0.3rem]">
          <ErrorMessage name="email" />
        </div>
      </div>
      <div class="h-auto w-full flex flex-row mb-8 gap-2 items-center">
        <router-link to="/sign-in" class="bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover rounded-md px-4 py-2 font-semibold md:px-4 md:py-3 md:text-xl">Cancel</router-link>
        <button
          class="bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover rounded-md px-4 py-2 font-semibold md:px-4 md:py-3 md:text-xl"
          type="submit"
          :disabled="loading"
        >
          <span v-if="!loading" class="font-semibold"> Submit </span>
          <span v-if="loading" class="indicator-progress">
            Please wait...
            <span
              class="spinner-border spinner-border-sm align-middle ms-2"
            ></span>
          </span>
        </button>
      </div>
    </VForm>
    <OTPVerification
      v-else-if="currentStep === STEP.OTP"
      :email="email"
      :userForgot="userForgot"
    />
  </div>
</template>

<script lang="ts">
import { ExceptionCode, ExceptionMessages } from "@/constants/exceptions";
import { useAuthStore } from "@/stores/auth";
import Swal from "sweetalert2";
import { ErrorMessage, Field, Form as VForm } from "vee-validate";
import { defineComponent, ref } from "vue";
import * as Yup from "yup";
import OTPVerification from "./OTPVerification.vue";

enum STEP {
  ENTER_EMAIL = 1,
  OTP,
}

export default defineComponent({
  name: "forgot-password",
  components: {
    Field,
    VForm,
    ErrorMessage,
    OTPVerification,
  },
  setup() {
    const store = useAuthStore();
    const currentStep = ref<number>(STEP.ENTER_EMAIL);
    const email = ref<string>("");
    const userForgot = ref<{
      name: String;
      email: String;
    }>();
    const loading = ref<boolean>(false);
    const forgotPassword = Yup.object().shape({
      email: Yup.string().email().required().label("Email"),
    });

    const onSubmitForgotPassword = async (values: any) => {
      loading.value = true;
      email.value = values?.email;
      store.forgotPassword({
        email: values?.email || "",
        callback: {
          onSuccess: (data) => {
            if (data) {
              userForgot.value = data;
            }
            currentStep.value = STEP.OTP;
          },
          onFinish: () => {
            loading.value = false;
          },
          onFailure: (error) => {
            Swal.fire({
              text:
                ExceptionMessages[
                  error?.response?.data?.errorCode as ExceptionCode
                ] ||
                error?.response?.data?.message ||
                error?.message ||
                "Sorry, looks like there are some errors detected, please try again.",

              icon: "error",
              buttonsStyling: false,
              confirmButtonText: "Got it!",
              heightAuto: false,
              customClass: {
                confirmButton: "px-2 py-1 bg-light-danger font-semibold ",
              },
            });
          },
        },
      });
    };

    return {
      onSubmitForgotPassword,
      forgotPassword,
      loading,
      currentStep,
      STEP,
      email,
      userForgot,
    };
  },
});
</script>
