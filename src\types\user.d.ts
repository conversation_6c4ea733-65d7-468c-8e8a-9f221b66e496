declare namespace User {
  export type Info = {
    id?: string | null;
    avatar?: string | null;
    firstName?: string | null;
    lastName?: string | null;
    companyId?: string | null;
    email?: string | null;
    officePhone?: string | null;
    mobilePhone?: string | null;
    address?: string | null;
    note?: string | null;
    roles?: { value: number }[] | null;
    companyName?: string | null;
    status?: number | null;
    userRoles?: number[] | null;
    company?: Company.Info | null;
    assignedDate?: string | null;
    supervisor?: User.Info | null;
  };

  export type ChangePassword = {
    currentPassword: string;
    newPassword: string;
  };

  export type Login = {
    email: string;
    password: string;
  };

  export type ValidateOTP = {
    email: string;
    otp: string;
  };

  export interface GetListParams extends Filter.FilterForm {}

  export interface GetAssignedListParams extends Filter.FilterForm {
    supervisorId: string;
  }

  export type Invite = {
    emails: string[];
    userRoles: number[];
    companyId: string;
  };

  export type RegisterInvitedUser = {
    userInfo: User.Info;
    changePass: User.ChangePassword;
  };
  export type Register = {
    email: string;
    password: string;
    firstName: string;
    lastName: string;
    companyId?: string;
  };

  export type ResetPassword = {
    email: string;
    otp: string;
    newPassword: string;
  };

  export type AssignEngineer = {
    engineerIds: string[];
    supervisorId: string;
  };

  export type ResetUserPassword = {
    email: string;
  };

  export type ResetNewPassword = {
    newPassword: string;
  };
}
