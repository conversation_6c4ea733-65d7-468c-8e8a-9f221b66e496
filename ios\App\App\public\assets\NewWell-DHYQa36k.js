import{P as d}from"./PageHeader-3hadTn26.js";import{S as p}from"./SvgIcon-CMhyaXWN.js";import{W as b}from"./WellGeneralInfo-BKW7Ii__.js";import{d as u,q as f,_ as g,c as x,b as n,a as r,n as v,F as _,r as s,o as h}from"./index-CGNRhvz7.js";import"./date-CvSHk5ED.js";import"./navigation-guard-BSVpYbbP.js";import"./company-oDyd0dWV.js";import"./handleFailure-DtTpu7r3.js";import"./customer-C9SausZF.js";import"./user-KFDu8xJF.js";import"./well-Caso2ZGG.js";const w=u({name:"wells-new",components:{SvgIcon:p,WellGeneralInfo:b,PageHeader:d},setup(){const e=["Home","Wells","New"],t=f("general");return{breadcrumbs:e,tabIndex:t,setActiveTab:o=>{const a=o.target;t.value=a.getAttribute("data-tab-index")}}}}),y={class:"h-auto w-11/12 mx-auto flex flex-row gap-3 items-center justify-end",role:"tablist"},I={class:"nav-item"},W={class:"bg-white h-auto w-11/12 rounded-xl mx-auto mt-7 px-3 py-4 flex flex-col items-center"};function H(e,t,l,o,a,A){const i=s("PageHeader"),m=s("WellGeneralInfo");return h(),x(_,null,[n(i,{title:"Home",breadcrumbs:e.breadcrumbs},null,8,["breadcrumbs"]),r("ul",y,[r("li",I,[r("a",{class:v(["cursor-pointer font-semibold text-grey-400 hover:text-primary hover:border-b-2 hover:border-primary",{active:e.tabIndex==="general","text-primary border-b-2 border-primary":e.tabIndex==="general","text-grey-400":e.tabIndex!=="general"}]),onClick:t[0]||(t[0]=c=>e.setActiveTab(c)),"data-tab-index":"general"}," General ",2)])]),r("div",W,[n(m)])],64)}const j=g(w,[["render",H]]);export{j as default};
