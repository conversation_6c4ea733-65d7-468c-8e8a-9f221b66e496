import{h as Ve,g as Se,d as se,q as w,U as B,a3 as Ue,$ as Q,J as q,j as Pe,i as Ae,_ as te,c as g,o as m,a as t,n as G,b as o,r as S,s as Re,t as N,B as v,l as C,w as x,Z as Ne,E as W,F as X,k as xe,p as ee,x as Ee,L as H,m as ye}from"./index-BmHWvWFS.js";import{u as oe}from"./user-CVSNmFaf.js";import{S as le}from"./SvgIcon-DYvlNVZf.js";import{v as Z,y as K}from"./validator-D_t2fUhD.js";import{u as Me}from"./company-KQxnMnUF.js";import{c as Fe}from"./index.esm-C4vtr4xS.js";import{R as je,a as Oe,b as Be,c as Le}from"./regex-BLjctcPP.js";const qe=e=>e.map(s=>{var U;return{value:s==null?void 0:s.value,key:((U=Ve(s==null?void 0:s.value,Se))==null?void 0:U.label)||""}})||[],_e=se({name:"font-semibold",components:{SvgIcon:le},props:{userDetail:{type:Object,required:!0}},setup(e){var l,F,j,M;const s=oe(),U=Pe(),E=w(null),A=w(((l=e.userDetail)==null?void 0:l.avatar)||""),f=w(null),n=w(!1),d=Ae("userInfo"),D=qe(((F=e==null?void 0:e.userDetail)==null?void 0:F.roles)||[]).map(a=>a.key).join(", "),V=!!((M=(j=e==null?void 0:e.userDetail)==null?void 0:j.roles)!=null&&M.find(a=>a.value===B.SystemAdmin)),y=()=>{var a,c,b,P;return!!(((c=(a=e==null?void 0:e.userDetail)==null?void 0:a.roles)==null?void 0:c.length)===1&&((P=(b=e==null?void 0:e.userDetail)==null?void 0:b.roles)!=null&&P.find(R=>(R==null?void 0:R.value)===B.Engineer)))},I=()=>U.name==="my-profile",h=a=>{var c,b;if(a.target.files[0]){E.value=URL.createObjectURL(a.target.files[0]);const P=new FormData;P.append("file",a.target.files[0],a.target.files[0].name),P.append("userId",I()?(c=q.getUserInfo())==null?void 0:c.id:(b=e==null?void 0:e.userDetail)==null?void 0:b.id),p(P)}},p=async a=>{n.value=!0,s.uploadAvatar({params:a,callback:{onSuccess:c=>{var b,P,R;A.value=c==null?void 0:c.url,(((P=(b=U.params)==null?void 0:b.id)==null?void 0:P.toString())===((R=q.getUserInfo())==null?void 0:R.id)||I())&&(d==null||d.updateUserInfo({avatar:c==null?void 0:c.url}))},onFinish:()=>{n.value=!1}}})};return{isAdmin:Q,UserStatus:Ue,userRoles:D,avatar:A,fileImage:E,fileRef:f,uploadingAvatar:n,isUserDetailSystemAdmin:V,userProfileIsEngineer:y,handleChooseImage:h,clickInputFile:()=>{var a;(a=f==null?void 0:f.value)==null||a.click()}}}}),Te={class:"bg-card-background text-card-text rounded-lg h-auto w-full flex flex-col my-3 p-4 md:flex-row md:items-center md:justify-between lg:w-4/5 lg:mx-auto lg:min-w-[1560px]"},ze={class:"me-7 mb-4 md:mx-10 lg"},Je={class:"symbol symbol-100px symbol-lg-160px symbol-fixed relative"},Ye=["src"],Ze={class:"h-auto w-auto absolute top-16 left-17"},Ge=["disabled"],He={key:0},Ke={key:1,class:"spinner-border spinner-border-sm align-middle text-primary"},Qe={class:"h-auto w-full text-sm"},We={href:"#",class:"text-link hover:text-link font-bold text-lg"},Xe={class:"md:h-auto md:w-full md:flex md:gap-4 md:justify-between"},es={class:"md:h-auto md:w-full md:flex md:flex-col md:gap-4"},ss={class:"flex flex-row items-center gap-2 hover:text-link"},ts={class:"h-auto w-full flex flex-col my-3 my-3"},os={class:"flex flex-row items-center gap-2 hover:text-link"},ls={class:"h-auto w-full flex flex-col my-3"},as={class:"mb-0"},ns={class:"flex flex-row items-center gap-2 hover:text-link"},rs={class:"h-auto w-full flex flex-col my-3"},is={class:"md:h-auto md:w-full md:flex md:flex-col md:gap-4"},ds={class:"flex flex-row items-center gap-2 hover:text-link"},us={class:"h-auto w-full flex flex-col my-3"},ms={class:"flex flex-row items-center gap-2 hover:text-link"},fs={class:"h-auto w-full flex flex-col my-3"},cs={class:"flex flex-row items-center gap-2 hover:text-link"},gs={class:"h-auto w-full flex flex-col my-3"},bs={class:"md:h-auto md:w-full md:flex md:flex-col md:gap-4"},vs={key:0,class:"flex flex-row items-center gap-2 hover:text-link"},ps={class:"h-auto w-full flex flex-col my-3"},hs={key:1,class:"flex flex-col items-center gap-2 hover:text-link"},ws={class:"flex flex-row items-center gap-2 hover:text-link self-start"},ys={key:0,class:"flex flex-row gap-2 items-center self-start"},Ps=["src"],xs={class:"flex flex-col items-start gap-2 hover:text-link"};function ks(e,s,U,E,A,f){var D,V,y,I,h,p,k,l,F,j,M,a,c,b,P,R,L,_;const n=S("SvgIcon"),d=S("router-link");return m(),g("div",Te,[t("div",ze,[t("div",Je,[t("img",{class:"h-24 w-24 rounded-full object-cover md:h-32 md:w-32",src:e.avatar?e.avatar:e.fileImage||"/media/avatars/blank.png",alt:"image"},null,8,Ye),t("input",{type:"file",ref:"fileRef",class:"hidden",onChange:s[0]||(s[0]=(...O)=>e.handleChooseImage&&e.handleChooseImage(...O))},null,544),t("div",Ze,[t("button",{type:"button",class:G(`bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover font-semibold h-8 w-8 pl-2 rounded-full ${e.isAdmin()?"":"hidden"}`),disabled:e.uploadingAvatar,onClick:s[1]||(s[1]=(...O)=>e.clickInputFile&&e.clickInputFile(...O))},[e.uploadingAvatar?(m(),g("span",Ke)):(m(),g("span",He,[o(n,{icon:"pencilIcon"})]))],10,Ge)]),t("div",{class:G(`absolute top-10 left-21 mb-6 rounded-full border-4 border-light-border h-5 w-5 ${((D=e.userDetail)==null?void 0:D.status)===e.UserStatus.Active?"bg-success":"bg-danger"}`)},null,2)])]),t("div",Qe,[t("a",We,N(`${((V=e.userDetail)==null?void 0:V.firstName)||""} ${((y=e.userDetail)==null?void 0:y.lastName)||""}`),1),t("div",Xe,[t("div",es,[t("div",ss,[o(n,{icon:"shieldIcon"}),t("div",ts,[s[2]||(s[2]=t("h6",{class:"font-semibold"},"Roles",-1)),v(" "+N(e.userRoles),1)])]),t("div",os,[o(n,{icon:"atIcon"}),t("div",ls,[s[3]||(s[3]=t("h6",{class:"font-semibold"},"Email",-1)),t("p",as,N(((I=e.userDetail)==null?void 0:I.email)||""),1)])]),t("div",ns,[o(n,{icon:"activeCallIcon"}),t("div",rs,[s[4]||(s[4]=t("h6",{class:"font-semibold"},"Office Phone",-1)),v(" "+N(((h=e.userDetail)==null?void 0:h.officePhone)||""),1)])])]),t("div",is,[t("div",ds,[o(n,{icon:"starIcon",classname:"scale-50"}),t("div",us,[s[5]||(s[5]=t("h6",{class:"font-semibold"},"Status",-1)),t("span",{class:G(["p-0 user-status",((p=e.userDetail)==null?void 0:p.status)===e.UserStatus.Active?"text-green-400":"text-danger"])},N(((k=e.userDetail)==null?void 0:k.status)===e.UserStatus.Active?"Active":"Deactive"),3)])]),t("div",ms,[o(n,{icon:"pinIcon"}),t("div",fs,[s[6]||(s[6]=t("h6",{class:"font-semibold"},"Address",-1)),v(" "+N(((l=e.userDetail)==null?void 0:l.address)||""),1)])]),t("div",cs,[o(n,{icon:"activeCallIcon"}),t("div",gs,[s[7]||(s[7]=t("h6",{class:"font-semibold"},"Mobile Phone",-1)),v(" "+N(((F=e.userDetail)==null?void 0:F.mobilePhone)||""),1)])])]),t("div",bs,[e.isUserDetailSystemAdmin?C("",!0):(m(),g("div",vs,[o(n,{icon:"briefcaseIcon"}),t("div",ps,[s[8]||(s[8]=t("h6",{class:"font-semibold"},"Company",-1)),v(" "+N(((M=(j=e.userDetail)==null?void 0:j.company)==null?void 0:M.name)||""),1)])])),e.userProfileIsEngineer()?(m(),g("div",hs,[t("div",ws,[o(n,{icon:"userIcon"}),s[9]||(s[9]=t("h6",{class:"font-semibold"},"Supervisor",-1))]),(a=e.userDetail)!=null&&a.supervisor?(m(),g("div",ys,[t("img",{class:"h-11 w-11 rounded-full",src:((b=(c=e.userDetail)==null?void 0:c.supervisor)==null?void 0:b.avatar)||"/media/avatars/blank.png",alt:"Avatar"},null,8,Ps),t("div",xs,[o(d,{to:`/users/${(R=(P=e.userDetail)==null?void 0:P.supervisor)==null?void 0:R.id}`,class:"font-bold text-link hover:text-link"},{default:x(()=>{var O,T,z,J;return[v(N(`${((T=(O=e.userDetail)==null?void 0:O.supervisor)==null?void 0:T.firstName)||""} ${((J=(z=e.userDetail)==null?void 0:z.supervisor)==null?void 0:J.lastName)||""}`),1)]}),_:1},8,["to"]),v(" "+N((_=(L=e.userDetail)==null?void 0:L.supervisor)==null?void 0:_.email),1)])])):C("",!0)])):C("",!0)])]),Re(e.$slots,"toolbar")])])}const St=te(_e,[["render",ks]]),Ds=se({name:"change-password-modal",components:{SvgIcon:le},props:{isVisible:{type:Boolean,required:!0},close:{type:Function,required:!0}},setup(e){const s=oe(),U=w(!1),E={currentPassword:"",newPassword:"",confirmPassword:"",hideCurrentPassword:!0,hideNewPassword:!0,hideConfirmPassword:!0},A={length:{isValid:!1,text:"At least 8 characters"},lowercase:{isValid:!1,text:"At least 1 lowercase character"},uppercase:{isValid:!1,text:"At least 1 uppercase character"},specialCharacter:{isValid:!1,text:"At least 1 number and 1 special character"}},f=w({...E}),n=w(!1),d=w(null),D=w({...A}),V=a=>{const c=Z.validate(a,{pattern:je,errorsMessage:{pattern:"Incorrect password format."}}),b=Z.validate(a,{pattern:Oe,errorsMessage:{pattern:"Incorrect password format."}}),P=Z.validate(a,{pattern:Be,errorsMessage:{pattern:"Incorrect password format."}}),R=Z.validate(a,{pattern:Le,errorsMessage:{pattern:"Incorrect password format."}});return D.value={length:{isValid:!c,text:"At least 8 characters"},lowercase:{isValid:!b,text:"At least 1 lowercase character"},uppercase:{isValid:!P,text:"At least 1 uppercase character"},specialCharacter:{isValid:!R,text:"At least 1 number and 1 special character"}},c||b||P||R||""},y=()=>{e.close()},I=()=>{var a;f.value={...E},D.value={...A},(a=d==null?void 0:d.value)==null||a.resetFields()},k=w({currentPassword:[{required:!0,message:"Please type Current Password",trigger:["change","blur"]}],newPassword:[{validator:(a,c,b)=>{if(c==="")b(new Error("Please type New Password"));else{const P=V(c);P!==""?b(new Error(P)):b()}},trigger:["change","blur"]}],confirmPassword:[{validator:(a,c,b)=>{c===""?b(new Error("Please type Confirm Password")):c!==f.value.newPassword?b(new Error("Confirm Password doesn't match New Password!")):b()},trigger:["change","blur"]}]}),l=()=>{d.value&&d.value.validate(a=>{a&&j()})},F=a=>{a==="currentPassword"?f.value.hideCurrentPassword=!f.value.hideCurrentPassword:a==="confirmPassword"?f.value.hideConfirmPassword=!f.value.hideConfirmPassword:a==="newPassword"&&(f.value.hideNewPassword=!f.value.hideNewPassword)},j=()=>{W.alert("Are you sure you want to change your password?",{confirmButtonText:"Yes, Change it!",cancelButtonText:"No, cancel!",confirmButtonClass:"btn fw-bold btn-primary btn-sm",cancelButtonClass:"btn fw-bold btn-blue btn-sm",callback:{onConfirmed:()=>{M()}}},"warning")},M=()=>{var a,c;s.changePassword({params:{currentPassword:(a=f==null?void 0:f.value)==null?void 0:a.currentPassword,newPassword:(c=f==null?void 0:f.value)==null?void 0:c.newPassword},callback:{onSuccess:b=>{e.close(),W.resultAlert("Password is changed!","success")}}})};return{formRef:d,rules:k,modal:U,loading:n,targetData:f,rolesOptions:Ne,checkPassword:D,reset:I,submit:l,toggleEye:F,close:y}}}),Cs={key:0,class:"fixed top-0 right-0 bottom-0 left-0 z-40 flex items-center"},Is={class:"relative bg-card-background text-card-text max-h-11/12 w-11/12 flex flex-col items-center gap-3 mx-auto p-5 rounded-xl md:w-4/5 lg:w-2/5 lg:h-auto"},$s={class:"h-auto w-full flex flex-row items-center justify-between"},Vs={class:"h-auto w-full flex flex-col relative"},Ss={class:"relative"},Us={class:"h-auto w-full flex flex-col relative"},As={class:"relative"},Rs={class:"h-auto w-full flex flex-col relative"},Ns={class:"relative"},Es={className:"text-xs font-medium mb-4"},Ms=["className"],Fs={class:"flex flex-row items-center self-center gap-2"},js=["disabled"],Os=["disabled"],Bs={key:0,class:"indicator-label"},Ls={key:1,class:"indicator-progress"};function qs(e,s,U,E,A,f){const n=S("SvgIcon"),d=S("el-input"),D=S("el-form-item"),V=S("el-form");return e.isVisible?(m(),g("div",Cs,[s[14]||(s[14]=t("div",{class:"absolute inset-0 bg-sidebar-background opacity-25"},null,-1)),t("div",Is,[t("div",$s,[s[8]||(s[8]=t("h3",{class:"font-bold"},"Change Password",-1)),t("span",{class:"cursor-pointer",onClick:s[0]||(s[0]=(...y)=>e.close&&e.close(...y))},[o(n,{icon:"closeModalIcon"})])]),o(V,{id:"change_pass_form",onSubmit:ee(e.submit,["prevent"]),model:e.targetData,rules:e.rules,ref:"formRef",class:"h-auto w-full flex flex-col items-start gap-3"},{default:x(()=>{var y,I,h;return[t("div",Vs,[s[9]||(s[9]=t("label",{class:"font-semibold"},[v(" Current Password "),t("span",{class:"text-danger-active font-light"},"*")],-1)),t("div",Ss,[o(D,{prop:"currentPassword"},{default:x(()=>{var p;return[o(d,{size:"large",modelValue:e.targetData.currentPassword,"onUpdate:modelValue":s[1]||(s[1]=k=>e.targetData.currentPassword=k),name:"currentPassword",type:(p=e.targetData)!=null&&p.hideCurrentPassword?"password":"input"},null,8,["modelValue","type"])]}),_:1}),t("span",{class:"absolute top-3 right-2",onClick:s[2]||(s[2]=()=>e.toggleEye("currentPassword"))},[o(n,{icon:(y=e.targetData)!=null&&y.hideCurrentPassword?"hidePasswordIcon":"showPasswordIcon"},null,8,["icon"])])])]),t("div",Us,[s[10]||(s[10]=t("label",{class:"font-semibold"},[v(" New Password "),t("span",{class:"text-danger-active font-light"},"*")],-1)),t("div",As,[o(D,{prop:"newPassword"},{default:x(()=>{var p;return[o(d,{size:"large",modelValue:e.targetData.newPassword,"onUpdate:modelValue":s[3]||(s[3]=k=>e.targetData.newPassword=k),name:"newPassword",type:(p=e.targetData)!=null&&p.hideNewPassword?"password":"input"},null,8,["modelValue","type"])]}),_:1}),t("span",{class:"absolute top-3 right-2",onClick:s[4]||(s[4]=()=>e.toggleEye("newPassword"))},[o(n,{icon:(I=e.targetData)!=null&&I.hideNewPassword?"hidePasswordIcon":"showPasswordIcon"},null,8,["icon"])])])]),t("div",Rs,[s[11]||(s[11]=t("label",{class:"font-semibold"},[v(" Confirm Password "),t("span",{class:"text-danger-active font-light"},"*")],-1)),t("div",Ns,[o(D,{prop:"confirmPassword"},{default:x(()=>{var p;return[o(d,{size:"large",modelValue:e.targetData.confirmPassword,"onUpdate:modelValue":s[5]||(s[5]=k=>e.targetData.confirmPassword=k),name:"confirmPassword",type:(p=e.targetData)!=null&&p.hideConfirmPassword?"password":"input"},null,8,["modelValue","type"])]}),_:1}),t("span",{class:"absolute top-3 right-2",onClick:s[6]||(s[6]=()=>e.toggleEye("confirmPassword"))},[o(n,{icon:(h=e.targetData)!=null&&h.hideConfirmPassword?"hidePasswordIcon":"showPasswordIcon"},null,8,["icon"])])])]),t("div",Es,[s[12]||(s[12]=t("p",null,"New password must contain:",-1)),t("div",null,[(m(!0),g(X,null,xe(Object.entries(e.checkPassword),([p,k])=>(m(),g("div",{key:p,className:`flex items-center mt-1 gap-2 ${k.isValid?"text-success":"text-danger"}`},[o(n,{icon:k.isValid?"checkMarkIcon":"exclamationMarkIcon"},null,8,["icon"]),v(N(k.text),1)],8,Ms))),128))])]),t("div",Fs,[t("button",{type:"button",class:"bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover rounded-md px-4 py-2 font-semibold",onClick:s[7]||(s[7]=(...p)=>e.close&&e.close(...p)),disabled:e.loading}," Discard ",8,js),t("button",{class:"bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover rounded-md px-4 py-2 font-semibold",type:"submit",disabled:e.loading},[e.loading?C("",!0):(m(),g("span",Bs," Save ")),e.loading?(m(),g("span",Ls,s[13]||(s[13]=[v(" Please wait... "),t("span",{class:"spinner-border spinner-border-sm align-middle ms-2"},null,-1)]))):C("",!0)],8,Os)])]}),_:1},8,["onSubmit","model","rules"])])])):C("",!0)}const _s=te(Ds,[["render",qs],["__scopeId","data-v-398a8463"]]),Ts=se({name:"account-overview",components:{ChangePasswordModal:_s,SvgIcon:le},props:{userDetail:{type:Object,required:!0},hideRole:{type:Boolean,required:!1,default:!1},reloadUserData:{type:Function,required:!0}},setup(e){var re,ie,de,ue;const s=Me(),U=oe(),E=Pe(),A=w(!1),f=w(!1),n=w(!1),d=w(!1),D=w(!1),V=w(null),y=w(null),I=w(!1),h=w({...e.userDetail,companyId:(ie=(re=e==null?void 0:e.userDetail)==null?void 0:re.company)==null?void 0:ie.id}),p=w([]),k=!!((ue=(de=e==null?void 0:e.userDetail)==null?void 0:de.roles)!=null&&ue.find(r=>r.value===B.SystemAdmin));Ee(()=>{l()});const l=()=>{H()?F():j()},F=async()=>{s.getCompanies({params:{page:1,limit:500},callback:{onSuccess:r=>{var u;p.value=(u=r==null?void 0:r.items)==null?void 0:u.map(i=>({value:i==null?void 0:i.id,label:i==null?void 0:i.name}))}}})},j=async()=>{var u,i;const r=(i=(u=q)==null?void 0:u.getUserInfo())==null?void 0:i.companyId;r&&s.getCompanyById({id:r,callback:{onSuccess:$=>{p.value=[{value:$==null?void 0:$.id,label:$==null?void 0:$.name}]}}})},M=()=>{var u;const r=[];if((u=e==null?void 0:e.userDetail)!=null&&u.roles)for(const i of e.userDetail.roles)i.value!==B.SystemAdmin&&r.push(i.value);return r},a=w({userRoles:M()}),c=w(null),b=Fe().shape({email:K.emailAddress,mobilePhone:K.mobilePhone,officePhone:K.officePhone}),P={firstName:[{required:!0,message:"Please type First Name",trigger:["blur","change"]}],lastName:[{required:!0,message:"Please type Last Name",trigger:["blur","change"]}],email:[{required:!0,validator:(r,u,i)=>{b.fields.email.validate(u).then(()=>{i()}).catch($=>{i(new Error($.errors[0]))})},trigger:["blur","change"]}],mobilePhone:[{required:!0,validator:(r,u,i)=>{b.fields.mobilePhone.validate(u).then(()=>{i()}).catch($=>{i(new Error($.errors[0]))})},trigger:["blur","change"]}],officePhone:[{validator:(r,u,i)=>{u?b.fields.officePhone.validate(u).then(()=>{i()}).catch($=>{i(new Error($.errors[0]))}):i()},trigger:["blur","change"]}]},R=w(H()||k?{...P,companyId:[{required:!0,message:"Please select Company",trigger:["change","blur"]}]}:P),L=w({userRoles:[{type:"array",required:!0,message:"Please choose role",trigger:"change"}]}),_=()=>{A.value=!0},O=()=>{var r,u,i;A.value=!1,(r=V==null?void 0:V.value)==null||r.resetFields(),h.value={...e.userDetail,companyId:(i=(u=e==null?void 0:e.userDetail)==null?void 0:u.company)==null?void 0:i.id}},T=()=>{var r;f.value=!1,(r=y==null?void 0:y.value)==null||r.resetFields(),a.value={userRoles:M()}},z=()=>{f.value=!0},J=()=>{V.value&&V.value.validate(r=>{var u,i,$,me,fe,ce,ge,be,ve,pe,he;if(r){n.value=!0;const we={id:(u=h.value)==null?void 0:u.id,email:((i=h.value)==null?void 0:i.email)!==(($=e==null?void 0:e.userDetail)==null?void 0:$.email)?(me=h.value)==null?void 0:me.email:null,firstName:(fe=h.value)==null?void 0:fe.firstName,lastName:(ce=h.value)==null?void 0:ce.lastName,address:((ge=h.value)==null?void 0:ge.address)||null,note:((be=h.value)==null?void 0:be.note)||null,officePhone:((ve=h.value)==null?void 0:ve.officePhone)||null,mobilePhone:(pe=h.value)==null?void 0:pe.mobilePhone,companyId:(he=h.value)==null?void 0:he.companyId};Y()?ae(we,()=>{n.value=!1,A.value=!1}):ne(we,()=>{n.value=!1,A.value=!1})}})},ke=()=>{y.value&&y.value.validate(r=>{var u;r&&(d.value=!0,Y()?ae(a.value,()=>{d.value=!1,f.value=!1}):ne({...a.value,id:(u=h.value)==null?void 0:u.id},()=>{d.value=!1,f.value=!1}))})},De=()=>{D.value=!0,U.resetUserPassword({params:{email:e.userDetail.email||""},callback:{onSuccess:()=>{D.value=!1,W.resultAlert("An email is sent to user’s email with the link to reset user password. Please check with your user!",void 0,"/media/reset-pass.jpg",70)},onFinish:()=>{D.value=!1}}})},Ce=()=>q.checkRole(B.CompanyAdmin),Y=()=>{var r,u;return E.name==="my-profile"||((r=q.getUserInfo())==null?void 0:r.id)===((u=e==null?void 0:e.userDetail)==null?void 0:u.id)},Ie=()=>Q()||Y(),$e=()=>{I.value=!I.value},ae=(r,u)=>{U.updateMyProfile({params:r,callback:{onSuccess:i=>{var $;($=e==null?void 0:e.reloadUserData)==null||$.call(e)},onFinish:u}})},ne=(r,u)=>{U.updateUserProfile({id:r==null?void 0:r.id,params:r,callback:{onSuccess:()=>{var i;(i=e==null?void 0:e.reloadUserData)==null||i.call(e)},onFinish:u}})};return{isSystemAdmin:H,isUserDetailSystemAdmin:k,UserType:B,roleRules:L,userInfoRules:R,formData:h,roleData:a,userInfoFormRef:V,roleFormRef:y,isEditMode:A,isEditRole:f,submittingUserInfo:n,submittingRole:d,changePasswordModal:c,companyList:p,loading:D,isModalVisible:I,submit:J,resetPass:De,submitRole:ke,isAdmin:Q,editUserInfo:_,cancelEditUserInfo:O,isOwner:Y,isShowPassword:Ie,isCompanyAdmin:Ce,editRole:z,cancelEditRole:T,toggleChangePassword:$e}}}),zs={class:"bg-card-background text-card-text h-auto w-full mx-auto py-4 px-4 mb-4 rounded-b-lg lg:w-4/5 lg:mx-auto lg:min-w-[1560px]"},Js={class:"h-auto w-full flex flex-row items-center justify-between"},Ys={key:0},Zs={key:1,class:"h-auto w-full gap-2 flex flex-row items-end justify-between"},Gs=["disabled"],Hs={key:0,class:"indicator-label"},Ks={key:1,class:"indicator-progress"},Qs={class:"md:h-auto md:w-full md:flex md:gap-4"},Ws={class:"md:h-auto md:w-full md:flex md:flex-col"},Xs={class:"flex flex-col gap-2 font-semibold"},et={class:"flex flex-col gap-2 font-semibold"},st={class:"flex flex-col gap-2 font-semibold"},tt={class:"md:h-auto md:w-full md:flex md:flex-col"},ot={class:"flex flex-col gap-2 font-semibold"},lt={class:"flex flex-col gap-2 font-semibold"},at={key:0,class:"flex flex-col gap-2 font-semibold"},nt={class:"flex flex-col gap-2 font-semibold"},rt={class:"flex flex-col gap-2 font-semibold"},it={class:"h-auto w-full border-t-[1px] border-dashed border-grey-300 pt-3"},dt={class:"py-4"},ut={class:"h-auto w-full flex flex-row items-center justify-between"},mt={key:0},ft={key:1,class:"h-auto w-full flex flex-row items-start gap-2"},ct=["disabled"],gt={type:"submit",class:"bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover rounded-md px-4 py-2 font-semibold"},bt={key:0,class:"indicator-label"},vt={key:1,class:"indicator-progress"},pt={key:1},ht={class:"h-auto w-full mt-4 flex flex-row flex-wrap items-start gap-3 text-sm"},wt={key:0},yt={key:1,class:"indicator-progress"};function Pt(e,s,U,E,A,f){const n=S("el-input"),d=S("el-form-item"),D=S("el-option"),V=S("el-select"),y=S("el-form"),I=S("el-checkbox"),h=S("el-checkbox-group"),p=S("SvgIcon"),k=S("ChangePasswordModal");return m(),g(X,null,[t("div",zs,[t("div",Js,[s[17]||(s[17]=t("h2",{class:"font-bold"},"User information",-1)),e.isAdmin()||e.isOwner()?(m(),g("div",Ys,[e.isEditMode?(m(),g("div",Zs,[t("button",{class:"bg-danger text-white rounded-md px-3 py-2 font-semibold",onClick:s[1]||(s[1]=(...l)=>e.cancelEditUserInfo&&e.cancelEditUserInfo(...l)),disabled:e.submittingUserInfo}," Cancel ",8,Gs),t("button",{class:"bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover rounded-md px-4 py-2 font-semibold",type:"button",onClick:s[2]||(s[2]=(...l)=>e.submit&&e.submit(...l))},[e.submittingUserInfo?C("",!0):(m(),g("span",Hs," Save ")),e.submittingUserInfo?(m(),g("span",Ks,s[16]||(s[16]=[v(" Please wait... "),t("span",{class:"spinner-border spinner-border-sm align-middle ms-2"},null,-1)]))):C("",!0)])])):(m(),g("button",{key:0,class:"bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover rounded-md px-4 py-2 font-semibold",onClick:s[0]||(s[0]=(...l)=>e.editUserInfo&&e.editUserInfo(...l))}," Edit "))])):C("",!0)]),o(y,{id:"user_form",onSubmit:ee(e.submit,["prevent"]),model:e.formData,rules:e.userInfoRules,ref:"userInfoFormRef",disabled:!e.isEditMode||e.submittingUserInfo,class:"h-auto w-full flex flex-col gap-2 text-sm mt-4"},{default:x(()=>[t("div",Qs,[t("div",Ws,[t("div",Xs,[s[18]||(s[18]=t("label",null,[v("First Name "),t("span",{class:"text-danger-active font-light"},"*")],-1)),o(d,{prop:"firstName"},{default:x(()=>[o(n,{placeholder:"First Name",name:"firstName",modelValue:e.formData.firstName,"onUpdate:modelValue":s[3]||(s[3]=l=>e.formData.firstName=l)},null,8,["modelValue"])]),_:1})]),t("div",et,[s[19]||(s[19]=t("label",null,[v("Last name "),t("span",{class:"text-danger-active font-light"},"*")],-1)),o(d,{prop:"lastName"},{default:x(()=>[o(n,{placeholder:"Last Name",name:"lastName",modelValue:e.formData.lastName,"onUpdate:modelValue":s[4]||(s[4]=l=>e.formData.lastName=l)},null,8,["modelValue"])]),_:1})]),t("div",st,[s[20]||(s[20]=t("label",null,[v("Email Address "),t("span",{class:"text-danger-active font-light"},"*")],-1)),o(d,{prop:"email"},{default:x(()=>[o(n,{placeholder:"Email",name:"email",modelValue:e.formData.email,"onUpdate:modelValue":s[5]||(s[5]=l=>e.formData.email=l)},null,8,["modelValue"])]),_:1})])]),t("div",tt,[t("div",ot,[s[21]||(s[21]=v(" Office Phone ")),o(d,{prop:"officePhone"},{default:x(()=>[o(n,{placeholder:"Office Phone",name:"officePhone",modelValue:e.formData.officePhone,"onUpdate:modelValue":s[6]||(s[6]=l=>e.formData.officePhone=l)},null,8,["modelValue"])]),_:1})]),t("div",lt,[s[22]||(s[22]=t("label",null,[v("Mobile Phone "),t("span",{class:"text-danger-active font-light"},"*")],-1)),o(d,{prop:"mobilePhone"},{default:x(()=>[o(n,{placeholder:"Mobile Phone",name:"mobilePhone",modelValue:e.formData.mobilePhone,"onUpdate:modelValue":s[7]||(s[7]=l=>e.formData.mobilePhone=l)},null,8,["modelValue"])]),_:1})]),e.isUserDetailSystemAdmin?C("",!0):(m(),g("div",at,[s[23]||(s[23]=t("label",null,[v("Company "),t("span",{class:"text-danger-active font-light"},"*")],-1)),o(d,{prop:"companyId",class:"mt-auto"},{default:x(()=>[o(V,{modelValue:e.formData.companyId,"onUpdate:modelValue":s[8]||(s[8]=l=>e.formData.companyId=l),placeholder:"Select company",class:"w-100",clearable:"",disabled:!e.isSystemAdmin()},{default:x(()=>[(m(!0),g(X,null,xe(e.companyList,l=>(m(),ye(D,{key:l.value,label:l.label,value:l.value,name:"companyId"},null,8,["label","value"]))),128))]),_:1},8,["modelValue","disabled"])]),_:1})]))])]),t("div",nt,[s[24]||(s[24]=v(" Address ")),o(d,{prop:"address"},{default:x(()=>[o(n,{placeholder:"Address",name:"address",modelValue:e.formData.address,"onUpdate:modelValue":s[9]||(s[9]=l=>e.formData.address=l)},null,8,["modelValue"])]),_:1})]),t("div",rt,[s[25]||(s[25]=v(" Note ")),o(d,{prop:"note",class:"mt-auto"},{default:x(()=>[o(n,{placeholder:"",type:"textarea",rows:"2",name:"note",modelValue:e.formData.note,"onUpdate:modelValue":s[10]||(s[10]=l=>e.formData.note=l)},null,8,["modelValue"])]),_:1})])]),_:1},8,["onSubmit","model","rules","disabled"]),t("div",it,[s[34]||(s[34]=t("h2",{class:"font-bold"},"Security",-1)),t("div",dt,[e.hideRole?C("",!0):(m(),ye(y,{key:0,id:"role_form",onSubmit:ee(e.submitRole,["prevent"]),model:e.roleData,ref:"roleFormRef",class:"flex flex-col gap-3",disabled:!e.isEditRole,rules:e.roleRules},{default:x(()=>[t("div",ut,[s[27]||(s[27]=t("label",{class:"font-bold"},"Roles",-1)),e.isAdmin()?(m(),g("div",mt,[e.isEditRole?(m(),g("div",ft,[t("button",{type:"button",class:"bg-danger text-white rounded-md px-3 py-2 font-semibold",onClick:s[12]||(s[12]=(...l)=>e.cancelEditRole&&e.cancelEditRole(...l)),disabled:e.submittingRole}," Cancel ",8,ct),t("button",gt,[e.submittingRole?C("",!0):(m(),g("span",bt," Save ")),e.submittingRole?(m(),g("span",vt,s[26]||(s[26]=[v(" Please wait... "),t("span",{class:"spinner-border spinner-border-sm align-middle ms-2"},null,-1)]))):C("",!0)])])):(m(),g("button",{key:0,type:"button",class:"bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover rounded-md px-4 py-2 font-semibold",onClick:s[11]||(s[11]=(...l)=>e.editRole&&e.editRole(...l))}," Edit "))])):C("",!0)]),o(d,{prop:"userRoles"},{default:x(()=>[o(h,{modelValue:e.roleData.userRoles,"onUpdate:modelValue":s[13]||(s[13]=l=>e.roleData.userRoles=l),class:"h-auto w-full flex flex-col items-start gap-2"},{default:x(()=>[o(I,{label:e.UserType.CompanyAdmin,name:"role"},{default:x(()=>s[28]||(s[28]=[v(" Company Admin ")])),_:1},8,["label"]),o(I,{label:e.UserType.Supervisor,name:"role"},{default:x(()=>s[29]||(s[29]=[v(" Supervisor ")])),_:1},8,["label"]),o(I,{label:e.UserType.Engineer,name:"role"},{default:x(()=>s[30]||(s[30]=[v(" Engineer ")])),_:1},8,["label"])]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["onSubmit","model","disabled","rules"])),e.isShowPassword()?(m(),g("div",pt,[s[33]||(s[33]=t("label",{class:"font-bold"},"Password",-1)),t("div",ht,[e.isAdmin()?(m(),g("button",{key:0,type:"button",class:"bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover rounded-md px-4 py-2 font-semibold flex flex-row items-center gap-2 justify-between",onClick:s[14]||(s[14]=(...l)=>e.resetPass&&e.resetPass(...l))},[o(p,{icon:"gearIcon"}),e.loading?C("",!0):(m(),g("span",wt," Reset password ")),e.loading?(m(),g("span",yt,s[31]||(s[31]=[v(" Please wait... "),t("span",{class:"spinner-border spinner-border-sm align-middle ms-2"},null,-1)]))):C("",!0)])):C("",!0),e.isOwner()?(m(),g("button",{key:1,type:"button",class:"bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover rounded-md px-4 py-2 font-semibold flex flex-row items-center gap-2 justify-between",onClick:s[15]||(s[15]=(...l)=>e.toggleChangePassword&&e.toggleChangePassword(...l))},[o(p,{icon:"gearIcon"}),s[32]||(s[32]=v(" Change Password "))])):C("",!0)])])):C("",!0)])])]),o(k,{isVisible:e.isModalVisible,close:e.toggleChangePassword,ref:"changePasswordModal"},null,8,["isVisible","close"])],64)}const Ut=te(Ts,[["render",Pt]]);export{Ut as O,St as U};
