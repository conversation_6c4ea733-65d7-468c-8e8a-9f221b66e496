import{S as O}from"./SvgIcon-CMhyaXWN.js";import{d as $,q as c,L as P,J as C,j as L,A as _,Z as J,_ as T,m as Z,o as N,w as r,a,b as d,r as b,c as q,l as j,B as z,p as G}from"./index-CGNRhvz7.js";import{y as D}from"./validator-6laVLK0J.js";import{u as H}from"./company-oDyd0dWV.js";import{u as K}from"./user-KFDu8xJF.js";import{c as Q}from"./index.esm-DXW765zG.js";const W=$({name:"user-modal",components:{SvgIcon:O},props:{loadPage:{type:Function,required:!1}},setup(l){var S;const e=L(),h=H(),U=K(),g=c(!1),y=c([]),w={firstName:"",lastName:"",companyId:P()?"":(S=C.getUserInfo())==null?void 0:S.companyId,email:"",officePhone:"",mobilePhone:"",userRoles:[],address:"",note:""},m=c(!1),i=c({...w}),u=c(null),v=c(!1),p=c(!1),o=e.name==="users";_(g,n=>{n===!1?I():P()?R():A()});const R=async()=>{p.value=!0,h.getCompanies({params:{page:1,limit:500},callback:{onSuccess:n=>{var s;y.value=(s=n==null?void 0:n.items)==null?void 0:s.map(t=>({value:t==null?void 0:t.id,label:t==null?void 0:t.name}))},onFinish:()=>{p.value=!1}}})},A=async()=>{var n;p.value=!0,h.getCompanyById({id:(n=C.getUserInfo())==null?void 0:n.companyId,callback:{onSuccess:s=>{y.value=[{value:s==null?void 0:s.id,label:s==null?void 0:s.name}]},onFinish:()=>{p.value=!1}}})},F=()=>{g.value=!0},k=()=>{g.value=!1},V=Q().shape({email:D.emailAddress,mobilePhone:D.mobilePhone,officePhone:D.officePhone}),M=c({firstName:[{required:!0,message:"Please type First Name",trigger:["blur","change"]}],lastName:[{required:!0,message:"Please type Last Name",trigger:["blur","change"]}],companyId:[{required:!0,message:"Please select Company",trigger:["blur","change"]}],email:[{required:!0,validator:(n,s,t)=>{V.fields.email.validate(s).then(()=>{t()}).catch(f=>{t(new Error(f.errors[0]))})},trigger:["blur","change"]}],mobilePhone:[{required:!0,validator:(n,s,t)=>{V.fields.mobilePhone.validate(s).then(()=>{t()}).catch(f=>{t(new Error(f.errors[0]))})},trigger:["blur","change"]}],officePhone:[{validator:(n,s,t)=>{s?V.fields.officePhone.validate(s).then(()=>{t()}).catch(f=>{t(new Error(f.errors[0]))}):t()},trigger:["blur","change"]}]}),x=()=>{u.value&&u.value.validate(n=>{var s,t,f;if(n){const E={...i.value,officePhone:((s=i.value)==null?void 0:s.officePhone)||null,address:((t=i.value)==null?void 0:t.address)||null,note:((f=i.value)==null?void 0:f.note)||null};B(E)}})},B=async n=>{v.value=!0,U.addUser({params:n,callback:{onSuccess:s=>{var t;k(),(t=l==null?void 0:l.loadPage)==null||t.call(l,s==null?void 0:s.id)},onFinish:()=>{v.value=!1}}})},I=()=>{var n;m.value=!1,i.value={...w},(n=u==null?void 0:u.value)==null||n.resetFields()};return{disabledRole:m,companyOptions:y,isSystemAdmin:P,modal:g,rules:M,loading:v,loadingCompany:p,targetData:i,formRef:u,rolesOptions:J,isUserManagementPage:o,show:F,hide:k,submit:x,reset:I,setInitialValue:n=>{m.value=!0,i.value={...i.value,...n}}}}}),X={class:"d-flex align-items-center w-100"},Y={class:"row mb-3"},ee={class:"col-3 fv-row d-flex flex-column justify-content-stretch"},le={class:"col-3 fv-row d-flex flex-column justify-content-stretch"},ae={class:"col-6 fv-row d-flex flex-column justify-content-stretch"},oe={class:"row mb-3"},se={class:"col-6 fv-row d-flex flex-column justify-content-stretch"},te={class:"col-6 fv-row d-flex flex-column justify-content-stretch"},ne={class:"row mb-3"},de={class:"col-6 fv-row d-flex flex-column justify-content-stretch"},ie={class:"col-6 fv-row d-flex flex-column justify-content-stretch"},re={class:"row mb-3"},me={class:"col-12 fv-row d-flex flex-column justify-content-stretch"},ue={class:"row mb-3"},fe={class:"col-12 fv-row d-flex flex-column justify-content-stretch"},ce={class:"modal-footer d-flex justify-content-center align-items-center"},pe=["disabled"],be=["data-kt-indicator","disabled"],ge={key:0,class:"indicator-label"},ve={key:1,class:"indicator-progress"};function ye(l,e,h,U,g,y){const w=b("SvgIcon"),m=b("el-input"),i=b("el-form-item"),u=b("el-select-v2"),v=b("el-form"),p=b("el-dialog");return N(),Z(p,{modelValue:l.modal,"onUpdate:modelValue":e[11]||(e[11]=o=>l.modal=o),"show-close":!1,width:"800","align-center":""},{header:r(()=>[a("div",X,[e[12]||(e[12]=a("h3",{class:"modal-title"},"Add User",-1)),a("span",{class:"cursor-pointer ms-auto",onClick:e[0]||(e[0]=(...o)=>l.hide&&l.hide(...o))},[d(w,{icon:"closeModalIcon"})])])]),default:r(()=>[a("div",null,[d(v,{id:"product_form",onSubmit:G(l.submit,["prevent"]),model:l.targetData,rules:l.rules,ref:"formRef",class:"form"},{default:r(()=>[a("div",Y,[a("div",ee,[e[13]||(e[13]=a("label",{class:"d-flex align-items-center fs-6 fw-semibold mb-2 text-break required"},"First Name ",-1)),d(i,{prop:"firstName",class:"mt-auto"},{default:r(()=>[d(m,{class:"w-100",modelValue:l.targetData.firstName,"onUpdate:modelValue":e[1]||(e[1]=o=>l.targetData.firstName=o),placeholder:"",name:"firstName"},null,8,["modelValue"])]),_:1})]),a("div",le,[e[14]||(e[14]=a("label",{class:"d-flex align-items-center fs-6 fw-semibold mb-2 text-break required"},"Last Name ",-1)),d(i,{prop:"lastName",class:"mt-auto"},{default:r(()=>[d(m,{class:"w-100",modelValue:l.targetData.lastName,"onUpdate:modelValue":e[2]||(e[2]=o=>l.targetData.lastName=o),placeholder:"",name:"lastName"},null,8,["modelValue"])]),_:1})]),a("div",ae,[e[15]||(e[15]=a("label",{class:"d-flex align-items-center fs-6 fw-semibold mb-2 required"}," Company ",-1)),d(i,{prop:"companyId"},{default:r(()=>[d(u,{disabled:!l.isSystemAdmin()||!l.isUserManagementPage,class:"w-100",modelValue:l.targetData.companyId,"onUpdate:modelValue":e[3]||(e[3]=o=>l.targetData.companyId=o),options:l.companyOptions,placeholder:"Company Name",name:"companyId",loading:l.loadingCompany},null,8,["disabled","modelValue","options","loading"])]),_:1})])]),a("div",oe,[a("div",se,[e[16]||(e[16]=a("label",{class:"d-flex align-items-center fs-6 fw-semibold mb-2 text-break required"},"Email Address ",-1)),d(i,{prop:"email",class:"mt-auto"},{default:r(()=>[d(m,{class:"w-100",modelValue:l.targetData.email,"onUpdate:modelValue":e[4]||(e[4]=o=>l.targetData.email=o),placeholder:"",name:"email"},null,8,["modelValue"])]),_:1})]),a("div",te,[e[17]||(e[17]=a("label",{class:"d-flex align-items-center fs-6 fw-semibold mb-2 text-break"},"Office Phone ",-1)),d(i,{prop:"officePhone",class:"mt-auto"},{default:r(()=>[d(m,{class:"w-100",modelValue:l.targetData.officePhone,"onUpdate:modelValue":e[5]||(e[5]=o=>l.targetData.officePhone=o),placeholder:"",name:"officePhone"},null,8,["modelValue"])]),_:1})])]),a("div",ne,[a("div",de,[e[18]||(e[18]=a("label",{class:"d-flex align-items-center fs-6 fw-semibold mb-2 text-break required"},"Mobile Phone ",-1)),d(i,{prop:"mobilePhone",class:"mt-auto"},{default:r(()=>[d(m,{class:"w-100",modelValue:l.targetData.mobilePhone,"onUpdate:modelValue":e[6]||(e[6]=o=>l.targetData.mobilePhone=o),placeholder:"",name:"mobilePhone"},null,8,["modelValue"])]),_:1})]),a("div",ie,[e[19]||(e[19]=a("label",{class:"d-flex align-items-center fs-6 fw-semibold mb-2 text-break"},"Roles ",-1)),d(i,{prop:"userRoles"},{default:r(()=>[d(u,{class:"w-100",modelValue:l.targetData.userRoles,"onUpdate:modelValue":e[7]||(e[7]=o=>l.targetData.userRoles=o),options:l.rolesOptions,placeholder:"Roles",multiple:"",clearable:"",name:"userRoles",disabled:l.disabledRole},null,8,["modelValue","options","disabled"])]),_:1})])]),a("div",re,[a("div",me,[e[20]||(e[20]=a("label",{class:"d-flex align-items-center fs-6 fw-semibold mb-2 text-break"},"Address ",-1)),d(i,{prop:"address",class:"mt-auto"},{default:r(()=>[d(m,{class:"w-100",modelValue:l.targetData.address,"onUpdate:modelValue":e[8]||(e[8]=o=>l.targetData.address=o),placeholder:"",name:"address"},null,8,["modelValue"])]),_:1})])]),a("div",ue,[a("div",fe,[e[21]||(e[21]=a("label",{class:"d-flex align-items-center fs-6 fw-semibold mb-2 text-break"},"Note ",-1)),d(i,{prop:"note",class:"mt-auto"},{default:r(()=>[d(m,{class:"w-100",modelValue:l.targetData.note,"onUpdate:modelValue":e[9]||(e[9]=o=>l.targetData.note=o),placeholder:"",type:"textarea",rows:"2",name:"note"},null,8,["modelValue"])]),_:1})])]),a("div",ce,[a("button",{type:"button",id:"kt_modal_add_customer_cancel",class:"btn text-gray-700 btn-sm me-3 btn-blue",onClick:e[10]||(e[10]=(...o)=>l.hide&&l.hide(...o)),disabled:l.loading}," Discard ",8,pe),a("button",{"data-kt-indicator":l.loading?"on":null,class:"btn btn-sm btn-primary",type:"submit",disabled:l.loading},[l.loading?j("",!0):(N(),q("span",ge," Save ")),l.loading?(N(),q("span",ve,e[22]||(e[22]=[z(" Please wait... "),a("span",{class:"spinner-border spinner-border-sm align-middle ms-2"},null,-1)]))):j("",!0)],8,be)])]),_:1},8,["onSubmit","model","rules"])])]),_:1},8,["modelValue"])}const ke=T(W,[["render",ye]]);export{ke as U};
