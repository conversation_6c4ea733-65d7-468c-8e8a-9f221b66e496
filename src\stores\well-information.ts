import ApiService from "../services/ApiService";
import { get, noop } from "lodash";
import { defineStore } from "pinia";
import { handleFailure } from "../utils/handleFailure";
import type { Callback } from "../types/common";

export const useWellInformationStore = defineStore("wellInformation", () => {
  const updateWellInformation = async ({
    id,
    params,
    callback,
  }: {
    id: string;
    params: any;
    callback: Callback;
  }): Promise<void> => {
    const onSuccess = get(callback, "onSuccess", noop);
    const onFinish = get(callback, "onFinish", noop);

    try {
      ApiService.setHeader();
      const response = await ApiService.put(`wellInformations/${id}`, params);
      onSuccess(response.data?.data || response.data);
    } catch (error) {
      handleFailure(error, callback);
    } finally {
      onFinish();
    }
  };

  const deleteWellInformation = async ({
    id,
    callback,
  }: {
    id: string;
    callback: Callback;
  }): Promise<void> => {
    const onSuccess = get(callback, "onSuccess", noop);
    const onFinish = get(callback, "onFinish", noop);

    try {
      ApiService.setHeader();
      const response = await ApiService.delete(`wellInformations/${id}`);
      onSuccess(response.data?.data || response.data);
    } catch (error) {
      handleFailure(error, callback);
    } finally {
      onFinish();
    }
  };

  const getWellInformation = async ({
    params,
    callback,
  }: {
    params: any;
    callback: Callback;
  }): Promise<void> => {
    const onSuccess = get(callback, "onSuccess", noop);
    const onFinish = get(callback, "onFinish", noop);

    try {
      ApiService.setHeader();
      const response = await ApiService.getWithParams(
        "wellInformations",
        params
      );
      onSuccess(response.data?.data || response.data);
    } catch (error) {
      handleFailure(error, callback);
    } finally {
      onFinish();
    }
  };

  const getWellInfoToday = async ({
    dailyReportId,
    callback,
  }: {
    dailyReportId: string;
    callback: Callback;
  }): Promise<void> => {
    const onSuccess = get(callback, "onSuccess", noop);
    const onFinish = get(callback, "onFinish", noop);

    try {
      ApiService.setHeader();
      const response = await ApiService.get(
        `/wellInformations/wellInformationToday/${dailyReportId}`
      );
      onSuccess(response.data?.data || response.data);
    } catch (error) {
      handleFailure(error, callback);
    } finally {
      onFinish();
    }
  };

  const createWellInformation = async ({
    params,
    callback,
  }: {
    params: any;
    callback: Callback;
  }): Promise<void> => {
    const onSuccess = get(callback, "onSuccess", noop);
    const onFinish = get(callback, "onFinish", noop);

    try {
      ApiService.setHeader();
      const response = await ApiService.post("wellInformations", params);
      onSuccess(response.data?.data || response.data);
    } catch (error) {
      handleFailure(error, callback);
    } finally {
      onFinish();
    }
  };

  return {
    updateWellInformation,
    deleteWellInformation,
    getWellInformation,
    getWellInfoToday,
    createWellInformation,
  };
});
