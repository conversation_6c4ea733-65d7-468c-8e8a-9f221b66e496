import{d as O,q as y,x as k,L as x,Y as I,Z as A,_ as N,c as V,o as w,a as t,b as a,w as u,p as U,r as f,l as $}from"./index-DalLS0_6.js";import{u as q}from"./company-DGE9srvS.js";const C={value:"",label:"All"},S={value:0,label:"All"},B=O({name:"user-management-filter",components:{},props:{hideFilter:{type:Function,required:!1,default:()=>{}},onFilter:{type:Function,required:!0}},setup(e){const l=q(),d=y(!1),b=y([C]),g=[S,...A],c=[S,...I],p={email:"",status:0,role:0,companyId:""},n=y({...p});k(()=>{x()&&r()});const r=async()=>{d.value=!0,l.getCompanies({params:{page:1,limit:500},callback:{onSuccess:i=>{var m;b.value=[C,...(m=i==null?void 0:i.items)==null?void 0:m.map(s=>({value:s==null?void 0:s.id,label:s==null?void 0:s.name}))]},onFinish:()=>{d.value=!1}}})},v=()=>{e!=null&&e.hideFilter&&(e==null||e.hideFilter())},o=()=>{n.value={...p},F()},F=()=>{var i,m,s,h;e.onFilter({email:((i=n.value)==null?void 0:i.email)||null,status:((m=n.value)==null?void 0:m.status)||null,role:((s=n.value)==null?void 0:s.role)||null,companyId:((h=n.value)==null?void 0:h.companyId)||null})};return{loading:d,isSystemAdmin:x,filterForm:n,roleOptions:g,companyOptions:b,statusOptions:c,apply:F,hideFilter:v,resetFilter:o}}}),E={class:"bg-minicard-background text-minicard-text-light max-h-11/12 w-11/12 flex flex-col items-center gap-3 mx-auto p-5 rounded-xl overflow-y-scroll shadow-sm md:overflow-hidden lg:w-2/5 lg:h-3/5"},R={class:"h-auto w-full flex flex-col"},M={class:"h-auto w-full flex flex-row gap-3"},L={class:"h-auto w-full flex flex-col gap-3"},Y={class:"flex flex-col gap-2"},Z={class:"flex flex-col gap-2"},j={class:"flex flex-col gap-2"},z={key:0,class:"flex flex-col gap-2"};function D(e,l,d,b,g,c){const p=f("el-input"),n=f("el-form-item"),r=f("el-select-v2"),v=f("el-form");return w(),V("div",E,[t("div",R,[l[7]||(l[7]=t("h5",{class:"h-auto w-full font-semibold self-start"}," Filter ",-1)),t("div",M,[t("button",{class:"px-4 py-2 font-semibold text-sm text-button-text-light hover:text-button-text-light-hover bg-button-primary hover:bg-button-primary-hover rounded-md",onClick:l[0]||(l[0]=(...o)=>e.resetFilter&&e.resetFilter(...o))}," Reset "),t("button",{class:"px-4 py-2 font-semibold text-sm text-button-text-light hover:text-button-text-light-hover bg-button-primary hover:bg-button-primary-hover rounded-md",onClick:l[1]||(l[1]=(...o)=>e.hideFilter&&e.hideFilter(...o))}," Close "),t("button",{class:"px-4 py-2 font-semibold text-sm text-button-text-light hover:text-button-text-light-hover bg-button-primary hover:bg-button-primary-hover rounded-md",type:"button",onClick:l[2]||(l[2]=(...o)=>e.apply&&e.apply(...o))}," Apply ")])]),t("div",L,[a(v,{model:e.filterForm,onSubmit:U(e.apply,["prevent"])},{default:u(()=>[t("div",Y,[l[8]||(l[8]=t("label",{class:"font-semibold"},"Email",-1)),a(n,{prop:"email"},{default:u(()=>[a(p,{placeholder:"Email",name:"email",modelValue:e.filterForm.email,"onUpdate:modelValue":l[3]||(l[3]=o=>e.filterForm.email=o)},null,8,["modelValue"])]),_:1})]),t("div",Z,[l[9]||(l[9]=t("label",{class:"font-semibold"},"Status",-1)),a(n,{prop:"status"},{default:u(()=>[a(r,{options:e.statusOptions,placeholder:"Status",name:"status",modelValue:e.filterForm.status,"onUpdate:modelValue":l[4]||(l[4]=o=>e.filterForm.status=o)},null,8,["options","modelValue"])]),_:1})]),t("div",j,[l[10]||(l[10]=t("label",{class:"font-semibold"},"Roles",-1)),a(n,{prop:"role"},{default:u(()=>[a(r,{options:e.roleOptions,placeholder:"Roles",name:"role",modelValue:e.filterForm.role,"onUpdate:modelValue":l[5]||(l[5]=o=>e.filterForm.role=o)},null,8,["options","modelValue"])]),_:1})]),e.isSystemAdmin()?(w(),V("div",z,[l[11]||(l[11]=t("label",{class:"font-semibold"},"Company",-1)),a(n,{prop:"companyId"},{default:u(()=>[a(r,{options:e.companyOptions,placeholder:"Company",name:"companyId",modelValue:e.filterForm.companyId,"onUpdate:modelValue":l[6]||(l[6]=o=>e.filterForm.companyId=o),loading:e.loading},null,8,["options","modelValue","loading"])]),_:1})])):$("",!0),l[12]||(l[12]=t("button",{class:"btn btn-sm btn-primary d-none",type:"submit"},null,-1))]),_:1},8,["model","onSubmit"])])])}const J=N(B,[["render",D]]);export{J as F};
