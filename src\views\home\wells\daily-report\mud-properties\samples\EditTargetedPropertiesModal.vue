<template>
  <div
    v-if="isVisible"
    class="fixed top-0 right-0 bottom-0 left-0 z-40 flex items-center"
  >
    <!-- Dark overlay background -->
    <div class="absolute inset-0 bg-sidebar-background opacity-25"></div>
    <div
      class="relative bg-card-background text-card-text-light max-h-11/12 w-11/12 flex flex-col items-center gap-3 mx-auto my-6 p-5 rounded-xl overflow-y-scroll md:overflow-hidden lg:w-3/5 lg:h-auto"
    >
      <div class="flex flex-row h-auto w-full items-center justify-between">
        <h3 class="text-lg font-bold">Edit Targeted Properties</h3>
        <span class="cursor-pointer" @click="closeModal">
          <SvgIcon icon="closeModalIcon" />
        </span>
      </div>

      <el-form
        id="edit_targeted_properties_form"
        @submit.prevent="submit"
        :model="targetData"
        :rules="rules"
        ref="formRef"
        class="h-auto w-full font-semibold"
      >
        <div class="h-auto w-full flex flex-col gap-3 md:flex-row">
          <div class="h-auto w-full flex flex-col gap-3">
            <div class="flex flex-col gap-1">
              <label
                >Fluid Type<span class="text-danger-active font-light">*</span>
                <el-popover placement="top" :width="400" trigger="hover">
                  <template #reference>
                    <i class="fas fa-exclamation-circle ms-2 fs-7"></i>
                  </template>
                  <span>
                    Water-based drilling muds are the most common type. They use
                    water as the base fluid and may contain various additives.
                    The key differences for WBM include water quality,
                    filtration properties, and clay content.<br /><br />
                    Oil-based drilling muds use oil as the base fluid. Key
                    considerations for OBM include oil composition, oil/water
                    ratio, and rheological properties.<br /><br />
                    Synthetic-based drilling muds use synthetic fluids as the
                    base, such as esters or olefins. Specific data requirements
                    depend on the type of synthetic fluid used.
                  </span>
                </el-popover></label
              >
              <el-form-item prop="fluidType">
                <el-select
                  v-model="targetData.fluidType"
                  placeholder=""
                  clearable
                >
                  <el-option
                    v-for="item in fluidTypeOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                    name="fluidType"
                  />
                </el-select>
              </el-form-item>
            </div>
            <div class="flex flex-col gap-1">
              <label>
                MW (ppg or lbs/gal)<span class="text-danger-active font-light"
                  >*</span
                >
                <el-popover placement="bottom" :width="400" trigger="hover">
                  <template #reference>
                    <i class="fas fa-exclamation-circle ms-2 fs-7 me-4"></i>
                  </template>
                  <span>
                    Mud Weight, also known as mud density, is the density of the
                    drilling mud, typically measured in pounds per gallon (ppg).
                    For different types of drilling fluids, the density
                    requirements can vary.
                  </span>
                </el-popover>
              </label>
              <el-form-item prop="mudWeight">
                <el-input
                  type="number"
                  :controls="false"
                  step="any"
                  v-model="targetData.mudWeight"
                  placeholder=""
                  name="mudWeight"
                ></el-input
              ></el-form-item>
            </div>

            <div class="flex flex-col gap-1">
              <label
                ><span>Funnel Viscosity (sec/qt) </span>
                <el-popover placement="top" :width="400" trigger="hover">
                  <template #reference>
                    <i class="fas fa-exclamation-circle ms-2 fs-7"></i>
                  </template>
                  <span>
                    The funnel viscosity measures the thickness or viscosity of
                    the drilling mud. It is typically measured in seconds per
                    quart (sec/qt).
                  </span>
                </el-popover></label
              >
              <el-form-item prop="funnelViscosity">
                <el-input
                  type="number"
                  :controls="false"
                  step="any"
                  v-model="targetData.funnelViscosity"
                  placeholder=""
                  name="funnelViscosity"
                ></el-input>
              </el-form-item>
            </div>
            <div class="flex flex-col gap-1">
              <label
                ><span>PV (Plastic Viscosity) (cP)</span>
                <el-popover placement="top" :width="400" trigger="hover">
                  <template #reference>
                    <i class="fas fa-exclamation-circle ms-2 fs-7"></i>
                  </template>
                  <span>
                    Plastic Viscosity is a measure of the resistance to flow of
                    the drilling mud. It is typically measured in centipoise
                    (cP).
                  </span>
                </el-popover></label
              >
              <el-form-item prop="plasticViscosity">
                <el-input
                  type="number"
                  :controls="false"
                  step="any"
                  v-model="targetData.plasticViscosity"
                  placeholder=""
                  name="plasticViscosity"
                ></el-input>
              </el-form-item>
            </div>

            <div class="flex flex-col gap-1">
              <label>
                <span>Yield Point (YP) (lbf/100ft2)</span>
                <el-popover placement="top" :width="400" trigger="hover">
                  <template #reference>
                    <i class="fas fa-exclamation-circle ms-2 fs-7 me-4"></i>
                  </template>
                  <span>
                    Yield Point is the amount of force required to initiate mud
                    flow. It's typically measured in pounds per 100 square feet
                    (lbf/100ft^2).
                  </span>
                </el-popover> </label
              ><el-form-item prop="yieldPoint">
                <el-input
                  type="number"
                  :controls="false"
                  step="any"
                  v-model="targetData.yieldPoint"
                  placeholder=""
                  name="yieldPoint"
                ></el-input
              ></el-form-item>
            </div>
            <div class="flex flex-col gap-1">
              <label>
                <span>6 rpm</span>
                <!-- <el-popover placement="top" :width="400" trigger="hover">
                  <template #reference>
                    <i class="fas fa-exclamation-circle ms-2 fs-7 me-4"></i>
                  </template>
                  <span>
                    The amount of fine LCM added to the mud to control lost
                    circulation.
                  </span>
                </el-popover> --> </label
              ><el-form-item prop="rpm">
                <el-input
                  type="number"
                  :controls="false"
                  step="any"
                  v-model="targetData.rpm"
                  placeholder=""
                  name="rpm"
                ></el-input
              ></el-form-item>
            </div>

            <div class="flex flex-col gap-1">
              <label
                ><span>API filtrate (ml/30min)</span>
                <el-popover placement="top" :width="400" trigger="hover">
                  <template #reference>
                    <i class="fas fa-exclamation-circle ms-2 fs-7"></i>
                  </template>
                  <span>
                    The volume of mud filtrate that passes through a standard
                    filter paper in 30 minutes, measured in milliliters.<br /><br />

                    WBM: API filtrate and cake thickness are commonly measured
                    for Water-Based Mud. The filtration properties may be
                    different for various types of WBM formulations. OBM:
                    Oil-Based Mud may have different filtration characteristics
                    compared to WBM due to the presence of oil. The API filtrate
                    and cake thickness may have specific requirements for OBM.
                    SBM: Synthetic-Based Mud may also have unique filtration
                    properties, and API filtrate and cake thickness measurements
                    should consider the specific synthetic fluid used.
                  </span>
                </el-popover></label
              >
              <el-form-item prop="apiFiltrate">
                <el-input
                  type="number"
                  :controls="false"
                  step="any"
                  v-model="targetData.apiFiltrate"
                  placeholder=""
                  name="apiFiltrate"
                ></el-input>
              </el-form-item>
            </div>
            <div class="flex flex-col gap-1">
              <label>
                <span>API Cake</span>
                <el-popover placement="top" :width="400" trigger="hover">
                  <template #reference>
                    <i class="fas fa-exclamation-circle ms-2 fs-7 me-4"></i>
                  </template>
                  <span>
                    The thickness of the filter cake formed by the drilling mud
                    on the filter paper, measured in 1/32 of an inch.<br /><br />

                    WBM: API filtrate and cake thickness are commonly measured
                    for Water-Based Mud. The filtration properties may be
                    different for various types of WBM formulations. OBM:
                    Oil-Based Mud may have different filtration characteristics
                    compared to WBM due to the presence of oil. The API filtrate
                    and cake thickness may have specific requirements for OBM.
                    SBM: Synthetic-Based Mud may also have unique filtration
                    properties, and API filtrate and cake thickness measurements
                    should consider the specific synthetic fluid used.
                  </span>
                </el-popover> </label
              ><el-form-item prop="apiCakeThickness">
                <el-input
                  type="number"
                  :controls="false"
                  step="any"
                  v-model="targetData.apiCakeThickness"
                  placeholder=""
                  name="apiCakeThickness"
                ></el-input
              ></el-form-item>
            </div>
          </div>
          <div class="h-auto w-full flex flex-col gap-3">
            <div class="flex flex-col gap-1">
              <label
                ><span>pH</span>
                <el-popover placement="top" :width="400" trigger="hover">
                  <template #reference>
                    <i class="fas fa-exclamation-circle ms-2 fs-7"></i>
                  </template>
                  <span>
                    The pH level of the drilling mud, which can affect the
                    performance of mud additives.
                  </span>
                </el-popover></label
              >
              <el-form-item prop="pH">
                <el-input
                  type="number"
                  :controls="false"
                  step="any"
                  v-model="targetData.pH"
                  placeholder=""
                  name="pH"
                ></el-input>
              </el-form-item>
            </div>
            <div class="flex flex-col gap-1">
              <label>
                <span>Mud Alkalinity (Pm) (ml)</span>
                <el-popover placement="top" :width="400" trigger="hover">
                  <template #reference>
                    <i class="fas fa-exclamation-circle ms-2 fs-7 me-4"></i>
                  </template>
                  <span>
                    Alkalinity measurements that can provide insights into the
                    mud's pH buffering capacity and stability.
                  </span>
                </el-popover> </label
              ><el-form-item prop="mudAlkalinity">
                <el-input
                  type="number"
                  :controls="false"
                  step="any"
                  v-model="targetData.mudAlkalinity"
                  placeholder=""
                  name="mudAlkalinity"
                ></el-input
              ></el-form-item>
            </div>

            <div class="flex flex-col gap-1">
              <label
                ><span>Filtrate Alkalinity (Pf) (ml)</span>
                <el-popover placement="top" :width="400" trigger="hover">
                  <template #reference>
                    <i class="fas fa-exclamation-circle ms-2 fs-7"></i>
                  </template>
                  <span>
                    Alkalinity measurements that can provide insights into the
                    mud's pH buffering capacity and stability.
                  </span>
                </el-popover></label
              >
              <el-form-item prop="filtrateAlkalinity">
                <el-input
                  type="number"
                  :controls="false"
                  step="any"
                  v-model="targetData.filtrateAlkalinity"
                  placeholder=""
                  name="filtrateAlkalinity"
                ></el-input>
              </el-form-item>
            </div>
            <div class="flex flex-col gap-1">
              <label
                ><span>Filtrate Alkalinity (Mf) (ml)</span>
                <el-popover placement="top" :width="400" trigger="hover">
                  <template #reference>
                    <i class="fas fa-exclamation-circle ms-2 fs-7"></i>
                  </template>
                  <span>
                    Alkalinity measurements that can provide insights into the
                    mud's pH buffering capacity and stability.
                  </span>
                </el-popover></label
              >
              <el-form-item prop="filtrateAlkalinity">
                <el-input
                  type="number"
                  :controls="false"
                  step="any"
                  v-model="targetData.filtrateAlkalinity"
                  placeholder=""
                  name="filtrateAlkalinity"
                ></el-input>
              </el-form-item>
            </div>

            <div class="flex flex-col gap-1">
              <label
                ><span>Chlorides (mg/L)</span>
                <el-popover placement="top" :width="400" trigger="hover">
                  <template #reference>
                    <i class="fas fa-exclamation-circle ms-2 fs-7"></i>
                  </template>
                  <span>
                    Measurements of various ions in the drilling mud, which can
                    affect mud chemistry and performance.
                  </span>
                </el-popover></label
              >
              <el-form-item prop="chlorides">
                <el-input
                  type="number"
                  :controls="false"
                  step="any"
                  v-model="targetData.chlorides"
                  placeholder=""
                  name="chlorides"
                ></el-input>
              </el-form-item>
            </div>
            <div class="flex flex-col gap-1">
              <label>
                <span>Total Hardness (mg/L)</span>
                <el-popover placement="top" :width="400" trigger="hover">
                  <template #reference>
                    <i class="fas fa-exclamation-circle ms-2 fs-7 me-4"></i>
                  </template>
                  <span>
                    Measurements of various ions in the drilling mud, which can
                    affect mud chemistry and performance.
                  </span>
                </el-popover> </label
              ><el-form-item prop="totalHardness">
                <el-input
                  type="number"
                  :controls="false"
                  step="any"
                  v-model="targetData.totalHardness"
                  placeholder=""
                  name="totalHardness"
                ></el-input
              ></el-form-item>
            </div>

            <div class="flex flex-col gap-1">
              <label
                ><span>Linear Gel Strength (LGS) (%)</span>
                <el-popover placement="top" :width="400" trigger="hover">
                  <template #reference>
                    <i class="fas fa-exclamation-circle ms-2 fs-7"></i>
                  </template>
                  <span>
                    LGS (%) = (Gel Strength (lbs/100 ft²) / Mud Weight
                    (lbs/gal)) x 100
                  </span>
                </el-popover></label
              >
              <el-form-item prop="linearGelStrengthPercent">
                <el-input
                  type="number"
                  :controls="false"
                  step="any"
                  :max="100"
                  :min="0"
                  v-model="targetData.linearGelStrengthPercent"
                  placeholder=""
                  name="linearGelStrengthPercent"
                ></el-input>
              </el-form-item>
            </div>
          </div>
        </div>
        <div class="h-auto w-full flex flex-row items-center gap-2">
          <button
            type="button"
            class="bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover rounded-md px-4 py-2 font-semibold"
            @click="reset"
            :disabled="loading"
          >
            Discard
          </button>
          <button
            class="bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover rounded-md px-4 py-2 font-semibold"
            type="submit"
            :disabled="loading"
          >
            <span v-if="!loading" class="indicator-label"> Save </span>
            <span v-if="loading" class="indicator-progress">
              Please wait...
              <span
                class="spinner-border spinner-border-sm align-middle ms-2"
              ></span>
            </span>
          </button>
        </div>
      </el-form>
    </div>
  </div>
</template>

<script lang="ts">
import SvgIcon from "@/constants/SvgIcon.vue";
import { fluidTypeOptions, sampleFromOptions } from "@/constants/sample";
import { useTargetPropertyStore } from "@/stores/target-property";
import { defineComponent, ref, watch } from "vue";

export default defineComponent({
  name: "edit-targeted-properties-modal",
  components: { SvgIcon },
  props: {
    isVisible: {
      type: Boolean,
      required: true,
    },
    close: {
      type: Function,
      required: true,
    },
    loadPage: {
      type: Function,
      default: () => {},
      required: true,
    },
    wellId: {
      type: String,
      required: true,
    },
  },
  setup(props) {
    const targetPropertyStore = useTargetPropertyStore();
    const initialForm = {
      fluidType: null,
      mudWeight: null,
      funnelViscosity: null,
      plasticViscosity: null,
      yieldPoint: null,
      apiFiltrate: null,
      apiCakeThickness: null,
      pH: null,
      mudAlkalinity: null,
      filtrateAlkalinity: null,
      chlorides: null,
      totalHardness: null,
      linearGelStrengthPercent: null,
      rpm: null,
    };
    const targetData = ref<any>(JSON.parse(JSON.stringify(initialForm)));
    const formRef = ref<null | HTMLFormElement>(null);
    const loading = ref<boolean>(false);
    const id = ref("");

    watch(
      () => props.isVisible,
      (newValue) => {
        if (newValue == true) {
          getTargetProperty();
        } else {
          reset();
          formRef?.value?.resetFields();
        }
      }
    );

    const getTargetProperty = async (): Promise<void> => {
      loading.value = true;

      targetPropertyStore.getTargetPropertyDetails({
        wellId: props?.wellId,
        callback: {
          onSuccess: (res: any) => {
            targetData.value = JSON.parse(JSON.stringify(res));
          },
          onFinish: (_err: any) => {
            loading.value = false;
          },
        },
      });
    };

    const updateTargetProperty = async (param: any): Promise<void> => {
      loading.value = true;
      targetPropertyStore.updateTargetProperty({
        id: targetData?.value?.id || "",
        params: param,
        callback: {
          onSuccess: (_res: any) => {
            if (props?.loadPage) {
              props?.loadPage();
            }
            closeModal();
            console.log("target property updated");
          },
          onFinish: (_err: any) => {
            loading.value = false;
          },
        },
      });
    };

    const setId = (targetId: string) => {
      id.value = targetId.toString();
    };

    const closeModal = () => {
      props.close();
    };

    const rules = ref({
      fluidType: [
        {
          required: true,
          message: "Please select Fluid Type",
          trigger: "change",
        },
      ],
    });

    const submit = () => {
      if (!formRef.value) {
        return;
      }

      formRef.value.validate((valid: boolean) => {
        if (valid) {
          const params = {
            fluidType: Number(targetData?.value?.fluidType),
            mudWeight: Number(targetData?.value?.mudWeight),
            funnelViscosity: Number(targetData?.value?.funnelViscosity),
            plasticViscosity: Number(targetData?.value?.plasticViscosity),
            yieldPoint: Number(targetData?.value?.yieldPoint),
            apiFiltrate: Number(targetData?.value?.apiFiltrate),
            apiCakeThickness: Number(targetData?.value?.apiCakeThickness),
            pH: Number(targetData?.value?.pH),
            mudAlkalinity: Number(targetData?.value?.mudAlkalinity),
            filtrateAlkalinity: Number(targetData?.value?.filtrateAlkalinity),
            chlorides: Number(targetData?.value?.chlorides),
            totalHardness: Number(targetData?.value?.totalHardness),
            linearGelStrengthPercent: Number(
              targetData?.value?.linearGelStrengthPercent
            ),
            rpm: Number(targetData?.value?.rpm),
          };
          updateTargetProperty(params);
        }
      });
    };

    const reset = () => {
      targetData.value = JSON.parse(JSON.stringify(initialForm));
    };

    return {
      rules,
      loading,
      targetData,
      fluidTypeOptions,
      sampleFromOptions,
      formRef,
      setId,
      closeModal,
      submit,
      reset,
    };
  },
});
</script>
