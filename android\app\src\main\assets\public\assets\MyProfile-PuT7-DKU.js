import{P as f}from"./PageHeader-Sj9hJFB8.js";import{u as y}from"./user-CVSNmFaf.js";import{U as g}from"./UserDetail-CBoVLdrv.js";import{d as b,q as l,x as D,i as P,_ as U,c as m,l as p,b as M,m as _,a as c,F as k,r as u,o as n}from"./index-BmHWvWFS.js";import"./handleFailure-WBgBpurp.js";import"./SvgIcon-DYvlNVZf.js";import"./Overview-DMdf3K2c.js";import"./validator-D_t2fUhD.js";import"./index.esm-C4vtr4xS.js";import"./company-KQxnMnUF.js";import"./regex-BLjctcPP.js";import"./AssignUserModal-kk-OATZ2.js";import"./UserModal-Cgio3UOU.js";import"./TablePagination-lslz6s2e.js";import"./TableHeader-DGdsllig.js";import"./table-bhK9qpe4.js";import"./date-CKteeARj.js";const v=b({name:"my-profile",components:{UserDetail:g,PageHeader:f},setup(){const e=y(),r=l(),o=l(!1),i=["My Profile","Settings"],t=P("userInfo");D(()=>{a()});const a=async()=>{o.value=!0,e.getMyProfile({callback:{onSuccess:s=>{t==null||t.updateUserInfo({...s}),r.value=s},onFinish:()=>{o.value=!1}}})};return{loading:o,userDetail:r,breadcrumbs:i,getMyProfile:a}}}),B={key:0,class:"text-center my-auto"};function S(e,r,o,i,t,a){const s=u("PageHeader"),d=u("UserDetail");return n(),m(k,null,[e.loading?(n(),m("div",B,r[0]||(r[0]=[c("div",{class:"spinner-border text-primary",role:"status"},[c("span",{class:"sr-only"},"Loading...")],-1)]))):p("",!0),M(s,{title:"My Profile",breadcrumbs:e.breadcrumbs},null,8,["breadcrumbs"]),!e.loading&&e.userDetail?(n(),_(d,{key:1,userDetail:e.userDetail,reloadUserData:e.getMyProfile},null,8,["userDetail","reloadUserData"])):p("",!0)],64)}const J=U(v,[["render",S]]);export{J as default};
