import{S as D}from"./SuccessTemplate-CUOzQV3M.js";import{S as M}from"./SvgIcon-DYvlNVZf.js";import{d as B,q as f,u as T,j as U,A as j,a7 as F,G as L,_ as $,c as g,m as G,l as N,a,b as m,w as k,p as q,r as h,o as u,F as z,k as H,t as O,B as Y}from"./index-BmHWvWFS.js";import{R as J,a as K,b as Q,c as W}from"./regex-BLjctcPP.js";import{v as S}from"./validator-D_t2fUhD.js";import{_ as X}from"./well-logo-diufKTHF.js";import"./index.esm-C4vtr4xS.js";var I=(e=>(e[e.RESET=1]="RESET",e[e.SUCCESS=2]="SUCCESS",e))(I||{});const Z=B({name:"reset-password",components:{SvgIcon:M,SuccessTemplate:D},setup(){const e=f(1),t=T(),l=U().query,E={newPassword:"",confirmPassword:"",hideNewPassword:!0,hideConfirmPassword:!0},x={length:{isValid:!1,text:"At least 8 characters"},lowercase:{isValid:!1,text:"At least 1 lowercase character"},uppercase:{isValid:!1,text:"At least 1 uppercase character"},specialCharacter:{isValid:!1,text:"At least 1 number and 1 special character"}},r=f({...E}),w=f(!1),p=f(null),v=f({...x});j(()=>r.value.newPassword,o=>{});const C=o=>{const n=S.validate(o,{pattern:J,errorsMessage:{pattern:"Incorrect password format."}}),s=S.validate(o,{pattern:K,errorsMessage:{pattern:"Incorrect password format."}}),i=S.validate(o,{pattern:Q,errorsMessage:{pattern:"Incorrect password format."}}),P=S.validate(o,{pattern:W,errorsMessage:{pattern:"Incorrect password format."}});return v.value={length:{isValid:!n,text:"At least 8 characters"},lowercase:{isValid:!s,text:"At least 1 lowercase character"},uppercase:{isValid:!i,text:"At least 1 uppercase character"},specialCharacter:{isValid:!P,text:"At least 1 number and 1 special character"}},n||s||i||P||""},b=f({newPassword:[{validator:(o,n,s)=>{if(n==="")s(new Error("Please type New Password"));else{const i=C(n);i!==""?s(new Error(i)):s()}},trigger:["change","blur"]}],confirmPassword:[{validator:(o,n,s)=>{n===""?s(new Error("Please type Confirm Password")):n!==r.value.newPassword?s(new Error("Confirm Password doesn't match New Password!")):s()},trigger:["change","blur"]}]});return{currentStep:e,STEP:I,formRef:p,rules:b,loading:w,targetData:r,checkPassword:v,query:l,submit:()=>{var o,n;p.value&&(w.value=!0,t.resetPassword({params:{email:((o=l==null?void 0:l.email)==null?void 0:o.toString())||"",otp:((n=l==null?void 0:l.otp)==null?void 0:n.toString())||"",newPassword:r.value.newPassword},callback:{onSuccess:()=>{var s;(s=p.value)==null||s.validate(i=>{i&&(e.value=2)})},onFinish:()=>{w.value=!1},onFailure:s=>{var i,P,_,R;F.fire({text:L[(P=(i=s==null?void 0:s.response)==null?void 0:i.data)==null?void 0:P.errorCode]||((R=(_=s==null?void 0:s.response)==null?void 0:_.data)==null?void 0:R.message)||(s==null?void 0:s.message)||"Sorry, looks like there are some errors detected, please try again.",icon:"error",buttonsStyling:!1,confirmButtonText:"Got it!",heightAuto:!1,customClass:{confirmButton:"btn fw-semibold btn-light-danger"}})}}}))},toggleEye:o=>{o==="confirmPassword"?r.value.hideConfirmPassword=!r.value.hideConfirmPassword:o==="newPassword"&&(r.value.hideNewPassword=!r.value.hideNewPassword)}}}}),ee={class:"form-container w-xxl-500px p-10 bg-white rounded-3"},se={key:0},te={class:"d-flex flex-column gap-10"},ae={class:"row"},oe={class:"col-12 fv-row d-flex flex-column justify-content-stretch mb-7"},re={class:"position-relative input-password"},ne={class:"col-12 fv-row d-flex flex-column justify-content-stretch"},ie={class:"position-relative input-password"},le={className:"validate-password my-5"},de=["className"],ce={class:"ms-2"},me={class:"d-flex justify-content-end mt-10"},pe=["data-kt-indicator","disabled"],ue={key:0,class:"indicator-label"},we={key:1,class:"indicator-progress"};function fe(e,t,A,l,E,x){var y;const r=h("el-input"),w=h("el-form-item"),p=h("SvgIcon"),v=h("el-form"),C=h("SuccessTemplate");return u(),g("div",ee,[e.currentStep===e.STEP.RESET?(u(),g("div",se,[t[9]||(t[9]=a("div",{class:"text-center mb-10"},[a("img",{src:X,width:100,height:100})],-1)),a("div",te,[t[8]||(t[8]=a("h1",{class:"text-dark text-center"},"Reset Password",-1)),m(v,{id:"change_pass_form",onSubmit:q(e.submit,["prevent"]),model:e.targetData,rules:e.rules,ref:"formRef",class:"form"},{default:k(()=>{var V,b;return[a("div",ae,[a("div",oe,[t[4]||(t[4]=a("label",{class:"d-flex align-items-center fs-6 fw-semibold mb-2 text-break required"},"New Password ",-1)),a("div",re,[m(w,{prop:"newPassword",class:"mt-auto mb-0"},{default:k(()=>{var d;return[m(r,{size:"large",class:"w-100",modelValue:e.targetData.newPassword,"onUpdate:modelValue":t[0]||(t[0]=c=>e.targetData.newPassword=c),name:"newPassword",type:(d=e.targetData)!=null&&d.hideNewPassword?"password":"input"},null,8,["modelValue","type"])]}),_:1}),a("span",{class:"svg-icon svg-icon-1 position-absolute top-50 eye-icon",onClick:t[1]||(t[1]=()=>e.toggleEye("newPassword"))},[m(p,{icon:(V=e.targetData)!=null&&V.hideNewPassword?"hidePasswordIcon":"showPasswordIcon"},null,8,["icon"])])])]),a("div",ne,[t[5]||(t[5]=a("label",{class:"d-flex align-items-center fs-6 fw-semibold mb-2 required"}," Confirm Password ",-1)),a("div",ie,[m(w,{prop:"confirmPassword",class:"mt-auto mb-0"},{default:k(()=>{var d;return[m(r,{size:"large",class:"w-100",modelValue:e.targetData.confirmPassword,"onUpdate:modelValue":t[2]||(t[2]=c=>e.targetData.confirmPassword=c),name:"confirmPassword",type:(d=e.targetData)!=null&&d.hideConfirmPassword?"password":"input"},null,8,["modelValue","type"])]}),_:1}),a("span",{class:"svg-icon svg-icon-1 position-absolute top-50 eye-icon",onClick:t[3]||(t[3]=()=>e.toggleEye("confirmPassword"))},[m(p,{icon:(b=e.targetData)!=null&&b.hideConfirmPassword?"hidePasswordIcon":"showPasswordIcon"},null,8,["icon"])])])])]),a("div",le,[t[6]||(t[6]=a("p",null,"New password must contain:",-1)),a("div",null,[(u(!0),g(z,null,H(Object.entries(e.checkPassword),([d,c])=>(u(),g("div",{key:d,className:`d-flex mt-1 ${c.isValid?"valid":"invalid"}`},[m(p,{icon:c.isValid?"checkMarkIcon":"exclamationMarkIcon"},null,8,["icon"]),a("span",ce,O(c.text),1)],8,de))),128))])]),a("div",me,[a("button",{"data-kt-indicator":e.loading?"on":null,class:"btn btn-sm btn-primary ms-auto",type:"submit",disabled:e.loading},[e.loading?N("",!0):(u(),g("span",ue," Reset ")),e.loading?(u(),g("span",we,t[7]||(t[7]=[Y(" Please wait... "),a("span",{class:"spinner-border spinner-border-sm align-middle ms-2"},null,-1)]))):N("",!0)],8,pe)])]}),_:1},8,["onSubmit","model","rules"])])])):e.currentStep===e.STEP.SUCCESS?(u(),G(C,{key:1,message:`Hey ${(y=e.query)==null?void 0:y.user_name}, Your new password is set`,description:"Go to Login Page and start your journey now"},null,8,["message"])):N("",!0)])}const ye=$(Z,[["render",fe]]);export{ye as default};
