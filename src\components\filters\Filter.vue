<template>
  <div
    class="bg-minicard-background text-minicard-text-light max-h-11/12 w-11/12 flex flex-col items-center gap-3 mx-auto p-5 rounded-xl overflow-y-scroll shadow-sm md:overflow-hidden lg:w-2/5 lg:h-3/5"
  >
    <div class="h-auto w-full flex flex-col">
      <h5 class="h-auto w-full  font-semibold self-start">
        Filter
      </h5>

      <div class="h-auto w-full flex flex-row gap-3">
        <button
          class="px-4 py-2 font-semibold text-sm text-button-text-light hover:text-button-text-light-hover bg-button-primary hover:bg-button-primary-hover rounded-md"
          @click="resetFilter"
        >
          Reset
        </button>
        <button
          class="px-4 py-2 font-semibold text-sm text-button-text-light hover:text-button-text-light-hover bg-button-primary hover:bg-button-primary-hover rounded-md"
          @click="hideFilter"
        >
          Close
        </button>
        <button
          class="px-4 py-2 font-semibold text-sm text-button-text-light hover:text-button-text-light-hover bg-button-primary hover:bg-button-primary-hover rounded-md"
          type="button"
          @click="apply"
        >
          Apply
        </button>
      </div>
    </div>
    <div class="h-auto w-full flex flex-col gap-3 ">
      <el-form :model="filterForm" @submit.prevent="apply">
        <div class="flex flex-col gap-2">
          <label class="font-semibold">Email</label>
          <el-form-item prop="email">
            <el-input
              placeholder="Email"
              name="email"
              v-model="filterForm.email"
            ></el-input>
          </el-form-item>
        </div>
        <div class="flex flex-col gap-2">
          <label class="font-semibold">Status</label>
          <el-form-item prop="status">
            <el-select-v2
              :options="statusOptions"
              placeholder="Status"
              name="status"
              v-model="filterForm.status"
            />
          </el-form-item>
        </div>
        <div class="flex flex-col gap-2">
          <label class="font-semibold">Roles</label>
          <el-form-item prop="role">
            <el-select-v2
              :options="roleOptions"
              placeholder="Roles"
              name="role"
              v-model="filterForm.role"
            />
          </el-form-item>
        </div>
        <div class="flex flex-col gap-2" v-if="isSystemAdmin()" >
          <label class="font-semibold">Company</label>
          <el-form-item prop="companyId">
            <el-select-v2
              :options="companyOptions"
              placeholder="Company"
              name="companyId"
              v-model="filterForm.companyId"
              :loading="loading"
            />
          </el-form-item>
        </div>
        <button class="btn btn-sm btn-primary d-none" type="submit"></button>
      </el-form>
    </div>
  </div>
</template>

<script lang="ts">
import { rolesOptions, userStatusOptions } from "../../constants/user";
import { isSystemAdmin } from "../../services/JwtService";
import { useCompanyStore } from "../../stores/company";
import { defineComponent, onMounted, ref } from "vue";

const allStringOption = { value: "", label: "All" };
const allNumberOption = { value: 0, label: "All" };

export default defineComponent({
  name: "user-management-filter",
  components: {},
  props: {
    hideFilter: {
      type: Function,
      required: false,
      default: () => {},
    },
    onFilter: {
      type: Function,
      required: true,
    },
  },
  setup(props) {
    const companyStore = useCompanyStore();
    const loading = ref(false);
    const companyOptions = ref([allStringOption]);
    const roleOptions = [allNumberOption, ...rolesOptions];
    const statusOptions = [allNumberOption, ...userStatusOptions];
    const initialFilter: Filter.FilterForm = {
      email: "",
      status: 0,
      role: 0,
      companyId: "",
    };
    const filterForm = ref<Filter.FilterForm>({ ...initialFilter });

    onMounted(() => {
      if (isSystemAdmin()) {
        getCompanies();
      }
    });

    const getCompanies = async (): Promise<void> => {
      loading.value = true;
      companyStore.getCompanies({
        params: {
          page: 1,
          limit: 500,
        },
        callback: {
          onSuccess: (res: any) => {
            companyOptions.value = [
              allStringOption,
              ...res?.items?.map((item: Company.Info) => {
                return {
                  value: item?.id,
                  label: item?.name,
                };
              }),
            ];
          },
          onFinish: () => {
            loading.value = false;
          },
        },
      });
    };

    const hideFilter = () => {
      if (props?.hideFilter) {
        props?.hideFilter();
      }
    };

    const resetFilter = () => {
      filterForm.value = { ...initialFilter };
      apply();
    };

    const apply = () => {
      props.onFilter({
        email: filterForm.value?.email || null,
        status: filterForm.value?.status || null,
        role: filterForm.value?.role || null,
        companyId: filterForm.value?.companyId || null,
      });
    };

    return {
      loading,
      isSystemAdmin,
      filterForm,
      roleOptions,
      companyOptions,
      statusOptions,
      apply,
      hideFilter,
      resetFilter,
    };
  },
});
</script>
