<template>
  <div v-if="loading" class="text-center my-auto">
    <div class="spinner-border text-primary" role="status">
      <span class="sr-only">Loading...</span>
    </div>
  </div>
  <PageHeader
    title="Customer details"
    :breadcrumbs="[
      customerDetails?.company?.name || '',
      'Customer',
      'Customer details',
    ]"
  />
  <div
    class="h-auto w-11/12 mx-auto my-4 bg-card-background text-card-text rounded-xl flex flex-col items-center lg:w-4/5 lg:min-w-[1560px]"
  >
    <!--begin::Card header-->
    <div
      class="h-auto w-full p-4 flex flex-col gap-3 items-center border-b-[1px] border-dashed border-light-border"
    >
      <h1 class="text-lg font-bold self-start">
        Customer Overview
      </h1>
      <div class="h-auto w-full flex flex-row items-center justify-end gap-3 md:justify-start">
        <button
          class="bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover rounded-md px-4 py-2 font-semibold"
          @click="toggleEditCompany"
          v-if="isAdmin()"
        >
          Edit
        </button>
        <button
          type="button"
          class="bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover rounded-md px-4 py-2 font-semibold"
          @click="back"
        >
          Back
        </button>
      </div>
    </div>
    <!--end::Card header-->

    <div class="h-auto w-full p-4 flex flex-col gap-3 items-start md:flex-row">
      <div class="h-auto w-full flex flex-col gap-2 group">
        <h4 class="font-semibold">Customer/Company Name</h4>
        <p class="group-hover:text-primary">
          {{ customerDetails?.customerName }}
        </p>
      </div>
      <div class="h-auto w-full flex flex-col gap-2 group">
        <div>
          <h4 class="font-semibold">Notes</h4>
          <p class="group-hover:text-primary">
            {{ customerDetails?.notes }}
          </p>
        </div>
      </div>
    </div>
  </div>
  <div>
    <CustomerList />
  </div>
  <CustomerModal
    :isVisible="isModalVisible"
    :close="toggleEditCompany"
    ref="customerModal"
    :loadPage="getCustomerDetails"
  />
</template>

<script lang="ts">
import PageHeader from "@/components/PageHeader.vue";
import { isAdmin } from "@/services/JwtService";
import { useCustomerStore } from "@/stores/customer";
import { defineComponent, onMounted, ref, type Ref } from "vue";
import { useRoute, useRouter } from "vue-router";
import CustomerModal from "@/views/company/detail/CustomerModal.vue";
import CustomerList from "./ContactList.vue";

export default defineComponent({
  name: "customer-overview",
  components: {
    PageHeader,
    CustomerModal,
    CustomerList,
  },
  props: {
    isVisible: {
      type: Boolean,
      required: true,
    },
    close: {
      type: Function,
      required: true,
    },
  },
  setup() {
    const customerStore = useCustomerStore();
    const loading = ref<boolean>(false);
    const route = useRoute();
    const router = useRouter();
    const customerDetails = ref<Customer.Info>();
    const customerModal: Ref<any> = ref<typeof CustomerModal | null>(null);
    const customerId = route.params?.customerId?.toString() || "";
    const isModalVisible = ref(false);

    onMounted(() => {
      if (customerId) {
        getCustomerDetails();
      }
    });

    const toggleEditCompany = (): void => {
      customerModal?.value?.setId(customerId);
      isModalVisible.value = !isModalVisible.value;
    };

    const getCustomerDetails = async (): Promise<void> => {
      loading.value = true;
      customerStore.getCustomerDetails({
        id: customerId,
        callback: {
          onSuccess: (res: any) => {
            customerDetails.value = res;
          },
          onFinish: () => {
            loading.value = false;
          },
        },
      });
    };

    const back = () => {
      router.go(-1);
    };

    return {
      loading,
      customerModal,
      customerDetails,
      isModalVisible,
      back,
      toggleEditCompany,
      getCustomerDetails,
      isAdmin,
    };
  },
});
</script>
