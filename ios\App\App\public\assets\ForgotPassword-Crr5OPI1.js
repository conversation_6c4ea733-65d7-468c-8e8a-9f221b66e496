import{d as $,q as b,A as I,c as y,o as v,l as _,F as L,k as U,m as P,R as J,x as Q,a as d,n as W,u as R,a8 as D,G as j,e as X,_ as N,t as B,b as T,r as x,w as S,B as O}from"./index-CGNRhvz7.js";import{E as Y,F as Z,a as ee}from"./vee-validate-CesDBK0n.js";import{c as te,a as ne}from"./index.esm-DXW765zG.js";import{_ as se}from"./well-logo-diufKTHF.js";const oe={style:{display:"flex","align-items":"center"}},ae=["type","inputmode","placeholder","disabled","maxlength","value"],le={key:0},ie=["innerHTML"],ue=$({__name:"single-otp-input",props:{inputType:{default:"tel"},inputmode:{default:"numeric"},value:{default:""},separator:{default:""},focus:{type:Boolean,default:!1},inputClasses:{default:""},conditionalClass:{default:""},shouldAutoFocus:{type:Boolean,default:!1},isLastChild:{type:Boolean,default:!1},placeholder:{default:""},isDisabled:{type:Boolean,default:!1}},emits:["on-change","on-keydown","on-paste","on-focus","on-blur"],setup(e,{emit:n}){const p=e,i=n,u=b(p.value||""),l=b(null),o=t=>{const r=t.target.value;return r&&r.trim().length>1?(t.clipboardData={getData:()=>r.trim()},i("on-paste",t)):i("on-change",r)},m=t=>t>=65&&t<=90,c=t=>t>=48&&t<=57||t>=96&&t<=105,f=t=>{p.isDisabled&&t.preventDefault();const r=t||window.event,h=r.which?r.which:r.keyCode;c(h)||p.inputType==="letter-numeric"&&m(h)||[8,9,13,37,39,46,86].includes(h)?i("on-keydown",t):r.preventDefault()},w=t=>i("on-paste",t),F=()=>(l.value.select(),i("on-focus")),a=()=>i("on-blur"),k=J(()=>["letter-numeric","number"].includes(p.inputType)?"text":p.inputType);return I(()=>p.value,(t,r)=>{t!==r&&(u.value=t)}),I(()=>p.focus,(t,r)=>{r!==t&&l.value&&p.focus&&(l.value.focus(),l.value.select())}),Q(()=>{l.value&&p.focus&&p.shouldAutoFocus&&(l.value.focus(),l.value.select())}),(t,r)=>(v(),y("div",oe,[d("input",{"data-test":"single-input",type:k.value,inputmode:t.inputmode,placeholder:t.placeholder,disabled:t.isDisabled,ref_key:"input",ref:l,min:"0",max:"9",maxlength:t.isLastChild?1:void 0,pattern:"[0-9]",value:u.value,class:W([t.inputClasses,t.conditionalClass,{"is-complete":u.value}]),onInput:o,onKeydown:f,onPaste:w,onFocus:F,onBlur:a},null,42,ae),!t.isLastChild&&t.separator?(v(),y("span",le,[d("span",{innerHTML:t.separator},null,8,ie)])):_("",!0)]))}}),de={style:{display:"flex"},class:"otp-input-container"},re={key:0,autocomplete:"off",name:"hidden",type:"text",style:{display:"none"}},ce=8,me=37,pe=39,fe=46,ve=$({__name:"vue3-otp-input",props:{value:{default:""},numInputs:{default:4},separator:{default:""},inputClasses:{default:""},conditionalClass:{default:()=>[]},inputType:{},inputmode:{default:"text"},shouldAutoFocus:{type:Boolean,default:!1},placeholder:{default:()=>[]},isDisabled:{type:Boolean,default:!1},shouldFocusOrder:{type:Boolean}},emits:["update:value","on-change","on-complete"],setup(e,{expose:n,emit:p}){const i=e,u=p,l=b(0),o=b([]),m=b([]);I(()=>i.value,s=>{if(s.length===i.numInputs||o.value.length===0){const g=s.split("");o.value=g}},{immediate:!0});const c=s=>{l.value=s},f=()=>{l.value=-1},w=()=>o.value.join("").length===i.numInputs?(u("update:value",o.value.join("")),u("on-complete",o.value.join(""))):"Wait until the user enters the required number of characters",F=s=>{l.value=Math.max(Math.min(i.numInputs-1,s),0)},a=()=>{F(l.value+1)},k=()=>{F(l.value-1)},t=s=>{m.value=Object.assign([],o.value),o.value[l.value]=s.toString(),m.value.join("")!==o.value.join("")&&(u("update:value",o.value.join("")),u("on-change",o.value.join("")),w())},r=s=>{s.preventDefault();const g=s.clipboardData.getData("text/plain").slice(0,i.numInputs-l.value).split("");if(i.inputType==="number"&&!g.join("").match(/^\d+$/)||i.inputType==="letter-numeric"&&!g.join("").match(/^\w+$/))return"Invalid pasted data";const V=o.value.slice(0,l.value).concat(g);return V.slice(0,i.numInputs).forEach(function(C,E){o.value[E]=C}),F(V.slice(0,i.numInputs).length),w()},h=s=>{t(s),a()},G=()=>{o.value.length>0&&(u("update:value",""),u("on-change","")),o.value=[],l.value=0},H=s=>{const g=s.split("");g.length===i.numInputs&&(o.value=g,u("update:value",o.value.join("")),u("on-complete",o.value.join("")))},K=(s,g)=>{switch(s.keyCode){case ce:s.preventDefault(),t(""),k();break;case fe:s.preventDefault(),t("");break;case me:s.preventDefault(),k();break;case pe:s.preventDefault(),a();break;default:z(g);break}},z=s=>{i.shouldFocusOrder&&setTimeout(()=>{const g=o.value.join("").length;s-g>=0&&(l.value=g,o.value[s]="")},100)};return n({clearInput:G,fillInput:H}),(s,g)=>(v(),y("div",de,[s.inputType==="password"?(v(),y("input",re)):_("",!0),(v(!0),y(L,null,U(s.numInputs,(V,C)=>{var E,A;return v(),P(ue,{key:C,focus:l.value===C,value:o.value[C],separator:s.separator,"input-type":s.inputType,inputmode:s.inputmode,"input-classes":s.inputClasses,conditionalClass:(E=s.conditionalClass)==null?void 0:E[C],"is-last-child":C===s.numInputs-1,"should-auto-focus":s.shouldAutoFocus,placeholder:(A=s.placeholder)==null?void 0:A[C],"is-disabled":s.isDisabled,onOnChange:h,onOnKeydown:M=>K(M,C),onOnPaste:r,onOnFocus:M=>c(C),onOnBlur:f},null,8,["focus","value","separator","input-type","inputmode","input-classes","conditionalClass","is-last-child","should-auto-focus","placeholder","is-disabled","onOnKeydown","onOnFocus"])}),128))]))}}),ge=$({name:"otp-verification",components:{Vue3OtpInput:ve},props:{email:String,userForgot:{type:Object,required:!1}},setup(e){const n=R(),p=X(),i=null,u=b(""),l=b(!0),o=b(""),m=b(!1);return{startCountdown:()=>{n.forgotPassword({email:(e==null?void 0:e.email)||"",callback:{onSuccess:()=>{l.value=!0},onFinish:()=>{m.value=!1},onFailure:a=>{var k,t,r,h;D.fire({text:j[(t=(k=a==null?void 0:a.response)==null?void 0:k.data)==null?void 0:t.errorCode]||((h=(r=a==null?void 0:a.response)==null?void 0:r.data)==null?void 0:h.message)||(a==null?void 0:a.message)||"Sorry, looks like there are some errors detected, please try again.",icon:"error",buttonsStyling:!1,confirmButtonText:"Got it!",heightAuto:!1,customClass:{confirmButton:"btn fw-semibold btn-light-danger"}})}}})},onCountdownEnd:()=>{l.value=!1},handleOnComplete:a=>{},onVerify:()=>{u.value.length!==6?o.value="Please type the code":(o.value="",m.value=!0,n.validateOTP({params:{email:(e==null?void 0:e.email)||"",otp:u.value},callback:{onSuccess:()=>{var a;p.push({path:"/reset-password",query:{email:e==null?void 0:e.email,otp:u.value,user_name:(a=e==null?void 0:e.userForgot)==null?void 0:a.name}})},onFinish:()=>{m.value=!1},onFailure:a=>{var k,t,r,h;D.fire({text:j[(t=(k=a==null?void 0:a.response)==null?void 0:k.data)==null?void 0:t.errorCode]||((h=(r=a==null?void 0:a.response)==null?void 0:r.data)==null?void 0:h.message)||(a==null?void 0:a.message)||"Sorry, looks like there are some errors detected, please try again.",icon:"error",buttonsStyling:!1,confirmButtonText:"Got it!",heightAuto:!1,customClass:{confirmButton:"btn fw-semibold btn-light-danger"}})}}}))},error:o,bindValue:u,otpInput:i,counting:l,loading:m}}}),ye="/media/OTP-verification.svg",be={class:"d-flex flex-column gap-10"},he={class:"text-gray-400 text-center fw-semibold fs-3"},we={class:"fv-row"},ke={key:0,class:"error-message mt-3"},Ce={class:"text-gray-400 text-center fw-semibold fs-3"},Fe={class:"d-flex flex-wrap justify-content-end pb-lg-0"},_e=["disabled"],xe={key:0,class:"indicator-label"},Te={key:1,class:"indicator-progress"};function Oe(e,n,p,i,u,l){const o=x("vue3-otp-input"),m=x("vue-countdown"),c=x("router-link");return v(),y(L,null,[n[8]||(n[8]=d("div",{class:"text-center mb-10"},[d("img",{src:ye,width:100})],-1)),d("div",be,[n[7]||(n[7]=d("h1",{class:"text-dark text-center"},"OTP Verification",-1)),d("div",he,B(`Please enter the 6 digit code sent to ${e.email} within 3
      minutes. Remember to check your email!`),1),d("div",we,[n[4]||(n[4]=d("div",{class:"text-dark fw-bold mb-3"},"Type your 6 digit security code",-1)),T(o,{ref:"otpInput","input-classes":"otp-input",inputType:"number","num-inputs":6,value:e.bindValue,"onUpdate:value":[n[0]||(n[0]=f=>e.bindValue=f),n[1]||(n[1]=f=>e.bindValue=f)],"should-auto-focus":!0,"should-focus-order":!0,onOnComplete:e.handleOnComplete},null,8,["value","onOnComplete"]),e.error?(v(),y("div",ke,B(e.error),1)):_("",!0)]),d("div",Ce,[e.counting?(v(),P(m,{key:0,"auto-start":"",time:180*1e3,onEnd:e.onCountdownEnd},{default:S(({totalSeconds:f})=>[O(" Resend code in "+B(f)+" s ",1)]),_:1},8,["onEnd"])):e.counting===!1?(v(),y("span",{key:1,class:"link-primary fs-3 fw-bold resend-code",onClick:n[2]||(n[2]=(...f)=>e.startCountdown&&e.startCountdown(...f))}," Resend code ")):_("",!0)]),d("div",Fe,[T(c,{to:"/sign-in",class:"btn btn-lg btn-light-primary fw-bold"},{default:S(()=>n[5]||(n[5]=[O("Cancel")])),_:1}),d("button",{class:"btn btn-sm btn-primary ms-5",type:"submit",disabled:e.loading,onClick:n[3]||(n[3]=(...f)=>e.onVerify&&e.onVerify(...f))},[e.loading?_("",!0):(v(),y("span",xe," Verify Code ")),e.loading?(v(),y("span",Te,n[6]||(n[6]=[O(" Please wait... "),d("span",{class:"spinner-border spinner-border-sm align-middle ms-2"},null,-1)]))):_("",!0)],8,_e)])])],64)}const Ee=N(ge,[["render",Oe]]);var q=(e=>(e[e.ENTER_EMAIL=1]="ENTER_EMAIL",e[e.OTP=2]="OTP",e))(q||{});const Pe=$({name:"forgot-password",components:{Field:ee,VForm:Z,ErrorMessage:Y,OTPVerification:Ee},setup(){const e=R(),n=b(1),p=b(""),i=b(),u=b(!1),l=te().shape({email:ne().email().required().label("Email")});return{onSubmitForgotPassword:async m=>{u.value=!0,p.value=m==null?void 0:m.email,e.forgotPassword({email:(m==null?void 0:m.email)||"",callback:{onSuccess:c=>{c&&(i.value=c),n.value=2},onFinish:()=>{u.value=!1},onFailure:c=>{var f,w,F,a;D.fire({text:j[(w=(f=c==null?void 0:c.response)==null?void 0:f.data)==null?void 0:w.errorCode]||((a=(F=c==null?void 0:c.response)==null?void 0:F.data)==null?void 0:a.message)||(c==null?void 0:c.message)||"Sorry, looks like there are some errors detected, please try again.",icon:"error",buttonsStyling:!1,confirmButtonText:"Got it!",heightAuto:!1,customClass:{confirmButton:"px-2 py-1 bg-light-danger font-semibold "}})}}})},forgotPassword:l,loading:u,currentStep:n,STEP:q,email:p,userForgot:i}}}),Se={class:"h-auto w-4/5 rounded-3xl mx-auto py-6 bg-white rounded-3 overflow-x-scroll align-self-center flex flex-col justify-center items-center"},$e={class:"flex flex-col mb-8 mx-3 h-auto w-full"},Ve={class:"text-danger mt-[0.3rem]"},Be={class:"w-full flex flex-row justify-end items-center gap-2"},Ie=["disabled"],De={key:0,class:"text-white font-semibold"},je={key:1,class:"indicator-progress"};function Ae(e,n,p,i,u,l){const o=x("Field"),m=x("ErrorMessage"),c=x("router-link"),f=x("VForm"),w=x("OTPVerification");return v(),y("div",Se,[e.currentStep===e.STEP.ENTER_EMAIL?(v(),P(f,{key:0,class:"flex flex-col items-center w-4/5",onSubmit:e.onSubmitForgotPassword,id:"kt_forgot_pass_form","validation-schema":e.forgotPassword},{default:S(()=>[n[3]||(n[3]=d("img",{src:se},null,-1)),n[4]||(n[4]=d("div",{class:"text-center mb-10"},[d("h1",{class:"text-dark my-2 font-bold"},"Forgot Password ?"),d("div",{class:"text-gray-400 font-bold text-xs"}," Enter your email to reset your password. ")],-1)),d("div",$e,[n[0]||(n[0]=d("label",{class:"form-label tracking-wide font-bold text-dark"},"Email",-1)),T(o,{class:"rounded-lg bg-grey-300 pl-3",type:"email",placeholder:"Enter email",name:"email",autocomplete:"off"}),d("div",Ve,[T(m,{name:"email"})])]),d("div",Be,[T(c,{to:"/sign-in",class:"font-bold text-primary"},{default:S(()=>n[1]||(n[1]=[O("Cancel")])),_:1}),d("button",{class:"bg-primary rounded-md px-2 py-1",type:"submit",disabled:e.loading},[e.loading?_("",!0):(v(),y("span",De," Submit ")),e.loading?(v(),y("span",je,n[2]||(n[2]=[O(" Please wait... "),d("span",{class:"spinner-border spinner-border-sm align-middle ms-2"},null,-1)]))):_("",!0)],8,Ie)])]),_:1},8,["onSubmit","validation-schema"])):e.currentStep===e.STEP.OTP?(v(),P(w,{key:1,email:e.email,userForgot:e.userForgot},null,8,["email","userForgot"])):_("",!0)])}const qe=N(Pe,[["render",Ae]]);export{qe as default};
