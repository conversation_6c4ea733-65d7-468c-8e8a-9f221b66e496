import{P as C}from"./PageHeader-3hadTn26.js";import{u as f}from"./company-oDyd0dWV.js";import{d as D,q as l,j as g,x as v,_ as b,c,l as d,b as _,m as k,a as u,F as B,r as y,o as p}from"./index-CGNRhvz7.js";import{C as P}from"./CompanyDetail-DrR_iaiM.js";import"./handleFailure-DtTpu7r3.js";import"./CompanyModal-Cba-MY3X.js";import"./SvgIcon-CMhyaXWN.js";import"./TablePagination-BmVxunEG.js";import"./d-left-arrow-079-B3YbfCzd.js";import"./TableHeader-C1CWTWQa.js";import"./table-bhK9qpe4.js";import"./date-CvSHk5ED.js";import"./customer-C9SausZF.js";import"./CustomerModal-B1iI3o6f.js";import"./user-KFDu8xJF.js";import"./index.esm-DXW765zG.js";import"./AssignUserModal-B-x10c2w.js";import"./UserModal-Ck8RxOB2.js";import"./validator-6laVLK0J.js";const S=D({name:"company-overview",components:{CompanyDetail:P,PageHeader:C},setup(){var t,n;const e=l(!1),o=f(),i=g(),s=l(),a=((n=(t=i.params)==null?void 0:t.id)==null?void 0:n.toString())||"";v(()=>{a&&m()});const m=async()=>{e.value=!0,o.getCompanyById({id:a,callback:{onSuccess:r=>{s.value=r},onFinish:()=>{e.value=!1}}})};return{loading:e,companyId:a,companyDetail:s,getCompanyDetails:m}}}),$={key:0,class:"text-center my-auto"};function F(e,o,i,s,a,m){var r;const t=y("PageHeader"),n=y("CompanyDetail");return p(),c(B,null,[e.loading?(p(),c("div",$,o[0]||(o[0]=[u("div",{class:"spinner-border text-primary",role:"status"},[u("span",{class:"sr-only"},"Loading...")],-1)]))):d("",!0),_(t,{title:"Companies",breadcrumbs:["Companies",((r=e.companyDetail)==null?void 0:r.name)||""]},null,8,["breadcrumbs"]),!e.loading&&e.companyDetail?(p(),k(n,{key:1,companyDetail:e.companyDetail,reloadCompanyData:e.getCompanyDetails},null,8,["companyDetail","reloadCompanyData"])):d("",!0)],64)}const Q=b(S,[["render",F]]);export{Q as default};
