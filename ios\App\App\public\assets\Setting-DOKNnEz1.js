import{S as O}from"./SvgIcon-CMhyaXWN.js";import{d as H,q as g,L as F,J as R,x as z,A as j,_ as Y,m as L,o as v,w,a as s,b as a,c as $,l as E,r as p,B,p as W,t as I,E as Z,H as x,n as J,I as ee,F as M,k as G,U as de,h as ce}from"./index-CGNRhvz7.js";import{T as te}from"./TablePagination-BmVxunEG.js";import{T as se}from"./TableHeader-C1CWTWQa.js";import{S as q,a as oe}from"./table-bhK9qpe4.js";import{f as ne}from"./date-CvSHk5ED.js";import{n as le}from"./numberFormatter-C7uP7NWj.js";import{u as ae,a as ie}from"./product-Dl6kto8A.js";import{F as ue}from"./Filter-cvywqNLp.js";import{u as K}from"./company-oDyd0dWV.js";import"./d-left-arrow-079-B3YbfCzd.js";import"./handleFailure-DtTpu7r3.js";var re=(e=>(e[e.Service=1]="Service",e[e.Engineer=2]="Engineer",e))(re||{});const me=H({name:"costs-modal",components:{SvgIcon:O},props:{loadPage:{type:Function,required:!1},type:{type:Number,required:!0},label:{type:String,required:!0}},setup(e){var h;const t=K(),S=ae(),y=g(!1),_={companyId:F()?null:(h=R.getUserInfo())==null?void 0:h.companyId,name:"",description:"",cost:null},c=g({..._}),m=g(null),f=g(!1),d=g(!1),u=g([]),n=g("");z(()=>{F()&&C()}),j(n,o=>{o!==""&&r()}),j(y,o=>{var i;o===!1&&(n.value="",U(),(i=m==null?void 0:m.value)==null||i.resetFields())});const C=async()=>{d.value=!0,t.getCompanies({params:{page:1,limit:500},callback:{onSuccess:o=>{const i=o==null?void 0:o.items.map(b=>({label:b==null?void 0:b.name,value:b==null?void 0:b.id}));u.value=[...i]},onFinish:()=>{d.value=!1}}})},r=async()=>{S.getCostSettingDetails({id:n.value,callback:{onSuccess:o=>{var i;c.value={...o,companyId:(i=o==null?void 0:o.company)==null?void 0:i.id}}}})},P=async o=>{f.value=!0,S.updateCostSetting({id:n.value,params:{...o,type:e.type},callback:{onSuccess:i=>{e!=null&&e.loadPage&&(e==null||e.loadPage()),k()},onFinish:i=>{f.value=!1}}})},A=async o=>{f.value=!0,S.createCostSetting({params:{...o,type:e.type},callback:{onSuccess:i=>{e!=null&&e.loadPage&&(e==null||e.loadPage()),k()},onFinish:i=>{f.value=!1}}})},N=()=>{y.value=!0},k=()=>{y.value=!1},D=o=>{n.value=o.toString()},l={name:[{required:!0,message:"Please type Description",trigger:"blur"}],description:[{required:!0,message:"Please type Description",trigger:"blur"}],cost:[{required:!0,message:"Please type Price",trigger:"blur"}]},V=F()?{...l,companyId:[{required:!0,message:"Please select Company",trigger:["change","blur"]}]}:l,T=()=>{m.value&&m.value.validate(o=>{var i;if(o){const b={...c==null?void 0:c.value,cost:Number((i=c==null?void 0:c.value)==null?void 0:i.cost)};n!=null&&n.value?P(b):A(b)}})},U=()=>{c.value={..._}};return{id:n,modal:y,rules:V,loading:f,targetData:c,formRef:m,isSystemAdmin:F,loadingCompany:d,companyList:u,show:N,hide:k,submit:T,setId:D,reset:U}}}),pe={class:"d-flex align-items-center w-100"},ge={class:"modal-title"},be={key:0,class:"row mb-3"},fe={class:"col-12 fv-row d-flex flex-column justify-content-stretch"},ve={class:"row mb-3"},ye={class:"col-12 fv-row d-flex flex-column justify-content-stretch"},_e={class:"row mb-3"},he={class:"col-12 fv-row d-flex flex-column justify-content-stretch"},we={class:"row mb-3"},$e={class:"col-12 fv-row d-flex flex-column justify-content-stretch"},Se={class:"modal-footer d-flex justify-content-center align-items-center"},Ce=["disabled"],Pe=["data-kt-indicator","disabled"],Fe={key:0,class:"indicator-label"},ke={key:1,class:"indicator-progress"};function Ie(e,t,S,y,_,c){const m=p("SvgIcon"),f=p("el-select-v2"),d=p("el-form-item"),u=p("el-input"),n=p("el-form"),C=p("el-dialog");return v(),L(C,{modelValue:e.modal,"onUpdate:modelValue":t[6]||(t[6]=r=>e.modal=r),"show-close":!1,width:"500","align-center":""},{header:w(()=>[s("div",pe,[s("h3",ge,I(e.id?`Edit ${e.label}`:`Add ${e.label}`),1),s("span",{class:"cursor-pointer ms-auto",onClick:t[0]||(t[0]=(...r)=>e.hide&&e.hide(...r))},[a(m,{icon:"closeModalIcon"})])])]),default:w(()=>[s("div",null,[a(n,{id:"service_costs_form",onSubmit:W(e.submit,["prevent"]),model:e.targetData,rules:e.rules,ref:"formRef",class:"form"},{default:w(()=>[e.isSystemAdmin()?(v(),$("div",be,[s("div",fe,[t[7]||(t[7]=s("label",{class:"d-flex align-items-center fs-6 fw-semibold mb-2 text-break required"},"Company ",-1)),a(d,{prop:"companyId"},{default:w(()=>[a(f,{class:"w-100",modelValue:e.targetData.companyId,"onUpdate:modelValue":t[1]||(t[1]=r=>e.targetData.companyId=r),options:e.companyList,placeholder:"Search Company",filterable:"",name:"companyId",loading:e.loadingCompany},null,8,["modelValue","options","loading"])]),_:1})])])):E("",!0),s("div",ve,[s("div",ye,[t[8]||(t[8]=s("label",{class:"d-flex align-items-center fs-6 fw-semibold mb-2 text-break required"},"Name ",-1)),a(d,{prop:"name",class:"mt-auto"},{default:w(()=>[a(u,{class:"w-100",modelValue:e.targetData.name,"onUpdate:modelValue":t[2]||(t[2]=r=>e.targetData.name=r),placeholder:"",name:"name"},null,8,["modelValue"])]),_:1})])]),s("div",_e,[s("div",he,[t[9]||(t[9]=s("label",{class:"d-flex align-items-center fs-6 fw-semibold mb-2 text-break required"},"Description ",-1)),a(d,{prop:"description",class:"mt-auto"},{default:w(()=>[a(u,{type:"textarea",rows:5,class:"w-100",modelValue:e.targetData.description,"onUpdate:modelValue":t[3]||(t[3]=r=>e.targetData.description=r),placeholder:"",name:"description"},null,8,["modelValue"])]),_:1})])]),s("div",we,[s("div",$e,[t[10]||(t[10]=s("label",{class:"d-flex align-items-center fs-6 fw-semibold mb-2 text-break required"},"Price ",-1)),a(d,{prop:"cost",class:"mt-auto"},{default:w(()=>[a(u,{type:"number",class:"w-100",controls:!1,step:"any",modelValue:e.targetData.cost,"onUpdate:modelValue":t[4]||(t[4]=r=>e.targetData.cost=r),placeholder:"",name:"cost"},null,8,["modelValue"])]),_:1})])]),s("div",Se,[s("button",{type:"button",id:"kt_modal_add_customer_cancel",class:"btn text-gray-700 btn-sm me-3 btn-blue",onClick:t[5]||(t[5]=(...r)=>e.hide&&e.hide(...r)),disabled:e.loading}," Discard ",8,Ce),s("button",{"data-kt-indicator":e.loading?"on":null,class:"btn btn-sm btn-primary",type:"submit",disabled:e.loading},[e.loading?E("",!0):(v(),$("span",Fe," Save ")),e.loading?(v(),$("span",ke,t[11]||(t[11]=[B(" Please wait... "),s("span",{class:"spinner-border spinner-border-sm align-middle ms-2"},null,-1)]))):E("",!0)],8,Pe)])]),_:1},8,["onSubmit","model","rules"])])]),_:1},8,["modelValue"])}const De=Y(me,[["render",Ie]]),Ve=H({name:"costs-page",components:{Filter:ue,SvgIcon:O,TablePagination:te,CostsModal:De,TableHeader:se},props:{type:{type:Number,required:!0},label:{type:String,required:!0}},setup(e){const t=ae(),S=g([]),y=g(0),_=g(0),c=g(1),m=g(!1),f=g(""),d=g(!1),u=g(null),n=g({}),C=g({sortDirection:oe.ASC,sortBy:q.Name}),r=[{label:"NAME",sortBy:q.Name,class:"min-w-150px"},{label:"COMPANY",class:"min-w-150px",display:F()},{label:"PRICE",sortBy:q.Cost},{label:"CREATED DATE",class:"min-w-150px",sortBy:q.CreatedAt},{label:"ACTIONS"}];z(()=>{P()}),j(c,()=>{P()});const P=async()=>{var o,i;m.value=!0,t.getCostSettings({params:{companyId:F()?null:(i=(o=R)==null?void 0:o.getUserInfo())==null?void 0:i.companyId,keyword:f.value.trim(),type:e.type,page:c.value,limit:10,...C.value,...n.value},callback:{onSuccess:b=>{S.value=b==null?void 0:b.items,y.value=b==null?void 0:b.totalPage,_.value=b==null?void 0:b.total,c.value=b==null?void 0:b.page},onFinish:b=>{m.value=!1}}})},A=o=>{C.value={...o},P()},N=o=>{c.value=o},k=()=>{var o;(o=u==null?void 0:u.value)==null||o.show()},D=o=>{var i,b;(i=u==null?void 0:u.value)==null||i.setId(o),(b=u==null?void 0:u.value)==null||b.show()},l=o=>{Z.deletionAlert({onConfirmed:()=>{V(o)}})},V=async o=>{m.value=!0,t.deleteCostSetting({id:o,callback:{onSuccess:i=>{P()},onFinish:i=>{m.value=!1}}})};return{onSort:A,onFilter:o=>{n.value={...o},c.value!==1?c.value=1:P()},toggleFilter:o=>{d.value=o},searchCostSettings:()=>{c.value!==1?c.value=1:P()},getCostSettings:P,deleteServiceCost:l,pageChange:N,numberWithCommas:le,formatDate:ne,toggleNewServiceCosts:k,toggleEditServiceCosts:D,isSystemAdmin:F,search:f,loading:m,costList:S,pageCount:y,currentPage:c,totalElements:_,isShowFilter:d,costsModal:u,sortParams:C,tableHeader:r}}}),Ee={class:"card h-100 my-8"},Ae={class:"card-header py-4 d-flex flex-wrap align-items-center"},Ne={class:"mb-0 me-4 text-gray-900"},Te={class:"card-toolbar gap-3 ms-auto"},qe={class:"d-flex align-items-center"},Be={class:"svg-icon svg-icon-2"},Ue={class:"svg-icon svg-icon-2"},je={class:"svg-icon svg-icon-2"},Le={class:"btn btn-flex btn-sm btn-success btn-export-success"},Me={class:"svg-icon svg-icon-1"},He={class:"card-body"},Ye={key:0,class:"text-center"},Oe={key:2},Re={class:"table-responsive"},ze={class:"table table-row-bordered align-middle gs-0 gy-3"},We={class:"fw-semibold text-gray-400 fs-6"},Je={class:"text-gray-900 fw-semibold fs-5"},Ge={key:0,class:"text-gray-900 fw-semibold fs-5"},Ke={class:"text-gray-600 fw-semibold fs-5"},Qe={class:"text-gray-600 fw-semibold fs-5"},Xe={class:"d-flex align-items-center"},Ze=["onClick"],xe={class:"svg-icon svg-icon-3"},et=["onClick"],tt={class:"svg-icon svg-icon-3 text-danger"},st={class:"d-flex flex-wrap align-items-center mt-5"},ot={class:"text-gray-700 fw-semibold fs-6 me-auto"};function nt(e,t,S,y,_,c){var D;const m=p("SvgIcon"),f=p("el-icon"),d=p("el-input"),u=p("el-form-item"),n=p("el-form"),C=p("inline-svg"),r=p("Filter"),P=p("el-empty"),A=p("TableHeader"),N=p("TablePagination"),k=p("CostsModal");return v(),$(M,null,[s("div",Ee,[s("div",Ae,[s("h1",Ne,I(e.label),1),s("div",Te,[a(n,{onSubmit:W(e.searchCostSettings,["prevent"])},{default:w(()=>[s("div",qe,[a(u,{class:"mb-0"},{default:w(()=>[a(d,{class:"w-250px",placeholder:"Search",modelValue:e.search,"onUpdate:modelValue":t[0]||(t[0]=l=>e.search=l),name:"search",size:"large"},{prefix:w(()=>[a(f,{class:"el-input__icon"},{default:w(()=>[s("span",Be,[a(m,{icon:"searchIcon"})])]),_:1})]),_:1},8,["modelValue"])]),_:1})])]),_:1},8,["onSubmit"]),s("button",{class:J(["btn btn-flex btn-sm",e.isShowFilter?"btn-primary":"btn-blue"]),onClick:t[1]||(t[1]=()=>e.toggleFilter(!e.isShowFilter))},[s("span",Ue,[a(C,{src:"/media/icons/duotune/general/gen031.svg"})]),t[3]||(t[3]=B(" Filter "))],2),s("button",{class:"btn btn-flex btn-sm btn-primary",onClick:t[2]||(t[2]=(...l)=>e.toggleNewServiceCosts&&e.toggleNewServiceCosts(...l))},[s("span",je,[a(C,{src:"media/icons/duotune/arrows/arr075.svg"})]),t[4]||(t[4]=B(" New "))]),s("button",Le,[s("span",Me,[a(m,{icon:"iconExport"})]),t[5]||(t[5]=B(" Export "))])]),x(a(r,{hideFilter:()=>e.toggleFilter(!1),onFilter:e.onFilter},null,8,["hideFilter","onFilter"]),[[ee,e.isShowFilter]])]),s("div",He,[e.loading?(v(),$("div",Ye,t[6]||(t[6]=[s("div",{class:"spinner-border text-primary",role:"status"},[s("span",{class:"sr-only"},"Loading...")],-1)]))):e.costList.length===0?(v(),L(P,{key:1,description:"No Data"})):(v(),$("div",Oe,[s("div",Re,[s("table",ze,[s("thead",null,[s("tr",We,[a(A,{headers:e.tableHeader,sortBy:e.sortParams.sortBy,sortDirection:e.sortParams.sortDirection,onSort:e.onSort},null,8,["headers","sortBy","sortDirection","onSort"])])]),s("tbody",null,[(v(!0),$(M,null,G(e.costList,l=>{var V;return v(),$("tr",{key:l==null?void 0:l.id},[s("td",Je,I(l==null?void 0:l.name),1),e.isSystemAdmin()?(v(),$("td",Ge,I((V=l==null?void 0:l.company)==null?void 0:V.name),1)):E("",!0),s("td",Ke,I(`$${e.numberWithCommas(l==null?void 0:l.cost)}`),1),s("td",Qe,I(e.formatDate(l==null?void 0:l.createdAt,"MMM DD, YYYY")),1),s("td",null,[s("div",Xe,[s("button",{class:"btn btn-icon btn-sm btn-blue me-3",onClick:T=>e.toggleEditServiceCosts(l==null?void 0:l.id)},[s("span",xe,[a(m,{icon:"newReportIcon"})])],8,Ze),s("button",{class:"btn btn-icon btn-sm btn-blue me-3",onClick:T=>e.deleteServiceCost(l==null?void 0:l.id)},[s("span",tt,[a(m,{icon:"trashIcon"})])],8,et)])])])}),128))])])]),s("div",st,[s("div",ot,I(`Showing ${(e.currentPage-1)*10+1} to ${(D=e.costList)==null?void 0:D.length} of ${e.totalElements} entries`),1),e.pageCount>=1?(v(),L(N,{key:0,"total-pages":e.pageCount,total:e.totalElements,"per-page":10,"current-page":e.currentPage,onPageChange:e.pageChange},null,8,["total-pages","total","current-page","onPageChange"])):E("",!0)])]))])]),a(k,{ref:"costsModal",loadPage:e.getCostSettings,label:e.label,type:e.type},null,8,["loadPage","label","type"])],64)}const lt=Y(Ve,[["render",nt]]),Q={value:"",label:"All"},at=H({name:"settings-filter",components:{},props:{hideFilter:{type:Function,required:!1,default:()=>{}},onFilter:{type:Function,required:!0}},setup(e){const t=K(),S=g([Q]),y=g({companyId:""});z(()=>{F()&&_()});const _=async()=>{t.getCompanies({params:{page:1,limit:500},callback:{onSuccess:d=>{const u=d==null?void 0:d.items.map(n=>({label:n==null?void 0:n.name,value:n==null?void 0:n.id}));S.value=[Q,...u]}}})},c=()=>{e!=null&&e.hideFilter&&(e==null||e.hideFilter())},m=()=>{y.value={companyId:""},f()},f=()=>{var d,u,n,C,r;e.onFilter({priceFrom:Number((d=y.value)==null?void 0:d.priceFrom)||null,priceTo:Number((u=y.value)==null?void 0:u.priceTo)||null,companyId:F()?((n=y.value)==null?void 0:n.companyId)||null:(r=(C=R)==null?void 0:C.getUserInfo())==null?void 0:r.companyId})};return{UserType:de,filterForm:y,companyOptions:S,isSystemAdmin:F,apply:f,hideFilter:c,resetFilter:m}}}),it={class:"card h-100 w-100 my-8 shadow-sm"},rt={class:"card-header align-items-center"},dt={class:"card-toolbar gap-3 ms-auto"},ct={class:"card-body"},ut={class:"row g-9"},mt={class:"col-md-4 fv-row d-flex flex-column justify-content-stretch"},pt={class:"col-md-4 fv-row d-flex flex-column justify-content-stretch"},gt={key:0,class:"col-md-4 fv-row d-flex flex-column justify-content-stretch"};function bt(e,t,S,y,_,c){const m=p("el-input"),f=p("el-form-item"),d=p("el-select-v2"),u=p("el-form");return v(),$("div",it,[s("div",rt,[t[6]||(t[6]=s("div",null,[s("h5",{class:"mb-0 me-4 text-gray-900"},"Filter")],-1)),s("div",dt,[s("button",{class:"btn btn-active-light btn-color-muted fw-semibold btn-sm",onClick:t[0]||(t[0]=(...n)=>e.resetFilter&&e.resetFilter(...n))}," Reset "),s("button",{class:"btn btn-light-primary btn-sm",onClick:t[1]||(t[1]=(...n)=>e.hideFilter&&e.hideFilter(...n))}," Close "),s("button",{class:"btn btn-sm btn-primary",type:"button",onClick:t[2]||(t[2]=(...n)=>e.apply&&e.apply(...n))}," Apply ")])]),s("div",ct,[a(u,{class:"form new-report-form",model:e.filterForm,onSubmit:W(e.apply,["prevent"])},{default:w(()=>[s("div",ut,[s("div",mt,[t[7]||(t[7]=s("label",{class:"d-flex align-items-center fs-6 fw-semibold mb-2 text-break"},"Price from",-1)),a(f,{prop:"priceFrom"},{default:w(()=>[a(m,{type:"number",controls:!1,step:"any",modelValue:e.filterForm.priceFrom,"onUpdate:modelValue":t[3]||(t[3]=n=>e.filterForm.priceFrom=n),placeholder:"Price from",name:"priceFrom"},null,8,["modelValue"])]),_:1})]),s("div",pt,[t[8]||(t[8]=s("label",{class:"d-flex align-items-center fs-6 fw-semibold mb-2 text-break"},"Price to",-1)),a(f,{prop:"priceTo"},{default:w(()=>[a(m,{type:"number",controls:!1,step:"any",modelValue:e.filterForm.priceTo,"onUpdate:modelValue":t[4]||(t[4]=n=>e.filterForm.priceTo=n),placeholder:"Price to",name:"priceTo"},null,8,["modelValue"])]),_:1})]),e.isSystemAdmin()?(v(),$("div",gt,[t[9]||(t[9]=s("label",{class:"d-flex align-items-center fs-6 fw-semibold mb-2 text-break"}," Company ",-1)),a(f,{prop:"companyId"},{default:w(()=>[a(d,{class:"w-100",options:e.companyOptions,placeholder:"Select Company",name:"companyId",modelValue:e.filterForm.companyId,"onUpdate:modelValue":t[5]||(t[5]=n=>e.filterForm.companyId=n)},null,8,["options","modelValue"])]),_:1})])):E("",!0)]),t[10]||(t[10]=s("button",{class:"btn btn-sm btn-primary d-none",type:"submit"},null,-1))]),_:1},8,["model","onSubmit"])])])}const ft=Y(at,[["render",bt]]),vt=H({name:"product-modal",components:{SvgIcon:O},props:{loadPage:{type:Function,required:!1}},setup(e){var h;const t=K(),S=ie(),y=g(!1),_={companyId:F()?null:(h=R.getUserInfo())==null?void 0:h.companyId,name:"",description:"",price:null},c=g({..._}),m=g(null),f=g(!1),d=g(!1),u=g([]),n=g("");z(()=>{F()&&C()}),j(n,o=>{o!==""&&r()}),j(y,o=>{var i;o===!1&&(n.value="",U(),(i=m==null?void 0:m.value)==null||i.resetFields())});const C=async()=>{d.value=!0,t.getCompanies({params:{page:1,limit:500},callback:{onSuccess:o=>{const i=o==null?void 0:o.items.map(b=>({label:b==null?void 0:b.name,value:b==null?void 0:b.id}));u.value=[...i]},onFinish:()=>{d.value=!1}}})},r=async()=>{S.getProductDetails({id:n.value,callback:{onSuccess:o=>{var i;c.value={...o,companyId:(i=o==null?void 0:o.company)==null?void 0:i.id}}}})},P=async o=>{f.value=!0,S.updateProduct({id:n.value,params:o,callback:{onSuccess:i=>{e!=null&&e.loadPage&&(e==null||e.loadPage()),k()},onFinish:i=>{f.value=!1}}})},A=async o=>{f.value=!0,S.createProduct({params:o,callback:{onSuccess:i=>{e!=null&&e.loadPage&&(e==null||e.loadPage()),k()},onFinish:i=>{f.value=!1}}})},N=()=>{y.value=!0},k=()=>{y.value=!1},D=o=>{n.value=o.toString()},l={name:[{required:!0,message:"Please type Name",trigger:["change","blur"]}],description:[{required:!0,message:"Please type Description",trigger:["change","blur"]}],price:[{required:!0,message:"Please type Price",trigger:["change","blur"]}]},V=F()?{...l,companyId:[{required:!0,message:"Please select Company",trigger:["change","blur"]}]}:l,T=()=>{m.value&&m.value.validate(o=>{var i;if(o){const b={...c==null?void 0:c.value,price:Number((i=c==null?void 0:c.value)==null?void 0:i.price)};n!=null&&n.value?P(b):A(b)}})},U=()=>{c.value={..._}};return{id:n,modal:y,rules:V,loading:f,targetData:c,formRef:m,isSystemAdmin:F,loadingCompany:d,companyList:u,show:N,hide:k,submit:T,setId:D,reset:U}}}),yt={class:"d-flex align-items-center w-100"},_t={class:"modal-title"},ht={key:0,class:"row mb-3"},wt={class:"col-12 fv-row d-flex flex-column justify-content-stretch"},$t={class:"row mb-3"},St={class:"col-12 fv-row d-flex flex-column justify-content-stretch"},Ct={class:"row mb-3"},Pt={class:"col-12 fv-row d-flex flex-column justify-content-stretch"},Ft={class:"row mb-3"},kt={class:"col-12 fv-row d-flex flex-column justify-content-stretch"},It={class:"modal-footer d-flex justify-content-center align-items-center"},Dt=["disabled"],Vt=["data-kt-indicator","disabled"],Et={key:0,class:"indicator-label"},At={key:1,class:"indicator-progress"};function Nt(e,t,S,y,_,c){const m=p("SvgIcon"),f=p("el-select-v2"),d=p("el-form-item"),u=p("el-input"),n=p("el-form"),C=p("el-dialog");return v(),L(C,{modelValue:e.modal,"onUpdate:modelValue":t[6]||(t[6]=r=>e.modal=r),"show-close":!1,width:"500","align-center":""},{header:w(()=>[s("div",yt,[s("h3",_t,I(e.id?"Edit Product":"Add Product"),1),s("span",{class:"cursor-pointer ms-auto",onClick:t[0]||(t[0]=(...r)=>e.hide&&e.hide(...r))},[a(m,{icon:"closeModalIcon"})])])]),default:w(()=>[s("div",null,[a(n,{id:"product_form",onSubmit:W(e.submit,["prevent"]),model:e.targetData,rules:e.rules,ref:"formRef",class:"form"},{default:w(()=>[e.isSystemAdmin()?(v(),$("div",ht,[s("div",wt,[t[7]||(t[7]=s("label",{class:"d-flex align-items-center fs-6 fw-semibold mb-2 text-break required"},"Company ",-1)),a(d,{prop:"companyId"},{default:w(()=>[a(f,{class:"w-100",modelValue:e.targetData.companyId,"onUpdate:modelValue":t[1]||(t[1]=r=>e.targetData.companyId=r),options:e.companyList,placeholder:"Search Company",filterable:"",name:"companyId",loading:e.loadingCompany},null,8,["modelValue","options","loading"])]),_:1})])])):E("",!0),s("div",$t,[s("div",St,[t[8]||(t[8]=s("label",{class:"d-flex align-items-center fs-6 fw-semibold mb-2 text-break required"},"Name ",-1)),a(d,{prop:"name",class:"mt-auto"},{default:w(()=>[a(u,{class:"w-100",modelValue:e.targetData.name,"onUpdate:modelValue":t[2]||(t[2]=r=>e.targetData.name=r),placeholder:"",name:"name"},null,8,["modelValue"])]),_:1})])]),s("div",Ct,[s("div",Pt,[t[9]||(t[9]=s("label",{class:"d-flex align-items-center fs-6 fw-semibold mb-2 text-break required"},"Description ",-1)),a(d,{prop:"description",class:"mt-auto"},{default:w(()=>[a(u,{type:"textarea",rows:5,class:"w-100",modelValue:e.targetData.description,"onUpdate:modelValue":t[3]||(t[3]=r=>e.targetData.description=r),placeholder:"",name:"description"},null,8,["modelValue"])]),_:1})])]),s("div",Ft,[s("div",kt,[t[10]||(t[10]=s("label",{class:"d-flex align-items-center fs-6 fw-semibold mb-2 text-break required"},"Price ",-1)),a(d,{prop:"price",class:"mt-auto"},{default:w(()=>[a(u,{type:"number",class:"w-100",controls:!1,step:"any",modelValue:e.targetData.price,"onUpdate:modelValue":t[4]||(t[4]=r=>e.targetData.price=r),placeholder:"",name:"price"},null,8,["modelValue"])]),_:1})])]),s("div",It,[s("button",{type:"button",id:"kt_modal_add_customer_cancel",class:"btn text-gray-700 btn-sm me-3 btn-blue",onClick:t[5]||(t[5]=(...r)=>e.hide&&e.hide(...r)),disabled:e.loading}," Discard ",8,Dt),s("button",{"data-kt-indicator":e.loading?"on":null,class:"btn btn-sm btn-primary",type:"submit",disabled:e.loading},[e.loading?E("",!0):(v(),$("span",Et," Save ")),e.loading?(v(),$("span",At,t[11]||(t[11]=[B(" Please wait... "),s("span",{class:"spinner-border spinner-border-sm align-middle ms-2"},null,-1)]))):E("",!0)],8,Vt)])]),_:1},8,["onSubmit","model","rules"])])]),_:1},8,["modelValue"])}const Tt=Y(vt,[["render",Nt]]),qt=H({name:"products",components:{Filter:ft,SvgIcon:O,TablePagination:te,ProductModal:Tt,TableHeader:se},setup(){const e=ie(),t=g([]),S=g(0),y=g(0),_=g(1),c=g(!1),m=g(""),f=g(!1),d=g(null),u=g({}),n=g({sortDirection:oe.ASC,sortBy:q.Name}),C=[{label:"NAME",sortBy:q.Name,class:"min-w-150px"},{label:"COMPANY",class:"min-w-150px",display:F()},{label:"PRICE",sortBy:q.Price},{label:"CREATED DATE",class:"min-w-150px",sortBy:q.CreatedAt},{label:"ACTIONS"}];z(()=>{r()}),j(_,()=>{r()});const r=async()=>{var h,o;c.value=!0,e.getProducts({params:{companyId:F()?null:(o=(h=R)==null?void 0:h.getUserInfo())==null?void 0:o.companyId,keyword:m.value.trim()||null,page:_.value,limit:10,...n.value,...u.value},callback:{onSuccess:i=>{t.value=i==null?void 0:i.items,S.value=i==null?void 0:i.totalPage,y.value=i==null?void 0:i.total,_.value=i==null?void 0:i.page},onFinish:i=>{c.value=!1}}})},P=h=>{n.value={...h},r()},A=h=>{_.value=h},N=()=>{var h;(h=d==null?void 0:d.value)==null||h.show()},k=h=>{var o,i;(o=d==null?void 0:d.value)==null||o.setId(h),(i=d==null?void 0:d.value)==null||i.show()},D=h=>{Z.deletionAlert({onConfirmed:()=>{l(h)}})},l=async h=>{c.value=!0,e.deleteProduct({id:h,callback:{onSuccess:o=>{r()},onFinish:o=>{c.value=!1}}})};return{onSort:P,onFilter:h=>{u.value={...h},_.value!==1?_.value=1:r()},toggleFilter:h=>{f.value=h},searchProduct:()=>{_.value!==1?_.value=1:r()},getProducts:r,deleteProduct:D,pageChange:A,numberWithCommas:le,formatDate:ne,toggleNewProduct:N,toggleEditProduct:k,loading:c,productList:t,pageCount:S,currentPage:_,totalElements:y,productModal:d,search:m,isShowFilter:f,isSystemAdmin:F,sortParams:n,tableHeader:C}}}),Bt={class:"card h-100 my-8"},Ut={class:"card-header py-4 align-items-center"},jt={class:"card-toolbar gap-3 ms-auto"},Lt={class:"d-flex align-items-center"},Mt={class:"svg-icon svg-icon-2"},Ht={class:"svg-icon svg-icon-2"},Yt={class:"svg-icon svg-icon-2"},Ot={class:"btn btn-flex btn-sm btn-success btn-export-success"},Rt={class:"svg-icon svg-icon-1"},zt={class:"card-body"},Wt={key:0,class:"text-center"},Jt={key:2},Gt={class:"table-responsive"},Kt={class:"table table-row-bordered align-middle gs-0 gy-3"},Qt={class:"fw-semibold text-gray-400 fs-6"},Xt={class:"text-gray-900 fw-semibold fs-5"},Zt={key:0,class:"text-gray-900 fw-semibold fs-5"},xt={class:"text-gray-600 fw-semibold fs-5"},es={class:"text-gray-600 fw-semibold fs-5"},ts={class:"d-flex align-items-center"},ss=["onClick"],os={class:"svg-icon svg-icon-3"},ns={class:"d-flex flex-wrap align-items-center mt-5"},ls={class:"text-gray-700 fw-semibold fs-6 me-auto"};function as(e,t,S,y,_,c){var D;const m=p("SvgIcon"),f=p("el-icon"),d=p("el-input"),u=p("el-form-item"),n=p("el-form"),C=p("inline-svg"),r=p("Filter"),P=p("el-empty"),A=p("TableHeader"),N=p("TablePagination"),k=p("ProductModal");return v(),$(M,null,[s("div",Bt,[s("div",Ut,[t[6]||(t[6]=s("h1",{class:"mb-0 me-4 text-gray-900"},"Products",-1)),s("div",jt,[a(n,{onSubmit:W(e.searchProduct,["prevent"])},{default:w(()=>[s("div",Lt,[a(u,{class:"mb-0"},{default:w(()=>[a(d,{class:"w-250px",placeholder:"Search",modelValue:e.search,"onUpdate:modelValue":t[0]||(t[0]=l=>e.search=l),name:"search",size:"large"},{prefix:w(()=>[a(f,{class:"el-input__icon"},{default:w(()=>[s("span",Mt,[a(m,{icon:"searchIcon"})])]),_:1})]),_:1},8,["modelValue"])]),_:1})])]),_:1},8,["onSubmit"]),s("button",{class:J(["btn btn-flex btn-sm",e.isShowFilter?"btn-primary":"btn-blue"]),onClick:t[1]||(t[1]=()=>e.toggleFilter(!e.isShowFilter))},[s("span",Ht,[a(C,{src:"media/icons/duotune/general/gen031.svg"})]),t[3]||(t[3]=B(" Filter "))],2),s("button",{class:"btn btn-flex btn-sm btn-primary",onClick:t[2]||(t[2]=(...l)=>e.toggleNewProduct&&e.toggleNewProduct(...l))},[s("span",Yt,[a(C,{src:"media/icons/duotune/arrows/arr075.svg"})]),t[4]||(t[4]=B(" New "))]),s("button",Ot,[s("span",Rt,[a(m,{icon:"iconExport"})]),t[5]||(t[5]=B(" Export "))])]),x(a(r,{hideFilter:()=>e.toggleFilter(!1),onFilter:e.onFilter},null,8,["hideFilter","onFilter"]),[[ee,e.isShowFilter]])]),s("div",zt,[e.loading?(v(),$("div",Wt,t[7]||(t[7]=[s("div",{class:"spinner-border text-primary",role:"status"},[s("span",{class:"sr-only"},"Loading...")],-1)]))):e.productList.length===0?(v(),L(P,{key:1,description:"No Data"})):(v(),$("div",Jt,[s("div",Gt,[s("table",Kt,[s("thead",null,[s("tr",Qt,[a(A,{headers:e.tableHeader,sortBy:e.sortParams.sortBy,sortDirection:e.sortParams.sortDirection,onSort:e.onSort},null,8,["headers","sortBy","sortDirection","onSort"])])]),s("tbody",null,[(v(!0),$(M,null,G(e.productList,l=>{var V;return v(),$("tr",{key:l==null?void 0:l.id},[s("td",Xt,I(l==null?void 0:l.name),1),e.isSystemAdmin()?(v(),$("td",Zt,I((V=l==null?void 0:l.company)==null?void 0:V.name),1)):E("",!0),s("td",xt,I(`$${e.numberWithCommas(l==null?void 0:l.price)}`),1),s("td",es,I(e.formatDate(l==null?void 0:l.createdAt,"MMM DD, YYYY")),1),s("td",null,[s("div",ts,[s("button",{class:"btn btn-icon btn-sm btn-blue me-3",onClick:T=>e.toggleEditProduct(l==null?void 0:l.id)},[s("span",os,[a(m,{icon:"newReportIcon"})])],8,ss)])])])}),128))])])]),s("div",ns,[s("div",ls,I(`Showing ${(e.currentPage-1)*10+1} to ${(D=e.productList)==null?void 0:D.length} of ${e.totalElements} entries`),1),e.pageCount>=1?(v(),L(N,{key:0,"total-pages":e.pageCount,total:e.totalElements,"per-page":10,"current-page":e.currentPage,onPageChange:e.pageChange},null,8,["total-pages","total","current-page","onPageChange"])):E("",!0)])]))])]),a(k,{ref:"productModal",loadPage:e.getProducts},null,8,["loadPage"])],64)}const is=Y(qt,[["render",as]]),X=[{value:"products",label:"Products"},{value:"serviceCosts",label:"Service Costs"},{value:"engineeringCosts",label:"Engineering Costs"}],rs=H({name:"setting",components:{SvgIcon:O,Products:is,CostsPage:lt},setup(){const e=g(X[0].value);return{tabs:X,tabIndex:e,CostSettingType:re,getOption:ce,setActiveTab:S=>{const y=S.target;e.value=y.getAttribute("data-tab-index")}}}}),ds={class:"d-flex flex-wrap align-items-center justify-content-between w-100"},cs={class:"page-title d-flex flex-column justify-content-center flex-wrap me-3"},us={class:"breadcrumb breadcrumb-separatorless fw-semibold fs-7 my-0 pt-1"},ms={class:"breadcrumb-item text-gray-800 breadcrumb-title"},ps={class:"d-flex align-items-center gap-2 gap-lg-3"},gs={class:"nav nav-stretch nav-line-tabs border-0 daily-nav",role:"tablist",id:"kt_layout_builder_tabs",ref:"kt_layout_builder_tabs"},bs=["data-tab-index"],fs={key:0},vs={key:1},ys={key:2};function _s(e,t,S,y,_,c){var d;const m=p("Products"),f=p("CostsPage");return v(),$(M,null,[s("div",ds,[s("div",cs,[t[3]||(t[3]=s("h1",{class:"page-heading d-flex text-gray-800 fw-bold fs-3 flex-column justify-content-center my-0"}," Setting ",-1)),s("ul",us,[t[1]||(t[1]=s("li",{class:"breadcrumb-item text-muted breadcrumb-title"},"Setting",-1)),t[2]||(t[2]=s("li",{class:"breadcrumb-item breadcrumb-bullet"},[s("span",{class:"bullet bg-gray-400 w-5px h-2px"})],-1)),s("li",ms,I((d=e.getOption(e.tabIndex,e.tabs))==null?void 0:d.label),1)])]),s("div",ps,[s("ul",gs,[(v(!0),$(M,null,G(e.tabs,u=>(v(),$("li",{class:"nav-item",key:u.value},[s("div",{class:J(["nav-link cursor-pointer text-active-primary fw-semibold text-hover-primary fs-5",{active:e.tabIndex===u.value}]),"data-bs-toggle":"tab",onClick:t[0]||(t[0]=n=>e.setActiveTab(n)),"data-tab-index":u.value,role:"tab"},I(u.label),11,bs)]))),128))],512)])]),e.tabIndex=="products"?(v(),$("div",fs,[a(m)])):e.tabIndex=="serviceCosts"?(v(),$("div",vs,[a(f,{label:"Service Costs",type:e.CostSettingType.Service},null,8,["type"])])):e.tabIndex=="engineeringCosts"?(v(),$("div",ys,[a(f,{label:"Engineering Costs",type:e.CostSettingType.Engineer},null,8,["type"])])):E("",!0)],64)}const As=Y(rs,[["render",_s]]);export{As as default};
