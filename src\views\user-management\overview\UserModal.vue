<template>
  <div
    v-if="isVisible"
    class="fixed top-0 right-0 bottom-0 left-0 z-40 flex items-center"
  >
    <!-- Dark overlay background -->
    <div class="absolute inset-0 bg-sidebar-background opacity-25"></div>
    <div
      class="relative bg-card-background text-card-text max-h-11/12 w-11/12 flex flex-col items-center gap-3 mx-auto p-5 rounded-xl lg:w-2/5 lg:h-auto"
    >
      <div class="h-auto w-full flex flex-row items-center justify-between">
        <h3 class="text-lg font-bold">Add User</h3>
        <span class="cursor-pointer" @click="closeUserModal">
          <SvgIcon icon="closeModalIcon" />
        </span>
      </div>

      <el-form
        id="product_form"
        @submit.prevent="submit"
        :model="targetData"
        :rules="rules"
        ref="formRef"
        class="h-auto w-full"
      >
        <div class="md:flex md:h-auto md:w-full md:gap-4">
          <div class="md:h-auto md:w-full md:flex md:flex-col">
            <div class="md:h-auto md:w-full md:flex md:justify-between md:gap-4">
              <div class="h-auto w-full flex flex-col gap-2">
                <label class="font-semibold">First Name </label>
                <el-form-item prop="firstName">
                  <el-input
                    v-model="targetData.firstName"
                    placeholder=""
                    name="firstName"
                  ></el-input>
                </el-form-item>
              </div>
              <div class="h-auto w-full flex flex-col gap-2">
                <label class="font-semibold">Last Name </label>
                <el-form-item prop="lastName">
                  <el-input
                    v-model="targetData.lastName"
                    placeholder=""
                    name="lastName"
                  ></el-input>
                </el-form-item>
              </div>
            </div>
            <div
              class="flex flex-col gap-2"
            >
              <label class="font-semibold">Address </label>
              <el-form-item prop="address">
                <el-input
                  v-model="targetData.address"
                  placeholder=""
                  name="address"
                ></el-input>
              </el-form-item>
            </div>
            <div class="flex flex-col gap-2">
              <label class="font-semibold"> Company </label>
              <el-form-item prop="companyId">
                <el-select-v2
                  :disabled="!isSystemAdmin() || !isUserManagementPage"
                  v-model="targetData.companyId"
                  :options="companyOptions"
                  placeholder="Company Name"
                  name="companyId"
                  :loading="loadingCompany"
                />
              </el-form-item>
            </div>
          </div>
          <div class="md:h-auto md:w-full md:flex md:flex-col">
            <div class="flex flex-col gap-2">
              <label class="font-semibold">Email Address </label>
              <el-form-item prop="email">
                <el-input
                  v-model="targetData.email"
                  placeholder=""
                  name="email"
                ></el-input>
              </el-form-item>
            </div>
            <div class="flex flex-col gap-2">
              <label class="font-semibold">Office Phone </label>
              <el-form-item prop="officePhone">
                <el-input
                  v-model="targetData.officePhone"
                  placeholder=""
                  name="officePhone"
                ></el-input>
              </el-form-item>
            </div>
            <div class="flex flex-col gap-2">
              <label class="font-semibold">Mobile Phone </label>
              <el-form-item prop="mobilePhone">
                <el-input
                  v-model="targetData.mobilePhone"
                  placeholder=""
                  name="mobilePhone"
                ></el-input>
              </el-form-item>
            </div>
          </div>
        </div>
        <div class="flex flex-col gap-2">
          <label class="font-semibold">Roles </label>
          <el-form-item prop="userRoles">
            <el-select-v2
              v-model="targetData.userRoles"
              :options="rolesOptions"
              placeholder="Roles"
              multiple
              clearable
              name="userRoles"
              :disabled="disabledRole"
            />
          </el-form-item>
        </div>

        <div class="col-12 fv-row d-flex flex-column justify-content-stretch">
          <label class="font-semibold">Note </label>
          <el-form-item prop="note">
            <el-input
              v-model="targetData.note"
              placeholder=""
              type="textarea"
              rows="2"
              name="note"
            ></el-input>
          </el-form-item>
        </div>
        <div class="flex flex-row items-start mt-4 gap-3">
          <button
            type="button"
            class="bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover rounded-md px-4 py-2 font-semibold"
            @click="closeUserModal"
            :disabled="loading"
          >
            Discard
          </button>
          <button
            class="bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover rounded-md px-4 py-2 font-semibold"
            type="submit"
            :disabled="loading"
          >
            <span v-if="!loading" class="indicator-label"> Save </span>
            <span v-if="loading" class="indicator-progress">
              Please wait...
              <span
                class="spinner-border spinner-border-sm align-middle ms-2"
              ></span>
            </span>
          </button>
        </div>
      </el-form>
    </div>
  </div>
</template>

<script lang="ts">
import SvgIcon from "@/constants/SvgIcon.vue";
import { rolesOptions } from "@/constants/user";
import { yupValidate } from "@/utils/validator";
import JwtService, { isSystemAdmin } from "@/services/JwtService";
import { useCompanyStore } from "@/stores/company";
import { useUserStore } from "@/stores/user";
import type { FormRules } from "element-plus";
import { defineComponent, ref, watch } from "vue";
import { useRoute } from "vue-router";
import * as yup from "yup";
import type { Option } from "@/types/common";

export default defineComponent({
  name: "user-modal",
  components: { SvgIcon },
  props: {
    isVisible: {
      type: Boolean,
      required: true,
    },
    close: {
      type: Function,
      required: true,
    },
    loadPage: {
      type: Function,
      required: false,
    },
  },
  setup(props) {
    const route = useRoute();
    const companyStore = useCompanyStore();
    const userStore = useUserStore();
    const modal = ref(false);
    const companyOptions = ref<Option[]>([]);
    const initialValue: User.Info = {
      firstName: "",
      lastName: "",
      companyId: isSystemAdmin() ? "" : JwtService.getUserInfo()?.companyId,
      email: "",
      officePhone: "",
      mobilePhone: "",
      userRoles: [],
      address: "",
      note: "",
    };
    const disabledRole = ref<boolean>(false);
    const targetData = ref({ ...initialValue });
    const formRef = ref<null | HTMLFormElement>(null);
    const loading = ref<boolean>(false);
    const loadingCompany = ref<boolean>(false);
    const isUserManagementPage = route.name === "users";

    watch(modal, (newValue) => {
      if (newValue === false) {
        reset();
      } else {
        if (isSystemAdmin()) {
          getAllCompanies();
        } else {
          getMyCompany();
        }
      }
    });

    const getAllCompanies = async (): Promise<void> => {
      loadingCompany.value = true;
      companyStore.getCompanies({
        params: {
          page: 1,
          limit: 500,
        },
        callback: {
          onSuccess: (res: any) => {
            companyOptions.value = res?.items?.map((item: Company.Info) => {
              return {
                value: item?.id,
                label: item?.name,
              };
            });
          },
          onFinish: () => {
            loadingCompany.value = false;
          },
        },
      });
    };

    const getMyCompany = async (): Promise<void> => {
      loadingCompany.value = true;
      companyStore.getCompanyById({
        id: JwtService.getUserInfo()?.companyId,
        callback: {
          onSuccess: (res: any) => {
            companyOptions.value = [
              {
                value: res?.id,
                label: res?.name,
              },
            ];
          },
          onFinish: () => {
            loadingCompany.value = false;
          },
        },
      });
    };

    const closeUserModal = (): void => {
      props.close();
    };

    const yupRule = yup.object().shape({
      email: yupValidate.emailAddress,
      mobilePhone: yupValidate.mobilePhone,
      officePhone: yupValidate.officePhone,
    });

    const rules = ref<FormRules<any>>({
      firstName: [
        {
          required: true,
          message: "Please type First Name",
          trigger: ["blur", "change"],
        },
      ],
      lastName: [
        {
          required: true,
          message: "Please type Last Name",
          trigger: ["blur", "change"],
        },
      ],
      companyId: [
        {
          required: true,
          message: "Please select Company",
          trigger: ["blur", "change"],
        },
      ],
      email: [
        {
          required: true,
          validator: (_: any, value: any, callback: any) => {
            (yupRule.fields.email as yup.AnySchema)
              .validate(value)
              .then(() => {
                callback();
              })
              .catch((error) => {
                callback(new Error(error.errors[0]));
              });
          },
          trigger: ["blur", "change"],
        },
      ],
      mobilePhone: [
        {
          required: true,
          validator: (_: any, value: any, callback: any) => {
            (yupRule.fields.mobilePhone as yup.AnySchema)
              .validate(value)
              .then(() => {
                callback();
              })
              .catch((error: any) => {
                callback(new Error(error.errors[0]));
              });
          },
          trigger: ["blur", "change"],
        },
      ],
      officePhone: [
        {
          validator: (_: any, value: any, callback: any) => {
            if (value) {
              (yupRule.fields.officePhone as yup.AnySchema)
                .validate(value)
                .then(() => {
                  callback();
                })
                .catch((error) => {
                  callback(new Error(error.errors[0]));
                });
            } else {
              callback();
            }
          },
          trigger: ["blur", "change"],
        },
      ],
    });

    const submit = () => {
      if (!formRef.value) {
        return;
      }

      formRef.value.validate((valid: boolean) => {
        if (valid) {
          const params = {
            ...targetData.value,
            officePhone: targetData.value?.officePhone || null,
            address: targetData.value?.address || null,
            note: targetData.value?.note || null,
          };
          addUser(params);
        }
      });
    };

    const addUser = async (params: User.Info): Promise<void> => {
      loading.value = true;

      userStore.addUser({
        params,
        callback: {
          onSuccess: (res: any) => {
            props.close();
            props?.loadPage?.(res?.id);
          },
          onFinish: () => {
            loading.value = false;
          },
        },
      });
    };

    const reset = () => {
      disabledRole.value = false;
      targetData.value = { ...initialValue };
      formRef?.value?.resetFields();
    };

    const setInitialValue = (initialAddUserValue: User.Info) => {
      disabledRole.value = true;
      targetData.value = { ...targetData.value, ...initialAddUserValue };
    };

    return {
      disabledRole,
      companyOptions,
      isSystemAdmin,
      rules,
      loading,
      loadingCompany,
      targetData,
      formRef,
      rolesOptions,
      isUserManagementPage,
      submit,
      reset,
      setInitialValue,
      closeUserModal,
    };
  },
});
</script>
