<template>
  <el-form
    @submit.prevent="submit"
    :model="targetData"
    :rules="rules"
    ref="formRef"
    class="bg-minicard-background text-minicard-text-light h-auto w-11/12 mx-auto p-4 rounded-xl"
  >
    <div v-if="loading" class="text-center">
      <div class="spinner-border text-primary" role="status">
        <span class="sr-only">Loading...</span>
      </div>
    </div>
    <div v-else>
      <div class="flex flex-col gap-1 md:flex-row md:gap-4">
        <div class="h-auto w-full flex flex-col gap-1">
          <div class="h-auto w-full flex flex-col gap-1">
            <label class="font-bold text-sm"
              ><span>Shale CEC (Cation Exchange Capacity) (mg/g)</span>
              <el-popover placement="top" :width="400" trigger="hover">
                <template #reference>
                  <i class="fas fa-exclamation-circle ms-2 fs-7"></i>
                </template>
                <span>
                  Shale CEC is a measure of the cation exchange capacity of
                  shale particles present in the drilling mud. It quantifies the
                  shale's ability to adsorb and exchange ions with the mud. High
                  CEC can lead to mud instability and unwanted interactions.
                </span>
              </el-popover></label
            >
            <el-form-item prop="shaleCEC">
              <el-input
                type="number"
                :controls="false"
                step="any"
                v-model="targetData.shaleCEC"
                placeholder=""
                name="shaleCEC"
              ></el-input>
            </el-form-item>
          </div>
          <div class="h-auto w-full flex flex-col gap-1">
            <label class="font-bold text-sm"
              ><span>Bent CEC (Cation Exchange Capacity) (mg/g)</span>
              <el-popover placement="top" :width="400" trigger="hover">
                <template #reference>
                  <i class="fas fa-exclamation-circle ms-2 fs-7"></i>
                </template>
                <span>
                  Bentonite CEC is a measure of the cation exchange capacity of
                  bentonite clay used as an additive in drilling mud. It
                  assesses the clay's capacity to exchange ions with the mud.
                  Bentonite is often added to enhance mud properties.
                </span>
              </el-popover></label
            >
            <el-form-item prop="bentCEC">
              <el-input
                type="number"
                :controls="false"
                step="any"
                v-model="targetData.bentCEC"
                placeholder=""
                name="bentCEC"
              ></el-input>
            </el-form-item>
          </div>
        </div>
        <div class="h-auto w-full flex flex-col gap-1">
          <div class="h-auto w-full flex flex-col gap-1">
            <label class="font-bold text-sm"
              ><span>HGS (High Gel Strength) Specific Gravity</span>
              <el-popover placement="top" :width="400" trigger="hover">
                <template #reference>
                  <i class="fas fa-exclamation-circle ms-2 fs-7"></i>
                </template>
                <span>
                  HGS Specific Gravity refers to the specific gravity (density)
                  of the high gel strength component of the drilling mud. It
                  assesses the density of the solid materials responsible for
                  high gel strength.
                </span>
              </el-popover></label
            >
            <el-form-item prop="highGelStrength">
              <el-input
                type="number"
                :controls="false"
                step="any"
                v-model="targetData.highGelStrength"
                placeholder=""
                name="highGelStrength"
              ></el-input>
            </el-form-item>
          </div>
          <div class="h-auto w-full flex flex-col gap-1">
            <label class="font-bold text-sm"
              ><span>LGS (Linear Gel Strength) Specific Gravity</span>
              <el-popover placement="top" :width="400" trigger="hover">
                <template #reference>
                  <i class="fas fa-exclamation-circle ms-2 fs-7"></i>
                </template>
                <span>
                  LGS Specific Gravity refers to the specific gravity (density)
                  of the linear gel strength component of the drilling mud. It
                  assesses the density of the solid materials responsible for
                  linear gel strength.
                </span>
              </el-popover></label
            >
            <el-form-item prop="linearGelStrength">
              <el-input
                type="number"
                :controls="false"
                step="any"
                v-model="targetData.linearGelStrength"
                placeholder=""
                name="linearGelStrength"
              ></el-input>
            </el-form-item>
          </div>
        </div>
      </div>

      <div class="h-auto w-full flex flex-row items-center gap-2">
        <button
          type="button"
          class="bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover rounded-md px-4 py-2 font-semibold"
          @click="cancel"
          :disabled="isFormDirty() !== true || submitting"
        >
          Cancel
        </button>
        <button
          class="bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover rounded-md px-4 py-2 font-semibold"
          type="submit"
          :disabled="isFormDirty() !== true || submitting"
        >
          <span v-if="!submitting" class="indicator-label"> Save </span>
          <span v-if="submitting" class="indicator-progress">
            Please wait...
            <span
              class="spinner-border spinner-border-sm align-middle ms-2"
            ></span>
          </span>
        </button>
      </div>
    </div>
  </el-form>
  <BottomTool :showAddNew="false" />
</template>

<script lang="ts">
import SvgIcon from "@/constants/SvgIcon.vue";
import { setupNavigationGuard } from "@/utils/navigation-guard";
import AlertService from "@/services/AlertService";
import { useSolidStore } from "@/stores/solid";
import _ from "lodash";
import { defineComponent, inject, onMounted, ref } from "vue";
import { onBeforeRouteLeave, useRoute } from "vue-router";
import BottomTool from "@/components/common/BottomTool.vue";
import type { Provide } from "@/types/injection-types";

export default defineComponent({
  name: "solid",
  components: {
    SvgIcon,
    BottomTool,
  },
  setup() {
    const route = useRoute();
    const dailyReportProvide = inject<Provide.DailyReport>("dailyReport");
    const solidStore = useSolidStore();
    const formRef = ref<null | HTMLFormElement>(null);
    const initialForm = ref<any>({
      shaleCEC: null,
      bentCEC: null,
      highGelStrength: null,
      linearGelStrength: null,
    });
    const loading = ref<boolean>(false);
    const submitting = ref(false);
    const targetData = ref<any>({ ...initialForm.value });

    const rules = ref({});

    onBeforeRouteLeave((to, from, next) => {
      setupNavigationGuard(to, from, next, submitting.value || isFormDirty());
    });

    onMounted(() => {
      getSolid();
    });

    const getSolid = async (): Promise<void> => {
      if (!dailyReportProvide?.getDailyReportId()) return;
      loading.value = true;

      solidStore.getSolidDetails({
        dailyReportId: dailyReportProvide?.getDailyReportId(),
        callback: {
          onSuccess: (res: any) => {
            if (res) {
              targetData.value = {
                ...res,
              };
              initialForm.value = {
                ...res,
              };
            }
          },
          onFinish: (_err: any) => {
            loading.value = false;
          },
        },
      });
    };

    const updateSolid = async (param: any): Promise<void> => {
      submitting.value = true;
      solidStore.updateSolid({
        id: targetData?.value?.id || "",
        params: param,
        callback: {
          onSuccess: (_res: any) => {
            getSolid();
          },
          onFinish: () => {
            submitting.value = false;
          },
        },
      });
    };

    const createSolid = async (param: any): Promise<void> => {
      solidStore.createSolid({
        params: param,
        callback: {
          onSuccess: (_res: any) => {
            getSolid();
          },
          onFinish: () => {
            submitting.value = false;
          },
        },
      });
    };

    const isFormDirty = () => {
      return !_.isEqual(targetData.value, initialForm.value);
    };

    const isValidForm = async (): Promise<boolean> => {
      const result = await formRef?.value?.validate((valid: boolean) => {
        return valid;
      });

      return result;
    };

    const submit = () => {
      if (!formRef.value) {
        return;
      }

      formRef.value.validate((valid: boolean) => {
        if (valid) {
          if (submitting.value) return;
          if (!isFormDirty()) return;
          const params = {
            shaleCEC: Number(targetData?.value?.shaleCEC),
            bentCEC: Number(targetData?.value?.bentCEC),
            highGelStrength: Number(targetData?.value?.highGelStrength),
            linearGelStrength: Number(targetData?.value?.linearGelStrength),
          };
          if (targetData?.value?.id) {
            updateSolid({
              ...params,
              dailyReportId: dailyReportProvide?.getDailyReportId(),
            });
          } else {
            submitting.value = true;
            dailyReportProvide?.createDailyReport({
              wellId: route?.params?.id as string,
              callback: {
                onSuccess: (_res: string) => {
                  createSolid({
                    ...params,
                    dailyReportId: dailyReportProvide?.getDailyReportId(),
                  });
                },
                onFailure: () => {
                  submitting.value = false;
                },
              },
            });
          }
        }
      });
    };

    const cancel = () => {
      AlertService.incompleteFormAlert(
        {
          onConfirmed: () => {
            targetData.value = JSON.parse(JSON.stringify(initialForm.value));
          },
        },
        "Cancel changes on this section?",
        "Yes"
      );
    };

    return {
      cancel,
      submit,
      isFormDirty,
      isValidForm,
      rules,
      loading,
      submitting,
      formRef,
      targetData,
    };
  },
});
</script>

<style scoped></style>
