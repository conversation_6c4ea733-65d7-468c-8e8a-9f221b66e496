import{S as _}from"./SuccessTemplate-BuLX5i7K.js";import{S as A}from"./SvgIcon-CMhyaXWN.js";import{d as F,q as w,J as D,A as M,a8 as q,G as T,a9 as L,_ as j,c as g,m as B,l as C,a,b as i,w as v,p as G,r as h,o as f,F as $,k as J,t as z,B as O}from"./index-CGNRhvz7.js";import{R as H,a as Y,b as K,c as Q}from"./regex-BLjctcPP.js";import{v as S}from"./validator-6laVLK0J.js";import{u as W}from"./user-KFDu8xJF.js";import{E as X,F as Z,a as ee}from"./vee-validate-CesDBK0n.js";import{_ as se}from"./well-logo-diufKTHF.js";import"./index.esm-DXW765zG.js";import"./handleFailure-DtTpu7r3.js";var R=(e=>(e[e.REGISTER=1]="REGISTER",e[e.SUCCESS=2]="SUCCESS",e))(R||{});const ae=F({name:"register-invited-user",components:{Field:ee,VForm:Z,ErrorMessage:X,SvgIcon:A,SuccessTemplate:_},setup(){var U;const e=W(),s=w(1),E={email:(U=D.getUserInfo())==null?void 0:U.email,firstName:"",lastName:"",newPassword:"",confirmPassword:"",currentPassword:"",hideNewPassword:!0,hideConfirmPassword:!0},k={length:{isValid:!1,text:"At least 8 characters"},lowercase:{isValid:!1,text:"At least 1 lowercase character"},uppercase:{isValid:!1,text:"At least 1 uppercase character"},specialCharacter:{isValid:!1,text:"At least 1 number and 1 special character"}},d=w({...E}),P=w(!1),m=w(null),c=w({...k});M(()=>d.value.newPassword,l=>{});const b=l=>{const t=S.validate(l,{pattern:H,errorsMessage:{pattern:"Incorrect password format."}}),r=S.validate(l,{pattern:Y,errorsMessage:{pattern:"Incorrect password format."}}),n=S.validate(l,{pattern:K,errorsMessage:{pattern:"Incorrect password format."}}),p=S.validate(l,{pattern:Q,errorsMessage:{pattern:"Incorrect password format."}});return c.value={length:{isValid:!t,text:"At least 8 characters"},lowercase:{isValid:!r,text:"At least 1 lowercase character"},uppercase:{isValid:!n,text:"At least 1 uppercase character"},specialCharacter:{isValid:!p,text:"At least 1 number and 1 special character"}},t||r||n||p||""},N=w({firstName:[{required:!0,message:"Please type First Name",trigger:["blur","change"]}],lastName:[{required:!0,message:"Please type Last Name",trigger:["blur","change"]}],email:[[{required:!0,message:"Please input email address",trigger:"blur"},{type:"email",message:"Please input correct email address",trigger:["blur","change"]}]],newPassword:[{validator:(l,t,r)=>{if(t==="")r(new Error("Please type New Password"));else{const n=b(t);n!==""?r(new Error(n)):r()}},trigger:["change","blur"]}],confirmPassword:[{validator:(l,t,r)=>{t===""?r(new Error("Please type Confirm Password")):t!==d.value.newPassword?r(new Error("Confirm Password doesn't match New Password!")):r()},trigger:["change","blur"]}]}),y=()=>{m.value&&m.value.validate(l=>{var t,r,n;if(l){const p={userInfo:{firstName:(t=d.value)==null?void 0:t.firstName,lastName:(r=d.value)==null?void 0:r.lastName},changePass:{currentPassword:L,newPassword:(n=d==null?void 0:d.value)==null?void 0:n.newPassword}};o(p)}})},o=async l=>{P.value=!0,e.registerInvitedUser({params:l,callback:{onSuccess:t=>{e.getMyProfile({callback:{onSuccess:r=>{var n;D.saveUserInfo(JSON.stringify({...r,companyId:((n=r==null?void 0:r.company)==null?void 0:n.id)||""})),s.value=2},onFinish:()=>{P.value=!1}}})},onFailure:t=>{var r,n,p,V;P.value=!1,q.fire({text:T[(n=(r=t==null?void 0:t.response)==null?void 0:r.data)==null?void 0:n.errorCode]||((V=(p=t==null?void 0:t.response)==null?void 0:p.data)==null?void 0:V.message)||(t==null?void 0:t.message)||"Sorry, looks like there are some errors detected, please try again.",icon:"error",buttonsStyling:!1,confirmButtonText:"Got it!",heightAuto:!1,customClass:{confirmButton:"btn fw-semibold btn-light-danger"}})}}})};return{currentStep:s,STEP:R,formRef:m,rules:N,loading:P,targetData:d,checkPassword:c,submit:y,toggleEye:l=>{l==="confirmPassword"?d.value.hideConfirmPassword=!d.value.hideConfirmPassword:l==="newPassword"&&(d.value.hideNewPassword=!d.value.hideNewPassword)}}}}),te={class:"form-container w-xxl-500px p-10 bg-white rounded-3"},re={key:0},oe={class:"d-flex flex-column gap-10"},le={class:"row"},ne={class:"col-12 fv-row d-flex flex-column justify-content-stretch mb-7"},ie={class:"col-12 fv-row d-flex flex-column justify-content-stretch mb-7"},de={class:"col-12 fv-row d-flex flex-column justify-content-stretch mb-7"},me={class:"col-12 fv-row d-flex flex-column justify-content-stretch mb-7"},ce={class:"position-relative input-password"},ue={class:"col-12 fv-row d-flex flex-column justify-content-stretch"},pe={class:"position-relative input-password"},fe={className:"validate-password my-5"},we=["className"],ge={class:"ms-2"},ve={class:"d-flex justify-content-end mt-10"},Pe=["data-kt-indicator","disabled"],be={key:0,class:"indicator-label"},he={key:1,class:"indicator-progress"};function Ne(e,s,E,k,d,P){const m=h("el-input"),c=h("el-form-item"),b=h("SvgIcon"),x=h("el-form"),I=h("SuccessTemplate");return f(),g("div",te,[e.currentStep===e.STEP.REGISTER?(f(),g("div",re,[s[15]||(s[15]=a("div",{class:"text-center mb-10"},[a("img",{src:se,width:100,height:100})],-1)),a("div",oe,[s[14]||(s[14]=a("h1",{class:"text-dark text-center"},"Register",-1)),i(x,{id:"change_pass_form",onSubmit:G(e.submit,["prevent"]),model:e.targetData,rules:e.rules,ref:"formRef",class:"form"},{default:v(()=>{var N,y;return[a("div",le,[a("div",ne,[s[7]||(s[7]=a("label",{class:"d-flex align-items-center fs-6 fw-semibold mb-2 text-break required"},"Email ",-1)),i(c,{class:"mt-auto"},{default:v(()=>[i(m,{class:"w-100",placeholder:"Enter email",modelValue:e.targetData.email,"onUpdate:modelValue":s[0]||(s[0]=o=>e.targetData.email=o),disabled:""},null,8,["modelValue"])]),_:1})]),a("div",ie,[s[8]||(s[8]=a("label",{class:"d-flex align-items-center fs-6 fw-semibold mb-2 text-break required"},"First Name ",-1)),i(c,{prop:"firstName",class:"mt-auto"},{default:v(()=>[i(m,{class:"w-100",modelValue:e.targetData.firstName,"onUpdate:modelValue":s[1]||(s[1]=o=>e.targetData.firstName=o),placeholder:"Enter first name",name:"firstName"},null,8,["modelValue"])]),_:1})]),a("div",de,[s[9]||(s[9]=a("label",{class:"d-flex align-items-center fs-6 fw-semibold mb-2 text-break required"},"Last Name ",-1)),i(c,{prop:"lastName",class:"mt-auto"},{default:v(()=>[i(m,{class:"w-100",modelValue:e.targetData.lastName,"onUpdate:modelValue":s[2]||(s[2]=o=>e.targetData.lastName=o),placeholder:"Enter last name",type:"input",name:"lastName"},null,8,["modelValue"])]),_:1})]),a("div",me,[s[10]||(s[10]=a("label",{class:"d-flex align-items-center fs-6 fw-semibold mb-2 text-break required"},"Password ",-1)),a("div",ce,[i(c,{prop:"newPassword",class:"mt-auto mb-0"},{default:v(()=>{var o;return[i(m,{size:"large",class:"w-100",modelValue:e.targetData.newPassword,"onUpdate:modelValue":s[3]||(s[3]=u=>e.targetData.newPassword=u),name:"newPassword",type:(o=e.targetData)!=null&&o.hideNewPassword?"password":"input"},null,8,["modelValue","type"])]}),_:1}),a("span",{class:"svg-icon svg-icon-1 position-absolute top-50 eye-icon",onClick:s[4]||(s[4]=()=>e.toggleEye("newPassword"))},[i(b,{icon:(N=e.targetData)!=null&&N.hideNewPassword?"hidePasswordIcon":"showPasswordIcon"},null,8,["icon"])])])]),a("div",ue,[s[11]||(s[11]=a("label",{class:"d-flex align-items-center fs-6 fw-semibold mb-2 required"}," Confirm Password ",-1)),a("div",pe,[i(c,{prop:"confirmPassword",class:"mt-auto mb-0"},{default:v(()=>{var o;return[i(m,{size:"large",class:"w-100",modelValue:e.targetData.confirmPassword,"onUpdate:modelValue":s[5]||(s[5]=u=>e.targetData.confirmPassword=u),name:"confirmPassword",type:(o=e.targetData)!=null&&o.hideConfirmPassword?"password":"input"},null,8,["modelValue","type"])]}),_:1}),a("span",{class:"svg-icon svg-icon-1 position-absolute top-50 eye-icon",onClick:s[6]||(s[6]=()=>e.toggleEye("confirmPassword"))},[i(b,{icon:(y=e.targetData)!=null&&y.hideConfirmPassword?"hidePasswordIcon":"showPasswordIcon"},null,8,["icon"])])])])]),a("div",fe,[s[12]||(s[12]=a("p",null,"New password must contain:",-1)),a("div",null,[(f(!0),g($,null,J(Object.entries(e.checkPassword),([o,u])=>(f(),g("div",{key:o,className:`d-flex mt-1 ${u.isValid?"valid":"invalid"}`},[i(b,{icon:u.isValid?"checkMarkIcon":"exclamationMarkIcon"},null,8,["icon"]),a("span",ge,z(u.text),1)],8,we))),128))])]),a("div",ve,[a("button",{"data-kt-indicator":e.loading?"on":null,class:"btn btn-sm btn-primary ms-auto",type:"submit",disabled:e.loading},[e.loading?C("",!0):(f(),g("span",be," Register ")),e.loading?(f(),g("span",he,s[13]||(s[13]=[O(" Please wait... "),a("span",{class:"spinner-border spinner-border-sm align-middle ms-2"},null,-1)]))):C("",!0)],8,Pe)])]}),_:1},8,["onSubmit","model","rules"])])])):e.currentStep===e.STEP.SUCCESS?(f(),B(I,{key:1,message:"Your new password is set",btnTitle:"Go to Home page",btnPath:"/"})):C("",!0)])}const Re=j(ae,[["render",Ne]]);export{Re as default};
