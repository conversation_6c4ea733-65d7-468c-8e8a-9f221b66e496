import { createApp } from 'vue'
import './style.css'
import App from './App.vue'
import router from "./router"
import { createPinia } from 'pinia';
import ApiService from './services/ApiService';
import i18n from './plugins/i18n';
import VueApexCharts from 'vue3-apexcharts';
import ElementPlus from 'element-plus';

const app = createApp(App);

app.use(createPinia());
app.use(router);
app.use(VueApexCharts);
app.use(ElementPlus)

ApiService.init(app);
app.use(i18n)



app.mount('#app');
