import ApiService from "../services/ApiService";
import JwtService from "../services/JwtService";
import { get, noop } from "lodash";
import { defineStore } from "pinia";
import { ref, watch } from "vue";
import { handleError } from "../utils/handleError";
import type { Callback } from "../types/common";

export const useAuthStore = defineStore("auth", () => {
  const isAuthenticated = ref(!!JwtService.getToken());

  watch(isAuthenticated, (newValue) => {
    const chatBoxBtn = document.getElementById("chatbase-bubble-button");
    const chatBubbles = document.getElementById("chatbase-message-bubbles");
    const chatWindow = document.getElementById("chatbase-bubble-window");
    if (chatBoxBtn) {
      chatBoxBtn.style.display = newValue ? "block" : "none";
    }

    if (chatBubbles) {
      chatBubbles.style.display = newValue ? "block" : "none";
    }

    if (chatWindow) {
      chatWindow.style.display = newValue ? "block" : "none";
    }
  });

  function setAuth(data: any) {
    isAuthenticated.value = true;
    // user.value = authUser;
    JwtService.saveToken(data?.token);
    JwtService.saveUserInfo(JSON.stringify(data?.profile));
  }

  function purgeAuth() {
    isAuthenticated.value = false;
    JwtService.destroyToken();
  }

  const login = async ({
    params,
    callback,
  }: {
    params: User.Login;
    callback: Callback;
  }): Promise<void> => {
    const onSuccess = get(callback, "onSuccess", noop);
    const onFinish = get(callback, "onFinish", noop);
    try {
      ApiService.removeHeader();
      const response = await ApiService.post("auth/login", params);
      setAuth(response.data?.data || response.data);
      onSuccess(response.data?.data || response.data);
    } catch (error) {
      handleError(error, callback);
    } finally {
      onFinish();
    }
  };

  function logout() {
    // return ApiService.post("auth/logout", {})
    //   .then(({ data }) => {
    //     purgeAuth();
    //   })
    //   .catch(({ response }) => {
    //     setError(response?.data);
    //   });

    purgeAuth();
  }

  function register({
    credentials,
    callback,
  }: {
    credentials: User.Register;
    callback: Callback;
  }) {
    const onSuccess = get(callback, "onSuccess", noop);
    const onFinish = get(callback, "onFinish", noop);
    ApiService.post("auth/register", credentials)
      .then(({ data }) => {
        onSuccess(data?.data || data);
      })
      .catch((error) => {
        handleError(error, callback);
      })
      .finally(() => {
        onFinish();
      });
  }

  const forgotPassword = async ({
    email,
    callback,
  }: {
    email: string;
    callback: Callback;
  }): Promise<void> => {
    const onSuccess = get(callback, "onSuccess", noop);
    const onFinish = get(callback, "onFinish", noop);

    try {
      const response = await ApiService.post("auth/forgot-password", { email });
      onSuccess(response.data?.data || response.data);
    } catch (error) {
      handleError(error, callback);
    } finally {
      onFinish();
    }
  };

  // function verifyAuth() {
  //   if (JwtService.getToken()) {
  //     ApiService.setHeader();
  //     ApiService.post("verify_token", { token: JwtService.getToken() })
  //       .then(({ data }) => {
  //         setAuth(data);
  //       })
  //       .catch(({ response }) => {
  //         setError(response.data.message);
  //         purgeAuth();
  //       });
  //   } else {
  //     purgeAuth();
  //   }
  // }

  const validateOTP = async ({
    params,
    callback,
  }: {
    params: User.ValidateOTP;
    callback: Callback;
  }): Promise<void> => {
    const onSuccess = get(callback, "onSuccess", noop);
    const onFinish = get(callback, "onFinish", noop);

    try {
      const response = await ApiService.post("auth/validate-otp", params);
      if (
        response.data?.data?.status !== "false" &&
        response.data?.data?.status !== false
      ) {
        onSuccess(response.data?.data || response.data);
      } else {
        handleError(response.data?.data, callback);
      }
    } catch (error) {
      handleError(error, callback);
    } finally {
      onFinish();
    }
  };

  const resetPassword = async ({
    params,
    callback,
  }: {
    params: User.ResetPassword;
    callback: Callback;
  }): Promise<void> => {
    const onSuccess = get(callback, "onSuccess", noop);
    const onFinish = get(callback, "onFinish", noop);

    try {
      const response = await ApiService.post("auth/reset-password", params);
      if (
        response.data?.data?.status !== "false" &&
        response.data?.data?.status !== false
      ) {
        onSuccess(response.data?.data || response.data);
      } else {
        handleError(response.data?.data, callback);
      }
    } catch (error) {
      handleError(error, callback);
    } finally {
      onFinish();
    }
  };

  const userResetPassword = async ({
    token,
    params,
    callback,
  }: {
    token: String;
    params: User.ResetNewPassword;
    callback: Callback;
  }): Promise<void> => {
    const onSuccess = get(callback, "onSuccess", noop);
    const onFinish = get(callback, "onFinish", noop);

    try {
      const response = await ApiService.put(
        `users/user-reset-password/${token}`,
        params
      );
      if (
        response.data?.data?.status !== "false" &&
        response.data?.data?.status !== false
      ) {
        onSuccess(response.data?.data || response.data);
      } else {
        handleError(response.data?.data, callback);
      }
    } catch (error) {
      handleError(error, callback);
    } finally {
      onFinish();
    }
  };

  return {
    isAuthenticated,
    login,
    logout,
    forgotPassword,
    purgeAuth,
    validateOTP,
    setAuth,
    register,
    resetPassword,
    userResetPassword,
    // verifyAuth,
  };
});
