<template>
  <div class="nozzles">
    <div class="bg-minicard-background text-minicard-text-light rounded-lg p-4">
      <h4 class="font-bold mb-2">Detail</h4>
      <div
        class="flex flex-col rounded-xl border border-dashed p-7 d-flex flex-column gap-3 text-sm"
      >
        <div v-if="loadingTFA" class="text-center">
          <div class="spinner-border text-primary" role="status">
            <span class="sr-only">Loading...</span>
          </div>
        </div>
        <template v-else>
          <h4 class="mb-0 font-semibold">TFA (Total Flow Area) (in^2)</h4>
          <div class="font-semibold">
            Calculated - Sum of areas of Nozzel section orifice size diameters =
            sum(pi*(d/2)^2)
          </div>
          <h3 class="font-semibold">
            {{ `${tfa || 0} (in^2)` }}
          </h3>
        </template>
      </div>
    </div>

    <div v-if="loading" class="text-center">
      <div class="spinner-border text-primary" role="status">
        <span class="sr-only">Loading...</span>
      </div>
    </div>
    <template v-else>
      <NoEntries
        v-if="nozzleList.length === 0"
        :addNew="toggleNewNozzleModal"
      />
      <div v-else class="mt-7 grid grid-cols-1 gap-4 md:grid-cols-2 md:gap-7 lg:grid-cols-3">
        <div
          v-for="item in nozzleList"
          :key="item.id"
          class="relative h-auto w-full bg-minicard-background text-minicard-text-light flex flex-col items-center p-4 rounded-xl border-t-2 border-active my-2 first:mt-4 last:mb-5 font-semibold md:first:mt-0 md:last:mb-0 md:m-0"
        >
          <div class="h-auto w-full flex flex-col gap-3">
            <h5 class="text-xl font-bold">{{ item.identificationNumber }}</h5>
            <div
              class="flex items-center justify-between border-dashed border-b-[1px] py-3"
            >
              <span>Orifice size</span>
              <span>
                {{ `${numberWithCommas(item.orificeSize)} (1/32in)` }}
              </span>
            </div>
          </div>
          <div
            class="flex flex-row items-center gap-2 absolute top-0 end-0 -translate-y-1/2"
          >
            <button
              class="p-2 rounded-full bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover"
              @click="toggleEditNozzleModal(item.id.toString())"
            >
              <span>
                <SvgIcon icon="pencilIcon" />
              </span>
            </button>
            <button
              class="p-2 rounded-full bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover"
              @click="deleteNozzle(item.id.toString())"
            >
              <span class="text-danger">
                <SvgIcon icon="trashIcon" />
              </span>
            </button>
          </div>
        </div>
      </div>
    </template>
  </div>
  <BottomTool :addNew="toggleNewNozzleModal" :showHelpWindow="showCustomize" />
  <NozzleModal
    :isVisible="isModalVisible"
    :close="toggleNewNozzleModal"
    ref="nozzleModal"
    :loadPage="loadPage"
  />
</template>

<script lang="ts">
import NoEntries from "@/components/NoEntries.vue";
import SvgIcon from "@/constants/SvgIcon.vue";
import { numberWithCommas } from "@/utils/numberFormatter";
import AlertService from "@/services/AlertService";
import { useNozzleStore } from "@/stores/nozzle";
import { defineComponent, inject, onMounted, ref, type Ref } from "vue";
import BottomTool from "@/components/common/BottomTool.vue";
import NozzleModal from "./NozzleModal.vue";
import type { Provide } from "@/types/injection-types";

export default defineComponent({
  name: "nozzles",
  components: {
    SvgIcon,
    BottomTool,
    NozzleModal,
    NoEntries,
  },
  props: {
    showCustomize: {
      type: Function,
      required: false,
    },
  },
  setup(_props) {
    const nozzleModal: Ref<any> = ref<typeof NozzleModal | null>(null);
    const nozzleStore = useNozzleStore();
    const loading = ref(false);
    const loadingTFA = ref(false);
    const nozzleList = ref<any>([]);
    const tfa = ref<string>("");
    const dailyReportProvide = inject<Provide.DailyReport>("dailyReport");
    const isModalVisible = ref(false);

    onMounted(() => {
      if (dailyReportProvide?.getDailyReportId()) {
        loadPage();
      }
    });

    const loadPage = () => {
      getNozzles();
      getNozzleTFA();
    };

    const getNozzles = async (): Promise<void> => {
      const params = {
        dailyReportId: dailyReportProvide?.getDailyReportId(),
        page: 1,
        limit: 200,
      };
      loading.value = true;

      nozzleStore.getNozzles({
        params: params,
        callback: {
          onSuccess: (res: any) => {
            nozzleList.value = res?.items;
          },
          onFinish: (_err: any) => {
            loading.value = false;
          },
        },
      });
    };

    const getNozzleTFA = async (): Promise<void> => {
      if (!dailyReportProvide?.getDailyReportId()) return;

      loadingTFA.value = true;

      nozzleStore.getNozzleTFA({
        dailyReportId: dailyReportProvide?.getDailyReportId(),
        callback: {
          onSuccess: (res: any) => {
            tfa.value = res;
          },
          onFinish: (_err: any) => {
            loadingTFA.value = false;
          },
        },
      });
    };

    const toggleNewNozzleModal = () => {
      isModalVisible.value = !isModalVisible.value;
    };

    const toggleEditNozzleModal = (id: string): void => {
      nozzleModal?.value?.setId(id);
      isModalVisible.value = !isModalVisible.value;
    };

    const deleteNozzle = (id: string) => {
      AlertService.deletionAlert({
        onConfirmed: () => {
          deleteNozzleById(id);
        },
      });
    };

    const deleteNozzleById = async (id: string): Promise<void> => {
      loading.value = true;
      nozzleStore.deleteNozzle({
        id: id,
        callback: {
          onSuccess: (_res: any) => {
            getNozzles();
          },
          onFinish: (_err: any) => {
            loading.value = false;
          },
        },
      });
    };

    return {
      tfa,
      loading,
      loadingTFA,
      nozzleList,
      nozzleModal,
      isModalVisible,
      loadPage,
      deleteNozzle,
      numberWithCommas,
      toggleEditNozzleModal,
      toggleNewNozzleModal,
    };
  },
});
</script>
