export enum FluidType {
  "Water" = 1,
  "Oil" = 2,
  "Synthetic" = 3,
}

export enum SampleFrom {
  "Active" = "1",
  "SuctionPit" = "2",
  "ReservePit" = "3",
  "Shakers" = "4",
  "MudTanks" = "5",
  "PumpSuction" = "6",
  "OtherLocations" = "7",
}

export const fluidTypeOptions = [
  {
    value: FluidType.Water,
    label: "Water",
  },
  {
    value: FluidType.Oil,
    label: "Oil",
  },
  {
    value: FluidType.Synthetic,
    label: "Synthetic",
  },
];

export const sampleFromOptions = [
  {
    value: SampleFrom.Active,
    label: "Active",
  },
  {
    value: SampleFrom.SuctionPit,
    label: "Suction Pit",
  },
  {
    value: SampleFrom.ReservePit,
    label: "Reserve Pit",
  },
  {
    value: SampleFrom.Shakers,
    label: "Shakers",
  },
  {
    value: SampleFrom.MudTanks,
    label: "Mud Tanks",
  },
  {
    value: SampleFrom.PumpSuction,
    label: "Pump Suction",
  },
  {
    value: SampleFrom.OtherLocations,
    label: "Other Locations",
  },
];
