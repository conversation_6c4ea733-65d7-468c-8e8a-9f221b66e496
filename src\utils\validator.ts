import * as yup from "yup";
interface ValidatorOptions {
  required?: boolean
  pattern?: RegExp
  errorsMessage?: ValidateErrorMessage
  length?: number
}

interface ValidateErrorMessage {
  required?: string
  pattern?: string
  length?: string
}

export class validator {
  public static isRequired(v: string | any | any[]): boolean {
    if (v instanceof Array) {
      return v != null && v != undefined && v.length > 0;
    }
    return v != "" && v != null && v != undefined;
  }

  public static isEqualsPattern(v: string, pattern: RegExp): boolean {
    if (v == null || v == undefined || v == "") return false;
    return pattern.test(v);
  }

  public static isEqualsLength(v: string, length: number): boolean {
    if (v == null || v == undefined || v == "") return false;
    return v.length >= length;
  }

  public static validate(
    v: string | any,
    validatorOptions: ValidatorOptions
  ): string {
    const res: any = "";

    if (validatorOptions.required && !this.isRequired(v)) {
      return (
        validatorOptions.errorsMessage?.required || "This field is required."
      );
    }

    if (
      validatorOptions.pattern &&
      !this.isEqualsPattern(v, validatorOptions.pattern)
    ) {
      return (
        validatorOptions.errorsMessage?.pattern ||
        "This field has incorrect format."
      );
    }

    if (
      validatorOptions.length &&
      !this.isEqualsLength(v, validatorOptions.length)
    ) {
      return (
        validatorOptions.errorsMessage?.length ||
        `This field is required at least ${validatorOptions.length} characters.`
      );
    }

    return res || null;
  }
}

export const yupValidate = {
  emailAddress: yup
    .string()
    .email("Invalid Email")
    .required("Please type Email"),
  mobilePhone: yup
    .string()
    .matches(/^\d+$/, "Phone number must contain only digits")
    .min(10, "Phone number must be at least 10 digits long")
    .max(15, "Phone number must be at most 15 digits long")
    .required("Phone number is required")
    .nullable(),
  officePhone: yup
    .string()
    .matches(/^\d+$/, "Phone number must contain only digits")
    .min(10, "Phone number must be at least 10 digits long")
    .max(15, "Phone number must be at most 15 digits long")
    .notRequired(),
};
