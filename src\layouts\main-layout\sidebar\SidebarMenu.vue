<template>
  <!--begin::sidebar menu-->
  <div class="h-full w-full flex flex-col bg-side">
    <div
      class="bg-sidebar-userinfo-background h-2/12 w-full flex flex-row items-center gap-2 px-6 py-3 md:text-xl"
    >
      <img
        class="h-4/5 w-auto rounded-2xl"
        :src="userProfile?.avatar || '/media/avatars/blank.png'"
      />
      <div class="flex flex-col">
        <div class="font-semibold">
          {{ `${userProfile?.firstName} ${userProfile?.lastName}` }}
        </div>
        <div class="text-xs font-semibold md:text-sm">
          {{ userProfile?.email }}
        </div>

        <div
          v-for="role in getUserRoles()"
          :key="role.value"
          class="font-semibold text-sm md:text-lg"
        >
          {{ role.label }}
        </div>
      </div>
      <div class="relative group">
        <div class="cursor-pointer">
          <SvgIcon icon="settingUser" classname="text-icon-light" />
        </div>
        <div
          class="hidden group-hover:block bg-popup-background h-auto w-auto px-2 py-3 rounded-lg absolute top-7 -left-7 z-20"
        >
          <div
            class="absolute -top-2 left-1/2 -translate-x-1/2 w-0 h-0 border-l-8 border-r-8 border-b-8 border-transparent border-b-popup-background"
          ></div>
          <router-link to="/my-profile">
            <span class="text-popup-text font-semibold">Settings</span>
          </router-link>
        </div>
      </div>
    </div>

    <div class="h-full w-full">
      <div class="pt-6 px-5">
        <template v-for="(menuItem, j) in menuConfig" :key="j">
          <div
            v-if="menuItem.sectionTitle && menuItem.route"
            :class="{ show: hasActiveChildren(menuItem.route) }"
          >
            <div>
              <router-link
                v-if="menuItem.route"
                class="group mx-2 px-2 py-3 bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover rounded-md font-semibold flex flex-row items-center gap-2"
                active-class="active"
                :to="menuItem.route"
                @click="closeSidebar"
              >
                <SvgIcon
                  :icon="menuItem.icon"
                  classname="group-hover:text-icon-dark"
                />

                <span class="menu-title group-hover:text-sidebar-text-hover">{{
                  translate(menuItem.sectionTitle)
                }}</span>
              </router-link>
            </div>
          </div>
        </template>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import SvgIcon from "../../../constants/SvgIcon.vue";
import { getOption } from "../../../utils/option";
import { userRoleOptions, UserType } from "../../../constants/user";
import { defineComponent, inject } from "vue";
import { useI18n } from "vue-i18n";
import { useRoute } from "vue-router";
import type { Option } from "../../../types/common";
import JwtService from "../../../services/JwtService";
import type { Provide } from "../../../types/injection-types";

export interface MenuItem {
  heading?: string;
  sectionTitle?: string;
  route?: string;
  pages?: Array<MenuItem>;
  icon?: string;
  sub?: Array<MenuItem>;
}

export default defineComponent({
  name: "sidebar-menu",
  components: { SvgIcon },
  props: {
    close: {
      type: Function,
      required: true,
    },
  },
  setup(props) {
    const { t, te } = useI18n();
    const route = useRoute();
    const userInfoProvide = inject<Provide.UserInfo>("userInfo");
    const userProfile = userInfoProvide?.userInfo;

    const closeSidebar = (): void => {
      props.close();
    };

    const getUserRoles = () => {
      if (userInfoProvide?.userInfo?.value?.roles) {
        return (
          userInfoProvide?.userInfo?.value?.roles?.map((item: Option) => {
            return {
              value: item?.value as UserType,
              label: getOption(item?.value, userRoleOptions)?.label,
            };
          }) || []
        );
      }
      return [];
    };

    const translate = (text: string) => {
      if (te(text)) {
        return t(text);
      } else {
        return text;
      }
    };

    const hasActiveChildren = (match: string) => {
      return route.path.indexOf(match) !== -1;
    };

    return {
      userProfile,
      userRoleOptions,
      getUserRoles,
      translate,
      hasActiveChildren,
      closeSidebar,
    };
  },
  data() {
    const fullMenu = [
      {
        sectionTitle: "Home",
        route: "/wells/overview",
        icon: "homeMenu",
      },
      {
        sectionTitle: "Companies",
        route: "/companies",
        icon: "customerMenu",
      },
      {
        sectionTitle: "My Company",
        route: "/my-company",
        icon: "customerMenu",
      },
      {
        sectionTitle: "User Management",
        route: "/users",
        icon: "useMenu",
      },
      {
        sectionTitle: "Settings",
        route: "/settings",
        icon: "settingMenu",
      },
    ];

    const getMenuConfig = (): Array<MenuItem> => {
      const menu = [fullMenu[0]];

      if (JwtService.checkRole(UserType.SystemAdmin)) {
        menu.push(fullMenu[1]);
      }

      if (
        JwtService.checkRole(UserType.CompanyAdmin) ||
        JwtService.checkRole(UserType.Supervisor) ||
        JwtService.checkRole(UserType.Engineer)
      ) {
        menu.push(fullMenu[2]);
      }

      menu.push(fullMenu[3]);

      if (
        JwtService.checkRole(UserType.SystemAdmin) ||
        JwtService.checkRole(UserType.CompanyAdmin) ||
        JwtService.checkRole(UserType.Supervisor)
      ) {
        menu.push(fullMenu[4]);
      }

      return menu;
    };
    return {
      menuConfig: getMenuConfig(),
    };
  },
});
</script>
