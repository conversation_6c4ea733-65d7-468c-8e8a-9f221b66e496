<template>
  <div class="bg-card-background text-card-text rounded-lg p-4 lg:w-4/5 lg:mx-auto lg:min-w-[1560px]">
    <!--begin::Card header-->
    <div class="h-auto w-full flex flex-col gap-2 items-start md:flex-row md:gap-4 md:items-center md:p-4">
      <h1 class="text-lg font-bold">Customer</h1>
      <div class="h-auto w-full">
        <el-form @submit.prevent="searchCustomer">
          <el-form-item class="mb-0">
            <el-input
              placeholder="Search"
              v-model="search"
              name="search"
              size="large"
              ><template #prefix>
                <el-icon class="el-input__icon">
                  <SvgIcon icon="searchIcon" />
                </el-icon> </template
            ></el-input> </el-form-item
        ></el-form>
      </div>
      <div class="h-auto w-full flex flex-row justify-end gap-4">
        <button
          class="bg-danger rounded-md px-4 py-2 font-semibold"
          v-if="checkedRows.length !== 0 && isAdmin()"
          @click="onRemove"
        >
          Remove
        </button>
        <button
          class="bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover rounded-md px-4 py-2 font-semibold flex flex-row items-center gap-2 justify-between"
          @click="toggleNewCustomer"
          v-if="isAdmin()"
        >
          <SvgIcon icon="addIcon" />
          New
        </button>
      </div>
    </div>
    <!--end::Card header-->
    <div v-if="loading" class="text-center p-5">
      <div class="spinner-border text-primary" role="status">
        <span class="sr-only">Loading...</span>
      </div>
    </div>
    <el-empty v-else-if="customerList.length === 0" description="No Data" />
    <div v-else class="h-auto w-full overflow-x-scroll p-4 md:overflow-hidden">
      <!--begin::Table-->
      <table class="md:w-11/12 md:mx-auto md:text-center">
        <!--begin::Table head-->
        <thead class="font-bold whitespace-nowrap">
          <tr class="font-bold">
            <TableHeader
              :headers="tableHeader"
              :sortBy="sortParams.sortBy!"
              :sortDirection="sortParams.sortDirection!"
              :onSort="onSort"
              ><template v-slot:customHeader="{ header }">
                <div v-if="header.label === ''">
                  <div
                    class="form-check form-check-sm form-check-custom form-check-solid"
                  >
                    <input
                      class="h-4 w-4"
                      type="checkbox"
                      v-model="checkAll"
                      @change="onToggleCheckAll"
                    />
                  </div>
                </div>
                <div v-else class="p-4">
                  {{ header.label }}
                </div>
              </template></TableHeader
            >
          </tr>
        </thead>
        <!--end::Table head-->

        <!--begin::Table body-->
        <tbody>
          <template v-for="item in customerList" :key="item.id">
            <tr class="font-bold my-2 text-center border-b-[1px] border-dashed border-light-border">
              <td v-if="isAdmin()">
                <input
                  class="h-4 w-4"
                  type="checkbox"
                  :value="item.id"
                  v-model="checkedRows"
                />
              </td>

              <td class="w-36 p-4">
                <div class="flex flex-row items-center gap-3">
                  <router-link
                    :to="getCustomerDetailPath(item.id!)"
                    class="font-semibold hover:text-primary"
                    >{{ item?.customerName }}</router-link
                  >
                </div>
              </td>

              <td class="p-4">
                <span
                  class="font-semibold line-clamp-4 hover:line-clamp-none cursor-pointer border-b-[1px]"
                  :class="{ 'line-clamp-none border-none': expandedNotes[item.id!] }"
                  @click="toggleNoteExpansion(item.id!)"
                >{{ item?.notes }}</span>
              </td>
              <td>
                <span class="font-semibold">{{
                  formatDate(item?.createdAt, "MMM DD, YYYY")
                }}</span>
              </td>

              <td v-if="isAdmin()">
                <div
                  class="h-auto w-full flex flex-row items-center justify-evenly gap-2"
                >
                  <button
                    class="bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover font-semibold h-8 w-8 flex items-center justify-center rounded-lg pl-[7.5px] pt-[1px] md:h-10 md:w-10 md:pl-[3px] md:pt-[5px] md:pb-[3.75px]"
                    @click="toggleEditCustomer(item?.id!)"
                  >
                    <SvgIcon icon="newReportIcon" classname="md:h-6 md:w-6"/>
                  </button>
                  <button
                    class="bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover font-semibold h-8 w-8 flex items-center justify-center rounded-lg pl-[7.5px] pt-[1px] md:h-10 md:w-10 md:pl-[3px] md:pb-[6px]"
                    @click="deleteCustomer(item?.id!)"
                  >
                    <span class="text-danger">
                      <SvgIcon icon="trashIcon" classname="md:h-7 md:w-7"/>
                    </span>
                  </button>
                </div>
              </td>
            </tr>
          </template>
        </tbody>
        <!--end::Table body-->
      </table>
      <!--end::Table-->
    </div>
    <div class="flex flex-col items-center my-5">
      <div v-if="customerList?.length" class="font-semibold">
        {{
          `Showing ${(currentPage - 1) * 10 + 1} to ${
            customerList?.length
          } of ${totalElements} entries`
        }}
      </div>
      <TablePagination
        v-if="pageCount >= 1"
        :total-pages="pageCount"
        :total="totalElements"
        :per-page="10"
        :current-page="currentPage"
        @page-change="pageChange"
      />
    </div>
  </div>
  <CustomerModal
    :isVisible="isModalVisible"
    :close="toggleNewCustomer"
    ref="customerModal"
    :loadPage="getCompanyCustomer"
  />
</template>

<script lang="ts">
import PageHeader from "@/components/PageHeader.vue";
import TablePagination from "@/components/common/TablePagination.vue";
import SvgIcon from "@/constants/SvgIcon.vue";
import TableHeader from "@/components/common/TableHeader.vue";
import { SortByEnum, SortDirectionEnum } from "@/constants/table";
import { formatDate } from "@/utils/date";
import AlertService from "@/services/AlertService";
import JwtService, { isAdmin } from "@/services/JwtService";
import { useCustomerStore } from "@/stores/customer";
import { defineComponent, onMounted, ref, type Ref, watch } from "vue";
import { useRoute, useRouter } from "vue-router";
import CustomerModal from "./CustomerModal.vue";

export default defineComponent({
  name: "customer-list",
  components: {
    PageHeader,
    SvgIcon,
    TablePagination,
    CustomerModal,
    TableHeader,
  },
  setup() {
    const customerStore = useCustomerStore();
    const route = useRoute();
    const router = useRouter();
    const checkedRows = ref<Array<string>>([]);
    const checkAll = ref<boolean>(false);
    const pageCount = ref(0);
    const totalElements = ref(0);
    const currentPage = ref(1);
    const loading = ref(false);
    const search = ref<string>("");
    const isShowModal = ref(false);
    const customerModal: Ref<any> = ref<typeof CustomerModal | null>(null);
    const searchList = ref<Array<any>>([]);
    const customerList = ref<Customer.Info[]>([]);
    const isMyCompany = route.name === "my-company";
    const sortParams = ref<Customer.GetFilter>({
      sortDirection: SortDirectionEnum.ASC,
      sortBy: SortByEnum.CustomerName,
    });
    const isModalVisible = ref(false);
    const expandedNotes = ref<Record<string, boolean>>({});

    const tableHeader: Table.ColumnHeader[] = [
      { label: "", class: "w-25px", display: isAdmin() },
      {
        label: "FULL NAME",
        sortBy: SortByEnum.CustomerName,
        class: "min-w-150px",
      },
      { label: "NOTE", class: "min-w-150px" },
      { label: "CREATED DATE", class: "min-w-150px" },
      { label: "ACTIONS", class: "min-w-60px", display: isAdmin() },
    ];

    onMounted(() => {
      getCompanyCustomer();
    });

    watch(checkedRows, (newValue) => {
      checkAll.value =
        customerList.value.length !== 0 &&
        newValue.length === customerList.value.length;
    });

    watch(currentPage, () => {
      getCompanyCustomer();
    });

    const getCompanyCustomer = async (): Promise<void> => {
      loading.value = true;

      customerStore.getCustomers({
        params: {
          name: search.value.trim() || null,
          companyId: isMyCompany
            ? JwtService.getUserInfo()?.companyId
            : route.params?.id?.toString(),

          page: currentPage.value,
          limit: 10,
          ...sortParams.value,
        },
        callback: {
          onSuccess: (res: any) => {
            customerList.value = [...res?.items];
            pageCount.value = res?.totalPage;
            totalElements.value = res?.total;
            currentPage.value = res?.page;
          },
          onFinish: () => {
            loading.value = false;
          },
        },
      });
    };

    const onSort = (newSortValue: Filter.FilterForm) => {
      sortParams.value = { ...newSortValue };

      getCompanyCustomer();
    };

    const searchCustomer = () => {
      if (currentPage.value !== 1) {
        currentPage.value = 1;
      } else {
        getCompanyCustomer();
      }
    };

    const toggleNewCustomer = (): void => {
      isModalVisible.value = !isModalVisible.value;
    };

    const toggleEditCustomer = (id: string): void => {
      isModalVisible.value = !isModalVisible.value;
      customerModal?.value?.setId(id);
    };

    const onRemove = () => {
      AlertService.deletionAlert({
        onConfirmed: () => {
          removeCustomers(checkedRows.value, true);
        },
      });
    };

    const deleteCustomer = (id: string) => {
      AlertService.deletionAlert({
        onConfirmed: () => {
          removeCustomers([id]);
        },
      });
    };

    const pageChange = (newPage: number) => {
      currentPage.value = newPage;
    };
    const toggleFilter = (): void => {
      isShowModal.value = !isShowModal.value;
    };

    const onToggleCheckAll = (e: any) => {
      if (e?.target?.checked) {
        checkedRows.value = customerList.value.map((user) => user.id!);
      } else {
        checkedRows.value = [];
      }
    };

    const view = (id: string) => {
      router.push({ path: `/users/${id}` });
    };

    const getCustomerDetailPath = (customerId: string) => {
      if (route.name === "company-detail") {
        return `/companies/${route.params.id}/customer/${customerId}`;
      } else if (route.name === "my-company") {
        return `/my-company/customer/${customerId}`;
      }

      return "#";
    };

    const removeCustomers = async (
      customerIds: string[],
      clearRemoveList = false
    ): Promise<void> => {
      loading.value = true;

      customerStore.removeCustomers({
        customerIds,
        callback: {
          onSuccess: (_res: any) => {
            if (clearRemoveList) {
              checkedRows.value = [];
            }
            getCompanyCustomer();
          },
          onFinish: () => {
            loading.value = false;
          },
        },
      });
    };

    const toggleNoteExpansion = (customerId: string) => {
      expandedNotes.value[customerId] = !expandedNotes.value[customerId];
    };

    return {
      sortParams,
      tableHeader,
      search,
      loading,
      customerModal,
      checkedRows,
      checkAll,
      customerList,
      currentPage,
      totalElements,
      pageCount,
      isShowModal,
      searchList,
      isModalVisible,
      expandedNotes,
      pageChange,
      toggleFilter,
      deleteCustomer,
      formatDate,
      view,
      onRemove,
      toggleNewCustomer,
      toggleEditCustomer,
      isAdmin,
      getCustomerDetailPath,
      onToggleCheckAll,
      getCompanyCustomer,
      searchCustomer,
      onSort,
      toggleNoteExpansion,
    };
  },
});
</script>
