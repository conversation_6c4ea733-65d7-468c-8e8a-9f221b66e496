<template>
  <div
    v-if="isVisible"
    class="fixed top-0 right-0 bottom-0 left-0 z-40 flex items-center"
  >
    <!-- Dark overlay background -->
    <div class="absolute inset-0 bg-sidebar-background opacity-25"></div>
    <div
      class="relative bg-card-background text-card-text h-auto w-11/12 flex flex-col items-center gap-3 mx-auto p-5 rounded-xl md:w-4/5 md:text-lg lg:w-2/5"
    >
      <div class="h-auto w-full flex flex-row items-center justify-between">
        <h3 class="text-lg font-bold">
          {{ id ? "Edit Customer" : "Add Customer" }}
        </h3>
        <span class="cursor-pointer" @click="closeModal">
          <SvgIcon icon="closeModalIcon" />
        </span>
      </div>

      <el-form
        id="product_form"
        @submit.prevent="submit"
        :model="targetData"
        :rules="rules"
        ref="formRef"
        class="h-auto w-full"
      >
        <div class="flex flex-col gap-2">
          <label class="font-semibold"
            >Customer/Company Name
            <span class="text-danger-active font-light">*</span></label
          >
          <el-form-item prop="customerName">
            <el-input
              v-model="targetData.customerName"
              placeholder=""
              name="customerName"
            ></el-input>
          </el-form-item>
        </div>
        <div class="flex flex-col gap-2">
          <label class="font-semibold">Note </label>
          <el-form-item prop="notes">
            <el-input
              v-model="targetData.notes"
              placeholder=""
              name="notes"
              type="textarea"
              :rows="3"
            ></el-input>
          </el-form-item>
        </div>
        <div class="flex flex-row items-start mt-4 gap-3">
          <button
            type="button"
            class="bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover rounded-md px-4 py-2 font-semibold"
            @click="closeModal"
            :disabled="loading"
          >
            Discard
          </button>
          <button
            class="bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover rounded-md px-4 py-2 font-semibold"
            type="submit"
            :disabled="loading"
          >
            <span v-if="!loading" class="indicator-label"> Save </span>
            <span v-if="loading" class="indicator-progress">
              Please wait...
              <span
                class="spinner-border spinner-border-sm align-middle ms-2"
              ></span>
            </span>
          </button>
        </div>
      </el-form>
    </div>
  </div>
</template>

<script lang="ts">
import SvgIcon from "@/constants/SvgIcon.vue";
import JwtService from "@/services/JwtService";
import { useCustomerStore } from "@/stores/customer";
import type { FormRules } from "element-plus";
import { defineComponent, ref, watch } from "vue";
import { useRoute } from "vue-router";

export default defineComponent({
  name: "customer-modal",
  components: { SvgIcon },
  props: {
    isVisible: {
      type: Boolean,
      required: true,
    },
    close: {
      type: Function,
      required: true,
    },
    loadPage: {
      type: Function,
      required: false,
    },
  },
  setup(props) {
    const route = useRoute();
    const targetData = ref({
      customerName: "",
      notes: "",
    });
    const formRef = ref<null | HTMLFormElement>(null);
    const loading = ref<boolean>(false);
    const customerStore = useCustomerStore();
    const id = ref("");
    const companyDetail = ref("");

    watch(
      () => props.isVisible,
      (newValue) => {
        if (newValue === false) {
          reset();
        }
      }
    );

    watch(id, (newValue) => {
      if (newValue !== "") {
        getCustomerDetails();
      }
    });

    const getCustomerDetails = async (): Promise<void> => {
      if (!id.value) return;
      customerStore.getCustomerDetails({
        id: id.value,
        callback: {
          onSuccess: (res: any) => {
            companyDetail.value = res;
            targetData.value = {
              customerName: res?.customerName,
              notes: res?.notes,
            };
          },
        },
      });
    };

    const closeModal = () => {
      props.close();
    };

    const setId = (newId: string) => {
      id.value = newId;
    };

    const rules = ref<FormRules<any>>({
      customerName: [
        {
          required: true,
          message: "Please type Customer Name",
          trigger: ["blur", "change"],
        },
      ],
    });

    const submit = () => {
      if (!formRef.value) {
        return;
      }

      formRef.value.validate((valid: boolean) => {
        if (valid) {
          if (id.value) {
            editCustomer();
          } else {
            createCustomer();
          }
        }
      });
    };

    const reset = () => {
      id.value = "";
      targetData.value = {
        customerName: "",
        notes: "",
      };
      formRef?.value?.resetFields();
    };

    const createCustomer = async (): Promise<void> => {
      loading.value = true;
      customerStore.createCustomer({
        params: {
          ...targetData.value,
          companyId:
            route.name === "my-company"
              ? JwtService.getUserInfo()?.companyId
              : route.params?.id?.toString(),
        },
        callback: {
          onSuccess: (_res: any) => {
            closeModal();
            props?.loadPage?.();
          },
          onFinish: () => {
            loading.value = false;
          },
        },
      });
    };

    const editCustomer = async (): Promise<void> => {
      if (!id.value) return;
      loading.value = true;
      customerStore.updateCustomer({
        id: id.value,
        params: targetData.value,
        callback: {
          onSuccess: (_res: any) => {
            closeModal();
            props?.loadPage?.();
          },
          onFinish: () => {
            loading.value = false;
          },
        },
      });
    };
    return {
      id,
      rules,
      loading,
      targetData,
      formRef,
      companyDetail,
      submit,
      setId,
      reset,
      closeModal,
    };
  },
});
</script>
