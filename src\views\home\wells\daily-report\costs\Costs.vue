<template>
  <div
    class="bg-card-background text-card-text-light w-11/12 mx-auto mb-4 h-auto rounded-xl"
  >
    <div class="h-auto w-11/12 flex flex-col gap-6 mx-auto mt-7 px-3 py-4">
      <h1 class="font-bold">Costs</h1>

      <div v-if="loading" class="text-center">
        <div class="spinner-border text-primary" role="status">
          <span class="sr-only">Loading...</span>
        </div>
      </div>
      <div v-else>
        <NoEntries v-if="costList.length === 0" :addNew="toggleAddCostModal" />
        <div
          v-else
          class="h-auto w-full grid grid-cols-1 gap-4 md:grid-cols-2 md:gap-7 lg:grid-cols-3"
        >
          <div
            v-for="item in costList"
            :key="item?.id"
            class="relative h-auto w-full bg-minicard-background text-minicard-text-light p-4 rounded-xl border-t-2 border-active my-2 first:mt-4 last:mb-5 font-semibold md:first:mt-0 md:last:mb-0 md:m-0"
          >
            <div class="h-auto w-full p-4 flex flex-col gap-2">
              <h5 class="font-bold">
                {{ item?.costSetting?.name }}
              </h5>

              <div
                class="flex items-center justify-between border-dashed border-b-[1px] py-3"
              >
                <span>Created Date</span>
                <span class="text-minicard-text-dark">
                  {{ `${formatDate(item?.createdAt, "DD MMM YYYY")}` }}
                </span>
              </div>
              <div
                class="flex items-center justify-between border-dashed border-b-[1px] py-3"
              >
                <span>Unit</span>
                <span class="text-minicard-text-dark">
                  {{ item?.unit }}
                </span>
              </div>
              <div
                class="flex items-center justify-between border-dashed border-b-[1px] py-3"
              >
                <span>Quantity</span>
                <span class="text-minicard-text-dark">
                  {{ numberWithCommas(item?.quantity) }}
                </span>
              </div>
              <div class="flex items-center justify-between pt-3">
                <span>Cost</span>
                <span class="text-minicard-text-dark">
                  {{ numberWithCommas(item?.costSetting?.cost) }}
                </span>
              </div>
            </div>
            <div
              class="flex flex-row items-center gap-2 absolute top-0 end-0 -translate-y-1/2"
            >
              <button
                class="p-2 rounded-full bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover"
                @click="toggleEditCostModal(item?.id)"
              >
                <span class="svg-icon svg-icon-3">
                  <SvgIcon icon="pencilIcon" />
                </span>
              </button>
              <button
                class="p-2 rounded-full bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover"
                @click="deleteCost(item?.id)"
              >
                <span class="svg-icon svg-icon-3 text-danger">
                  <SvgIcon icon="trashIcon" />
                </span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <BottomTool :addNew="toggleAddCostModal" :showHelpInfo="false" />
  <CostModal
    :isVisible="isModalVisible"
    :close="toggleAddCostModal"
    ref="costModal"
    :loadPage="getCosts"
  />
</template>

<script lang="ts">
import NoEntries from "@/components/NoEntries.vue";
import SvgIcon from "@/constants/SvgIcon.vue";
import { formatDate } from "@/utils/date";
import { numberWithCommas } from "@/utils/numberFormatter";
import AlertService from "@/services/AlertService";
import { useCostStore } from "@/stores/cost";
import { defineComponent, inject, onMounted, ref, type Ref } from "vue";
import BottomTool from "@/components/common/BottomTool.vue";
import CostModal from "./CostModal.vue";
import type { Provide } from "@/types/injection-types";

export default defineComponent({
  name: "costs",
  components: {
    SvgIcon,
    BottomTool,
    CostModal,
    NoEntries,
  },
  props: {
    setChildActiveTab: {
      type: Function,
      required: false,
      default: () => {},
    },
  },
  setup(_props) {
    const costStore = useCostStore();
    const costModal: Ref<any> = ref<typeof CostModal | null>(null);
    const loading = ref(false);
    const costList = ref<any>([]);
    const dailyReportProvide = inject<Provide.DailyReport>("dailyReport");
    const isModalVisible = ref(false);

    onMounted(() => {
      if (dailyReportProvide?.getDailyReportId()) {
        getCosts();
      }
    });

    const getCosts = async (
      params = {
        dailyReportId: dailyReportProvide?.getDailyReportId(),
        page: 1,
        limit: 200,
      }
    ): Promise<void> => {
      loading.value = true;

      costStore.getCosts({
        params: params,
        callback: {
          onSuccess: (res: any) => {
            costList.value = JSON.parse(JSON.stringify(res?.items));
          },
          onFinish: (_err: any) => {
            loading.value = false;
          },
        },
      });
    };

    const toggleAddCostModal = () => {
      isModalVisible.value = !isModalVisible.value;
    };

    const toggleEditCostModal = (id: string) => {
      costModal?.value?.setId(id);
      isModalVisible.value = !isModalVisible.value;
    };

    const deleteCost = (id: string) => {
      AlertService.deletionAlert({
        onConfirmed: () => {
          deleteCostById(id);
        },
      });
    };

    const deleteCostById = (id: string) => {
      loading.value = true;
      costStore.deleteCost({
        id: id,
        callback: {
          onSuccess: (_res: any) => {
            getCosts();
          },
          onFinish: (_err: any) => {
            loading.value = false;
          },
        },
      });
    };

    return {
      loading,
      costList,
      costModal,
      isModalVisible,
      numberWithCommas,
      toggleAddCostModal,
      toggleEditCostModal,
      deleteCost,
      formatDate,
      getCosts,
    };
  },
});
</script>
