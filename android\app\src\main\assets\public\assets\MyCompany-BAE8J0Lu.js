import{P as C}from"./PageHeader-Sj9hJFB8.js";import{d as f,q as l,x as D,J as g,_ as b,c,l as y,b as v,m as _,a as d,F as k,r as u,o as p}from"./index-BmHWvWFS.js";import{u as B}from"./company-KQxnMnUF.js";import{C as M}from"./CompanyDetail-BzJa4nxJ.js";import"./handleFailure-WBgBpurp.js";import"./CompanyModal-C5rLKitg.js";import"./SvgIcon-DYvlNVZf.js";import"./TablePagination-lslz6s2e.js";import"./TableHeader-DGdsllig.js";import"./table-bhK9qpe4.js";import"./date-CKteeARj.js";import"./customer-2JcOh_4Q.js";import"./CustomerModal-DQWo9a5A.js";import"./user-CVSNmFaf.js";import"./index.esm-C4vtr4xS.js";import"./AssignUserModal-kk-OATZ2.js";import"./UserModal-Cgio3UOU.js";import"./validator-D_t2fUhD.js";const P=f({name:"my-company",components:{CompanyDetail:M,PageHeader:C},setup(){const e=l(!1),o=B(),t=l();D(()=>{n()});const n=async()=>{var m,a;const r=(a=(m=g)==null?void 0:m.getUserInfo())==null?void 0:a.companyId;r&&(e.value=!0,o.getCompanyById({id:r,callback:{onSuccess:s=>{t.value=s},onFinish:()=>{e.value=!1}}}))};return{loading:e,companyDetail:t,getCompanyDetails:n}}}),S={key:0,class:"text-center my-auto"};function $(e,o,t,n,r,m){var i;const a=u("PageHeader"),s=u("CompanyDetail");return p(),c(k,null,[e.loading?(p(),c("div",S,o[0]||(o[0]=[d("div",{class:"spinner-border text-primary",role:"status"},[d("span",{class:"sr-only"},"Loading...")],-1)]))):y("",!0),v(a,{title:"My Company",breadcrumbs:["My Company",((i=e.companyDetail)==null?void 0:i.name)||""]},null,8,["breadcrumbs"]),!e.loading&&e.companyDetail?(p(),_(s,{key:1,companyDetail:e.companyDetail,reloadCompanyData:e.getCompanyDetails},null,8,["companyDetail","reloadCompanyData"])):y("",!0)],64)}const O=b(P,[["render",$]]);export{O as default};
