import{P as X}from"./PageHeader-l8HvxxsN.js";import{S as E}from"./SvgIcon-CfrWCA-H.js";import{E as N,w as Y}from"./daily-report-BYo3Yy2S.js";import{O as U,P as c,Q as I,d as j,q as y,A as C,_ as A,c as $,l as M,o as w,a as o,b as v,t as V,r as P,w as S,p as R,B as k,x as W,E as L,m as H,F as q,k as B,R as Z,j as T,J as ee,U as te,S as ne,n as oe}from"./index-DalLS0_6.js";import{u as le}from"./well-8ACiu0oO.js";import{W as G}from"./WellGeneralInfo-CntYZnXa.js";import{T as O}from"./TablePagination-BmkwndgK.js";import{h as F}from"./handleFailure-DrOe_u9W.js";import{n as se}from"./numberFormatter-C7uP7NWj.js";import"./date-CCTVzEJd.js";import"./navigation-guard-9jXpDdGb.js";import"./company-DGE9srvS.js";import"./customer-CD9RajQq.js";import"./user-UjS69U41.js";const z=U("interval",()=>({getIntervals:async({params:l,callback:s})=>{var a;const r=c.get(s,"onSuccess",c.noop),i=c.get(s,"onFinish",c.noop);try{I.setHeader();const t=await I.getWithParams("intervals",l);r(((a=t.data)==null?void 0:a.data)||t.data)}catch(t){F(t,s)}finally{i()}},getIntervalDetails:async({id:l,callback:s})=>{var a;const r=c.get(s,"onSuccess",c.noop),i=c.get(s,"onFinish",c.noop);try{I.setHeader();const t=await I.get(`intervals/${l}`);r(((a=t.data)==null?void 0:a.data)||t.data)}catch(t){F(t,s)}finally{i()}},updateInterval:async({id:l,params:s,callback:r})=>{var t;const i=c.get(r,"onSuccess",c.noop),a=c.get(r,"onFinish",c.noop);try{I.setHeader();const d=await I.put(`intervals/${l}`,s);i(((t=d.data)==null?void 0:t.data)||d.data)}catch(d){F(d,r)}finally{a()}},deleteInterval:async({id:l,callback:s})=>{var a;const r=c.get(s,"onSuccess",c.noop),i=c.get(s,"onFinish",c.noop);try{I.setHeader();const t=await I.delete(`intervals/${l}`);r(((a=t.data)==null?void 0:a.data)||t.data)}catch(t){F(t,s)}finally{i()}},createInterval:async({params:l,callback:s})=>{var a;const r=c.get(s,"onSuccess",c.noop),i=c.get(s,"onFinish",c.noop);try{I.setHeader();const t=await I.post("intervals",l);r(((a=t.data)==null?void 0:a.data)||t.data)}catch(t){F(t,s)}finally{i()}}})),ae=j({name:"interval-modal",components:{SvgIcon:E},props:{isVisible:{type:Boolean,required:!0},close:{type:Function,required:!0},loadPage:{type:Function,default:()=>{},required:!0},wellId:{type:String,required:!0}},setup(e){const n=z(),f=y(!1),m=y(""),b=y({interval:null,notes:""}),l=y(null),s=y(!1);C(m,g=>{g!==""&&r()}),C(f,g=>{var p;g===!1&&(m.value="",D(),(p=l==null?void 0:l.value)==null||p.resetFields())});const r=async()=>{n.getIntervalDetails({id:m.value,callback:{onSuccess:g=>{b.value=g}}})},i=async g=>{s.value=!0,n.updateInterval({id:m.value,params:g,callback:{onSuccess:p=>{e!=null&&e.loadPage&&(e==null||e.loadPage()),e.close()},onFinish:p=>{s.value=!1}}})},a=async g=>{s.value=!0,n.createInterval({params:g,callback:{onSuccess:p=>{e!=null&&e.loadPage&&(e==null||e.loadPage()),e.close()},onFinish:p=>{s.value=!1}}})},t=()=>{e.close()},d=g=>{m.value=g.toString()},x=y({interval:[{required:!0,message:"Please input interval number",trigger:"blur"}],notes:[{required:!0,message:"Please input notes",trigger:"blur"}]}),h=()=>{l.value&&l.value.validate(g=>{g&&(s.value=!0,m!=null&&m.value?i(b.value):a({...b.value,wellId:e==null?void 0:e.wellId}))})},D=()=>{b.value={interval:null,notes:""}};return{id:m,modal:f,rules:x,loading:s,targetData:b,formRef:l,submit:h,setId:d,reset:D,closeModal:t}}}),re={key:0,class:"fixed top-0 right-0 bottom-0 left-0 z-40 flex items-center"},ie={class:"relative bg-card-background text-card-text max-h-11/12 w-11/12 flex flex-col items-center gap-3 mx-auto p-5 rounded-xl md:w-4/5 md:text-lg lg:w-2/5 lg:h-auto"},de={class:"h-auto w-full flex flex-row items-center justify-between"},ue={class:"text-lg font-bold md:text-xl"},ce={class:"flex flex-col gap-2"},me={class:"flex flex-row items-start mt-4 gap-3"},ge=["disabled"],pe=["data-kt-indicator","disabled"],ve={key:0,class:"indicator-label"},fe={key:1,class:"indicator-progress"};function be(e,n,f,m,b,l){const s=P("SvgIcon"),r=P("el-input-number"),i=P("el-form-item"),a=P("el-input"),t=P("el-form");return e.isVisible?(w(),$("div",re,[n[7]||(n[7]=o("div",{class:"absolute inset-0 bg-sidebar-background opacity-25"},null,-1)),o("div",ie,[o("div",de,[o("h3",ue,V(`${e.id?"Edit Interval":"New Interval"}`),1),o("span",{class:"cursor-pointer",onClick:n[0]||(n[0]=(...d)=>e.closeModal&&e.closeModal(...d))},[v(s,{icon:"closeModalIcon"})])]),v(t,{id:"interval_form",onSubmit:R(e.submit,["prevent"]),model:e.targetData,rules:e.rules,ref:"formRef",class:"h-auto w-full"},{default:S(()=>[o("div",null,[v(i,{prop:"interval"},{default:S(()=>[n[4]||(n[4]=o("label",{class:"font-semibold pr-6 md:text-lg"},[k("Interval number"),o("span",{class:"text-danger-active font-light"},"*")],-1)),v(r,{type:"number",controls:!1,min:0,step:1,modelValue:e.targetData.interval,"onUpdate:modelValue":n[1]||(n[1]=d=>e.targetData.interval=d),placeholder:"",name:"interval"},null,8,["modelValue"])]),_:1})]),o("div",ce,[n[5]||(n[5]=o("label",{class:"font-semibold"},[k("Notes"),o("span",{class:"text-danger-active font-light"},"*")],-1)),v(i,{prop:"notes"},{default:S(()=>[v(a,{modelValue:e.targetData.notes,"onUpdate:modelValue":n[2]||(n[2]=d=>e.targetData.notes=d),type:"textarea",rows:"10",name:"notes",placeholder:"Type Notes"},null,8,["modelValue"])]),_:1})]),o("div",me,[o("button",{type:"button",class:"bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover rounded-md px-4 py-2 font-semibold md:px-4 md:py-3 md:text-xl",onClick:n[3]||(n[3]=(...d)=>e.closeModal&&e.closeModal(...d)),disabled:e.loading}," Discard ",8,ge),o("button",{"data-kt-indicator":e.loading?"on":null,class:"bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover rounded-md px-4 py-2 font-semibold md:px-4 md:py-3 md:text-xl",type:"submit",disabled:e.loading},[e.loading?M("",!0):(w(),$("span",ve," Save ")),e.loading?(w(),$("span",fe,n[6]||(n[6]=[k(" Please wait... "),o("span",{class:"spinner-border spinner-border-sm align-middle ms-2"},null,-1)]))):M("",!0)],8,pe)])]),_:1},8,["onSubmit","model","rules"])])])):M("",!0)}const he=A(ae,[["render",be]]),ye=j({name:"well-interval",props:{id:{type:String,required:!0}},components:{SvgIcon:E,TablePagination:O,IntervalModal:he},setup(e){const n=z(),f=y([]),m=y(0),b=y(0),l=y(1),s=y(!1),r=y(null),i=y(!1);W(()=>{a()}),C(l,()=>{a()});const a=async(p={wellId:e==null?void 0:e.id,page:l.value,limit:10})=>{s.value=!0,n.getIntervals({params:p,callback:{onSuccess:u=>{m.value=u==null?void 0:u.totalPage,b.value=u==null?void 0:u.total,l.value=u==null?void 0:u.page,f.value=u==null?void 0:u.items},onFinish:u=>{s.value=!1}}})},t=(p,u)=>{const _=f.value[u];f.value[u]=f.value[p],f.value[p]=_},d=p=>{l.value=p},x=()=>{i.value=!i.value},h=p=>{var u;(u=r==null?void 0:r.value)==null||u.setId(p),i.value=!i.value},D=p=>{L.deletionAlert({onConfirmed:()=>{g(p)}})},g=async p=>{s.value=!0,n.deleteInterval({id:p,callback:{onSuccess:u=>{a()},onFinish:u=>{s.value=!1}}})};return{changePosition:t,toggleNewInterval:x,toggleEditInterval:h,deleteInterval:D,getIntervals:a,pageChange:d,intervalList:f,pageCount:m,currentPage:l,totalElements:b,intervalModal:r,loading:s,isModalVisible:i}}}),xe={class:"h-auto w-full rounded-b-xl p-b-8 py-4 md:text-lg"},we={class:"h-auto w-full flex flex-row p-4 items-center justify-between"},$e={key:0,class:"text-center"},Ie={key:2,class:"h-auto w-full overflow-x-scroll p-4 md:overflow-hidden"},Pe={class:"md:w-11/12 md:mx-auto md:text-center w-full table-fixed"},_e={class:"h-auto w-1/5 p-4 text-center font-semibold"},Se={class:"h-auto w-1/2 p-4"},De={class:"max-h-30 w-full line-clamp-4 text-left"},Fe={class:"h-auto w-1/3 p-4"},ke={class:"flex items-center justify-center gap-2"},Ve=["onClick"],Me=["onClick"],Ne={class:"text-danger"},Ce=["disabled","onClick"],qe={class:"text-icon-primary"},Ee=["disabled","onClick"],je={class:"text-icon-primary"},Ae={class:"h-auto w-11/12 flex flex-col items-center mx-auto my-5"},He={key:0,class:"font-semibold"};function We(e,n,f,m,b,l){const s=P("SvgIcon"),r=P("el-empty"),i=P("TablePagination"),a=P("IntervalModal");return w(),$(q,null,[o("div",xe,[o("div",we,[n[2]||(n[2]=o("h1",{class:"text-lg font-bold"},"Interval",-1)),o("div",{class:"bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover rounded-md px-4 py-2 font-semibold flex flex-row items-center gap-2 justify-between md:px-4 md:py-3 md:text-xl",onClick:n[0]||(n[0]=(...t)=>e.toggleNewInterval&&e.toggleNewInterval(...t))},[v(s,{icon:"addIcon"}),n[1]||(n[1]=k(" New Interval "))])]),e.loading?(w(),$("div",$e,n[3]||(n[3]=[o("div",{class:"spinner-border text-primary",role:"status"},[o("span",{class:"sr-only"},"Loading...")],-1)]))):e.intervalList.length===0?(w(),H(r,{key:1,description:"No Data"})):(w(),$("div",Ie,[o("table",Pe,[n[4]||(n[4]=o("thead",{class:"font-bold"},[o("tr",null,[o("th",{class:"p-4 w-1/5"},"Interval"),o("th",{class:"p-4 w-1/2"},"Note"),o("th",{class:"p-4 w-1/3"},"Actions")])],-1)),o("tbody",null,[(w(!0),$(q,null,B(e.intervalList,(t,d)=>(w(),$("tr",{key:t.id},[o("td",_e,V(t==null?void 0:t.interval),1),o("td",Se,[o("div",De,V(t==null?void 0:t.notes),1)]),o("td",Fe,[o("div",ke,[o("button",{type:"button",class:"bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover font-semibold h-8 w-8 flex items-center justify-center rounded-lg pl-[7.5px] pt-[1px] md:h-10 md:w-10 md:pl-[3px] md:pt-[2px]",onClick:x=>{var h;return e.toggleEditInterval(((h=t==null?void 0:t.id)==null?void 0:h.toString())||"")}},[v(s,{icon:"newReportIcon",classname:"md:h-6 md:w-6"})],8,Ve),o("button",{type:"button",class:"bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover font-semibold h-8 w-8 flex items-center justify-center rounded-lg pl-[7.5px] pt-[1px] md:h-10 md:w-10 md:pl-[3px] md:pb-[3.75px]",onClick:x=>{var h;return e.deleteInterval(((h=t==null?void 0:t.id)==null?void 0:h.toString())||"")}},[o("span",Ne,[v(s,{icon:"trashIcon",classname:"md:h-7 md:w-7"})])],8,Me),o("button",{type:"button",class:"bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover font-semibold h-8 w-8 flex items-center justify-center rounded-lg pl-[7.5px] pt-[1px] md:h-10 md:w-10 md:pl-[3px] md:pb-[3.75px]",disabled:d==e.intervalList.length-1,onClick:x=>e.changePosition(d,d+1)},[o("span",qe,[v(s,{icon:"arrowDown",classname:"md:h-7 md:w-7"})])],8,Ce),o("button",{type:"button",class:"bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover font-semibold h-8 w-8 flex items-center justify-center rounded-lg pl-[7.5px] pt-[1px] md:h-10 md:w-10 md:pl-[3px] md:pb-[3.75px]",disabled:d==0,onClick:x=>e.changePosition(d,d-1)},[o("span",je,[v(s,{icon:"arrowUp",classname:"md:h-7 md:w-7"})])],8,Ee)])])]))),128))])])])),o("div",Ae,[e.intervalList.length?(w(),$("div",He,V(`Showing ${(e.currentPage-1)*10+1} to ${e.intervalList.length} of ${e.totalElements} entries`),1)):M("",!0),e.pageCount>=1?(w(),H(i,{key:1,"total-pages":e.pageCount,total:e.totalElements,"per-page":10,"current-page":e.currentPage,onPageChange:e.pageChange},null,8,["total-pages","total","current-page","onPageChange"])):M("",!0)])]),v(a,{isVisible:e.isModalVisible,close:e.toggleNewInterval,ref:"intervalModal",loadPage:e.getIntervals,wellId:e.id},null,8,["isVisible","close","loadPage","wellId"])],64)}const J=A(ye,[["render",We]]),Q=U("plan",()=>({getPlans:async({params:l,callback:s})=>{var a;const r=c.get(s,"onSuccess",c.noop),i=c.get(s,"onFinish",c.noop);try{I.setHeader();const t=await I.getWithParams("plans",l);r(((a=t.data)==null?void 0:a.data)||t.data)}catch(t){F(t,s)}finally{i()}},getPlanDetails:async({id:l,callback:s})=>{var a;const r=c.get(s,"onSuccess",c.noop),i=c.get(s,"onFinish",c.noop);try{I.setHeader();const t=await I.get(`plans/${l}`);r(((a=t.data)==null?void 0:a.data)||t.data)}catch(t){F(t,s)}finally{i()}},updatePlan:async({id:l,params:s,callback:r})=>{var t;const i=c.get(r,"onSuccess",c.noop),a=c.get(r,"onFinish",c.noop);try{I.setHeader();const d=await I.put(`plans/${l}`,s);i(((t=d.data)==null?void 0:t.data)||d.data)}catch(d){F(d,r)}finally{a()}},deletePlan:async({id:l,callback:s})=>{var a;const r=c.get(s,"onSuccess",c.noop),i=c.get(s,"onFinish",c.noop);try{I.setHeader();const t=await I.delete(`plans/${l}`);r(((a=t.data)==null?void 0:a.data)||t.data)}catch(t){F(t,s)}finally{i()}},createPlan:async({params:l,callback:s})=>{var a;const r=c.get(s,"onSuccess",c.noop),i=c.get(s,"onFinish",c.noop);try{I.setHeader();const t=await I.post("plans",l);r(((a=t.data)==null?void 0:a.data)||t.data)}catch(t){F(t,s)}finally{i()}}})),Le=j({name:"plan-modal",components:{SvgIcon:E},props:{isVisible:{type:Boolean,required:!0},close:{type:Function,required:!0},loadPage:{type:Function,default:()=>{},required:!0},wellId:{type:String,required:!0}},setup(e){const n=Q(),f=y(!1),m=y({mudDepth:null,day:null,cost:null}),b=y(null),l=y(!1),s=y("");C(s,g=>{g!==""&&r()}),C(f,g=>{var p;g===!1&&(s.value="",D(),(p=b==null?void 0:b.value)==null||p.resetFields())});const r=async()=>{n.getPlanDetails({id:s.value,callback:{onSuccess:g=>{m.value=g}}})},i=async g=>{l.value=!0,n.updatePlan({id:s.value,params:g,callback:{onSuccess:p=>{e!=null&&e.loadPage&&(e==null||e.loadPage()),e.close()},onFinish:p=>{l.value=!1}}})},a=async g=>{l.value=!0,n.createPlan({params:g,callback:{onSuccess:p=>{e!=null&&e.loadPage&&(e==null||e.loadPage()),e.close()},onFinish:p=>{l.value=!1}}})},t=()=>{e.close()},d=g=>{s.value=g.toString()},x=y({mudDepth:[{required:!0,message:"Please type mud depth",trigger:"blur"}],day:[{required:!0,message:"Please type day",trigger:"blur"}],cost:[{required:!0,message:"Please type cost",trigger:"blur"}]}),h=()=>{b.value&&b.value.validate(g=>{if(g){l.value=!0;const p={mudDepth:Number(m.value.mudDepth),day:Number(m.value.day),cost:Number(m.value.cost)};s!=null&&s.value?i(p):a({...p,wellId:e==null?void 0:e.wellId})}})},D=()=>{m.value={mudDepth:null,day:null,cost:null}};return{id:s,modal:f,rules:x,loading:l,targetData:m,formRef:b,submit:h,setId:d,reset:D,closeModal:t}}}),Be={key:0,class:"fixed top-0 right-0 bottom-0 left-0 z-40 flex items-center"},Ue={class:"relative bg-card-background text-card-text max-h-11/12 w-11/12 flex flex-col items-center gap-3 mx-auto p-5 rounded-xl md:w-4/5 md:text-lg lg:w-2/5 lg:h-auto"},Re={class:"h-auto w-full flex flex-row items-center justify-between"},Ge={class:"text-lg font-bold md:text-xl"},Oe={class:"h-auto w-full flex flex-col gap-2"},ze={class:"h-auto w-full flex items-center font-semibold"},Je={class:"h-auto w-full flex flex-col gap-2"},Qe={class:"flex items-center font-semibold"},Ke={class:"flex flex-col gap-2"},Xe={class:"flex items-center font-semibold"},Ye={class:"flex flex-row items-start mt-4 gap-3"},Ze=["disabled"],Te=["disabled"],et={key:0,class:"indicator-label"},tt={key:1,class:"indicator-progress"};function nt(e,n,f,m,b,l){const s=P("SvgIcon"),r=P("el-popover"),i=P("el-input"),a=P("el-form-item"),t=P("el-form");return e.isVisible?(w(),$("div",Be,[n[18]||(n[18]=o("div",{class:"absolute inset-0 bg-sidebar-background opacity-25"},null,-1)),o("div",Ue,[o("div",Re,[o("h3",Ge,V(`${e.id?"Edit Plan":"New Plan"}`),1),o("span",{class:"cursor-pointer",onClick:n[0]||(n[0]=(...d)=>e.closeModal&&e.closeModal(...d))},[v(s,{icon:"closeModalIcon"})])]),v(t,{id:"plan_form",onSubmit:R(e.submit,["prevent"]),model:e.targetData,rules:e.rules,ref:"formRef",class:"h-auto w-full"},{default:S(()=>[o("div",Oe,[o("label",ze,[n[7]||(n[7]=k("Mud Depth (ft) ")),n[8]||(n[8]=o("span",{class:"text-danger-active font-light"},"*",-1)),v(r,{placement:"right",width:30,trigger:"hover"},{reference:S(()=>n[5]||(n[5]=[o("i",{class:"fas fa-exclamation-circle ms-2"},null,-1)])),default:S(()=>[n[6]||(n[6]=o("span",null," ... ",-1))]),_:1})]),v(a,{prop:"mudDepth"},{default:S(()=>[v(i,{type:"number",controls:!1,step:"any",modelValue:e.targetData.mudDepth,"onUpdate:modelValue":n[1]||(n[1]=d=>e.targetData.mudDepth=d),placeholder:"",name:"mudDepth"},null,8,["modelValue"])]),_:1})]),o("div",Je,[o("label",Qe,[n[11]||(n[11]=k("Day ")),n[12]||(n[12]=o("span",{class:"text-danger-active font-light"},"*",-1)),v(r,{placement:"right",width:30,trigger:"hover"},{reference:S(()=>n[9]||(n[9]=[o("i",{class:"fas fa-exclamation-circle ms-2"},null,-1)])),default:S(()=>[n[10]||(n[10]=o("span",null," ... ",-1))]),_:1})]),v(a,{prop:"day"},{default:S(()=>[v(i,{type:"number",controls:!1,min:0,step:1,"step-strictly":"",modelValue:e.targetData.day,"onUpdate:modelValue":n[2]||(n[2]=d=>e.targetData.day=d),placeholder:"",name:"day"},null,8,["modelValue"])]),_:1})]),o("div",Ke,[o("label",Xe,[n[15]||(n[15]=k("Cost ")),n[16]||(n[16]=o("span",{class:"text-danger-active font-light"},"*",-1)),v(r,{placement:"right",width:30,trigger:"hover"},{reference:S(()=>n[13]||(n[13]=[o("i",{class:"fas fa-exclamation-circle ms-2"},null,-1)])),default:S(()=>[n[14]||(n[14]=o("span",null," ... ",-1))]),_:1})]),v(a,{prop:"cost",class:"mt-auto"},{default:S(()=>[v(i,{type:"number",controls:!1,step:"any",modelValue:e.targetData.cost,"onUpdate:modelValue":n[3]||(n[3]=d=>e.targetData.cost=d),placeholder:"",name:"cost"},null,8,["modelValue"])]),_:1})]),o("div",Ye,[o("button",{type:"button",class:"bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover rounded-md px-4 py-2 font-semibold md:px-4 md:py-3 md:text-xl",onClick:n[4]||(n[4]=(...d)=>e.closeModal&&e.closeModal(...d)),disabled:e.loading}," Discard ",8,Ze),o("button",{class:"bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover rounded-md px-4 py-2 font-semibold md:px-4 md:py-3 md:text-xl",type:"submit",disabled:e.loading},[e.loading?M("",!0):(w(),$("span",et," Save ")),e.loading?(w(),$("span",tt,n[17]||(n[17]=[k(" Please wait... "),o("span",{class:"spinner-border spinner-border-sm align-middle ms-2"},null,-1)]))):M("",!0)],8,Te)])]),_:1},8,["onSubmit","model","rules"])])])):M("",!0)}const ot=A(Le,[["render",nt]]),lt=j({name:"well-plan",props:{id:{type:String,required:!0}},components:{SvgIcon:E,TablePagination:O,PlanModal:ot},setup(e){const n=Q(),f=y([]),m=y(!1),b=y(null),l=y(!1);W(()=>{s()});const s=async(x={wellId:e==null?void 0:e.id,page:1,limit:200})=>{m.value=!0,n.getPlans({params:x,callback:{onSuccess:h=>{f.value=h==null?void 0:h.items},onFinish:h=>{m.value=!1}}})},r=(x,h)=>{const D=f.value[h];f.value[h]=f.value[x],f.value[x]=D},i=()=>{l.value=!l.value},a=x=>{var h;(h=b==null?void 0:b.value)==null||h.setId(x),l.value=!l.value},t=x=>{L.deletionAlert({onConfirmed:()=>{d(x)}})},d=async x=>{m.value=!0,n.deletePlan({id:x,callback:{onSuccess:h=>{s()},onFinish:h=>{m.value=!1}}})};return{numberWithCommas:se,changePosition:r,toggleNewPlan:i,toggleEditPlan:a,deletePlan:t,getPlans:s,planList:f,planModal:b,loading:m,isModalVisible:l}}}),st={class:"well-plan"},at={class:"bg-card-background text-card-text-light w-11/12 mx-auto p-4 h-auto rounded-xl"},rt={class:"h-auto w-full flex flex-row p-4 items-center justify-between md:mb-4"},it={key:0,class:"text-center"},dt={key:2,class:"grid grid-cols-1 gap-6 md:grid-cols-2 md:gap-7 lg:grid-cols-3"},ut={class:"flex flex-col gap-5 font-semibold text-lg p-4"},ct={class:"text-lg"},mt={class:"flex items-center justify-between border-b-[1px] border-dashed pb-3"},gt={class:"text-success"},pt={class:"flex items-center justify-between"},vt={class:"flex flex-row items-center gap-2 absolute top-0 right-0 -translate-y-1/2"},ft=["onClick"],bt=["onClick"],ht={class:"text-danger"};function yt(e,n,f,m,b,l){const s=P("SvgIcon"),r=P("el-empty"),i=P("el-tooltip"),a=P("PlanModal");return w(),$("div",st,[o("div",at,[o("div",rt,[n[2]||(n[2]=o("h1",{class:"text-lg font-bold"},"Plans",-1)),o("div",{class:"bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover rounded-md px-4 py-2 font-semibold flex flex-row items-center gap-2 justify-between md:px-4 md:py-3 md:text-xl",onClick:n[0]||(n[0]=(...t)=>e.toggleNewPlan&&e.toggleNewPlan(...t))},[v(s,{icon:"addIcon"}),n[1]||(n[1]=k(" New Plan "))])]),e.loading?(w(),$("div",it,n[3]||(n[3]=[o("div",{class:"spinner-border text-primary",role:"status"},[o("span",{class:"sr-only"},"Loading...")],-1)]))):e.planList.length===0?(w(),H(r,{key:1,description:"No Data"})):(w(),$("div",dt,[(w(!0),$(q,null,B(e.planList,t=>(w(),$("div",{key:t.id,class:"bg-minicard-background text-minicard-text-light rounded-lg shadow-sm border-t-2 border-active relative first:mt-0 last:mb-0 md:mt-0 md:mb-0"},[o("div",ut,[o("h5",ct,V(`${t.day} Days`),1),o("div",mt,[n[4]||(n[4]=o("span",null,"Measured Depth",-1)),o("span",gt,V(`${t==null?void 0:t.mudDepth} (ft)`),1)]),o("div",pt,[n[5]||(n[5]=o("span",null,"Cost",-1)),o("span",null,V(`$${e.numberWithCommas((t==null?void 0:t.cost)||0)}`),1)])]),o("div",vt,[v(i,{content:"Edit Plan"},{default:S(()=>[o("div",{class:"rounded-full p-1.5 bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover",onClick:d=>{var x;return e.toggleEditPlan(((x=t==null?void 0:t.id)==null?void 0:x.toString())||"")}},[v(s,{icon:"pencilIcon",classname:"md:h-7 md:w-7"})],8,ft)]),_:2},1024),v(i,{content:"Delete Plan"},{default:S(()=>[o("button",{class:"rounded-full p-1.5 bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover",onClick:d=>e.deletePlan((t==null?void 0:t.id)||"")},[o("span",ht,[v(s,{icon:"trashIcon",classname:"md:h-7 md:w-7"})])],8,bt)]),_:2},1024)])]))),128))]))]),v(a,{isVisible:e.isModalVisible,close:e.toggleNewPlan,ref:"planModal",loadPage:e.getPlans,wellId:e.id},null,8,["isVisible","close","loadPage","wellId"])])}const K=A(lt,[["render",yt],["__scopeId","data-v-a02d41a7"]]),xt={[N.General]:G,[N.Interval]:J,[N.Plan]:K},wt=j({name:"well-detail",components:{SvgIcon:E,WellGeneralInfo:G,WellInterval:J,WellPlan:K,PageHeader:X},setup(){var g,p;const e=T(),n=le(),f=y(),m=y(N.General),b=["Home","Well","Edit"],l=y(null),s=Z(()=>xt[m.value]),r=(p=(g=e.params)==null?void 0:g.id)==null?void 0:p.toString();W(()=>{r&&i(r)});const i=async u=>{n.getWellDetails({wellId:u,callback:{onSuccess:_=>{f.value=_}}})},a=u=>{const _=u.target;m.value=Number(_.getAttribute("data-tab-index"))},t=()=>{var u,_;if(l!=null&&l.value&&((u=l==null?void 0:l.value)!=null&&u.loading))return(_=l==null?void 0:l.value)==null?void 0:_.loading},d=()=>{var u,_;if(l!=null&&l.value&&((u=l==null?void 0:l.value)!=null&&u.isFormDirty))return(_=l==null?void 0:l.value)==null?void 0:_.isFormDirty()},x=async()=>{var u,_;return l!=null&&l.value&&((u=l==null?void 0:l.value)!=null&&u.isValidForm)?await((_=l==null?void 0:l.value)==null?void 0:_.isValidForm()):null},h=async()=>{var u;l!=null&&l.value&&((u=l==null?void 0:l.value)!=null&&u.submit)&&l.value.submit()},D=async u=>{if(m.value!==N.General||t())a(u);else{if(!d()){a(u);return}const _=await x();if(_===null){a(u);return}_?(h(),a(u)):L.incompleteFormAlert({onConfirmed:()=>{a(u)}})}};return{wellId:r,EWellDetailsTab:N,tabIndex:m,wellInfo:f,breadcrumbs:b,wellDetailsTabs:Y,currentChildTab:l,currentComponent:s,isSystemAdmin:ee.checkRole(te.SystemAdmin),handleActiveTab:D}}}),$t={class:"h-auto w-11/12 mx-auto mb-4 bg-card-background text-card-text-light rounded-xl md:text-lg"},It={class:"h-auto w-11/12 flex flex-col gap-6 p-4"},Pt={key:0,class:"font-bold"},_t={class:"flex items-center gap-3",role:"tablist"},St=["data-tab-index"];function Dt(e,n,f,m,b,l){var r,i,a;const s=P("PageHeader");return w(),$(q,null,[v(s,{title:(r=e.wellInfo)==null?void 0:r.nameOrNo,breadcrumbs:e.breadcrumbs},null,8,["title","breadcrumbs"]),o("div",$t,[o("div",It,[e.isSystemAdmin?(w(),$("h2",Pt,[n[1]||(n[1]=o("span",null,"Company: ",-1)),k(" "+V((a=(i=e.wellInfo)==null?void 0:i.company)==null?void 0:a.name),1)])):M("",!0),o("ul",_t,[(w(!0),$(q,null,B(e.wellDetailsTabs,t=>(w(),$("li",{key:t.value},[o("a",{class:oe(["whitespace-nowrap cursor-pointer font-semibold hover:text-active-hover hover:border-b-2 hover:border-active-border-hover",{active:e.tabIndex===(t==null?void 0:t.value),"text-active border-b-2 border-active-border":e.tabIndex===(t==null?void 0:t.value),"text-inactive border-b-2 border-inactive-border":e.tabIndex!==(t==null?void 0:t.value)}]),onClick:n[0]||(n[0]=d=>e.handleActiveTab(d)),"data-tab-index":t.value,role:"tab"},V(t.label),11,St)]))),128))])]),(w(),H(ne(e.currentComponent),{id:e.wellId,ref:"currentChildTab",class:"mx-auto pb-4"},null,8,["id"]))])],64)}const Rt=A(wt,[["render",Dt]]);export{Rt as default};
