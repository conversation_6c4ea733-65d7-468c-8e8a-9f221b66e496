import{S as O}from"./SvgIcon-DYvlNVZf.js";import{d as $,q as p,L as V,J as C,j,A as L,Z as J,_ as z,c as P,l as w,o as U,a as o,b as d,r as b,w as m,B as T,p as Z}from"./index-BmHWvWFS.js";import{y as N}from"./validator-D_t2fUhD.js";import{u as G}from"./company-KQxnMnUF.js";import{u as H}from"./user-CVSNmFaf.js";import{c as K}from"./index.esm-C4vtr4xS.js";const Q=$({name:"user-modal",components:{SvgIcon:O},props:{isVisible:{type:Boolean,required:!0},close:{type:Function,required:!0},loadPage:{type:Function,required:!1}},setup(l){var S;const e=j(),h=G(),D=H(),x=p(!1),c=p([]),v={firstName:"",lastName:"",companyId:V()?"":(S=C.getUserInfo())==null?void 0:S.companyId,email:"",officePhone:"",mobilePhone:"",userRoles:[],address:"",note:""},r=p(!1),i=p({...v}),u=p(null),g=p(!1),a=p(!1),M=e.name==="users";L(x,n=>{n===!1?I():V()?R():q()});const R=async()=>{a.value=!0,h.getCompanies({params:{page:1,limit:500},callback:{onSuccess:n=>{var t;c.value=(t=n==null?void 0:n.items)==null?void 0:t.map(s=>({value:s==null?void 0:s.id,label:s==null?void 0:s.name}))},onFinish:()=>{a.value=!1}}})},q=async()=>{var n;a.value=!0,h.getCompanyById({id:(n=C.getUserInfo())==null?void 0:n.companyId,callback:{onSuccess:t=>{c.value=[{value:t==null?void 0:t.id,label:t==null?void 0:t.name}]},onFinish:()=>{a.value=!1}}})},k=()=>{l.close()},y=K().shape({email:N.emailAddress,mobilePhone:N.mobilePhone,officePhone:N.officePhone}),A=p({firstName:[{required:!0,message:"Please type First Name",trigger:["blur","change"]}],lastName:[{required:!0,message:"Please type Last Name",trigger:["blur","change"]}],companyId:[{required:!0,message:"Please select Company",trigger:["blur","change"]}],email:[{required:!0,validator:(n,t,s)=>{y.fields.email.validate(t).then(()=>{s()}).catch(f=>{s(new Error(f.errors[0]))})},trigger:["blur","change"]}],mobilePhone:[{required:!0,validator:(n,t,s)=>{y.fields.mobilePhone.validate(t).then(()=>{s()}).catch(f=>{s(new Error(f.errors[0]))})},trigger:["blur","change"]}],officePhone:[{validator:(n,t,s)=>{t?y.fields.officePhone.validate(t).then(()=>{s()}).catch(f=>{s(new Error(f.errors[0]))}):s()},trigger:["blur","change"]}]}),F=()=>{u.value&&u.value.validate(n=>{var t,s,f;if(n){const E={...i.value,officePhone:((t=i.value)==null?void 0:t.officePhone)||null,address:((s=i.value)==null?void 0:s.address)||null,note:((f=i.value)==null?void 0:f.note)||null};B(E)}})},B=async n=>{g.value=!0,D.addUser({params:n,callback:{onSuccess:t=>{var s;l.close(),(s=l==null?void 0:l.loadPage)==null||s.call(l,t==null?void 0:t.id)},onFinish:()=>{g.value=!1}}})},I=()=>{var n;r.value=!1,i.value={...v},(n=u==null?void 0:u.value)==null||n.resetFields()};return{disabledRole:r,companyOptions:c,isSystemAdmin:V,rules:A,loading:g,loadingCompany:a,targetData:i,formRef:u,rolesOptions:J,isUserManagementPage:M,submit:F,reset:I,setInitialValue:n=>{r.value=!0,i.value={...i.value,...n}},closeUserModal:k}}}),W={key:0,class:"fixed top-0 right-0 bottom-0 left-0 z-40 flex items-center"},X={class:"relative bg-card-background text-card-text max-h-11/12 w-11/12 flex flex-col items-center gap-3 mx-auto p-5 rounded-xl lg:w-2/5 lg:h-auto"},Y={class:"h-auto w-full flex flex-row items-center justify-between"},_={class:"md:flex md:h-auto md:w-full md:gap-4"},ee={class:"md:h-auto md:w-full md:flex md:flex-col"},le={class:"md:h-auto md:w-full md:flex md:justify-between md:gap-4"},oe={class:"h-auto w-full flex flex-col gap-2"},ae={class:"h-auto w-full flex flex-col gap-2"},te={class:"flex flex-col gap-2"},se={class:"flex flex-col gap-2"},ne={class:"md:h-auto md:w-full md:flex md:flex-col"},de={class:"flex flex-col gap-2"},ie={class:"flex flex-col gap-2"},re={class:"flex flex-col gap-2"},me={class:"flex flex-col gap-2"},ue={class:"col-12 fv-row d-flex flex-column justify-content-stretch"},fe={class:"flex flex-row items-start mt-4 gap-3"},pe=["disabled"],ge=["disabled"],be={key:0,class:"indicator-label"},ce={key:1,class:"indicator-progress"};function ve(l,e,h,D,x,c){const v=b("SvgIcon"),r=b("el-input"),i=b("el-form-item"),u=b("el-select-v2"),g=b("el-form");return l.isVisible?(U(),P("div",W,[e[22]||(e[22]=o("div",{class:"absolute inset-0 bg-sidebar-background opacity-25"},null,-1)),o("div",X,[o("div",Y,[e[11]||(e[11]=o("h3",{class:"text-lg font-bold"},"Add User",-1)),o("span",{class:"cursor-pointer",onClick:e[0]||(e[0]=(...a)=>l.closeUserModal&&l.closeUserModal(...a))},[d(v,{icon:"closeModalIcon"})])]),d(g,{id:"product_form",onSubmit:Z(l.submit,["prevent"]),model:l.targetData,rules:l.rules,ref:"formRef",class:"h-auto w-full"},{default:m(()=>[o("div",_,[o("div",ee,[o("div",le,[o("div",oe,[e[12]||(e[12]=o("label",{class:"font-semibold"},"First Name ",-1)),d(i,{prop:"firstName"},{default:m(()=>[d(r,{modelValue:l.targetData.firstName,"onUpdate:modelValue":e[1]||(e[1]=a=>l.targetData.firstName=a),placeholder:"",name:"firstName"},null,8,["modelValue"])]),_:1})]),o("div",ae,[e[13]||(e[13]=o("label",{class:"font-semibold"},"Last Name ",-1)),d(i,{prop:"lastName"},{default:m(()=>[d(r,{modelValue:l.targetData.lastName,"onUpdate:modelValue":e[2]||(e[2]=a=>l.targetData.lastName=a),placeholder:"",name:"lastName"},null,8,["modelValue"])]),_:1})])]),o("div",te,[e[14]||(e[14]=o("label",{class:"font-semibold"},"Address ",-1)),d(i,{prop:"address"},{default:m(()=>[d(r,{modelValue:l.targetData.address,"onUpdate:modelValue":e[3]||(e[3]=a=>l.targetData.address=a),placeholder:"",name:"address"},null,8,["modelValue"])]),_:1})]),o("div",se,[e[15]||(e[15]=o("label",{class:"font-semibold"}," Company ",-1)),d(i,{prop:"companyId"},{default:m(()=>[d(u,{disabled:!l.isSystemAdmin()||!l.isUserManagementPage,modelValue:l.targetData.companyId,"onUpdate:modelValue":e[4]||(e[4]=a=>l.targetData.companyId=a),options:l.companyOptions,placeholder:"Company Name",name:"companyId",loading:l.loadingCompany},null,8,["disabled","modelValue","options","loading"])]),_:1})])]),o("div",ne,[o("div",de,[e[16]||(e[16]=o("label",{class:"font-semibold"},"Email Address ",-1)),d(i,{prop:"email"},{default:m(()=>[d(r,{modelValue:l.targetData.email,"onUpdate:modelValue":e[5]||(e[5]=a=>l.targetData.email=a),placeholder:"",name:"email"},null,8,["modelValue"])]),_:1})]),o("div",ie,[e[17]||(e[17]=o("label",{class:"font-semibold"},"Office Phone ",-1)),d(i,{prop:"officePhone"},{default:m(()=>[d(r,{modelValue:l.targetData.officePhone,"onUpdate:modelValue":e[6]||(e[6]=a=>l.targetData.officePhone=a),placeholder:"",name:"officePhone"},null,8,["modelValue"])]),_:1})]),o("div",re,[e[18]||(e[18]=o("label",{class:"font-semibold"},"Mobile Phone ",-1)),d(i,{prop:"mobilePhone"},{default:m(()=>[d(r,{modelValue:l.targetData.mobilePhone,"onUpdate:modelValue":e[7]||(e[7]=a=>l.targetData.mobilePhone=a),placeholder:"",name:"mobilePhone"},null,8,["modelValue"])]),_:1})])])]),o("div",me,[e[19]||(e[19]=o("label",{class:"font-semibold"},"Roles ",-1)),d(i,{prop:"userRoles"},{default:m(()=>[d(u,{modelValue:l.targetData.userRoles,"onUpdate:modelValue":e[8]||(e[8]=a=>l.targetData.userRoles=a),options:l.rolesOptions,placeholder:"Roles",multiple:"",clearable:"",name:"userRoles",disabled:l.disabledRole},null,8,["modelValue","options","disabled"])]),_:1})]),o("div",ue,[e[20]||(e[20]=o("label",{class:"font-semibold"},"Note ",-1)),d(i,{prop:"note"},{default:m(()=>[d(r,{modelValue:l.targetData.note,"onUpdate:modelValue":e[9]||(e[9]=a=>l.targetData.note=a),placeholder:"",type:"textarea",rows:"2",name:"note"},null,8,["modelValue"])]),_:1})]),o("div",fe,[o("button",{type:"button",class:"bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover rounded-md px-4 py-2 font-semibold",onClick:e[10]||(e[10]=(...a)=>l.closeUserModal&&l.closeUserModal(...a)),disabled:l.loading}," Discard ",8,pe),o("button",{class:"bg-button-primary hover:bg-button-primary-hover text-button-text-light hover:text-button-text-light-hover rounded-md px-4 py-2 font-semibold",type:"submit",disabled:l.loading},[l.loading?w("",!0):(U(),P("span",be," Save ")),l.loading?(U(),P("span",ce,e[21]||(e[21]=[T(" Please wait... "),o("span",{class:"spinner-border spinner-border-sm align-middle ms-2"},null,-1)]))):w("",!0)],8,ge)])]),_:1},8,["onSubmit","model","rules"])])])):w("",!0)}const De=z(Q,[["render",ve]]);export{De as U};
